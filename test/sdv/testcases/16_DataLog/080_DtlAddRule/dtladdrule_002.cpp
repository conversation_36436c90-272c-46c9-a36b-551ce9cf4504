/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.0.0 迭代四Datalog热补丁支持新增投影规则、修改tbm_udf-执行测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.04.10]
*****************************************************************************/
#include "dtladdrule.h"
#include "DatalogRun.h"

using namespace std;

class dtladdrule_002_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtladdrule_002_test::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtladdrule_002_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

class dtladdrule_002_test1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
#ifdef RUN_INDEPENDENT
        system("sh ${TEST_HOME}/tools/stop.sh -f");
#endif
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtladdrule_002_test1::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtladdrule_002_test1::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

// 新增空表进行join，会存在数据不一致情况
/* ****************************************************************************
 Description  : 001.patch.d显示声明block为0，直接TBM表UDF的实现，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 11, name: REDO_PROCESS]
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r2
    ------------------
    RULE_NAME: r1
    ------------------
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
    UDF_NAME: dtl_tbm_tbl_out1
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn3[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn4[2 * recordNum] = {{1, 0, 4, 6, 5}, {1, 0, 5, 7, 6}, {1, 0, 1, 9, 8}, {1, 0, 1, 1, 1},
        {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");
    system("gmsysview count");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out3 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_001.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.patch.d显示声明block为1，直接TBM表UDF的实现，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 11, name: REDO_PROCESS]
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r2
    ------------------
    RULE_NAME: r1
    ------------------
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
    UDF_NAME: dtl_tbm_tbl_out1
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn3[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn4[2 * recordNum] = {{1, 0, 4, 6, 5}, {1, 0, 5, 7, 6}, {1, 0, 1, 9, 8}, {1, 0, 1, 1, 1},
        {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");
    system("gmsysview count");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out3 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_002.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.patch.d显示声明block为0，新增含TBM表的规则，右表仅含原始.d同一个topo的输入表，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
    RULE_NAME: rN1
  Node[id: 16, name: UPDATE_RULES]
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_003.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.patch.d显示声明block为1，新增含TBM表的规则，右表仅含原始.d同一个topo的输入表，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
    RULE_NAME: rN1
  Node[id: 16, name: UPDATE_RULES]
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_004.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.patch.d显示声明block为0，新增含TBM表的规则，右表仅含原始.d中间表，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
    RULE_NAME: rN1
  Node[id: 16, name: UPDATE_RULES]
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_005.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.patch.d显示声明block为1，新增含TBM表的规则，右表仅含原始.d中间表，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf006";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
    RULE_NAME: rN1
  Node[id: 16, name: UPDATE_RULES]
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_006.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.patch.d显示声明block为0，新增含TBM表的规则，右表仅含原始.d输入表和function共16个，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf007";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
    RULE_NAME: rN1
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
    ------------------
    RULE_NAME: r5
    ------------------
    RULE_NAME: r4
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
    UDF_NAME: dtl_ext_func_func1
    ------------------
    UDF_NAME: dtl_ext_func_func2
    ------------------
    UDF_NAME: dtl_ext_func_func3
  Node[id: 19, name: UPDATE_UDFS]
    UDF_NAME: dtl_tbm_tbl_out1
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn3[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn4[2 * recordNum] = {{1, 0, 4, 6, 5}, {1, 0, 5, 7, 6}, {1, 0, 1, 9, 8}, {1, 0, 1, 1, 1},
        {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");
    system("gmsysview count");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out3 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_007.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.patch.d显示声明block为0，新增含TBM表的规则，右表仅原始.d输入表、中间表、原始.d中function,
 新增patch.d中function和输入表共16个，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp5
    ------------------
    TABLE_NAME: inp4
    ------------------
    TABLE_NAME: inp6
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
    RULE_NAME: rN1
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
    ------------------
    RULE_NAME: r5
    ------------------
    RULE_NAME: r4
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
    UDF_NAME: dtl_ext_func_func1
    ------------------
    UDF_NAME: dtl_ext_func_func2
    ------------------
    UDF_NAME: dtl_ext_func_func3
  Node[id: 19, name: UPDATE_UDFS]
    UDF_NAME: dtl_tbm_tbl_out1
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn3[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn4[2 * recordNum] = {{1, 0, 4, 6, 5}, {1, 0, 5, 7, 6}, {1, 0, 1, 9, 8}, {1, 0, 1, 1, 1},
        {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");
    system("gmsysview count");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out3 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_008.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.patch.d显示声明block为1，新增含TBM表的规则，右表仅含原始.d输入表、中间表、原始.d中function,
 新增patch.d中function和输入表共16个，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf009";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp5
    ------------------
    TABLE_NAME: inp4
    ------------------
    TABLE_NAME: inp6
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
    RULE_NAME: rN1
  Node[id: 16, name: UPDATE_RULES]
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
    UDF_NAME: dtl_ext_func_func1
    ------------------
    UDF_NAME: dtl_ext_func_func2
    ------------------
    UDF_NAME: dtl_ext_func_func3
  Node[id: 19, name: UPDATE_UDFS]
    UDF_NAME: dtl_tbm_tbl_out1
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn3[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn4[2 * recordNum] = {{1, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {1, 0, 1, 8, 9}, {1, 0, 1, 1, 1},
        {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");
    system("gmsysview count");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out3 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_009.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.patch.d显示声明block为0，新增含多个TBM表的规则，右表含patch中新增输入表投影，
 右边含原始.d、中间表、新增输入表投影，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf010";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    const char *expectHotPatchView = R"(
  VERSION: [v1.0.0]->[v2.0.0]
  UPGRADE_VERSION: 0
  DATALOG_UPGRADE_FETCH_SIZE: 1
  ENABLE_DATALOG_DML_WHEN_UPGRADING: 0
  BLOCK_MODE: 0
  REDO_OFF_CONFIG: 1
  PATCH_STATE: SUCCESS
  LOAD_PROCESS: 100%
  Node[id: 11, name: REDO_PROCESS]
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
    ------------------
    TABLE_NAME: inp5
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
    RULE_NAME: rN1
    ------------------
    RULE_NAME: rN2
  Node[id: 16, name: UPDATE_RULES]
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
    UDF_NAME: dtl_ext_func_func1
    ------------------
    UDF_NAME: dtl_ext_func_func2
    ------------------
    UDF_NAME: dtl_ext_func_func3
  Node[id: 19, name: UPDATE_UDFS]
    UDF_NAME: dtl_tbm_tbl_out1
    ------------------
    UDF_NAME: dtl_tbm_tbl_out2
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
  Node[id: 22, name: REDO_RELATED_TABLES]
  Node[id: 23, name: REDO_OUT_TABLES]
)";
    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 对输入表写入数据
    C5Int8T objIn5[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn6[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_010.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011对原始不同topo先进行block为0升级，使得upgradeVersion不同，patch.d显示声明block为0，新增含TBM表的规则，
 右表含原始.d输入表（upgradeVersion为1）和新增patch.d中新增的输入表含多个，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn5[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName3));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 对输入表写入数据
    C5Int8T objIn6[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn7[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn6, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_011.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.对原始不同topo先进行block为0升级，使得upgradeVersion不同，patch.d显示声明block为1，新增含TBM表的规则，
 右表含原始.d输入表和新增patch.d中新增的输入表含多个，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn5[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn4, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName3));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 对输入表写入数据
    C5Int8T objIn6[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn7[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn6, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_012.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.对原始不同topo先进行block为0升级，使得upgradeVersion不同，patch.d显示声明block为0，新增含TBM表的规则，
 右表含原始.d中间表（upgradeVersion为1）和新增patch.d中新增的输入表含多个，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn5[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn6[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName3));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 对输入表写入数据
    C5Int8T objIn7[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn8[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn8, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_013.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.对原始不同topo先进行block为0升级，使得upgradeVersion不同，patch.d显示声明block为1，新增含TBM表的规则，
 右表含原始.d中间表（upgradeVersion为0）和新增patch.d中新增的输入表含多个，加载升降级so，写入数据，预期正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn5[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn6[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName3));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 对输入表写入数据
    C5Int8T objIn7[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn8[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn8, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_014.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.对原始不同topo先进行block为0升级，使得upgradeVersion不同，patch.d显示声明block为0，
 新增含TBM表的规则，右表含原始.d输入表（upgradeVersion为1）和新增patch.d中新增的输入表含多个，加载升降级so，写入数据，预期正常，继续进行升级，block为0
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char patchSoName4[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char rollbackSoName4[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(patchSoName4, "%s/%s_patchV5.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName4, "%s/%s_rollbackV5.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn5[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn6[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn2, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn2, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载第4次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName4));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 3", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 对新增输入表inp6,写数据
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName4));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName3));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(2);
   // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 对输入表写入数据
    C5Int8T objIn7[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn8[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn8, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_015.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.对原始不同topo先进行block为0升级，使得upgradeVersion不同，patch.d显示声明block为0，新增含TBM表的规则，
 右表含原始.d输入表（upgradeVersion为1）和新增patch.d中新增的输入表含多个，加载升降级so，写入数据，预期正常，继续进行升级，block为1
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab006";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char patchSoName4[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char rollbackSoName4[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(patchSoName4, "%s/%s_patchV5.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName4, "%s/%s_rollbackV5.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");

    // 对输入表写入数据
    C5Int8T objIn5[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn6[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn2, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn2, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载第4次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName4));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
        system(g_command);
    }
    // 对新增输入表inp6,写数据
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName4));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName3));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(2);
   // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 对输入表写入数据
    C5Int8T objIn7[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn8[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn8, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_016.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.含namespace和precedence以及readonly，patch.d中，新增含tbm规则，降加升降级so，写入数据，查询正常
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char patchSoName4[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");
    system("gmsysview count");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 校验数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 对输入表写入数据
    C5Int8T objIn3[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn4, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp4", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp5", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
   // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 对输入表写入数据
    C5Int8T objIn5[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn6[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn6, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_017.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024041112047
/* ****************************************************************************
 Description  : 018.循环加载含REDO_OFF升降级so 2000次，无内存泄漏
**************************************************************************** */
TEST_F(dtladdrule_002_test1, DataLog_080_002_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;
    int32_t tryCnt = 0;
    int32_t tryMaxCnt = 3;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char patchSoName4[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 2000;
#endif

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");
    system("gmsysview count");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 校验数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 对输入表写入数据
    C5Int8T objIn3[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn4, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp4", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp5", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    sleep(10);
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    // 循环升级和降级补丁2 2000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2, NULL, false));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2, NULL, false));
    }

    while (tryCnt < tryMaxCnt) {
        sleep(10);
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!strcmp(tableShareMem01, tableShareMem02) && !strcmp(udfDynMem01, udfDynMem02) &&
            !strcmp(planCacheDynMem01, planCacheDynMem02)) {
                break;
            }
        tryCnt++;
    }

    AW_FUN_Log(LOG_DEBUG, "after Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tryCnt is %d", tryCnt);
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
    // 校验内存值
#ifndef ENV_RTOSV2X
    // 打印catalog视图，增加DFX信息
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO > DataLog_080_002_018.txt");
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);  // 可能不太准确
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 对输入表写入数据
    C5Int8T objIn5[recordNum] = {{1, 0, 10, 1, 5, 2, 3}, {1, 0, 11, 7, 8, 4, 4}, {2, 0, 12, 9, 10, 5, 10}};
    C3Int8T objIn6[recordNum] = {{1, 0, 10, 1, 5}, {1, 0, 11, 7, 8}, {2, 0, 12, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn6, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn5, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_080_002_018.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.block为0，循环加载升降级so2000次，内存无泄露；修改规则，查看plan cache无内存泄漏
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;
    int32_t tryCnt = 0;
    int32_t tryMaxCnt = 3;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 2000;
#endif

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema); 

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp4", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 校验数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    // 升级后，对输入表写入数据
    C5Int8T objIn3[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn4, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp4", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp4", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp5", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    sleep(10);
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    pthread_t thr_arr[1];
    // 循环升级和降级补丁1 2000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, NULL, false));
        pthread_create(&thr_arr[0], NULL, ThreadScanPatchView, (void *)soName);
        pthread_join(thr_arr[0], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, NULL, false));
        pthread_create(&thr_arr[0], NULL, ThreadScanPatchView, (void *)soName);
        pthread_join(thr_arr[0], NULL);
    }

    while (tryCnt < tryMaxCnt) {
        sleep(10);
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!strcmp(tableShareMem01, tableShareMem02) && !strcmp(udfDynMem01, udfDynMem02) &&
            !strcmp(planCacheDynMem01, planCacheDynMem02)) {
                break;
            }
        tryCnt++;
    }

    AW_FUN_Log(LOG_DEBUG, "after Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tryCnt is %d", tryCnt);
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
    // 校验内存值
#ifndef ENV_RTOSV2X
    // 打印catalog视图，增加DFX信息
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO > DataLog_080_002_019.txt");
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);  // 可能不太准确
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    system("gmsysview count");
    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.block为1，循环加载升降级so2000次，内存无泄露；修改规则，查看plan cache无内存泄漏
**************************************************************************** */
TEST_F(dtladdrule_002_test, DataLog_080_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab009";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;
    int32_t tryCnt = 0;
    int32_t tryMaxCnt = 3;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 2000;
#endif

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema); 

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C5Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1, 2, 3}, {1, 0, 1, 2, 3, 4, 4},  {2, 0, 2, 3, 5, 5, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp4", objIn2, recordNum, C5Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 校验数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 升级后，对输入表写入数据
    C5Int8T objIn3[recordNum] = {{1, 0, 4, 4, 5, 2, 3}, {1, 0, 6, 7, 8, 4, 4}, {2, 0, 8, 9, 10, 5, 10}};
    C3Int8T objIn4[recordNum] = {{1, 0, 4, 4, 5}, {1, 0, 6, 7, 8}, {2, 0, 8, 9, 10}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn4, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp3", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp4", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp4", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp5", objIn3, recordNum, C5Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    sleep(10);
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    pthread_t thr_arr[1];
    // 循环升级和降级补丁1 2000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, NULL, false));
        pthread_create(&thr_arr[0], NULL, ThreadScanPatchView, (void *)soName);
        pthread_join(thr_arr[0], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, NULL, false));
        pthread_create(&thr_arr[0], NULL, ThreadScanPatchView, (void *)soName);
        pthread_join(thr_arr[0], NULL);
    }

    while (tryCnt < tryMaxCnt) {
        sleep(10);
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!strcmp(tableShareMem01, tableShareMem02) && !strcmp(udfDynMem01, udfDynMem02) &&
            !strcmp(planCacheDynMem01, planCacheDynMem02)) {
                break;
            }
        tryCnt++;
    }

    AW_FUN_Log(LOG_DEBUG, "after Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tryCnt is %d", tryCnt);
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
    // 校验内存值
#ifndef ENV_RTOSV2X
    // 打印catalog视图，增加DFX信息
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO > DataLog_080_002_020.txt");
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);  // 可能不太准确
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    system("gmsysview count");
    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
