%table inpA1(a:int4,b:int4)
%table inpA2(a:int4,b:int4)
%table inpA3(a:int4,b:int4)
%table inpA4(a:int4,b:int4)
%table inpA5(a:int4,b:int4)
%table inpA6(a:int4,b:int4)
%table inpA7(a:int4,b:int4)
%table inpA8(a:int4,b:int4)
%table inpA9(a:int4,b:int4)
%table inpA10(a:int4,b:int4)
%table inpA11(a:int4,b:int4)
%table inpA12(a:int4,b:int4)
%table inpA13(a:int4,b:int4)
%table inpA14(a:int4,b:int4)
%table inpA15(a:int4,b:int4)
%table inpA16(a:int4,b:int4)
%table inpA17(a:int4,b:int4)
%table inpA18(a:int4,b:int4)
%table inpA19(a:int4,b:int4)
%table inpA20(a:int4,b:int4)
%table inpA21(a:int4,b:int4)
%table inpA22(a:int4,b:int4)
%table inpA23(a:int4,b:int4)
%table inpA24(a:int4,b:int4)
%table inpA25(a:int4,b:int4)
%table inpA26(a:int4,b:int4)
%table inpA27(a:int4,b:int4)
%table inpA28(a:int4,b:int4)
%table inpA29(a:int4,b:int4)
%table inpA30(a:int4,b:int4)
%table inpA31(a:int4,b:int4)
%table inpA32(a:int4,b:int4)
%table inpA33(a:int4,b:int4)
%table inpA34(a:int4,b:int4)
%table inpA35(a:int4,b:int4)
%table inpA36(a:int4,b:int4)
%table inpA37(a:int4,b:int4)
%table inpA38(a:int4,b:int4)
%table inpA39(a:int4,b:int4)
%table inpA40(a:int4,b:int4)
%table inpA41(a:int4,b:int4)
%table inpA42(a:int4,b:int4)
%table inpA43(a:int4,b:int4)
%table inpA44(a:int4,b:int4)
%table inpA45(a:int4,b:int4)
%table inpA46(a:int4,b:int4)
%table inpA47(a:int4,b:int4)
%table inpA48(a:int4,b:int4)
%table inpA49(a:int4,b:int4)
%table inpA50(a:int4,b:int4)
%table inpA51(a:int4,b:int4)
%table inpA52(a:int4,b:int4)
%table inpA53(a:int4,b:int4)
%table inpA54(a:int4,b:int4)
%table inpA55(a:int4,b:int4)
%table inpA56(a:int4,b:int4)
%table inpA57(a:int4,b:int4)
%table inpA58(a:int4,b:int4)
%table inpA59(a:int4,b:int4)
%table inpA60(a:int4,b:int4)
%table inpA61(a:int4,b:int4)
%table inpA62(a:int4,b:int4)
%table inpA63(a:int4,b:int4)
%table inpA64(a:int4,b:int4)
%table inpA65(a:int4,b:int4)
%table inpA66(a:int4,b:int4)
%table inpA67(a:int4,b:int4)
%table inpA68(a:int4,b:int4)
%table inpA69(a:int4,b:int4)
%table inpA70(a:int4,b:int4)
%table inpA71(a:int4,b:int4)
%table inpA72(a:int4,b:int4)
%table inpA73(a:int4,b:int4)
%table inpA74(a:int4,b:int4)
%table inpA75(a:int4,b:int4)
%table inpA76(a:int4,b:int4)
%table inpA77(a:int4,b:int4)
%table inpA78(a:int4,b:int4)
%table inpA79(a:int4,b:int4)
%table inpA80(a:int4,b:int4)
%table inpA81(a:int4,b:int4)
%table inpA82(a:int4,b:int4)
%table inpA83(a:int4,b:int4)
%table inpA84(a:int4,b:int4)
%table inpA85(a:int4,b:int4)
%table inpA86(a:int4,b:int4)
%table inpA87(a:int4,b:int4)
%table inpA88(a:int4,b:int4)
%table inpA89(a:int4,b:int4)
%table inpA90(a:int4,b:int4)
%table inpA91(a:int4,b:int4)
%table inpA92(a:int4,b:int4)
%table inpA93(a:int4,b:int4)
%table inpA94(a:int4,b:int4)
%table inpA95(a:int4,b:int4)
%table inpA96(a:int4,b:int4)
%table inpA97(a:int4,b:int4)
%table inpA98(a:int4,b:int4)
%table inpA99(a:int4,b:int4)
%table inpA100(a:int4,b:int4)
%table inpA101(a:int4,b:int4)
%table inpA102(a:int4,b:int4)
%table inpA103(a:int4,b:int4)
%table inpA104(a:int4,b:int4)
%table inpA105(a:int4,b:int4)
%table inpA106(a:int4,b:int4)
%table inpA107(a:int4,b:int4)
%table inpA108(a:int4,b:int4)
%table inpA109(a:int4,b:int4)
%table inpA110(a:int4,b:int4)
%table inpA111(a:int4,b:int4)
%table inpA112(a:int4,b:int4)
%table inpA113(a:int4,b:int4)
%table inpA114(a:int4,b:int4)
%table inpA115(a:int4,b:int4)
%table inpA116(a:int4,b:int4)
%table inpA117(a:int4,b:int4)
%table inpA118(a:int4,b:int4)
%table inpA119(a:int4,b:int4)
%table inpA120(a:int4,b:int4)
%table inpA121(a:int4,b:int4)
%table inpA122(a:int4,b:int4)
%table inpA123(a:int4,b:int4)
%table inpA124(a:int4,b:int4)
%table inpA125(a:int4,b:int4)
%table inpA126(a:int4,b:int4)
%table inpA127(a:int4,b:int4)
%table inpA128(a:int4,b:int4)
%table inpA129(a:int4,b:int4)
%table inpA130(a:int4,b:int4)
%table inpA131(a:int4,b:int4)
%table inpA132(a:int4,b:int4)
%table inpA133(a:int4,b:int4)
%table inpA134(a:int4,b:int4)
%table inpA135(a:int4,b:int4)
%table inpA136(a:int4,b:int4)
%table inpA137(a:int4,b:int4)
%table inpA138(a:int4,b:int4)
%table inpA139(a:int4,b:int4)
%table inpA140(a:int4,b:int4)
%table inpA141(a:int4,b:int4)
%table inpA142(a:int4,b:int4)
%table inpA143(a:int4,b:int4)
%table inpA144(a:int4,b:int4)
%table inpA145(a:int4,b:int4)
%table inpA146(a:int4,b:int4)
%table inpA147(a:int4,b:int4)
%table inpA148(a:int4,b:int4)
%table inpA149(a:int4,b:int4)
%table inpA150(a:int4,b:int4)
%table inpA151(a:int4,b:int4)
%table inpA152(a:int4,b:int4)
%table inpA153(a:int4,b:int4)
%table inpA154(a:int4,b:int4)
%table inpA155(a:int4,b:int4)
%table inpA156(a:int4,b:int4)
%table inpA157(a:int4,b:int4)
%table inpA158(a:int4,b:int4)
%table inpA159(a:int4,b:int4)
%table inpA160(a:int4,b:int4)
%table inpA161(a:int4,b:int4)
%table inpA162(a:int4,b:int4)
%table inpA163(a:int4,b:int4)
%table inpA164(a:int4,b:int4)
%table inpA165(a:int4,b:int4)
%table inpA166(a:int4,b:int4)
%table inpA167(a:int4,b:int4)
%table inpA168(a:int4,b:int4)
%table inpA169(a:int4,b:int4)
%table inpA170(a:int4,b:int4)
%table inpA171(a:int4,b:int4)
%table inpA172(a:int4,b:int4)
%table inpA173(a:int4,b:int4)
%table inpA174(a:int4,b:int4)
%table inpA175(a:int4,b:int4)
%table inpA176(a:int4,b:int4)
%table inpA177(a:int4,b:int4)
%table inpA178(a:int4,b:int4)
%table inpA179(a:int4,b:int4)
%table inpA180(a:int4,b:int4)
%table inpA181(a:int4,b:int4)
%table inpA182(a:int4,b:int4)
%table inpA183(a:int4,b:int4)
%table inpA184(a:int4,b:int4)
%table inpA185(a:int4,b:int4)
%table inpA186(a:int4,b:int4)
%table inpA187(a:int4,b:int4)
%table inpA188(a:int4,b:int4)
%table inpA189(a:int4,b:int4)
%table inpA190(a:int4,b:int4)
%table inpA191(a:int4,b:int4)
%table inpA192(a:int4,b:int4)
%table inpA193(a:int4,b:int4)
%table inpA194(a:int4,b:int4)
%table inpA195(a:int4,b:int4)
%table inpA196(a:int4,b:int4)
%table inpA197(a:int4,b:int4)
%table inpA198(a:int4,b:int4)
%table inpA199(a:int4,b:int4)
%table inpA200(a:int4,b:int4)
%table inpA201(a:int4,b:int4)
%table inpA202(a:int4,b:int4)
%table inpA203(a:int4,b:int4)
%table inpA204(a:int4,b:int4)
%table inpA205(a:int4,b:int4)
%table inpA206(a:int4,b:int4)
%table inpA207(a:int4,b:int4)
%table inpA208(a:int4,b:int4)
%table inpA209(a:int4,b:int4)
%table inpA210(a:int4,b:int4)
%table inpA211(a:int4,b:int4)
%table inpA212(a:int4,b:int4)
%table inpA213(a:int4,b:int4)
%table inpA214(a:int4,b:int4)
%table inpA215(a:int4,b:int4)
%table inpA216(a:int4,b:int4)
%table inpA217(a:int4,b:int4)
%table inpA218(a:int4,b:int4)
%table inpA219(a:int4,b:int4)
%table inpA220(a:int4,b:int4)
%table inpA221(a:int4,b:int4)
%table inpA222(a:int4,b:int4)
%table inpA223(a:int4,b:int4)
%table inpA224(a:int4,b:int4)
%table inpA225(a:int4,b:int4)
%table inpA226(a:int4,b:int4)
%table inpA227(a:int4,b:int4)
%table inpA228(a:int4,b:int4)
%table inpA229(a:int4,b:int4)
%table inpA230(a:int4,b:int4)
%table inpA231(a:int4,b:int4)
%table inpA232(a:int4,b:int4)
%table inpA233(a:int4,b:int4)
%table inpA234(a:int4,b:int4)
%table inpA235(a:int4,b:int4)
%table inpA236(a:int4,b:int4)
%table inpA237(a:int4,b:int4)
%table inpA238(a:int4,b:int4)
%table inpA239(a:int4,b:int4)
%table inpA240(a:int4,b:int4)
%table inpA241(a:int4,b:int4)
%table inpA242(a:int4,b:int4)
%table inpA243(a:int4,b:int4)
%table inpA244(a:int4,b:int4)
%table inpA245(a:int4,b:int4)
%table inpA246(a:int4,b:int4)
%table inpA247(a:int4,b:int4)
%table inpA248(a:int4,b:int4)
%table inpA249(a:int4,b:int4)
%table inpA250(a:int4,b:int4)
%table inpA251(a:int4,b:int4)
%table inpA252(a:int4,b:int4)
%table inpA253(a:int4,b:int4)
%table inpA254(a:int4,b:int4)
%table inpA255(a:int4,b:int4)
%table inpA256(a:int4,b:int4)
%table inpA257(a:int4,b:int4)
%table inpA258(a:int4,b:int4)
%table inpA259(a:int4,b:int4)
%table inpA260(a:int4,b:int4)
%table inpA261(a:int4,b:int4)
%table inpA262(a:int4,b:int4)
%table inpA263(a:int4,b:int4)
%table inpA264(a:int4,b:int4)
%table inpA265(a:int4,b:int4)
%table inpA266(a:int4,b:int4)
%table inpA267(a:int4,b:int4)
%table inpA268(a:int4,b:int4)
%table inpA269(a:int4,b:int4)
%table inpA270(a:int4,b:int4)
%table inpA271(a:int4,b:int4)
%table inpA272(a:int4,b:int4)
%table inpA273(a:int4,b:int4)
%table inpA274(a:int4,b:int4)
%table inpA275(a:int4,b:int4)
%table inpA276(a:int4,b:int4)
%table inpA277(a:int4,b:int4)
%table inpA278(a:int4,b:int4)
%table inpA279(a:int4,b:int4)
%table inpA280(a:int4,b:int4)
%table inpA281(a:int4,b:int4)
%table inpA282(a:int4,b:int4)
%table inpA283(a:int4,b:int4)
%table inpA284(a:int4,b:int4)
%table inpA285(a:int4,b:int4)
%table inpA286(a:int4,b:int4)
%table inpA287(a:int4,b:int4)
%table inpA288(a:int4,b:int4)
%table inpA289(a:int4,b:int4)
%table inpA290(a:int4,b:int4)
%table inpA291(a:int4,b:int4)
%table inpA292(a:int4,b:int4)
%table inpA293(a:int4,b:int4)
%table inpA294(a:int4,b:int4)
%table inpA295(a:int4,b:int4)
%table inpA296(a:int4,b:int4)
%table inpA297(a:int4,b:int4)
%table inpA298(a:int4,b:int4)
%table inpA299(a:int4,b:int4)
%table inpA300(a:int4,b:int4)
%table inpA301(a:int4,b:int4)
%table inpA302(a:int4,b:int4)
%table inpA303(a:int4,b:int4)
%table inpA304(a:int4,b:int4)
%table inpA305(a:int4,b:int4)
%table inpA306(a:int4,b:int4)
%table inpA307(a:int4,b:int4)
%table inpA308(a:int4,b:int4)
%table inpA309(a:int4,b:int4)
%table inpA310(a:int4,b:int4)
%table inpA311(a:int4,b:int4)
%table inpA312(a:int4,b:int4)
%table inpA313(a:int4,b:int4)
%table inpA314(a:int4,b:int4)
%table inpA315(a:int4,b:int4)
%table inpA316(a:int4,b:int4)
%table inpA317(a:int4,b:int4)
%table inpA318(a:int4,b:int4)
%table inpA319(a:int4,b:int4)
%table inpA320(a:int4,b:int4)
%table inpA321(a:int4,b:int4)
%table inpA322(a:int4,b:int4)
%table inpA323(a:int4,b:int4)
%table inpA324(a:int4,b:int4)
%table inpA325(a:int4,b:int4)
%table inpA326(a:int4,b:int4)
%table inpA327(a:int4,b:int4)
%table inpA328(a:int4,b:int4)
%table inpA329(a:int4,b:int4)
%table inpA330(a:int4,b:int4)
%table inpA331(a:int4,b:int4)
%table inpA332(a:int4,b:int4)
%table inpA333(a:int4,b:int4)
%table inpA334(a:int4,b:int4)
%table inpA335(a:int4,b:int4)
%table inpA336(a:int4,b:int4)
%table inpA337(a:int4,b:int4)
%table inpA338(a:int4,b:int4)
%table inpA339(a:int4,b:int4)
%table inpA340(a:int4,b:int4)
%table inpA341(a:int4,b:int4)
%table inpA342(a:int4,b:int4)
%table inpA343(a:int4,b:int4)
%table inpA344(a:int4,b:int4)
%table inpA345(a:int4,b:int4)
%table inpA346(a:int4,b:int4)
%table inpA347(a:int4,b:int4)
%table inpA348(a:int4,b:int4)
%table inpA349(a:int4,b:int4)
%table inpA350(a:int4,b:int4)
%table inpA351(a:int4,b:int4)
%table inpA352(a:int4,b:int4)
%table inpA353(a:int4,b:int4)
%table inpA354(a:int4,b:int4)
%table inpA355(a:int4,b:int4)
%table inpA356(a:int4,b:int4)
%table inpA357(a:int4,b:int4)
%table inpA358(a:int4,b:int4)
%table inpA359(a:int4,b:int4)
%table inpA360(a:int4,b:int4)
%table inpA361(a:int4,b:int4)
%table inpA362(a:int4,b:int4)
%table inpA363(a:int4,b:int4)
%table inpA364(a:int4,b:int4)
%table inpA365(a:int4,b:int4)
%table inpA366(a:int4,b:int4)
%table inpA367(a:int4,b:int4)
%table inpA368(a:int4,b:int4)
%table inpA369(a:int4,b:int4)
%table inpA370(a:int4,b:int4)
%table inpA371(a:int4,b:int4)
%table inpA372(a:int4,b:int4)
%table inpA373(a:int4,b:int4)
%table inpA374(a:int4,b:int4)
%table inpA375(a:int4,b:int4)
%table inpA376(a:int4,b:int4)
%table inpA377(a:int4,b:int4)
%table inpA378(a:int4,b:int4)
%table inpA379(a:int4,b:int4)
%table inpA380(a:int4,b:int4)
%table inpA381(a:int4,b:int4)
%table inpA382(a:int4,b:int4)
%table inpA383(a:int4,b:int4)
%table inpA384(a:int4,b:int4)
%table inpA385(a:int4,b:int4)
%table inpA386(a:int4,b:int4)
%table inpA387(a:int4,b:int4)
%table inpA388(a:int4,b:int4)
%table inpA389(a:int4,b:int4)
%table inpA390(a:int4,b:int4)
%table inpA391(a:int4,b:int4)
%table inpA392(a:int4,b:int4)
%table inpA393(a:int4,b:int4)
%table inpA394(a:int4,b:int4)
%table inpA395(a:int4,b:int4)
%table inpA396(a:int4,b:int4)
%table inpA397(a:int4,b:int4)
%table inpA398(a:int4,b:int4)
%table inpA399(a:int4,b:int4)
%table inpA400(a:int4,b:int4)
%table inpA401(a:int4,b:int4)
%table inpA402(a:int4,b:int4)
%table inpA403(a:int4,b:int4)
%table inpA404(a:int4,b:int4)
%table inpA405(a:int4,b:int4)
%table inpA406(a:int4,b:int4)
%table inpA407(a:int4,b:int4)
%table inpA408(a:int4,b:int4)
%table inpA409(a:int4,b:int4)
%table inpA410(a:int4,b:int4)
%table inpA411(a:int4,b:int4)
%table inpA412(a:int4,b:int4)
%table inpA413(a:int4,b:int4)
%table inpA414(a:int4,b:int4)
%table inpA415(a:int4,b:int4)
%table inpA416(a:int4,b:int4)
%table inpA417(a:int4,b:int4)
%table inpA418(a:int4,b:int4)
%table inpA419(a:int4,b:int4)
%table inpA420(a:int4,b:int4)
%table inpA421(a:int4,b:int4)
%table inpA422(a:int4,b:int4)
%table inpA423(a:int4,b:int4)
%table inpA424(a:int4,b:int4)
%table inpA425(a:int4,b:int4)
%table inpA426(a:int4,b:int4)
%table inpA427(a:int4,b:int4)
%table inpA428(a:int4,b:int4)
%table inpA429(a:int4,b:int4)
%table inpA430(a:int4,b:int4)
%table inpA431(a:int4,b:int4)
%table inpA432(a:int4,b:int4)
%table inpA433(a:int4,b:int4)
%table inpA434(a:int4,b:int4)
%table inpA435(a:int4,b:int4)
%table inpA436(a:int4,b:int4)
%table inpA437(a:int4,b:int4)
%table inpA438(a:int4,b:int4)
%table inpA439(a:int4,b:int4)
%table inpA440(a:int4,b:int4)
%table inpA441(a:int4,b:int4)
%table inpA442(a:int4,b:int4)
%table inpA443(a:int4,b:int4)
%table inpA444(a:int4,b:int4)
%table inpA445(a:int4,b:int4)
%table inpA446(a:int4,b:int4)
%table inpA447(a:int4,b:int4)
%table inpA448(a:int4,b:int4)
%table inpA449(a:int4,b:int4)
%table inpA450(a:int4,b:int4)
%table inpA451(a:int4,b:int4)
%table inpA452(a:int4,b:int4)
%table inpA453(a:int4,b:int4)
%table inpA454(a:int4,b:int4)
%table inpA455(a:int4,b:int4)
%table inpA456(a:int4,b:int4)
%table inpA457(a:int4,b:int4)
%table inpA458(a:int4,b:int4)
%table inpA459(a:int4,b:int4)
%table inpA460(a:int4,b:int4)
%table inpA461(a:int4,b:int4)
%table inpA462(a:int4,b:int4)
%table inpA463(a:int4,b:int4)
%table inpA464(a:int4,b:int4)
%table inpA465(a:int4,b:int4)
%table inpA466(a:int4,b:int4)
%table inpA467(a:int4,b:int4)
%table inpA468(a:int4,b:int4)
%table inpA469(a:int4,b:int4)
%table inpA470(a:int4,b:int4)
%table inpA471(a:int4,b:int4)
%table inpA472(a:int4,b:int4)
%table inpA473(a:int4,b:int4)
%table inpA474(a:int4,b:int4)
%table inpA475(a:int4,b:int4)
%table inpA476(a:int4,b:int4)
%table inpA477(a:int4,b:int4)
%table inpA478(a:int4,b:int4)
%table inpA479(a:int4,b:int4)
%table inpA480(a:int4,b:int4)
%table inpA481(a:int4,b:int4)
%table inpA482(a:int4,b:int4)
%table inpA483(a:int4,b:int4)
%table inpA484(a:int4,b:int4)
%table inpA485(a:int4,b:int4)
%table inpA486(a:int4,b:int4)
%table inpA487(a:int4,b:int4)
%table inpA488(a:int4,b:int4)
%table inpA489(a:int4,b:int4)
%table inpA490(a:int4,b:int4)
%table inpA491(a:int4,b:int4)
%table inpA492(a:int4,b:int4)
%table inpA493(a:int4,b:int4)
%table inpA494(a:int4,b:int4)
%table inpA495(a:int4,b:int4)
%table inpA496(a:int4,b:int4)
%table inpA497(a:int4,b:int4)
%table inpA498(a:int4,b:int4)
%table inpA499(a:int4,b:int4)
%table inpA500(a:int4,b:int4)
%table inpA501(a:int4,b:int4)
%table inpA502(a:int4,b:int4)
%table inpA503(a:int4,b:int4)
%table inpA504(a:int4,b:int4)
%table inpA505(a:int4,b:int4)
%table inpA506(a:int4,b:int4)
%table inpA507(a:int4,b:int4)
%table inpA508(a:int4,b:int4)
%table inpA509(a:int4,b:int4)
%table inpA510(a:int4,b:int4)
%table inpA511(a:int4,b:int4)
%table inpA512(a:int4,b:int4)
%table inpA513(a:int4,b:int4)
%table inpA514(a:int4,b:int4)
%table inpA515(a:int4,b:int4)
%table inpA516(a:int4,b:int4)
%table inpA517(a:int4,b:int4)
%table inpA518(a:int4,b:int4)
%table inpA519(a:int4,b:int4)
%table inpA520(a:int4,b:int4)
%table inpA521(a:int4,b:int4)
%table inpA522(a:int4,b:int4)
%table inpA523(a:int4,b:int4)
%table inpA524(a:int4,b:int4)
%table inpA525(a:int4,b:int4)
%table inpA526(a:int4,b:int4)
%table inpA527(a:int4,b:int4)
%table inpA528(a:int4,b:int4)
%table inpA529(a:int4,b:int4)
%table inpA530(a:int4,b:int4)
%table inpA531(a:int4,b:int4)
%table inpA532(a:int4,b:int4)
%table inpA533(a:int4,b:int4)
%table inpA534(a:int4,b:int4)
%table inpA535(a:int4,b:int4)
%table inpA536(a:int4,b:int4)
%table inpA537(a:int4,b:int4)
%table inpA538(a:int4,b:int4)
%table inpA539(a:int4,b:int4)
%table inpA540(a:int4,b:int4)
%table inpA541(a:int4,b:int4)
%table inpA542(a:int4,b:int4)
%table inpA543(a:int4,b:int4)
%table inpA544(a:int4,b:int4)
%table inpA545(a:int4,b:int4)
%table inpA546(a:int4,b:int4)
%table inpA547(a:int4,b:int4)
%table inpA548(a:int4,b:int4)
%table inpA549(a:int4,b:int4)
%table inpA550(a:int4,b:int4)
%table inpA551(a:int4,b:int4)
%table inpA552(a:int4,b:int4)
%table inpA553(a:int4,b:int4)
%table inpA554(a:int4,b:int4)
%table inpA555(a:int4,b:int4)
%table inpA556(a:int4,b:int4)
%table inpA557(a:int4,b:int4)
%table inpA558(a:int4,b:int4)
%table inpA559(a:int4,b:int4)
%table inpA560(a:int4,b:int4)
%table inpA561(a:int4,b:int4)
%table inpA562(a:int4,b:int4)
%table inpA563(a:int4,b:int4)
%table inpA564(a:int4,b:int4)
%table inpA565(a:int4,b:int4)
%table inpA566(a:int4,b:int4)
%table inpA567(a:int4,b:int4)
%table inpA568(a:int4,b:int4)
%table inpA569(a:int4,b:int4)
%table inpA570(a:int4,b:int4)
%table inpA571(a:int4,b:int4)
%table inpA572(a:int4,b:int4)
%table inpA573(a:int4,b:int4)
%table inpA574(a:int4,b:int4)
%table inpA575(a:int4,b:int4)
%table inpA576(a:int4,b:int4)
%table inpA577(a:int4,b:int4)
%table inpA578(a:int4,b:int4)
%table inpA579(a:int4,b:int4)
%table inpA580(a:int4,b:int4)
%table inpA581(a:int4,b:int4)
%table inpA582(a:int4,b:int4)
%table inpA583(a:int4,b:int4)
%table inpA584(a:int4,b:int4)
%table inpA585(a:int4,b:int4)
%table inpA586(a:int4,b:int4)
%table inpA587(a:int4,b:int4)
%table inpA588(a:int4,b:int4)
%table inpA589(a:int4,b:int4)
%table inpA590(a:int4,b:int4)
%table inpA591(a:int4,b:int4)
%table inpA592(a:int4,b:int4)
%table inpA593(a:int4,b:int4)
%table inpA594(a:int4,b:int4)
%table inpA595(a:int4,b:int4)
%table inpA596(a:int4,b:int4)
%table inpA597(a:int4,b:int4)
%table inpA598(a:int4,b:int4)
%table inpA599(a:int4,b:int4)
%table inpA600(a:int4,b:int4)
%table inpA601(a:int4,b:int4)
%table inpA602(a:int4,b:int4)
%table inpA603(a:int4,b:int4)
%table inpA604(a:int4,b:int4)
%table inpA605(a:int4,b:int4)
%table inpA606(a:int4,b:int4)
%table inpA607(a:int4,b:int4)
%table inpA608(a:int4,b:int4)
%table inpA609(a:int4,b:int4)
%table inpA610(a:int4,b:int4)
%table inpA611(a:int4,b:int4)
%table inpA612(a:int4,b:int4)
%table inpA613(a:int4,b:int4)
%table inpA614(a:int4,b:int4)
%table inpA615(a:int4,b:int4)
%table inpA616(a:int4,b:int4)
%table inpA617(a:int4,b:int4)
%table inpA618(a:int4,b:int4)
%table inpA619(a:int4,b:int4)
%table inpA620(a:int4,b:int4)
%table inpA621(a:int4,b:int4)
%table inpA622(a:int4,b:int4)
%table inpA623(a:int4,b:int4)
%table inpA624(a:int4,b:int4)
%table inpA625(a:int4,b:int4)
%table inpA626(a:int4,b:int4)
%table inpA627(a:int4,b:int4)
%table inpA628(a:int4,b:int4)
%table inpA629(a:int4,b:int4)
%table inpA630(a:int4,b:int4)
%table inpA631(a:int4,b:int4)
%table inpA632(a:int4,b:int4)
%table inpA633(a:int4,b:int4)
%table inpA634(a:int4,b:int4)
%table inpA635(a:int4,b:int4)
%table inpA636(a:int4,b:int4)
%table inpA637(a:int4,b:int4)
%table inpA638(a:int4,b:int4)
%table inpA639(a:int4,b:int4)
%table inpA640(a:int4,b:int4)
%table inpA641(a:int4,b:int4)
%table inpA642(a:int4,b:int4)
%table inpA643(a:int4,b:int4)
%table inpA644(a:int4,b:int4)
%table inpA645(a:int4,b:int4)
%table inpA646(a:int4,b:int4)
%table inpA647(a:int4,b:int4)
%table inpA648(a:int4,b:int4)
%table inpA649(a:int4,b:int4)
%table inpA650(a:int4,b:int4)
%table inpA651(a:int4,b:int4)
%table inpA652(a:int4,b:int4)
%table inpA653(a:int4,b:int4)
%table inpA654(a:int4,b:int4)
%table inpA655(a:int4,b:int4)
%table inpA656(a:int4,b:int4)
%table inpA657(a:int4,b:int4)
%table inpA658(a:int4,b:int4)
%table inpA659(a:int4,b:int4)
%table inpA660(a:int4,b:int4)
%table inpA661(a:int4,b:int4)
%table inpA662(a:int4,b:int4)
%table inpA663(a:int4,b:int4)
%table inpA664(a:int4,b:int4)
%table inpA665(a:int4,b:int4)
%table inpA666(a:int4,b:int4)
%table inpA667(a:int4,b:int4)
%table inpA668(a:int4,b:int4)
%table inpA669(a:int4,b:int4)
%table inpA670(a:int4,b:int4)
%table inpA671(a:int4,b:int4)
%table inpA672(a:int4,b:int4)
%table inpA673(a:int4,b:int4)
%table inpA674(a:int4,b:int4)
%table inpA675(a:int4,b:int4)
%table inpA676(a:int4,b:int4)
%table inpA677(a:int4,b:int4)
%table inpA678(a:int4,b:int4)
%table inpA679(a:int4,b:int4)
%table inpA680(a:int4,b:int4)
%table inpA681(a:int4,b:int4)
%table inpA682(a:int4,b:int4)
%table inpA683(a:int4,b:int4)
%table inpA684(a:int4,b:int4)
%table inpA685(a:int4,b:int4)
%table inpA686(a:int4,b:int4)
%table inpA687(a:int4,b:int4)
%table inpA688(a:int4,b:int4)
%table inpA689(a:int4,b:int4)
%table inpA690(a:int4,b:int4)
%table inpA691(a:int4,b:int4)
%table inpA692(a:int4,b:int4)
%table inpA693(a:int4,b:int4)
%table inpA694(a:int4,b:int4)
%table inpA695(a:int4,b:int4)
%table inpA696(a:int4,b:int4)
%table inpA697(a:int4,b:int4)
%table inpA698(a:int4,b:int4)
%table inpA699(a:int4,b:int4)
%table inpA700(a:int4,b:int4)
%table inpA701(a:int4,b:int4)
%table inpA702(a:int4,b:int4)
%table inpA703(a:int4,b:int4)
%table inpA704(a:int4,b:int4)
%table inpA705(a:int4,b:int4)
%table inpA706(a:int4,b:int4)
%table inpA707(a:int4,b:int4)
%table inpA708(a:int4,b:int4)
%table inpA709(a:int4,b:int4)
%table inpA710(a:int4,b:int4)
%table inpA711(a:int4,b:int4)
%table inpA712(a:int4,b:int4)
%table inpA713(a:int4,b:int4)
%table inpA714(a:int4,b:int4)
%table inpA715(a:int4,b:int4)
%table inpA716(a:int4,b:int4)
%table inpA717(a:int4,b:int4)
%table inpA718(a:int4,b:int4)
%table inpA719(a:int4,b:int4)
%table inpA720(a:int4,b:int4)
%table inpA721(a:int4,b:int4)
%table inpA722(a:int4,b:int4)
%table inpA723(a:int4,b:int4)
%table inpA724(a:int4,b:int4)
%table inpA725(a:int4,b:int4)
%table inpA726(a:int4,b:int4)
%table inpA727(a:int4,b:int4)
%table inpA728(a:int4,b:int4)
%table inpA729(a:int4,b:int4)
%table inpA730(a:int4,b:int4)
%table inpA731(a:int4,b:int4)
%table inpA732(a:int4,b:int4)
%table inpA733(a:int4,b:int4)
%table inpA734(a:int4,b:int4)
%table inpA735(a:int4,b:int4)
%table inpA736(a:int4,b:int4)
%table inpA737(a:int4,b:int4)
%table inpA738(a:int4,b:int4)
%table inpA739(a:int4,b:int4)
%table inpA740(a:int4,b:int4)
%table inpA741(a:int4,b:int4)
%table inpA742(a:int4,b:int4)
%table inpA743(a:int4,b:int4)
%table inpA744(a:int4,b:int4)
%table inpA745(a:int4,b:int4)
%table inpA746(a:int4,b:int4)
%table inpA747(a:int4,b:int4)
%table inpA748(a:int4,b:int4)
%table inpA749(a:int4,b:int4)
%table inpA750(a:int4,b:int4)
%table inpA751(a:int4,b:int4)
%table inpA752(a:int4,b:int4)
%table inpA753(a:int4,b:int4)
%table inpA754(a:int4,b:int4)
%table inpA755(a:int4,b:int4)
%table inpA756(a:int4,b:int4)
%table inpA757(a:int4,b:int4)
%table inpA758(a:int4,b:int4)
%table inpA759(a:int4,b:int4)
%table inpA760(a:int4,b:int4)
%table inpA761(a:int4,b:int4)
%table inpA762(a:int4,b:int4)
%table inpA763(a:int4,b:int4)
%table inpA764(a:int4,b:int4)
%table inpA765(a:int4,b:int4)
%table inpA766(a:int4,b:int4)
%table inpA767(a:int4,b:int4)
%table inpA768(a:int4,b:int4)
%table inpA769(a:int4,b:int4)
%table inpA770(a:int4,b:int4)
%table inpA771(a:int4,b:int4)
%table inpA772(a:int4,b:int4)
%table inpA773(a:int4,b:int4)
%table inpA774(a:int4,b:int4)
%table inpA775(a:int4,b:int4)
%table inpA776(a:int4,b:int4)
%table inpA777(a:int4,b:int4)
%table inpA778(a:int4,b:int4)
%table inpA779(a:int4,b:int4)
%table inpA780(a:int4,b:int4)
%table inpA781(a:int4,b:int4)
%table inpA782(a:int4,b:int4)
%table inpA783(a:int4,b:int4)
%table inpA784(a:int4,b:int4)
%table inpA785(a:int4,b:int4)
%table inpA786(a:int4,b:int4)
%table inpA787(a:int4,b:int4)
%table inpA788(a:int4,b:int4)
%table inpA789(a:int4,b:int4)
%table inpA790(a:int4,b:int4)
%table inpA791(a:int4,b:int4)
%table inpA792(a:int4,b:int4)
%table inpA793(a:int4,b:int4)
%table inpA794(a:int4,b:int4)
%table inpA795(a:int4,b:int4)
%table inpA796(a:int4,b:int4)
%table inpA797(a:int4,b:int4)
%table inpA798(a:int4,b:int4)
%table inpA799(a:int4,b:int4)
%table inpA800(a:int4,b:int4)
%table inpA801(a:int4,b:int4)
%table inpA802(a:int4,b:int4)
%table inpA803(a:int4,b:int4)
%table inpA804(a:int4,b:int4)
%table inpA805(a:int4,b:int4)
%table inpA806(a:int4,b:int4)
%table inpA807(a:int4,b:int4)
%table inpA808(a:int4,b:int4)
%table inpA809(a:int4,b:int4)
%table inpA810(a:int4,b:int4)
%table inpA811(a:int4,b:int4)
%table inpA812(a:int4,b:int4)
%table inpA813(a:int4,b:int4)
%table inpA814(a:int4,b:int4)
%table inpA815(a:int4,b:int4)
%table inpA816(a:int4,b:int4)
%table inpA817(a:int4,b:int4)
%table inpA818(a:int4,b:int4)
%table inpA819(a:int4,b:int4)
%table inpA820(a:int4,b:int4)
%table inpA821(a:int4,b:int4)
%table inpA822(a:int4,b:int4)
%table inpA823(a:int4,b:int4)
%table inpA824(a:int4,b:int4)
%table inpA825(a:int4,b:int4)
%table inpA826(a:int4,b:int4)
%table inpA827(a:int4,b:int4)
%table inpA828(a:int4,b:int4)
%table inpA829(a:int4,b:int4)
%table inpA830(a:int4,b:int4)
%table inpA831(a:int4,b:int4)
%table inpA832(a:int4,b:int4)
%table inpA833(a:int4,b:int4)
%table inpA834(a:int4,b:int4)
%table inpA835(a:int4,b:int4)
%table inpA836(a:int4,b:int4)
%table inpA837(a:int4,b:int4)
%table inpA838(a:int4,b:int4)
%table inpA839(a:int4,b:int4)
%table inpA840(a:int4,b:int4)
%table inpA841(a:int4,b:int4)
%table inpA842(a:int4,b:int4)
%table inpA843(a:int4,b:int4)
%table inpA844(a:int4,b:int4)
%table inpA845(a:int4,b:int4)
%table inpA846(a:int4,b:int4)
%table inpA847(a:int4,b:int4)
%table inpA848(a:int4,b:int4)
%table inpA849(a:int4,b:int4)
%table inpA850(a:int4,b:int4)
%table inpA851(a:int4,b:int4)
%table inpA852(a:int4,b:int4)
%table inpA853(a:int4,b:int4)
%table inpA854(a:int4,b:int4)
%table inpA855(a:int4,b:int4)
%table inpA856(a:int4,b:int4)
%table inpA857(a:int4,b:int4)
%table inpA858(a:int4,b:int4)
%table inpA859(a:int4,b:int4)
%table inpA860(a:int4,b:int4)
%table inpA861(a:int4,b:int4)
%table inpA862(a:int4,b:int4)
%table inpA863(a:int4,b:int4)
%table inpA864(a:int4,b:int4)
%table inpA865(a:int4,b:int4)
%table inpA866(a:int4,b:int4)
%table inpA867(a:int4,b:int4)
%table inpA868(a:int4,b:int4)
%table inpA869(a:int4,b:int4)
%table inpA870(a:int4,b:int4)
%table inpA871(a:int4,b:int4)
%table inpA872(a:int4,b:int4)
%table inpA873(a:int4,b:int4)
%table inpA874(a:int4,b:int4)
%table inpA875(a:int4,b:int4)
%table inpA876(a:int4,b:int4)
%table inpA877(a:int4,b:int4)
%table inpA878(a:int4,b:int4)
%table inpA879(a:int4,b:int4)
%table inpA880(a:int4,b:int4)
%table inpA881(a:int4,b:int4)
%table inpA882(a:int4,b:int4)
%table inpA883(a:int4,b:int4)
%table inpA884(a:int4,b:int4)
%table inpA885(a:int4,b:int4)
%table inpA886(a:int4,b:int4)
%table inpA887(a:int4,b:int4)
%table inpA888(a:int4,b:int4)
%table inpA889(a:int4,b:int4)
%table inpA890(a:int4,b:int4)
%table inpA891(a:int4,b:int4)
%table inpA892(a:int4,b:int4)
%table inpA893(a:int4,b:int4)
%table inpA894(a:int4,b:int4)
%table inpA895(a:int4,b:int4)
%table inpA896(a:int4,b:int4)
%table inpA897(a:int4,b:int4)
%table inpA898(a:int4,b:int4)
%table inpA899(a:int4,b:int4)
%table inpA900(a:int4,b:int4)
%table inpA901(a:int4,b:int4)
%table inpA902(a:int4,b:int4)
%table inpA903(a:int4,b:int4)
%table inpA904(a:int4,b:int4)
%table inpA905(a:int4,b:int4)
%table inpA906(a:int4,b:int4)
%table inpA907(a:int4,b:int4)
%table inpA908(a:int4,b:int4)
%table inpA909(a:int4,b:int4)
%table inpA910(a:int4,b:int4)
%table inpA911(a:int4,b:int4)
%table inpA912(a:int4,b:int4)
%table inpA913(a:int4,b:int4)
%table inpA914(a:int4,b:int4)
%table inpA915(a:int4,b:int4)
%table inpA916(a:int4,b:int4)
%table inpA917(a:int4,b:int4)
%table inpA918(a:int4,b:int4)
%table inpA919(a:int4,b:int4)
%table inpA920(a:int4,b:int4)
%table inpA921(a:int4,b:int4)
%table inpA922(a:int4,b:int4)
%table inpA923(a:int4,b:int4)
%table inpA924(a:int4,b:int4)
%table inpA925(a:int4,b:int4)
%table inpA926(a:int4,b:int4)
%table inpA927(a:int4,b:int4)
%table inpA928(a:int4,b:int4)
%table inpA929(a:int4,b:int4)
%table inpA930(a:int4,b:int4)
%table inpA931(a:int4,b:int4)
%table inpA932(a:int4,b:int4)
%table inpA933(a:int4,b:int4)
%table inpA934(a:int4,b:int4)
%table inpA935(a:int4,b:int4)
%table inpA936(a:int4,b:int4)
%table inpA937(a:int4,b:int4)
%table inpA938(a:int4,b:int4)
%table inpA939(a:int4,b:int4)
%table inpA940(a:int4,b:int4)
%table inpA941(a:int4,b:int4)
%table inpA942(a:int4,b:int4)
%table inpA943(a:int4,b:int4)
%table inpA944(a:int4,b:int4)
%table inpA945(a:int4,b:int4)
%table inpA946(a:int4,b:int4)
%table inpA947(a:int4,b:int4)
%table inpA948(a:int4,b:int4)
%table inpA949(a:int4,b:int4)
%table inpA950(a:int4,b:int4)
%table inpA951(a:int4,b:int4)
%table inpA952(a:int4,b:int4)
%table inpA953(a:int4,b:int4)
%table inpA954(a:int4,b:int4)
%table inpA955(a:int4,b:int4)
%table inpA956(a:int4,b:int4)
%table inpA957(a:int4,b:int4)
%table inpA958(a:int4,b:int4)
%table inpA959(a:int4,b:int4)
%table inpA960(a:int4,b:int4)
%table inpA961(a:int4,b:int4)
%table inpA962(a:int4,b:int4)
%table inpA963(a:int4,b:int4)
%table inpA964(a:int4,b:int4)
%table inpA965(a:int4,b:int4)
%table inpA966(a:int4,b:int4)
%table inpA967(a:int4,b:int4)
%table inpA968(a:int4,b:int4)
%table inpA969(a:int4,b:int4)
%table inpA970(a:int4,b:int4)
%table inpA971(a:int4,b:int4)
%table inpA972(a:int4,b:int4)
%table inpA973(a:int4,b:int4)
%table inpA974(a:int4,b:int4)
%table inpA975(a:int4,b:int4)
%table inpA976(a:int4,b:int4)
%table inpA977(a:int4,b:int4)
%table inpA978(a:int4,b:int4)
%table inpA979(a:int4,b:int4)
%table inpA980(a:int4,b:int4)
%table inpA981(a:int4,b:int4)
%table inpA982(a:int4,b:int4)
%table inpA983(a:int4,b:int4)
%table inpA984(a:int4,b:int4)
%table inpA985(a:int4,b:int4)
%table inpA986(a:int4,b:int4)
%table inpA987(a:int4,b:int4)
%table inpA988(a:int4,b:int4)
%table inpA989(a:int4,b:int4)
%table inpA990(a:int4,b:int4)
%table inpA991(a:int4,b:int4)
%table inpA992(a:int4,b:int4)
%table inpA993(a:int4,b:int4)
%table inpA994(a:int4,b:int4)
%table inpA995(a:int4,b:int4)
%table inpA996(a:int4,b:int4)
%table inpA997(a:int4,b:int4)
%table inpA998(a:int4,b:int4)
%table inpA999(a:int4,b:int4)
%table inpA1000(a:int4,b:int4)
%table inpA1001(a:int4,b:int4)
%table inpA1002(a:int4,b:int4)
%table inpA1003(a:int4,b:int4)
%table inpA1004(a:int4,b:int4)
%table inpA1005(a:int4,b:int4)
%table inpA1006(a:int4,b:int4)
%table inpA1007(a:int4,b:int4)
%table inpA1008(a:int4,b:int4)
%table inpA1009(a:int4,b:int4)
%table inpA1010(a:int4,b:int4)
%table inpA1011(a:int4,b:int4)
%table inpA1012(a:int4,b:int4)
%table inpA1013(a:int4,b:int4)
%table inpA1014(a:int4,b:int4)
%table inpA1015(a:int4,b:int4)
%table inpA1016(a:int4,b:int4)
%table inpA1017(a:int4,b:int4)
%table inpA1018(a:int4,b:int4)
%table inpA1019(a:int4,b:int4)
%table inpA1020(a:int4,b:int4)
%table inpA1021(a:int4,b:int4)
%table inpA1022(a:int4,b:int4)
%table inpA1023(a:int4,b:int4)
%table inpA1024(a:int4,b:int4)
%table inpA1025(a:int4,b:int4)
%table inpA1026(a:int4,b:int4)
%table inpA1027(a:int4,b:int4)
%table inpA1028(a:int4,b:int4)
%table inpA1029(a:int4,b:int4)
%table inpA1030(a:int4,b:int4)
%table inpA1031(a:int4,b:int4)
%table inpA1032(a:int4,b:int4)
%table inpA1033(a:int4,b:int4)
%table inpA1034(a:int4,b:int4)
%table inpA1035(a:int4,b:int4)
%table inpA1036(a:int4,b:int4)
%table inpA1037(a:int4,b:int4)
%table inpA1038(a:int4,b:int4)
%table inpA1039(a:int4,b:int4)
%table inpA1040(a:int4,b:int4)
%table inpA1041(a:int4,b:int4)
%table inpA1042(a:int4,b:int4)
%table inpA1043(a:int4,b:int4)
%table inpA1044(a:int4,b:int4)
%table inpA1045(a:int4,b:int4)
%table inpA1046(a:int4,b:int4)
%table inpA1047(a:int4,b:int4)
%table inpA1048(a:int4,b:int4)
%table inpA1049(a:int4,b:int4)
%table inpA1050(a:int4,b:int4)
%table inpA1051(a:int4,b:int4)
%table inpA1052(a:int4,b:int4)
%table inpA1053(a:int4,b:int4)
%table inpA1054(a:int4,b:int4)
%table inpA1055(a:int4,b:int4)
%table inpA1056(a:int4,b:int4)
%table inpA1057(a:int4,b:int4)
%table inpA1058(a:int4,b:int4)
%table inpA1059(a:int4,b:int4)
%table inpA1060(a:int4,b:int4)
%table inpA1061(a:int4,b:int4)
%table inpA1062(a:int4,b:int4)
%table inpA1063(a:int4,b:int4)
%table inpA1064(a:int4,b:int4)
%table inpA1065(a:int4,b:int4)
%table inpA1066(a:int4,b:int4)
%table inpA1067(a:int4,b:int4)
%table inpA1068(a:int4,b:int4)
%table inpA1069(a:int4,b:int4)
%table inpA1070(a:int4,b:int4)
%table inpA1071(a:int4,b:int4)
%table inpA1072(a:int4,b:int4)
%table inpA1073(a:int4,b:int4)
%table inpA1074(a:int4,b:int4)
%table inpA1075(a:int4,b:int4)
%table inpA1076(a:int4,b:int4)
%table inpA1077(a:int4,b:int4)
%table inpA1078(a:int4,b:int4)
%table inpA1079(a:int4,b:int4)
%table inpA1080(a:int4,b:int4)
%table inpA1081(a:int4,b:int4)
%table inpA1082(a:int4,b:int4)
%table inpA1083(a:int4,b:int4)
%table inpA1084(a:int4,b:int4)
%table inpA1085(a:int4,b:int4)
%table inpA1086(a:int4,b:int4)
%table inpA1087(a:int4,b:int4)
%table inpA1088(a:int4,b:int4)
%table inpA1089(a:int4,b:int4)
%table inpA1090(a:int4,b:int4)
%table inpA1091(a:int4,b:int4)
%table inpA1092(a:int4,b:int4)
%table inpA1093(a:int4,b:int4)
%table inpA1094(a:int4,b:int4)
%table inpA1095(a:int4,b:int4)
%table inpA1096(a:int4,b:int4)
%table inpA1097(a:int4,b:int4)
%table inpA1098(a:int4,b:int4)
%table inpA1099(a:int4,b:int4)
%table inpA1100(a:int4,b:int4)
%table inpA1101(a:int4,b:int4)
%table inpA1102(a:int4,b:int4)
%table inpA1103(a:int4,b:int4)
%table inpA1104(a:int4,b:int4)
%table inpA1105(a:int4,b:int4)
%table inpA1106(a:int4,b:int4)
%table inpA1107(a:int4,b:int4)
%table inpA1108(a:int4,b:int4)
%table inpA1109(a:int4,b:int4)
%table inpA1110(a:int4,b:int4)
%table inpA1111(a:int4,b:int4)
%table inpA1112(a:int4,b:int4)
%table inpA1113(a:int4,b:int4)
%table inpA1114(a:int4,b:int4)
%table inpA1115(a:int4,b:int4)
%table inpA1116(a:int4,b:int4)
%table inpA1117(a:int4,b:int4)
%table inpA1118(a:int4,b:int4)
%table inpA1119(a:int4,b:int4)
%table inpA1120(a:int4,b:int4)
%table inpA1121(a:int4,b:int4)
%table inpA1122(a:int4,b:int4)
%table inpA1123(a:int4,b:int4)
%table inpA1124(a:int4,b:int4)
%table inpA1125(a:int4,b:int4)
%table inpA1126(a:int4,b:int4)
%table inpA1127(a:int4,b:int4)
%table inpA1128(a:int4,b:int4)
%table inpA1129(a:int4,b:int4)
%table inpA1130(a:int4,b:int4)
%table inpA1131(a:int4,b:int4)
%table inpA1132(a:int4,b:int4)
%table inpA1133(a:int4,b:int4)
%table inpA1134(a:int4,b:int4)
%table inpA1135(a:int4,b:int4)
%table inpA1136(a:int4,b:int4)
%table inpA1137(a:int4,b:int4)
%table inpA1138(a:int4,b:int4)
%table inpA1139(a:int4,b:int4)
%table inpA1140(a:int4,b:int4)
%table inpA1141(a:int4,b:int4)
%table inpA1142(a:int4,b:int4)
%table inpA1143(a:int4,b:int4)
%table inpA1144(a:int4,b:int4)
%table inpA1145(a:int4,b:int4)
%table inpA1146(a:int4,b:int4)
%table inpA1147(a:int4,b:int4)
%table inpA1148(a:int4,b:int4)
%table inpA1149(a:int4,b:int4)
%table inpA1150(a:int4,b:int4)
%table inpA1151(a:int4,b:int4)
%table inpA1152(a:int4,b:int4)
%table inpA1153(a:int4,b:int4)
%table inpA1154(a:int4,b:int4)
%table inpA1155(a:int4,b:int4)
%table inpA1156(a:int4,b:int4)
%table inpA1157(a:int4,b:int4)
%table inpA1158(a:int4,b:int4)
%table inpA1159(a:int4,b:int4)
%table inpA1160(a:int4,b:int4)
%table inpA1161(a:int4,b:int4)
%table inpA1162(a:int4,b:int4)
%table inpA1163(a:int4,b:int4)
%table inpA1164(a:int4,b:int4)
%table inpA1165(a:int4,b:int4)
%table inpA1166(a:int4,b:int4)
%table inpA1167(a:int4,b:int4)
%table inpA1168(a:int4,b:int4)
%table inpA1169(a:int4,b:int4)
%table inpA1170(a:int4,b:int4)
%table inpA1171(a:int4,b:int4)
%table inpA1172(a:int4,b:int4)
%table inpA1173(a:int4,b:int4)
%table inpA1174(a:int4,b:int4)
%table inpA1175(a:int4,b:int4)
%table inpA1176(a:int4,b:int4)
%table inpA1177(a:int4,b:int4)
%table inpA1178(a:int4,b:int4)
%table inpA1179(a:int4,b:int4)
%table inpA1180(a:int4,b:int4)
%table inpA1181(a:int4,b:int4)
%table inpA1182(a:int4,b:int4)
%table inpA1183(a:int4,b:int4)
%table inpA1184(a:int4,b:int4)
%table inpA1185(a:int4,b:int4)
%table inpA1186(a:int4,b:int4)
%table inpA1187(a:int4,b:int4)
%table inpA1188(a:int4,b:int4)
%table inpA1189(a:int4,b:int4)
%table inpA1190(a:int4,b:int4)
%table inpA1191(a:int4,b:int4)
%table inpA1192(a:int4,b:int4)
%table inpA1193(a:int4,b:int4)
%table inpA1194(a:int4,b:int4)
%table inpA1195(a:int4,b:int4)
%table inpA1196(a:int4,b:int4)
%table inpA1197(a:int4,b:int4)
%table inpA1198(a:int4,b:int4)
%table inpA1199(a:int4,b:int4)
%table inpA1200(a:int4,b:int4)
%table inpA1201(a:int4,b:int4)
%table inpA1202(a:int4,b:int4)
%table inpA1203(a:int4,b:int4)
%table inpA1204(a:int4,b:int4)
%table inpA1205(a:int4,b:int4)
%table inpA1206(a:int4,b:int4)
%table inpA1207(a:int4,b:int4)
%table inpA1208(a:int4,b:int4)
%table inpA1209(a:int4,b:int4)
%table inpA1210(a:int4,b:int4)
%table inpA1211(a:int4,b:int4)
%table inpA1212(a:int4,b:int4)
%table inpA1213(a:int4,b:int4)
%table inpA1214(a:int4,b:int4)
%table inpA1215(a:int4,b:int4)
%table inpA1216(a:int4,b:int4)
%table inpA1217(a:int4,b:int4)
%table inpA1218(a:int4,b:int4)
%table inpA1219(a:int4,b:int4)
%table inpA1220(a:int4,b:int4)
%table inpA1221(a:int4,b:int4)
%table inpA1222(a:int4,b:int4)
%table inpA1223(a:int4,b:int4)
%table inpA1224(a:int4,b:int4)
%table inpA1225(a:int4,b:int4)
%table inpA1226(a:int4,b:int4)
%table inpA1227(a:int4,b:int4)
%table inpA1228(a:int4,b:int4)
%table inpA1229(a:int4,b:int4)
%table inpA1230(a:int4,b:int4)
%table inpA1231(a:int4,b:int4)
%table inpA1232(a:int4,b:int4)
%table inpA1233(a:int4,b:int4)
%table inpA1234(a:int4,b:int4)
%table inpA1235(a:int4,b:int4)
%table inpA1236(a:int4,b:int4)
%table inpA1237(a:int4,b:int4)
%table inpA1238(a:int4,b:int4)
%table inpA1239(a:int4,b:int4)
%table inpA1240(a:int4,b:int4)
%table inpA1241(a:int4,b:int4)
%table inpA1242(a:int4,b:int4)
%table inpA1243(a:int4,b:int4)
%table inpA1244(a:int4,b:int4)
%table inpA1245(a:int4,b:int4)
%table inpA1246(a:int4,b:int4)
%table inpA1247(a:int4,b:int4)
%table inpA1248(a:int4,b:int4)
%table inpA1249(a:int4,b:int4)
%table inpA1250(a:int4,b:int4)
%table inpA1251(a:int4,b:int4)
%table inpA1252(a:int4,b:int4)
%table inpA1253(a:int4,b:int4)
%table inpA1254(a:int4,b:int4)
%table inpA1255(a:int4,b:int4)
%table inpA1256(a:int4,b:int4)
%table inpA1257(a:int4,b:int4)
%table inpA1258(a:int4,b:int4)
%table inpA1259(a:int4,b:int4)
%table inpA1260(a:int4,b:int4)
%table inpA1261(a:int4,b:int4)
%table inpA1262(a:int4,b:int4)
%table inpA1263(a:int4,b:int4)
%table inpA1264(a:int4,b:int4)
%table inpA1265(a:int4,b:int4)
%table inpA1266(a:int4,b:int4)
%table inpA1267(a:int4,b:int4)
%table inpA1268(a:int4,b:int4)
%table inpA1269(a:int4,b:int4)
%table inpA1270(a:int4,b:int4)
%table inpA1271(a:int4,b:int4)
%table inpA1272(a:int4,b:int4)
%table inpA1273(a:int4,b:int4)
%table inpA1274(a:int4,b:int4)
%table inpA1275(a:int4,b:int4)
%table inpA1276(a:int4,b:int4)
%table inpA1277(a:int4,b:int4)
%table inpA1278(a:int4,b:int4)
%table inpA1279(a:int4,b:int4)
%table inpA1280(a:int4,b:int4)
%table inpA1281(a:int4,b:int4)
%table inpA1282(a:int4,b:int4)
%table inpA1283(a:int4,b:int4)
%table inpA1284(a:int4,b:int4)
%table inpA1285(a:int4,b:int4)
%table inpA1286(a:int4,b:int4)
%table inpA1287(a:int4,b:int4)
%table inpA1288(a:int4,b:int4)
%table inpA1289(a:int4,b:int4)
%table inpA1290(a:int4,b:int4)
%table inpA1291(a:int4,b:int4)
%table inpA1292(a:int4,b:int4)
%table inpA1293(a:int4,b:int4)
%table inpA1294(a:int4,b:int4)
%table inpA1295(a:int4,b:int4)
%table inpA1296(a:int4,b:int4)
%table inpA1297(a:int4,b:int4)
%table inpA1298(a:int4,b:int4)
%table inpA1299(a:int4,b:int4)
%table inpA1300(a:int4,b:int4)
%table inpA1301(a:int4,b:int4)
%table inpA1302(a:int4,b:int4)
%table inpA1303(a:int4,b:int4)
%table inpA1304(a:int4,b:int4)
%table inpA1305(a:int4,b:int4)
%table inpA1306(a:int4,b:int4)
%table inpA1307(a:int4,b:int4)
%table inpA1308(a:int4,b:int4)
%table inpA1309(a:int4,b:int4)
%table inpA1310(a:int4,b:int4)
%table inpA1311(a:int4,b:int4)
%table inpA1312(a:int4,b:int4)
%table inpA1313(a:int4,b:int4)
%table inpA1314(a:int4,b:int4)
%table inpA1315(a:int4,b:int4)
%table inpA1316(a:int4,b:int4)
%table inpA1317(a:int4,b:int4)
%table inpA1318(a:int4,b:int4)
%table inpA1319(a:int4,b:int4)
%table inpA1320(a:int4,b:int4)
%table inpA1321(a:int4,b:int4)
%table inpA1322(a:int4,b:int4)
%table inpA1323(a:int4,b:int4)
%table inpA1324(a:int4,b:int4)
%table inpA1325(a:int4,b:int4)
%table inpA1326(a:int4,b:int4)
%table inpA1327(a:int4,b:int4)
%table inpA1328(a:int4,b:int4)
%table inpA1329(a:int4,b:int4)
%table inpA1330(a:int4,b:int4)
%table inpA1331(a:int4,b:int4)
%table inpA1332(a:int4,b:int4)
%table inpA1333(a:int4,b:int4)
%table inpA1334(a:int4,b:int4)
%table inpA1335(a:int4,b:int4)
%table inpA1336(a:int4,b:int4)
%table inpA1337(a:int4,b:int4)
%table inpA1338(a:int4,b:int4)
%table inpA1339(a:int4,b:int4)
%table inpA1340(a:int4,b:int4)
%table inpA1341(a:int4,b:int4)
%table inpA1342(a:int4,b:int4)
%table inpA1343(a:int4,b:int4)
%table inpA1344(a:int4,b:int4)
%table inpA1345(a:int4,b:int4)
%table inpA1346(a:int4,b:int4)
%table inpA1347(a:int4,b:int4)
%table inpA1348(a:int4,b:int4)
%table inpA1349(a:int4,b:int4)
%table inpA1350(a:int4,b:int4)
%table inpA1351(a:int4,b:int4)
%table inpA1352(a:int4,b:int4)
%table inpA1353(a:int4,b:int4)
%table inpA1354(a:int4,b:int4)
%table inpA1355(a:int4,b:int4)
%table inpA1356(a:int4,b:int4)
%table inpA1357(a:int4,b:int4)
%table inpA1358(a:int4,b:int4)
%table inpA1359(a:int4,b:int4)
%table inpA1360(a:int4,b:int4)
%table inpA1361(a:int4,b:int4)
%table inpA1362(a:int4,b:int4)
%table inpA1363(a:int4,b:int4)
%table inpA1364(a:int4,b:int4)
%table inpA1365(a:int4,b:int4)
%table inpA1366(a:int4,b:int4)
%table inpA1367(a:int4,b:int4)
%table inpA1368(a:int4,b:int4)
%table inpA1369(a:int4,b:int4)
%table inpA1370(a:int4,b:int4)
%table inpA1371(a:int4,b:int4)
%table inpA1372(a:int4,b:int4)
%table inpA1373(a:int4,b:int4)
%table inpA1374(a:int4,b:int4)
%table inpA1375(a:int4,b:int4)
%table inpA1376(a:int4,b:int4)
%table inpA1377(a:int4,b:int4)
%table inpA1378(a:int4,b:int4)
%table inpA1379(a:int4,b:int4)
%table inpA1380(a:int4,b:int4)
%table inpA1381(a:int4,b:int4)
%table inpA1382(a:int4,b:int4)
%table inpA1383(a:int4,b:int4)
%table inpA1384(a:int4,b:int4)
%table inpA1385(a:int4,b:int4)
%table inpA1386(a:int4,b:int4)
%table inpA1387(a:int4,b:int4)
%table inpA1388(a:int4,b:int4)
%table inpA1389(a:int4,b:int4)
%table inpA1390(a:int4,b:int4)
%table inpA1391(a:int4,b:int4)
%table inpA1392(a:int4,b:int4)
%table inpA1393(a:int4,b:int4)
%table inpA1394(a:int4,b:int4)
%table inpA1395(a:int4,b:int4)
%table inpA1396(a:int4,b:int4)
%table inpA1397(a:int4,b:int4)
%table inpA1398(a:int4,b:int4)
%table inpA1399(a:int4,b:int4)
%table inpA1400(a:int4,b:int4)
%table inpA1401(a:int4,b:int4)
%table inpA1402(a:int4,b:int4)
%table inpA1403(a:int4,b:int4)
%table inpA1404(a:int4,b:int4)
%table inpA1405(a:int4,b:int4)
%table inpA1406(a:int4,b:int4)
%table inpA1407(a:int4,b:int4)
%table inpA1408(a:int4,b:int4)
%table inpA1409(a:int4,b:int4)
%table inpA1410(a:int4,b:int4)
%table inpA1411(a:int4,b:int4)
%table inpA1412(a:int4,b:int4)
%table inpA1413(a:int4,b:int4)
%table inpA1414(a:int4,b:int4)
%table inpA1415(a:int4,b:int4)
%table inpA1416(a:int4,b:int4)
%table inpA1417(a:int4,b:int4)
%table inpA1418(a:int4,b:int4)
%table inpA1419(a:int4,b:int4)
%table inpA1420(a:int4,b:int4)
%table inpA1421(a:int4,b:int4)
%table inpA1422(a:int4,b:int4)
%table inpA1423(a:int4,b:int4)
%table inpA1424(a:int4,b:int4)
%table inpA1425(a:int4,b:int4)
%table inpA1426(a:int4,b:int4)
%table inpA1427(a:int4,b:int4)
%table inpA1428(a:int4,b:int4)
%table inpA1429(a:int4,b:int4)
%table inpA1430(a:int4,b:int4)
%table inpA1431(a:int4,b:int4)
%table inpA1432(a:int4,b:int4)
%table inpA1433(a:int4,b:int4)
%table inpA1434(a:int4,b:int4)
%table inpA1435(a:int4,b:int4)
%table inpA1436(a:int4,b:int4)
%table inpA1437(a:int4,b:int4)
%table inpA1438(a:int4,b:int4)
%table inpA1439(a:int4,b:int4)
%table inpA1440(a:int4,b:int4)
%table inpA1441(a:int4,b:int4)
%table inpA1442(a:int4,b:int4)
%table inpA1443(a:int4,b:int4)
%table inpA1444(a:int4,b:int4)
%table inpA1445(a:int4,b:int4)
%table inpA1446(a:int4,b:int4)
%table inpA1447(a:int4,b:int4)
%table inpA1448(a:int4,b:int4)
%table inpA1449(a:int4,b:int4)
%table inpA1450(a:int4,b:int4)
%table inpA1451(a:int4,b:int4)
%table inpA1452(a:int4,b:int4)
%table inpA1453(a:int4,b:int4)
%table inpA1454(a:int4,b:int4)
%table inpA1455(a:int4,b:int4)
%table inpA1456(a:int4,b:int4)
%table inpA1457(a:int4,b:int4)
%table inpA1458(a:int4,b:int4)
%table inpA1459(a:int4,b:int4)
%table inpA1460(a:int4,b:int4)
%table inpA1461(a:int4,b:int4)
%table inpA1462(a:int4,b:int4)
%table inpA1463(a:int4,b:int4)
%table inpA1464(a:int4,b:int4)
%table inpA1465(a:int4,b:int4)
%table inpA1466(a:int4,b:int4)
%table inpA1467(a:int4,b:int4)
%table inpA1468(a:int4,b:int4)
%table inpA1469(a:int4,b:int4)
%table inpA1470(a:int4,b:int4)
%table inpA1471(a:int4,b:int4)
%table inpA1472(a:int4,b:int4)
%table inpA1473(a:int4,b:int4)
%table inpA1474(a:int4,b:int4)
%table inpA1475(a:int4,b:int4)
%table inpA1476(a:int4,b:int4)
%table inpA1477(a:int4,b:int4)
%table inpA1478(a:int4,b:int4)
%table inpA1479(a:int4,b:int4)
%table inpA1480(a:int4,b:int4)
%table inpA1481(a:int4,b:int4)
%table inpA1482(a:int4,b:int4)
%table inpA1483(a:int4,b:int4)
%table inpA1484(a:int4,b:int4)
%table inpA1485(a:int4,b:int4)
%table inpA1486(a:int4,b:int4)
%table inpA1487(a:int4,b:int4)
%table inpA1488(a:int4,b:int4)
%table inpA1489(a:int4,b:int4)
%table inpA1490(a:int4,b:int4)
%table inpA1491(a:int4,b:int4)
%table inpA1492(a:int4,b:int4)
%table inpA1493(a:int4,b:int4)
%table inpA1494(a:int4,b:int4)
%table inpA1495(a:int4,b:int4)
%table inpA1496(a:int4,b:int4)
%table inpA1497(a:int4,b:int4)
%table inpA1498(a:int4,b:int4)
%table inpA1499(a:int4,b:int4)
%table inpA1500(a:int4,b:int4)
%table inpA1501(a:int4,b:int4)
%table inpA1502(a:int4,b:int4)
%table inpA1503(a:int4,b:int4)
%table inpA1504(a:int4,b:int4)
%table inpA1505(a:int4,b:int4)
%table inpA1506(a:int4,b:int4)
%table inpA1507(a:int4,b:int4)
%table inpA1508(a:int4,b:int4)
%table inpA1509(a:int4,b:int4)
%table inpA1510(a:int4,b:int4)
%table inpA1511(a:int4,b:int4)
%table inpA1512(a:int4,b:int4)
%table inpA1513(a:int4,b:int4)
%table inpA1514(a:int4,b:int4)
%table inpA1515(a:int4,b:int4)
%table inpA1516(a:int4,b:int4)
%table inpA1517(a:int4,b:int4)
%table inpA1518(a:int4,b:int4)
%table inpA1519(a:int4,b:int4)
%table inpA1520(a:int4,b:int4)
%table inpA1521(a:int4,b:int4)
%table inpA1522(a:int4,b:int4)
%table inpA1523(a:int4,b:int4)
%table inpA1524(a:int4,b:int4)
%table inpA1525(a:int4,b:int4)
%table inpA1526(a:int4,b:int4)
%table inpA1527(a:int4,b:int4)
%table inpA1528(a:int4,b:int4)
%table inpA1529(a:int4,b:int4)
%table inpA1530(a:int4,b:int4)
%table inpA1531(a:int4,b:int4)
%table inpA1532(a:int4,b:int4)
%table inpA1533(a:int4,b:int4)
%table inpA1534(a:int4,b:int4)
%table inpA1535(a:int4,b:int4)
%table inpA1536(a:int4,b:int4)
%table inpA1537(a:int4,b:int4)
%table inpA1538(a:int4,b:int4)
%table inpA1539(a:int4,b:int4)
%table inpA1540(a:int4,b:int4)
%table inpA1541(a:int4,b:int4)
%table inpA1542(a:int4,b:int4)
%table inpA1543(a:int4,b:int4)
%table inpA1544(a:int4,b:int4)
%table inpA1545(a:int4,b:int4)
%table inpA1546(a:int4,b:int4)
%table inpA1547(a:int4,b:int4)
%table inpA1548(a:int4,b:int4)
%table inpA1549(a:int4,b:int4)
%table inpA1550(a:int4,b:int4)
%table inpA1551(a:int4,b:int4)
%table inpA1552(a:int4,b:int4)
%table inpA1553(a:int4,b:int4)
%table inpA1554(a:int4,b:int4)
%table inpA1555(a:int4,b:int4)
%table inpA1556(a:int4,b:int4)
%table inpA1557(a:int4,b:int4)
%table inpA1558(a:int4,b:int4)
%table inpA1559(a:int4,b:int4)
%table inpA1560(a:int4,b:int4)
%table inpA1561(a:int4,b:int4)
%table inpA1562(a:int4,b:int4)
%table inpA1563(a:int4,b:int4)
%table inpA1564(a:int4,b:int4)
%table inpA1565(a:int4,b:int4)
%table inpA1566(a:int4,b:int4)
%table inpA1567(a:int4,b:int4)
%table inpA1568(a:int4,b:int4)
%table inpA1569(a:int4,b:int4)
%table inpA1570(a:int4,b:int4)
%table inpA1571(a:int4,b:int4)
%table inpA1572(a:int4,b:int4)
%table inpA1573(a:int4,b:int4)
%table inpA1574(a:int4,b:int4)
%table inpA1575(a:int4,b:int4)
%table inpA1576(a:int4,b:int4)
%table inpA1577(a:int4,b:int4)
%table inpA1578(a:int4,b:int4)
%table inpA1579(a:int4,b:int4)
%table inpA1580(a:int4,b:int4)
%table inpA1581(a:int4,b:int4)
%table inpA1582(a:int4,b:int4)
%table inpA1583(a:int4,b:int4)
%table inpA1584(a:int4,b:int4)
%table inpA1585(a:int4,b:int4)
%table inpA1586(a:int4,b:int4)
%table inpA1587(a:int4,b:int4)
%table inpA1588(a:int4,b:int4)
%table inpA1589(a:int4,b:int4)
%table inpA1590(a:int4,b:int4)
%table inpA1591(a:int4,b:int4)
%table inpA1592(a:int4,b:int4)
%table inpA1593(a:int4,b:int4)
%table inpA1594(a:int4,b:int4)
%table inpA1595(a:int4,b:int4)
%table inpA1596(a:int4,b:int4)
%table inpA1597(a:int4,b:int4)
%table inpA1598(a:int4,b:int4)
%table inpA1599(a:int4,b:int4)
%table inpA1600(a:int4,b:int4)
%table inpA1601(a:int4,b:int4)
%table inpA1602(a:int4,b:int4)
%table inpA1603(a:int4,b:int4)
%table inpA1604(a:int4,b:int4)
%table inpA1605(a:int4,b:int4)
%table inpA1606(a:int4,b:int4)
%table inpA1607(a:int4,b:int4)
%table inpA1608(a:int4,b:int4)
%table inpA1609(a:int4,b:int4)
%table inpA1610(a:int4,b:int4)
%table inpA1611(a:int4,b:int4)
%table inpA1612(a:int4,b:int4)
%table inpA1613(a:int4,b:int4)
%table inpA1614(a:int4,b:int4)
%table inpA1615(a:int4,b:int4)
%table inpA1616(a:int4,b:int4)
%table inpA1617(a:int4,b:int4)
%table inpA1618(a:int4,b:int4)
%table inpA1619(a:int4,b:int4)
%table inpA1620(a:int4,b:int4)
%table inpA1621(a:int4,b:int4)
%table inpA1622(a:int4,b:int4)
%table inpA1623(a:int4,b:int4)
%table inpA1624(a:int4,b:int4)
%table inpA1625(a:int4,b:int4)
%table inpA1626(a:int4,b:int4)
%table inpA1627(a:int4,b:int4)
%table inpA1628(a:int4,b:int4)
%table inpA1629(a:int4,b:int4)
%table inpA1630(a:int4,b:int4)
%table inpA1631(a:int4,b:int4)
%table inpA1632(a:int4,b:int4)
%table inpA1633(a:int4,b:int4)
%table inpA1634(a:int4,b:int4)
%table inpA1635(a:int4,b:int4)
%table inpA1636(a:int4,b:int4)
%table inpA1637(a:int4,b:int4)
%table inpA1638(a:int4,b:int4)
%table inpA1639(a:int4,b:int4)
%table inpA1640(a:int4,b:int4)
%table inpA1641(a:int4,b:int4)
%table inpA1642(a:int4,b:int4)
%table inpA1643(a:int4,b:int4)
%table inpA1644(a:int4,b:int4)
%table inpA1645(a:int4,b:int4)
%table inpA1646(a:int4,b:int4)
%table inpA1647(a:int4,b:int4)
%table inpA1648(a:int4,b:int4)
%table inpA1649(a:int4,b:int4)
%table inpA1650(a:int4,b:int4)
%table inpA1651(a:int4,b:int4)
%table inpA1652(a:int4,b:int4)
%table inpA1653(a:int4,b:int4)
%table inpA1654(a:int4,b:int4)
%table inpA1655(a:int4,b:int4)
%table inpA1656(a:int4,b:int4)
%table inpA1657(a:int4,b:int4)
%table inpA1658(a:int4,b:int4)
%table inpA1659(a:int4,b:int4)
%table inpA1660(a:int4,b:int4)
%table inpA1661(a:int4,b:int4)
%table inpA1662(a:int4,b:int4)
%table inpA1663(a:int4,b:int4)
%table inpA1664(a:int4,b:int4)
%table inpA1665(a:int4,b:int4)
%table inpA1666(a:int4,b:int4)
%table inpA1667(a:int4,b:int4)
%table inpA1668(a:int4,b:int4)
%table inpA1669(a:int4,b:int4)
%table inpA1670(a:int4,b:int4)
%table inpA1671(a:int4,b:int4)
%table inpA1672(a:int4,b:int4)
%table inpA1673(a:int4,b:int4)
%table inpA1674(a:int4,b:int4)
%table inpA1675(a:int4,b:int4)
%table inpA1676(a:int4,b:int4)
%table inpA1677(a:int4,b:int4)
%table inpA1678(a:int4,b:int4)
%table inpA1679(a:int4,b:int4)
%table inpA1680(a:int4,b:int4)
%table inpA1681(a:int4,b:int4)
%table inpA1682(a:int4,b:int4)
%table inpA1683(a:int4,b:int4)
%table inpA1684(a:int4,b:int4)
%table inpA1685(a:int4,b:int4)
%table inpA1686(a:int4,b:int4)
%table inpA1687(a:int4,b:int4)
%table inpA1688(a:int4,b:int4)
%table inpA1689(a:int4,b:int4)
%table inpA1690(a:int4,b:int4)
%table inpA1691(a:int4,b:int4)
%table inpA1692(a:int4,b:int4)
%table inpA1693(a:int4,b:int4)
%table inpA1694(a:int4,b:int4)
%table inpA1695(a:int4,b:int4)
%table inpA1696(a:int4,b:int4)
%table inpA1697(a:int4,b:int4)
%table inpA1698(a:int4,b:int4)
%table inpA1699(a:int4,b:int4)
%table inpA1700(a:int4,b:int4)
%table inpA1701(a:int4,b:int4)
%table inpA1702(a:int4,b:int4)
%table inpA1703(a:int4,b:int4)
%table inpA1704(a:int4,b:int4)
%table inpA1705(a:int4,b:int4)
%table inpA1706(a:int4,b:int4)
%table inpA1707(a:int4,b:int4)
%table inpA1708(a:int4,b:int4)
%table inpA1709(a:int4,b:int4)
%table inpA1710(a:int4,b:int4)
%table inpA1711(a:int4,b:int4)
%table inpA1712(a:int4,b:int4)
%table inpA1713(a:int4,b:int4)
%table inpA1714(a:int4,b:int4)
%table inpA1715(a:int4,b:int4)
%table inpA1716(a:int4,b:int4)
%table inpA1717(a:int4,b:int4)
%table inpA1718(a:int4,b:int4)
%table inpA1719(a:int4,b:int4)
%table inpA1720(a:int4,b:int4)
%table inpA1721(a:int4,b:int4)
%table inpA1722(a:int4,b:int4)
%table inpA1723(a:int4,b:int4)
%table inpA1724(a:int4,b:int4)
%table inpA1725(a:int4,b:int4)
%table inpA1726(a:int4,b:int4)
%table inpA1727(a:int4,b:int4)
%table inpA1728(a:int4,b:int4)
%table inpA1729(a:int4,b:int4)
%table inpA1730(a:int4,b:int4)
%table inpA1731(a:int4,b:int4)
%table inpA1732(a:int4,b:int4)
%table inpA1733(a:int4,b:int4)
%table inpA1734(a:int4,b:int4)
%table inpA1735(a:int4,b:int4)
%table inpA1736(a:int4,b:int4)
%table inpA1737(a:int4,b:int4)
%table inpA1738(a:int4,b:int4)
%table inpA1739(a:int4,b:int4)
%table inpA1740(a:int4,b:int4)
%table inpA1741(a:int4,b:int4)
%table inpA1742(a:int4,b:int4)
%table inpA1743(a:int4,b:int4)
%table inpA1744(a:int4,b:int4)
%table inpA1745(a:int4,b:int4)
%table inpA1746(a:int4,b:int4)
%table inpA1747(a:int4,b:int4)
%table inpA1748(a:int4,b:int4)
%table inpA1749(a:int4,b:int4)
%table inpA1750(a:int4,b:int4)
%table inpA1751(a:int4,b:int4)
%table inpA1752(a:int4,b:int4)
%table inpA1753(a:int4,b:int4)
%table inpA1754(a:int4,b:int4)
%table inpA1755(a:int4,b:int4)
%table inpA1756(a:int4,b:int4)
%table inpA1757(a:int4,b:int4)
%table inpA1758(a:int4,b:int4)
%table inpA1759(a:int4,b:int4)
%table inpA1760(a:int4,b:int4)
%table inpA1761(a:int4,b:int4)
%table inpA1762(a:int4,b:int4)
%table inpA1763(a:int4,b:int4)
%table inpA1764(a:int4,b:int4)
%table inpA1765(a:int4,b:int4)
%table inpA1766(a:int4,b:int4)
%table inpA1767(a:int4,b:int4)
%table inpA1768(a:int4,b:int4)
%table inpA1769(a:int4,b:int4)
%table inpA1770(a:int4,b:int4)
%table inpA1771(a:int4,b:int4)
%table inpA1772(a:int4,b:int4)
%table inpA1773(a:int4,b:int4)
%table inpA1774(a:int4,b:int4)
%table inpA1775(a:int4,b:int4)
%table inpA1776(a:int4,b:int4)
%table inpA1777(a:int4,b:int4)
%table inpA1778(a:int4,b:int4)
%table inpA1779(a:int4,b:int4)
%table inpA1780(a:int4,b:int4)
%table inpA1781(a:int4,b:int4)
%table inpA1782(a:int4,b:int4)
%table inpA1783(a:int4,b:int4)
%table inpA1784(a:int4,b:int4)
%table inpA1785(a:int4,b:int4)
%table inpA1786(a:int4,b:int4)
%table inpA1787(a:int4,b:int4)
%table inpA1788(a:int4,b:int4)
%table inpA1789(a:int4,b:int4)
%table inpA1790(a:int4,b:int4)
%table inpA1791(a:int4,b:int4)
%table inpA1792(a:int4,b:int4)
%table inpA1793(a:int4,b:int4)
%table inpA1794(a:int4,b:int4)
%table inpA1795(a:int4,b:int4)
%table inpA1796(a:int4,b:int4)
%table inpA1797(a:int4,b:int4)
%table inpA1798(a:int4,b:int4)
%table inpA1799(a:int4,b:int4)
%table inpA1800(a:int4,b:int4)
%table inpA1801(a:int4,b:int4)
%table inpA1802(a:int4,b:int4)
%table inpA1803(a:int4,b:int4)
%table inpA1804(a:int4,b:int4)
%table inpA1805(a:int4,b:int4)
%table inpA1806(a:int4,b:int4)
%table inpA1807(a:int4,b:int4)
%table inpA1808(a:int4,b:int4)
%table inpA1809(a:int4,b:int4)
%table inpA1810(a:int4,b:int4)
%table inpA1811(a:int4,b:int4)
%table inpA1812(a:int4,b:int4)
%table inpA1813(a:int4,b:int4)
%table inpA1814(a:int4,b:int4)
%table inpA1815(a:int4,b:int4)
%table inpA1816(a:int4,b:int4)
%table inpA1817(a:int4,b:int4)
%table inpA1818(a:int4,b:int4)
%table inpA1819(a:int4,b:int4)
%table inpA1820(a:int4,b:int4)
%table inpA1821(a:int4,b:int4)
%table inpA1822(a:int4,b:int4)
%table inpA1823(a:int4,b:int4)
%table inpA1824(a:int4,b:int4)
%table inpA1825(a:int4,b:int4)
%table inpA1826(a:int4,b:int4)
%table inpA1827(a:int4,b:int4)
%table inpA1828(a:int4,b:int4)
%table inpA1829(a:int4,b:int4)
%table inpA1830(a:int4,b:int4)
%table inpA1831(a:int4,b:int4)
%table inpA1832(a:int4,b:int4)
%table inpA1833(a:int4,b:int4)
%table inpA1834(a:int4,b:int4)
%table inpA1835(a:int4,b:int4)
%table inpA1836(a:int4,b:int4)
%table inpA1837(a:int4,b:int4)
%table inpA1838(a:int4,b:int4)
%table inpA1839(a:int4,b:int4)
%table inpA1840(a:int4,b:int4)
%table inpA1841(a:int4,b:int4)
%table inpA1842(a:int4,b:int4)
%table inpA1843(a:int4,b:int4)
%table inpA1844(a:int4,b:int4)
%table inpA1845(a:int4,b:int4)
%table inpA1846(a:int4,b:int4)
%table inpA1847(a:int4,b:int4)
%table inpA1848(a:int4,b:int4)
%table inpA1849(a:int4,b:int4)
%table inpA1850(a:int4,b:int4)
%table inpA1851(a:int4,b:int4)
%table inpA1852(a:int4,b:int4)
%table inpA1853(a:int4,b:int4)
%table inpA1854(a:int4,b:int4)
%table inpA1855(a:int4,b:int4)
%table inpA1856(a:int4,b:int4)
%table inpA1857(a:int4,b:int4)
%table inpA1858(a:int4,b:int4)
%table inpA1859(a:int4,b:int4)
%table inpA1860(a:int4,b:int4)
%table inpA1861(a:int4,b:int4)
%table inpA1862(a:int4,b:int4)
%table inpA1863(a:int4,b:int4)
%table inpA1864(a:int4,b:int4)
%table inpA1865(a:int4,b:int4)
%table inpA1866(a:int4,b:int4)
%table inpA1867(a:int4,b:int4)
%table inpA1868(a:int4,b:int4)
%table inpA1869(a:int4,b:int4)
%table inpA1870(a:int4,b:int4)
%table inpA1871(a:int4,b:int4)
%table inpA1872(a:int4,b:int4)
%table inpA1873(a:int4,b:int4)
%table inpA1874(a:int4,b:int4)
%table inpA1875(a:int4,b:int4)
%table inpA1876(a:int4,b:int4)
%table inpA1877(a:int4,b:int4)
%table inpA1878(a:int4,b:int4)
%table inpA1879(a:int4,b:int4)
%table inpA1880(a:int4,b:int4)
%table inpA1881(a:int4,b:int4)
%table inpA1882(a:int4,b:int4)
%table inpA1883(a:int4,b:int4)
%table inpA1884(a:int4,b:int4)
%table inpA1885(a:int4,b:int4)
%table inpA1886(a:int4,b:int4)
%table inpA1887(a:int4,b:int4)
%table inpA1888(a:int4,b:int4)
%table inpA1889(a:int4,b:int4)
%table inpA1890(a:int4,b:int4)
%table inpA1891(a:int4,b:int4)
%table inpA1892(a:int4,b:int4)
%table inpA1893(a:int4,b:int4)
%table inpA1894(a:int4,b:int4)
%table inpA1895(a:int4,b:int4)
%table inpA1896(a:int4,b:int4)
%table inpA1897(a:int4,b:int4)
%table inpA1898(a:int4,b:int4)
%table inpA1899(a:int4,b:int4)
%table inpA1900(a:int4,b:int4)
%table inpA1901(a:int4,b:int4)
%table inpA1902(a:int4,b:int4)
%table inpA1903(a:int4,b:int4)
%table inpA1904(a:int4,b:int4)
%table inpA1905(a:int4,b:int4)
%table inpA1906(a:int4,b:int4)
%table inpA1907(a:int4,b:int4)
%table inpA1908(a:int4,b:int4)
%table inpA1909(a:int4,b:int4)
%table inpA1910(a:int4,b:int4)
%table inpA1911(a:int4,b:int4)
%table inpA1912(a:int4,b:int4)
%table inpA1913(a:int4,b:int4)
%table inpA1914(a:int4,b:int4)
%table inpA1915(a:int4,b:int4)
%table inpA1916(a:int4,b:int4)
%table inpA1917(a:int4,b:int4)
%table inpA1918(a:int4,b:int4)
%table inpA1919(a:int4,b:int4)
%table inpA1920(a:int4,b:int4)
%table inpA1921(a:int4,b:int4)
%table inpA1922(a:int4,b:int4)
%table inpA1923(a:int4,b:int4)
%table inpA1924(a:int4,b:int4)
%table inpA1925(a:int4,b:int4)
%table inpA1926(a:int4,b:int4)
%table inpA1927(a:int4,b:int4)
%table inpA1928(a:int4,b:int4)
%table inpA1929(a:int4,b:int4)
%table inpA1930(a:int4,b:int4)
%table inpA1931(a:int4,b:int4)
%table inpA1932(a:int4,b:int4)
%table inpA1933(a:int4,b:int4)
%table inpA1934(a:int4,b:int4)
%table inpA1935(a:int4,b:int4)
%table inpA1936(a:int4,b:int4)
%table inpA1937(a:int4,b:int4)
%table inpA1938(a:int4,b:int4)
%table inpA1939(a:int4,b:int4)
%table inpA1940(a:int4,b:int4)
%table inpA1941(a:int4,b:int4)
%table inpA1942(a:int4,b:int4)
%table inpA1943(a:int4,b:int4)
%table inpA1944(a:int4,b:int4)
%table inpA1945(a:int4,b:int4)
%table inpA1946(a:int4,b:int4)
%table inpA1947(a:int4,b:int4)
%table inpA1948(a:int4,b:int4)
%table inpA1949(a:int4,b:int4)
%table inpA1950(a:int4,b:int4)
%table inpA1951(a:int4,b:int4)
%table inpA1952(a:int4,b:int4)
%table inpA1953(a:int4,b:int4)
%table inpA1954(a:int4,b:int4)
%table inpA1955(a:int4,b:int4)
%table inpA1956(a:int4,b:int4)
%table inpA1957(a:int4,b:int4)
%table inpA1958(a:int4,b:int4)
%table inpA1959(a:int4,b:int4)
%table inpA1960(a:int4,b:int4)
%table inpA1961(a:int4,b:int4)
%table inpA1962(a:int4,b:int4)
%table inpA1963(a:int4,b:int4)
%table inpA1964(a:int4,b:int4)
%table inpA1965(a:int4,b:int4)
%table inpA1966(a:int4,b:int4)
%table inpA1967(a:int4,b:int4)
%table inpA1968(a:int4,b:int4)
%table inpA1969(a:int4,b:int4)
%table inpA1970(a:int4,b:int4)
%table inpA1971(a:int4,b:int4)
%table inpA1972(a:int4,b:int4)
%table inpA1973(a:int4,b:int4)
%table inpA1974(a:int4,b:int4)
%table inpA1975(a:int4,b:int4)
%table inpA1976(a:int4,b:int4)
%table inpA1977(a:int4,b:int4)
%table inpA1978(a:int4,b:int4)
%table inpA1979(a:int4,b:int4)
%table inpA1980(a:int4,b:int4)
%table inpA1981(a:int4,b:int4)
%table inpA1982(a:int4,b:int4)
%table inpA1983(a:int4,b:int4)
%table inpA1984(a:int4,b:int4)
%table inpA1985(a:int4,b:int4)
%table inpA1986(a:int4,b:int4)
%table inpA1987(a:int4,b:int4)
%table inpA1988(a:int4,b:int4)
%table inpA1989(a:int4,b:int4)
%table inpA1990(a:int4,b:int4)
%table inpA1991(a:int4,b:int4)
%table inpA1992(a:int4,b:int4)
%table inpA1993(a:int4,b:int4)
%table inpA1994(a:int4,b:int4)
%table inpA1995(a:int4,b:int4)
%table inpA1996(a:int4,b:int4)
%table inpA1997(a:int4,b:int4)
%table inpA1998(a:int4,b:int4)
%table inpA1999(a:int4,b:int4)
%table outA(a:int4,b:int4)

outA(a,b):-inpA1(a,b).
outA(a,b):-inpA2(a,b).
outA(a,b):-inpA3(a,b).
outA(a,b):-inpA4(a,b).
outA(a,b):-inpA5(a,b).
outA(a,b):-inpA6(a,b).
outA(a,b):-inpA7(a,b).
outA(a,b):-inpA8(a,b).
outA(a,b):-inpA9(a,b).
outA(a,b):-inpA10(a,b).
outA(a,b):-inpA11(a,b).
outA(a,b):-inpA12(a,b).
outA(a,b):-inpA13(a,b).
outA(a,b):-inpA14(a,b).
outA(a,b):-inpA15(a,b).
outA(a,b):-inpA16(a,b).
outA(a,b):-inpA17(a,b).
outA(a,b):-inpA18(a,b).
outA(a,b):-inpA19(a,b).
outA(a,b):-inpA20(a,b).
outA(a,b):-inpA21(a,b).
outA(a,b):-inpA22(a,b).
outA(a,b):-inpA23(a,b).
outA(a,b):-inpA24(a,b).
outA(a,b):-inpA25(a,b).
outA(a,b):-inpA26(a,b).
outA(a,b):-inpA27(a,b).
outA(a,b):-inpA28(a,b).
outA(a,b):-inpA29(a,b).
outA(a,b):-inpA30(a,b).
outA(a,b):-inpA31(a,b).
outA(a,b):-inpA32(a,b).
outA(a,b):-inpA33(a,b).
outA(a,b):-inpA34(a,b).
outA(a,b):-inpA35(a,b).
outA(a,b):-inpA36(a,b).
outA(a,b):-inpA37(a,b).
outA(a,b):-inpA38(a,b).
outA(a,b):-inpA39(a,b).
outA(a,b):-inpA40(a,b).
outA(a,b):-inpA41(a,b).
outA(a,b):-inpA42(a,b).
outA(a,b):-inpA43(a,b).
outA(a,b):-inpA44(a,b).
outA(a,b):-inpA45(a,b).
outA(a,b):-inpA46(a,b).
outA(a,b):-inpA47(a,b).
outA(a,b):-inpA48(a,b).
outA(a,b):-inpA49(a,b).
outA(a,b):-inpA50(a,b).
outA(a,b):-inpA51(a,b).
outA(a,b):-inpA52(a,b).
outA(a,b):-inpA53(a,b).
outA(a,b):-inpA54(a,b).
outA(a,b):-inpA55(a,b).
outA(a,b):-inpA56(a,b).
outA(a,b):-inpA57(a,b).
outA(a,b):-inpA58(a,b).
outA(a,b):-inpA59(a,b).
outA(a,b):-inpA60(a,b).
outA(a,b):-inpA61(a,b).
outA(a,b):-inpA62(a,b).
outA(a,b):-inpA63(a,b).
outA(a,b):-inpA64(a,b).
outA(a,b):-inpA65(a,b).
outA(a,b):-inpA66(a,b).
outA(a,b):-inpA67(a,b).
outA(a,b):-inpA68(a,b).
outA(a,b):-inpA69(a,b).
outA(a,b):-inpA70(a,b).
outA(a,b):-inpA71(a,b).
outA(a,b):-inpA72(a,b).
outA(a,b):-inpA73(a,b).
outA(a,b):-inpA74(a,b).
outA(a,b):-inpA75(a,b).
outA(a,b):-inpA76(a,b).
outA(a,b):-inpA77(a,b).
outA(a,b):-inpA78(a,b).
outA(a,b):-inpA79(a,b).
outA(a,b):-inpA80(a,b).
outA(a,b):-inpA81(a,b).
outA(a,b):-inpA82(a,b).
outA(a,b):-inpA83(a,b).
outA(a,b):-inpA84(a,b).
outA(a,b):-inpA85(a,b).
outA(a,b):-inpA86(a,b).
outA(a,b):-inpA87(a,b).
outA(a,b):-inpA88(a,b).
outA(a,b):-inpA89(a,b).
outA(a,b):-inpA90(a,b).
outA(a,b):-inpA91(a,b).
outA(a,b):-inpA92(a,b).
outA(a,b):-inpA93(a,b).
outA(a,b):-inpA94(a,b).
outA(a,b):-inpA95(a,b).
outA(a,b):-inpA96(a,b).
outA(a,b):-inpA97(a,b).
outA(a,b):-inpA98(a,b).
outA(a,b):-inpA99(a,b).
outA(a,b):-inpA100(a,b).
outA(a,b):-inpA101(a,b).
outA(a,b):-inpA102(a,b).
outA(a,b):-inpA103(a,b).
outA(a,b):-inpA104(a,b).
outA(a,b):-inpA105(a,b).
outA(a,b):-inpA106(a,b).
outA(a,b):-inpA107(a,b).
outA(a,b):-inpA108(a,b).
outA(a,b):-inpA109(a,b).
outA(a,b):-inpA110(a,b).
outA(a,b):-inpA111(a,b).
outA(a,b):-inpA112(a,b).
outA(a,b):-inpA113(a,b).
outA(a,b):-inpA114(a,b).
outA(a,b):-inpA115(a,b).
outA(a,b):-inpA116(a,b).
outA(a,b):-inpA117(a,b).
outA(a,b):-inpA118(a,b).
outA(a,b):-inpA119(a,b).
outA(a,b):-inpA120(a,b).
outA(a,b):-inpA121(a,b).
outA(a,b):-inpA122(a,b).
outA(a,b):-inpA123(a,b).
outA(a,b):-inpA124(a,b).
outA(a,b):-inpA125(a,b).
outA(a,b):-inpA126(a,b).
outA(a,b):-inpA127(a,b).
outA(a,b):-inpA128(a,b).
outA(a,b):-inpA129(a,b).
outA(a,b):-inpA130(a,b).
outA(a,b):-inpA131(a,b).
outA(a,b):-inpA132(a,b).
outA(a,b):-inpA133(a,b).
outA(a,b):-inpA134(a,b).
outA(a,b):-inpA135(a,b).
outA(a,b):-inpA136(a,b).
outA(a,b):-inpA137(a,b).
outA(a,b):-inpA138(a,b).
outA(a,b):-inpA139(a,b).
outA(a,b):-inpA140(a,b).
outA(a,b):-inpA141(a,b).
outA(a,b):-inpA142(a,b).
outA(a,b):-inpA143(a,b).
outA(a,b):-inpA144(a,b).
outA(a,b):-inpA145(a,b).
outA(a,b):-inpA146(a,b).
outA(a,b):-inpA147(a,b).
outA(a,b):-inpA148(a,b).
outA(a,b):-inpA149(a,b).
outA(a,b):-inpA150(a,b).
outA(a,b):-inpA151(a,b).
outA(a,b):-inpA152(a,b).
outA(a,b):-inpA153(a,b).
outA(a,b):-inpA154(a,b).
outA(a,b):-inpA155(a,b).
outA(a,b):-inpA156(a,b).
outA(a,b):-inpA157(a,b).
outA(a,b):-inpA158(a,b).
outA(a,b):-inpA159(a,b).
outA(a,b):-inpA160(a,b).
outA(a,b):-inpA161(a,b).
outA(a,b):-inpA162(a,b).
outA(a,b):-inpA163(a,b).
outA(a,b):-inpA164(a,b).
outA(a,b):-inpA165(a,b).
outA(a,b):-inpA166(a,b).
outA(a,b):-inpA167(a,b).
outA(a,b):-inpA168(a,b).
outA(a,b):-inpA169(a,b).
outA(a,b):-inpA170(a,b).
outA(a,b):-inpA171(a,b).
outA(a,b):-inpA172(a,b).
outA(a,b):-inpA173(a,b).
outA(a,b):-inpA174(a,b).
outA(a,b):-inpA175(a,b).
outA(a,b):-inpA176(a,b).
outA(a,b):-inpA177(a,b).
outA(a,b):-inpA178(a,b).
outA(a,b):-inpA179(a,b).
outA(a,b):-inpA180(a,b).
outA(a,b):-inpA181(a,b).
outA(a,b):-inpA182(a,b).
outA(a,b):-inpA183(a,b).
outA(a,b):-inpA184(a,b).
outA(a,b):-inpA185(a,b).
outA(a,b):-inpA186(a,b).
outA(a,b):-inpA187(a,b).
outA(a,b):-inpA188(a,b).
outA(a,b):-inpA189(a,b).
outA(a,b):-inpA190(a,b).
outA(a,b):-inpA191(a,b).
outA(a,b):-inpA192(a,b).
outA(a,b):-inpA193(a,b).
outA(a,b):-inpA194(a,b).
outA(a,b):-inpA195(a,b).
outA(a,b):-inpA196(a,b).
outA(a,b):-inpA197(a,b).
outA(a,b):-inpA198(a,b).
outA(a,b):-inpA199(a,b).
outA(a,b):-inpA200(a,b).
outA(a,b):-inpA201(a,b).
outA(a,b):-inpA202(a,b).
outA(a,b):-inpA203(a,b).
outA(a,b):-inpA204(a,b).
outA(a,b):-inpA205(a,b).
outA(a,b):-inpA206(a,b).
outA(a,b):-inpA207(a,b).
outA(a,b):-inpA208(a,b).
outA(a,b):-inpA209(a,b).
outA(a,b):-inpA210(a,b).
outA(a,b):-inpA211(a,b).
outA(a,b):-inpA212(a,b).
outA(a,b):-inpA213(a,b).
outA(a,b):-inpA214(a,b).
outA(a,b):-inpA215(a,b).
outA(a,b):-inpA216(a,b).
outA(a,b):-inpA217(a,b).
outA(a,b):-inpA218(a,b).
outA(a,b):-inpA219(a,b).
outA(a,b):-inpA220(a,b).
outA(a,b):-inpA221(a,b).
outA(a,b):-inpA222(a,b).
outA(a,b):-inpA223(a,b).
outA(a,b):-inpA224(a,b).
outA(a,b):-inpA225(a,b).
outA(a,b):-inpA226(a,b).
outA(a,b):-inpA227(a,b).
outA(a,b):-inpA228(a,b).
outA(a,b):-inpA229(a,b).
outA(a,b):-inpA230(a,b).
outA(a,b):-inpA231(a,b).
outA(a,b):-inpA232(a,b).
outA(a,b):-inpA233(a,b).
outA(a,b):-inpA234(a,b).
outA(a,b):-inpA235(a,b).
outA(a,b):-inpA236(a,b).
outA(a,b):-inpA237(a,b).
outA(a,b):-inpA238(a,b).
outA(a,b):-inpA239(a,b).
outA(a,b):-inpA240(a,b).
outA(a,b):-inpA241(a,b).
outA(a,b):-inpA242(a,b).
outA(a,b):-inpA243(a,b).
outA(a,b):-inpA244(a,b).
outA(a,b):-inpA245(a,b).
outA(a,b):-inpA246(a,b).
outA(a,b):-inpA247(a,b).
outA(a,b):-inpA248(a,b).
outA(a,b):-inpA249(a,b).
outA(a,b):-inpA250(a,b).
outA(a,b):-inpA251(a,b).
outA(a,b):-inpA252(a,b).
outA(a,b):-inpA253(a,b).
outA(a,b):-inpA254(a,b).
outA(a,b):-inpA255(a,b).
outA(a,b):-inpA256(a,b).
outA(a,b):-inpA257(a,b).
outA(a,b):-inpA258(a,b).
outA(a,b):-inpA259(a,b).
outA(a,b):-inpA260(a,b).
outA(a,b):-inpA261(a,b).
outA(a,b):-inpA262(a,b).
outA(a,b):-inpA263(a,b).
outA(a,b):-inpA264(a,b).
outA(a,b):-inpA265(a,b).
outA(a,b):-inpA266(a,b).
outA(a,b):-inpA267(a,b).
outA(a,b):-inpA268(a,b).
outA(a,b):-inpA269(a,b).
outA(a,b):-inpA270(a,b).
outA(a,b):-inpA271(a,b).
outA(a,b):-inpA272(a,b).
outA(a,b):-inpA273(a,b).
outA(a,b):-inpA274(a,b).
outA(a,b):-inpA275(a,b).
outA(a,b):-inpA276(a,b).
outA(a,b):-inpA277(a,b).
outA(a,b):-inpA278(a,b).
outA(a,b):-inpA279(a,b).
outA(a,b):-inpA280(a,b).
outA(a,b):-inpA281(a,b).
outA(a,b):-inpA282(a,b).
outA(a,b):-inpA283(a,b).
outA(a,b):-inpA284(a,b).
outA(a,b):-inpA285(a,b).
outA(a,b):-inpA286(a,b).
outA(a,b):-inpA287(a,b).
outA(a,b):-inpA288(a,b).
outA(a,b):-inpA289(a,b).
outA(a,b):-inpA290(a,b).
outA(a,b):-inpA291(a,b).
outA(a,b):-inpA292(a,b).
outA(a,b):-inpA293(a,b).
outA(a,b):-inpA294(a,b).
outA(a,b):-inpA295(a,b).
outA(a,b):-inpA296(a,b).
outA(a,b):-inpA297(a,b).
outA(a,b):-inpA298(a,b).
outA(a,b):-inpA299(a,b).
outA(a,b):-inpA300(a,b).
outA(a,b):-inpA301(a,b).
outA(a,b):-inpA302(a,b).
outA(a,b):-inpA303(a,b).
outA(a,b):-inpA304(a,b).
outA(a,b):-inpA305(a,b).
outA(a,b):-inpA306(a,b).
outA(a,b):-inpA307(a,b).
outA(a,b):-inpA308(a,b).
outA(a,b):-inpA309(a,b).
outA(a,b):-inpA310(a,b).
outA(a,b):-inpA311(a,b).
outA(a,b):-inpA312(a,b).
outA(a,b):-inpA313(a,b).
outA(a,b):-inpA314(a,b).
outA(a,b):-inpA315(a,b).
outA(a,b):-inpA316(a,b).
outA(a,b):-inpA317(a,b).
outA(a,b):-inpA318(a,b).
outA(a,b):-inpA319(a,b).
outA(a,b):-inpA320(a,b).
outA(a,b):-inpA321(a,b).
outA(a,b):-inpA322(a,b).
outA(a,b):-inpA323(a,b).
outA(a,b):-inpA324(a,b).
outA(a,b):-inpA325(a,b).
outA(a,b):-inpA326(a,b).
outA(a,b):-inpA327(a,b).
outA(a,b):-inpA328(a,b).
outA(a,b):-inpA329(a,b).
outA(a,b):-inpA330(a,b).
outA(a,b):-inpA331(a,b).
outA(a,b):-inpA332(a,b).
outA(a,b):-inpA333(a,b).
outA(a,b):-inpA334(a,b).
outA(a,b):-inpA335(a,b).
outA(a,b):-inpA336(a,b).
outA(a,b):-inpA337(a,b).
outA(a,b):-inpA338(a,b).
outA(a,b):-inpA339(a,b).
outA(a,b):-inpA340(a,b).
outA(a,b):-inpA341(a,b).
outA(a,b):-inpA342(a,b).
outA(a,b):-inpA343(a,b).
outA(a,b):-inpA344(a,b).
outA(a,b):-inpA345(a,b).
outA(a,b):-inpA346(a,b).
outA(a,b):-inpA347(a,b).
outA(a,b):-inpA348(a,b).
outA(a,b):-inpA349(a,b).
outA(a,b):-inpA350(a,b).
outA(a,b):-inpA351(a,b).
outA(a,b):-inpA352(a,b).
outA(a,b):-inpA353(a,b).
outA(a,b):-inpA354(a,b).
outA(a,b):-inpA355(a,b).
outA(a,b):-inpA356(a,b).
outA(a,b):-inpA357(a,b).
outA(a,b):-inpA358(a,b).
outA(a,b):-inpA359(a,b).
outA(a,b):-inpA360(a,b).
outA(a,b):-inpA361(a,b).
outA(a,b):-inpA362(a,b).
outA(a,b):-inpA363(a,b).
outA(a,b):-inpA364(a,b).
outA(a,b):-inpA365(a,b).
outA(a,b):-inpA366(a,b).
outA(a,b):-inpA367(a,b).
outA(a,b):-inpA368(a,b).
outA(a,b):-inpA369(a,b).
outA(a,b):-inpA370(a,b).
outA(a,b):-inpA371(a,b).
outA(a,b):-inpA372(a,b).
outA(a,b):-inpA373(a,b).
outA(a,b):-inpA374(a,b).
outA(a,b):-inpA375(a,b).
outA(a,b):-inpA376(a,b).
outA(a,b):-inpA377(a,b).
outA(a,b):-inpA378(a,b).
outA(a,b):-inpA379(a,b).
outA(a,b):-inpA380(a,b).
outA(a,b):-inpA381(a,b).
outA(a,b):-inpA382(a,b).
outA(a,b):-inpA383(a,b).
outA(a,b):-inpA384(a,b).
outA(a,b):-inpA385(a,b).
outA(a,b):-inpA386(a,b).
outA(a,b):-inpA387(a,b).
outA(a,b):-inpA388(a,b).
outA(a,b):-inpA389(a,b).
outA(a,b):-inpA390(a,b).
outA(a,b):-inpA391(a,b).
outA(a,b):-inpA392(a,b).
outA(a,b):-inpA393(a,b).
outA(a,b):-inpA394(a,b).
outA(a,b):-inpA395(a,b).
outA(a,b):-inpA396(a,b).
outA(a,b):-inpA397(a,b).
outA(a,b):-inpA398(a,b).
outA(a,b):-inpA399(a,b).
outA(a,b):-inpA400(a,b).
outA(a,b):-inpA401(a,b).
outA(a,b):-inpA402(a,b).
outA(a,b):-inpA403(a,b).
outA(a,b):-inpA404(a,b).
outA(a,b):-inpA405(a,b).
outA(a,b):-inpA406(a,b).
outA(a,b):-inpA407(a,b).
outA(a,b):-inpA408(a,b).
outA(a,b):-inpA409(a,b).
outA(a,b):-inpA410(a,b).
outA(a,b):-inpA411(a,b).
outA(a,b):-inpA412(a,b).
outA(a,b):-inpA413(a,b).
outA(a,b):-inpA414(a,b).
outA(a,b):-inpA415(a,b).
outA(a,b):-inpA416(a,b).
outA(a,b):-inpA417(a,b).
outA(a,b):-inpA418(a,b).
outA(a,b):-inpA419(a,b).
outA(a,b):-inpA420(a,b).
outA(a,b):-inpA421(a,b).
outA(a,b):-inpA422(a,b).
outA(a,b):-inpA423(a,b).
outA(a,b):-inpA424(a,b).
outA(a,b):-inpA425(a,b).
outA(a,b):-inpA426(a,b).
outA(a,b):-inpA427(a,b).
outA(a,b):-inpA428(a,b).
outA(a,b):-inpA429(a,b).
outA(a,b):-inpA430(a,b).
outA(a,b):-inpA431(a,b).
outA(a,b):-inpA432(a,b).
outA(a,b):-inpA433(a,b).
outA(a,b):-inpA434(a,b).
outA(a,b):-inpA435(a,b).
outA(a,b):-inpA436(a,b).
outA(a,b):-inpA437(a,b).
outA(a,b):-inpA438(a,b).
outA(a,b):-inpA439(a,b).
outA(a,b):-inpA440(a,b).
outA(a,b):-inpA441(a,b).
outA(a,b):-inpA442(a,b).
outA(a,b):-inpA443(a,b).
outA(a,b):-inpA444(a,b).
outA(a,b):-inpA445(a,b).
outA(a,b):-inpA446(a,b).
outA(a,b):-inpA447(a,b).
outA(a,b):-inpA448(a,b).
outA(a,b):-inpA449(a,b).
outA(a,b):-inpA450(a,b).
outA(a,b):-inpA451(a,b).
outA(a,b):-inpA452(a,b).
outA(a,b):-inpA453(a,b).
outA(a,b):-inpA454(a,b).
outA(a,b):-inpA455(a,b).
outA(a,b):-inpA456(a,b).
outA(a,b):-inpA457(a,b).
outA(a,b):-inpA458(a,b).
outA(a,b):-inpA459(a,b).
outA(a,b):-inpA460(a,b).
outA(a,b):-inpA461(a,b).
outA(a,b):-inpA462(a,b).
outA(a,b):-inpA463(a,b).
outA(a,b):-inpA464(a,b).
outA(a,b):-inpA465(a,b).
outA(a,b):-inpA466(a,b).
outA(a,b):-inpA467(a,b).
outA(a,b):-inpA468(a,b).
outA(a,b):-inpA469(a,b).
outA(a,b):-inpA470(a,b).
outA(a,b):-inpA471(a,b).
outA(a,b):-inpA472(a,b).
outA(a,b):-inpA473(a,b).
outA(a,b):-inpA474(a,b).
outA(a,b):-inpA475(a,b).
outA(a,b):-inpA476(a,b).
outA(a,b):-inpA477(a,b).
outA(a,b):-inpA478(a,b).
outA(a,b):-inpA479(a,b).
outA(a,b):-inpA480(a,b).
outA(a,b):-inpA481(a,b).
outA(a,b):-inpA482(a,b).
outA(a,b):-inpA483(a,b).
outA(a,b):-inpA484(a,b).
outA(a,b):-inpA485(a,b).
outA(a,b):-inpA486(a,b).
outA(a,b):-inpA487(a,b).
outA(a,b):-inpA488(a,b).
outA(a,b):-inpA489(a,b).
outA(a,b):-inpA490(a,b).
outA(a,b):-inpA491(a,b).
outA(a,b):-inpA492(a,b).
outA(a,b):-inpA493(a,b).
outA(a,b):-inpA494(a,b).
outA(a,b):-inpA495(a,b).
outA(a,b):-inpA496(a,b).
outA(a,b):-inpA497(a,b).
outA(a,b):-inpA498(a,b).
outA(a,b):-inpA499(a,b).
outA(a,b):-inpA500(a,b).
outA(a,b):-inpA501(a,b).
outA(a,b):-inpA502(a,b).
outA(a,b):-inpA503(a,b).
outA(a,b):-inpA504(a,b).
outA(a,b):-inpA505(a,b).
outA(a,b):-inpA506(a,b).
outA(a,b):-inpA507(a,b).
outA(a,b):-inpA508(a,b).
outA(a,b):-inpA509(a,b).
outA(a,b):-inpA510(a,b).
outA(a,b):-inpA511(a,b).
outA(a,b):-inpA512(a,b).
outA(a,b):-inpA513(a,b).
outA(a,b):-inpA514(a,b).
outA(a,b):-inpA515(a,b).
outA(a,b):-inpA516(a,b).
outA(a,b):-inpA517(a,b).
outA(a,b):-inpA518(a,b).
outA(a,b):-inpA519(a,b).
outA(a,b):-inpA520(a,b).
outA(a,b):-inpA521(a,b).
outA(a,b):-inpA522(a,b).
outA(a,b):-inpA523(a,b).
outA(a,b):-inpA524(a,b).
outA(a,b):-inpA525(a,b).
outA(a,b):-inpA526(a,b).
outA(a,b):-inpA527(a,b).
outA(a,b):-inpA528(a,b).
outA(a,b):-inpA529(a,b).
outA(a,b):-inpA530(a,b).
outA(a,b):-inpA531(a,b).
outA(a,b):-inpA532(a,b).
outA(a,b):-inpA533(a,b).
outA(a,b):-inpA534(a,b).
outA(a,b):-inpA535(a,b).
outA(a,b):-inpA536(a,b).
outA(a,b):-inpA537(a,b).
outA(a,b):-inpA538(a,b).
outA(a,b):-inpA539(a,b).
outA(a,b):-inpA540(a,b).
outA(a,b):-inpA541(a,b).
outA(a,b):-inpA542(a,b).
outA(a,b):-inpA543(a,b).
outA(a,b):-inpA544(a,b).
outA(a,b):-inpA545(a,b).
outA(a,b):-inpA546(a,b).
outA(a,b):-inpA547(a,b).
outA(a,b):-inpA548(a,b).
outA(a,b):-inpA549(a,b).
outA(a,b):-inpA550(a,b).
outA(a,b):-inpA551(a,b).
outA(a,b):-inpA552(a,b).
outA(a,b):-inpA553(a,b).
outA(a,b):-inpA554(a,b).
outA(a,b):-inpA555(a,b).
outA(a,b):-inpA556(a,b).
outA(a,b):-inpA557(a,b).
outA(a,b):-inpA558(a,b).
outA(a,b):-inpA559(a,b).
outA(a,b):-inpA560(a,b).
outA(a,b):-inpA561(a,b).
outA(a,b):-inpA562(a,b).
outA(a,b):-inpA563(a,b).
outA(a,b):-inpA564(a,b).
outA(a,b):-inpA565(a,b).
outA(a,b):-inpA566(a,b).
outA(a,b):-inpA567(a,b).
outA(a,b):-inpA568(a,b).
outA(a,b):-inpA569(a,b).
outA(a,b):-inpA570(a,b).
outA(a,b):-inpA571(a,b).
outA(a,b):-inpA572(a,b).
outA(a,b):-inpA573(a,b).
outA(a,b):-inpA574(a,b).
outA(a,b):-inpA575(a,b).
outA(a,b):-inpA576(a,b).
outA(a,b):-inpA577(a,b).
outA(a,b):-inpA578(a,b).
outA(a,b):-inpA579(a,b).
outA(a,b):-inpA580(a,b).
outA(a,b):-inpA581(a,b).
outA(a,b):-inpA582(a,b).
outA(a,b):-inpA583(a,b).
outA(a,b):-inpA584(a,b).
outA(a,b):-inpA585(a,b).
outA(a,b):-inpA586(a,b).
outA(a,b):-inpA587(a,b).
outA(a,b):-inpA588(a,b).
outA(a,b):-inpA589(a,b).
outA(a,b):-inpA590(a,b).
outA(a,b):-inpA591(a,b).
outA(a,b):-inpA592(a,b).
outA(a,b):-inpA593(a,b).
outA(a,b):-inpA594(a,b).
outA(a,b):-inpA595(a,b).
outA(a,b):-inpA596(a,b).
outA(a,b):-inpA597(a,b).
outA(a,b):-inpA598(a,b).
outA(a,b):-inpA599(a,b).
outA(a,b):-inpA600(a,b).
outA(a,b):-inpA601(a,b).
outA(a,b):-inpA602(a,b).
outA(a,b):-inpA603(a,b).
outA(a,b):-inpA604(a,b).
outA(a,b):-inpA605(a,b).
outA(a,b):-inpA606(a,b).
outA(a,b):-inpA607(a,b).
outA(a,b):-inpA608(a,b).
outA(a,b):-inpA609(a,b).
outA(a,b):-inpA610(a,b).
outA(a,b):-inpA611(a,b).
outA(a,b):-inpA612(a,b).
outA(a,b):-inpA613(a,b).
outA(a,b):-inpA614(a,b).
outA(a,b):-inpA615(a,b).
outA(a,b):-inpA616(a,b).
outA(a,b):-inpA617(a,b).
outA(a,b):-inpA618(a,b).
outA(a,b):-inpA619(a,b).
outA(a,b):-inpA620(a,b).
outA(a,b):-inpA621(a,b).
outA(a,b):-inpA622(a,b).
outA(a,b):-inpA623(a,b).
outA(a,b):-inpA624(a,b).
outA(a,b):-inpA625(a,b).
outA(a,b):-inpA626(a,b).
outA(a,b):-inpA627(a,b).
outA(a,b):-inpA628(a,b).
outA(a,b):-inpA629(a,b).
outA(a,b):-inpA630(a,b).
outA(a,b):-inpA631(a,b).
outA(a,b):-inpA632(a,b).
outA(a,b):-inpA633(a,b).
outA(a,b):-inpA634(a,b).
outA(a,b):-inpA635(a,b).
outA(a,b):-inpA636(a,b).
outA(a,b):-inpA637(a,b).
outA(a,b):-inpA638(a,b).
outA(a,b):-inpA639(a,b).
outA(a,b):-inpA640(a,b).
outA(a,b):-inpA641(a,b).
outA(a,b):-inpA642(a,b).
outA(a,b):-inpA643(a,b).
outA(a,b):-inpA644(a,b).
outA(a,b):-inpA645(a,b).
outA(a,b):-inpA646(a,b).
outA(a,b):-inpA647(a,b).
outA(a,b):-inpA648(a,b).
outA(a,b):-inpA649(a,b).
outA(a,b):-inpA650(a,b).
outA(a,b):-inpA651(a,b).
outA(a,b):-inpA652(a,b).
outA(a,b):-inpA653(a,b).
outA(a,b):-inpA654(a,b).
outA(a,b):-inpA655(a,b).
outA(a,b):-inpA656(a,b).
outA(a,b):-inpA657(a,b).
outA(a,b):-inpA658(a,b).
outA(a,b):-inpA659(a,b).
outA(a,b):-inpA660(a,b).
outA(a,b):-inpA661(a,b).
outA(a,b):-inpA662(a,b).
outA(a,b):-inpA663(a,b).
outA(a,b):-inpA664(a,b).
outA(a,b):-inpA665(a,b).
outA(a,b):-inpA666(a,b).
outA(a,b):-inpA667(a,b).
outA(a,b):-inpA668(a,b).
outA(a,b):-inpA669(a,b).
outA(a,b):-inpA670(a,b).
outA(a,b):-inpA671(a,b).
outA(a,b):-inpA672(a,b).
outA(a,b):-inpA673(a,b).
outA(a,b):-inpA674(a,b).
outA(a,b):-inpA675(a,b).
outA(a,b):-inpA676(a,b).
outA(a,b):-inpA677(a,b).
outA(a,b):-inpA678(a,b).
outA(a,b):-inpA679(a,b).
outA(a,b):-inpA680(a,b).
outA(a,b):-inpA681(a,b).
outA(a,b):-inpA682(a,b).
outA(a,b):-inpA683(a,b).
outA(a,b):-inpA684(a,b).
outA(a,b):-inpA685(a,b).
outA(a,b):-inpA686(a,b).
outA(a,b):-inpA687(a,b).
outA(a,b):-inpA688(a,b).
outA(a,b):-inpA689(a,b).
outA(a,b):-inpA690(a,b).
outA(a,b):-inpA691(a,b).
outA(a,b):-inpA692(a,b).
outA(a,b):-inpA693(a,b).
outA(a,b):-inpA694(a,b).
outA(a,b):-inpA695(a,b).
outA(a,b):-inpA696(a,b).
outA(a,b):-inpA697(a,b).
outA(a,b):-inpA698(a,b).
outA(a,b):-inpA699(a,b).
outA(a,b):-inpA700(a,b).
outA(a,b):-inpA701(a,b).
outA(a,b):-inpA702(a,b).
outA(a,b):-inpA703(a,b).
outA(a,b):-inpA704(a,b).
outA(a,b):-inpA705(a,b).
outA(a,b):-inpA706(a,b).
outA(a,b):-inpA707(a,b).
outA(a,b):-inpA708(a,b).
outA(a,b):-inpA709(a,b).
outA(a,b):-inpA710(a,b).
outA(a,b):-inpA711(a,b).
outA(a,b):-inpA712(a,b).
outA(a,b):-inpA713(a,b).
outA(a,b):-inpA714(a,b).
outA(a,b):-inpA715(a,b).
outA(a,b):-inpA716(a,b).
outA(a,b):-inpA717(a,b).
outA(a,b):-inpA718(a,b).
outA(a,b):-inpA719(a,b).
outA(a,b):-inpA720(a,b).
outA(a,b):-inpA721(a,b).
outA(a,b):-inpA722(a,b).
outA(a,b):-inpA723(a,b).
outA(a,b):-inpA724(a,b).
outA(a,b):-inpA725(a,b).
outA(a,b):-inpA726(a,b).
outA(a,b):-inpA727(a,b).
outA(a,b):-inpA728(a,b).
outA(a,b):-inpA729(a,b).
outA(a,b):-inpA730(a,b).
outA(a,b):-inpA731(a,b).
outA(a,b):-inpA732(a,b).
outA(a,b):-inpA733(a,b).
outA(a,b):-inpA734(a,b).
outA(a,b):-inpA735(a,b).
outA(a,b):-inpA736(a,b).
outA(a,b):-inpA737(a,b).
outA(a,b):-inpA738(a,b).
outA(a,b):-inpA739(a,b).
outA(a,b):-inpA740(a,b).
outA(a,b):-inpA741(a,b).
outA(a,b):-inpA742(a,b).
outA(a,b):-inpA743(a,b).
outA(a,b):-inpA744(a,b).
outA(a,b):-inpA745(a,b).
outA(a,b):-inpA746(a,b).
outA(a,b):-inpA747(a,b).
outA(a,b):-inpA748(a,b).
outA(a,b):-inpA749(a,b).
outA(a,b):-inpA750(a,b).
outA(a,b):-inpA751(a,b).
outA(a,b):-inpA752(a,b).
outA(a,b):-inpA753(a,b).
outA(a,b):-inpA754(a,b).
outA(a,b):-inpA755(a,b).
outA(a,b):-inpA756(a,b).
outA(a,b):-inpA757(a,b).
outA(a,b):-inpA758(a,b).
outA(a,b):-inpA759(a,b).
outA(a,b):-inpA760(a,b).
outA(a,b):-inpA761(a,b).
outA(a,b):-inpA762(a,b).
outA(a,b):-inpA763(a,b).
outA(a,b):-inpA764(a,b).
outA(a,b):-inpA765(a,b).
outA(a,b):-inpA766(a,b).
outA(a,b):-inpA767(a,b).
outA(a,b):-inpA768(a,b).
outA(a,b):-inpA769(a,b).
outA(a,b):-inpA770(a,b).
outA(a,b):-inpA771(a,b).
outA(a,b):-inpA772(a,b).
outA(a,b):-inpA773(a,b).
outA(a,b):-inpA774(a,b).
outA(a,b):-inpA775(a,b).
outA(a,b):-inpA776(a,b).
outA(a,b):-inpA777(a,b).
outA(a,b):-inpA778(a,b).
outA(a,b):-inpA779(a,b).
outA(a,b):-inpA780(a,b).
outA(a,b):-inpA781(a,b).
outA(a,b):-inpA782(a,b).
outA(a,b):-inpA783(a,b).
outA(a,b):-inpA784(a,b).
outA(a,b):-inpA785(a,b).
outA(a,b):-inpA786(a,b).
outA(a,b):-inpA787(a,b).
outA(a,b):-inpA788(a,b).
outA(a,b):-inpA789(a,b).
outA(a,b):-inpA790(a,b).
outA(a,b):-inpA791(a,b).
outA(a,b):-inpA792(a,b).
outA(a,b):-inpA793(a,b).
outA(a,b):-inpA794(a,b).
outA(a,b):-inpA795(a,b).
outA(a,b):-inpA796(a,b).
outA(a,b):-inpA797(a,b).
outA(a,b):-inpA798(a,b).
outA(a,b):-inpA799(a,b).
outA(a,b):-inpA800(a,b).
outA(a,b):-inpA801(a,b).
outA(a,b):-inpA802(a,b).
outA(a,b):-inpA803(a,b).
outA(a,b):-inpA804(a,b).
outA(a,b):-inpA805(a,b).
outA(a,b):-inpA806(a,b).
outA(a,b):-inpA807(a,b).
outA(a,b):-inpA808(a,b).
outA(a,b):-inpA809(a,b).
outA(a,b):-inpA810(a,b).
outA(a,b):-inpA811(a,b).
outA(a,b):-inpA812(a,b).
outA(a,b):-inpA813(a,b).
outA(a,b):-inpA814(a,b).
outA(a,b):-inpA815(a,b).
outA(a,b):-inpA816(a,b).
outA(a,b):-inpA817(a,b).
outA(a,b):-inpA818(a,b).
outA(a,b):-inpA819(a,b).
outA(a,b):-inpA820(a,b).
outA(a,b):-inpA821(a,b).
outA(a,b):-inpA822(a,b).
outA(a,b):-inpA823(a,b).
outA(a,b):-inpA824(a,b).
outA(a,b):-inpA825(a,b).
outA(a,b):-inpA826(a,b).
outA(a,b):-inpA827(a,b).
outA(a,b):-inpA828(a,b).
outA(a,b):-inpA829(a,b).
outA(a,b):-inpA830(a,b).
outA(a,b):-inpA831(a,b).
outA(a,b):-inpA832(a,b).
outA(a,b):-inpA833(a,b).
outA(a,b):-inpA834(a,b).
outA(a,b):-inpA835(a,b).
outA(a,b):-inpA836(a,b).
outA(a,b):-inpA837(a,b).
outA(a,b):-inpA838(a,b).
outA(a,b):-inpA839(a,b).
outA(a,b):-inpA840(a,b).
outA(a,b):-inpA841(a,b).
outA(a,b):-inpA842(a,b).
outA(a,b):-inpA843(a,b).
outA(a,b):-inpA844(a,b).
outA(a,b):-inpA845(a,b).
outA(a,b):-inpA846(a,b).
outA(a,b):-inpA847(a,b).
outA(a,b):-inpA848(a,b).
outA(a,b):-inpA849(a,b).
outA(a,b):-inpA850(a,b).
outA(a,b):-inpA851(a,b).
outA(a,b):-inpA852(a,b).
outA(a,b):-inpA853(a,b).
outA(a,b):-inpA854(a,b).
outA(a,b):-inpA855(a,b).
outA(a,b):-inpA856(a,b).
outA(a,b):-inpA857(a,b).
outA(a,b):-inpA858(a,b).
outA(a,b):-inpA859(a,b).
outA(a,b):-inpA860(a,b).
outA(a,b):-inpA861(a,b).
outA(a,b):-inpA862(a,b).
outA(a,b):-inpA863(a,b).
outA(a,b):-inpA864(a,b).
outA(a,b):-inpA865(a,b).
outA(a,b):-inpA866(a,b).
outA(a,b):-inpA867(a,b).
outA(a,b):-inpA868(a,b).
outA(a,b):-inpA869(a,b).
outA(a,b):-inpA870(a,b).
outA(a,b):-inpA871(a,b).
outA(a,b):-inpA872(a,b).
outA(a,b):-inpA873(a,b).
outA(a,b):-inpA874(a,b).
outA(a,b):-inpA875(a,b).
outA(a,b):-inpA876(a,b).
outA(a,b):-inpA877(a,b).
outA(a,b):-inpA878(a,b).
outA(a,b):-inpA879(a,b).
outA(a,b):-inpA880(a,b).
outA(a,b):-inpA881(a,b).
outA(a,b):-inpA882(a,b).
outA(a,b):-inpA883(a,b).
outA(a,b):-inpA884(a,b).
outA(a,b):-inpA885(a,b).
outA(a,b):-inpA886(a,b).
outA(a,b):-inpA887(a,b).
outA(a,b):-inpA888(a,b).
outA(a,b):-inpA889(a,b).
outA(a,b):-inpA890(a,b).
outA(a,b):-inpA891(a,b).
outA(a,b):-inpA892(a,b).
outA(a,b):-inpA893(a,b).
outA(a,b):-inpA894(a,b).
outA(a,b):-inpA895(a,b).
outA(a,b):-inpA896(a,b).
outA(a,b):-inpA897(a,b).
outA(a,b):-inpA898(a,b).
outA(a,b):-inpA899(a,b).
outA(a,b):-inpA900(a,b).
outA(a,b):-inpA901(a,b).
outA(a,b):-inpA902(a,b).
outA(a,b):-inpA903(a,b).
outA(a,b):-inpA904(a,b).
outA(a,b):-inpA905(a,b).
outA(a,b):-inpA906(a,b).
outA(a,b):-inpA907(a,b).
outA(a,b):-inpA908(a,b).
outA(a,b):-inpA909(a,b).
outA(a,b):-inpA910(a,b).
outA(a,b):-inpA911(a,b).
outA(a,b):-inpA912(a,b).
outA(a,b):-inpA913(a,b).
outA(a,b):-inpA914(a,b).
outA(a,b):-inpA915(a,b).
outA(a,b):-inpA916(a,b).
outA(a,b):-inpA917(a,b).
outA(a,b):-inpA918(a,b).
outA(a,b):-inpA919(a,b).
outA(a,b):-inpA920(a,b).
outA(a,b):-inpA921(a,b).
outA(a,b):-inpA922(a,b).
outA(a,b):-inpA923(a,b).
outA(a,b):-inpA924(a,b).
outA(a,b):-inpA925(a,b).
outA(a,b):-inpA926(a,b).
outA(a,b):-inpA927(a,b).
outA(a,b):-inpA928(a,b).
outA(a,b):-inpA929(a,b).
outA(a,b):-inpA930(a,b).
outA(a,b):-inpA931(a,b).
outA(a,b):-inpA932(a,b).
outA(a,b):-inpA933(a,b).
outA(a,b):-inpA934(a,b).
outA(a,b):-inpA935(a,b).
outA(a,b):-inpA936(a,b).
outA(a,b):-inpA937(a,b).
outA(a,b):-inpA938(a,b).
outA(a,b):-inpA939(a,b).
outA(a,b):-inpA940(a,b).
outA(a,b):-inpA941(a,b).
outA(a,b):-inpA942(a,b).
outA(a,b):-inpA943(a,b).
outA(a,b):-inpA944(a,b).
outA(a,b):-inpA945(a,b).
outA(a,b):-inpA946(a,b).
outA(a,b):-inpA947(a,b).
outA(a,b):-inpA948(a,b).
outA(a,b):-inpA949(a,b).
outA(a,b):-inpA950(a,b).
outA(a,b):-inpA951(a,b).
outA(a,b):-inpA952(a,b).
outA(a,b):-inpA953(a,b).
outA(a,b):-inpA954(a,b).
outA(a,b):-inpA955(a,b).
outA(a,b):-inpA956(a,b).
outA(a,b):-inpA957(a,b).
outA(a,b):-inpA958(a,b).
outA(a,b):-inpA959(a,b).
outA(a,b):-inpA960(a,b).
outA(a,b):-inpA961(a,b).
outA(a,b):-inpA962(a,b).
outA(a,b):-inpA963(a,b).
outA(a,b):-inpA964(a,b).
outA(a,b):-inpA965(a,b).
outA(a,b):-inpA966(a,b).
outA(a,b):-inpA967(a,b).
outA(a,b):-inpA968(a,b).
outA(a,b):-inpA969(a,b).
outA(a,b):-inpA970(a,b).
outA(a,b):-inpA971(a,b).
outA(a,b):-inpA972(a,b).
outA(a,b):-inpA973(a,b).
outA(a,b):-inpA974(a,b).
outA(a,b):-inpA975(a,b).
outA(a,b):-inpA976(a,b).
outA(a,b):-inpA977(a,b).
outA(a,b):-inpA978(a,b).
outA(a,b):-inpA979(a,b).
outA(a,b):-inpA980(a,b).
outA(a,b):-inpA981(a,b).
outA(a,b):-inpA982(a,b).
outA(a,b):-inpA983(a,b).
outA(a,b):-inpA984(a,b).
outA(a,b):-inpA985(a,b).
outA(a,b):-inpA986(a,b).
outA(a,b):-inpA987(a,b).
outA(a,b):-inpA988(a,b).
outA(a,b):-inpA989(a,b).
outA(a,b):-inpA990(a,b).
outA(a,b):-inpA991(a,b).
outA(a,b):-inpA992(a,b).
outA(a,b):-inpA993(a,b).
outA(a,b):-inpA994(a,b).
outA(a,b):-inpA995(a,b).
outA(a,b):-inpA996(a,b).
outA(a,b):-inpA997(a,b).
outA(a,b):-inpA998(a,b).
outA(a,b):-inpA999(a,b).
outA(a,b):-inpA1000(a,b).
outA(a,b):-inpA1001(a,b).
outA(a,b):-inpA1002(a,b).
outA(a,b):-inpA1003(a,b).
outA(a,b):-inpA1004(a,b).
outA(a,b):-inpA1005(a,b).
outA(a,b):-inpA1006(a,b).
outA(a,b):-inpA1007(a,b).
outA(a,b):-inpA1008(a,b).
outA(a,b):-inpA1009(a,b).
outA(a,b):-inpA1010(a,b).
outA(a,b):-inpA1011(a,b).
outA(a,b):-inpA1012(a,b).
outA(a,b):-inpA1013(a,b).
outA(a,b):-inpA1014(a,b).
outA(a,b):-inpA1015(a,b).
outA(a,b):-inpA1016(a,b).
outA(a,b):-inpA1017(a,b).
outA(a,b):-inpA1018(a,b).
outA(a,b):-inpA1019(a,b).
outA(a,b):-inpA1020(a,b).
outA(a,b):-inpA1021(a,b).
outA(a,b):-inpA1022(a,b).
outA(a,b):-inpA1023(a,b).
outA(a,b):-inpA1024(a,b).
outA(a,b):-inpA1025(a,b).
outA(a,b):-inpA1026(a,b).
outA(a,b):-inpA1027(a,b).
outA(a,b):-inpA1028(a,b).
outA(a,b):-inpA1029(a,b).
outA(a,b):-inpA1030(a,b).
outA(a,b):-inpA1031(a,b).
outA(a,b):-inpA1032(a,b).
outA(a,b):-inpA1033(a,b).
outA(a,b):-inpA1034(a,b).
outA(a,b):-inpA1035(a,b).
outA(a,b):-inpA1036(a,b).
outA(a,b):-inpA1037(a,b).
outA(a,b):-inpA1038(a,b).
outA(a,b):-inpA1039(a,b).
outA(a,b):-inpA1040(a,b).
outA(a,b):-inpA1041(a,b).
outA(a,b):-inpA1042(a,b).
outA(a,b):-inpA1043(a,b).
outA(a,b):-inpA1044(a,b).
outA(a,b):-inpA1045(a,b).
outA(a,b):-inpA1046(a,b).
outA(a,b):-inpA1047(a,b).
outA(a,b):-inpA1048(a,b).
outA(a,b):-inpA1049(a,b).
outA(a,b):-inpA1050(a,b).
outA(a,b):-inpA1051(a,b).
outA(a,b):-inpA1052(a,b).
outA(a,b):-inpA1053(a,b).
outA(a,b):-inpA1054(a,b).
outA(a,b):-inpA1055(a,b).
outA(a,b):-inpA1056(a,b).
outA(a,b):-inpA1057(a,b).
outA(a,b):-inpA1058(a,b).
outA(a,b):-inpA1059(a,b).
outA(a,b):-inpA1060(a,b).
outA(a,b):-inpA1061(a,b).
outA(a,b):-inpA1062(a,b).
outA(a,b):-inpA1063(a,b).
outA(a,b):-inpA1064(a,b).
outA(a,b):-inpA1065(a,b).
outA(a,b):-inpA1066(a,b).
outA(a,b):-inpA1067(a,b).
outA(a,b):-inpA1068(a,b).
outA(a,b):-inpA1069(a,b).
outA(a,b):-inpA1070(a,b).
outA(a,b):-inpA1071(a,b).
outA(a,b):-inpA1072(a,b).
outA(a,b):-inpA1073(a,b).
outA(a,b):-inpA1074(a,b).
outA(a,b):-inpA1075(a,b).
outA(a,b):-inpA1076(a,b).
outA(a,b):-inpA1077(a,b).
outA(a,b):-inpA1078(a,b).
outA(a,b):-inpA1079(a,b).
outA(a,b):-inpA1080(a,b).
outA(a,b):-inpA1081(a,b).
outA(a,b):-inpA1082(a,b).
outA(a,b):-inpA1083(a,b).
outA(a,b):-inpA1084(a,b).
outA(a,b):-inpA1085(a,b).
outA(a,b):-inpA1086(a,b).
outA(a,b):-inpA1087(a,b).
outA(a,b):-inpA1088(a,b).
outA(a,b):-inpA1089(a,b).
outA(a,b):-inpA1090(a,b).
outA(a,b):-inpA1091(a,b).
outA(a,b):-inpA1092(a,b).
outA(a,b):-inpA1093(a,b).
outA(a,b):-inpA1094(a,b).
outA(a,b):-inpA1095(a,b).
outA(a,b):-inpA1096(a,b).
outA(a,b):-inpA1097(a,b).
outA(a,b):-inpA1098(a,b).
outA(a,b):-inpA1099(a,b).
outA(a,b):-inpA1100(a,b).
outA(a,b):-inpA1101(a,b).
outA(a,b):-inpA1102(a,b).
outA(a,b):-inpA1103(a,b).
outA(a,b):-inpA1104(a,b).
outA(a,b):-inpA1105(a,b).
outA(a,b):-inpA1106(a,b).
outA(a,b):-inpA1107(a,b).
outA(a,b):-inpA1108(a,b).
outA(a,b):-inpA1109(a,b).
outA(a,b):-inpA1110(a,b).
outA(a,b):-inpA1111(a,b).
outA(a,b):-inpA1112(a,b).
outA(a,b):-inpA1113(a,b).
outA(a,b):-inpA1114(a,b).
outA(a,b):-inpA1115(a,b).
outA(a,b):-inpA1116(a,b).
outA(a,b):-inpA1117(a,b).
outA(a,b):-inpA1118(a,b).
outA(a,b):-inpA1119(a,b).
outA(a,b):-inpA1120(a,b).
outA(a,b):-inpA1121(a,b).
outA(a,b):-inpA1122(a,b).
outA(a,b):-inpA1123(a,b).
outA(a,b):-inpA1124(a,b).
outA(a,b):-inpA1125(a,b).
outA(a,b):-inpA1126(a,b).
outA(a,b):-inpA1127(a,b).
outA(a,b):-inpA1128(a,b).
outA(a,b):-inpA1129(a,b).
outA(a,b):-inpA1130(a,b).
outA(a,b):-inpA1131(a,b).
outA(a,b):-inpA1132(a,b).
outA(a,b):-inpA1133(a,b).
outA(a,b):-inpA1134(a,b).
outA(a,b):-inpA1135(a,b).
outA(a,b):-inpA1136(a,b).
outA(a,b):-inpA1137(a,b).
outA(a,b):-inpA1138(a,b).
outA(a,b):-inpA1139(a,b).
outA(a,b):-inpA1140(a,b).
outA(a,b):-inpA1141(a,b).
outA(a,b):-inpA1142(a,b).
outA(a,b):-inpA1143(a,b).
outA(a,b):-inpA1144(a,b).
outA(a,b):-inpA1145(a,b).
outA(a,b):-inpA1146(a,b).
outA(a,b):-inpA1147(a,b).
outA(a,b):-inpA1148(a,b).
outA(a,b):-inpA1149(a,b).
outA(a,b):-inpA1150(a,b).
outA(a,b):-inpA1151(a,b).
outA(a,b):-inpA1152(a,b).
outA(a,b):-inpA1153(a,b).
outA(a,b):-inpA1154(a,b).
outA(a,b):-inpA1155(a,b).
outA(a,b):-inpA1156(a,b).
outA(a,b):-inpA1157(a,b).
outA(a,b):-inpA1158(a,b).
outA(a,b):-inpA1159(a,b).
outA(a,b):-inpA1160(a,b).
outA(a,b):-inpA1161(a,b).
outA(a,b):-inpA1162(a,b).
outA(a,b):-inpA1163(a,b).
outA(a,b):-inpA1164(a,b).
outA(a,b):-inpA1165(a,b).
outA(a,b):-inpA1166(a,b).
outA(a,b):-inpA1167(a,b).
outA(a,b):-inpA1168(a,b).
outA(a,b):-inpA1169(a,b).
outA(a,b):-inpA1170(a,b).
outA(a,b):-inpA1171(a,b).
outA(a,b):-inpA1172(a,b).
outA(a,b):-inpA1173(a,b).
outA(a,b):-inpA1174(a,b).
outA(a,b):-inpA1175(a,b).
outA(a,b):-inpA1176(a,b).
outA(a,b):-inpA1177(a,b).
outA(a,b):-inpA1178(a,b).
outA(a,b):-inpA1179(a,b).
outA(a,b):-inpA1180(a,b).
outA(a,b):-inpA1181(a,b).
outA(a,b):-inpA1182(a,b).
outA(a,b):-inpA1183(a,b).
outA(a,b):-inpA1184(a,b).
outA(a,b):-inpA1185(a,b).
outA(a,b):-inpA1186(a,b).
outA(a,b):-inpA1187(a,b).
outA(a,b):-inpA1188(a,b).
outA(a,b):-inpA1189(a,b).
outA(a,b):-inpA1190(a,b).
outA(a,b):-inpA1191(a,b).
outA(a,b):-inpA1192(a,b).
outA(a,b):-inpA1193(a,b).
outA(a,b):-inpA1194(a,b).
outA(a,b):-inpA1195(a,b).
outA(a,b):-inpA1196(a,b).
outA(a,b):-inpA1197(a,b).
outA(a,b):-inpA1198(a,b).
outA(a,b):-inpA1199(a,b).
outA(a,b):-inpA1200(a,b).
outA(a,b):-inpA1201(a,b).
outA(a,b):-inpA1202(a,b).
outA(a,b):-inpA1203(a,b).
outA(a,b):-inpA1204(a,b).
outA(a,b):-inpA1205(a,b).
outA(a,b):-inpA1206(a,b).
outA(a,b):-inpA1207(a,b).
outA(a,b):-inpA1208(a,b).
outA(a,b):-inpA1209(a,b).
outA(a,b):-inpA1210(a,b).
outA(a,b):-inpA1211(a,b).
outA(a,b):-inpA1212(a,b).
outA(a,b):-inpA1213(a,b).
outA(a,b):-inpA1214(a,b).
outA(a,b):-inpA1215(a,b).
outA(a,b):-inpA1216(a,b).
outA(a,b):-inpA1217(a,b).
outA(a,b):-inpA1218(a,b).
outA(a,b):-inpA1219(a,b).
outA(a,b):-inpA1220(a,b).
outA(a,b):-inpA1221(a,b).
outA(a,b):-inpA1222(a,b).
outA(a,b):-inpA1223(a,b).
outA(a,b):-inpA1224(a,b).
outA(a,b):-inpA1225(a,b).
outA(a,b):-inpA1226(a,b).
outA(a,b):-inpA1227(a,b).
outA(a,b):-inpA1228(a,b).
outA(a,b):-inpA1229(a,b).
outA(a,b):-inpA1230(a,b).
outA(a,b):-inpA1231(a,b).
outA(a,b):-inpA1232(a,b).
outA(a,b):-inpA1233(a,b).
outA(a,b):-inpA1234(a,b).
outA(a,b):-inpA1235(a,b).
outA(a,b):-inpA1236(a,b).
outA(a,b):-inpA1237(a,b).
outA(a,b):-inpA1238(a,b).
outA(a,b):-inpA1239(a,b).
outA(a,b):-inpA1240(a,b).
outA(a,b):-inpA1241(a,b).
outA(a,b):-inpA1242(a,b).
outA(a,b):-inpA1243(a,b).
outA(a,b):-inpA1244(a,b).
outA(a,b):-inpA1245(a,b).
outA(a,b):-inpA1246(a,b).
outA(a,b):-inpA1247(a,b).
outA(a,b):-inpA1248(a,b).
outA(a,b):-inpA1249(a,b).
outA(a,b):-inpA1250(a,b).
outA(a,b):-inpA1251(a,b).
outA(a,b):-inpA1252(a,b).
outA(a,b):-inpA1253(a,b).
outA(a,b):-inpA1254(a,b).
outA(a,b):-inpA1255(a,b).
outA(a,b):-inpA1256(a,b).
outA(a,b):-inpA1257(a,b).
outA(a,b):-inpA1258(a,b).
outA(a,b):-inpA1259(a,b).
outA(a,b):-inpA1260(a,b).
outA(a,b):-inpA1261(a,b).
outA(a,b):-inpA1262(a,b).
outA(a,b):-inpA1263(a,b).
outA(a,b):-inpA1264(a,b).
outA(a,b):-inpA1265(a,b).
outA(a,b):-inpA1266(a,b).
outA(a,b):-inpA1267(a,b).
outA(a,b):-inpA1268(a,b).
outA(a,b):-inpA1269(a,b).
outA(a,b):-inpA1270(a,b).
outA(a,b):-inpA1271(a,b).
outA(a,b):-inpA1272(a,b).
outA(a,b):-inpA1273(a,b).
outA(a,b):-inpA1274(a,b).
outA(a,b):-inpA1275(a,b).
outA(a,b):-inpA1276(a,b).
outA(a,b):-inpA1277(a,b).
outA(a,b):-inpA1278(a,b).
outA(a,b):-inpA1279(a,b).
outA(a,b):-inpA1280(a,b).
outA(a,b):-inpA1281(a,b).
outA(a,b):-inpA1282(a,b).
outA(a,b):-inpA1283(a,b).
outA(a,b):-inpA1284(a,b).
outA(a,b):-inpA1285(a,b).
outA(a,b):-inpA1286(a,b).
outA(a,b):-inpA1287(a,b).
outA(a,b):-inpA1288(a,b).
outA(a,b):-inpA1289(a,b).
outA(a,b):-inpA1290(a,b).
outA(a,b):-inpA1291(a,b).
outA(a,b):-inpA1292(a,b).
outA(a,b):-inpA1293(a,b).
outA(a,b):-inpA1294(a,b).
outA(a,b):-inpA1295(a,b).
outA(a,b):-inpA1296(a,b).
outA(a,b):-inpA1297(a,b).
outA(a,b):-inpA1298(a,b).
outA(a,b):-inpA1299(a,b).
outA(a,b):-inpA1300(a,b).
outA(a,b):-inpA1301(a,b).
outA(a,b):-inpA1302(a,b).
outA(a,b):-inpA1303(a,b).
outA(a,b):-inpA1304(a,b).
outA(a,b):-inpA1305(a,b).
outA(a,b):-inpA1306(a,b).
outA(a,b):-inpA1307(a,b).
outA(a,b):-inpA1308(a,b).
outA(a,b):-inpA1309(a,b).
outA(a,b):-inpA1310(a,b).
outA(a,b):-inpA1311(a,b).
outA(a,b):-inpA1312(a,b).
outA(a,b):-inpA1313(a,b).
outA(a,b):-inpA1314(a,b).
outA(a,b):-inpA1315(a,b).
outA(a,b):-inpA1316(a,b).
outA(a,b):-inpA1317(a,b).
outA(a,b):-inpA1318(a,b).
outA(a,b):-inpA1319(a,b).
outA(a,b):-inpA1320(a,b).
outA(a,b):-inpA1321(a,b).
outA(a,b):-inpA1322(a,b).
outA(a,b):-inpA1323(a,b).
outA(a,b):-inpA1324(a,b).
outA(a,b):-inpA1325(a,b).
outA(a,b):-inpA1326(a,b).
outA(a,b):-inpA1327(a,b).
outA(a,b):-inpA1328(a,b).
outA(a,b):-inpA1329(a,b).
outA(a,b):-inpA1330(a,b).
outA(a,b):-inpA1331(a,b).
outA(a,b):-inpA1332(a,b).
outA(a,b):-inpA1333(a,b).
outA(a,b):-inpA1334(a,b).
outA(a,b):-inpA1335(a,b).
outA(a,b):-inpA1336(a,b).
outA(a,b):-inpA1337(a,b).
outA(a,b):-inpA1338(a,b).
outA(a,b):-inpA1339(a,b).
outA(a,b):-inpA1340(a,b).
outA(a,b):-inpA1341(a,b).
outA(a,b):-inpA1342(a,b).
outA(a,b):-inpA1343(a,b).
outA(a,b):-inpA1344(a,b).
outA(a,b):-inpA1345(a,b).
outA(a,b):-inpA1346(a,b).
outA(a,b):-inpA1347(a,b).
outA(a,b):-inpA1348(a,b).
outA(a,b):-inpA1349(a,b).
outA(a,b):-inpA1350(a,b).
outA(a,b):-inpA1351(a,b).
outA(a,b):-inpA1352(a,b).
outA(a,b):-inpA1353(a,b).
outA(a,b):-inpA1354(a,b).
outA(a,b):-inpA1355(a,b).
outA(a,b):-inpA1356(a,b).
outA(a,b):-inpA1357(a,b).
outA(a,b):-inpA1358(a,b).
outA(a,b):-inpA1359(a,b).
outA(a,b):-inpA1360(a,b).
outA(a,b):-inpA1361(a,b).
outA(a,b):-inpA1362(a,b).
outA(a,b):-inpA1363(a,b).
outA(a,b):-inpA1364(a,b).
outA(a,b):-inpA1365(a,b).
outA(a,b):-inpA1366(a,b).
outA(a,b):-inpA1367(a,b).
outA(a,b):-inpA1368(a,b).
outA(a,b):-inpA1369(a,b).
outA(a,b):-inpA1370(a,b).
outA(a,b):-inpA1371(a,b).
outA(a,b):-inpA1372(a,b).
outA(a,b):-inpA1373(a,b).
outA(a,b):-inpA1374(a,b).
outA(a,b):-inpA1375(a,b).
outA(a,b):-inpA1376(a,b).
outA(a,b):-inpA1377(a,b).
outA(a,b):-inpA1378(a,b).
outA(a,b):-inpA1379(a,b).
outA(a,b):-inpA1380(a,b).
outA(a,b):-inpA1381(a,b).
outA(a,b):-inpA1382(a,b).
outA(a,b):-inpA1383(a,b).
outA(a,b):-inpA1384(a,b).
outA(a,b):-inpA1385(a,b).
outA(a,b):-inpA1386(a,b).
outA(a,b):-inpA1387(a,b).
outA(a,b):-inpA1388(a,b).
outA(a,b):-inpA1389(a,b).
outA(a,b):-inpA1390(a,b).
outA(a,b):-inpA1391(a,b).
outA(a,b):-inpA1392(a,b).
outA(a,b):-inpA1393(a,b).
outA(a,b):-inpA1394(a,b).
outA(a,b):-inpA1395(a,b).
outA(a,b):-inpA1396(a,b).
outA(a,b):-inpA1397(a,b).
outA(a,b):-inpA1398(a,b).
outA(a,b):-inpA1399(a,b).
outA(a,b):-inpA1400(a,b).
outA(a,b):-inpA1401(a,b).
outA(a,b):-inpA1402(a,b).
outA(a,b):-inpA1403(a,b).
outA(a,b):-inpA1404(a,b).
outA(a,b):-inpA1405(a,b).
outA(a,b):-inpA1406(a,b).
outA(a,b):-inpA1407(a,b).
outA(a,b):-inpA1408(a,b).
outA(a,b):-inpA1409(a,b).
outA(a,b):-inpA1410(a,b).
outA(a,b):-inpA1411(a,b).
outA(a,b):-inpA1412(a,b).
outA(a,b):-inpA1413(a,b).
outA(a,b):-inpA1414(a,b).
outA(a,b):-inpA1415(a,b).
outA(a,b):-inpA1416(a,b).
outA(a,b):-inpA1417(a,b).
outA(a,b):-inpA1418(a,b).
outA(a,b):-inpA1419(a,b).
outA(a,b):-inpA1420(a,b).
outA(a,b):-inpA1421(a,b).
outA(a,b):-inpA1422(a,b).
outA(a,b):-inpA1423(a,b).
outA(a,b):-inpA1424(a,b).
outA(a,b):-inpA1425(a,b).
outA(a,b):-inpA1426(a,b).
outA(a,b):-inpA1427(a,b).
outA(a,b):-inpA1428(a,b).
outA(a,b):-inpA1429(a,b).
outA(a,b):-inpA1430(a,b).
outA(a,b):-inpA1431(a,b).
outA(a,b):-inpA1432(a,b).
outA(a,b):-inpA1433(a,b).
outA(a,b):-inpA1434(a,b).
outA(a,b):-inpA1435(a,b).
outA(a,b):-inpA1436(a,b).
outA(a,b):-inpA1437(a,b).
outA(a,b):-inpA1438(a,b).
outA(a,b):-inpA1439(a,b).
outA(a,b):-inpA1440(a,b).
outA(a,b):-inpA1441(a,b).
outA(a,b):-inpA1442(a,b).
outA(a,b):-inpA1443(a,b).
outA(a,b):-inpA1444(a,b).
outA(a,b):-inpA1445(a,b).
outA(a,b):-inpA1446(a,b).
outA(a,b):-inpA1447(a,b).
outA(a,b):-inpA1448(a,b).
outA(a,b):-inpA1449(a,b).
outA(a,b):-inpA1450(a,b).
outA(a,b):-inpA1451(a,b).
outA(a,b):-inpA1452(a,b).
outA(a,b):-inpA1453(a,b).
outA(a,b):-inpA1454(a,b).
outA(a,b):-inpA1455(a,b).
outA(a,b):-inpA1456(a,b).
outA(a,b):-inpA1457(a,b).
outA(a,b):-inpA1458(a,b).
outA(a,b):-inpA1459(a,b).
outA(a,b):-inpA1460(a,b).
outA(a,b):-inpA1461(a,b).
outA(a,b):-inpA1462(a,b).
outA(a,b):-inpA1463(a,b).
outA(a,b):-inpA1464(a,b).
outA(a,b):-inpA1465(a,b).
outA(a,b):-inpA1466(a,b).
outA(a,b):-inpA1467(a,b).
outA(a,b):-inpA1468(a,b).
outA(a,b):-inpA1469(a,b).
outA(a,b):-inpA1470(a,b).
outA(a,b):-inpA1471(a,b).
outA(a,b):-inpA1472(a,b).
outA(a,b):-inpA1473(a,b).
outA(a,b):-inpA1474(a,b).
outA(a,b):-inpA1475(a,b).
outA(a,b):-inpA1476(a,b).
outA(a,b):-inpA1477(a,b).
outA(a,b):-inpA1478(a,b).
outA(a,b):-inpA1479(a,b).
outA(a,b):-inpA1480(a,b).
outA(a,b):-inpA1481(a,b).
outA(a,b):-inpA1482(a,b).
outA(a,b):-inpA1483(a,b).
outA(a,b):-inpA1484(a,b).
outA(a,b):-inpA1485(a,b).
outA(a,b):-inpA1486(a,b).
outA(a,b):-inpA1487(a,b).
outA(a,b):-inpA1488(a,b).
outA(a,b):-inpA1489(a,b).
outA(a,b):-inpA1490(a,b).
outA(a,b):-inpA1491(a,b).
outA(a,b):-inpA1492(a,b).
outA(a,b):-inpA1493(a,b).
outA(a,b):-inpA1494(a,b).
outA(a,b):-inpA1495(a,b).
outA(a,b):-inpA1496(a,b).
outA(a,b):-inpA1497(a,b).
outA(a,b):-inpA1498(a,b).
outA(a,b):-inpA1499(a,b).
outA(a,b):-inpA1500(a,b).
outA(a,b):-inpA1501(a,b).
outA(a,b):-inpA1502(a,b).
outA(a,b):-inpA1503(a,b).
outA(a,b):-inpA1504(a,b).
outA(a,b):-inpA1505(a,b).
outA(a,b):-inpA1506(a,b).
outA(a,b):-inpA1507(a,b).
outA(a,b):-inpA1508(a,b).
outA(a,b):-inpA1509(a,b).
outA(a,b):-inpA1510(a,b).
outA(a,b):-inpA1511(a,b).
outA(a,b):-inpA1512(a,b).
outA(a,b):-inpA1513(a,b).
outA(a,b):-inpA1514(a,b).
outA(a,b):-inpA1515(a,b).
outA(a,b):-inpA1516(a,b).
outA(a,b):-inpA1517(a,b).
outA(a,b):-inpA1518(a,b).
outA(a,b):-inpA1519(a,b).
outA(a,b):-inpA1520(a,b).
outA(a,b):-inpA1521(a,b).
outA(a,b):-inpA1522(a,b).
outA(a,b):-inpA1523(a,b).
outA(a,b):-inpA1524(a,b).
outA(a,b):-inpA1525(a,b).
outA(a,b):-inpA1526(a,b).
outA(a,b):-inpA1527(a,b).
outA(a,b):-inpA1528(a,b).
outA(a,b):-inpA1529(a,b).
outA(a,b):-inpA1530(a,b).
outA(a,b):-inpA1531(a,b).
outA(a,b):-inpA1532(a,b).
outA(a,b):-inpA1533(a,b).
outA(a,b):-inpA1534(a,b).
outA(a,b):-inpA1535(a,b).
outA(a,b):-inpA1536(a,b).
outA(a,b):-inpA1537(a,b).
outA(a,b):-inpA1538(a,b).
outA(a,b):-inpA1539(a,b).
outA(a,b):-inpA1540(a,b).
outA(a,b):-inpA1541(a,b).
outA(a,b):-inpA1542(a,b).
outA(a,b):-inpA1543(a,b).
outA(a,b):-inpA1544(a,b).
outA(a,b):-inpA1545(a,b).
outA(a,b):-inpA1546(a,b).
outA(a,b):-inpA1547(a,b).
outA(a,b):-inpA1548(a,b).
outA(a,b):-inpA1549(a,b).
outA(a,b):-inpA1550(a,b).
outA(a,b):-inpA1551(a,b).
outA(a,b):-inpA1552(a,b).
outA(a,b):-inpA1553(a,b).
outA(a,b):-inpA1554(a,b).
outA(a,b):-inpA1555(a,b).
outA(a,b):-inpA1556(a,b).
outA(a,b):-inpA1557(a,b).
outA(a,b):-inpA1558(a,b).
outA(a,b):-inpA1559(a,b).
outA(a,b):-inpA1560(a,b).
outA(a,b):-inpA1561(a,b).
outA(a,b):-inpA1562(a,b).
outA(a,b):-inpA1563(a,b).
outA(a,b):-inpA1564(a,b).
outA(a,b):-inpA1565(a,b).
outA(a,b):-inpA1566(a,b).
outA(a,b):-inpA1567(a,b).
outA(a,b):-inpA1568(a,b).
outA(a,b):-inpA1569(a,b).
outA(a,b):-inpA1570(a,b).
outA(a,b):-inpA1571(a,b).
outA(a,b):-inpA1572(a,b).
outA(a,b):-inpA1573(a,b).
outA(a,b):-inpA1574(a,b).
outA(a,b):-inpA1575(a,b).
outA(a,b):-inpA1576(a,b).
outA(a,b):-inpA1577(a,b).
outA(a,b):-inpA1578(a,b).
outA(a,b):-inpA1579(a,b).
outA(a,b):-inpA1580(a,b).
outA(a,b):-inpA1581(a,b).
outA(a,b):-inpA1582(a,b).
outA(a,b):-inpA1583(a,b).
outA(a,b):-inpA1584(a,b).
outA(a,b):-inpA1585(a,b).
outA(a,b):-inpA1586(a,b).
outA(a,b):-inpA1587(a,b).
outA(a,b):-inpA1588(a,b).
outA(a,b):-inpA1589(a,b).
outA(a,b):-inpA1590(a,b).
outA(a,b):-inpA1591(a,b).
outA(a,b):-inpA1592(a,b).
outA(a,b):-inpA1593(a,b).
outA(a,b):-inpA1594(a,b).
outA(a,b):-inpA1595(a,b).
outA(a,b):-inpA1596(a,b).
outA(a,b):-inpA1597(a,b).
outA(a,b):-inpA1598(a,b).
outA(a,b):-inpA1599(a,b).
outA(a,b):-inpA1600(a,b).
outA(a,b):-inpA1601(a,b).
outA(a,b):-inpA1602(a,b).
outA(a,b):-inpA1603(a,b).
outA(a,b):-inpA1604(a,b).
outA(a,b):-inpA1605(a,b).
outA(a,b):-inpA1606(a,b).
outA(a,b):-inpA1607(a,b).
outA(a,b):-inpA1608(a,b).
outA(a,b):-inpA1609(a,b).
outA(a,b):-inpA1610(a,b).
outA(a,b):-inpA1611(a,b).
outA(a,b):-inpA1612(a,b).
outA(a,b):-inpA1613(a,b).
outA(a,b):-inpA1614(a,b).
outA(a,b):-inpA1615(a,b).
outA(a,b):-inpA1616(a,b).
outA(a,b):-inpA1617(a,b).
outA(a,b):-inpA1618(a,b).
outA(a,b):-inpA1619(a,b).
outA(a,b):-inpA1620(a,b).
outA(a,b):-inpA1621(a,b).
outA(a,b):-inpA1622(a,b).
outA(a,b):-inpA1623(a,b).
outA(a,b):-inpA1624(a,b).
outA(a,b):-inpA1625(a,b).
outA(a,b):-inpA1626(a,b).
outA(a,b):-inpA1627(a,b).
outA(a,b):-inpA1628(a,b).
outA(a,b):-inpA1629(a,b).
outA(a,b):-inpA1630(a,b).
outA(a,b):-inpA1631(a,b).
outA(a,b):-inpA1632(a,b).
outA(a,b):-inpA1633(a,b).
outA(a,b):-inpA1634(a,b).
outA(a,b):-inpA1635(a,b).
outA(a,b):-inpA1636(a,b).
outA(a,b):-inpA1637(a,b).
outA(a,b):-inpA1638(a,b).
outA(a,b):-inpA1639(a,b).
outA(a,b):-inpA1640(a,b).
outA(a,b):-inpA1641(a,b).
outA(a,b):-inpA1642(a,b).
outA(a,b):-inpA1643(a,b).
outA(a,b):-inpA1644(a,b).
outA(a,b):-inpA1645(a,b).
outA(a,b):-inpA1646(a,b).
outA(a,b):-inpA1647(a,b).
outA(a,b):-inpA1648(a,b).
outA(a,b):-inpA1649(a,b).
outA(a,b):-inpA1650(a,b).
outA(a,b):-inpA1651(a,b).
outA(a,b):-inpA1652(a,b).
outA(a,b):-inpA1653(a,b).
outA(a,b):-inpA1654(a,b).
outA(a,b):-inpA1655(a,b).
outA(a,b):-inpA1656(a,b).
outA(a,b):-inpA1657(a,b).
outA(a,b):-inpA1658(a,b).
outA(a,b):-inpA1659(a,b).
outA(a,b):-inpA1660(a,b).
outA(a,b):-inpA1661(a,b).
outA(a,b):-inpA1662(a,b).
outA(a,b):-inpA1663(a,b).
outA(a,b):-inpA1664(a,b).
outA(a,b):-inpA1665(a,b).
outA(a,b):-inpA1666(a,b).
outA(a,b):-inpA1667(a,b).
outA(a,b):-inpA1668(a,b).
outA(a,b):-inpA1669(a,b).
outA(a,b):-inpA1670(a,b).
outA(a,b):-inpA1671(a,b).
outA(a,b):-inpA1672(a,b).
outA(a,b):-inpA1673(a,b).
outA(a,b):-inpA1674(a,b).
outA(a,b):-inpA1675(a,b).
outA(a,b):-inpA1676(a,b).
outA(a,b):-inpA1677(a,b).
outA(a,b):-inpA1678(a,b).
outA(a,b):-inpA1679(a,b).
outA(a,b):-inpA1680(a,b).
outA(a,b):-inpA1681(a,b).
outA(a,b):-inpA1682(a,b).
outA(a,b):-inpA1683(a,b).
outA(a,b):-inpA1684(a,b).
outA(a,b):-inpA1685(a,b).
outA(a,b):-inpA1686(a,b).
outA(a,b):-inpA1687(a,b).
outA(a,b):-inpA1688(a,b).
outA(a,b):-inpA1689(a,b).
outA(a,b):-inpA1690(a,b).
outA(a,b):-inpA1691(a,b).
outA(a,b):-inpA1692(a,b).
outA(a,b):-inpA1693(a,b).
outA(a,b):-inpA1694(a,b).
outA(a,b):-inpA1695(a,b).
outA(a,b):-inpA1696(a,b).
outA(a,b):-inpA1697(a,b).
outA(a,b):-inpA1698(a,b).
outA(a,b):-inpA1699(a,b).
outA(a,b):-inpA1700(a,b).
outA(a,b):-inpA1701(a,b).
outA(a,b):-inpA1702(a,b).
outA(a,b):-inpA1703(a,b).
outA(a,b):-inpA1704(a,b).
outA(a,b):-inpA1705(a,b).
outA(a,b):-inpA1706(a,b).
outA(a,b):-inpA1707(a,b).
outA(a,b):-inpA1708(a,b).
outA(a,b):-inpA1709(a,b).
outA(a,b):-inpA1710(a,b).
outA(a,b):-inpA1711(a,b).
outA(a,b):-inpA1712(a,b).
outA(a,b):-inpA1713(a,b).
outA(a,b):-inpA1714(a,b).
outA(a,b):-inpA1715(a,b).
outA(a,b):-inpA1716(a,b).
outA(a,b):-inpA1717(a,b).
outA(a,b):-inpA1718(a,b).
outA(a,b):-inpA1719(a,b).
outA(a,b):-inpA1720(a,b).
outA(a,b):-inpA1721(a,b).
outA(a,b):-inpA1722(a,b).
outA(a,b):-inpA1723(a,b).
outA(a,b):-inpA1724(a,b).
outA(a,b):-inpA1725(a,b).
outA(a,b):-inpA1726(a,b).
outA(a,b):-inpA1727(a,b).
outA(a,b):-inpA1728(a,b).
outA(a,b):-inpA1729(a,b).
outA(a,b):-inpA1730(a,b).
outA(a,b):-inpA1731(a,b).
outA(a,b):-inpA1732(a,b).
outA(a,b):-inpA1733(a,b).
outA(a,b):-inpA1734(a,b).
outA(a,b):-inpA1735(a,b).
outA(a,b):-inpA1736(a,b).
outA(a,b):-inpA1737(a,b).
outA(a,b):-inpA1738(a,b).
outA(a,b):-inpA1739(a,b).
outA(a,b):-inpA1740(a,b).
outA(a,b):-inpA1741(a,b).
outA(a,b):-inpA1742(a,b).
outA(a,b):-inpA1743(a,b).
outA(a,b):-inpA1744(a,b).
outA(a,b):-inpA1745(a,b).
outA(a,b):-inpA1746(a,b).
outA(a,b):-inpA1747(a,b).
outA(a,b):-inpA1748(a,b).
outA(a,b):-inpA1749(a,b).
outA(a,b):-inpA1750(a,b).
outA(a,b):-inpA1751(a,b).
outA(a,b):-inpA1752(a,b).
outA(a,b):-inpA1753(a,b).
outA(a,b):-inpA1754(a,b).
outA(a,b):-inpA1755(a,b).
outA(a,b):-inpA1756(a,b).
outA(a,b):-inpA1757(a,b).
outA(a,b):-inpA1758(a,b).
outA(a,b):-inpA1759(a,b).
outA(a,b):-inpA1760(a,b).
outA(a,b):-inpA1761(a,b).
outA(a,b):-inpA1762(a,b).
outA(a,b):-inpA1763(a,b).
outA(a,b):-inpA1764(a,b).
outA(a,b):-inpA1765(a,b).
outA(a,b):-inpA1766(a,b).
outA(a,b):-inpA1767(a,b).
outA(a,b):-inpA1768(a,b).
outA(a,b):-inpA1769(a,b).
outA(a,b):-inpA1770(a,b).
outA(a,b):-inpA1771(a,b).
outA(a,b):-inpA1772(a,b).
outA(a,b):-inpA1773(a,b).
outA(a,b):-inpA1774(a,b).
outA(a,b):-inpA1775(a,b).
outA(a,b):-inpA1776(a,b).
outA(a,b):-inpA1777(a,b).
outA(a,b):-inpA1778(a,b).
outA(a,b):-inpA1779(a,b).
outA(a,b):-inpA1780(a,b).
outA(a,b):-inpA1781(a,b).
outA(a,b):-inpA1782(a,b).
outA(a,b):-inpA1783(a,b).
outA(a,b):-inpA1784(a,b).
outA(a,b):-inpA1785(a,b).
outA(a,b):-inpA1786(a,b).
outA(a,b):-inpA1787(a,b).
outA(a,b):-inpA1788(a,b).
outA(a,b):-inpA1789(a,b).
outA(a,b):-inpA1790(a,b).
outA(a,b):-inpA1791(a,b).
outA(a,b):-inpA1792(a,b).
outA(a,b):-inpA1793(a,b).
outA(a,b):-inpA1794(a,b).
outA(a,b):-inpA1795(a,b).
outA(a,b):-inpA1796(a,b).
outA(a,b):-inpA1797(a,b).
outA(a,b):-inpA1798(a,b).
outA(a,b):-inpA1799(a,b).
outA(a,b):-inpA1800(a,b).
outA(a,b):-inpA1801(a,b).
outA(a,b):-inpA1802(a,b).
outA(a,b):-inpA1803(a,b).
outA(a,b):-inpA1804(a,b).
outA(a,b):-inpA1805(a,b).
outA(a,b):-inpA1806(a,b).
outA(a,b):-inpA1807(a,b).
outA(a,b):-inpA1808(a,b).
outA(a,b):-inpA1809(a,b).
outA(a,b):-inpA1810(a,b).
outA(a,b):-inpA1811(a,b).
outA(a,b):-inpA1812(a,b).
outA(a,b):-inpA1813(a,b).
outA(a,b):-inpA1814(a,b).
outA(a,b):-inpA1815(a,b).
outA(a,b):-inpA1816(a,b).
outA(a,b):-inpA1817(a,b).
outA(a,b):-inpA1818(a,b).
outA(a,b):-inpA1819(a,b).
outA(a,b):-inpA1820(a,b).
outA(a,b):-inpA1821(a,b).
outA(a,b):-inpA1822(a,b).
outA(a,b):-inpA1823(a,b).
outA(a,b):-inpA1824(a,b).
outA(a,b):-inpA1825(a,b).
outA(a,b):-inpA1826(a,b).
outA(a,b):-inpA1827(a,b).
outA(a,b):-inpA1828(a,b).
outA(a,b):-inpA1829(a,b).
outA(a,b):-inpA1830(a,b).
outA(a,b):-inpA1831(a,b).
outA(a,b):-inpA1832(a,b).
outA(a,b):-inpA1833(a,b).
outA(a,b):-inpA1834(a,b).
outA(a,b):-inpA1835(a,b).
outA(a,b):-inpA1836(a,b).
outA(a,b):-inpA1837(a,b).
outA(a,b):-inpA1838(a,b).
outA(a,b):-inpA1839(a,b).
outA(a,b):-inpA1840(a,b).
outA(a,b):-inpA1841(a,b).
outA(a,b):-inpA1842(a,b).
outA(a,b):-inpA1843(a,b).
outA(a,b):-inpA1844(a,b).
outA(a,b):-inpA1845(a,b).
outA(a,b):-inpA1846(a,b).
outA(a,b):-inpA1847(a,b).
outA(a,b):-inpA1848(a,b).
outA(a,b):-inpA1849(a,b).
outA(a,b):-inpA1850(a,b).
outA(a,b):-inpA1851(a,b).
outA(a,b):-inpA1852(a,b).
outA(a,b):-inpA1853(a,b).
outA(a,b):-inpA1854(a,b).
outA(a,b):-inpA1855(a,b).
outA(a,b):-inpA1856(a,b).
outA(a,b):-inpA1857(a,b).
outA(a,b):-inpA1858(a,b).
outA(a,b):-inpA1859(a,b).
outA(a,b):-inpA1860(a,b).
outA(a,b):-inpA1861(a,b).
outA(a,b):-inpA1862(a,b).
outA(a,b):-inpA1863(a,b).
outA(a,b):-inpA1864(a,b).
outA(a,b):-inpA1865(a,b).
outA(a,b):-inpA1866(a,b).
outA(a,b):-inpA1867(a,b).
outA(a,b):-inpA1868(a,b).
outA(a,b):-inpA1869(a,b).
outA(a,b):-inpA1870(a,b).
outA(a,b):-inpA1871(a,b).
outA(a,b):-inpA1872(a,b).
outA(a,b):-inpA1873(a,b).
outA(a,b):-inpA1874(a,b).
outA(a,b):-inpA1875(a,b).
outA(a,b):-inpA1876(a,b).
outA(a,b):-inpA1877(a,b).
outA(a,b):-inpA1878(a,b).
outA(a,b):-inpA1879(a,b).
outA(a,b):-inpA1880(a,b).
outA(a,b):-inpA1881(a,b).
outA(a,b):-inpA1882(a,b).
outA(a,b):-inpA1883(a,b).
outA(a,b):-inpA1884(a,b).
outA(a,b):-inpA1885(a,b).
outA(a,b):-inpA1886(a,b).
outA(a,b):-inpA1887(a,b).
outA(a,b):-inpA1888(a,b).
outA(a,b):-inpA1889(a,b).
outA(a,b):-inpA1890(a,b).
outA(a,b):-inpA1891(a,b).
outA(a,b):-inpA1892(a,b).
outA(a,b):-inpA1893(a,b).
outA(a,b):-inpA1894(a,b).
outA(a,b):-inpA1895(a,b).
outA(a,b):-inpA1896(a,b).
outA(a,b):-inpA1897(a,b).
outA(a,b):-inpA1898(a,b).
outA(a,b):-inpA1899(a,b).
outA(a,b):-inpA1900(a,b).
outA(a,b):-inpA1901(a,b).
outA(a,b):-inpA1902(a,b).
outA(a,b):-inpA1903(a,b).
outA(a,b):-inpA1904(a,b).
outA(a,b):-inpA1905(a,b).
outA(a,b):-inpA1906(a,b).
outA(a,b):-inpA1907(a,b).
outA(a,b):-inpA1908(a,b).
outA(a,b):-inpA1909(a,b).
outA(a,b):-inpA1910(a,b).
outA(a,b):-inpA1911(a,b).
outA(a,b):-inpA1912(a,b).
outA(a,b):-inpA1913(a,b).
outA(a,b):-inpA1914(a,b).
outA(a,b):-inpA1915(a,b).
outA(a,b):-inpA1916(a,b).
outA(a,b):-inpA1917(a,b).
outA(a,b):-inpA1918(a,b).
outA(a,b):-inpA1919(a,b).
outA(a,b):-inpA1920(a,b).
outA(a,b):-inpA1921(a,b).
outA(a,b):-inpA1922(a,b).
outA(a,b):-inpA1923(a,b).
outA(a,b):-inpA1924(a,b).
outA(a,b):-inpA1925(a,b).
outA(a,b):-inpA1926(a,b).
outA(a,b):-inpA1927(a,b).
outA(a,b):-inpA1928(a,b).
outA(a,b):-inpA1929(a,b).
outA(a,b):-inpA1930(a,b).
outA(a,b):-inpA1931(a,b).
outA(a,b):-inpA1932(a,b).
outA(a,b):-inpA1933(a,b).
outA(a,b):-inpA1934(a,b).
outA(a,b):-inpA1935(a,b).
outA(a,b):-inpA1936(a,b).
outA(a,b):-inpA1937(a,b).
outA(a,b):-inpA1938(a,b).
outA(a,b):-inpA1939(a,b).
outA(a,b):-inpA1940(a,b).
outA(a,b):-inpA1941(a,b).
outA(a,b):-inpA1942(a,b).
outA(a,b):-inpA1943(a,b).
outA(a,b):-inpA1944(a,b).
outA(a,b):-inpA1945(a,b).
outA(a,b):-inpA1946(a,b).
outA(a,b):-inpA1947(a,b).
outA(a,b):-inpA1948(a,b).
outA(a,b):-inpA1949(a,b).
outA(a,b):-inpA1950(a,b).
outA(a,b):-inpA1951(a,b).
outA(a,b):-inpA1952(a,b).
outA(a,b):-inpA1953(a,b).
outA(a,b):-inpA1954(a,b).
outA(a,b):-inpA1955(a,b).
outA(a,b):-inpA1956(a,b).
outA(a,b):-inpA1957(a,b).
outA(a,b):-inpA1958(a,b).
outA(a,b):-inpA1959(a,b).
outA(a,b):-inpA1960(a,b).
outA(a,b):-inpA1961(a,b).
outA(a,b):-inpA1962(a,b).
outA(a,b):-inpA1963(a,b).
outA(a,b):-inpA1964(a,b).
outA(a,b):-inpA1965(a,b).
outA(a,b):-inpA1966(a,b).
outA(a,b):-inpA1967(a,b).
outA(a,b):-inpA1968(a,b).
outA(a,b):-inpA1969(a,b).
outA(a,b):-inpA1970(a,b).
outA(a,b):-inpA1971(a,b).
outA(a,b):-inpA1972(a,b).
outA(a,b):-inpA1973(a,b).
outA(a,b):-inpA1974(a,b).
outA(a,b):-inpA1975(a,b).
outA(a,b):-inpA1976(a,b).
outA(a,b):-inpA1977(a,b).
outA(a,b):-inpA1978(a,b).
outA(a,b):-inpA1979(a,b).
outA(a,b):-inpA1980(a,b).
outA(a,b):-inpA1981(a,b).
outA(a,b):-inpA1982(a,b).
outA(a,b):-inpA1983(a,b).
outA(a,b):-inpA1984(a,b).
outA(a,b):-inpA1985(a,b).
outA(a,b):-inpA1986(a,b).
outA(a,b):-inpA1987(a,b).
outA(a,b):-inpA1988(a,b).
outA(a,b):-inpA1989(a,b).
outA(a,b):-inpA1990(a,b).
outA(a,b):-inpA1991(a,b).
outA(a,b):-inpA1992(a,b).
outA(a,b):-inpA1993(a,b).
outA(a,b):-inpA1994(a,b).
outA(a,b):-inpA1995(a,b).
outA(a,b):-inpA1996(a,b).
outA(a,b):-inpA1997(a,b).
outA(a,b):-inpA1998(a,b).
outA(a,b):-inpA1999(a,b).
