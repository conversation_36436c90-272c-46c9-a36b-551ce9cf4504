%table inpA1(a:int4,b:int4)
%table inpA2(a:int4,b:int4)
%table inpA3(a:int4,b:int4)
%table inpA4(a:int4,b:int4)
%table inpA5(a:int4,b:int4)
%table inpA6(a:int4,b:int4)
%table inpA7(a:int4,b:int4)
%table inpA8(a:int4,b:int4)
%table inpA9(a:int4,b:int4)
%table inpA10(a:int4,b:int4)
%table inpA11(a:int4,b:int4)
%table inpA12(a:int4,b:int4)
%table inpA13(a:int4,b:int4)
%table inpA14(a:int4,b:int4)
%table inpA15(a:int4,b:int4)
%table inpA16(a:int4,b:int4)
%table inpA17(a:int4,b:int4)
%table inpA18(a:int4,b:int4)
%table inpA19(a:int4,b:int4)
%table inpA20(a:int4,b:int4)
%table inpA21(a:int4,b:int4)
%table inpA22(a:int4,b:int4)
%table inpA23(a:int4,b:int4)
%table inpA24(a:int4,b:int4)
%table inpA25(a:int4,b:int4)
%table inpA26(a:int4,b:int4)
%table inpA27(a:int4,b:int4)
%table inpA28(a:int4,b:int4)
%table inpA29(a:int4,b:int4)
%table inpA30(a:int4,b:int4)
%table inpA31(a:int4,b:int4)
%table inpA32(a:int4,b:int4)
%table inpA33(a:int4,b:int4)
%table inpA34(a:int4,b:int4)
%table inpA35(a:int4,b:int4)
%table inpA36(a:int4,b:int4)
%table inpA37(a:int4,b:int4)
%table inpA38(a:int4,b:int4)
%table inpA39(a:int4,b:int4)
%table inpA40(a:int4,b:int4)
%table inpA41(a:int4,b:int4)
%table inpA42(a:int4,b:int4)
%table inpA43(a:int4,b:int4)
%table inpA44(a:int4,b:int4)
%table inpA45(a:int4,b:int4)
%table inpA46(a:int4,b:int4)
%table inpA47(a:int4,b:int4)
%table inpA48(a:int4,b:int4)
%table inpA49(a:int4,b:int4)
%table inpA50(a:int4,b:int4)
%table inpA51(a:int4,b:int4)
%table inpA52(a:int4,b:int4)
%table inpA53(a:int4,b:int4)
%table inpA54(a:int4,b:int4)
%table inpA55(a:int4,b:int4)
%table inpA56(a:int4,b:int4)
%table inpA57(a:int4,b:int4)
%table inpA58(a:int4,b:int4)
%table inpA59(a:int4,b:int4)
%table inpA60(a:int4,b:int4)
%table inpA61(a:int4,b:int4)
%table inpA62(a:int4,b:int4)
%table inpA63(a:int4,b:int4)
%table inpA64(a:int4,b:int4)
%table inpA65(a:int4,b:int4)
%table inpA66(a:int4,b:int4)
%table inpA67(a:int4,b:int4)
%table inpA68(a:int4,b:int4)
%table inpA69(a:int4,b:int4)
%table inpA70(a:int4,b:int4)
%table inpA71(a:int4,b:int4)
%table inpA72(a:int4,b:int4)
%table inpA73(a:int4,b:int4)
%table inpA74(a:int4,b:int4)
%table inpA75(a:int4,b:int4)
%table inpA76(a:int4,b:int4)
%table inpA77(a:int4,b:int4)
%table inpA78(a:int4,b:int4)
%table inpA79(a:int4,b:int4)
%table inpA80(a:int4,b:int4)
%table inpA81(a:int4,b:int4)
%table inpA82(a:int4,b:int4)
%table inpA83(a:int4,b:int4)
%table inpA84(a:int4,b:int4)
%table inpA85(a:int4,b:int4)
%table inpA86(a:int4,b:int4)
%table inpA87(a:int4,b:int4)
%table inpA88(a:int4,b:int4)
%table inpA89(a:int4,b:int4)
%table inpA90(a:int4,b:int4)
%table inpA91(a:int4,b:int4)
%table inpA92(a:int4,b:int4)
%table inpA93(a:int4,b:int4)
%table inpA94(a:int4,b:int4)
%table inpA95(a:int4,b:int4)
%table inpA96(a:int4,b:int4)
%table inpA97(a:int4,b:int4)
%table inpA98(a:int4,b:int4)
%table inpA99(a:int4,b:int4)
%table inpA100(a:int4,b:int4)
%table inpA101(a:int4,b:int4)
%table inpA102(a:int4,b:int4)
%table inpA103(a:int4,b:int4)
%table inpA104(a:int4,b:int4)
%table inpA105(a:int4,b:int4)
%table inpA106(a:int4,b:int4)
%table inpA107(a:int4,b:int4)
%table inpA108(a:int4,b:int4)
%table inpA109(a:int4,b:int4)
%table inpA110(a:int4,b:int4)
%table inpA111(a:int4,b:int4)
%table inpA112(a:int4,b:int4)
%table inpA113(a:int4,b:int4)
%table inpA114(a:int4,b:int4)
%table inpA115(a:int4,b:int4)
%table inpA116(a:int4,b:int4)
%table inpA117(a:int4,b:int4)
%table inpA118(a:int4,b:int4)
%table inpA119(a:int4,b:int4)
%table inpA120(a:int4,b:int4)
%table inpA121(a:int4,b:int4)
%table inpA122(a:int4,b:int4)
%table inpA123(a:int4,b:int4)
%table inpA124(a:int4,b:int4)
%table inpA125(a:int4,b:int4)
%table inpA126(a:int4,b:int4)
%table inpA127(a:int4,b:int4)
%table inpA128(a:int4,b:int4)
%table inpA129(a:int4,b:int4)
%table inpA130(a:int4,b:int4)
%table inpA131(a:int4,b:int4)
%table inpA132(a:int4,b:int4)
%table inpA133(a:int4,b:int4)
%table inpA134(a:int4,b:int4)
%table inpA135(a:int4,b:int4)
%table inpA136(a:int4,b:int4)
%table inpA137(a:int4,b:int4)
%table inpA138(a:int4,b:int4)
%table inpA139(a:int4,b:int4)
%table inpA140(a:int4,b:int4)
%table inpA141(a:int4,b:int4)
%table inpA142(a:int4,b:int4)
%table inpA143(a:int4,b:int4)
%table inpA144(a:int4,b:int4)
%table inpA145(a:int4,b:int4)
%table inpA146(a:int4,b:int4)
%table inpA147(a:int4,b:int4)
%table inpA148(a:int4,b:int4)
%table inpA149(a:int4,b:int4)
%table inpA150(a:int4,b:int4)
%table inpA151(a:int4,b:int4)
%table inpA152(a:int4,b:int4)
%table inpA153(a:int4,b:int4)
%table inpA154(a:int4,b:int4)
%table inpA155(a:int4,b:int4)
%table inpA156(a:int4,b:int4)
%table inpA157(a:int4,b:int4)
%table inpA158(a:int4,b:int4)
%table inpA159(a:int4,b:int4)
%table inpA160(a:int4,b:int4)
%table inpA161(a:int4,b:int4)
%table inpA162(a:int4,b:int4)
%table inpA163(a:int4,b:int4)
%table inpA164(a:int4,b:int4)
%table inpA165(a:int4,b:int4)
%table inpA166(a:int4,b:int4)
%table inpA167(a:int4,b:int4)
%table inpA168(a:int4,b:int4)
%table inpA169(a:int4,b:int4)
%table inpA170(a:int4,b:int4)
%table inpA171(a:int4,b:int4)
%table inpA172(a:int4,b:int4)
%table inpA173(a:int4,b:int4)
%table inpA174(a:int4,b:int4)
%table inpA175(a:int4,b:int4)
%table inpA176(a:int4,b:int4)
%table inpA177(a:int4,b:int4)
%table inpA178(a:int4,b:int4)
%table inpA179(a:int4,b:int4)
%table inpA180(a:int4,b:int4)
%table inpA181(a:int4,b:int4)
%table inpA182(a:int4,b:int4)
%table inpA183(a:int4,b:int4)
%table inpA184(a:int4,b:int4)
%table inpA185(a:int4,b:int4)
%table inpA186(a:int4,b:int4)
%table inpA187(a:int4,b:int4)
%table inpA188(a:int4,b:int4)
%table inpA189(a:int4,b:int4)
%table inpA190(a:int4,b:int4)
%table inpA191(a:int4,b:int4)
%table inpA192(a:int4,b:int4)
%table inpA193(a:int4,b:int4)
%table inpA194(a:int4,b:int4)
%table inpA195(a:int4,b:int4)
%table inpA196(a:int4,b:int4)
%table inpA197(a:int4,b:int4)
%table inpA198(a:int4,b:int4)
%table inpA199(a:int4,b:int4)
%table inpA200(a:int4,b:int4)
%table inpA201(a:int4,b:int4)
%table inpA202(a:int4,b:int4)
%table inpA203(a:int4,b:int4)
%table inpA204(a:int4,b:int4)
%table inpA205(a:int4,b:int4)
%table inpA206(a:int4,b:int4)
%table inpA207(a:int4,b:int4)
%table inpA208(a:int4,b:int4)
%table inpA209(a:int4,b:int4)
%table inpA210(a:int4,b:int4)
%table inpA211(a:int4,b:int4)
%table inpA212(a:int4,b:int4)
%table inpA213(a:int4,b:int4)
%table inpA214(a:int4,b:int4)
%table inpA215(a:int4,b:int4)
%table inpA216(a:int4,b:int4)
%table inpA217(a:int4,b:int4)
%table inpA218(a:int4,b:int4)
%table inpA219(a:int4,b:int4)
%table inpA220(a:int4,b:int4)
%table inpA221(a:int4,b:int4)
%table inpA222(a:int4,b:int4)
%table inpA223(a:int4,b:int4)
%table inpA224(a:int4,b:int4)
%table inpA225(a:int4,b:int4)
%table inpA226(a:int4,b:int4)
%table inpA227(a:int4,b:int4)
%table inpA228(a:int4,b:int4)
%table inpA229(a:int4,b:int4)
%table inpA230(a:int4,b:int4)
%table inpA231(a:int4,b:int4)
%table inpA232(a:int4,b:int4)
%table inpA233(a:int4,b:int4)
%table inpA234(a:int4,b:int4)
%table inpA235(a:int4,b:int4)
%table inpA236(a:int4,b:int4)
%table inpA237(a:int4,b:int4)
%table inpA238(a:int4,b:int4)
%table inpA239(a:int4,b:int4)
%table inpA240(a:int4,b:int4)
%table inpA241(a:int4,b:int4)
%table inpA242(a:int4,b:int4)
%table inpA243(a:int4,b:int4)
%table inpA244(a:int4,b:int4)
%table inpA245(a:int4,b:int4)
%table inpA246(a:int4,b:int4)
%table inpA247(a:int4,b:int4)
%table inpA248(a:int4,b:int4)
%table inpA249(a:int4,b:int4)
%table inpA250(a:int4,b:int4)
%table inpA251(a:int4,b:int4)
%table inpA252(a:int4,b:int4)
%table inpA253(a:int4,b:int4)
%table inpA254(a:int4,b:int4)
%table inpA255(a:int4,b:int4)
%table inpA256(a:int4,b:int4)
%table inpA257(a:int4,b:int4)
%table inpA258(a:int4,b:int4)
%table inpA259(a:int4,b:int4)
%table inpA260(a:int4,b:int4)
%table inpA261(a:int4,b:int4)
%table inpA262(a:int4,b:int4)
%table inpA263(a:int4,b:int4)
%table inpA264(a:int4,b:int4)
%table inpA265(a:int4,b:int4)
%table inpA266(a:int4,b:int4)
%table inpA267(a:int4,b:int4)
%table inpA268(a:int4,b:int4)
%table inpA269(a:int4,b:int4)
%table inpA270(a:int4,b:int4)
%table inpA271(a:int4,b:int4)
%table inpA272(a:int4,b:int4)
%table inpA273(a:int4,b:int4)
%table inpA274(a:int4,b:int4)
%table inpA275(a:int4,b:int4)
%table inpA276(a:int4,b:int4)
%table inpA277(a:int4,b:int4)
%table inpA278(a:int4,b:int4)
%table inpA279(a:int4,b:int4)
%table inpA280(a:int4,b:int4)
%table inpA281(a:int4,b:int4)
%table inpA282(a:int4,b:int4)
%table inpA283(a:int4,b:int4)
%table inpA284(a:int4,b:int4)
%table inpA285(a:int4,b:int4)
%table inpA286(a:int4,b:int4)
%table inpA287(a:int4,b:int4)
%table inpA288(a:int4,b:int4)
%table inpA289(a:int4,b:int4)
%table inpA290(a:int4,b:int4)
%table inpA291(a:int4,b:int4)
%table inpA292(a:int4,b:int4)
%table inpA293(a:int4,b:int4)
%table inpA294(a:int4,b:int4)
%table inpA295(a:int4,b:int4)
%table inpA296(a:int4,b:int4)
%table inpA297(a:int4,b:int4)
%table inpA298(a:int4,b:int4)
%table inpA299(a:int4,b:int4)
%table inpA300(a:int4,b:int4)
%table inpA301(a:int4,b:int4)
%table inpA302(a:int4,b:int4)
%table inpA303(a:int4,b:int4)
%table inpA304(a:int4,b:int4)
%table inpA305(a:int4,b:int4)
%table inpA306(a:int4,b:int4)
%table inpA307(a:int4,b:int4)
%table inpA308(a:int4,b:int4)
%table inpA309(a:int4,b:int4)
%table inpA310(a:int4,b:int4)
%table inpA311(a:int4,b:int4)
%table inpA312(a:int4,b:int4)
%table inpA313(a:int4,b:int4)
%table inpA314(a:int4,b:int4)
%table inpA315(a:int4,b:int4)
%table inpA316(a:int4,b:int4)
%table inpA317(a:int4,b:int4)
%table inpA318(a:int4,b:int4)
%table inpA319(a:int4,b:int4)
%table inpA320(a:int4,b:int4)
%table inpA321(a:int4,b:int4)
%table inpA322(a:int4,b:int4)
%table inpA323(a:int4,b:int4)
%table inpA324(a:int4,b:int4)
%table inpA325(a:int4,b:int4)
%table inpA326(a:int4,b:int4)
%table inpA327(a:int4,b:int4)
%table inpA328(a:int4,b:int4)
%table inpA329(a:int4,b:int4)
%table inpA330(a:int4,b:int4)
%table inpA331(a:int4,b:int4)
%table inpA332(a:int4,b:int4)
%table inpA333(a:int4,b:int4)
%table inpA334(a:int4,b:int4)
%table inpA335(a:int4,b:int4)
%table inpA336(a:int4,b:int4)
%table inpA337(a:int4,b:int4)
%table inpA338(a:int4,b:int4)
%table inpA339(a:int4,b:int4)
%table inpA340(a:int4,b:int4)
%table inpA341(a:int4,b:int4)
%table inpA342(a:int4,b:int4)
%table inpA343(a:int4,b:int4)
%table inpA344(a:int4,b:int4)
%table inpA345(a:int4,b:int4)
%table inpA346(a:int4,b:int4)
%table inpA347(a:int4,b:int4)
%table inpA348(a:int4,b:int4)
%table inpA349(a:int4,b:int4)
%table inpA350(a:int4,b:int4)
%table inpA351(a:int4,b:int4)
%table inpA352(a:int4,b:int4)
%table inpA353(a:int4,b:int4)
%table inpA354(a:int4,b:int4)
%table inpA355(a:int4,b:int4)
%table inpA356(a:int4,b:int4)
%table inpA357(a:int4,b:int4)
%table inpA358(a:int4,b:int4)
%table inpA359(a:int4,b:int4)
%table inpA360(a:int4,b:int4)
%table inpA361(a:int4,b:int4)
%table inpA362(a:int4,b:int4)
%table inpA363(a:int4,b:int4)
%table inpA364(a:int4,b:int4)
%table inpA365(a:int4,b:int4)
%table inpA366(a:int4,b:int4)
%table inpA367(a:int4,b:int4)
%table inpA368(a:int4,b:int4)
%table inpA369(a:int4,b:int4)
%table inpA370(a:int4,b:int4)
%table inpA371(a:int4,b:int4)
%table inpA372(a:int4,b:int4)
%table inpA373(a:int4,b:int4)
%table inpA374(a:int4,b:int4)
%table inpA375(a:int4,b:int4)
%table inpA376(a:int4,b:int4)
%table inpA377(a:int4,b:int4)
%table inpA378(a:int4,b:int4)
%table inpA379(a:int4,b:int4)
%table inpA380(a:int4,b:int4)
%table inpA381(a:int4,b:int4)
%table inpA382(a:int4,b:int4)
%table inpA383(a:int4,b:int4)
%table inpA384(a:int4,b:int4)
%table inpA385(a:int4,b:int4)
%table inpA386(a:int4,b:int4)
%table inpA387(a:int4,b:int4)
%table inpA388(a:int4,b:int4)
%table inpA389(a:int4,b:int4)
%table inpA390(a:int4,b:int4)
%table inpA391(a:int4,b:int4)
%table inpA392(a:int4,b:int4)
%table inpA393(a:int4,b:int4)
%table inpA394(a:int4,b:int4)
%table inpA395(a:int4,b:int4)
%table inpA396(a:int4,b:int4)
%table inpA397(a:int4,b:int4)
%table inpA398(a:int4,b:int4)
%table inpA399(a:int4,b:int4)
%table inpA400(a:int4,b:int4)
%table inpA401(a:int4,b:int4)
%table inpA402(a:int4,b:int4)
%table inpA403(a:int4,b:int4)
%table inpA404(a:int4,b:int4)
%table inpA405(a:int4,b:int4)
%table inpA406(a:int4,b:int4)
%table inpA407(a:int4,b:int4)
%table inpA408(a:int4,b:int4)
%table inpA409(a:int4,b:int4)
%table inpA410(a:int4,b:int4)
%table inpA411(a:int4,b:int4)
%table inpA412(a:int4,b:int4)
%table inpA413(a:int4,b:int4)
%table inpA414(a:int4,b:int4)
%table inpA415(a:int4,b:int4)
%table inpA416(a:int4,b:int4)
%table inpA417(a:int4,b:int4)
%table inpA418(a:int4,b:int4)
%table inpA419(a:int4,b:int4)
%table inpA420(a:int4,b:int4)
%table inpA421(a:int4,b:int4)
%table inpA422(a:int4,b:int4)
%table inpA423(a:int4,b:int4)
%table inpA424(a:int4,b:int4)
%table inpA425(a:int4,b:int4)
%table inpA426(a:int4,b:int4)
%table inpA427(a:int4,b:int4)
%table inpA428(a:int4,b:int4)
%table inpA429(a:int4,b:int4)
%table inpA430(a:int4,b:int4)
%table inpA431(a:int4,b:int4)
%table inpA432(a:int4,b:int4)
%table inpA433(a:int4,b:int4)
%table inpA434(a:int4,b:int4)
%table inpA435(a:int4,b:int4)
%table inpA436(a:int4,b:int4)
%table inpA437(a:int4,b:int4)
%table inpA438(a:int4,b:int4)
%table inpA439(a:int4,b:int4)
%table inpA440(a:int4,b:int4)
%table inpA441(a:int4,b:int4)
%table inpA442(a:int4,b:int4)
%table inpA443(a:int4,b:int4)
%table inpA444(a:int4,b:int4)
%table inpA445(a:int4,b:int4)
%table inpA446(a:int4,b:int4)
%table inpA447(a:int4,b:int4)
%table inpA448(a:int4,b:int4)
%table inpA449(a:int4,b:int4)
%table inpA450(a:int4,b:int4)
%table inpA451(a:int4,b:int4)
%table inpA452(a:int4,b:int4)
%table inpA453(a:int4,b:int4)
%table inpA454(a:int4,b:int4)
%table inpA455(a:int4,b:int4)
%table inpA456(a:int4,b:int4)
%table inpA457(a:int4,b:int4)
%table inpA458(a:int4,b:int4)
%table inpA459(a:int4,b:int4)
%table inpA460(a:int4,b:int4)
%table inpA461(a:int4,b:int4)
%table inpA462(a:int4,b:int4)
%table inpA463(a:int4,b:int4)
%table inpA464(a:int4,b:int4)
%table inpA465(a:int4,b:int4)
%table inpA466(a:int4,b:int4)
%table inpA467(a:int4,b:int4)
%table inpA468(a:int4,b:int4)
%table inpA469(a:int4,b:int4)
%table inpA470(a:int4,b:int4)
%table inpA471(a:int4,b:int4)
%table inpA472(a:int4,b:int4)
%table inpA473(a:int4,b:int4)
%table inpA474(a:int4,b:int4)
%table inpA475(a:int4,b:int4)
%table inpA476(a:int4,b:int4)
%table inpA477(a:int4,b:int4)
%table inpA478(a:int4,b:int4)
%table inpA479(a:int4,b:int4)
%table inpA480(a:int4,b:int4)
%table inpA481(a:int4,b:int4)
%table inpA482(a:int4,b:int4)
%table inpA483(a:int4,b:int4)
%table inpA484(a:int4,b:int4)
%table inpA485(a:int4,b:int4)
%table inpA486(a:int4,b:int4)
%table inpA487(a:int4,b:int4)
%table inpA488(a:int4,b:int4)
%table inpA489(a:int4,b:int4)
%table inpA490(a:int4,b:int4)
%table inpA491(a:int4,b:int4)
%table inpA492(a:int4,b:int4)
%table inpA493(a:int4,b:int4)
%table inpA494(a:int4,b:int4)
%table inpA495(a:int4,b:int4)
%table inpA496(a:int4,b:int4)
%table inpA497(a:int4,b:int4)
%table inpA498(a:int4,b:int4)
%table inpA499(a:int4,b:int4)
%table inpA500(a:int4,b:int4)
%table inpA501(a:int4,b:int4)
%table inpA502(a:int4,b:int4)
%table inpA503(a:int4,b:int4)
%table inpA504(a:int4,b:int4)
%table inpA505(a:int4,b:int4)
%table inpA506(a:int4,b:int4)
%table inpA507(a:int4,b:int4)
%table inpA508(a:int4,b:int4)
%table inpA509(a:int4,b:int4)
%table inpA510(a:int4,b:int4)
%table inpA511(a:int4,b:int4)
%table inpA512(a:int4,b:int4)
%table inpA513(a:int4,b:int4)
%table inpA514(a:int4,b:int4)
%table inpA515(a:int4,b:int4)
%table inpA516(a:int4,b:int4)
%table inpA517(a:int4,b:int4)
%table inpA518(a:int4,b:int4)
%table inpA519(a:int4,b:int4)
%table inpA520(a:int4,b:int4)
%table inpA521(a:int4,b:int4)
%table inpA522(a:int4,b:int4)
%table inpA523(a:int4,b:int4)
%table inpA524(a:int4,b:int4)
%table inpA525(a:int4,b:int4)
%table inpA526(a:int4,b:int4)
%table inpA527(a:int4,b:int4)
%table inpA528(a:int4,b:int4)
%table inpA529(a:int4,b:int4)
%table inpA530(a:int4,b:int4)
%table inpA531(a:int4,b:int4)
%table inpA532(a:int4,b:int4)
%table inpA533(a:int4,b:int4)
%table inpA534(a:int4,b:int4)
%table inpA535(a:int4,b:int4)
%table inpA536(a:int4,b:int4)
%table inpA537(a:int4,b:int4)
%table inpA538(a:int4,b:int4)
%table inpA539(a:int4,b:int4)
%table inpA540(a:int4,b:int4)
%table inpA541(a:int4,b:int4)
%table inpA542(a:int4,b:int4)
%table inpA543(a:int4,b:int4)
%table inpA544(a:int4,b:int4)
%table inpA545(a:int4,b:int4)
%table inpA546(a:int4,b:int4)
%table inpA547(a:int4,b:int4)
%table inpA548(a:int4,b:int4)
%table inpA549(a:int4,b:int4)
%table inpA550(a:int4,b:int4)
%table inpA551(a:int4,b:int4)
%table inpA552(a:int4,b:int4)
%table inpA553(a:int4,b:int4)
%table inpA554(a:int4,b:int4)
%table inpA555(a:int4,b:int4)
%table inpA556(a:int4,b:int4)
%table inpA557(a:int4,b:int4)
%table inpA558(a:int4,b:int4)
%table inpA559(a:int4,b:int4)
%table inpA560(a:int4,b:int4)
%table inpA561(a:int4,b:int4)
%table inpA562(a:int4,b:int4)
%table inpA563(a:int4,b:int4)
%table inpA564(a:int4,b:int4)
%table inpA565(a:int4,b:int4)
%table inpA566(a:int4,b:int4)
%table inpA567(a:int4,b:int4)
%table inpA568(a:int4,b:int4)
%table inpA569(a:int4,b:int4)
%table inpA570(a:int4,b:int4)
%table inpA571(a:int4,b:int4)
%table inpA572(a:int4,b:int4)
%table inpA573(a:int4,b:int4)
%table inpA574(a:int4,b:int4)
%table inpA575(a:int4,b:int4)
%table inpA576(a:int4,b:int4)
%table inpA577(a:int4,b:int4)
%table inpA578(a:int4,b:int4)
%table inpA579(a:int4,b:int4)
%table inpA580(a:int4,b:int4)
%table inpA581(a:int4,b:int4)
%table inpA582(a:int4,b:int4)
%table inpA583(a:int4,b:int4)
%table inpA584(a:int4,b:int4)
%table inpA585(a:int4,b:int4)
%table inpA586(a:int4,b:int4)
%table inpA587(a:int4,b:int4)
%table inpA588(a:int4,b:int4)
%table inpA589(a:int4,b:int4)
%table inpA590(a:int4,b:int4)
%table inpA591(a:int4,b:int4)
%table inpA592(a:int4,b:int4)
%table inpA593(a:int4,b:int4)
%table inpA594(a:int4,b:int4)
%table inpA595(a:int4,b:int4)
%table inpA596(a:int4,b:int4)
%table inpA597(a:int4,b:int4)
%table inpA598(a:int4,b:int4)
%table inpA599(a:int4,b:int4)
%table inpA600(a:int4,b:int4)
%table inpA601(a:int4,b:int4)
%table inpA602(a:int4,b:int4)
%table inpA603(a:int4,b:int4)
%table inpA604(a:int4,b:int4)
%table inpA605(a:int4,b:int4)
%table inpA606(a:int4,b:int4)
%table inpA607(a:int4,b:int4)
%table inpA608(a:int4,b:int4)
%table inpA609(a:int4,b:int4)
%table inpA610(a:int4,b:int4)
%table inpA611(a:int4,b:int4)
%table inpA612(a:int4,b:int4)
%table inpA613(a:int4,b:int4)
%table inpA614(a:int4,b:int4)
%table inpA615(a:int4,b:int4)
%table inpA616(a:int4,b:int4)
%table inpA617(a:int4,b:int4)
%table inpA618(a:int4,b:int4)
%table inpA619(a:int4,b:int4)
%table inpA620(a:int4,b:int4)
%table inpA621(a:int4,b:int4)
%table inpA622(a:int4,b:int4)
%table inpA623(a:int4,b:int4)
%table inpA624(a:int4,b:int4)
%table inpA625(a:int4,b:int4)
%table inpA626(a:int4,b:int4)
%table inpA627(a:int4,b:int4)
%table inpA628(a:int4,b:int4)
%table inpA629(a:int4,b:int4)
%table inpA630(a:int4,b:int4)
%table inpA631(a:int4,b:int4)
%table inpA632(a:int4,b:int4)
%table inpA633(a:int4,b:int4)
%table inpA634(a:int4,b:int4)
%table inpA635(a:int4,b:int4)
%table inpA636(a:int4,b:int4)
%table inpA637(a:int4,b:int4)
%table inpA638(a:int4,b:int4)
%table inpA639(a:int4,b:int4)
%table inpA640(a:int4,b:int4)
%table inpA641(a:int4,b:int4)
%table inpA642(a:int4,b:int4)
%table inpA643(a:int4,b:int4)
%table inpA644(a:int4,b:int4)
%table inpA645(a:int4,b:int4)
%table inpA646(a:int4,b:int4)
%table inpA647(a:int4,b:int4)
%table inpA648(a:int4,b:int4)
%table inpA649(a:int4,b:int4)
%table inpA650(a:int4,b:int4)
%table inpA651(a:int4,b:int4)
%table inpA652(a:int4,b:int4)
%table inpA653(a:int4,b:int4)
%table inpA654(a:int4,b:int4)
%table inpA655(a:int4,b:int4)
%table inpA656(a:int4,b:int4)
%table inpA657(a:int4,b:int4)
%table inpA658(a:int4,b:int4)
%table inpA659(a:int4,b:int4)
%table inpA660(a:int4,b:int4)
%table inpA661(a:int4,b:int4)
%table inpA662(a:int4,b:int4)
%table inpA663(a:int4,b:int4)
%table inpA664(a:int4,b:int4)
%table inpA665(a:int4,b:int4)
%table inpA666(a:int4,b:int4)
%table inpA667(a:int4,b:int4)
%table inpA668(a:int4,b:int4)
%table inpA669(a:int4,b:int4)
%table inpA670(a:int4,b:int4)
%table inpA671(a:int4,b:int4)
%table inpA672(a:int4,b:int4)
%table inpA673(a:int4,b:int4)
%table inpA674(a:int4,b:int4)
%table inpA675(a:int4,b:int4)
%table inpA676(a:int4,b:int4)
%table inpA677(a:int4,b:int4)
%table inpA678(a:int4,b:int4)
%table inpA679(a:int4,b:int4)
%table inpA680(a:int4,b:int4)
%table inpA681(a:int4,b:int4)
%table inpA682(a:int4,b:int4)
%table inpA683(a:int4,b:int4)
%table inpA684(a:int4,b:int4)
%table inpA685(a:int4,b:int4)
%table inpA686(a:int4,b:int4)
%table inpA687(a:int4,b:int4)
%table inpA688(a:int4,b:int4)
%table inpA689(a:int4,b:int4)
%table inpA690(a:int4,b:int4)
%table inpA691(a:int4,b:int4)
%table inpA692(a:int4,b:int4)
%table inpA693(a:int4,b:int4)
%table inpA694(a:int4,b:int4)
%table inpA695(a:int4,b:int4)
%table inpA696(a:int4,b:int4)
%table inpA697(a:int4,b:int4)
%table inpA698(a:int4,b:int4)
%table inpA699(a:int4,b:int4)
%table inpA700(a:int4,b:int4)
%table inpA701(a:int4,b:int4)
%table inpA702(a:int4,b:int4)
%table inpA703(a:int4,b:int4)
%table inpA704(a:int4,b:int4)
%table inpA705(a:int4,b:int4)
%table inpA706(a:int4,b:int4)
%table inpA707(a:int4,b:int4)
%table inpA708(a:int4,b:int4)
%table inpA709(a:int4,b:int4)
%table inpA710(a:int4,b:int4)
%table inpA711(a:int4,b:int4)
%table inpA712(a:int4,b:int4)
%table inpA713(a:int4,b:int4)
%table inpA714(a:int4,b:int4)
%table inpA715(a:int4,b:int4)
%table inpA716(a:int4,b:int4)
%table inpA717(a:int4,b:int4)
%table inpA718(a:int4,b:int4)
%table inpA719(a:int4,b:int4)
%table inpA720(a:int4,b:int4)
%table inpA721(a:int4,b:int4)
%table inpA722(a:int4,b:int4)
%table inpA723(a:int4,b:int4)
%table inpA724(a:int4,b:int4)
%table inpA725(a:int4,b:int4)
%table inpA726(a:int4,b:int4)
%table inpA727(a:int4,b:int4)
%table inpA728(a:int4,b:int4)
%table inpA729(a:int4,b:int4)
%table inpA730(a:int4,b:int4)
%table inpA731(a:int4,b:int4)
%table inpA732(a:int4,b:int4)
%table inpA733(a:int4,b:int4)
%table inpA734(a:int4,b:int4)
%table inpA735(a:int4,b:int4)
%table inpA736(a:int4,b:int4)
%table inpA737(a:int4,b:int4)
%table inpA738(a:int4,b:int4)
%table inpA739(a:int4,b:int4)
%table inpA740(a:int4,b:int4)
%table inpA741(a:int4,b:int4)
%table inpA742(a:int4,b:int4)
%table inpA743(a:int4,b:int4)
%table inpA744(a:int4,b:int4)
%table inpA745(a:int4,b:int4)
%table inpA746(a:int4,b:int4)
%table inpA747(a:int4,b:int4)
%table inpA748(a:int4,b:int4)
%table inpA749(a:int4,b:int4)
%table inpA750(a:int4,b:int4)
%table inpA1001(a:int4,b:int4)

%table outA(a:int4,b:int4)
%function funcA(a:int4->b:int4){access_delta(inpA1,inpA2,inpA3,inpA4,inpA5,inpA6,inpA7,inpA8,inpA9,inpA10,inpA11,inpA12,inpA13,inpA14,inpA15,inpA16,inpA17,inpA18,inpA19,inpA20,inpA21,inpA22,inpA23,inpA24,inpA25,inpA26,inpA27,inpA28,inpA29,inpA30,inpA31,inpA32,inpA33,inpA34,inpA35,inpA36,inpA37,inpA38,inpA39,inpA40,inpA41,inpA42,inpA43,inpA44,inpA45,inpA46,inpA47,inpA48,inpA49,inpA50,inpA51,inpA52,inpA53,inpA54,inpA55,inpA56,inpA57,inpA58,inpA59,inpA60,inpA61,inpA62,inpA63,inpA64,inpA65,inpA66,inpA67,inpA68,inpA69,inpA70,inpA71,inpA72,inpA73,inpA74,inpA75,inpA76,inpA77,inpA78,inpA79,inpA80,inpA81,inpA82,inpA83,inpA84,inpA85,inpA86,inpA87,inpA88,inpA89,inpA90,inpA91,inpA92,inpA93,inpA94,inpA95,inpA96,inpA97,inpA98,inpA99,inpA100,inpA101,inpA102,inpA103,inpA104,inpA105,inpA106,inpA107,inpA108,inpA109,inpA110,inpA111,inpA112,inpA113,inpA114,inpA115,inpA116,inpA117,inpA118,inpA119,inpA120,inpA121,inpA122,inpA123,inpA124,inpA125,inpA126,inpA127,inpA128,inpA129,inpA130,inpA131,inpA132,inpA133,inpA134,inpA135,inpA136,inpA137,inpA138,inpA139,inpA140,inpA141,inpA142,inpA143,inpA144,inpA145,inpA146,inpA147,inpA148,inpA149,inpA150,inpA151,inpA152,inpA153,inpA154,inpA155,inpA156,inpA157,inpA158,inpA159,inpA160,inpA161,inpA162,inpA163,inpA164,inpA165,inpA166,inpA167,inpA168,inpA169,inpA170,inpA171,inpA172,inpA173,inpA174,inpA175,inpA176,inpA177,inpA178,inpA179,inpA180,inpA181,inpA182,inpA183,inpA184,inpA185,inpA186,inpA187,inpA188,inpA189,inpA190,inpA191,inpA192,inpA193,inpA194,inpA195,inpA196,inpA197,inpA198,inpA199,inpA200,inpA201,inpA202,inpA203,inpA204,inpA205,inpA206,inpA207,inpA208,inpA209,inpA210,inpA211,inpA212,inpA213,inpA214,inpA215,inpA216,inpA217,inpA218,inpA219,inpA220,inpA221,inpA222,inpA223,inpA224,inpA225,inpA226,inpA227,inpA228,inpA229,inpA230,inpA231,inpA232,inpA233,inpA234,inpA235,inpA236,inpA237,inpA238,inpA239,inpA240,inpA241,inpA242,inpA243,inpA244,inpA245,inpA246,inpA247,inpA248,inpA249,inpA250,inpA251,inpA252,inpA253,inpA254,inpA255,inpA256,inpA257,inpA258,inpA259,inpA260,inpA261,inpA262,inpA263,inpA264,inpA265,inpA266,inpA267,inpA268,inpA269,inpA270,inpA271,inpA272,inpA273,inpA274,inpA275,inpA276,inpA277,inpA278,inpA279,inpA280,inpA281,inpA282,inpA283,inpA284,inpA285,inpA286,inpA287,inpA288,inpA289,inpA290,inpA291,inpA292,inpA293,inpA294,inpA295,inpA296,inpA297,inpA298,inpA299,inpA300,inpA301,inpA302,inpA303,inpA304,inpA305,inpA306,inpA307,inpA308,inpA309,inpA310,inpA311,inpA312,inpA313,inpA314,inpA315,inpA316,inpA317,inpA318,inpA319,inpA320,inpA321,inpA322,inpA323,inpA324,inpA325,inpA326,inpA327,inpA328,inpA329,inpA330,inpA331,inpA332,inpA333,inpA334,inpA335,inpA336,inpA337,inpA338,inpA339,inpA340,inpA341,inpA342,inpA343,inpA344,inpA345,inpA346,inpA347,inpA348,inpA349,inpA350,inpA351,inpA352,inpA353,inpA354,inpA355,inpA356,inpA357,inpA358,inpA359,inpA360,inpA361,inpA362,inpA363,inpA364,inpA365,inpA366,inpA367,inpA368,inpA369,inpA370,inpA371,inpA372,inpA373,inpA374,inpA375,inpA376,inpA377,inpA378,inpA379,inpA380,inpA381,inpA382,inpA383,inpA384,inpA385,inpA386,inpA387,inpA388,inpA389,inpA390,inpA391,inpA392,inpA393,inpA394,inpA395,inpA396,inpA397,inpA398,inpA399,inpA400,inpA401,inpA402,inpA403,inpA404,inpA405,inpA406,inpA407,inpA408,inpA409,inpA410,inpA411,inpA412,inpA413,inpA414,inpA415,inpA416,inpA417,inpA418,inpA419,inpA420,inpA421,inpA422,inpA423,inpA424,inpA425,inpA426,inpA427,inpA428,inpA429,inpA430,inpA431,inpA432,inpA433,inpA434,inpA435,inpA436,inpA437,inpA438,inpA439,inpA440,inpA441,inpA442,inpA443,inpA444,inpA445,inpA446,inpA447,inpA448,inpA449,inpA450,inpA451,inpA452,inpA453,inpA454,inpA455,inpA456,inpA457,inpA458,inpA459,inpA460,inpA461,inpA462,inpA463,inpA464,inpA465,inpA466,inpA467,inpA468,inpA469,inpA470,inpA471,inpA472,inpA473,inpA474,inpA475,inpA476,inpA477,inpA478,inpA479,inpA480,inpA481,inpA482,inpA483,inpA484,inpA485,inpA486,inpA487,inpA488,inpA489,inpA490,inpA491,inpA492,inpA493,inpA494,inpA495,inpA496,inpA497,inpA498,inpA499,inpA500,inpA501,inpA502,inpA503,inpA504,inpA505,inpA506,inpA507,inpA508,inpA509,inpA510,inpA511,inpA512,inpA513,inpA514,inpA515,inpA516,inpA517,inpA518,inpA519,inpA520,inpA521,inpA522,inpA523,inpA524,inpA525,inpA526,inpA527,inpA528,inpA529,inpA530,inpA531,inpA532,inpA533,inpA534,inpA535,inpA536,inpA537,inpA538,inpA539,inpA540,inpA541,inpA542,inpA543,inpA544,inpA545,inpA546,inpA547,inpA548,inpA549,inpA550,inpA551,inpA552,inpA553,inpA554,inpA555,inpA556,inpA557,inpA558,inpA559,inpA560,inpA561,inpA562,inpA563,inpA564,inpA565,inpA566,inpA567,inpA568,inpA569,inpA570,inpA571,inpA572,inpA573,inpA574,inpA575,inpA576,inpA577,inpA578,inpA579,inpA580,inpA581,inpA582,inpA583,inpA584,inpA585,inpA586,inpA587,inpA588,inpA589,inpA590,inpA591,inpA592,inpA593,inpA594,inpA595,inpA596,inpA597,inpA598,inpA599,inpA600,inpA601,inpA602,inpA603,inpA604,inpA605,inpA606,inpA607,inpA608,inpA609,inpA610,inpA611,inpA612,inpA613,inpA614,inpA615,inpA616,inpA617,inpA618,inpA619,inpA620,inpA621,inpA622,inpA623,inpA624,inpA625,inpA626,inpA627,inpA628,inpA629,inpA630,inpA631,inpA632,inpA633,inpA634,inpA635,inpA636,inpA637,inpA638,inpA639,inpA640,inpA641,inpA642,inpA643,inpA644,inpA645,inpA646,inpA647,inpA648,inpA649,inpA650,inpA651,inpA652,inpA653,inpA654,inpA655,inpA656,inpA657,inpA658,inpA659,inpA660,inpA661,inpA662,inpA663,inpA664,inpA665,inpA666,inpA667,inpA668,inpA669,inpA670,inpA671,inpA672,inpA673,inpA674,inpA675,inpA676,inpA677,inpA678,inpA679,inpA680,inpA681,inpA682,inpA683,inpA684,inpA685,inpA686,inpA687,inpA688,inpA689,inpA690,inpA691,inpA692,inpA693,inpA694,inpA695,inpA696,inpA697,inpA698,inpA699,inpA700,inpA701,inpA702,inpA703,inpA704,inpA705,inpA706,inpA707,inpA708,inpA709,inpA710,inpA711,inpA712,inpA713,inpA714,inpA715,inpA716,inpA717,inpA718,inpA719,inpA720,inpA721,inpA722,inpA723,inpA724,inpA725,inpA726,inpA727,inpA728,inpA729,inpA730,inpA731,inpA732,inpA733,inpA734,inpA735,inpA736,inpA737,inpA738,inpA739,inpA740,inpA741,inpA742,inpA743,inpA744,inpA745,inpA746,inpA747,inpA748,inpA749,inpA750),
access_current(inpA1,inpA2,inpA3,inpA4,inpA5,inpA6,inpA7,inpA8,inpA9,inpA10,inpA11,inpA12,inpA13,inpA14,inpA15,inpA16,inpA17,inpA18,inpA19,inpA20,inpA21,inpA22,inpA23,inpA24,inpA25,inpA26,inpA27,inpA28,inpA29,inpA30,inpA31,inpA32,inpA33,inpA34,inpA35,inpA36,inpA37,inpA38,inpA39,inpA40,inpA41,inpA42,inpA43,inpA44,inpA45,inpA46,inpA47,inpA48,inpA49,inpA50,inpA51,inpA52,inpA53,inpA54,inpA55,inpA56,inpA57,inpA58,inpA59,inpA60,inpA61,inpA62,inpA63,inpA64,inpA65,inpA66,inpA67,inpA68,inpA69,inpA70,inpA71,inpA72,inpA73,inpA74,inpA75,inpA76,inpA77,inpA78,inpA79,inpA80,inpA81,inpA82,inpA83,inpA84,inpA85,inpA86,inpA87,inpA88,inpA89,inpA90,inpA91,inpA92,inpA93,inpA94,inpA95,inpA96,inpA97,inpA98,inpA99,inpA100,inpA101,inpA102,inpA103,inpA104,inpA105,inpA106,inpA107,inpA108,inpA109,inpA110,inpA111,inpA112,inpA113,inpA114,inpA115,inpA116,inpA117,inpA118,inpA119,inpA120,inpA121,inpA122,inpA123,inpA124,inpA125,inpA126,inpA127,inpA128,inpA129,inpA130,inpA131,inpA132,inpA133,inpA134,inpA135,inpA136,inpA137,inpA138,inpA139,inpA140,inpA141,inpA142,inpA143,inpA144,inpA145,inpA146,inpA147,inpA148,inpA149,inpA150,inpA151,inpA152,inpA153,inpA154,inpA155,inpA156,inpA157,inpA158,inpA159,inpA160,inpA161,inpA162,inpA163,inpA164,inpA165,inpA166,inpA167,inpA168,inpA169,inpA170,inpA171,inpA172,inpA173,inpA174,inpA175,inpA176,inpA177,inpA178,inpA179,inpA180,inpA181,inpA182,inpA183,inpA184,inpA185,inpA186,inpA187,inpA188,inpA189,inpA190,inpA191,inpA192,inpA193,inpA194,inpA195,inpA196,inpA197,inpA198,inpA199,inpA200,inpA201,inpA202,inpA203,inpA204,inpA205,inpA206,inpA207,inpA208,inpA209,inpA210,inpA211,inpA212,inpA213,inpA214,inpA215,inpA216,inpA217,inpA218,inpA219,inpA220,inpA221,inpA222,inpA223,inpA224,inpA225,inpA226,inpA227,inpA228,inpA229,inpA230,inpA231,inpA232,inpA233,inpA234,inpA235,inpA236,inpA237,inpA238,inpA239,inpA240,inpA241,inpA242,inpA243,inpA244,inpA245,inpA246,inpA247,inpA248,inpA249,inpA250,inpA251,inpA252,inpA253,inpA254,inpA255,inpA256,inpA257,inpA258,inpA259,inpA260,inpA261,inpA262,inpA263,inpA264,inpA265,inpA266,inpA267,inpA268,inpA269,inpA270,inpA271,inpA272,inpA273,inpA274,inpA275,inpA276,inpA277,inpA278,inpA279,inpA280,inpA281,inpA282,inpA283,inpA284,inpA285,inpA286,inpA287,inpA288,inpA289,inpA290,inpA291,inpA292,inpA293,inpA294,inpA295,inpA296,inpA297,inpA298,inpA299,inpA300,inpA301,inpA302,inpA303,inpA304,inpA305,inpA306,inpA307,inpA308,inpA309,inpA310,inpA311,inpA312,inpA313,inpA314,inpA315,inpA316,inpA317,inpA318,inpA319,inpA320,inpA321,inpA322,inpA323,inpA324,inpA325,inpA326,inpA327,inpA328,inpA329,inpA330,inpA331,inpA332,inpA333,inpA334,inpA335,inpA336,inpA337,inpA338,inpA339,inpA340,inpA341,inpA342,inpA343,inpA344,inpA345,inpA346,inpA347,inpA348,inpA349,inpA350,inpA351,inpA352,inpA353,inpA354,inpA355,inpA356,inpA357,inpA358,inpA359,inpA360,inpA361,inpA362,inpA363,inpA364,inpA365,inpA366,inpA367,inpA368,inpA369,inpA370,inpA371,inpA372,inpA373,inpA374,inpA375,inpA376,inpA377,inpA378,inpA379,inpA380,inpA381,inpA382,inpA383,inpA384,inpA385,inpA386,inpA387,inpA388,inpA389,inpA390,inpA391,inpA392,inpA393,inpA394,inpA395,inpA396,inpA397,inpA398,inpA399,inpA400,inpA401,inpA402,inpA403,inpA404,inpA405,inpA406,inpA407,inpA408,inpA409,inpA410,inpA411,inpA412,inpA413,inpA414,inpA415,inpA416,inpA417,inpA418,inpA419,inpA420,inpA421,inpA422,inpA423,inpA424,inpA425,inpA426,inpA427,inpA428,inpA429,inpA430,inpA431,inpA432,inpA433,inpA434,inpA435,inpA436,inpA437,inpA438,inpA439,inpA440,inpA441,inpA442,inpA443,inpA444,inpA445,inpA446,inpA447,inpA448,inpA449,inpA450,inpA451,inpA452,inpA453,inpA454,inpA455,inpA456,inpA457,inpA458,inpA459,inpA460,inpA461,inpA462,inpA463,inpA464,inpA465,inpA466,inpA467,inpA468,inpA469,inpA470,inpA471,inpA472,inpA473,inpA474,inpA475,inpA476,inpA477,inpA478,inpA479,inpA480,inpA481,inpA482,inpA483,inpA484,inpA485,inpA486,inpA487,inpA488,inpA489,inpA490,inpA491,inpA492,inpA493,inpA494,inpA495,inpA496,inpA497,inpA498,inpA499,inpA500,inpA501,inpA502,inpA503,inpA504,inpA505,inpA506,inpA507,inpA508,inpA509,inpA510,inpA511,inpA512,inpA513,inpA514,inpA515,inpA516,inpA517,inpA518,inpA519,inpA520,inpA521,inpA522,inpA523,inpA524,inpA525,inpA526,inpA527,inpA528,inpA529,inpA530,inpA531,inpA532,inpA533,inpA534,inpA535,inpA536,inpA537,inpA538,inpA539,inpA540,inpA541,inpA542,inpA543,inpA544,inpA545,inpA546,inpA547,inpA548,inpA549,inpA550,inpA551,inpA552,inpA553,inpA554,inpA555,inpA556,inpA557,inpA558,inpA559,inpA560,inpA561,inpA562,inpA563,inpA564,inpA565,inpA566,inpA567,inpA568,inpA569,inpA570,inpA571,inpA572,inpA573,inpA574,inpA575,inpA576,inpA577,inpA578,inpA579,inpA580,inpA581,inpA582,inpA583,inpA584,inpA585,inpA586,inpA587,inpA588,inpA589,inpA590,inpA591,inpA592,inpA593,inpA594,inpA595,inpA596,inpA597,inpA598,inpA599,inpA600,inpA601,inpA602,inpA603,inpA604,inpA605,inpA606,inpA607,inpA608,inpA609,inpA610,inpA611,inpA612,inpA613,inpA614,inpA615,inpA616,inpA617,inpA618,inpA619,inpA620,inpA621,inpA622,inpA623,inpA624,inpA625,inpA626,inpA627,inpA628,inpA629,inpA630,inpA631,inpA632,inpA633,inpA634,inpA635,inpA636,inpA637,inpA638,inpA639,inpA640,inpA641,inpA642,inpA643,inpA644,inpA645,inpA646,inpA647,inpA648,inpA649,inpA650,inpA651,inpA652,inpA653,inpA654,inpA655,inpA656,inpA657,inpA658,inpA659,inpA660,inpA661,inpA662,inpA663,inpA664,inpA665,inpA666,inpA667,inpA668,inpA669,inpA670,inpA671,inpA672,inpA673,inpA674,inpA675,inpA676,inpA677,inpA678,inpA679,inpA680,inpA681,inpA682,inpA683,inpA684,inpA685,inpA686,inpA687,inpA688,inpA689,inpA690,inpA691,inpA692,inpA693,inpA694,inpA695,inpA696,inpA697,inpA698,inpA699,inpA700,inpA701,inpA702,inpA703,inpA704,inpA705,inpA706,inpA707,inpA708,inpA709,inpA710,inpA711,inpA712,inpA713,inpA714,inpA715,inpA716,inpA717,inpA718,inpA719,inpA720,inpA721,inpA722,inpA723,inpA724,inpA725,inpA726,inpA727,inpA728,inpA729,inpA730,inpA731,inpA732,inpA733,inpA734,inpA735,inpA736,inpA737,inpA738,inpA739,inpA740,inpA741,inpA742,inpA743,inpA744,inpA745,inpA746,inpA747,inpA748,inpA749,inpA750)}

outA(a,b):-inpA1(a,b).
outA(a,b):-inpA2(a,b).
outA(a,b):-inpA3(a,b).
outA(a,b):-inpA4(a,b).
outA(a,b):-inpA5(a,b).
outA(a,b):-inpA6(a,b).
outA(a,b):-inpA7(a,b).
outA(a,b):-inpA8(a,b).
outA(a,b):-inpA9(a,b).
outA(a,b):-inpA10(a,b).
outA(a,b):-inpA11(a,b).
outA(a,b):-inpA12(a,b).
outA(a,b):-inpA13(a,b).
outA(a,b):-inpA14(a,b).
outA(a,b):-inpA15(a,b).
outA(a,b):-inpA16(a,b).
outA(a,b):-inpA17(a,b).
outA(a,b):-inpA18(a,b).
outA(a,b):-inpA19(a,b).
outA(a,b):-inpA20(a,b).
outA(a,b):-inpA21(a,b).
outA(a,b):-inpA22(a,b).
outA(a,b):-inpA23(a,b).
outA(a,b):-inpA24(a,b).
outA(a,b):-inpA25(a,b).
outA(a,b):-inpA26(a,b).
outA(a,b):-inpA27(a,b).
outA(a,b):-inpA28(a,b).
outA(a,b):-inpA29(a,b).
outA(a,b):-inpA30(a,b).
outA(a,b):-inpA31(a,b).
outA(a,b):-inpA32(a,b).
outA(a,b):-inpA33(a,b).
outA(a,b):-inpA34(a,b).
outA(a,b):-inpA35(a,b).
outA(a,b):-inpA36(a,b).
outA(a,b):-inpA37(a,b).
outA(a,b):-inpA38(a,b).
outA(a,b):-inpA39(a,b).
outA(a,b):-inpA40(a,b).
outA(a,b):-inpA41(a,b).
outA(a,b):-inpA42(a,b).
outA(a,b):-inpA43(a,b).
outA(a,b):-inpA44(a,b).
outA(a,b):-inpA45(a,b).
outA(a,b):-inpA46(a,b).
outA(a,b):-inpA47(a,b).
outA(a,b):-inpA48(a,b).
outA(a,b):-inpA49(a,b).
outA(a,b):-inpA50(a,b).
outA(a,b):-inpA51(a,b).
outA(a,b):-inpA52(a,b).
outA(a,b):-inpA53(a,b).
outA(a,b):-inpA54(a,b).
outA(a,b):-inpA55(a,b).
outA(a,b):-inpA56(a,b).
outA(a,b):-inpA57(a,b).
outA(a,b):-inpA58(a,b).
outA(a,b):-inpA59(a,b).
outA(a,b):-inpA60(a,b).
outA(a,b):-inpA61(a,b).
outA(a,b):-inpA62(a,b).
outA(a,b):-inpA63(a,b).
outA(a,b):-inpA64(a,b).
outA(a,b):-inpA65(a,b).
outA(a,b):-inpA66(a,b).
outA(a,b):-inpA67(a,b).
outA(a,b):-inpA68(a,b).
outA(a,b):-inpA69(a,b).
outA(a,b):-inpA70(a,b).
outA(a,b):-inpA71(a,b).
outA(a,b):-inpA72(a,b).
outA(a,b):-inpA73(a,b).
outA(a,b):-inpA74(a,b).
outA(a,b):-inpA75(a,b).
outA(a,b):-inpA76(a,b).
outA(a,b):-inpA77(a,b).
outA(a,b):-inpA78(a,b).
outA(a,b):-inpA79(a,b).
outA(a,b):-inpA80(a,b).
outA(a,b):-inpA81(a,b).
outA(a,b):-inpA82(a,b).
outA(a,b):-inpA83(a,b).
outA(a,b):-inpA84(a,b).
outA(a,b):-inpA85(a,b).
outA(a,b):-inpA86(a,b).
outA(a,b):-inpA87(a,b).
outA(a,b):-inpA88(a,b).
outA(a,b):-inpA89(a,b).
outA(a,b):-inpA90(a,b).
outA(a,b):-inpA91(a,b).
outA(a,b):-inpA92(a,b).
outA(a,b):-inpA93(a,b).
outA(a,b):-inpA94(a,b).
outA(a,b):-inpA95(a,b).
outA(a,b):-inpA96(a,b).
outA(a,b):-inpA97(a,b).
outA(a,b):-inpA98(a,b).
outA(a,b):-inpA99(a,b).
outA(a,b):-inpA100(a,b).
outA(a,b):-inpA101(a,b).
outA(a,b):-inpA102(a,b).
outA(a,b):-inpA103(a,b).
outA(a,b):-inpA104(a,b).
outA(a,b):-inpA105(a,b).
outA(a,b):-inpA106(a,b).
outA(a,b):-inpA107(a,b).
outA(a,b):-inpA108(a,b).
outA(a,b):-inpA109(a,b).
outA(a,b):-inpA110(a,b).
outA(a,b):-inpA111(a,b).
outA(a,b):-inpA112(a,b).
outA(a,b):-inpA113(a,b).
outA(a,b):-inpA114(a,b).
outA(a,b):-inpA115(a,b).
outA(a,b):-inpA116(a,b).
outA(a,b):-inpA117(a,b).
outA(a,b):-inpA118(a,b).
outA(a,b):-inpA119(a,b).
outA(a,b):-inpA120(a,b).
outA(a,b):-inpA121(a,b).
outA(a,b):-inpA122(a,b).
outA(a,b):-inpA123(a,b).
outA(a,b):-inpA124(a,b).
outA(a,b):-inpA125(a,b).
outA(a,b):-inpA126(a,b).
outA(a,b):-inpA127(a,b).
outA(a,b):-inpA128(a,b).
outA(a,b):-inpA129(a,b).
outA(a,b):-inpA130(a,b).
outA(a,b):-inpA131(a,b).
outA(a,b):-inpA132(a,b).
outA(a,b):-inpA133(a,b).
outA(a,b):-inpA134(a,b).
outA(a,b):-inpA135(a,b).
outA(a,b):-inpA136(a,b).
outA(a,b):-inpA137(a,b).
outA(a,b):-inpA138(a,b).
outA(a,b):-inpA139(a,b).
outA(a,b):-inpA140(a,b).
outA(a,b):-inpA141(a,b).
outA(a,b):-inpA142(a,b).
outA(a,b):-inpA143(a,b).
outA(a,b):-inpA144(a,b).
outA(a,b):-inpA145(a,b).
outA(a,b):-inpA146(a,b).
outA(a,b):-inpA147(a,b).
outA(a,b):-inpA148(a,b).
outA(a,b):-inpA149(a,b).
outA(a,b):-inpA150(a,b).
outA(a,b):-inpA151(a,b).
outA(a,b):-inpA152(a,b).
outA(a,b):-inpA153(a,b).
outA(a,b):-inpA154(a,b).
outA(a,b):-inpA155(a,b).
outA(a,b):-inpA156(a,b).
outA(a,b):-inpA157(a,b).
outA(a,b):-inpA158(a,b).
outA(a,b):-inpA159(a,b).
outA(a,b):-inpA160(a,b).
outA(a,b):-inpA161(a,b).
outA(a,b):-inpA162(a,b).
outA(a,b):-inpA163(a,b).
outA(a,b):-inpA164(a,b).
outA(a,b):-inpA165(a,b).
outA(a,b):-inpA166(a,b).
outA(a,b):-inpA167(a,b).
outA(a,b):-inpA168(a,b).
outA(a,b):-inpA169(a,b).
outA(a,b):-inpA170(a,b).
outA(a,b):-inpA171(a,b).
outA(a,b):-inpA172(a,b).
outA(a,b):-inpA173(a,b).
outA(a,b):-inpA174(a,b).
outA(a,b):-inpA175(a,b).
outA(a,b):-inpA176(a,b).
outA(a,b):-inpA177(a,b).
outA(a,b):-inpA178(a,b).
outA(a,b):-inpA179(a,b).
outA(a,b):-inpA180(a,b).
outA(a,b):-inpA181(a,b).
outA(a,b):-inpA182(a,b).
outA(a,b):-inpA183(a,b).
outA(a,b):-inpA184(a,b).
outA(a,b):-inpA185(a,b).
outA(a,b):-inpA186(a,b).
outA(a,b):-inpA187(a,b).
outA(a,b):-inpA188(a,b).
outA(a,b):-inpA189(a,b).
outA(a,b):-inpA190(a,b).
outA(a,b):-inpA191(a,b).
outA(a,b):-inpA192(a,b).
outA(a,b):-inpA193(a,b).
outA(a,b):-inpA194(a,b).
outA(a,b):-inpA195(a,b).
outA(a,b):-inpA196(a,b).
outA(a,b):-inpA197(a,b).
outA(a,b):-inpA198(a,b).
outA(a,b):-inpA199(a,b).
outA(a,b):-inpA200(a,b).
outA(a,b):-inpA201(a,b).
outA(a,b):-inpA202(a,b).
outA(a,b):-inpA203(a,b).
outA(a,b):-inpA204(a,b).
outA(a,b):-inpA205(a,b).
outA(a,b):-inpA206(a,b).
outA(a,b):-inpA207(a,b).
outA(a,b):-inpA208(a,b).
outA(a,b):-inpA209(a,b).
outA(a,b):-inpA210(a,b).
outA(a,b):-inpA211(a,b).
outA(a,b):-inpA212(a,b).
outA(a,b):-inpA213(a,b).
outA(a,b):-inpA214(a,b).
outA(a,b):-inpA215(a,b).
outA(a,b):-inpA216(a,b).
outA(a,b):-inpA217(a,b).
outA(a,b):-inpA218(a,b).
outA(a,b):-inpA219(a,b).
outA(a,b):-inpA220(a,b).
outA(a,b):-inpA221(a,b).
outA(a,b):-inpA222(a,b).
outA(a,b):-inpA223(a,b).
outA(a,b):-inpA224(a,b).
outA(a,b):-inpA225(a,b).
outA(a,b):-inpA226(a,b).
outA(a,b):-inpA227(a,b).
outA(a,b):-inpA228(a,b).
outA(a,b):-inpA229(a,b).
outA(a,b):-inpA230(a,b).
outA(a,b):-inpA231(a,b).
outA(a,b):-inpA232(a,b).
outA(a,b):-inpA233(a,b).
outA(a,b):-inpA234(a,b).
outA(a,b):-inpA235(a,b).
outA(a,b):-inpA236(a,b).
outA(a,b):-inpA237(a,b).
outA(a,b):-inpA238(a,b).
outA(a,b):-inpA239(a,b).
outA(a,b):-inpA240(a,b).
outA(a,b):-inpA241(a,b).
outA(a,b):-inpA242(a,b).
outA(a,b):-inpA243(a,b).
outA(a,b):-inpA244(a,b).
outA(a,b):-inpA245(a,b).
outA(a,b):-inpA246(a,b).
outA(a,b):-inpA247(a,b).
outA(a,b):-inpA248(a,b).
outA(a,b):-inpA249(a,b).
outA(a,b):-inpA250(a,b).
outA(a,b):-inpA251(a,b).
outA(a,b):-inpA252(a,b).
outA(a,b):-inpA253(a,b).
outA(a,b):-inpA254(a,b).
outA(a,b):-inpA255(a,b).
outA(a,b):-inpA256(a,b).
outA(a,b):-inpA257(a,b).
outA(a,b):-inpA258(a,b).
outA(a,b):-inpA259(a,b).
outA(a,b):-inpA260(a,b).
outA(a,b):-inpA261(a,b).
outA(a,b):-inpA262(a,b).
outA(a,b):-inpA263(a,b).
outA(a,b):-inpA264(a,b).
outA(a,b):-inpA265(a,b).
outA(a,b):-inpA266(a,b).
outA(a,b):-inpA267(a,b).
outA(a,b):-inpA268(a,b).
outA(a,b):-inpA269(a,b).
outA(a,b):-inpA270(a,b).
outA(a,b):-inpA271(a,b).
outA(a,b):-inpA272(a,b).
outA(a,b):-inpA273(a,b).
outA(a,b):-inpA274(a,b).
outA(a,b):-inpA275(a,b).
outA(a,b):-inpA276(a,b).
outA(a,b):-inpA277(a,b).
outA(a,b):-inpA278(a,b).
outA(a,b):-inpA279(a,b).
outA(a,b):-inpA280(a,b).
outA(a,b):-inpA281(a,b).
outA(a,b):-inpA282(a,b).
outA(a,b):-inpA283(a,b).
outA(a,b):-inpA284(a,b).
outA(a,b):-inpA285(a,b).
outA(a,b):-inpA286(a,b).
outA(a,b):-inpA287(a,b).
outA(a,b):-inpA288(a,b).
outA(a,b):-inpA289(a,b).
outA(a,b):-inpA290(a,b).
outA(a,b):-inpA291(a,b).
outA(a,b):-inpA292(a,b).
outA(a,b):-inpA293(a,b).
outA(a,b):-inpA294(a,b).
outA(a,b):-inpA295(a,b).
outA(a,b):-inpA296(a,b).
outA(a,b):-inpA297(a,b).
outA(a,b):-inpA298(a,b).
outA(a,b):-inpA299(a,b).
outA(a,b):-inpA300(a,b).
outA(a,b):-inpA301(a,b).
outA(a,b):-inpA302(a,b).
outA(a,b):-inpA303(a,b).
outA(a,b):-inpA304(a,b).
outA(a,b):-inpA305(a,b).
outA(a,b):-inpA306(a,b).
outA(a,b):-inpA307(a,b).
outA(a,b):-inpA308(a,b).
outA(a,b):-inpA309(a,b).
outA(a,b):-inpA310(a,b).
outA(a,b):-inpA311(a,b).
outA(a,b):-inpA312(a,b).
outA(a,b):-inpA313(a,b).
outA(a,b):-inpA314(a,b).
outA(a,b):-inpA315(a,b).
outA(a,b):-inpA316(a,b).
outA(a,b):-inpA317(a,b).
outA(a,b):-inpA318(a,b).
outA(a,b):-inpA319(a,b).
outA(a,b):-inpA320(a,b).
outA(a,b):-inpA321(a,b).
outA(a,b):-inpA322(a,b).
outA(a,b):-inpA323(a,b).
outA(a,b):-inpA324(a,b).
outA(a,b):-inpA325(a,b).
outA(a,b):-inpA326(a,b).
outA(a,b):-inpA327(a,b).
outA(a,b):-inpA328(a,b).
outA(a,b):-inpA329(a,b).
outA(a,b):-inpA330(a,b).
outA(a,b):-inpA331(a,b).
outA(a,b):-inpA332(a,b).
outA(a,b):-inpA333(a,b).
outA(a,b):-inpA334(a,b).
outA(a,b):-inpA335(a,b).
outA(a,b):-inpA336(a,b).
outA(a,b):-inpA337(a,b).
outA(a,b):-inpA338(a,b).
outA(a,b):-inpA339(a,b).
outA(a,b):-inpA340(a,b).
outA(a,b):-inpA341(a,b).
outA(a,b):-inpA342(a,b).
outA(a,b):-inpA343(a,b).
outA(a,b):-inpA344(a,b).
outA(a,b):-inpA345(a,b).
outA(a,b):-inpA346(a,b).
outA(a,b):-inpA347(a,b).
outA(a,b):-inpA348(a,b).
outA(a,b):-inpA349(a,b).
outA(a,b):-inpA350(a,b).
outA(a,b):-inpA351(a,b).
outA(a,b):-inpA352(a,b).
outA(a,b):-inpA353(a,b).
outA(a,b):-inpA354(a,b).
outA(a,b):-inpA355(a,b).
outA(a,b):-inpA356(a,b).
outA(a,b):-inpA357(a,b).
outA(a,b):-inpA358(a,b).
outA(a,b):-inpA359(a,b).
outA(a,b):-inpA360(a,b).
outA(a,b):-inpA361(a,b).
outA(a,b):-inpA362(a,b).
outA(a,b):-inpA363(a,b).
outA(a,b):-inpA364(a,b).
outA(a,b):-inpA365(a,b).
outA(a,b):-inpA366(a,b).
outA(a,b):-inpA367(a,b).
outA(a,b):-inpA368(a,b).
outA(a,b):-inpA369(a,b).
outA(a,b):-inpA370(a,b).
outA(a,b):-inpA371(a,b).
outA(a,b):-inpA372(a,b).
outA(a,b):-inpA373(a,b).
outA(a,b):-inpA374(a,b).
outA(a,b):-inpA375(a,b).
outA(a,b):-inpA376(a,b).
outA(a,b):-inpA377(a,b).
outA(a,b):-inpA378(a,b).
outA(a,b):-inpA379(a,b).
outA(a,b):-inpA380(a,b).
outA(a,b):-inpA381(a,b).
outA(a,b):-inpA382(a,b).
outA(a,b):-inpA383(a,b).
outA(a,b):-inpA384(a,b).
outA(a,b):-inpA385(a,b).
outA(a,b):-inpA386(a,b).
outA(a,b):-inpA387(a,b).
outA(a,b):-inpA388(a,b).
outA(a,b):-inpA389(a,b).
outA(a,b):-inpA390(a,b).
outA(a,b):-inpA391(a,b).
outA(a,b):-inpA392(a,b).
outA(a,b):-inpA393(a,b).
outA(a,b):-inpA394(a,b).
outA(a,b):-inpA395(a,b).
outA(a,b):-inpA396(a,b).
outA(a,b):-inpA397(a,b).
outA(a,b):-inpA398(a,b).
outA(a,b):-inpA399(a,b).
outA(a,b):-inpA400(a,b).
outA(a,b):-inpA401(a,b).
outA(a,b):-inpA402(a,b).
outA(a,b):-inpA403(a,b).
outA(a,b):-inpA404(a,b).
outA(a,b):-inpA405(a,b).
outA(a,b):-inpA406(a,b).
outA(a,b):-inpA407(a,b).
outA(a,b):-inpA408(a,b).
outA(a,b):-inpA409(a,b).
outA(a,b):-inpA410(a,b).
outA(a,b):-inpA411(a,b).
outA(a,b):-inpA412(a,b).
outA(a,b):-inpA413(a,b).
outA(a,b):-inpA414(a,b).
outA(a,b):-inpA415(a,b).
outA(a,b):-inpA416(a,b).
outA(a,b):-inpA417(a,b).
outA(a,b):-inpA418(a,b).
outA(a,b):-inpA419(a,b).
outA(a,b):-inpA420(a,b).
outA(a,b):-inpA421(a,b).
outA(a,b):-inpA422(a,b).
outA(a,b):-inpA423(a,b).
outA(a,b):-inpA424(a,b).
outA(a,b):-inpA425(a,b).
outA(a,b):-inpA426(a,b).
outA(a,b):-inpA427(a,b).
outA(a,b):-inpA428(a,b).
outA(a,b):-inpA429(a,b).
outA(a,b):-inpA430(a,b).
outA(a,b):-inpA431(a,b).
outA(a,b):-inpA432(a,b).
outA(a,b):-inpA433(a,b).
outA(a,b):-inpA434(a,b).
outA(a,b):-inpA435(a,b).
outA(a,b):-inpA436(a,b).
outA(a,b):-inpA437(a,b).
outA(a,b):-inpA438(a,b).
outA(a,b):-inpA439(a,b).
outA(a,b):-inpA440(a,b).
outA(a,b):-inpA441(a,b).
outA(a,b):-inpA442(a,b).
outA(a,b):-inpA443(a,b).
outA(a,b):-inpA444(a,b).
outA(a,b):-inpA445(a,b).
outA(a,b):-inpA446(a,b).
outA(a,b):-inpA447(a,b).
outA(a,b):-inpA448(a,b).
outA(a,b):-inpA449(a,b).
outA(a,b):-inpA450(a,b).
outA(a,b):-inpA451(a,b).
outA(a,b):-inpA452(a,b).
outA(a,b):-inpA453(a,b).
outA(a,b):-inpA454(a,b).
outA(a,b):-inpA455(a,b).
outA(a,b):-inpA456(a,b).
outA(a,b):-inpA457(a,b).
outA(a,b):-inpA458(a,b).
outA(a,b):-inpA459(a,b).
outA(a,b):-inpA460(a,b).
outA(a,b):-inpA461(a,b).
outA(a,b):-inpA462(a,b).
outA(a,b):-inpA463(a,b).
outA(a,b):-inpA464(a,b).
outA(a,b):-inpA465(a,b).
outA(a,b):-inpA466(a,b).
outA(a,b):-inpA467(a,b).
outA(a,b):-inpA468(a,b).
outA(a,b):-inpA469(a,b).
outA(a,b):-inpA470(a,b).
outA(a,b):-inpA471(a,b).
outA(a,b):-inpA472(a,b).
outA(a,b):-inpA473(a,b).
outA(a,b):-inpA474(a,b).
outA(a,b):-inpA475(a,b).
outA(a,b):-inpA476(a,b).
outA(a,b):-inpA477(a,b).
outA(a,b):-inpA478(a,b).
outA(a,b):-inpA479(a,b).
outA(a,b):-inpA480(a,b).
outA(a,b):-inpA481(a,b).
outA(a,b):-inpA482(a,b).
outA(a,b):-inpA483(a,b).
outA(a,b):-inpA484(a,b).
outA(a,b):-inpA485(a,b).
outA(a,b):-inpA486(a,b).
outA(a,b):-inpA487(a,b).
outA(a,b):-inpA488(a,b).
outA(a,b):-inpA489(a,b).
outA(a,b):-inpA490(a,b).
outA(a,b):-inpA491(a,b).
outA(a,b):-inpA492(a,b).
outA(a,b):-inpA493(a,b).
outA(a,b):-inpA494(a,b).
outA(a,b):-inpA495(a,b).
outA(a,b):-inpA496(a,b).
outA(a,b):-inpA497(a,b).
outA(a,b):-inpA498(a,b).
outA(a,b):-inpA499(a,b).
outA(a,b):-inpA500(a,b).
outA(a,b):-inpA501(a,b).
outA(a,b):-inpA502(a,b).
outA(a,b):-inpA503(a,b).
outA(a,b):-inpA504(a,b).
outA(a,b):-inpA505(a,b).
outA(a,b):-inpA506(a,b).
outA(a,b):-inpA507(a,b).
outA(a,b):-inpA508(a,b).
outA(a,b):-inpA509(a,b).
outA(a,b):-inpA510(a,b).
outA(a,b):-inpA511(a,b).
outA(a,b):-inpA512(a,b).
outA(a,b):-inpA513(a,b).
outA(a,b):-inpA514(a,b).
outA(a,b):-inpA515(a,b).
outA(a,b):-inpA516(a,b).
outA(a,b):-inpA517(a,b).
outA(a,b):-inpA518(a,b).
outA(a,b):-inpA519(a,b).
outA(a,b):-inpA520(a,b).
outA(a,b):-inpA521(a,b).
outA(a,b):-inpA522(a,b).
outA(a,b):-inpA523(a,b).
outA(a,b):-inpA524(a,b).
outA(a,b):-inpA525(a,b).
outA(a,b):-inpA526(a,b).
outA(a,b):-inpA527(a,b).
outA(a,b):-inpA528(a,b).
outA(a,b):-inpA529(a,b).
outA(a,b):-inpA530(a,b).
outA(a,b):-inpA531(a,b).
outA(a,b):-inpA532(a,b).
outA(a,b):-inpA533(a,b).
outA(a,b):-inpA534(a,b).
outA(a,b):-inpA535(a,b).
outA(a,b):-inpA536(a,b).
outA(a,b):-inpA537(a,b).
outA(a,b):-inpA538(a,b).
outA(a,b):-inpA539(a,b).
outA(a,b):-inpA540(a,b).
outA(a,b):-inpA541(a,b).
outA(a,b):-inpA542(a,b).
outA(a,b):-inpA543(a,b).
outA(a,b):-inpA544(a,b).
outA(a,b):-inpA545(a,b).
outA(a,b):-inpA546(a,b).
outA(a,b):-inpA547(a,b).
outA(a,b):-inpA548(a,b).
outA(a,b):-inpA549(a,b).
outA(a,b):-inpA550(a,b).
outA(a,b):-inpA551(a,b).
outA(a,b):-inpA552(a,b).
outA(a,b):-inpA553(a,b).
outA(a,b):-inpA554(a,b).
outA(a,b):-inpA555(a,b).
outA(a,b):-inpA556(a,b).
outA(a,b):-inpA557(a,b).
outA(a,b):-inpA558(a,b).
outA(a,b):-inpA559(a,b).
outA(a,b):-inpA560(a,b).
outA(a,b):-inpA561(a,b).
outA(a,b):-inpA562(a,b).
outA(a,b):-inpA563(a,b).
outA(a,b):-inpA564(a,b).
outA(a,b):-inpA565(a,b).
outA(a,b):-inpA566(a,b).
outA(a,b):-inpA567(a,b).
outA(a,b):-inpA568(a,b).
outA(a,b):-inpA569(a,b).
outA(a,b):-inpA570(a,b).
outA(a,b):-inpA571(a,b).
outA(a,b):-inpA572(a,b).
outA(a,b):-inpA573(a,b).
outA(a,b):-inpA574(a,b).
outA(a,b):-inpA575(a,b).
outA(a,b):-inpA576(a,b).
outA(a,b):-inpA577(a,b).
outA(a,b):-inpA578(a,b).
outA(a,b):-inpA579(a,b).
outA(a,b):-inpA580(a,b).
outA(a,b):-inpA581(a,b).
outA(a,b):-inpA582(a,b).
outA(a,b):-inpA583(a,b).
outA(a,b):-inpA584(a,b).
outA(a,b):-inpA585(a,b).
outA(a,b):-inpA586(a,b).
outA(a,b):-inpA587(a,b).
outA(a,b):-inpA588(a,b).
outA(a,b):-inpA589(a,b).
outA(a,b):-inpA590(a,b).
outA(a,b):-inpA591(a,b).
outA(a,b):-inpA592(a,b).
outA(a,b):-inpA593(a,b).
outA(a,b):-inpA594(a,b).
outA(a,b):-inpA595(a,b).
outA(a,b):-inpA596(a,b).
outA(a,b):-inpA597(a,b).
outA(a,b):-inpA598(a,b).
outA(a,b):-inpA599(a,b).
outA(a,b):-inpA600(a,b).
outA(a,b):-inpA601(a,b).
outA(a,b):-inpA602(a,b).
outA(a,b):-inpA603(a,b).
outA(a,b):-inpA604(a,b).
outA(a,b):-inpA605(a,b).
outA(a,b):-inpA606(a,b).
outA(a,b):-inpA607(a,b).
outA(a,b):-inpA608(a,b).
outA(a,b):-inpA609(a,b).
outA(a,b):-inpA610(a,b).
outA(a,b):-inpA611(a,b).
outA(a,b):-inpA612(a,b).
outA(a,b):-inpA613(a,b).
outA(a,b):-inpA614(a,b).
outA(a,b):-inpA615(a,b).
outA(a,b):-inpA616(a,b).
outA(a,b):-inpA617(a,b).
outA(a,b):-inpA618(a,b).
outA(a,b):-inpA619(a,b).
outA(a,b):-inpA620(a,b).
outA(a,b):-inpA621(a,b).
outA(a,b):-inpA622(a,b).
outA(a,b):-inpA623(a,b).
outA(a,b):-inpA624(a,b).
outA(a,b):-inpA625(a,b).
outA(a,b):-inpA626(a,b).
outA(a,b):-inpA627(a,b).
outA(a,b):-inpA628(a,b).
outA(a,b):-inpA629(a,b).
outA(a,b):-inpA630(a,b).
outA(a,b):-inpA631(a,b).
outA(a,b):-inpA632(a,b).
outA(a,b):-inpA633(a,b).
outA(a,b):-inpA634(a,b).
outA(a,b):-inpA635(a,b).
outA(a,b):-inpA636(a,b).
outA(a,b):-inpA637(a,b).
outA(a,b):-inpA638(a,b).
outA(a,b):-inpA639(a,b).
outA(a,b):-inpA640(a,b).
outA(a,b):-inpA641(a,b).
outA(a,b):-inpA642(a,b).
outA(a,b):-inpA643(a,b).
outA(a,b):-inpA644(a,b).
outA(a,b):-inpA645(a,b).
outA(a,b):-inpA646(a,b).
outA(a,b):-inpA647(a,b).
outA(a,b):-inpA648(a,b).
outA(a,b):-inpA649(a,b).
outA(a,b):-inpA650(a,b).
outA(a,b):-inpA651(a,b).
outA(a,b):-inpA652(a,b).
outA(a,b):-inpA653(a,b).
outA(a,b):-inpA654(a,b).
outA(a,b):-inpA655(a,b).
outA(a,b):-inpA656(a,b).
outA(a,b):-inpA657(a,b).
outA(a,b):-inpA658(a,b).
outA(a,b):-inpA659(a,b).
outA(a,b):-inpA660(a,b).
outA(a,b):-inpA661(a,b).
outA(a,b):-inpA662(a,b).
outA(a,b):-inpA663(a,b).
outA(a,b):-inpA664(a,b).
outA(a,b):-inpA665(a,b).
outA(a,b):-inpA666(a,b).
outA(a,b):-inpA667(a,b).
outA(a,b):-inpA668(a,b).
outA(a,b):-inpA669(a,b).
outA(a,b):-inpA670(a,b).
outA(a,b):-inpA671(a,b).
outA(a,b):-inpA672(a,b).
outA(a,b):-inpA673(a,b).
outA(a,b):-inpA674(a,b).
outA(a,b):-inpA675(a,b).
outA(a,b):-inpA676(a,b).
outA(a,b):-inpA677(a,b).
outA(a,b):-inpA678(a,b).
outA(a,b):-inpA679(a,b).
outA(a,b):-inpA680(a,b).
outA(a,b):-inpA681(a,b).
outA(a,b):-inpA682(a,b).
outA(a,b):-inpA683(a,b).
outA(a,b):-inpA684(a,b).
outA(a,b):-inpA685(a,b).
outA(a,b):-inpA686(a,b).
outA(a,b):-inpA687(a,b).
outA(a,b):-inpA688(a,b).
outA(a,b):-inpA689(a,b).
outA(a,b):-inpA690(a,b).
outA(a,b):-inpA691(a,b).
outA(a,b):-inpA692(a,b).
outA(a,b):-inpA693(a,b).
outA(a,b):-inpA694(a,b).
outA(a,b):-inpA695(a,b).
outA(a,b):-inpA696(a,b).
outA(a,b):-inpA697(a,b).
outA(a,b):-inpA698(a,b).
outA(a,b):-inpA699(a,b).
outA(a,b):-inpA700(a,b).
outA(a,b):-inpA701(a,b).
outA(a,b):-inpA702(a,b).
outA(a,b):-inpA703(a,b).
outA(a,b):-inpA704(a,b).
outA(a,b):-inpA705(a,b).
outA(a,b):-inpA706(a,b).
outA(a,b):-inpA707(a,b).
outA(a,b):-inpA708(a,b).
outA(a,b):-inpA709(a,b).
outA(a,b):-inpA710(a,b).
outA(a,b):-inpA711(a,b).
outA(a,b):-inpA712(a,b).
outA(a,b):-inpA713(a,b).
outA(a,b):-inpA714(a,b).
outA(a,b):-inpA715(a,b).
outA(a,b):-inpA716(a,b).
outA(a,b):-inpA717(a,b).
outA(a,b):-inpA718(a,b).
outA(a,b):-inpA719(a,b).
outA(a,b):-inpA720(a,b).
outA(a,b):-inpA721(a,b).
outA(a,b):-inpA722(a,b).
outA(a,b):-inpA723(a,b).
outA(a,b):-inpA724(a,b).
outA(a,b):-inpA725(a,b).
outA(a,b):-inpA726(a,b).
outA(a,b):-inpA727(a,b).
outA(a,b):-inpA728(a,b).
outA(a,b):-inpA729(a,b).
outA(a,b):-inpA730(a,b).
outA(a,b):-inpA731(a,b).
outA(a,b):-inpA732(a,b).
outA(a,b):-inpA733(a,b).
outA(a,b):-inpA734(a,b).
outA(a,b):-inpA735(a,b).
outA(a,b):-inpA736(a,b).
outA(a,b):-inpA737(a,b).
outA(a,b):-inpA738(a,b).
outA(a,b):-inpA739(a,b).
outA(a,b):-inpA740(a,b).
outA(a,b):-inpA741(a,b).
outA(a,b):-inpA742(a,b).
outA(a,b):-inpA743(a,b).
outA(a,b):-inpA744(a,b).
outA(a,b):-inpA745(a,b).
outA(a,b):-inpA746(a,b).
outA(a,b):-inpA747(a,b).
outA(a,b):-inpA748(a,b).
outA(a,b):-inpA749(a,b).
outA(a,b):-inpA750(a,b).

outA(a,c):-inpA1001(a,b),funcA(b,c).

