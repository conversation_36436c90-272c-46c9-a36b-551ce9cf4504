/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: implicit.cpp
 * Description: Datalog隐性规格测试
 * Author: <PERSON><PERSON><PERSON><PERSON> ywx1252574
 * Create: 2023-10-31
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "t_rd_sn.h"
#include "DatalogRunEnhance.h"

#define MAX_CMD_SIZE 1024


class implicitTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase()
    {

    }
    static void TearDownTestCase()
    {
        
    }

};
void implicitTest::SetUp()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    system("rm -rf /root/_datalog_");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void implicitTest::TearDown()
{
    AW_CHECK_LOG_END();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
class implicitTest2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase()
    {

    }
    static void TearDownTestCase()
    {
        TestUninstallAllDatalog();
    }

};
void implicitTest2::SetUp()
{
    int ret = 0;
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxLockTimeOut=10000\" \"workerHungThreshold=11,200,300\"");
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void implicitTest2::TearDown()
{
    AW_CHECK_LOG_END();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ********************************************************************************
 Description  : 001.一个表上关联的udf不超过2个(可能已被覆盖)
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_001)
{
        AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_001.d";
    char udfFileName[] = "imp_files/CT002_001_udf.c";
    char libName[] = "imp_files/CT002_001.so";
    char nsName[] = "CT002_001";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t data1 = 10000;
    int32_t data[3] = {1,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &data1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    sleep(1);
    ret = executeCommand(g_command, "\"a\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 002.表名中最多一个“.”
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_002)
{
        AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_002.d";
    char udfFileName[] = "imp_files/CT002_002_udf.c";
    char libName[] = "imp_files/CT002_002.so";
    char nsName[] = "CT002_002";
    char labelName_in[] = "ns_ns1.in.pA";
    char labelName_out[] = "ns_ns1.ou.tB";
    char labelName_in1[] = "in.pC";
    char labelName_out1[] = "ou.tD";

    (void)TestUninstallDatalog(nsName);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -f %s", &fileName);
    ret = executeCommand(g_command,"Fail to parse datalog file. Exit with code 1009000.");
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 003.so的名字长度（不含\0）至少为4
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/T.d";
    char udfFileName[] = "imp_files/T_udf.c";
    char libName[] = "imp_files/T.so";
    char nsName[] = "T";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,5,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5","\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 004. .d中%xxx的类型不超过7个
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_004.d";
    char udfFileName[] = "imp_files/CT002_004_udf.c";
    char libName[] = "imp_files/CT002_004.so";
    char nsName[] = "CT002_004";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outE";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 15");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 005. .d中的基础关系类型不超过4个
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_004.d";
    char udfFileName[] = "imp_files/CT002_004_udf.c";
    char libName[] = "imp_files/CT002_004.so";
    char nsName[] = "CT002_004";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outE";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 15");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 006. aggregate的option个数不超过5个
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_006.d";
    char udfFileName[] = "imp_files/CT002_006_udf.c";
    char libName[] = "imp_files/CT002_006.so";
    char nsName[] = "CT002_006";
    char labelName_in[] = "inpB";
    char labelName_out[] = "outD";
    
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvDropTable(stmt,"test_kv");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c kvtable -f imp_files/test_kv.gmjson -s %s -ns %s",
        g_toolPath, g_connServer, g_testNameSpace);
    system(g_command);
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = GmcKvDropTable(stmt,"test_kv");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 007. 普通function的option个数不超过3个
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_007.d";
    char udfFileName[] = "imp_files/CT002_007_udf.c";
    char libName[] = "imp_files/CT002_007.so";
    char nsName[] = "CT002_007";
    char labelName_in[] = "inpB";
    char labelName_out[] = "outD";
    
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvDropTable(stmt,"test_kv");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c kvtable -f imp_files/test_kv.gmjson -s %s -ns %s",
        g_toolPath, g_connServer, g_testNameSpace);
    system(g_command);
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = GmcKvDropTable(stmt,"test_kv");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 008. 状态转移函数的option个数不超过4个
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_008.d";
    char udfFileName[] = "imp_files/CT002_008_udf.c";
    char libName[] = "imp_files/CT002_008.so";
    char nsName[] = "CT002_008";
    char labelName_in[] = "inpB";
    char labelName_out[] = "outE";
    
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvDropTable(stmt,"test_kv");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c kvtable -f imp_files/test_kv.gmjson -s %s -ns %s",
        g_toolPath, g_connServer, g_testNameSpace);
    system(g_command);
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = GmcKvDropTable(stmt,"test_kv");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 009. resource的option个数不超过34个
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_009.d";
    char udfFileName[] = "imp_files/CT002_009_udf.c";
    char libName[] = "imp_files/CT002_009.so";
    char nsName[] = "CT002_009";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 010.规则成环的时候，离线编译工具报错信息至多只显示10个表名
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_010)
{
        AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_010.d";
    char udfFileName[] = "imp_files/CT002_010_udf.c";
    char libName[] = "imp_files/CT002_010.so";
    char nsName[] = "CT002_010";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    (void)TestUninstallDatalog(nsName);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -f %s", &fileName);
    ret = executeCommand(g_command,"midM");
    ASSERT_EQ(-1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 011.规则成环的时候，离线编译工具报错信息长度不超过1024
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_011)
{
        AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_011.d";
    char udfFileName[] = "imp_files/CT002_011_udf.c";
    char libName[] = "imp_files/CT002_011.so";
    char nsName[] = "CT002_011";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    (void)TestUninstallDatalog(nsName);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -f %s", &fileName);
    ret = executeCommand(g_command,"testlong011");
    ASSERT_EQ(-1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 012.timeout字段实际存入DB的数值是加了clock_gettime的，因此无法直接更新/删除
******************************************************************************** */
TEST_F(implicitTest, DataLog_055_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_012.d";
    char udfFileName[] = "imp_files/CT002_012_udf.c";
    char libName[] = "imp_files/CT002_012.so";
    char nsName[] = "CT002_012";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t data1 = 5000;
    int32_t data[3] = {1,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &data1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_in, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5000");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ********************************************************************************
 Description  : 013.timeout后台线程一次最多过期1024条数据
******************************************************************************** */
TEST_F(implicitTest2, DataLog_055_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT002_013.d";
    char udfFileName[] = "imp_files/CT002_013_udf.c";
    char libName[] = "imp_files/CT002_013.so";
    char nsName[] = "CT002_013";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t data1 = 1000;
    int64_t data2 = 10000;
    int32_t data[3] = {2,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    for (int i = 1;i<1100;i++){
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &data1, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "inpA done.");
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &data1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &data2, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(6);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview count %s -ns %s", &labelName_in, g_testNameSpace);
    ret = executeCommand(g_command, "inpA", "75");
    system(g_command);
    if(ret != GMERR_OK){
        sleep(30);
        ret = executeCommand(g_command, "inpA","75");
        system(g_command);
    } 
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(15);
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
void *DMLEXEC(void *args)
{
    char labelName_in[] = "inpA";
    char libName[] = "imp_files/CT003_001.so";
    char nsName[] = "CT003_001";
    int ret = 0;

    int32_t data[3] = {1,1,1};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 15;
    // set value
        DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = 1;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 2;
    }
    AW_FUN_Log(LOG_STEP, "inputAbegin------------------------------------------------");
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *DMLEXECF(void *args)
{
    char labelName_in[] = "inpA";
    char libName[] = "imp_files/CT003_025.so";
    char nsName[] = "CT003_025";
    int ret = 0;

    int32_t data[3] = {1,1,1};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 4;
    // set value
        DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = 1;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 2;
    }
    AW_FUN_Log(LOG_STEP, "inputAbegin------------------------------------------------");
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *LOADSO03(void *args)
{
    int ret = 0;
    char soName[] = "CT003_003_patchV2";
    char filePath[] = "imp_files";
    ret = LoadUpdateSoFile(soName, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *DROPSO04(void *args)
{
    int ret = 0;
    char soName[] = "CT003_003_rollbackV2";
    char filePath[] = "imp_files";
    ret = LoadRollbackSoFile(soName, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *DROPSO05(void *args)
{
    char nsName[] = "CT003_005";
    (void)TestUninstallDatalog(nsName);
}
void *LOADSO06(void *args)
{
    int ret = 0;
    char soName[] = "CT003_006_patchV2";
    char filePath[] = "imp_files";
    ret = LoadUpdateSoFile(soName, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *LOADSO15(void *args)
{
    int ret = 0;
    char soName[] = "CT003_015_patchV2";
    char filePath[] = "imp_files";
    ret = LoadUpdateSoFile(soName, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *DROPSO15(void *args)
{
    int ret = 0;
    char soName[] = "CT003_015_rollbackV2";
    char filePath[] = "imp_files";
    ret = LoadRollbackSoFile(soName, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *SYSVIEWCOUNT(void *args)
{
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview count -ns %s", g_testNameSpace);
    ret = executeCommand(g_command,"Lock is not available.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
}
void *SYSVIEWRECORD(void *args)
{
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record inpA -ns %s", g_testNameSpace);
    ret = executeCommand(g_command, "\"a\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
}
void *SYSVIEWPLAN(void *args)
{
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V$\\DATALOG_PLAN_EXPLAIN_INFO");
    ret = executeCommand(g_command, "DefaultDeltaMerge on Label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
}
void *SYSVIEWGENERAL(void *args)
{
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V$\\CATA_GENERAL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NUM:");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
}
void *SYSVIEWLABEL(void *args)
{
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V$\\CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "V$CATA_VERTEX_LABEL_INFO");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
}
void *SYSVIEWCOUNTP(void *args)
{
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview count -ns %s", g_testNameSpace);
    ret = executeCommand(g_command,"Lock is not available.");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
}
/* ****************************************************************************
 Description  : 003_001 对原始so的输入表进行dml，并发gmsysview count执行报12002错误码
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_001.d";
    char udfFileName[] = "imp_files/CT003_001_udf.c";
    char libName[] = "imp_files/CT003_001.so";
    char nsName[] = "CT003_001";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    ret = pthread_create(&client_thr[1], NULL, SYSVIEWCOUNT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_002 对原始so的输入表进行dml，并发gmsysview record inpA读到数据
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_001.d";
    char udfFileName[] = "imp_files/CT003_001_udf.c";
    char libName[] = "imp_files/CT003_001.so";
    char nsName[] = "CT003_001";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(500000);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWRECORD, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_003 加载升级so重做过程中，并发gmsysview count 执行报12002错误码
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_003.d";
    char udfFileName[] = "imp_files/CT003_003_udf.c";
    char libName[] = "imp_files/CT003_003.so";
    char nsName[] = "CT003_003";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    for (int i =0;i<5;i++){
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, LOADSO03, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
#ifdef RUN_SIMULATE
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWCOUNTP, NULL);
#else
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWCOUNT, NULL);
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        sleep(10);
    }
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_004 加载降级so重做过程中，并发gmsysview record inpA 能查到数据
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_003.d";
    char udfFileName[] = "imp_files/CT003_003_udf.c";
    char libName[] = "imp_files/CT003_003.so";
    char patchName[] = "imp_files/CT003_003_patchV2.so";
    char nsName[] = "CT003_003";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char soName[] = "CT003_003_patchV2";
    char filePath[] = "imp_files";
    ret = LoadUpdateSoFile(soName, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    for (int i =0;i<15;i++){
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DROPSO04, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWRECORD, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        sleep(10);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_005 线程1卸载原始so，线程2并发查gmsysview count视图
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_005.d";
    char udfFileName[] = "imp_files/CT003_005_udf.c";
    char libName[] = "imp_files/CT003_005.so";
    char patchName[] = "imp_files/CT003_005_patchV2.so";
    char nsName[] = "CT003_005";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DROPSO05, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(500000);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWCOUNTP, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(8);
    // 卸载
    TestUninstallDatalog(nsName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_006 升级patch.d新增多个表，加载升级so，并发查询gmsysviewcount视图
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_006.d";
    char udfFileName[] = "imp_files/CT003_006_udf.c";
    char libName[] = "imp_files/CT003_006.so";
    char patchName[] = "imp_files/CT003_006_patchV2.so";
    char nsName[] = "CT003_006";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, LOADSO06, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(500000);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWCOUNTP, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(8);
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_007 tbm函数中的init函数返回失败，加载原始so，查看catalog视图资源是否删除干净
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_007.d";
    char udfFileName[] = "imp_files/CT003_007_udf.c";
    char libName[] = "imp_files/CT003_007.so";
    char nsName[] = "CT003_007";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    
    (void)TestUninstallDatalog(nsName);
    TestLoadDatalog(libName);
    // 触发回滚

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "outB,outC");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_GENERAL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NUM: ");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_008 加载升级so，新增patch.d中含多个表，ddl内存防呆成功，升级
                重做dml防呆失败，加载升级so，查看catalog视图资源是否删除干净;
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_008.d";
    char udfFileName[] = "imp_files/CT003_008_udf.c";
    char libName[] = "imp_files/CT003_008.so";
    char nsName[] = "CT003_008";
    char labelName_in[] = "inp1";
    char labelName_in1[] = "inp2";
    char labelName_in2[] = "inp3";
    char labelName_out[] = "out1";

    system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxTotalDynSize=48\""); // 总动态内存48M
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxSysDynSize=16\""); // 系统动态内存16M
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=8\"");
    system("gmadmin -cfgName upgradeMemActualAndEstimatedPercentage -cfgVal 300");
    system("stop.sh -f");
    system("start.sh");

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    TestLoadDatalog(libName);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,1};  
    // set value
    for (int i =0;i<40000;i++){
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 加载升级.so，内存不足升级失败回滚
    system("gmimport -c datalog -upgrade imp_files/CT003_008_patchV2.so");
    
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "inp1_1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_009 加载降级so，升级之后写大量数据，ddl内存防呆
                成功，降级重做dml防呆失败，加载降级so，查看catalog视图资源是否删除干净
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_009.d";
    char udfFileName[] = "imp_files/CT003_009_udf.c";
    char libName[] = "imp_files/CT003_009.so";
    char nsName[] = "CT003_009";
    char labelName_in[] = "inp3";
    char labelName_out[] = "out1";

    system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxTotalDynSize=48\""); // 总动态内存48M
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxSysDynSize=16\""); // 系统动态内存16M
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=8\"");
    system("gmadmin -cfgName upgradeMemActualAndEstimatedPercentage -cfgVal 300");
    system("stop.sh -f");
    system("start.sh");
    AW_FUN_Log(LOG_STEP, "enableDatalogDmlWhenUpgrading修改为1");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(3,errorMsg1,errorMsg2,errorMsg3);

    (void)TestUninstallDatalog(nsName);
    TestLoadDatalog(libName);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,1};  
    // set value
    
    for (int i =0;i<20000;i++){
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 加载升级.so，
    char soName[] = "CT003_009_patchV2";
    char filePath[] = "imp_files";
    ret = LoadUpdateSoFile(soName, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    for (int i =20000;i<40000;i++){
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "加载卸载.so");
    // 卸载.so，内存不足触发回滚
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        system(g_command);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        sleep(10);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback imp_files/CT003_009_rollbackV2.so -ns %s",
        g_testNameSpace);
    system(g_command);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    system(g_command);
    ret = executeCommand(g_command, "inp3_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    ret = executeCommand(g_command, "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
    int i = 1;
    while (ret != GMERR_OK) {
        ret = executeCommand(g_command, "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
        system(g_command);
        sleep(10);
        i++;
        if (i == 20) {
            break;
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_010 udfEnable配置项设置为0，原始.d文件含function和多
                个表，加载原始so失败，查看catalog视图资源是否删除干净
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_010.d";
    char udfFileName[] = "imp_files/CT003_010_udf.c";
    char libName[] = "imp_files/CT003_010.so";
    char nsName[] = "CT003_010";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    system("sh $TEST_HOME/tools/modifyCfg.sh  \"udfEnable=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    TestLoadDatalog(libName);
    // 触发回滚

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    system(g_command);
    ret = executeCommand(g_command, "outB");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_GENERAL_INFO");
    system(g_command);
    ret = executeCommand(g_command, "VERTEX_LABEL_NUM: ");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_011 udfEnable配置项设置为0，新增patch.d文件含function和
                多个表，加载升级so失败，查看catalog视图资源是否删除干净
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_011.d";
    char udfFileName[] = "imp_files/CT003_011_udf.c";
    char libName[] = "imp_files/CT003_011.so";
    char patchso[] = "imp_files/CT003_011_patchV2.so";
    char nsName[] = "CT003_011";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    system("sh $TEST_HOME/tools/modifyCfg.sh  \"udfEnable=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s -ns %s", &patchso, g_testNameSpace);
    system(g_command);
    // 触发回滚

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    system(g_command);
    ret = executeCommand(g_command, "outD");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "inpB");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_GENERAL_INFO");
    system(g_command);
    ret = executeCommand(g_command, "VERTEX_LABEL_NUM: 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_012 patch.d新增输入表，加载升级so，查看catalog视图
                中的表是否新增，加载对应降级so，查看catalog视图；加载卸载so，查看catalog视图
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_012.d";
    char udfFileName[] = "imp_files/CT003_012_udf.c";
    char libName[] = "imp_files/CT003_012.so";
    char patchso[] = "imp_files/CT003_012_patchV2.so";
    char rbso[] = "imp_files/CT003_012_rollbackV2.so";
    char nsName[] = "CT003_012";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    char g_command1[MAX_CMD_SIZE];

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(g_command1, 0, sizeof(g_command1));
    (void)snprintf(g_command1, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command1, "inpA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command1, "inpB");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s -ns %s", &patchso, g_testNameSpace);
    system(g_command);

    ret = executeCommand(g_command1, "inpA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command1, "inpB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", &rbso, g_testNameSpace);
    system(g_command);

    ret = executeCommand(g_command1, "inpA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command1, "inpB");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = executeCommand(g_command1, "inpA");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command1, "inpB");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_013 加载原始so，加载补丁max_size重做后超出限制，补丁回滚，查看catalog视图
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_013.d";
    char udfFileName[] = "imp_files/CT003_013_udf.c";
    char libName[] = "imp_files/CT003_013.so";
    char patchso[] = "imp_files/CT003_013_patchV2.so";
    char nsName[] = "CT003_013";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[5]={1,1,1,2,2};

    for (int i =1;i<6;i++){
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i-1], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 升级后，触发回滚
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s -ns %s", &patchso, g_testNameSpace);
    system(g_command);
    
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    ret = executeCommand(g_command, "outB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "inpC");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_GENERAL_INFO");
    ret = executeCommand(g_command, "VERTEX_LABEL_NUM: ");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_014 加载原始so查看物理执行计划视图，加载升级so查看
                物理执行计划，加载降级so查看物理执行计划并校验
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_014.d";
    char udfFileName[] = "imp_files/CT003_014_udf.c";
    char libName[] = "imp_files/CT003_014.so";
    char patchso[] = "imp_files/CT003_014_patchV2.so";
    char rbso[] = "imp_files/CT003_014_rollbackV2.so";
    char nsName[] = "CT003_014";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    char g_command1[MAX_CMD_SIZE];

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(g_command1, 0, sizeof(g_command1));
    (void)snprintf(g_command1, MAX_CMD_SIZE, "gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");
    ret = executeCommand(g_command1, "inpA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command1, "inpB");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s -ns %s", &patchso, g_testNameSpace);
    system(g_command);

    ret = executeCommand(g_command1, "inpA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command1, "inpB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);   

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", &rbso, g_testNameSpace);
    system(g_command); 

    ret = executeCommand(g_command1, "inpA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command1, "inpB");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = executeCommand(g_command1, "inpA");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command1, "inpB");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_015 加载原始so，线程1加载升级so，线程2并发查询物理执行计划视图
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_015.d";
    char udfFileName[] = "imp_files/CT003_015_udf.c";
    char libName[] = "imp_files/CT003_015.so";
    char nsName[] = "CT003_015";
    char patchso[] = "CT003_015_patchV2";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, LOADSO15, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWPLAN, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_016 加载原始so，加载升级so，线程1加载降级so，线程2并发查询物理执行计划视图
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_015.d";
    char udfFileName[] = "imp_files/CT003_015_udf.c";
    char libName[] = "imp_files/CT003_015.so";
    char nsName[] = "CT003_015";
    char patchso[] = "CT003_015_patchV2";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmimport -c datalog -upgrade imp_files/CT003_015_patchV2.so -s %s -ns %s", g_toolPath, g_connServer,
        g_testNameSpace);
    system(g_command);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DROPSO15, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWPLAN, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_017 投影到null 升级为 投影到非null,编译报错
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_017_rule.d";
    char udfFileName[] = "imp_files/CT003_017_udf.c";
    char libName[] = "imp_files/CT003_017.so";
    char patchfile[] = "imp_files/CT003_017_patch.d";
    char patchso[] = "imp_files/CT003_017_patchV2.so";
    char rbso[] = "imp_files/CT003_017_rollbackV2.so";
    char nsName[] = "CT003_017";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    char g_command1[MAX_CMD_SIZE];

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &fileName,&patchfile);
    ret = executeCommand(g_command, "exit code 1004009");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_018 投影到非null 升级为 投影到null，编译报错
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_018_rule.d";
    char udfFileName[] = "imp_files/CT003_018_udf.c";
    char libName[] = "imp_files/CT003_018.so";
    char patchfile[] = "imp_files/CT003_018_patch.d";
    char patchso[] = "imp_files/CT003_018_patchV2.so";
    char rbso[] = "imp_files/CT003_018_rollbackV2.so";
    char nsName[] = "CT003_018";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    char g_command1[MAX_CMD_SIZE];

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &fileName,&patchfile);
    ret = executeCommand(g_command, "exit code 1004009");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_019 对原始so的输入表，进行dml，并发CATA_GENERAL_INFO
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_001.d";
    char udfFileName[] = "imp_files/CT003_001_udf.c";
    char libName[] = "imp_files/CT003_001.so";
    char nsName[] = "CT003_001";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWGENERAL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_020 对原始so的输入表，进行dml，并发CATA_VERTEX_LABEL_INFO
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_001.d";
    char udfFileName[] = "imp_files/CT003_001_udf.c";
    char libName[] = "imp_files/CT003_001.so";
    char nsName[] = "CT003_001";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWLABEL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_021 对原始so的输入表，进行dml，并发DATALOG_PLAN_EXPLAIN_INFO
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_001.d";
    char udfFileName[] = "imp_files/CT003_001_udf.c";
    char libName[] = "imp_files/CT003_001.so";
    char nsName[] = "CT003_001";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWPLAN, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_022 升级so，并发DATALOG_PLAN_EXPLAIN_INFO
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_006.d";
    char udfFileName[] = "imp_files/CT003_006_udf.c";
    char libName[] = "imp_files/CT003_006.so";
    char nsName[] = "CT003_006";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, LOADSO06, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWPLAN, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_023 升级so，并发CATA_VERTEX_LABEL_INFO
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_006.d";
    char udfFileName[] = "imp_files/CT003_006_udf.c";
    char libName[] = "imp_files/CT003_006.so";
    char nsName[] = "CT003_006";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, LOADSO06, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWLABEL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_024 升级so，并发CATA_GENERAL_INFO
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_006.d";
    char udfFileName[] = "imp_files/CT003_006_udf.c";
    char libName[] = "imp_files/CT003_006.so";
    char nsName[] = "CT003_006";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, LOADSO06, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWGENERAL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 003_025 加载升级so重做过程较短，并发gmsysview count 执行成功获取数据
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_003_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "imp_files/CT003_003.d";
    char udfFileName[] = "imp_files/CT003_003_udf.c";
    char libName[] = "imp_files/CT003_003.so";
    char nsName[] = "CT003_003";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXECF, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&client_thr[1], NULL, SYSVIEWCOUNTP, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 004_001 使用255字节的udf文件
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_004_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("mkdir d");
    system("cp imp_files/CT004_001_udf.c d/akjghfsakgfuaisgfagshjkdfga"
                        "sgfhjasgkfhgahsgfahjsgfkahjsgfkagsfagsfkh"
                        "asgfhjagsfahjsfiuashfiasfhuaisfhasfgasfua"
                        "guisfgiasgfsafulagsflgasfglasfgailsgfiasg"
                        "fagsufgaiusgfuagsfilasgfuasgfuasgfiluasgf"
                        "iuasgfiuasgfilasugfaiwulgfialuwgfiluasgfaiusgffddCT004_001_udf.c");
    char fileName[] = "CT004_001.so";
    char nsName[] = "CT004_001";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    (void)TestUninstallDatalog(nsName);

    system("gmprecompiler -f ./imp_files/CT004_001.d ");
    system("cp ./plan_str.c ./d");
    system("gcc ./d/plan_str.c ./d/akjghfsakgfuaisgfagshjkdfga"
                        "sgfhjasgkfhgahsgfahjsgfkahjsgfkagsfagsfkh"
                        "asgfhjagsfahjsfiuashfiasfhuaisfhasfgasfua"
                        "guisfgiasgfsafulagsflgasfglasfgailsgfiasg"
                        "fagsufgaiusgfuagsfilasgfuasgfuasgfiluasgf"
                        "iuasgfiuasgfilasugfaiwulgfialuwgfiluasgfa"
                        "iusgffddCT004_001_udf.c -fPIC --shared -W"
                        "l,-Bsymbolic -I ../../../../../pub/include/ -o ./d/CT004_001.so");
    system("gmimport -c datalog -f d/CT004_001.so");
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data1 = 5;
    int32_t data[3] = {1,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    sleep(1);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf d");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 004_002 创建256字节的udf文件
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_004_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("touch akjghfsakgfuaisgfagshjkdlfga"
                    "sgfhjasgkfhgahsgfahjsgfkahjsgfkagsfagsfkh"
                    "asgfhjagsfahjsfiuashfiasfhuaisfhasfgasfua"
                    "guisfgiasgfsafulagsflgasfglasfgailsgfiasg"
                    "fagsufgaiusgfuagsfilasgfuasgfuasgfiluasgf"
                    "iuasgfiuasgfilasugfaiwulgfialuwgfiluasgfaiusgffddCT004_002_udf.c");
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "ls");
    ret = executeCommand(g_command, "CT004_002_udf.c");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_001 原始.d有access_delta(midA)，但udf内没有对midA操作
                        补丁V1设置block0，修改udf操作A，写入数据
                        补丁V2设置block1，重做写入规则，触发function对A操作，查看数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_001/RF005_001.so";
    char nsName[] = "RF005_001";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data_a[2] = {5,10};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org midA");
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &inp_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_001_patchV2";
    char filePath[] = "reinforcement_files/RF005_001";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V1 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &inp_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_001_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V2 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &out_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3","\"c\": 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_002 原始.d有access_delta(midA)，udf内有对midA操作
                        补丁V1设置block0，修改udf不操作A，写入数据
                        补丁V2设置block1，重做写入规则，触发function，查看数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_002/RF005_002.so";
    char nsName[] = "RF005_002";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    ret = TestLoadDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data_a[2] = {5,10};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_002_patchV2";
    char filePath[] = "reinforcement_files/RF005_002";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V1 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &inp_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_002_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V2 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &out_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3","\"c\": 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_003 原始.d有access_delta(midA)，但udf内没有对midA操作
                        补丁V1设置block1，修改udf操作A，写入数据
                        补丁V2设置block0，重做写入规则，触发function对A操作，查看数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_003/RF005_003.so";
    char nsName[] = "RF005_003";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    ret = TestLoadDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data_a[2] = {5,10};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org midA");
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &inp_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_003_patchV2";
    char filePath[] = "reinforcement_files/RF005_003";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V1 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &inp_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_003_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V2 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &out_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3","\"c\": 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_004 原始.d有access_delta(midA)，udf内有对midA操作
                        补丁V1设置block1，修改udf不操作A，写入数据
                        补丁V2设置block0，重做写入规则，触发function，查看数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_004/RF005_004.so";
    char nsName[] = "RF005_004";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    ret = TestLoadDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data_a[2] = {5,10};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_004_patchV2";
    char filePath[] = "reinforcement_files/RF005_004";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V1 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &inp_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_004_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V2 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &out_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org inpB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3","\"c\": 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_005 原始.d有access_current(midA)，但udf内没有查询midA
                        补丁V1设置block0，修改udf查询midA，写入数据
                        补丁V2设置block1，重做写入规则，触发function对midA查询，查看数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_005/RF005_005.so";
    char nsName[] = "RF005_005";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    system("mkdir -p /root/_datalog_");
    system("touch /root/_datalog_/RF005_005.txt");
    system("chmod 777 -R /root/_datalog_/");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    ret = TestLoadDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data_a[3] = {5,10,15};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_A, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_005.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_005_patchV2";
    char filePath[] = "reinforcement_files/RF005_005";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_005.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_005_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V2 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 15", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &out_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org outB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3","\"c\": 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_005.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_005.txt");
    AW_FUN_Log(LOG_STEP, "cat data");
    system(g_command);
    ret = executeCommand(g_command, "a = 15", "b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_006 原始.d有access_current(midA)，udf查询midA
                        补丁V1设置block0，修改udf不查询midA，写入数据
                        补丁V2设置block1，重做写入规则，触发function查询midA，查看数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_006/RF005_006.so";
    char nsName[] = "RF005_006";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    system("mkdir -p /root/_datalog_");
    system("touch /root/_datalog_/RF005_006.txt");
    system("chmod 777 -R /root/_datalog_/");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    ret = TestLoadDatalog(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data_a[3] = {5,10,15};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_A, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_006.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_006_patchV2";
    char filePath[] = "reinforcement_files/RF005_006";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_006.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "check data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_006_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V2 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 15", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &out_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org outB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3","\"c\": 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_006.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "check data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_006.txt");
    AW_FUN_Log(LOG_STEP, "check data");
    system(g_command);
    ret = executeCommand(g_command, "a = 15", "b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_007 原始.d有access_current(midA)，但udf内没有查询midA
                        补丁V1设置block1，修改udf操作midA，写入数据
                        补丁V2设置block0，重做写入规则，触发function对midA操作，查看数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_007/RF005_007.so";
    char nsName[] = "RF005_007";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    system("mkdir -p /root/_datalog_");
    system("touch /root/_datalog_/RF005_007.txt");
    system("chmod 777 -R /root/_datalog_/");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    ret = TestLoadDatalog(fileName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data_a[3] = {5,10,15};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_A, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_007.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_007_patchV2";
    char filePath[] = "reinforcement_files/RF005_007";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_007.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_007_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V2 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 15", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &out_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org outB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3","\"c\": 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_007.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    // midA先重做，表内为新数据，undo不读，redo才读
    ret = executeCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_007.txt");
    AW_FUN_Log(LOG_STEP, "cat data");
    system(g_command);
    ret = executeCommand(g_command, "a = 15", "b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_008 原始.d有access_current(midA)，udf查询midA
                        补丁V1设置block1，修改udf不查询midA，写入数据
                        补丁V2设置block0，重做写入规则，触发function查询midA，查看数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_008/RF005_008.so";
    char nsName[] = "RF005_008";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    system("mkdir -p /root/_datalog_");
    system("touch /root/_datalog_/RF005_008.txt");
    system("chmod 777 -R /root/_datalog_/");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    ret = TestLoadDatalog(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data_a[3] = {5,10,15};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_A, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_008_patchV2";
    char filePath[] = "reinforcement_files/RF005_008";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_008.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "check data num");
    system(g_command);
    ret = executeCommand(g_command, "2");

    char soNameV2[] = "RF005_008_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &mid_A, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "V2 midA");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 15", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &out_B, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "org outB");
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 10", "\"b\": 3","\"c\": 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_008.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "check data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_008.txt");
    AW_FUN_Log(LOG_STEP, "check data");
    system(g_command);
    ret = executeCommand(g_command, "a = 15", "b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_009 原始.d有access_kv，udf内不查询kv表
                        补丁V1设置block0，修改udf查看kv表，写入数据
                        补丁V2设置block1，重做输入表，触发function读取kv表数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_009/RF005_009.so";
    char nsName[] = "RF005_009";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    system("mkdir -p /root/_datalog_");
    system("touch /root/_datalog_/RF005_009.txt");
    system("chmod 777 -R /root/_datalog_/");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName1[32] = "kv_test";
    GmcKvDropTable(stmt,tableName1);
    // kv元组结构体
    GmcKvTupleT kvInfo = {0};
    char key[32] = "para1";
    uint32_t value = 100;
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmimport -c kvtable -f ./reinforcement_files/kv_test.gmjson -s %s -ns %s", g_toolPath, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSet(stmt, key, strlen(key)+1, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestLoadDatalog(fileName);

    int32_t data_a[3] = {5,10,15};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_009.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_009_patchV2";
    char filePath[] = "reinforcement_files/RF005_009";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_009.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_009_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_009.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_009.txt");
    AW_FUN_Log(LOG_STEP, "cat data");
    system(g_command);
    ret = executeCommand(g_command, "key = para1", "value = 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_010 原始.d有access_kv，udf内查询kv表
                        补丁V1设置block0，修改udf不查看kv表，写入数据
                        补丁V2设置block1，重做输入表，触发function，重做后读取kv表预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_010/RF005_010.so";
    char nsName[] = "RF005_010";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    system("mkdir -p /root/_datalog_");
    system("touch /root/_datalog_/RF005_010.txt");
    system("chmod 777 -R /root/_datalog_/");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName1[32] = "kv_test";
    GmcKvDropTable(stmt,tableName1);
    // kv元组结构体
    GmcKvTupleT kvInfo = {0};
    char key[32] = "para1";
    uint32_t value = 100;
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmimport -c kvtable -f ./reinforcement_files/kv_test.gmjson -s %s -ns %s", g_toolPath, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSet(stmt, key, strlen(key)+1, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestLoadDatalog(fileName);

    int32_t data_a[3] = {5,10,15};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_010.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_010_patchV2";
    char filePath[] = "reinforcement_files/RF005_010";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_010.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_010_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_010.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_010.txt");
    AW_FUN_Log(LOG_STEP, "cat data");
    system(g_command);
    ret = executeCommand(g_command, "key = para1", "value = 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_011 原始.d有access_kv，udf内不查询kv表
                        补丁V1设置block1，修改udf查看kv表，写入数据
                        补丁V2设置block0，重做输入表，触发function读取kv表数据，预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_011/RF005_011.so";
    char nsName[] = "RF005_011";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    system("mkdir -p /root/_datalog_");
    system("touch /root/_datalog_/RF005_011.txt");
    system("chmod 777 -R /root/_datalog_/"); 

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName1[32] = "kv_test";
    GmcKvDropTable(stmt,tableName1);
    // kv元组结构体
    GmcKvTupleT kvInfo = {0};
    char key[32] = "para1";
    uint32_t value = 100;
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmimport -c kvtable -f ./reinforcement_files/kv_test.gmjson -s %s -ns %s", g_toolPath, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSet(stmt, key, strlen(key)+1, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestLoadDatalog(fileName);

    int32_t data_a[3] = {5,10,15};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_011.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_011_patchV2";
    char filePath[] = "reinforcement_files/RF005_011";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_011.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_011_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_011.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_011.txt");
    AW_FUN_Log(LOG_STEP, "cat data");
    system(g_command);
    ret = executeCommand(g_command, "key = para1", "value = 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 005_012 原始.d有access_kv，udf内查询kv表
                        补丁V1设置block1，修改udf不查看kv表，写入数据
                        补丁V2设置block0，重做输入表，触发function，重做后读取kv表预期正常
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "reinforcement_files/RF005_012/RF005_012.so";
    char nsName[] = "RF005_012";
    char inp_A[] = "inpA";
    char inp_B[] = "inpB";
    char mid_A[] = "midA";
    char out_A[] = "outA";
    char out_B[] = "outB";

    system("mkdir -p /root/_datalog_");
    system("touch /root/_datalog_/RF005_012.txt");
    system("chmod 777 -R /root/_datalog_/");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName1[32] = "kv_test";
    GmcKvDropTable(stmt,tableName1);
    // kv元组结构体
    GmcKvTupleT kvInfo = {0};
    char key[32] = "para1";
    uint32_t value = 100;
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmimport -c kvtable -f ./reinforcement_files/kv_test.gmjson -s %s -ns %s", g_toolPath, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSet(stmt, key, strlen(key)+1, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestLoadDatalog(fileName);

    int32_t data_a[3] = {5,10,15};
    int32_t data_b[4] = {1,2,3,1};

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_012.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV1[] = "RF005_012_patchV2";
    char filePath[] = "reinforcement_files/RF005_012";
    ret = LoadUpdateSoFile(soNameV1, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, inp_B, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data_a[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data_b[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data_b[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_012.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char soNameV2[] = "RF005_012_patchV3";
    ret = LoadUpdateSoFile(soNameV2, filePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_012.txt |wc -l");
    AW_FUN_Log(LOG_STEP, "cat data num");
    system(g_command);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/_datalog_/RF005_012.txt");
    AW_FUN_Log(LOG_STEP, "cat data");
    system(g_command);
    ret = executeCommand(g_command, "key = para1", "value = 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// DTS2024052011809
/* ****************************************************************************
 Description  : 005_013 DTS2024052011809补充用例，验证supressSpeErr显示
**************************************************************************** */
TEST_F(implicitTest, DataLog_055_005_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -h");
    system(g_command);
    ret = executeCommand(g_command, "-supressSpeErr");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
