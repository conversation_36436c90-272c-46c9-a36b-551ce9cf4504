/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : GMDBV502 迭代三 表记录过期
 Notes        : 本文件主要是可靠性场景
 History      :
 Author       : jiangshan/j00811785
 Modification : [2022.10.24]
*****************************************************************************/
#include "timeoutCommon.h"

class timeoutReliablityTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp()
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

        // 创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建连接
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_CHECK_LOG_BEGIN();
    }
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        // 断开同步连接
        int ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 001.precedence里都是输入表（包含timeout表）
TEST_F(timeoutReliablityTest, DataLog_011_007_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_001";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.订阅timeout表，校验推送数据
TEST_F(timeoutReliablityTest, DataLog_011_007_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_002";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    SnUserDataT *userData;
    int ret = testSnMallocUserData(&userData, 6 * 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //创建订阅连接
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *g_subConnName = "subConnName";
    const char *g_subName = "subNs01A";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    // 创建订阅关系
    char *sub_info = NULL;
    readJanssonFile("./schemaFile/subInfoNs01A.json", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_sub_info, conn_sn_sync, snCallback, userData);
    free(sub_info);

    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

class timeoutReliablityTest005 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("modifyCfg.sh \"udfEnable=1\" \"datalogTimeoutScheduleInterval=100\" \"datalogQueueNum=1\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
        system("modifyCfg.sh recover");
        system("rm -rf ./planStrFile/*");
    }

public:
    virtual void SetUp()
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

        // 创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建连接
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        // 断开同步连接
        int ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};
// 003.过期记录已经在流控队列中，流控队列堵塞（不一定要堵塞）
// 过期记录已经放到流控队列里，还没来得及删除，定时任务又扫描了一次，又把过期记录放到了队列里，预期重复删除
// 把定时任务改小，100ms查询一次；定时任务周期要比流控队列处理的快
// 503迭代三流控队列删除
TEST_F(timeoutReliablityTest005, DISABLED_DataLog_011_007_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_003";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    int64_t data[5] = {};
    char labelName[LABEL_NAME] = "A";
    // 预置数据
    for (int64_t i = 1; i <= 2000; i++) {
        data[0] = i;
        data[1] = i;
        data[2] = i;
        data[3] = -i;
        data[4] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setPkValue(g_stmt, data);
        setOtherValue(g_stmt, data);
        setTimeoutValue(g_stmt, data, currentTime);
        // 通过Datalog流控队列执行DML操作
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 5));
    }
    usleep(10000);
    // 比较难构造
    // 快要堵塞的时候，把过期记录放进去，然后再触发一次定时任务，构造重复扫描
    // 查询可以看到有些记录被重复删除了，count为负数，但是无法校验
    sleep(DELAY_TIME);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004.多个过期表，多回调函数
TEST_F(timeoutReliablityTest, DataLog_011_007_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_004";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readNS02A01[recordNum][5] = {{3, 6, 6, 19, -1}, {3, 3, 9, 18, 1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readNS03A01[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");
    int64_t readNs02B02[recordNum][5] = {{3, 6, 6, 19, -1}, {3, 3, 9, 18, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs02B, READ, readNs02B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");
    int64_t readNs03B02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, -1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs03B, READ, readNs03B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005.多过期表，其中一个回调函数失败，验证其他过期表不受影响
TEST_F(timeoutReliablityTest, DataLog_011_007_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_005";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);
    // ns_02.A的回调失败，预期其余表不受影响

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readNS02A01[recordNum - 2][5] = {{1, 2, 2, -1, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");
    int64_t readNs02B02[recordNum - 2][5] = {{1, 2, 2, -1, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02B, READ, readNs02B02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");
    int64_t readNs03B02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03B, READ, readNs03B02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006.1024个过期表
// 迭代八修改为最多1000个表
TEST_F(timeoutReliablityTest, DataLog_011_007_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
#ifndef ENV_RTOSV2X
    char soName[FILE_PATH] = "timeout_relibility_006";
    int tableNum = 1000;
#else
    char soName[FILE_PATH] = "timeout_relibility_006_iot";
    int tableNum = 200;
#endif

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    char labelName[LABEL_NAME] = {};
    for (int i = 1; i <= tableNum; i++) {
        sprintf(labelName, "ns%03dA", i);
        ret = testTable(g_conn, g_stmt, labelName, SINGLE_INSERT, data, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    for (int i = 1; i <= tableNum; i++) {
        sprintf(labelName, "ns%03dA", i);
        if (i == 1) {
            int64_t readDataA01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
            ret = testTable(g_conn, g_stmt, labelName, READ, readDataA01, recordNum, currentTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            int64_t readDataA02[recordNum - 2][5] = {{2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
            ret = testTable(g_conn, g_stmt, labelName, READ, readDataA02, recordNum - 2, currentTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.100万过期记录
TEST_F(timeoutReliablityTest, DataLog_011_007_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_007";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 1000;
#ifdef ENV_RTOSV2X
    recordNum = 100;
#endif
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    int64_t data[5] = {};
    char labelName[LABEL_NAME] = "A";
    int64_t i = 1;
    for (i = 1; i < recordNum + 1; i++) {
        // 预置数据
        data[0] = i;
        data[1] = i;
        data[2] = i;
        data[3] = -i;
        data[4] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setPkValue(g_stmt, data);
        setOtherValue(g_stmt, data);
        setTimeoutValue(g_stmt, data, currentTime);
        // 通过Datalog流控队列执行DML操作
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 10));

        if (i % 5000 == 0) {
            AW_FUN_Log(LOG_STEP, "till now:insert records %d\n", i);
        }
    }
    AW_FUN_Log(LOG_STEP, "actul insert records is:%d\n", i);
    
    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readDataA[0][5] = {};
    ret = testTable(g_conn, g_stmt, labelName, READ, readDataA, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
class timeoutReliablityTest002 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=8\"");  // 内存大小改小，减少单个用例执行时间
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
        system("modifyCfg.sh recover");
        system("rm -rf ./planStrFile/*");
    }

public:
    virtual void SetUp()
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

        // 创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建连接
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        // 断开同步连接
        int ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 008.过期表写数据至内存满，写满后删除，再次写满内存
TEST_F(timeoutReliablityTest002, DataLog_011_007_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_007";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    // 删除部分数据
    // 再次写入数据到内存满
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

class timeoutReliablityTest006 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("modifyCfg.sh \"datalogQueueNum=1\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
        system("modifyCfg.sh recover");
    }

public:
    virtual void SetUp()
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

        // 创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建连接
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_CHECK_LOG_BEGIN();
    }
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        // 断开同步连接
        int ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 009.流控队列内存满，写入流控队列的过期记录删除成功，未写入的删除失败
// 503迭代三流控队列删除
TEST_F(timeoutReliablityTest006, DISABLED_DataLog_011_007_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_009";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    int64_t i = 1;
    int64_t data[5] = {};
    char labelName[LABEL_NAME] = "A";
    // 加入重试机制后，队列满不会报GMERR_PROGRAM_LIMIT_EXCEEDED，使用视图中的重试字段判断是否队列满
    char command[1024];
    int retryTimeB, retryTimeE;
    (void)snprintf(command, sizeof(command), "gmsysview -q %s |grep %s |awk -F '[:]' '{print $2}' |sed 's/ //g'",
        "V\\$PTL_DATALOG_QUEUE", "INPUT_QUEUE_PUSH_RETRY_TIMES");
    (void)TestGetResultCommand(command, &retryTimeB);
    // 开始写数据
    AW_FUN_Log(LOG_STEP, "insert start.");
    while ((void)TestGetResultCommand(command, &retryTimeE), retryTimeB == retryTimeE) {
        // 预置数据
        for (int loop = 0; loop < 1000; loop++) {
            data[0] = i;
            data[1] = i;
            data[2] = i;
            if (i % 9 == 0) {
                data[3] = -i;
            } else {
                data[3] = i;
            }
            data[4] = i;
            ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            setPkValue(g_stmt, data);
            setOtherValue(g_stmt, data);
            setTimeoutValue(g_stmt, data, currentTime);
            // 通过Datalog流控队列执行DML操作
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if ((i % 10000 == 0) && PRINT_INFO) {
                AW_FUN_Log(LOG_STEP, "till now:insert records %d\n", i);
            }
            i++;
        }
    }
    AW_FUN_Log(LOG_STEP, "actul insert records is:%d\n", i);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    sleep(DELAY_TIME);

    // 无法主键读，使用写验证；写相同数据，预期成功
    // 读第一条数据，因为删除成功，预期读不到
    int64_t dataA01[1][5] = {{9, 9, 9, -9, 9}};
    ret = testTable(g_conn, g_stmt, labelName, SINGLE_INSERT, dataA01, 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读最后一条，因为写入失败，预期读不到
    int64_t dataA02[5] = {};
    dataA02[0] = i;
    dataA02[1] = i;
    dataA02[2] = i;
    dataA02[3] = i;
    dataA02[4] = -i;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    setPkValue(g_stmt, data);
    setOtherValue(g_stmt, data);
    setTimeoutValue(g_stmt, data, currentTime);
    // 通过Datalog流控队列执行DML操作
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 1));

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
class timeoutReliablityTest003 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 默认走非流控队列
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
        system("rm -rf ./planStrFile/*");
    }

public:
    virtual void SetUp()
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

        // 创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建连接
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        // 断开同步连接
        int ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};
// 010.不开流控队列，连接线程执行udf，udf函数内部sleep
TEST_F(timeoutReliablityTest003, DataLog_011_007_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_010";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    int64_t currentTime = 0;

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    // 插入数据执行GmcExecute接口，不走流控队列
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT_NO_QUEUE, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    // udf内部超时，运行正常
    char tableNs01B[LABEL_NAME] = "B";
    int64_t readNs01B01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
class timeoutReliablityTest004 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("modifyCfg.sh \"udfEnable=1\" \"workerHungThreshold=6,200,300\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
        system("modifyCfg.sh recover");
        system("rm -rf ./planStrFile/*");
    }

public:
    virtual void SetUp()
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

        // 创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建连接
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        // 断开同步连接
        int ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};
// 011.开流控队列，表记录过期守护线程执行udf，udf函数内部sleep
TEST_F(timeoutReliablityTest004, DataLog_011_007_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_011";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    
    sleep(1);

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(6);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // 回调函数超时，运行正常
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
void *threadWriteTableAtest(void *args)
{
    int conn_id = *((int *)args);
    int ret = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id], GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t currentTime;
    GetTimeMs(&currentTime);

    int64_t data[5] = {};
    for (int64_t i = 1; i < 10; i++) {
        // 预置数据
        data[0] = i;
        data[1] = i;
        data[2] = i;
        data[3] = -i;
        data[4] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], "A", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setPkValue(g_stmt_tht[conn_id], data);
        setOtherValue(g_stmt_tht[conn_id], data);
        setTimeoutValue(g_stmt_tht[conn_id], data, currentTime);
        // 通过Datalog流控队列执行DML操作
        ret = GmcExecute(g_stmt_tht[conn_id]);
        if (ret != GMERR_OK) {
            printf("%d\n", ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 5));
    }

    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

void *threadWriteTableBtest(void *args)
{
    int conn_id = *((int *)args);
    int ret = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id], GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t currentTime;
    GetTimeMs(&currentTime);

    int64_t data[5] = {};
    for (int64_t i = 1; i < 10; i++) {
        // 预置数据
        data[0] = i;
        data[1] = i;
        data[2] = i;
        data[3] = -i;
        data[4] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], "B", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setPkValue(g_stmt_tht[conn_id], data);
        setOtherValue(g_stmt_tht[conn_id], data);
        setTimeoutValue(g_stmt_tht[conn_id], data, currentTime);
        // 通过Datalog流控队列执行DML操作
        ret = GmcExecute(g_stmt_tht[conn_id]);
        if (ret != GMERR_OK) {
            printf("%d\n", ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 5));
    }

    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

// 012.并发场景：线程1：写timeout表1；线程2：写timeout表1
TEST_F(timeoutReliablityTest, DataLog_011_007_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_012";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 多线程并发
    pthread_t thr_arr[2];
    void *thr_ret[2];
    int index[2] = {0, 1};

    int ret = pthread_create(&thr_arr[0], NULL, threadWriteTableAtest, (void *)&index[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, threadWriteTableAtest, (void *)&index[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thr_arr[1], &thr_ret[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.并发场景：线程1：写timeout表1；线程2：写timeout表2
TEST_F(timeoutReliablityTest, DataLog_011_007_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_012";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 多线程并发
    pthread_t thr_arr[2];
    void *thr_ret[2];
    int index[2] = {0, 1};

    int ret = pthread_create(&thr_arr[0], NULL, threadWriteTableAtest, (void *)&index[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, threadWriteTableBtest, (void *)&index[1]);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(thr_arr[0], &thr_ret[0]);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(thr_arr[1], &thr_ret[1]);
    EXPECT_EQ(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014.返回的length小于malloc的大小
TEST_F(timeoutReliablityTest, DataLog_011_007_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_014";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // 回调函数报错，输入输出表保持原纪录不变
    int64_t readNS01A01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.返回的length正确，malloc的大小大于length
TEST_F(timeoutReliablityTest, DataLog_011_007_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_015";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // 回调函数不报错，过期记录删除成功
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.返回的length大于malloc的大小
TEST_F(timeoutReliablityTest, DataLog_011_007_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_016";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // 回调函数报错，输入输出表保持原纪录不变
    int64_t readNS01A01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.普通timeout表，变长字段len为0
TEST_F(timeoutReliablityTest, DataLog_011_007_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_017";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = singleWriteTableAllStr(g_conn, g_stmt, tableNs01A, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // 回调函数报错，输入输出表保持原纪录不变
    int64_t readNS01A01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = readTableAllStr(g_conn, g_stmt, tableNs01A, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = readTableAllStr(g_conn, g_stmt, tableNs01B, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.全量可更新timeout表，变长字段len为0
TEST_F(timeoutReliablityTest, DataLog_011_007_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_018";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = singleWriteTableAllStr(g_conn, g_stmt, tableNs01A, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // 回调函数报错，输入输出表保持原纪录不变
    int64_t readNS01A01[recordNum - 1][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, 1}};
    ret = readTableAllStr(g_conn, g_stmt, tableNs01A, readNS01A01, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum - 1][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, 1}};
    ret = readTableAllStr(g_conn, g_stmt, tableNs01B, readNs01B02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.部分可更新timeout表，变长字段len为0
TEST_F(timeoutReliablityTest, DataLog_011_007_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_019";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = singleWriteTableAllStr(g_conn, g_stmt, tableNs01A, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // 回调函数报错，输入输出表保持原纪录不变
    int64_t readNS01A01[recordNum - 1][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, 1}};
    ret = readTableAllStr(g_conn, g_stmt, tableNs01A, readNS01A01, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum - 1][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, 1}};
    ret = readTableAllStr(g_conn, g_stmt, tableNs01B, readNs01B02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.部分可更新timeout表，忽略字段的len为0
TEST_F(timeoutReliablityTest, DataLog_011_007_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char outputFilePath[FILE_PATH] = "./timeoutFile/reliability";
    char soName[FILE_PATH] = "timeout_relibility_020";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A"; 
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, 2, 1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = singleWriteTableAllStr(g_conn, g_stmt, tableNs01A, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // 回调函数不报错,过期记录删除成功，写入新数据 1, 1, x, 9, 1,这条数据会更新表中记录
    int64_t readNS01A01[recordNum - 2][5] = {{1, 1, 3, 9, 1}, {2, 1, 1, 1, 1}};
    ret = readTableAllStr(g_conn, g_stmt, tableNs01A, readNS01A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum - 2][5] = {{1, 1, 3, 9, 1}, {2, 1, 1, 1, 1}};
    ret = readTableAllStr(g_conn, g_stmt, tableNs01B, readNs01B02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
