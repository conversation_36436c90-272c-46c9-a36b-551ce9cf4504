/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-10-18
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} A;
typedef struct B {
    uint8_t propeNum;
    uint8_t *nullInfo;
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} B;
#pragma pack(0)

int32_t dtl_timeout_callback_ns_01_A(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
    GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录
    A *input = (A*)timeoutTuple;
    A *output = GmUdfMemAlloc(ctx, sizeof(A));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a * 2;
    output->b = input->b * 2;
    output->c = input->c * 2;
    output->d = input->d + (int64_t)(10 * 60 * 60 * 1000);
    output->dtlReservedCount = 2;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(A);

    return GMERR_OK;
}

int32_t dtl_timeout_callback_ns_02_A(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
    GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录
    A *input = (A*)timeoutTuple;
    A *output = GmUdfMemAlloc(ctx, sizeof(A));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a * 3;
    output->b = input->b * 3;
    output->c = input->c * 3;
    output->d = input->d + (int64_t)(20 * 60 * 60 * 1000);
    output->dtlReservedCount = -input->dtlReservedCount;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(A);

    return GMERR_OK;
}

int32_t dtl_timeout_callback_ns_03_A(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
    GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录
    B *input = (B*)timeoutTuple;
    B *output = GmUdfMemAlloc(ctx, sizeof(B));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    if (input->propeNum != 6) {
        return GMERR_DATA_EXCEPTION;
    }

    output->propeNum = input->propeNum;
    output->nullInfo = GmUdfMemAlloc(ctx, sizeof(uint8_t) * (output->propeNum));
    if (output->nullInfo == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    for (int i = 0; i < output->propeNum; i++) {
        output->nullInfo[i] = 1;
    }
    output->a = input->a * 2;
    output->b = input->b * 2;
    output->c = input->c * 2;
    output->d = input->d + (int64_t)(10 * 60 * 60 * 1000);
    output->dtlReservedCount = -input->dtlReservedCount;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(B);

    return GMERR_OK;
}
