/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : GMDBV502 迭代三 表记录过期
 Notes        : 本文件主要是综合场景测试(可更新表中：返回记录更新原有记录、返回记录是新纪录、插入记录count为负)
 History      :
 Author       : jiangshan/j00811785
 Modification : [2022.10.24]
*****************************************************************************/
#include "timeoutCommon.h"

class timeoutComplexFuncTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp()
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

        // 创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建连接
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_CHECK_LOG_BEGIN();
    }
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        // 断开同步连接
        int ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 针对tbm回滚，不检查日志
class timeoutComplexFuncTest1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp()
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

        // 创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建连接
        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_CHECK_LOG_BEGIN(0);
    }
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        // 断开同步连接
        int ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// DTS2022102909783
// 001.左表普通表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_001";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readNS02A01[recordNum][5] = {{3, 6, 6, 19, -1}, {3, 3, 9, 18, 1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readNS03A01[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");
    int64_t readNs02B02[recordNum][5] = {{3, 6, 6, 19, -1}, {3, 3, 9, 18, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs02B, READ, readNs02B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");
    int64_t readNs03B02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, -1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs03B, READ, readNs03B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.左表transient field表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_002";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    
    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, -2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读中间表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, -2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 1][5] = {{3, 6, 6, 19, 1}, {3, 3, 9, 18, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读中间表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][6] = {{2, 4, 4, 9, 0, 1}, {2, 2, 6, 8, 0, 1}, {2, 1, 1, 1, 0, -1},
        {2, 2, 2, 1, 0, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs01B, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02B02[recordNum - 1][6] = {{3, 6, 6, 19, 0, 1}, {3, 3, 9, 18, 0, 1}, {2, 2, 2, 1, 0, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs02B, readNs02B02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03B02[recordNum - 2][6] = {{2, 4, 4, 9, 0, 1}, {2, 2, 6, 8, 0, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs03B, readNs03B02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum][6] = {{2, 4, 4, 9, 0, 1}, {2, 2, 6, 8, 0, 1}, {2, 1, 1, 1, 0, -1},
    {2, 2, 2, 1, 0, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs01C, readNs01C02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[recordNum - 1][6] = {{3, 6, 6, 19, 0, 1}, {3, 3, 9, 18, 0, 1}, {2, 2, 2, 1, 0, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs02C, readNs02C02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum - 2][6] = {{2, 4, 4, 9, 0, 1}, {2, 2, 6, 8, 0, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs03C, readNs03C02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003.左表transient tuple表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_003";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, -2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读中间表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, -2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 1][5] = {{3, 6, 6, 19, 1}, {3, 3, 9, 18, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读中间表------------------------------------------------------- */
    int64_t readNs01B02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNs01B02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02B02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs02B, STRUCT_READ, readNs02B02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03B02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs03B, READ, readNs03B02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read midtable end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}, {2, 1, 1, 1, -1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[recordNum - 1][5] = {{3, 6, 6, 19, 1}, {3, 3, 9, 18, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, STRUCT_READ, readNs02C02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.左表固定资源表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_004";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, SINGLE_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    // count为负，资源表merge失败，事务回滚，输入表数据也会插入失败

    /*-----------------------------------------读中间表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A02[recordNum - 1][5] = {{2, 4, 4, 9, 2}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A02[recordNum - 1][5] = {{3, 6, 6, 19, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A02[recordNum - 1][5] = {{2, 4, 4, 9, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读中间表------------------------------------------------------- */
    // 这里资源分配不固定
    int64_t readNs01B02[recordNum - 1][6] = {{2, 4, 4, 9, 0, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 2, 1}};
    int64_t readNs01B03[recordNum - 1][6] = {{2, 4, 4, 9, 3, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 2, 1}};
    int64_t readNs01B04[recordNum - 1][6] = {{2, 4, 4, 9, 1, 1}, {1, 1, 3, 2, 0, 1}, {2, 2, 2, 1, 2, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs01B, readNs01B02, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = readOutTable(g_conn, g_stmt, tableNs01B, readNs01B03, recordNum - 1, currentTime);
        if (ret != GMERR_OK) {
            ret = readOutTable(g_conn, g_stmt, tableNs01B, readNs01B04, recordNum - 1, currentTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    int64_t readNs02B02[recordNum - 1][6] = {{3, 6, 6, 19, 0, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 2, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs02B, readNs02B02, recordNum - 1, currentTime);
    if (ret != 0) {
        int64_t readNs02B03[recordNum - 1][6] = {{3, 6, 6, 19, 3, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 2, 1}};
        int64_t readNs02B04[recordNum - 1][6] = {{3, 6, 6, 19, 1, 1}, {1, 1, 3, 2, 0, 1}, {2, 2, 2, 1, 2, 1}};
        ret = readOutTable(g_conn, g_stmt, tableNs02B, readNs02B03, recordNum - 1, currentTime);
        if (ret != GMERR_OK) {
            ret = readOutTable(g_conn, g_stmt, tableNs02B, readNs02B04, recordNum - 1, currentTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    int64_t readNs03B02[recordNum - 1][6] = {{2, 4, 4, 9, 0, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 2, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs03B, readNs03B02, recordNum - 1, currentTime);
    if (ret != 0) {
        int64_t readNs03B03[recordNum - 1][6] = {{2, 4, 4, 9, 3, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 2, 1}};
        int64_t readNs03B04[recordNum - 1][6] = {{2, 4, 4, 9, 1, 1}, {1, 1, 3, 2, 0, 1}, {2, 2, 2, 1, 2, 1}};
        ret = readOutTable(g_conn, g_stmt, tableNs03B, readNs03B03, recordNum - 1, currentTime);
        if (ret != GMERR_OK) {
            ret = readOutTable(g_conn, g_stmt, tableNs03B, readNs03B04, recordNum - 1, currentTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "read midtable end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum - 1][5] = {{2, 4, 4, 9, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[recordNum - 1][5] = {{3, 6, 6, 19, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, STRUCT_READ, readNs02C02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum - 1][5] = {{2, 4, 4, 9, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2022110211877
// 005.左表pubsub资源表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_005";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    // 订阅推送的记录
    C4Int8C1Int4T obj_out01[6] = {{1, 2, 2, -1, 1, 1}, {1, 2, 2, -1, 1, -1}, {2, 4, 4, 9, 1, 1},
        {1, 1, 3, 2, 1, 1}, {2, 1, 1, 1, 1, 1}, {2, 2, 2, 1, 1, 1}};
    C4Int8C1Int4T obj_out02[6] = {{1, 2, 2, -1, 1, 1}, {1, 2, 2, -1, 1, -1}, {3, 6, 6, 19, 1, 1},
        {1, 1, 3, 2, 1, 1}, {2, 1, 1, 1, 1, 1}, {2, 2, 2, 1, 1, 1}};
    C4Int8C1Int4T obj_out03[6] = {{1, 2, 2, -1, 1, 1}, {1, 2, 2, -1, 1, -1}, {2, 4, 4, 9, 1, 1},
        {1, 1, 3, 2, 1, 1}, {2, 1, 1, 1, 1, 1}, {2, 2, 2, 1, 1, 1}};

    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    const char *subName01 = "subVertexLabelNs01B";
    const char *subName02 = "subVertexLabelNs02B";
    const char *subName03 = "subVertexLabelNs03B";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub资源表ns_01.B
    char *sub_info01 = NULL;
    readJanssonFile("./schemaFile/subInfoNs01B.json", &sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C4Int8C1Int4RescGet;
    userData01->objLen = 6;
    userData01->obj = obj_out01;
    userData01->isResourcePubSub = true;
    userData01->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallbackPubSub, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系, pubsub资源表ns_02.B
    char *sub_info02 = NULL;
    readJanssonFile("./schemaFile/subInfoNs02B.json", &sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C4Int8C1Int4RescGet;
    userData02->objLen = 6;
    userData02->obj = obj_out02;
    userData02->isResourcePubSub = true;
    userData02->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallbackPubSub, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 创建订阅关系, pubsub资源表ns_03.B
    char *sub_info03 = NULL;
    readJanssonFile("./schemaFile/subInfoNs03B.json", &sub_info03);
    GmcSubConfigT tmp_sub_info03;
    tmp_sub_info03.subsName = subName03;
    tmp_sub_info03.configJson = sub_info03;
    SnUserDataWithFuncT *userData03 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData03->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData03->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData03->readResFunc = C4Int8C1Int4RescGet;
    userData03->objLen = 6;
    userData03->obj = obj_out03;
    userData03->isResourcePubSub = true;
    userData03->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info03, conn_sn_sync, snCallbackPubSub, userData03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info03);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, 2, 1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, SINGLE_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*-----------------------------------------读输入表------------------------------------------------------- */
    // count为负，资源表merge失败，事务回滚，输入表数据也会插入失败

    /*-----------------------------------------读中间表------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);
    

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A02[recordNum][5] = {{2, 4, 4, 9, 2}, {1, 1, 3, 2, 1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A02[recordNum][5] = {{3, 6, 6, 19, 1}, {1, 1, 3, 2, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A02[recordNum][5] = {{2, 4, 4, 9, 1}, {1, 1, 3, 2, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读中间表------------------------------------------------------- */
    int64_t readNs01B02[recordNum][6] = {{2, 4, 4, 9, 1, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 1, 1},
        {2, 1, 1, 1, 1, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs01B, readNs01B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02B02[recordNum][6] = {{3, 6, 6, 19, 1, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 1, 1},
        {2, 1, 1, 1, 1, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs02B, readNs02B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03B02[recordNum][6] = {{2, 4, 4, 9, 1, 1}, {1, 1, 3, 2, 1, 1}, {2, 2, 2, 1, 1, 1},
        {2, 1, 1, 1, 1, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs03B, readNs03B02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read midtable end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum][5] = {{2, 4, 4, 9, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[recordNum][5] = {{3, 6, 6, 19, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, STRUCT_READ, readNs02C02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum][5] = {{2, 4, 4, 9, 1}, {1, 1, 3, 2, 1}, {2, 2, 2, 1, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);
    free(userData03->data);
    free(userData03);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.左表HPR表,单写输出不稳定，改成批量写入（看成一个事务）

// 007.左表null表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_007";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, -2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, -2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readNS02A01[recordNum - 1][5] = {{3, 6, 6, 19, 1}, {3, 3, 9, 18, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 2, 6, 8, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.超时表（普通表）包含常量（非过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_008";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_01.A";
    char tableB[LABEL_NAME] = "ns_01.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据
    int64_t readA[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readB02[recordNum - 3][5] = {{2, 2, 10, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, recordNum - 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.超时表（普通表）包含常量（过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_009";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_01.A";
    char tableB[LABEL_NAME] = "ns_01.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime = 0;
    // currentTime为0，.d中的32400000相当于是9
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, -9, 2}, {2, 2, 2, -9, -2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据
    int64_t readA[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readB02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.超时表（普通表）包含忽略字段（非过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_010";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_01.A";
    char tableB[LABEL_NAME] = "ns_01.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 100, 2}, {2, 2, 2, 8, 1}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据
    int64_t readA[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 100, 2}, {2, 2, 2, 8, 1}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readB02[recordNum - 1][5] = {{2, 4, 10, 9, 1}, {2, 2, 10, 8, 2}, {2, 1, 10, 100, 1}};
    int64_t readB03[recordNum][5] = {{2, 4, 10, 9, 1}, {2, 2, 10, 8, 1}, {2, 2, 10, 8, 1}, {2, 1, 10, 100, 1}};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableB, READ, readB03, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.超时表（普通表）包含忽略字段（过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_011";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_01.A";
    char tableB[LABEL_NAME] = "ns_01.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime = 0;
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, -1, 2}, {2, 2, 2, -1, -2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据
    int64_t readA[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second read end.");
    int64_t readB02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "third read end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012.超时表（全量可更新表）包含常量（非过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_012";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_02.A";
    char tableB[LABEL_NAME] = "ns_02.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {3, 3, 2, 100, 2}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据,这里主键相同，会更新
    int64_t readA02[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {3, 3, 9, 18, 1}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second read end.");
    int64_t readB02[recordNum - 3][5] = {{3, 3, 10, 18, 1}};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, recordNum - 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "third read end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013.超时表（全量可更新表）包含常量（过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_013";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_02.A";
    char tableB[LABEL_NAME] = "ns_02.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime = 0;
    // B(a, b, c, 10):- A(a, b, c, 199).
    // IOT设备写入的数据
    int64_t data1[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, -19, 2}, {2, 2, 2, -2, 2}};
    // Euler写入的数据
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 19, 2}, {2, 2, 2, 1, 2}};

    // 输出数据不稳定，可能过期一次，也可能过期两次
    // 在udf文件中加判断，当A表过期超过2次，使其结果与过期1次结果保持一致
    int64_t readA02[recordNum][5] = {{3, 6, 6, 199, 1}, {3, 3, 9, 198, 1}, {2, 1, 1, 19, 1}, {2, 2, 2, 1, 1}};
    int64_t readA03[recordNum][5] = {{3, 6, 6, 199, 1}, {3, 3, 9, 198, 1}, {6, 3, 3, 181, 1}, {6, 6, 6, 198, 1}};
    int64_t readB02[1][5] = {0};

#if defined (ENV_RTOSV2X) ||defined(ENV_RTOSV2)
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data1, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    ret = testTable(g_conn, g_stmt, tableA, READ, readA03, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014.超时表（全量可更新表）包含忽略字段（非过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_014";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_02.A";
    char tableB[LABEL_NAME] = "ns_02.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 100, 2}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据
    int64_t readA02[recordNum - 1][5] = {{3, 6, 6, 19, 1}, {3, 3, 9, 18, 1}, {2, 1, 1, 100, 1}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readB02[recordNum - 1][5] = {{3, 6, 10, 19, 1}, {3, 3, 10, 18, 1}, {2, 1, 10, 100, 1}};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015.超时表（全量可更新表）包含忽略字段（过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_015";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_02.A";
    char tableB[LABEL_NAME] = "ns_02.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime = 0;
    // B(a, b, c, 10):- A(a, b, c, -).
    // IOT设备写入的数据
    int64_t data1[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, -19, 2}, {2, 2, 2, -2, 2}};
    // Euler写入的数据
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 19, 2}, {2, 2, 2, 1, 2}};

    // 输出数据不稳定，可能过期一次，也可能过期两次
    // 在udf文件中加判断，当A表过期超过2次，使其结果与过期1次结果保持一致
    int64_t readA02[recordNum][5] = {{3, 6, 6, 199, 1}, {3, 3, 9, 198, 1}, {2, 1, 1, 19, 1}, {2, 2, 2, 1, 1}};
    int64_t readA03[recordNum][5] = {{3, 6, 6, 199, 1}, {3, 3, 9, 198, 1}, {6, 3, 3, 181, 1}, {6, 6, 6, 198, 1}};
    int64_t readB02[recordNum][5] = {{3, 6, 6, 10, 1}, {3, 3, 9, 10, 1}, {2, 1, 1, 10, 1}, {2, 2, 2, 1, 1}};
    int64_t readB03[recordNum][5] = {{3, 6, 6, 10, 1}, {3, 3, 9, 10, 1}, {6, 3, 3, 10, 1}, {6, 6, 6, 10, 1}};

#if defined (ENV_RTOSV2X) ||defined(ENV_RTOSV2)
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data1, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    ret = testTable(g_conn, g_stmt, tableA, READ, readA03, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableB, READ, readB03, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.超时表（部分可更新表）包含常量（非过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_016";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_03.A";
    char tableB[LABEL_NAME] = "ns_03.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 10, 2}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据
    int64_t readA02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 10, 1}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readB02[recordNum - 3][5] = {{2, 4, 5, 9, 1}};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, recordNum - 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.超时表（部分可更新表）包含常量（过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_017";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_03.A";
    char tableB[LABEL_NAME] = "ns_03.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime = 0;
    // B(a, b, c, 1):- A(a, b, c, 1). B(a, b, c, 1):- A(a, b, c, 8).
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {3, 6, 8, 10, 2}, {2, 2, 2, 1, 2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据,这里报冲突
    int64_t readA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {3, 6, 8, 10, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readB02[1][5] = {0};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018.超时表（部分可更新表）包含忽略字段（非过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_018";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_03.A";
    char tableB[LABEL_NAME] = "ns_03.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 10, 2}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据
    int64_t readA02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 10, 1}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readB02[recordNum - 2][5] = {{2, 4, 10, 9, 1}, {2, 1, 10, 10, 1}};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019.超时表（部分可更新表）包含忽略字段（过期字段）
TEST_F(timeoutComplexFuncTest, DataLog_011_006_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_019";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;    // 写表
    char tableA[LABEL_NAME] = "ns_03.A";
    char tableB[LABEL_NAME] = "ns_03.B";
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime = 0;
    // B(a, b, 10, 8):- A(a, b, -, -).
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 2, 1, -1, 2}, {2, 1, 2, -1, 2}};
    // 写数据
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待过期字段删除
    sleep(DELAY_TIME);
    // 读数据
    int64_t readA02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    int64_t readB02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableB, READ, readB02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read out end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.超时表join普通表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_020";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表B------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";
    int64_t dataB01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, SINGLE_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataB02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs03B, BATCH_STRUCT_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 4, 10, 20, 2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");
    // 获取ns_02.A表主键为3,6对应的d字段的值
    int64_t dvalue = 0;
    tableAPKScan(g_conn, g_stmt, tableNs02A, 3, 6, &dvalue);
    // 获取客户端时间
    int64_t clienttime = 0;
    ret = GetClientTimeMs(&clienttime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 需要减掉sleep的2s
    clienttime = clienttime + 19 * 60 * 60 * 1000 - 2000;
    int32_t diff = (clienttime - dvalue) / 1000;
    AW_FUN_Log(LOG_DEBUG, "dvalue:%ld, currenttime:%ld, diff:%d", dvalue, clienttime, diff);
    // 误差60s以内
    AW_MACRO_EXPECT_EQ_BOOL(true, diff < 60);
    // 写输入表ns_02.B
    int64_t dataB03[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {3, 6, 6, dvalue, -2}};
    ret = writeTableB(g_conn, g_stmt, tableNs02B, dataB03, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum - 2][5] = {{2, 2, 2, 1, 1}, {2, 4, 4, 9, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读ns_02.C表进行数据校验
    int64_t readNs02C02[recordNum - 3][5] = {{3, 6, 6, dvalue, -1}};
    ret = readTableC(g_conn, g_stmt, tableNs02C, readNs02C02, recordNum - 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum - 3][5] = {{2, 4, 4, 9, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum - 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.超时表join transient表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_021";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 4, 10, 20, 2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------写输入表B------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";
    int64_t dataB01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, SINGLE_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataB02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02B, BATCH_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03B, BATCH_STRUCT_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C01[recordNum - 2][5] = {{1, 2, 2, -1, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C01, recordNum - 2, currentTime);
    if (ret != GMERR_OK) {
        int64_t readNs01C02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 2, 2, 1, 1}};
        ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum - 2, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNs02C01[recordNum - 3][5] = {{1, 2, 2, -1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C01, recordNum - 3, currentTime);
    if (ret != GMERR_OK) {
        int64_t readNs02C02[recordNum - 3][5] = {{3, 6, 6, 19, -1}};
        ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C02, recordNum - 3, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNs03C01[recordNum - 3][5] = {{1, 2, 2, -1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C01, recordNum - 3, currentTime);
    if (ret != GMERR_OK) {
        int64_t readNs03C02[recordNum - 3][5] = {{2, 4, 4, 9, 1}};
        ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum - 3, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022.超时表join 资源表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_022";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表D------------------------------------------------------- */
    char tableNs01D[LABEL_NAME] = "ns_01.D";
    char tableNs02D[LABEL_NAME] = "ns_02.D";
    char tableNs03D[LABEL_NAME] = "ns_03.D";
    int64_t dataD01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01D, SINGLE_INSERT, dataD01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataD02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {3, 6, 6, 19, 2}};
    ret = testTable(g_conn, g_stmt, tableNs02D, SINGLE_INSERT, dataD02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03D, SINGLE_STRUCT_INSERT, dataD02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读中间表B------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 4, 10, 20, 2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum - 2][6] = {{2, 4, 4, 9, 2, 1}, {2, 2, 2, 1, 3, -1}};
    ret = readOutTable(g_conn, g_stmt, tableNs01C, readNs01C02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[recordNum - 3][6] = {{3, 6, 6, 19, 3, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs02C, readNs02C02, recordNum - 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum - 3][6] = {{2, 4, 4, 9, 2, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs03C, readNs03C02, recordNum - 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023.超时表join超时表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_023";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表B------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";
    int64_t dataB01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, SINGLE_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataB02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02B, BATCH_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03B, BATCH_STRUCT_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 4, 10, 20, 2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表B------------------------------------------------------- */
    int64_t readNS01B01[recordNum - 1][5] = {{2, 4, 4, 9, 4}, {2, 6, 6, 8, 2}, {2, 2, 2, 1, -2}};
    // 单写过期字段的值不一致导致，这边不校验过期字段
    int64_t readNS01B02[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 4, 4, 9, 2}, {2, 6, 6, 8, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNS01B01, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNS01B02, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNS02B01[recordNum - 1][5] = {{2, 4, 4, 9, 4}, {2, 6, 6, 8, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02B, STRUCT_READ, readNS02B01, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03B01[recordNum - 1][5] = {{2, 4, 4, 9, 4}, {2, 6, 6, 8, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs03B, READ, readNS03B01, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum - 2][5] = {{2, 2, 2, 1, 1}, {2, 4, 4, 9, 1}};
    int64_t readNs01C03[recordNum - 1][5] = {{2, 2, 2, 1, 1}, {2, 4, 4, 9, 1}, {2, 4, 4, 9, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum - 2, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C03, recordNum - 1, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNs02C02[recordNum - 3][5] = {{3, 6, 6, 19, -1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C02, recordNum - 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum - 3][5] = {{2, 4, 4, 9, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum - 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024.超时表join普通表join超时表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_024";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表B------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";
    int64_t dataB01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, SINGLE_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataB02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02B, BATCH_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03B, BATCH_STRUCT_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 4, 10, 20, 2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------写输入表C------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";
    ret = testTable(g_conn, g_stmt, tableNs01C, SINGLE_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02C, BATCH_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03C, BATCH_STRUCT_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01D[LABEL_NAME] = "ns_01.D";
    char tableNs02D[LABEL_NAME] = "ns_02.D";
    char tableNs03D[LABEL_NAME] = "ns_03.D";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输入表C------------------------------------------------------- */
    int64_t readNS01C01[recordNum - 1][5] = {{2, 4, 4, 9, 4}, {2, 6, 6, 8, 2}, {2, 2, 2, 1, -2}};
    int64_t readNS01C02[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 4, 4, 9, 2}, {2, 6, 6, 8, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNS01C01, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNS01C02, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNS02C01[recordNum - 1][5] = {{2, 6, 6, 8, 2}, {2, 4, 4, 9, 4}, {2, 2, 2, 1, -2}};
    int64_t readNS02C02[recordNum][5] = {{2, 6, 6, 8, 2}, {2, 4, 4, 9, 2}, {2, 4, 4, 9, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02C, STRUCT_READ, readNS02C01, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs02C, STRUCT_READ, readNS02C02, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNS03C01[recordNum - 1][5] = {{2, 6, 6, 8, 2}, {2, 4, 4, 9, 4}, {3, 6, 6, 19, -2}};
    int64_t readNS03C02[recordNum][5] = {{2, 6, 6, 8, 2}, {2, 4, 4, 9, 2}, {2, 4, 4, 9, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNS03C01, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNS03C02, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01D01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 2, 2, 1, -1}};
    int64_t readNs01D02[recordNum - 1][5] = {{2, 4, 4, 9, 1}, {2, 4, 4, 9, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01D, READ, readNs01D01, recordNum - 2, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs01D, READ, readNs01D02, recordNum - 1, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNs02D01[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs02D, READ, readNs02D01, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // join得到的记录有2种情况，系统过期字段的值不同
    int64_t readNs03D01[recordNum - 3][5] = {{2, 4, 4, 9, 1}};
    int64_t readNs03D02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 4, 4, 9, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03D, READ, readNs03D01, recordNum - 3, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs03D, READ, readNs03D02, recordNum - 2, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025.超时表join func
TEST_F(timeoutComplexFuncTest, DataLog_011_006_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_025";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 3, 2}, {1, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 4, 10, 20, 2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输入表A------------------------------------------------------- */

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A02[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 3, 2}, {1, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A02[recordNum - 2][5] = {{3, 3, 9, 18, 1}, {2, 1, 1, 3, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum][6] = {{2, 4, 4, 9, 2, 1}, {2, 2, 6, 8, 2, 1}, {2, 1, 1, 3, 2, 1}, {1, 2, 2, 1, 1, -1}};
    ret = readOutTable(g_conn, g_stmt, tableNs01C, readNs01C02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[recordNum - 2][6] = {{3, 3, 9, 18, 3, 1}, {2, 1, 1, 3, 2, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs02C, readNs02C02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum - 2][6] = {{2, 4, 4, 9, 2, 1}, {2, 1, 1, 1, 2, 1}};
    ret = readOutTable(g_conn, g_stmt, tableNs03C, readNs03C02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 026.超时表not join超时表
TEST_F(timeoutComplexFuncTest, DataLog_011_006_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_026";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表B------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";
    int64_t dataB01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, SINGLE_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataB02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 3, -2, 1}, {2, 4, 4, 9, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02B, BATCH_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03B, BATCH_STRUCT_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, -1}, {2, 1, 1, 1, 2}, {2, 4, 10, 20, 2}};
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表B------------------------------------------------------- */
    int64_t readNS01B01[recordNum - 1][5] = {{2, 4, 4, 9, 4}, {2, 6, 6, 8, 2}, {2, 2, 2, 1, -2}};
    int64_t readNS01B02[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 4, 4, 9, 2}, {2, 6, 6, 8, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNS01B01, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs01B, READ, readNS01B02, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNS02B01[recordNum - 1][5] = {{2, 4, 4, 9, 4}, {2, 6, 6, 8, 2}, {3, 6, 6, 19, -2}};
    int64_t readNS02B02[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 4, 4, 9, 2}, {2, 6, 6, 8, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs02B, STRUCT_READ, readNS02B01, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs02B, STRUCT_READ, readNS02B02, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNS03B01[recordNum - 1][5] = {{2, 4, 4, 9, 4}, {2, 6, 6, 8, 2}, {3, 6, 6, 19, -2}};
    int64_t readNS03B02[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 4, 4, 9, 2}, {2, 6, 6, 8, 2}, {3, 6, 6, 19, -2}};
    ret = testTable(g_conn, g_stmt, tableNs03B, READ, readNS03B01, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs03B, READ, readNS03B02, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A01[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 2, 6, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    // 过期字段的值，可能不同
    int64_t readNs01C02[recordNum - 2][5] = {{2, 1, 1, 1, 1}, {2, 2, 6, 8, 1}};
    int64_t readNs01C03[recordNum][5] = {{2, 1, 1, 1, 1}, {2, 2, 6, 8, 1}, {2, 4, 4, 9, 1}, {2, 2, 2, 1, -1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum - 2, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C03, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNs02C02[recordNum - 3][5] = {{2, 1, 1, 1, 1}};
    int64_t readNs02C03[recordNum - 2][5] = {{2, 1, 1, 1, 1}, {3, 6, 6, 19, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C02, recordNum - 3, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C03, recordNum - 2, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNs03C02[recordNum - 3][5] = {{2, 1, 1, 1, 1}};
    int64_t readNs03C03[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum - 3, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C03, recordNum - 2, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.超时表 GROUP-BY(非过期字段)  agg()
TEST_F(timeoutComplexFuncTest, DataLog_011_006_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_027";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime = 0;

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 2, 2, -2, -1}, {2, 1, 1, -1, 2}, {2, 2, 2, -1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataA02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {1, 3, 1, -1, 2}, {2, 4, 10, -20, 2}};
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A01[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A01, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2022110307331
// 028.超时表 GROUP-BY(过期字段)  agg()
TEST_F(timeoutComplexFuncTest, DataLog_011_006_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_028";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";
    int64_t dataA01[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 2, 2, -1, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, SINGLE_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs02A, BATCH_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03A, BATCH_STRUCT_INSERT, dataA01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输入表A------------------------------------------------------- */

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A02[recordNum][5] = {{2, 4, 4, 9, 2}, {4, 4, 4, 9, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A02[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    // 过期字段的值可能不同
    int64_t readNs01C02[recordNum - 1][5] = {{6, 4, 4, 9, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, 1}};
    int64_t readNs01C03[recordNum][5] = {{2, 4, 4, 9, 1}, {4, 4, 4, 9, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum - 1, currentTime);
    if (ret != GMERR_OK) {
        ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C03, recordNum, currentTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t readNs02C02[recordNum - 2][5] = {{3, 6, 6, 19, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum - 2][5] = {{2, 4, 4, 9, 1}, {2, 1, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.UDF写超时表(普通表/全量可更新表/部分可更新表)，产生过期记录
TEST_F(timeoutComplexFuncTest, DataLog_011_006_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_029";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表B------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";
    int64_t dataB01[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 2, 2, -2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, SINGLE_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataB02[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 3, 2, -2, 1}, {2, 6, 1, 1, 2}, {3, 6, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs02B, SINGLE_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03B, SINGLE_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A02[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 4, 4, 8, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A02[recordNum - 1][5] = {{3, 6, 6, 19, 1}, {3, 9, 6, 18, 1}, {2, 6, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A02[recordNum - 1][5] = {{2, 4, 4, 9, 1}, {2, 6, 4, 8, 1}, {3, 6, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A02, recordNum - 1, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    // 过期字段的值不同，目前无校验
    int64_t readNs01C02[recordNum * 2][5] = {{1, 2, 2, -1, 1}, {1, 2, 2, -2, -1}, {2, 1, 1, 1, 1}, {2, 1, 1, 1, 1},
        {2, 2, 2, 1, 1}, {2, 2, 2, 1, -1}, {2, 4, 4, 9, 1}, {2, 4, 4, 8, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum * 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[recordNum + 3][5] = {{1, 2, 2, -1, 1}, {1, 3, 2, -2, 1}, {2, 6, 1, 1, 1}, {2, 6, 1, 1, 1},
        {3, 6, 2, 1, 1}, {3, 6, 6, 19, 1}, {3, 9, 6, 18, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C02, recordNum + 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum + 3][5] = {{1, 2, 2, -1, 1}, {1, 3, 2, -2, 1}, {2, 6, 1, 1, 1}, {3, 6, 2, 1, 1},
        {3, 6, 2, 1, 1}, {2, 4, 4, 9, 1}, {2, 6, 4, 8, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum + 3, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.UDF写超时表(普通表/全量可更新表/部分可更新表)，不产生过期记录
TEST_F(timeoutComplexFuncTest, DataLog_011_006_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_030";
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    /*-----------------------------------------写输入表B------------------------------------------------------- */
    char tableNs01B[LABEL_NAME] = "ns_01.B";
    char tableNs02B[LABEL_NAME] = "ns_02.B";
    char tableNs03B[LABEL_NAME] = "ns_03.B";
    int64_t dataB01[recordNum][5] = {{1, 2, 2, 1, 1}, {1, 2, 2, 2, -1}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTable(g_conn, g_stmt, tableNs01B, SINGLE_INSERT, dataB01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataB02[recordNum][5] = {{1, 2, 2, 1, -1}, {1, 3, 2, 2, 1}, {2, 6, 1, 1, -2}, {3, 6, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs02B, SINGLE_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTable(g_conn, g_stmt, tableNs03B, SINGLE_INSERT, dataB02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    char tableNs01A[LABEL_NAME] = "ns_01.A";
    char tableNs02A[LABEL_NAME] = "ns_02.A";
    char tableNs03A[LABEL_NAME] = "ns_03.A";

    int64_t readNS01A01[recordNum][5] = {{1, 2, 2, 1, 2}, {1, 2, 2, 2, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A01[recordNum - 2][5] = {{2, 6, 1, 1, 1}, {3, 6, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, READ, readNS02A01, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A01[recordNum][5] = {{1, 2, 3, 1, 1}, {1, 3, 3, 2, 1}, {2, 6, 2, 1, 1}, {3, 6, 3, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A01, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    char tableNs01C[LABEL_NAME] = "ns_01.C";
    char tableNs02C[LABEL_NAME] = "ns_02.C";
    char tableNs03C[LABEL_NAME] = "ns_03.C";

    // d字段显示的值与真实值不同，先不校验
    int64_t readNs01C01[recordNum * 2][5] = {{1, 2, 2, 1, 1}, {1, 2, 2, 1, 1}, {1, 2, 2, 2, -1}, {1, 2, 2, 2, 1},
        {2, 1, 1, 1, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C01, recordNum * 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C01[recordNum + 2][5] = {{1, 2, 2, 1, -1}, {1, 3, 2, 2, 1}, {3, 6, 2, 1, 1}, {3, 6, 2, 1, 1},
        {2, 6, 1, 1, -1}, {2, 6, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C01, recordNum + 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C01[recordNum * 2][5] = {{1, 2, 2, 1, -1}, {1, 3, 2, 2, 1}, {2, 6, 1, 1, -1}, {3, 6, 2, 1, 1},
    {1, 2, 3, 1, 1}, {1, 3, 3, 2, 1}, {2, 6, 2, 1, 1}, {3, 6, 3, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C01, recordNum * 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    // 这里没有产生过期记录
    sleep(DELAY_TIME);

    /*-----------------------------------------读输入表A------------------------------------------------------- */
    int64_t readNS01A02[recordNum][5] = {{1, 2, 2, 1, 2}, {1, 2, 2, 2, 2}, {2, 1, 1, 1, 2}, {2, 2, 2, 1, 2}};
    ret = testTable(g_conn, g_stmt, tableNs01A, READ, readNS01A02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS02A02[recordNum - 2][5] = {{2, 6, 1, 1, 1}, {3, 6, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02A, STRUCT_READ, readNS02A02, recordNum - 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNS03A02[recordNum][5] = {{1, 2, 3, 1, 1}, {1, 3, 3, 2, 1}, {2, 6, 2, 1, 1}, {3, 6, 3, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03A, READ, readNS03A02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    /*-----------------------------------------读输出表------------------------------------------------------- */
    int64_t readNs01C02[recordNum*2][5] = {{1, 2, 2, 1, 1}, {1, 2, 2, 1, 1}, {1, 2, 2, 2, -1}, {1, 2, 2, 2, 1},
        {2, 1, 1, 1, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, -1}, {2, 2, 2, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs01C, READ, readNs01C02, recordNum * 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs02C02[recordNum + 2][5] = {{1, 2, 2, 1, -1}, {1, 3, 2, 2, 1}, {3, 6, 2, 1, 1}, {3, 6, 2, 1, 1},
        {2, 6, 1, 1, -1}, {2, 6, 1, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs02C, READ, readNs02C02, recordNum + 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t readNs03C02[recordNum * 2][5] = {{1, 2, 2, 1, -1}, {1, 3, 2, 2, 1}, {2, 6, 1, 1, -1}, {3, 6, 2, 1, 1},
    {1, 2, 3, 1, 1}, {1, 3, 3, 2, 1}, {2, 6, 2, 1, 1}, {3, 6, 3, 1, 1}};
    ret = testTable(g_conn, g_stmt, tableNs03C, READ, readNs03C02, recordNum * 2, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read output end.");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.普通过期表触发普通pubsub表,过期记录和产生的新记录相互抵消
TEST_F(timeoutComplexFuncTest, DataLog_011_006_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_031";
    char command[MAX_CMD_SIZE] = {0};
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 运行
    int ret = 0;
    int recordNum = 2;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    // 订阅推送的记录
    C4Int8T obj_out[recordNum] = {{1, 0, 1, 2, 2, -1}, {1, 0, 2, 1, 3, 2}};

    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    const char *subName = "subVertexLabel";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系
    char *sub_info = NULL;
    readJanssonFile("./schemaFile/subInfo031B.json", &sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData->readResFunc = C4Int8RescGet;
    userData->objLen = recordNum;
    userData->obj = obj_out;
    userData->isResourcePubSub = false;
    userData->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info, conn_sn_sync, snCallbackPubSub, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableA[LABEL_NAME] = "A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 3}, {2, 1, 3, 2, 2}};
    ret = testTable(g_conn, g_stmt, tableA, SINGLE_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview count -s %s -ns %s", g_toolPath, g_connServer, g_testNameSpace);
    system(command);

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(DELAY_TIME);
    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readA02[recordNum][5] = {{1, 2, 2, -1, 3}, {2, 1, 3, 2, 2}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA02, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "A", g_connServer, g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 3", "\"a\": 1", "\"b\": 2", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放分配的内存
    free(userData->data);
    free(userData);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.普通过期表触发多种类型的表，正常场景
TEST_F(timeoutComplexFuncTest, DataLog_011_006_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_032";
    char labelName_out[FILE_PATH] = "External";
    char command[MAX_CMD_SIZE] = {0};
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    // 运行
    int ret = 0;
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName_out);
    readJanssonFile("schemaFile/external_external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    
    const char *configJson = "{\"max_record_count\":10, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    // 订阅推送的记录
    C4Int8C1Int4T obj_out01[6] = {{1, 0, 1, 2, 2, -1, 1}, {-1, 0, 1, 2, 2, -1, 1}, {1, 0, 2, 4, 4, 9, 1},
        {1, 0, 2, 1, 3, 2, 1}, {1, 0, 3, 1, 1, 3, 1}, {1, 0, 4, 2, 2, 3, 1}};
    // 写入的记录 {1, 2, 2, -1, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}
    C4Int8T obj_out02[6] = {{1, 0, 1, 2, 2, -1}, {-1, 0, 1, 2, 2, -1}, {1, 0, 2, 4, 4, 9}, {1, 0, 2, 1, 3, 2},
        {1, 0, 3, 1, 1, 3}, {1, 0, 4, 2, 2, 3}};
    C4Int8T obj_out03[6] = {{1, 0, 1, 2, 2, -1}, {-1, 0, 1, 2, 2, -1}, {1, 0, 2, 4, 4, 9},
        {1, 0, 2, 1, 3, 2}, {1, 0, 3, 1, 1, 3}, {1, 0, 4, 2, 2, 3}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    const char *subName01 = "subVertexLabelB";
    const char *subName02 = "subVertexLabelC";
    const char *subName03 = "subVertexLabelD";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub资源表
    char *sub_info01 = NULL;
    readJanssonFile("./schemaFile/subInfo032B.json", &sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C4Int8C1Int4RescGet;
    userData01->objLen = 6;
    userData01->obj = obj_out01;
    userData01->isResourcePubSub = true;
    userData01->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallbackPubSub, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系，pubsub普通表
    char *sub_info02 = NULL;
    readJanssonFile("./schemaFile/subInfo032C.json", &sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C4Int8RescGet;
    userData02->objLen = 6;
    userData02->obj = obj_out02;
    userData02->isResourcePubSub = false;
    userData02->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallbackPubSub, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 创建订阅关系，pubsub普通更新表
    char *sub_info03 = NULL;
    readJanssonFile("./schemaFile/subInfo032D.json", &sub_info03);
    GmcSubConfigT tmp_sub_info03;
    tmp_sub_info03.subsName = subName03;
    tmp_sub_info03.configJson = sub_info03;
    SnUserDataWithFuncT *userData03 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData03->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData03->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData03->readResFunc = C4Int8RescGet;
    userData03->objLen = 6;
    userData03->obj = obj_out03;
    userData03->isResourcePubSub = false;
    userData03->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info03, conn_sn_sync, snCallbackPubSub, userData03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info03);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableA[LABEL_NAME] = "A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}};
    // 批写4条数据
    ret = testTable(g_conn, g_stmt, tableA, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(4);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readA[recordNum][5] = {{2, 4, 4, 9, 2}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    // 读pubsub资源表B
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "B", g_connServer, g_testNameSpace);
    system(command);
    ret =
        executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4", "\"e\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读External表
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "External", g_connServer,
        g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读普通表
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "H", g_connServer, g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 可更新表
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "G", g_connServer, g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);
    free(userData03->data);
    free(userData03);

    ret = GmcDropVertexLabel(g_stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.普通过期表触发多种类型的表，TBM插入失败回滚
TEST_F(timeoutComplexFuncTest1, DataLog_011_006_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_033";
    char labelName_out[FILE_PATH] = "External";
    char command[MAX_CMD_SIZE] = {0};
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    // 运行
    int ret = 0;
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName_out);
    readJanssonFile("schemaFile/external_external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    
    const char *configJson = "{\"max_record_count\":10, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    // 订阅推送的记录以及回滚推送
    C4Int8C1Int4T obj_out01[8] = {{1, 0, 1, 2, 2, -1, 1}, {1, 0, 2, 1, 3, 2, 1}, {1, 0, 3, 1, 1, 3, 1},
        {1, 0, 4, 2, 2, 3, 1}, {-1, 0, 1, 2, 2, -1, 1}, {-1, 0, 2, 1, 3, 2, 1}, {-1, 0, 3, 1, 1, 3, 1},
        {-1, 0, 4, 2, 2, 3, 1}};
    // 写入的记录 {1, 2, 2, -1, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}
    C4Int8T obj_out02[8] = {{1, 0, 1, 2, 2, -1}, {1, 0, 2, 1, 3, 2}, {1, 0, 3, 1, 1, 3}, 
        {1, 0, 4, 2, 2, 3}, {-1, 0, 1, 2, 2, -1}, {-1, 0, 2, 1, 3, 2}, {-1, 0, 3, 1, 1, 3}, {-1, 0, 4, 2, 2, 3}};
    C4Int8T obj_out03[8] = {{1, 0, 1, 2, 2, -1}, {1, 0, 2, 1, 3, 2}, {1, 0, 3, 1, 1, 3},
        {1, 0, 4, 2, 2, 3}, {-1, 0, 1, 2, 2, -1}, {-1, 0, 2, 1, 3, 2}, {-1, 0, 3, 1, 1, 3}, {-1, 0, 4, 2, 2, 3}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    const char *subName01 = "subVertexLabelB";
    const char *subName02 = "subVertexLabelC";
    const char *subName03 = "subVertexLabelD";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub资源表
    char *sub_info01 = NULL;
    readJanssonFile("./schemaFile/subInfo032B.json", &sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C4Int8C1Int4RescGet;
    userData01->objLen = 8;
    userData01->obj = obj_out01;
    userData01->isResourcePubSub = true;
    userData01->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallbackPubSub, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系，pubsub普通表
    char *sub_info02 = NULL;
    readJanssonFile("./schemaFile/subInfo032C.json", &sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C4Int8RescGet;
    userData02->objLen = 8;
    userData02->obj = obj_out02;
    userData02->isResourcePubSub = false;
    userData02->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallbackPubSub, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 创建订阅关系，pubsub普通更新表
    char *sub_info03 = NULL;
    readJanssonFile("./schemaFile/subInfo032D.json", &sub_info03);
    GmcSubConfigT tmp_sub_info03;
    tmp_sub_info03.subsName = subName03;
    tmp_sub_info03.configJson = sub_info03;
    SnUserDataWithFuncT *userData03 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData03->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData03->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData03->readResFunc = C4Int8RescGet;
    userData03->objLen = 8;
    userData03->obj = obj_out03;
    userData03->isResourcePubSub = false;
    userData03->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info03, conn_sn_sync, snCallbackPubSub, userData03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info03);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableA[LABEL_NAME] = "A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}};
    // 批写4条数据,失败回滚
    ret = testTable(g_conn, g_stmt, tableA, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(4);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readA[0][5] = {};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA, 0, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    // 校验表记录，全部回滚
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA, g_connServer, g_testNameSpace);
    system(command);
    ret = executeCommand(command, "A", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);
    free(userData03->data);
    free(userData03);

    ret = GmcDropVertexLabel(g_stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_tbmlogName);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.可更新过期表触发多种类型的表，正常场景
TEST_F(timeoutComplexFuncTest, DataLog_011_006_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_034";
    char labelName_out[FILE_PATH] = "External";
    char command[MAX_CMD_SIZE] = {0};
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    // 运行
    int ret = 0;
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName_out);
    readJanssonFile("schemaFile/external_external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    
    const char *configJson = "{\"max_record_count\":10, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    // 订阅推送的记录
    C4Int8C1Int4T obj_out01[6] = {{1, 0, 1, 2, 2, -1, 1}, {-1, 0, 1, 2, 2, -1, 1}, {1, 0, 2, 4, 4, 9, 1},
        {1, 0, 2, 1, 3, 2, 1}, {1, 0, 3, 1, 1, 3, 1}, {1, 0, 4, 2, 2, 3, 1}};
    // 写入的记录 {1, 2, 2, -1, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}
    C4Int8T obj_out02[6] = {{1, 0, 1, 2, 2, -1}, {-1, 0, 1, 2, 2, -1}, {1, 0, 2, 4, 4, 9},
        {1, 0, 2, 1, 3, 2}, {1, 0, 3, 1, 1, 3}, {1, 0, 4, 2, 2, 3}};
    C4Int8T obj_out03[6] = {{1, 0, 1, 2, 2, -1}, {-1, 0, 1, 2, 2, -1}, {1, 0, 2, 4, 4, 9},
        {1, 0, 2, 1, 3, 2}, {1, 0, 3, 1, 1, 3}, {1, 0, 4, 2, 2, 3}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    const char *subName01 = "subVertexLabelB";
    const char *subName02 = "subVertexLabelC";
    const char *subName03 = "subVertexLabelD";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub资源表
    char *sub_info01 = NULL;
    readJanssonFile("./schemaFile/subInfo032B.json", &sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C4Int8C1Int4RescGet;
    userData01->objLen = 6;
    userData01->obj = obj_out01;
    userData01->isResourcePubSub = true;
    userData01->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallbackPubSub, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系，pubsub普通表
    char *sub_info02 = NULL;
    readJanssonFile("./schemaFile/subInfo032C.json", &sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C4Int8RescGet;
    userData02->objLen = 6;
    userData02->obj = obj_out02;
    userData02->isResourcePubSub = false;
    userData02->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallbackPubSub, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 创建订阅关系，pubsub普通更新表
    char *sub_info03 = NULL;
    readJanssonFile("./schemaFile/subInfo032D.json", &sub_info03);
    GmcSubConfigT tmp_sub_info03;
    tmp_sub_info03.subsName = subName03;
    tmp_sub_info03.configJson = sub_info03;
    SnUserDataWithFuncT *userData03 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData03->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData03->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData03->readResFunc = C4Int8RescGet;
    userData03->objLen = 6;
    userData03->obj = obj_out03;
    userData03->isResourcePubSub = false;
    userData03->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info03, conn_sn_sync, snCallbackPubSub, userData03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info03);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableA[LABEL_NAME] = "A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}};
    // 批写4条数据
    ret = testTable(g_conn, g_stmt, tableA, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(4);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readA[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 1}, {4, 2, 2, 3, 1}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    // 读pubsub资源表B
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "B", g_connServer, g_testNameSpace);
    system(command);
    ret =
        executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4", "\"e\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读External表
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "External", g_connServer,
        g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读普通表
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "H", g_connServer, g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 可更新表
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "G", g_connServer, g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
  
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);
    free(userData03->data);
    free(userData03);

    ret = GmcDropVertexLabel(g_stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.部分可更新过期表触发多种类型的表，正常场景
TEST_F(timeoutComplexFuncTest, DataLog_011_006_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char outputFilePath[FILE_PATH] = "./timeoutFile/complex";
    char soName[FILE_PATH] = "timeout_complex_035";
    char labelName_out[FILE_PATH] = "External";
    char command[MAX_CMD_SIZE] = {0};
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    // 运行
    int ret = 0;
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName_out);
    readJanssonFile("schemaFile/external_external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    
    const char *configJson = "{\"max_record_count\":10, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    
    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    int recordNum = 4;
    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);

    // 订阅推送的记录
    C4Int8C1Int4T obj_out01[6] = {{1, 0, 1, 2, 2, -1, 1}, {-1, 0, 1, 2, 2, -1, 1}, {1, 0, 2, 4, 4, 9, 1},
        {1, 0, 2, 1, 3, 2, 1}, {1, 0, 3, 1, 1, 3, 1}, {1, 0, 4, 2, 2, 3, 1}};
    // 写入的记录 {1, 2, 2, -1, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}
    C4Int8T obj_out02[6] = {{1, 0, 1, 2, 2, -1}, {-1, 0, 1, 2, 2, -1}, {1, 0, 2, 4, 4, 9},
        {1, 0, 2, 1, 3, 2}, {1, 0, 3, 1, 1, 3}, {1, 0, 4, 2, 2, 3}};
    C4Int8T obj_out03[6] = {{1, 0, 1, 2, 2, -1}, {-1, 0, 1, 2, 2, -1}, {1, 0, 2, 4, 4, 9},
        {1, 0, 2, 1, 3, 2}, {1, 0, 3, 1, 1, 3}, {1, 0, 4, 2, 2, 3}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    const char *subName01 = "subVertexLabelB";
    const char *subName02 = "subVertexLabelC";
    const char *subName03 = "subVertexLabelD";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub资源表
    char *sub_info01 = NULL;
    readJanssonFile("./schemaFile/subInfo032B.json", &sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C4Int8C1Int4RescGet;
    userData01->objLen = 6;
    userData01->obj = obj_out01;
    userData01->isResourcePubSub = true;
    userData01->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallbackPubSub, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系，pubsub普通表
    char *sub_info02 = NULL;
    readJanssonFile("./schemaFile/subInfo032C.json", &sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C4Int8RescGet;
    userData02->objLen = 6;
    userData02->obj = obj_out02;
    userData02->isResourcePubSub = false;
    userData02->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallbackPubSub, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 创建订阅关系，pubsub普通更新表
    char *sub_info03 = NULL;
    readJanssonFile("./schemaFile/subInfo032D.json", &sub_info03);
    GmcSubConfigT tmp_sub_info03;
    tmp_sub_info03.subsName = subName03;
    tmp_sub_info03.configJson = sub_info03;
    SnUserDataWithFuncT *userData03 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData03->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData03->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData03->readResFunc = C4Int8RescGet;
    userData03->objLen = 6;
    userData03->obj = obj_out03;
    userData03->isResourcePubSub = false;
    userData03->current = currentTime;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info03, conn_sn_sync, snCallbackPubSub, userData03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info03);

    /*-----------------------------------------写输入表------------------------------------------------------- */
    char tableA[LABEL_NAME] = "A";
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 2}, {4, 2, 2, 3, 3}};
    // 批写4条数据
    ret = testTable(g_conn, g_stmt, tableA, BATCH_INSERT, data, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*------------------------------------等待过期字段删除----------------------------------------------------- */
    sleep(4);

    /*-----------------------------------------读输入表------------------------------------------------------- */
    int64_t readA[recordNum][5] = {{2, 4, 4, 9, 1}, {2, 1, 3, 2, 1}, {3, 1, 1, 3, 1}, {4, 2, 2, 3, 1}};
    ret = testTable(g_conn, g_stmt, tableA, READ, readA, recordNum, currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "read input end.");

    // 读pubsub资源表B
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "B", g_connServer, g_testNameSpace);
    system(command);
    ret =
        executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4", "\"e\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读External表
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "External", g_connServer,
        g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读普通表
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "H", g_connServer, g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 可更新表
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, "G", g_connServer, g_testNameSpace);
    system(command);
    ret = executeRemoveSpacesCommand(command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
  
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);
    free(userData03->data);
    free(userData03);

    ret = GmcDropVertexLabel(g_stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
