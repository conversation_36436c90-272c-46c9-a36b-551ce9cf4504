/* ****************************************************************************
 Description  : datalog程序运行-复杂规则实现
 Node      :
 001 定义类：%resource
    001 新增定义%resource验证，固定型资源建表包含投影规则
    002 新增定义%resource验证，固定型资源建表包含NOTJOIN规则
    003 新增定义%resource验证，固定型资源建表包含聚合规则
    004 新增定义%resource验证，固定型资源建表多表join规则中包含普通表
    005 新增定义%resource验证，固定型资源建表多表join规则中包含transient表
    006 新增定义%resource验证，固定型资源建表多表join规则中包含function
    007 新增定义%resource验证，pubsub型资源建表包含投影规则
    008 新增定义%resource验证，pubsub型资源建表包含NOTJOIN规则
    009 新增定义%resource验证，pubsub型资源建表包含聚合规则
    010 新增定义%resource验证，pubsub型资源建表多表join规则中包含普通表
    011 新增定义%resource验证，pubsub型资源建表多表join规则中包含transient表
    012 新增定义%resource验证，pubsub型资源建表多表join规则中包含function
 002 定义类：%function
    001 新增定义%function验证，选项包含仅access_delta，写func的输入表的输入表
    002 新增定义%function验证，选项包含仅access_delta，写规则的左表
    003 新增定义%function验证，选项包含仅access_delta，读func的输入表的输入表
    004 新增定义%function验证，选项包含仅access_delta，读规则的左表
    005 新增定义%function验证，选项包含仅access_delta，读写func的输入表的输入表
    006 新增定义%function验证，选项包含仅access_delta，读写规则的左表
    007 新增定义%function验证，选项包含仅access_current，读规则的左表
    008 新增定义%function验证，选项包含仅access_current，读func的输入表的输入表
    009 新增定义%function验证，选项包含access_delta和access_current指定为同个表，写delta读org的func的输入表的输入表
    010 新增定义%function验证，选项包含access_delta和access_current指定为同个表，写delta读org的规则的左表
    011 新增定义%function验证，选项包含access_delta和access_current指定为同个表，读delta读org的func的输入表的输入表
    012 新增定义%function验证，选项包含access_delta和access_current指定为同个表，读delta读org的规则的左表
    013 新增定义%function验证，选项包含access_delta和access_current指定为不同表，写delta的func的输入表的输入表，读org的规则的左表
    014 新增定义%function验证，选项包含access_delta和access_current指定为不同表，读org的func的输入表的输入表，写delta的规则的左表
    015 新增定义%function验证，选项包含access_delta和access_current指定为不同表，读delta的func的输入表的输入表，读org的规则的左表
    016 新增定义%function验证，选项包含access_delta和access_current指定为不同表，读org的func的输入表的输入表，读delta的规则的左表
 003 定义类：%aggregate
    001 新增定义%aggregate验证，选项包含many_to_one同时仅order，函数进行大到小排序
    002 新增定义%aggregate验证，选项包含many_to_one同时仅order，函数进行小到大排序
    003 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，写func的输入表的输入表
    004 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，写规则的左表
    005 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，读func的输入表的输入表
    006 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，读规则的左表
    007 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，读写func的输入表的输入表
    008 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，读写规则的左表
    009 新增定义%aggregate验证，选项包含many_to_one同时仅access_current，读func的输入表的输入表
    010 新增定义%aggregate验证，选项包含many_to_one同时仅access_current，读规则的左表
    011 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为同个表，写delta读org的func的输入表的输入表
    012 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为同个表，写delta读org的规则的左表
    013 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为同个表，读delta读org的func的输入表的输入表
    014 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为同个表，读delta读org的规则的左表
    015 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为不同表，写delta的func的输入表的输入表，读org的规则的左表，读org的func的输入表
    016 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为不同表，读org的func的输入表的输入表，写delta的规则的左表，读org的func的输入表
    017 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为不同表，读delta的func的输入表的输入表，读org的规则的左表，读delta的func的输入表
    018 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为不同表，读org的func的输入表的输入表，读delta的规则的左表，读delta的func的输入表
    019 新增定义%aggregate验证，选项包含many_to_many同时仅order，函数进行大到小排序
    020 新增定义%aggregate验证，选项包含many_to_many同时仅order，函数进行小到大排序
    021 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，写func的输入表的输入表
    022 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，写规则的左表
    023 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，读func的输入表的输入表
    024 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，读规则的左表
    025 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，读写func的输入表的输入表
    026 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，读写规则的左表
    027 新增定义%aggregate验证，选项包含many_to_many同时仅access_current，读func的输入表的输入表
    028 新增定义%aggregate验证，选项包含many_to_many同时仅access_current，读规则的左表
    029 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为同个表，写delta读org的func的输入表的输入表
    030 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为同个表，写delta读org的规则的左表
    031 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为同个表，读delta读org的func的输入表的输入表
    032 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为同个表，读delta读org的规则的左表
    033 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为不同表，写delta的func的输入表的输入表，读org的规则的左表，读org的func的输入表
    034 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为不同表，读org的func的输入表的输入表，写delta的规则的左表，读org的func的输入表
    035 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为不同表，读delta的func的输入表的输入表，读org的规则的左表，读delta的func的输入表
    036 新增定义%aggregate验证，选项包含many_to_many同时access_delta和acce
 004 表选项类：transient(tuple)
    001 新增表选项类transient(tuple)验证，定义表在规则中作输入表包含投影规则
    002 新增表选项类transient(tuple)验证，定义表在规则中作输入表包含NOTJOIN规则
    003 新增表选项类transient(tuple)验证，定义表在规则中作输入表包含聚合规则
    004 新增表选项类transient(tuple)验证，定义表在规则中作输入表多表join规则中包含普通表
    005 新增表选项类transient(tuple)验证，定义表在规则中作输入表多表join规则中包含function
    006 新增表选项类transient(tuple)验证，定义表在规则中作中间表包含投影规则
    007 新增表选项类transient(tuple)验证，定义表在规则中作中间表包含NOTJOIN规则
    008 新增表选项类transient(tuple)验证，定义表在规则中作中间表包含聚合规则
    009 新增表选项类transient(tuple)验证，定义表在规则中作中间表多表join规则中包含普通表
    010 新增表选项类transient(tuple)验证，定义表在规则中作中间表多表join规则中包含function
 005 表选项类：transient(field)
    001 新增表选项类transient(field)验证，定义表在规则中作中间表包含投影规则
    002 新增表选项类transient(field)验证，定义表在规则中作中间表包含NOTJOIN规则
    003 新增表选项类transient(field)验证，定义表在规则中作中间表包含聚合规则
    004 新增表选项类transient(field)验证，定义表在规则中作中间表多表join规则中包含普通表
    005 新增表选项类transient(field)验证，定义表在规则中作中间表多表join规则中包含function
 006 表选项类：local
    001 新增表选项类local验证，定义表%table在规则中作输出表
    002 新增表选项类local验证，定义表%table在规则中作中间表
    003 新增表选项类local验证，定义表%table在规则中作输入表
    004 新增表选项类local验证，定义表%table在规则中作输出表，且同时存在shared或者notify
    005 新增表选项类local验证，定义表为%resource
    006 新增表选项类local验证，定义表为%function
    007 新增表选项类local验证，定义表为%aggregate
 007 表选项类：shared
    001 新增表选项类shared验证，定义表%table在规则中作输出表
    002 新增表选项类shared验证，定义表%table在规则中作中间表
    003 新增表选项类shared验证，定义表%table在规则中作输入表
    004 新增表选项类shared验证，定义表%table在规则中作输出表，且同时存在local或者notify
    005 新增表选项类shared验证，定义表为%resource
    006 新增表选项类shared验证，定义表为%function
    007 新增表选项类shared验证，定义表为%aggregate
 008 表选项类：notify
    001 新增表选项类notify验证，定义表%table在规则中作输出表
    002 新增表选项类notify验证，定义表%table在规则中作中间表
    003 新增表选项类notify验证，定义表%table在规则中作输入表
    004 新增表选项类notify验证，定义表%table在规则中作输出表，且同时存在shared或者local
    005 新增表选项类notify验证，定义表为%resource
    006 新增表选项类notify验证，定义表为%function
    007 新增表选项类notify验证，定义表为%aggregate
 009 规则类：notjoin
    001 新增规则类NOTJOIN验证，规则中全部表字段数一致
    002 新增规则类NOTJOIN验证，规则中输入表字段数一致，输出表较少
    003 新增规则类NOTJOIN验证，规则中输入表字段数一致，输出表较多且为常数字段
    004 新增规则类NOTJOIN验证，规则中输出表与JOIN表字段数一致，NOT表较少
    005 新增规则类NOTJOIN验证，规则中输出表与JOIN表字段数一致，NOT表较多且为忽略字段
    006 新增规则类NOTJOIN验证，规则中输出表与JOIN表字段数一致，NOT表较多且为常数字段
    007 新增规则类NOTJOIN验证，规则中输出表与NOT表字段数一致，JOIN表较多且为忽略字段
    008 新增规则类NOTJOIN验证，规则中输出表与NOT表字段数一致，JOIN表较多且为常数字段
 010 规则类：GROUP-BY
    001 新增规则类GROUP-BY验证，规则中输出表为transient(tuple)
    002 新增规则类GROUP-BY验证，规则中输出表为transient(field)
 011 特殊场景
    001 规则中左表出现忽略字段且为resource表
    002 规则中右表出现忽略字段且为普通表
    003 规则中右表出现忽略字段且为transient表
    004 规则中右表出现忽略字段且为resource表
    005 规则中右表出现忽略字段且为NOTJOIN规则
    006 规则中右表出现忽略字段且为多表JOIN规则
    007 规则中左表出现常数字段且为普通表
    008 规则中左表出现常数字段且为transient表
    009 规则中左表出现常数字段且为resource表
    010 规则中左表出现常数字段且为NOTJOIN规则
    011 规则中左表出现常数字段且为多表JOIN规则
    012 规则中右表出现常数字段且为普通表
    013 规则中右表出现常数字段且为transient表
    014 规则中右表出现常数字段且为resource表
    015 规则中右表出现常数字段且为NOTJOIN规则
    016 规则中右表出现常数字段且为多表JOIN规则
    017 左表为null(0)的投影规则
    018 左表为null(0)的NOTJOIN规则
    019 左表为null(0)的聚合规则
    020 左表为null(0)的多表join规则包含普通表
    021 左表为null(0)的多表join规则包含transient表
    022 左表为null(0)的多表join规则包含resource表
    023 左表为null(0)的多表join规则包含function
    024 新增索引扫描算子验证，投影规则中右表全为变量
    025 新增索引扫描算子验证，投影规则中右表存在忽略字段
    026 新增索引扫描算子验证，投影规则中右表存在常数字段
    027 新增索引扫描算子验证，NOTJOIN规则中右表全为变量
    028 新增索引扫描算子验证，NOTJOIN规则中右表存在忽略字段
    029 新增索引扫描算子验证，NOTJOIN规则中右表存在常数字段
    030 新增索引扫描算子验证，聚合规则中右表全为变量
    031 新增索引扫描算子验证，多表join规则中右表全为变量
    032 新增索引扫描算子验证，多表join规则中右表存在忽略字段
    033 新增索引扫描算子验证，多表join规则中右表存在常数字段
 012 失败场景
    001 触发规则resource的count字段值为负
    002 调用Gmc接口直接写固定型资源建表
    003 调用Gmc接口直接写transient(field)表
 013 综合场景
    001 NOTJOIN场景
    002 聚合场景
    003 transient(field)使用场景
    004 resource使用场景
    005 聚合规则中输出表记录由其他规则产生
    006 resource定义表出现在右表
 014 交互场景
    001 同步单写输入表
    002 固定资源池建表，查看资源池相关视图
    003 规则中存在%function定义函数的使用，视图查看相关的算子
    004 规则中存在%aggregate定义函数的使用，视图查看相关的算子
    005 存在using namespace的语法时，验证规则的准确性
    006 投影相同列用例
 015 可靠/可用性
    001 允许的最大固定资源池建表最大resource数量（1个输入表，1个输出表，1022个资源表），资源字段max_size设置为最大，并发写输入表至资源字段分配失败
    002 固定资源池建表，资源字段设置为最大，验证全部分配成功
    003 写入过程中，使用gmsysview并发查询不同视图
    004 多表Join过程中一次写入大量的数据（表字段类型为byte或str）
    005 验证允许变长字段个数规格
    006 验证keybuf大小规则（主键Index 0）
    007 规则中存在表字段个数为上限值64
    008 输出表为pubsub，写输入表触发规则运算，在func中验证输出delta表已有数据，主动回滚
    009 输出表为pubsub，写输入表触发规则运算至输入表超过max_size导致失败，在func中验证输出delta数据回滚，主动回滚
    010 输出表为pubsub，写输入表触发规则运算至输出表超过max_size导致失败，在func中验证输出delta数据回滚，主动回滚
 Author       : 黄楚灿 hwx1007418
 Modification :
 Date         : 2022/09/
 node :
**************************************************************************** */

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "DatalogRunEnhance.h"
#include "t_datacom_lite.h"

class EMPTY_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        char result[64] = { 0 };
        ret = GetPrintBycmd("cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, g_libDir, sizeof(g_libDir));
        EXPECT_EQ(0, ret);
        ret = GetPrintBycmd("cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, result, 64);
        EXPECT_EQ(0, ret);
        SystemSnprintf("mkdir -p %s", g_libDir);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {
        if (!(g_usrMode)) {
            SystemSnprintf("rm -rf %s", g_libDir);
        }
    }

public:
    virtual void SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

    virtual void TearDown(){}
};

class DatalogRunEnhance_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        char result[64] = { 0 };
        ret = GetPrintBycmd("cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, g_libDir, sizeof(g_libDir));
        EXPECT_EQ(0, ret);
        ret = GetPrintBycmd("cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, result, 64);
        EXPECT_EQ(0, ret);
        SystemSnprintf("mkdir -p %s", g_libDir);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {
        if (!(g_usrMode)) {
            SystemSnprintf("rm -rf %s", g_libDir);
        }
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogRunEnhance_test::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret = 0;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void DatalogRunEnhance_test::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

// 001 定义类：%resource
// 001 新增定义%resource验证，固定型资源建表包含投影规则
TEST_F(DatalogRunEnhance_test, DataLog_007_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    SingleInt4St *Obj_in = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, SingleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, SingleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DoubleInt4St *Obj_out = Obj_mid;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_mid);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增定义%resource验证，固定型资源建表包含NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in1[] = "ns2.A";
    char labelName_in2[] = "ns2.B";
    char labelName_mid[] = "ns2.rsc0";
    char labelName_out[] = "ns2.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in1, (Obj_in1 + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in1);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 新增定义%resource验证，固定型资源建表包含聚合规则
TEST_F(DatalogRunEnhance_test, DataLog_007_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns3.A";
    char labelName_mid[] = "ns3.rsc0";
    char labelName_out[] = "ns3.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = 1;
        if (i >= 5) {
            Obj_in[i].a = 2;
        }
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue()); // 待确认，聚合规则导致

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 11;
    int sum_a[2] = {}, sum_b[2] = {};
    ThreeInt4St *Obj_mid = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount - 1; i++, rsc_value++) {
        Obj_mid[i].a = 1;
        if (i >= 5) {
            Obj_mid[i].a = 2;
        }
        Obj_mid[i].b = i;
        Obj_mid[i].c = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
        if (Obj_mid[i].a == 1) {
            sum_a[0] += Obj_mid[i].b;
            sum_b[0] += Obj_mid[i].c;
        } else if (Obj_mid[i].a == 2) {
            sum_a[1] += Obj_mid[i].b;
            sum_b[1] += Obj_mid[i].c;
        }
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 2;
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    Obj_out[0].a = 1;
    Obj_out[1].a = 2;
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i + 1;
        Obj_out[i].b = sum_a[i];
        Obj_out[i].c = sum_b[i];
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 新增定义%resource验证，固定型资源建表多表join规则中包含普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in1[] = "ns4.A";
    char labelName_in2[] = "ns4.B";
    char labelName_in3[] = "ns4.C";
    char labelName_mid[] = "ns4.rsc0";
    char labelName_out[] = "ns4.D";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in3, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in3, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in1, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in3, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 11;
    ThreeInt4St *Obj_mid = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].c = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 11;
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].c = (rsc_value < 10) ? rsc_value : -1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 005 新增定义%resource验证，固定型资源建表多表join规则中包含transient表
TEST_F(DatalogRunEnhance_test, DataLog_007_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in1[] = "ns5.A";
    char labelName_in2[] = "ns5.B";
    char labelName_in3[] = "ns5.C";
    char labelName_mid[] = "ns5.rsc0";
    char labelName_out[] = "ns5.D";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in3, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in1, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in2, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in3, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 11;
    ThreeInt4St *Obj_mid = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].c = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 006 新增定义%resource验证，固定型资源建表多表join规则中包含function (DTS2022092101435)
TEST_F(DatalogRunEnhance_test, DataLog_007_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns6.A";
    char labelName_mid[] = "ns6.rsc0";
    char labelName_out[] = "ns6.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 11;
    ThreeInt4St *Obj_mid = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i + 1;
        Obj_mid[i].c = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 11;
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].c = (rsc_value < 10) ? rsc_value + 1 : 0;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
int snCallbackCheck_pubsub(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *resp)
{
    SnUserDataT *data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;

    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    bool isNull;
                    int32_t a2 = -2, dtlReservedCount;
                    static int32_t rsc0 = 0;
                    ret = GmcGetVertexPropertyByName(subStmt, "b", &a2,
                        sizeof(a2), &isNull);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    if (a2 == -2) {
                        a2 = rsc0++;
                        ret = GmcSetVertexProperty(subStmt, "b", GMC_DATATYPE_INT32, &a2, sizeof(a2));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    }
                    ret = GmcSubAddRespDML(resp, subStmt);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
    }

    return GMERR_OK;
}
void snCallback_pubsub(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    // 用户消息创建
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 
    ret = snCallbackCheck_pubsub(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 用户信息发送
    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 用户信息销毁
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007 新增定义%resource验证，pubsub型资源建表包含投影规则
TEST_F(DatalogRunEnhance_test, DataLog_007_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubResource_ns1.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataT *userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_pubsub, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = 0;
    int endid = 10;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 008 新增定义%resource验证，pubsub型资源建表包含NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in1[] = "ns2.A";
    char labelName_in2[] = "ns2.B";
    char labelName_mid[] = "ns2.rsc0";
    char labelName_out[] = "ns2.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubResource_ns2.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns2.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataT *userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_pubsub, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = 0;
    int endid = 10;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordId(conn, stmt, labelName_in2, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in2, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DoubleInt4St Obj_mid = {};
    ret = readRecord(conn, stmt, labelName_mid, &Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId(conn, stmt, labelName_out, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
int snCallbackCheck_pubsub2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *resp)
{
    SnUserDataT *data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;

    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    bool isNull;
                    int32_t a2 = -2, dtlReservedCount;
                    static int32_t rsc0 = 0;
                    ret = GmcGetVertexPropertyByName(subStmt, "c", &a2,
                        sizeof(a2), &isNull);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    if (a2 == -2) {
                        a2 = rsc0++;
                        ret = GmcSetVertexProperty(subStmt, "c", GMC_DATATYPE_INT32, &a2, sizeof(a2));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    }
                    ret = GmcSubAddRespDML(resp, subStmt);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
    }

    return GMERR_OK;
}
void snCallback_pubsub2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    // 用户消息创建
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 
    ret = snCallbackCheck_pubsub2(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 用户信息发送
    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 用户信息销毁
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 009 新增定义%resource验证，pubsub型资源建表包含聚合规则
TEST_F(DatalogRunEnhance_test, DataLog_007_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in[] = "ns3.A";
    char labelName_mid[] = "ns3.rsc0";
    char labelName_out[] = "ns3.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubResource_ns3.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns3.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataT *userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_pubsub2, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = 0;
    int endid = 10;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 010 新增定义%resource验证，pubsub型资源建表多表join规则中包含普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in1[] = "ns4.A";
    char labelName_in2[] = "ns4.B";
    char labelName_in3[] = "ns4.C";
    char labelName_mid[] = "ns4.rsc0";
    char labelName_out[] = "ns4.D";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubResource_ns4.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns4.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataT *userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_pubsub2, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = 0;
    int endid = 10;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordId(conn, stmt, labelName_in2, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordId(conn, stmt, labelName_in3, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId(conn, stmt, labelName_in2, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId(conn, stmt, labelName_in3, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 011 新增定义%resource验证，pubsub型资源建表多表join规则中包含transient表
TEST_F(DatalogRunEnhance_test, DataLog_007_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in1[] = "ns5.A";
    char labelName_in2[] = "ns5.B";
    char labelName_in3[] = "ns5.C";
    char labelName_mid[] = "ns5.rsc0";
    char labelName_out[] = "ns5.D";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubResource_ns5.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns5.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataT *userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_pubsub2, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = 0;
    int endid = 10;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in2, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordId(conn, stmt, labelName_in3, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    DoubleInt4St Obj_in = {};
    ret = readRecord(conn, stmt, labelName_in1, &Obj_in, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId(conn, stmt, labelName_in2, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId(conn, stmt, labelName_in3, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 012 新增定义%resource验证，pubsub型资源建表多表join规则中包含function (DTS2022092101435)
TEST_F(DatalogRunEnhance_test, DataLog_007_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in[] = "ns6.A";
    char labelName_mid[] = "ns6.rsc0";
    char labelName_out[] = "ns6.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubResource_ns6.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns6.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataT *userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_pubsub2, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = 0;
    int endid = 10;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 002 定义类：%function
// 001 新增定义%function验证，选项包含仅access_delta，写func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns1_w.A";
    char labelName_mid[] = "ns1_w.B";
    char labelName_out[] = "ns1_w.C";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增定义%function验证，选项包含仅access_delta，写规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns3_w.A";
    char labelName_mid[] = "ns3_w.B";
    char labelName_out[] = "ns3_w.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10 * 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount/2; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    for (int i = writeCount/2, j = 0; i < writeCount; i++, j++) {
        Obj_out[i].a = j + 1;
        Obj_out[i].b = j - 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 新增定义%function验证，选项包含仅access_delta，读func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns1_r.A";
    char labelName_mid[] = "ns1_r.B";
    char labelName_out[] = "ns1_r.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 新增定义%function验证，选项包含仅access_delta，读规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns3_r.A";
    char labelName_mid[] = "ns3_r.B";
    char labelName_out[] = "ns3_r.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 005 新增定义%function验证，选项包含仅access_delta，读写func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns1_wr.A";
    char labelName_mid[] = "ns1_wr.B";
    char labelName_out[] = "ns1_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount-1; i++) {
        Obj_in[i].a = i + 1;
        Obj_in[i].b = i + 1;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    Obj_in[writeCount-1].a = 0;
    Obj_in[writeCount-1].b = 0;
    Obj_in[writeCount-1].dtlReservedCount = 11;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 11;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 11;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 006 新增定义%function验证，选项包含仅access_delta，读写规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns3_r.A";
    char labelName_mid[] = "ns3_r.B";
    char labelName_out[] = "ns3_r.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 007 新增定义%function验证，选项包含仅access_current，读规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns6.A";
    char labelName_mid[] = "ns6.B";
    char labelName_out[] = "ns6.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 008 新增定义%function验证，选项包含仅access_current，读func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns4.A";
    char labelName_mid[] = "ns4.B";
    char labelName_out[] = "ns4.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
/*
write   (0+..+n)/n
0       0/ 1    0
1       1/ 2    0
2       3/ 3    1
3       6/ 4    1
4       10/ 5   2
5       15/ 6   2
6       21/ 7   3
7       28/ 8   3
8       36/ 9   4
9       47/ 10  4
*/
// 009 新增定义%function验证，选项包含access_delta和access_current指定为同个表，写delta读org的func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns7_wr.A";
    char labelName_mid[] = "ns7_wr.B";
    char labelName_out[] = "ns7_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < 5; i++) {
        Obj_in[i].dtlReservedCount = 3;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 010 新增定义%function验证，选项包含access_delta和access_current指定为同个表，写delta读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_wr.A";
    char labelName_mid[] = "ns9_wr.B";
    char labelName_out[] = "ns9_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 11;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount-1; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[writeCount-1].a = 0;
    Obj_out[writeCount-1].b = 0;
    Obj_out[writeCount-1].dtlReservedCount = 10;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 011 新增定义%function验证，选项包含access_delta和access_current指定为同个表，读delta读org的func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns7_rr.A";
    char labelName_mid[] = "ns7_rr.B";
    char labelName_out[] = "ns7_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 012 新增定义%function验证，选项包含access_delta和access_current指定为同个表，读delta读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
/*
func:
    read C  ->  write A
*/
// 013 新增定义%function验证，选项包含access_delta和access_current指定为不同表，写delta的func的输入表的输入表，读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_013) // 待确认
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns10.A";
    char labelName_mid[] = "ns10.B";
    char labelName_out[] = "ns10.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    int readCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = (i == 0) ? 11 : 1;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 014 新增定义%function验证，选项包含access_delta和access_current指定为不同表，读org的func的输入表的输入表，写delta的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns12.A";
    char labelName_mid[] = "ns12.B";
    char labelName_out[] = "ns12.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 015 新增定义%function验证，选项包含access_delta和access_current指定为不同表，读delta的func的输入表的输入表，读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns13.A";
    char labelName_mid[] = "ns13.B";
    char labelName_out[] = "ns13.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 016 新增定义%function验证，选项包含access_delta和access_current指定为不同表，读org的func的输入表的输入表，读delta的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns12.A";
    char labelName_mid[] = "ns12.B";
    char labelName_out[] = "ns12.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 003 定义类：%aggregate
// 001 新增定义%aggregate验证，选项包含many_to_one同时仅order，函数进行大到小排序
TEST_F(DatalogRunEnhance_test, DataLog_007_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns14_max.A";
    char labelName_out[] = "ns14_max.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增定义%aggregate验证，选项包含many_to_one同时仅order，函数进行小到大排序
TEST_F(DatalogRunEnhance_test, DataLog_007_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns14_min.A";
    char labelName_out[] = "ns14_min.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，写func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns1_w.A";
    char labelName_mid[] = "ns1_w.B";
    char labelName_out[] = "ns1_w.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[3] = {1, 2, -10};
    int32_t sum[3] = {0, 0, -10};
    int writeCount = 10;
    int readCount = writeCount + 1;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    Obj_in[writeCount].a = groupId[2];
    Obj_in[writeCount].b = groupId[2];
    Obj_in[writeCount].dtlReservedCount = writeCount/2 + 2;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount + 1;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_mid[readCount - 1].a = groupId[2];
    Obj_mid[readCount - 1].b = groupId[2];
    Obj_mid[readCount - 1].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 3;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，写规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns3_w.A";
    char labelName_mid[] = "ns3_w.B";
    char labelName_out[] = "ns3_w.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[3] = {1, 2, -10};
    int32_t sum[3] = {0, 0, -10};
    int writeCount = 10;
    int readCount = writeCount;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 3;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].dtlReservedCount = writeCount/2 + 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 005 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，读func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns1_r.A";
    char labelName_mid[] = "ns1_r.B";
    char labelName_out[] = "ns1_r.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 006 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，读规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns3_r.A";
    char labelName_mid[] = "ns3_r.B";
    char labelName_out[] = "ns3_r.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 007 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，读写func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns1_wr.A";
    char labelName_mid[] = "ns1_wr.B";
    char labelName_out[] = "ns1_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[3] = {1, 2, -10};
    int32_t sum[3] = {0, 0, -10};
    int writeCount = 10;
    int readCount = writeCount + 1;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    char cmd[1024] = {0};
    const char *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(cmd, 1024, "%s/gmsysview -q %s -f TABLE_NAME=ns1_wr.B", g_toolPath, viewName);
    ret = executeCommand(cmd, "Access Delta", "ns1_wr.A");
    EXPECT_EQ(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);

    Obj_in[writeCount].a = groupId[2];
    Obj_in[writeCount].b = groupId[2];
    Obj_in[writeCount].dtlReservedCount = writeCount/2 + 2;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount + 1;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_mid[writeCount].a = groupId[2];
    Obj_mid[writeCount].b = groupId[2];
    Obj_mid[writeCount].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 3;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 008 新增定义%aggregate验证，选项包含many_to_one同时仅access_delta，读写规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns3_wr.A";
    char labelName_mid[] = "ns3_wr.B";
    char labelName_out[] = "ns3_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[3] = {1, 2, -10};
    int32_t sum[3] = {0, 0, -10};
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 3;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount - 1].dtlReservedCount = writeCount/2 + 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 009 新增定义%aggregate验证，选项包含many_to_one同时仅access_current，读func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns4.A";
    char labelName_mid[] = "ns4.B";
    char labelName_out[] = "ns4.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 010 新增定义%aggregate验证，选项包含many_to_one同时仅access_current，读规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns6.A";
    char labelName_mid[] = "ns6.B";
    char labelName_out[] = "ns6.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 011 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为同个表，写delta读org的func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns7_wr.A";
    char labelName_mid[] = "ns7_wr.B";
    char labelName_out[] = "ns7_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[3] = {1, 2, -10};
    int32_t sum[3] = {0, 0, -10};
    int writeCount = 10;
    int readCount = writeCount + 1;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    Obj_in[writeCount].a = groupId[2];
    Obj_in[writeCount].b = groupId[2];
    Obj_in[writeCount].dtlReservedCount = writeCount/2 + 2;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount + 1;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_mid[writeCount].a = groupId[2];
    Obj_mid[writeCount].b = groupId[2];
    Obj_mid[writeCount].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 3;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 012 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为同个表，写delta读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns9_wr.A";
    char labelName_mid[] = "ns9_wr.B";
    char labelName_out[] = "ns9_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[3] = {1, 2, -10};
    int32_t sum[3] = {0, 0, -10};
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 3;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount - 1].dtlReservedCount = writeCount/2 + 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 013 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为同个表，读delta读org的func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns7_rr.A";
    char labelName_mid[] = "ns7_rr.B";
    char labelName_out[] = "ns7_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 014 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为同个表，读delta读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 015 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为不同表，写delta的func的输入表的输入表，读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns10.A";
    char labelName_mid[] = "ns10.B";
    char labelName_out[] = "ns10.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[3] = {1, 2, -10};
    int32_t sum[3] = {0, 0, -10};
    int writeCount = 10;
    int readCount = writeCount + 1;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    Obj_in[writeCount].a = groupId[2];
    Obj_in[writeCount].b = groupId[2];
    Obj_in[writeCount].dtlReservedCount = writeCount/2 + 2;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount + 1;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_mid[writeCount].a = groupId[2];
    Obj_mid[writeCount].b = groupId[2];
    Obj_mid[writeCount].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 3;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 016 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为不同表，读org的func的输入表的输入表，写delta的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns11.A";
    char labelName_mid[] = "ns11.B";
    char labelName_out[] = "ns11.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[3] = {1, 2, -10};
    int32_t sum[3] = {0, 0, -10};
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 3;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount - 1].dtlReservedCount = writeCount/2 + 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 017 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为不同表，读delta的func的输入表的输入表，读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns13.A";
    char labelName_mid[] = "ns13.B";
    char labelName_out[] = "ns13.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 018 新增定义%aggregate验证，选项包含many_to_one同时access_delta和access_current指定为不同表，读org的func的输入表的输入表，读delta的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";
    char labelName_in[] = "ns12.A";
    char labelName_mid[] = "ns12.B";
    char labelName_out[] = "ns12.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = 2;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = groupId[i];
        Obj_out[i].b = sum[i];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 019 新增定义%aggregate验证，选项包含many_to_many同时仅order，函数进行大到小排序
TEST_F(DatalogRunEnhance_test, DataLog_007_003_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns14_max.A";
    char labelName_out[] = "ns14_max.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 020 新增定义%aggregate验证，选项包含many_to_many同时仅order，函数进行小到大排序
TEST_F(DatalogRunEnhance_test, DataLog_007_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns14_min.A";
    char labelName_out[] = "ns14_min.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 021 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，写func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns1_w.A";
    char labelName_mid[] = "ns1_w.B";
    char labelName_out[] = "ns1_w.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    int readCount = writeCount + 1;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    Obj_in[readCount-1].a = -10;
    Obj_in[readCount-1].b = -10;
    Obj_in[readCount-1].dtlReservedCount = 12;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount + 1;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_mid[readCount-1].a = -10;
    Obj_mid[readCount-1].b = -10;
    Obj_mid[readCount-1].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = sizeof(groupOut) / sizeof(int32_t) + 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].a = -10;
    Obj_out[readCount-1].b = -10;
    Obj_out[readCount-1].dtlReservedCount = 3;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 022 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，写规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns3_w.A";
    char labelName_mid[] = "ns3_w.B";
    char labelName_out[] = "ns3_w.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t) + 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].a = -10;
    Obj_out[readCount-1].b = -10;
    Obj_out[readCount-1].dtlReservedCount = 11;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 023 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，读func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns1_r.A";
    char labelName_mid[] = "ns1_r.B";
    char labelName_out[] = "ns1_r.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 024 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，读规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns3_r.A";
    char labelName_mid[] = "ns3_r.B";
    char labelName_out[] = "ns3_r.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 025 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，读写func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns1_wr.A";
    char labelName_mid[] = "ns1_wr.B";
    char labelName_out[] = "ns1_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    int readCount = writeCount + 1;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    Obj_in[readCount-1].a = -10;
    Obj_in[readCount-1].b = -10;
    Obj_in[readCount-1].dtlReservedCount = 12;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount + 1;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_mid[readCount-1].a = -10;
    Obj_mid[readCount-1].b = -10;
    Obj_mid[readCount-1].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = sizeof(groupOut) / sizeof(int32_t) + 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].a = -10;
    Obj_out[readCount-1].b = -10;
    Obj_out[readCount-1].dtlReservedCount = 3;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 026 新增定义%aggregate验证，选项包含many_to_many同时仅access_delta，读写规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns3_wr.A";
    char labelName_mid[] = "ns3_wr.B";
    char labelName_out[] = "ns3_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t) + 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].a = -10;
    Obj_out[readCount-1].b = -10;
    Obj_out[readCount-1].dtlReservedCount = 11;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 027 新增定义%aggregate验证，选项包含many_to_many同时仅access_current，读func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns4.A";
    char labelName_mid[] = "ns4.B";
    char labelName_out[] = "ns4.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 028 新增定义%aggregate验证，选项包含many_to_many同时仅access_current，读规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns6.A";
    char labelName_mid[] = "ns6.B";
    char labelName_out[] = "ns6.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 029 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为同个表，写delta读org的func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns7_wr.A";
    char labelName_mid[] = "ns7_wr.B";
    char labelName_out[] = "ns7_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    int readCount = writeCount + 1;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    Obj_in[readCount-1].a = -10;
    Obj_in[readCount-1].b = -10;
    Obj_in[readCount-1].dtlReservedCount = 12;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount + 1;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_mid[readCount-1].a = -10;
    Obj_mid[readCount-1].b = -10;
    Obj_mid[readCount-1].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = sizeof(groupOut) / sizeof(int32_t) + 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].a = -10;
    Obj_out[readCount-1].b = -10;
    Obj_out[readCount-1].dtlReservedCount = 3;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 030 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为同个表，写delta读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns9_wr.A";
    char labelName_mid[] = "ns9_wr.B";
    char labelName_out[] = "ns9_wr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t) + 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].a = -10;
    Obj_out[readCount-1].b = -10;
    Obj_out[readCount-1].dtlReservedCount = 11;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 031 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为同个表，读delta读org的func的输入表的输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns7_rr.A";
    char labelName_mid[] = "ns7_rr.B";
    char labelName_out[] = "ns7_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 032 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为同个表，读delta读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 033 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为不同表，写delta的func的输入表的输入表，读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns10.A";
    char labelName_mid[] = "ns10.B";
    char labelName_out[] = "ns10.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    int readCount = writeCount + 1;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    Obj_in[readCount-1].a = -10;
    Obj_in[readCount-1].b = -10;
    Obj_in[readCount-1].dtlReservedCount = 12;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    readCount = writeCount + 1;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_mid[readCount-1].a = -10;
    Obj_mid[readCount-1].b = -10;
    Obj_mid[readCount-1].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = sizeof(groupOut) / sizeof(int32_t) + 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount - 1; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].a = -10;
    Obj_out[readCount-1].b = -10;
    Obj_out[readCount-1].dtlReservedCount = 3;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 034 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为不同表，读org的func的输入表的输入表，写delta的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns11.A";
    char labelName_mid[] = "ns11.B";
    char labelName_out[] = "ns11.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t) + 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    Obj_out[readCount-1].a = -10;
    Obj_out[readCount-1].b = -10;
    Obj_out[readCount-1].dtlReservedCount = 11;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 035 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为不同表，读delta的func的输入表的输入表，读org的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns12.A";
    char labelName_mid[] = "ns12.B";
    char labelName_out[] = "ns12.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 036 新增定义%aggregate验证，选项包含many_to_many同时access_delta和access_current指定为不同表，读org的func的输入表的输入表，读delta的规则的左表
TEST_F(DatalogRunEnhance_test, DataLog_007_003_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns13.A";
    char labelName_mid[] = "ns13.B";
    char labelName_out[] = "ns13.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 004 表选项类：transient(tuple)
// 001 新增表选项类transient(tuple)验证，定义表在规则中作输入表包含投影规则
TEST_F(DatalogRunEnhance_test, DataLog_007_004_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_in.d";
    char udfFileName[] = "base_prefile/transient_tuple_in_udf.c";
    char libName[] = "base_prefile/transient_tuple_in.so";
    char nsName[] = "transient_tuple_in";
    char labelName_in[] = "ns1.B";
    char labelName_out[] = "ns1.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增表选项类transient(tuple)验证，定义表在规则中作输入表包含NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_004_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_in.d";
    char udfFileName[] = "base_prefile/transient_tuple_in_udf.c";
    char libName[] = "base_prefile/transient_tuple_in.so";
    char nsName[] = "transient_tuple_in";
    char labelName_in1[] = "ns2.B";
    char labelName_in2[] = "ns2.C";
    char labelName_out[] = "ns2.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 新增表选项类transient(tuple)验证，定义表在规则中作输入表包含聚合规则
TEST_F(DatalogRunEnhance_test, DataLog_007_004_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_in.d";
    char udfFileName[] = "base_prefile/transient_tuple_in_udf.c";
    char libName[] = "base_prefile/transient_tuple_in.so";
    char nsName[] = "transient_tuple_in";
    char labelName_in[] = "ns3.B";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int32_t groupId = 1;
    int32_t sum = 0;
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;

    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);    // aggregate需要org
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 新增表选项类transient(tuple)验证，定义表在规则中作输入表多表join规则中包含普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_004_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_in.d";
    char udfFileName[] = "base_prefile/transient_tuple_in_udf.c";
    char libName[] = "base_prefile/transient_tuple_in.so";
    char nsName[] = "transient_tuple_in";
    char labelName_in1[] = "ns4.B";
    char labelName_in2[] = "ns4.C";
    char labelName_in3[] = "ns4.D";
    char labelName_out[] = "ns4.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in3 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in3[i].a = i;
        Obj_in3[i].b = i;
        Obj_in3[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_in3);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 005 新增表选项类transient(tuple)验证，定义表在规则中作输入表多表join规则中包含function
TEST_F(DatalogRunEnhance_test, DataLog_007_004_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_in.d";
    char udfFileName[] = "base_prefile/transient_tuple_in_udf.c";
    char libName[] = "base_prefile/transient_tuple_in.so";
    char nsName[] = "transient_tuple_in";
    char labelName_in[] = "ns5.B";
    char labelName_out[] = "ns5.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 006 新增表选项类transient(tuple)验证，定义表在规则中作中间表包含投影规则
TEST_F(DatalogRunEnhance_test, DataLog_007_004_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_mid.d";
    char udfFileName[] = "base_prefile/transient_tuple_mid_udf.c";
    char libName[] = "base_prefile/transient_tuple_mid.so";
    char nsName[] = "transient_tuple_mid";
    char labelName_in[] = "ns1.C";
    char labelName_mid[] = "ns1.B";
    char labelName_out[] = "ns1.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 007 新增表选项类transient(tuple)验证，定义表在规则中作中间表包含NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_004_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_mid.d";
    char udfFileName[] = "base_prefile/transient_tuple_mid_udf.c";
    char libName[] = "base_prefile/transient_tuple_mid.so";
    char nsName[] = "transient_tuple_mid";
    char labelName_in1[] = "ns2.D";
    char labelName_in2[] = "ns2.C";
    char labelName_mid[] = "ns2.B";
    char labelName_out[] = "ns2.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 008 新增表选项类transient(tuple)验证，定义表在规则中作中间表包含聚合规则
TEST_F(DatalogRunEnhance_test, DataLog_007_004_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_mid.d";
    char udfFileName[] = "base_prefile/transient_tuple_mid_udf.c";
    char libName[] = "base_prefile/transient_tuple_mid.so";
    char nsName[] = "transient_tuple_mid";
    char labelName_in[] = "ns3.C";
    char labelName_mid[] = "ns3.B";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int32_t groupId = 1;
    int32_t sum = 0;
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 009 新增表选项类transient(tuple)验证，定义表在规则中作中间表多表join规则中包含普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_004_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_mid.d";
    char udfFileName[] = "base_prefile/transient_tuple_mid_udf.c";
    char libName[] = "base_prefile/transient_tuple_mid.so";
    char nsName[] = "transient_tuple_mid";
    char labelName_in1[] = "ns4.C";
    char labelName_in2[] = "ns4.D";
    char labelName_in3[] = "ns4.E";
    char labelName_mid[] = "ns4.B";
    char labelName_out[] = "ns4.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in3 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in3[i].a = i;
        Obj_in3[i].b = i;
        Obj_in3[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_in3);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 010 新增表选项类transient(tuple)验证，定义表在规则中作中间表多表join规则中包含function
TEST_F(DatalogRunEnhance_test, DataLog_007_004_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_mid.d";
    char udfFileName[] = "base_prefile/transient_tuple_mid_udf.c";
    char libName[] = "base_prefile/transient_tuple_mid.so";
    char nsName[] = "transient_tuple_mid";
    char labelName_in[] = "ns5.C";
    char labelName_mid[] = "ns5.B";
    char labelName_out[] = "ns5.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i + 1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 005 表选项类：transient(field)
// 001 新增表选项类transient(field)验证，定义表在规则中作中间表包含投影规则
TEST_F(DatalogRunEnhance_test, DataLog_007_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in[] = "ns1.C";
    char labelName_mid[] = "ns1.B";
    char labelName_out[] = "ns1.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 0;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增表选项类transient(field)验证，定义表在规则中作中间表包含NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in1[] = "ns2.C";
    char labelName_in2[] = "ns2.D";
    char labelName_mid[] = "ns2.B";
    char labelName_out[] = "ns2.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = 0;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 0;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 新增表选项类transient(field)验证，定义表在规则中作中间表包含聚合规则
TEST_F(DatalogRunEnhance_test, DataLog_007_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in[] = "ns3.C";
    char labelName_mid[] = "ns3.B";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 0;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 新增表选项类transient(field)验证，定义表在规则中作中间表多表join规则中包含普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_005_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in1[] = "ns4.C";
    char labelName_in2[] = "ns4.D";
    char labelName_in3[] = "ns4.E";
    char labelName_mid[] = "ns4.B";
    char labelName_out[] = "ns4.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in3 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = 0;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = 0;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in3[i].a = i;
        Obj_in3[i].b = i;
        Obj_in3[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 0;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_in3);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 005 新增表选项类transient(field)验证，定义表在规则中作中间表多表join规则中包含function
TEST_F(DatalogRunEnhance_test, DataLog_007_005_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in[] = "ns5.C";
    char labelName_mid[] = "ns5.B";
    char labelName_out[] = "ns5.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = 0;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 006 表选项类：local
// 001 新增表选项类local验证，定义表%table在规则中作输出表
TEST_F(DatalogRunEnhance_test, DataLog_007_006_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/local_table_out.d";
    char libName[] = "compile_prefile/local_table_out.so";
    char nsName[] = "local_table_out";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增表选项类local验证，定义表%table在规则中作中间表
TEST_F(DatalogRunEnhance_test, DataLog_007_006_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/local_table_mid.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: table \"A\" with option \"local/notify\" should be an output table near line 1.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 003 新增表选项类local验证，定义表%table在规则中作输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_006_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/local_table_in.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: table \"B\" with option \"local/notify\" should be an output table near line 2.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 004 新增表选项类local验证，定义表%table在规则中作输出表，且同时存在shared或者notify
TEST_F(DatalogRunEnhance_test, DataLog_007_006_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/local_AddOther.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "\"local/notify\" can't occur at the same time or occur multiple times in the option of table \"ns2.C\"",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 005 新增表选项类local验证，定义表为%resource
TEST_F(DatalogRunEnhance_test, DataLog_007_006_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/local_resource.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in resource \"rsc0\": local",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 006 新增表选项类local验证，定义表为%function
TEST_F(DatalogRunEnhance_test, DataLog_007_006_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/local_function.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in function \"func\": local",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007 新增表选项类local验证，定义表为%aggregate
TEST_F(DatalogRunEnhance_test, DataLog_007_006_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/local_aggregate.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in aggregate \"funcA\": local",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007 表选项类：shared
// 001 新增表选项类shared验证，定义表%table在规则中作输出表
TEST_F(DatalogRunEnhance_test, DataLog_007_007_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/shared_table_out.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in table \"C\": shared",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 002 新增表选项类shared验证，定义表%table在规则中作中间表
TEST_F(DatalogRunEnhance_test, DataLog_007_007_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/shared_table_mid.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in table \"A\": shared",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 003 新增表选项类shared验证，定义表%table在规则中作输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_007_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/shared_table_in.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in table \"B\": shared",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 004 新增表选项类shared验证，定义表%table在规则中作输出表，且同时存在local或者notify
TEST_F(DatalogRunEnhance_test, DataLog_007_007_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/shared_AddOther.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in table \"ns2.C\": shared",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 005 新增表选项类shared验证，定义表为%resource
TEST_F(DatalogRunEnhance_test, DataLog_007_007_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/shared_resource.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in resource \"rsc0\": shared",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 006 新增表选项类shared验证，定义表为%function
TEST_F(DatalogRunEnhance_test, DataLog_007_007_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/shared_function.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in function \"func\": shared",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007 新增表选项类shared验证，定义表为%aggregate
TEST_F(DatalogRunEnhance_test, DataLog_007_007_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/shared_aggregate.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in aggregate \"funcA\": shared",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008 表选项类：notify
// 001 新增表选项类notify验证，定义表%table在规则中作输出表
TEST_F(DatalogRunEnhance_test, DataLog_007_008_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/notify_table_out.d";
    char libName[] = "compile_prefile/local_table_out.so";
    char nsName[] = "local_table_out";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增表选项类notify验证，定义表%table在规则中作中间表
TEST_F(DatalogRunEnhance_test, DataLog_007_008_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/notify_table_mid.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: table \"A\" with option \"local/notify\" should be an output table near line 1.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 003 新增表选项类notify验证，定义表%table在规则中作输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_008_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/notify_table_in.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: table \"B\" with option \"local/notify\" should be an output table near line 2.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 004 新增表选项类notify验证，定义表%table在规则中作输出表，且同时存在shared或者local
TEST_F(DatalogRunEnhance_test, DataLog_007_008_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/notify_AddOther.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "\"local/notify\" can't occur at the same time or occur multiple times in the option of table \"ns2.C\"",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 005 新增表选项类notify验证，定义表为%resource
TEST_F(DatalogRunEnhance_test, DataLog_007_008_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/notify_resource.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in resource \"rsc0\": notify",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 006 新增表选项类notify验证，定义表为%function
TEST_F(DatalogRunEnhance_test, DataLog_007_008_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/notify_function.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in function \"func\": notify",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007 新增表选项类notify验证，定义表为%aggregate
TEST_F(DatalogRunEnhance_test, DataLog_007_008_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "compile_prefile/notify_aggregate.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: invalid option in aggregate \"funcA\": notify",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009 规则类：notjoin
// 001 新增规则类NOTJOIN验证，规则中全部表字段数一致
TEST_F(DatalogRunEnhance_test, DataLog_007_009_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/notjoin.d";
    char libName[] = "base_prefile/notjoin.so";
    char nsName[] = "notjoin";
    char labelName_in1[] = "ns1.B";
    char labelName_in2[] = "ns1.A";
    char labelName_out[] = "ns1.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增规则类NOTJOIN验证，规则中输入表字段数一致，输出表较少
TEST_F(DatalogRunEnhance_test, DataLog_007_009_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/notjoin.d";
    char libName[] = "base_prefile/notjoin.so";
    char nsName[] = "notjoin";
    char labelName_in1[] = "ns2.B";
    char labelName_in2[] = "ns2.A";
    char labelName_out[] = "ns2.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    SingleInt4St *Obj_out = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 新增规则类NOTJOIN验证，规则中输入表字段数一致，输出表较多且为常数字段
TEST_F(DatalogRunEnhance_test, DataLog_007_009_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/notjoin.d";
    char libName[] = "base_prefile/notjoin.so";
    char nsName[] = "notjoin";
    char labelName_in1[] = "ns3.B";
    char labelName_in2[] = "ns3.A";
    char labelName_out[] = "ns3.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].c = 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 新增规则类NOTJOIN验证，规则中输出表与JOIN表字段数一致，NOT表较少
TEST_F(DatalogRunEnhance_test, DataLog_007_009_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/notjoin.d";
    char libName[] = "base_prefile/notjoin.so";
    char nsName[] = "notjoin";
    char labelName_in1[] = "ns4.B";
    char labelName_in2[] = "ns4.A";
    char labelName_out[] = "ns4.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    SingleInt4St *Obj_in2 = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, SingleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 005 新增规则类NOTJOIN验证，规则中输出表与JOIN表字段数一致，NOT表较多且为忽略字段
TEST_F(DatalogRunEnhance_test, DataLog_007_009_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/notjoin.d";
    char libName[] = "base_prefile/notjoin.so";
    char nsName[] = "notjoin";
    char labelName_in1[] = "ns5.B";
    char labelName_in2[] = "ns5.A";
    char labelName_out[] = "ns5.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_in2 = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].c = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 006 新增规则类NOTJOIN验证，规则中输出表与JOIN表字段数一致，NOT表较多且为常数字段
TEST_F(DatalogRunEnhance_test, DataLog_007_009_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/notjoin.d";
    char libName[] = "base_prefile/notjoin.so";
    char nsName[] = "notjoin";
    char labelName_in1[] = "ns6.B";
    char labelName_in2[] = "ns6.A";
    char labelName_out[] = "ns6.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // TreeInt4St TreeInt4_set TreeInt4_get
    int writeCount = 10;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_in2 = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].c = 1;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 007 新增规则类NOTJOIN验证，规则中输出表与NOT表字段数一致，JOIN表较多且为忽略字段
TEST_F(DatalogRunEnhance_test, DataLog_007_009_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/notjoin.d";
    char libName[] = "base_prefile/notjoin.so";
    char nsName[] = "notjoin";
    char labelName_in1[] = "ns7.B";
    char labelName_in2[] = "ns7.A";
    char labelName_out[] = "ns7.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    ThreeInt4St *Obj_in1 = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].c = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 008 新增规则类NOTJOIN验证，规则中输出表与NOT表字段数一致，JOIN表较多且为常数字段
TEST_F(DatalogRunEnhance_test, DataLog_007_009_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/notjoin.d";
    char libName[] = "base_prefile/notjoin.so";
    char nsName[] = "notjoin";
    char labelName_in1[] = "ns8.B";
    char labelName_in2[] = "ns8.A";
    char labelName_out[] = "ns8.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    ThreeInt4St *Obj_in1 = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].c = 1;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 010 规则类：GROUP-BY
// 001 新增规则类GROUP-BY验证，规则中输出表为transient(tuple)
TEST_F(DatalogRunEnhance_test, DataLog_007_010_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/groupby.d";
    char udfFileName[] = "base_prefile/groupby_udf.c";
    char libName[] = "base_prefile/groupby.so";
    char nsName[] = "groupby";
    char labelName_in[] = "ns2.B";
    char labelName_mid[] = "ns2.C";
    char labelName_out[] = "ns2.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int32_t groupId = 0;
    int32_t sum = 0;
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    Obj_mid[0].a = groupId;
    Obj_mid[0].b = sum;
    Obj_mid[0].dtlReservedCount = 1;
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 新增规则类GROUP-BY验证，规则中输出表为transient(field) (DTS2022092701730)
TEST_F(DatalogRunEnhance_test, DataLog_007_010_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/groupby.d";
    char udfFileName[] = "base_prefile/groupby_udf.c";
    char libName[] = "base_prefile/groupby.so";
    char nsName[] = "groupby";
    char labelName_in[] = "ns3.B";
    char labelName_mid[] = "ns3.C";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int32_t groupId = 0;
    int32_t sum = 0;
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    Obj_mid[0].a = groupId;
    Obj_mid[0].b = sum;
    Obj_mid[0].dtlReservedCount = 1;
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 11;
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 011 特殊场景
// 001 规则中左表出现忽略字段且为resource表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/ignore_field_resource.d";
    char libName[] = "base_prefile/ignore_field_resource.so";
    char nsName[] = "ignore_field_resource";
    char labelName_in[] = "ns1.B";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 规则中右表出现忽略字段且为普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/ignore_field.d";
    char udfFileName[] = "base_prefile/ignore_field_udf.c";
    char libName[] = "base_prefile/ignore_field.so";
    char nsName[] = "ignore_field";
    char labelName_in[] = "ns2.B";
    char labelName_out[] = "ns2.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 规则中右表出现忽略字段且为transient表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/ignore_field.d";
    char udfFileName[] = "base_prefile/ignore_field_udf.c";
    char libName[] = "base_prefile/ignore_field.so";
    char nsName[] = "ignore_field";
    char labelName_in[] = "ns3.B";
    char labelName_mid[] = "ns3.C";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 规则中右表出现忽略字段且为resource表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/ignore_field_resource.d";
    char libName[] = "base_prefile/ignore_field_resource.so";
    char nsName[] = "ignore_field_resource";
    char labelName_in[] = "ns4.B";
    char labelName_mid[] = "ns4.rsc0";
    char labelName_out[] = "ns4.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 005 规则中右表出现忽略字段且为NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_011_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/ignore_field.d";
    char udfFileName[] = "base_prefile/ignore_field_udf.c";
    char libName[] = "base_prefile/ignore_field.so";
    char nsName[] = "ignore_field";
    char labelName_in1[] = "ns5.B";
    char labelName_in2[] = "ns5.C";
    char labelName_out[] = "ns5.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_in2 = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].c = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 006 规则中右表出现忽略字段且为多表JOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_011_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/ignore_field.d";
    char udfFileName[] = "base_prefile/ignore_field_udf.c";
    char libName[] = "base_prefile/ignore_field.so";
    char nsName[] = "ignore_field";
    char labelName_in[] = "ns6.B";
    char labelName_out[] = "ns6.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 007 规则中左表出现常数字段且为普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field.d";
    char udfFileName[] = "base_prefile/const_field_udf.c";
    char libName[] = "base_prefile/const_field.so";
    char nsName[] = "const_field";
    char labelName_in[] = "ns1.B";
    char labelName_out[] = "ns1.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].c = 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 008 规则中左表出现常数字段且为transient表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field.d";
    char udfFileName[] = "base_prefile/const_field_udf.c";
    char libName[] = "base_prefile/const_field.so";
    char nsName[] = "const_field";
    char labelName_in[] = "ns2.B";
    char labelName_mid1[] = "ns2.C";
    char labelName_mid2[] = "ns2.D";
    char labelName_out[] = "ns2.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid1[i].a = i;
        Obj_mid1[i].b = 1;
        Obj_mid1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid2[i].a = i;
        Obj_mid2[i].b = 0;
        Obj_mid2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 1;
        Obj_out[i].c = 0;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid1, Obj_mid1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid2, Obj_mid2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid1);
    free(Obj_mid2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 009 规则中左表出现常数字段且为resource表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field_resource.d";
    char libName[] = "base_prefile/const_field_resource.so";
    char nsName[] = "const_field_resource";
    char labelName_in[] = "ns3.B";
    char labelName_mid[] = "ns3.rsc0";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_mid = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 1;
        Obj_mid[i].c = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = 1;
        Obj_out[i].c = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 010 规则中左表出现常数字段且为NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_011_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field.d";
    char udfFileName[] = "base_prefile/const_field_udf.c";
    char libName[] = "base_prefile/const_field.so";
    char nsName[] = "const_field";
    char labelName_in1[] = "ns4.B";
    char labelName_in2[] = "ns4.C";
    char labelName_out[] = "ns4.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].c = 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 011 规则中左表出现常数字段且为多表JOIN规则 (DTS2022092101435)
TEST_F(DatalogRunEnhance_test, DataLog_007_011_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field.d";
    char udfFileName[] = "base_prefile/const_field_udf.c";
    char libName[] = "base_prefile/const_field.so";
    char nsName[] = "const_field";
    char labelName_in1[] = "ns5.B";
    char labelName_in2[] = "ns5.C";
    char labelName_out[] = "ns5.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount * 2);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].c = 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    for (int i = writeCount, j = 0; i < writeCount * 2; i++, j++) {
        Obj_out[i].a = j;
        Obj_out[i].b = j;
        Obj_out[i].c = 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount*2, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 012 规则中右表出现常数字段且为普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field.d";
    char udfFileName[] = "base_prefile/const_field_udf.c";
    char libName[] = "base_prefile/const_field.so";
    char nsName[] = "const_field";
    char labelName_in[] = "ns6.B";
    char labelName_out[] = "ns6.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    SingleInt4St *Obj_out = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = 1;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 013 规则中右表出现常数字段且为transient表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field.d";
    char udfFileName[] = "base_prefile/const_field_udf.c";
    char libName[] = "base_prefile/const_field.so";
    char nsName[] = "const_field";
    char labelName_in1[] = "ns7.B";
    char labelName_in2[] = "ns7.C";
    char labelName_mid[] = "ns7.D";
    char labelName_out[] = "ns7.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    SingleInt4St *Obj_out = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = 1;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].dtlReservedCount = 2;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 014 规则中右表出现常数字段且为resource表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field_resource.d";
    char libName[] = "base_prefile/const_field_resource.so";
    char nsName[] = "const_field_resource";
    char labelName_in[] = "ns8.B";
    char labelName_mid[] = "ns8.rsc0";
    char labelName_out[] = "ns8.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    SingleInt4St *Obj_out = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    Obj_out[0].a = 1;
    Obj_out[0].dtlReservedCount = 1;
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 015 规则中右表出现常数字段且为NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_011_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field.d";
    char udfFileName[] = "base_prefile/const_field_udf.c";
    char libName[] = "base_prefile/const_field.so";
    char nsName[] = "const_field";
    char labelName_in1[] = "ns9.B";
    char labelName_in2[] = "ns9.C";
    char labelName_out[] = "ns9.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_in2 = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].c = 1;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 016 规则中右表出现常数字段且为多表JOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_011_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/const_field.d";
    char udfFileName[] = "base_prefile/const_field_udf.c";
    char libName[] = "base_prefile/const_field.so";
    char nsName[] = "const_field";
    char labelName_in1[] = "ns10.B";
    char labelName_in2[] = "ns10.C";
    char labelName_out[] = "ns10.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    ThreeInt4St *Obj_in2 = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    ThreeInt4St *Obj_out = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount*2);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].c = 1;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].c = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    for (int i = writeCount, j = 0; i < writeCount*2; i++, j++) {
        Obj_out[i].a = j;
        Obj_out[i].b = j;
        Obj_out[i].c = j + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount*2, ThreeInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 017 左表为null(0)的投影规则
TEST_F(DatalogRunEnhance_test, DataLog_007_011_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/null_table.d";
    char udfFileName[] = "base_prefile/null_table_udf.c";
    char libName[] = "base_prefile/null_table.so";
    char nsName[] = "null_table";
    char labelName_in[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 018 左表为null(0)的NOTJOIN规则
TEST_F(DatalogRunEnhance_test, DataLog_007_011_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/null_table.d";
    char udfFileName[] = "base_prefile/null_table_udf.c";
    char libName[] = "base_prefile/null_table.so";
    char nsName[] = "null_table";
    char labelName_in1[] = "ns2.A";
    char labelName_in2[] = "ns2.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 019 左表为null(0)的聚合规则
TEST_F(DatalogRunEnhance_test, DataLog_007_011_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/null_table.d";
    char udfFileName[] = "base_prefile/null_table_udf.c";
    char libName[] = "base_prefile/null_table.so";
    char nsName[] = "null_table";
    char labelName_in[] = "ns3.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 020 左表为null(0)的多表join规则包含普通表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/null_table.d";
    char udfFileName[] = "base_prefile/null_table_udf.c";
    char libName[] = "base_prefile/null_table.so";
    char nsName[] = "null_table";
    char labelName_in1[] = "ns4.A";
    char labelName_in2[] = "ns4.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 021 左表为null(0)的多表join规则包含transient表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/null_table.d";
    char udfFileName[] = "base_prefile/null_table_udf.c";
    char libName[] = "base_prefile/null_table.so";
    char nsName[] = "null_table";
    char labelName_in1[] = "ns5.A";
    char labelName_in2[] = "ns5.B";
    char labelName_in3[] = "ns5.C";
    char labelName_mid[] = "ns5.D";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    SingleInt4St *Obj_in1 = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in3 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in3[i].a = i;
        Obj_in3[i].b = i;
        Obj_in3[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, SingleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in3, Obj_in3, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_in3);
    free(Obj_mid);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 022 左表为null(0)的多表join规则包含resource表
TEST_F(DatalogRunEnhance_test, DataLog_007_011_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/null_table_resource.d";
    char libName[] = "base_prefile/null_table_resource.so";
    char nsName[] = "null_table_resource";
    char labelName_in1[] = "ns6.A";
    char labelName_in2[] = "ns6.B";
    char labelName_mid[] = "ns6.rsc0";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    SingleInt4St *Obj_in1 = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, SingleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_mid);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 023 左表为null(0)的多表join规则包含function
TEST_F(DatalogRunEnhance_test, DataLog_007_011_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/null_table.d";
    char udfFileName[] = "base_prefile/null_table_udf.c";
    char libName[] = "base_prefile/null_table.so";
    char nsName[] = "null_table";
    char labelName_in1[] = "ns7.A";
    char labelName_in2[] = "ns7.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 024 新增索引扫描算子验证，投影规则中右表全为变量
TEST_F(DatalogRunEnhance_test, DataLog_007_011_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in);
    ret = Debug_executeCommand(g_command, "SeqScan on Label(ns1.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 025 新增索引扫描算子验证，投影规则中右表存在忽略字段
TEST_F(DatalogRunEnhance_test, DataLog_007_011_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in[] = "ns2.A";
    char labelName_out[] = "ns2.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in);
    ret = Debug_executeCommand(g_command, "SeqScan on Label(ns2.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 026 新增索引扫描算子验证，投影规则中右表存在常数字段
TEST_F(DatalogRunEnhance_test, DataLog_007_011_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in[] = "ns3.A";
    char labelName_out[] = "ns3.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in);
    ret = Debug_executeCommand(g_command, "SeqScan on Label(ns3.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 027 新增索引扫描算子验证，NOTJOIN规则中右表全为变量
TEST_F(DatalogRunEnhance_test, DataLog_007_011_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in1[] = "ns4.A";
    char labelName_in2[] = "ns4.C";
    char labelName_out[] = "ns4.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in1);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns4.C) Using Index[0]=0", "SeqScan on Label(ns4.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in2);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns4.A) Using Index[0]=0", "SeqScan on Label(ns4.C)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 028 新增索引扫描算子验证，NOTJOIN规则中右表存在忽略字段
TEST_F(DatalogRunEnhance_test, DataLog_007_011_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in1[] = "ns5.A";
    char labelName_in2[] = "ns5.C";
    char labelName_out[] = "ns5.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in1);
    // 优先主键索引
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns5.C) Using Index[0]=0", "SeqScan on Label(ns5.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in2);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns5.A) Using Index[0]=0", "SeqScan on Label(ns5.C)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 029 新增索引扫描算子验证，NOTJOIN规则中右表存在常数字段
TEST_F(DatalogRunEnhance_test, DataLog_007_011_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in1[] = "ns6.A";
    char labelName_in2[] = "ns6.C";
    char labelName_out[] = "ns6.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in1);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns6.C) Using Index[7]=7", "SeqScan on Label(ns6.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in2);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns6.A) Using Index[0]=0", "SeqScan on Label(ns6.C)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 030 新增索引扫描算子验证，聚合规则中右表全为变量
TEST_F(DatalogRunEnhance_test, DataLog_007_011_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in[] = "ns7.A";
    char labelName_out[] = "ns7.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in);
    ret = Debug_executeCommand(g_command, "InputTable: ns7.A, using index[4]=4", "OutTable: ns7.B, using Index[4]=4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 031 新增索引扫描算子验证，多表join规则中右表全为变量
TEST_F(DatalogRunEnhance_test, DataLog_007_011_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in1[] = "ns8.A";
    char labelName_in2[] = "ns8.C";
    char labelName_out[] = "ns8.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in1);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns8.C) Using Index[0]=0", "SeqScan on Label(ns8.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in2);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns8.A) Using Index[0]=0", "SeqScan on Label(ns8.C)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 032 新增索引扫描算子验证，多表join规则中右表存在忽略字段
TEST_F(DatalogRunEnhance_test, DataLog_007_011_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in1[] = "ns9.A";
    char labelName_in2[] = "ns9.C";
    char labelName_out[] = "ns9.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in1);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns9.C) Using Index[2]=2", "SeqScan on Label(ns9.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in2);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns9.A) Using Index[0]=0", "SeqScan on Label(ns9.C)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 033 新增索引扫描算子验证，多表join规则中右表存在常数字段
TEST_F(DatalogRunEnhance_test, DataLog_007_011_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_scan.d";
    char udfFileName[] = "base_prefile/index_scan_udf.c";
    char libName[] = "base_prefile/index_scan.so";
    char nsName[] = "index_scan";
    char labelName_in1[] = "ns10.A";
    char labelName_in2[] = "ns10.C";
    char labelName_out[] = "ns10.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in1);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns10.C) Using Index[7]=7", "SeqScan on Label(ns10.A)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f TABLE_NAME=\"%s\"\n",
        g_toolPath, viewName, labelName_in2);
    ret = Debug_executeCommand(g_command, "IndexScan on Label(ns10.A) Using Index[0]=0", "SeqScan on Label(ns10.C)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 012 失败场景
// 001 触发规则resource的count字段值为负
TEST_F(DatalogRunEnhance_test, DataLog_007_012_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    SingleInt4St *Obj_in = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, SingleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 调用Gmc接口直接写固定型资源建表
TEST_F(DatalogRunEnhance_test, DataLog_007_012_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int writeCount = 11;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_mid, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(Obj_mid);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 调用Gmc接口直接写transient(field)表
TEST_F(DatalogRunEnhance_test, DataLog_007_012_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in[] = "ns1.C";
    char labelName_mid[] = "ns1.B";
    char labelName_out[] = "ns1.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_mid, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(Obj_mid);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 013 综合场景
// 001 NOTJOIN场景
TEST_F(DatalogRunEnhance_test, DataLog_007_013_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/complex_scene.d";
    char udfFileName[] = "run_prefile/complex_scene_udf.c";
    char libName[] = "run_prefile/complex_scene.so";
    char nsName[] = "complex_scene";
    char labelName_in1[] = "ns1.A";
    char labelName_in2[] = "ns1.C";
    char labelName_out[] = "ns1.B";
    char nsNameTest[] = "complex_scene";

    (void)TestUninstallDatalog(nsNameTest);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 5;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    // JOIN表记录为新增
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // NOT表记录为新增
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // JOIN表count为正，NOT表count为正
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // JOIN表count为正，NOT表count为负
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].dtlReservedCount = 2;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // JOIN表count为负，NOT表count为正
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].dtlReservedCount = 2;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // JOIN表count为负，NOT表count为负
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].dtlReservedCount = 2;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in2[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsNameTest));
    }
}
// 002 聚合场景
TEST_F(DatalogRunEnhance_test, DataLog_007_013_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/complex_scene.d";
    char udfFileName[] = "run_prefile/complex_scene_udf.c";
    char libName[] = "run_prefile/complex_scene.so";
    char nsName[] = "complex_scene";
    char labelName_in[] = "ns2.A";
    char labelName_out[] = "ns2.B";
    char nsNameTest[] = "complex_scene";

    (void)TestUninstallDatalog(nsNameTest);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 5;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);

    int32_t groupId = 1;
    int32_t sum = 0;
    // 新增聚合组
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 修改聚合组，删除记录
    sum = 0;
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = (i == writeCount - 1) ? -1 : 1;
        sum += Obj_in[i].b;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount - 1; i++) {
        Obj_in[i].dtlReservedCount = 2;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum - (writeCount - 1);
    Obj_out[0].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 修改聚合组，新增记录
    sum = 0;
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = (i == writeCount - 1) ? (i + 2) : i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount - 1; i++) {
        Obj_in[i].dtlReservedCount = 3;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除聚合组
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = (i == writeCount - 1) ? (i + 2) : i;
        Obj_in[i].dtlReservedCount = (i == writeCount - 1) ? -1 : -3;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsNameTest));
    }
}
// 003 transient(field)使用场景
TEST_F(DatalogRunEnhance_test, DataLog_007_013_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/complex_scene.d";
    char udfFileName[] = "run_prefile/complex_scene_udf.c";
    char libName[] = "run_prefile/complex_scene.so";
    char nsName[] = "complex_scene";
    char labelName_in[] = "ns3.B";
    char labelName_mid[] = "ns3.C";
    char labelName_out[] = "ns3.A";
    char nsNameTest[] = "complex_scene";

    (void)TestUninstallDatalog(nsNameTest);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 5;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    // transient表记录为新增且count为负
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].dtlReservedCount = -1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // transient表记录为新增且count为正
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // transient表org表记录count为正，delta新增记录count为正
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = 2;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -2;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // transient表org表记录count为正，delta新增记录count为负
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -2;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // transient表org表记录count为负，delta新增记录count为正
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -2;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // transient表org表记录count为负，delta新增记录count为负
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -5;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -2;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsNameTest));
    }
}
// 004 resource使用场景
TEST_F(DatalogRunEnhance_test, DataLog_007_013_004)
{
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/complex_scene.d";
    char udfFileName[] = "run_prefile/complex_scene_udf.c";
    char libName[] = "run_prefile/complex_scene.so";
    char nsName[] = "complex_scene";
    char labelName_in[] = "ns4.A";
    char labelName_mid[] = "ns4.rsc0";
    char labelName_out[] = "ns4.B";
    char nsNameTest[] = "complex_scene";

    (void)TestUninstallDatalog(nsNameTest);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }

    // 申请资源成功
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 申请资源失败
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].b = (i == writeCount - 1) ? -1 : i;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].b = (i == writeCount - 1) ? -1 : i;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源成功
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放后重新申请成功
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 新增输入count为负
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].dtlReservedCount = (i == writeCount - 1) ? -1 : 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 输入count为负，org的count也为负，不影响resource的count值
    for (int i = 0; i < writeCount - 1; i++) {
        Obj_in[i].dtlReservedCount = -10;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    for (int i = 0; i < writeCount - 1; i++) {
        Obj_in[i].dtlReservedCount = -9;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsNameTest));
    }
}
// 005 聚合规则中输出表记录由其他规则产生
TEST_F(DatalogRunEnhance_test, DataLog_007_013_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/complex_scene.d";
    char udfFileName[] = "run_prefile/complex_scene_udf.c";
    char libName[] = "run_prefile/complex_scene.so";
    char nsName[] = "complex_scene";
    char labelName_in1[] = "ns5.A";
    char labelName_in2[] = "ns5.B";
    char labelName_out[] = "ns5.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 5;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);

    int32_t groupId = 1;
    int32_t sum = 0;
    // 非聚合规则写入聚合规则输出表组记录，写聚合规则输入表（记录相同）
    // 写聚合，写非聚合
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = groupId;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
        sum += Obj_in1[i].b;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_in2[0].a = groupId;
    Obj_in2[0].b = sum;
    Obj_in2[0].dtlReservedCount = 1;
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 2;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_get);    //groupId,sum,+2
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 聚合规则输入表，写非聚合规则写入聚合规则输出表组记录（记录相同）
    // 写非聚合，写聚合
    Obj_in2[0].dtlReservedCount = 2;
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    Obj_in2[0].dtlReservedCount = 3;
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_in2[0].dtlReservedCount = -3;
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].dtlReservedCount = -1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);    //groupId,sum,-1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写聚合规则输入表，通过非聚合规则删除新增组记录（count为负）
    // 写聚合，写非聚合
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_in2[0].dtlReservedCount = -5;       // merge后org的count为‘-’的时候，投影count为‘+’，记录已存在，不做投影
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_in2[0].dtlReservedCount = 5;
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);    //groupId,sum,+1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 非聚合规则写入聚合规则输出表，写聚合规则输入表修改聚合组
    // 写非聚合，写聚合
    Obj_in2[0].b = sum + 10;
    Obj_in2[0].dtlReservedCount = 2;
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_get);    //groupId,sum+10,+2
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 修改聚合规则输出表组记录的count值
    // 写聚合，写非聚合
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].a = groupId;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_in2[0].b = sum;
    Obj_in2[0].dtlReservedCount = 10;
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    Obj_in2[1].a = groupId;
    Obj_in2[1].b = sum + 10;
    Obj_in2[1].dtlReservedCount = 2;
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 2, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 2;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        Obj_in1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, 2, DoubleInt4_get);    // groupId,sum+10,+2
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);                                      //groupId,sum,+10
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 006 resource定义表出现在右表
TEST_F(DatalogRunEnhance_test, DataLog_007_013_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/complex_scene.d";
    char udfFileName[] = "run_prefile/complex_scene_udf.c";
    char libName[] = "run_prefile/complex_scene.so";
    char nsName[] = "complex_scene";
    char labelName_in1[] = "ns6.A";
    char labelName_in2[] = "ns6.C";
    char labelName_in3[] = "ns6.D";
    char labelName_mid[] = "ns6.rsc0";
    char labelName_out[] = "ns6.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 5;
    DoubleInt4St *Obj_in1 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_in3 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {  // A
        Obj_in1[i].a = i;
        Obj_in1[i].b = i;
        Obj_in1[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {  // C
        Obj_in2[i].a = i;
        Obj_in2[i].b = i;
        Obj_in2[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {  // D
        Obj_in3[i].a = i;
        Obj_in3[i].b = i;
        Obj_in3[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }

    // 资源表没有数据的时候，触发多表join规则
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源表存在数据的时候，触发多表join规则
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in3, Obj_in3, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_in1);
    free(Obj_in2);
    free(Obj_in3);
    free(Obj_mid);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

// 014 交互场景
// 001 同步单写输入表
TEST_F(DatalogRunEnhance_test, DataLog_007_014_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/normal.d";
    char libName[] = "run_prefile/normal.so";
    char nsName[] = "normal";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.B";
    char labelName_out[] = "ns1.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i + 1;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    int readCount = writeCount;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i + 1;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = writeCount - 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 2;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 固定资源池建表，查看资源池相关视图
TEST_F(DatalogRunEnhance_test, DataLog_007_014_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/complex_scene.d";
    char udfFileName[] = "run_prefile/complex_scene_udf.c";
    char libName[] = "run_prefile/complex_scene.so";
    char nsName[] = "complex_scene";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$CATA_RESOURCE_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s \n",
        g_toolPath, viewName);
    ret = Debug_executeCommand(g_command, "POOL_ID", "RESOURCE_POOL_ALLOC_ORDER");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 规则中存在%function定义函数的使用，视图查看相关的算子
TEST_F(DatalogRunEnhance_test, DataLog_007_014_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s \n",
        g_toolPath, viewName);
    ret = Debug_executeCommand(g_command, "UDF: dtl_ext_func_");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 规则中存在%aggregate定义函数的使用，视图查看相关的算子
TEST_F(DatalogRunEnhance_test, DataLog_007_014_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toOne.d";
    char udfFileName[] = "base_prefile/aggregate_toOne_udf.c";
    char libName[] = "base_prefile/aggregate_toOne.so";
    char nsName[] = "aggregate_toOne";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s \n",
        g_toolPath, viewName);
    ret = Debug_executeCommand(g_command, "Aggregate Function: dtl_agg_func_", "Comparison Function: dtl_agg_compare_");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 005 存在using namespace的语法时，验证规则的准确性
TEST_F(DatalogRunEnhance_test, DataLog_007_014_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/normal.d";
    char libName[] = "run_prefile/normal.so";
    char nsName[] = "normal";
    char labelName_in1[] = "ns1.A2";
    char labelName_in2[] = "ns1.A3";
    char labelName_out[] = "ns1.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int startid = 0;
    int endid = 10;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in1, startid, endid, count, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordId(conn, stmt, labelName_in2, startid, endid, count, ThreeInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in2, startid, endid, count, ThreeInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DoubleInt4St Obj_out[19] = {
        {0, 1, 1}, {1, 1, 2}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1},
        {5, 1, 1}, {6, 1, 1}, {7, 1, 1}, {8, 1, 1}, {9, 1, 1},
        {0, 0, 1}, {2, 2, 1}, {3, 3, 1}, {4, 4, 1},
        {5, 5, 1}, {6, 6, 1}, {7, 7, 1}, {8, 8, 1}, {9, 9, 1}
    };
    ret = readRecord(conn, stmt, labelName_out, &Obj_out, sizeof(Obj_out) / sizeof(DoubleInt4St), DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 006 投影相同列用例
TEST_F(DatalogRunEnhance_test, DataLog_007_014_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/normal.d";
    char libName[] = "run_prefile/normal.so";
    char nsName[] = "normal";
    char labelName_in1[] = "ns2.A";
    char labelName_in2[] = "ns2.C";
    char labelName_out1[] = "B0";
    char labelName_out2[] = "D";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 10;
    DoubleByte8St Obj_in1[writeCount] = {
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x02,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x03,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x04,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x05,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x06,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x07,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x08,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x09,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0a,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 1}
    };
    ret = writeRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleByte8_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in1, Obj_in1, writeCount, DoubleByte8_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int readCount = writeCount;
    DoubleByte8St Obj_out1[readCount] = {
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x02,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x02, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x03,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x03, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x04,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x04, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x05,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x05, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x06,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x06, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x07,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x07, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x08,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x08, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x09,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x09, 1},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0a,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0a, 1}
    };
    ret = readRecord(conn, stmt, labelName_out1, Obj_out1, readCount, DoubleByte8_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    writeCount = 10;
    char strTemp[128] = {};
    StrAndByte8St Obj_in2[writeCount] = {
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x02},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x03},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x04},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x05},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x06},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x07},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x08},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x09},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0a}
    };
    for (int i = 0; i < writeCount; i++) {
        memset(strTemp, 0, sizeof(strTemp));
        snprintf(strTemp, sizeof(strTemp), "test_%04d", i);
        Obj_in2[i].bLen = strlen(strTemp) + 1;
        Obj_in2[i].b = (char *)malloc(Obj_in2[i].bLen);
        memcpy_s(Obj_in2[i].b, Obj_in2[i].bLen, strTemp, Obj_in2[i].bLen);
        Obj_in2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, StrAndByte8_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in2, Obj_in2, writeCount, StrAndByte8_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(Obj_in2[i].b);
    }

    readCount = writeCount;
    Byte8AndDoubleStrSt Obj_out2[readCount] = {
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x02},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x03},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x04},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x05},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x06},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x07},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x08},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x09},
        { 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0a}
    };
    for (int i = 0; i < readCount; i++) {
        memset(strTemp, 0, sizeof(strTemp));
        snprintf(strTemp, sizeof(strTemp), "test_%04d", i);
        Obj_out2[i].bLen = strlen(strTemp) + 1;
        Obj_out2[i].b = (char *)malloc(Obj_out2[i].bLen);
        memcpy_s(Obj_out2[i].b, Obj_out2[i].bLen, strTemp, Obj_out2[i].bLen);
        Obj_out2[i].cLen = strlen(strTemp) + 1;
        Obj_out2[i].c = (char *)malloc(Obj_out2[i].cLen);
        memcpy_s(Obj_out2[i].c, Obj_out2[i].cLen, strTemp, Obj_out2[i].cLen);
        Obj_out2[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out2, Obj_out2, readCount, Byte8AndDoubleStr_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < readCount; i++) {
        free(Obj_out2[i].b);
        free(Obj_out2[i].c);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}

#define RSC_MAXSIZE 100
#define THREAD_NUM_015_001 5
// 015可靠/可用性
// 001 允许的最大固定资源池建表最大resource数量（1个输入表，1个输出表，200个资源表），资源字段max_size设置为最大，并发写输入表至资源字段分配失败
TEST_F(DatalogRunEnhance_test, DataLog_007_015_001) // 待确认
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/resource_maxNum.d";
    char libName[128] = "run_prefile/resource_maxNum.so";
    char nsName[128] = "resource_maxNum";
    char labelName_in[] = "ns007.A";
    char labelName_mid[128] = "ns007.rsc0000";
    char labelName_out[] = "ns007.B";

#if defined ENV_RTOSV2X
    (void)snprintf(libName, sizeof(libName), "%s", "run_prefile/s5710_resource_maxNum.so");
    (void)snprintf(nsName, sizeof(nsName), "%s", "s5710_resource_maxNum");
#endif

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int64_t startid = 0;
    int64_t endid = RSC_MAXSIZE;
    int32_t count = 1;
#if defined ENV_RTOSV2X
    endid = RSC_MAXSIZE / 10;
#endif
    DatalogUserDataT *usrData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    (void)TestWaitDatalogQueue(usrData);
    ret = writeRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_setId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  // 批量超时16004，单写耗时327s
#if defined ENV_RTOSV2X
    endid = RSC_MAXSIZE / 10;
    ret = TestWaitDatalogQueue(NULL, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, usrData->outputQueue.failTimes + (endid - startid));
#else
    ret = TestWaitDatalogQueue(NULL, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, usrData->outputQueue.sucessTimes + (endid - startid));
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(usrData);

    int resourceNum = 200;
#if defined ENV_RTOSV2X
    resourceNum = 200;
#else
    ret = readRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < resourceNum; i++) {
        memset(labelName_mid, 0, sizeof(labelName_mid));
        snprintf(labelName_mid, sizeof(labelName_mid), "ns007.rsc%04d", i);
        ret = readRecordId(conn, stmt, labelName_mid, startid, endid, count, DoubleInt4_getId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    count = resourceNum;
    ret = readRecordId(conn, stmt, labelName_out, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 002 固定资源池建表，资源字段设置为最大，验证全部分配成功 (DTS2022092407924)
TEST_F(DatalogRunEnhance_test, DataLog_007_015_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/resource_maxCount.d";
    char libName[] = "run_prefile/resource_maxCount.so";
    char nsName[] = "resource_maxCount";
    char labelName_in[] = "A";
    char labelName_mid[] = "rsc0";
    char labelName_out[] = "B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int64_t maxSizeR = 1000;    // 最大值 4294967296 - 1，当前需要遍历资源池，最大值执行不完
    ret = writeRecordId(conn, stmt, labelName_in, 0, maxSizeR, 1, SingleInt4_setId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  // 执行太久，考虑使用批量多次提交方式
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in, 0, maxSizeR, 1, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId(conn, stmt, labelName_mid, 0, maxSizeR, 1, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId(conn, stmt, labelName_out, 0, maxSizeR, 1, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 003 写入过程中，使用gmsysview并发查询不同视图
TEST_F(DatalogRunEnhance_test, DataLog_007_015_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/field_maxNum_32.d";
    char libName[] = "run_prefile/field_maxNum_32.so";
    char nsName[] = "field_maxNum_32";
    char labelName_in[] = "B";
    char labelName_out[] = "A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    system("sh runView.sh 10000 &");

    int64_t maxSizeR = 1000;
    ret = writeRecordId(conn, stmt, labelName_in, 0, maxSizeR, 1, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in, 0, maxSizeR, 1, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#if defined ENV_RTOSV2X
    system("kill -9 $(ps| grep runView.sh | grep -v grep | awk '{print $1}')");
#else
    system("kill -9 $(ps -aux| grep runView.sh | grep -v grep | awk '{print $2}')");
#endif

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 004 多表Join过程中一次写入大量的数据（表字段类型为byte或str）
TEST_F(EMPTY_test, DataLog_007_015_004) // DTS2022122109129
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/delate_maxSize.d";
    char libName[] = "run_prefile/delate_maxSize.so";
    char nsName[] = "delate_maxSize";
    char labelName_in[] = "A001";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int32_t count1[500][7] = {{1, 2, 1, 2, 2, 1, 2}};

  int dataNum = 500;
    // 加载so后写一百条数据
    AW_FUN_Log(LOG_STEP, "1.插入并更新数据");
    for (int b = 0; b < dataNum; b++) {
        count1[b][1] = b;
    }
    ret = BatchInsertByteOne(conn, stmt, labelName_in, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

// 005 验证允许变长字段个数规格 (DTS2022092407262)
TEST_F(EMPTY_test, DataLog_007_015_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/field_str_maxNum.d";
    char libName[] = "run_prefile/field_str_maxNum.so";
    char nsName[] = "field_str_maxNum";
    char labelName_in[] = "B";
    char labelName_out[] = "A";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int writeCount = 2;
    char strTemp[1024] = {};
    int offset = 0;
    C1StrSt Obj_in[writeCount] = {};
    for (int i = 0; i < writeCount; i++) {
        offset = 100;
        memset(strTemp, 0, sizeof(strTemp));
        memset(strTemp, 'a', offset);
        snprintf(strTemp + offset, sizeof(strTemp)-offset, "test_%04d", i);
        Obj_in[i].aLen = strlen(strTemp) + 1;
        Obj_in[i].a = (char *)malloc(Obj_in[i].aLen);
        memcpy_s(Obj_in[i].a, Obj_in[i].aLen, strTemp, Obj_in[i].aLen);
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, C1Str_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    for (int i = 0; i < writeCount; i++) {
        free(Obj_in[i].a);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    if (!(g_usrMode)) {
        system("sh $TEST_HOME/tools/stop.sh -f"); // 当前输出队列一直重试入列
    }
}
// 006 验证keybuf大小规则（主键Index 0） (DTS2022092407262)
TEST_F(DatalogRunEnhance_test, DataLog_007_015_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/field_keyBuf_maxSize.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128-offset, "%s", ".c");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s \n",
        g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: index length: 4100 exceeds limit: 532 near line 1.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007 规则中存在表字段个数为上限值64
TEST_F(DatalogRunEnhance_test, DataLog_007_015_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/field_maxNum_32.d";
    char libName[] = "run_prefile/field_maxNum_32.so";
    char nsName[] = "field_maxNum_32";
    char labelName_in[] = "B";
    char labelName_out[] = "A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    int64_t maxSizeR = 1000;
    ret = writeRecordId(conn, stmt, labelName_in, 0, maxSizeR, 1, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in, 0, maxSizeR, 1, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 008 输出表为pubsub，写输入表触发规则运算，在func中验证输出delta表已有数据，主动回滚
TEST_F(DatalogRunEnhance_test, DISABLED_DataLog_007_015_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/pubsubTable.d";
    char libName[] = "run_prefile/pubsubTable.so";
    char nsName[] = "pubsubTable";
    char labelName_in1[] = "ns1.A";
    char labelName_in2[] = "ns1.A2";
    char labelName_in3[] = "ns1.A3";
    char labelName_mid[] = "ns1.B";
    char labelName_out[] = "ns1.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubsubTable.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 0;
    userData->readIdFunc = DoubleInt4_getId;
    userData->startid = 0;
    userData->endid = 60;
    userData->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in3, startid, endid, count, ThreeInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = GmcTransStart(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startid = 0, endid = 50, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_setId, true, false, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, userData->endid - userData->startid, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData->data);
    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 009 输出表为pubsub，写输入表触发规则运算至输入表超过max_size导致失败，在func中验证输出delta数据回滚，主动回滚
TEST_F(DatalogRunEnhance_test, DISABLED_DataLog_007_015_009) // (DTS2022092407262) DTS2023010700714
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/pubsubTable.d";
    char libName[] = "run_prefile/pubsubTable.so";
    char nsName[] = "pubsubTable";
    char labelName_in1[] = "ns1.A";
    char labelName_in2[] = "ns1.A2";
    char labelName_in3[] = "ns1.A3";
    char labelName_mid[] = "ns1.B";
    char labelName_out[] = "ns1.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubsubTable.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 0;
    userData->readIdFunc = DoubleInt4_getId;
    userData->startid = 0;
    userData->endid = 70;
    userData->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = 0;
    int endid = 70;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in2, startid, endid, count, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startid = 0, endid = 50, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_setId, true, false, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startid = 0, endid = 70, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in2, startid, endid, count, SingleInt4_setId, true, false, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startid = 0, endid = 10, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in3, startid, endid, count, ThreeInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 80, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData->data);
    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
// 010 输出表为pubsub，写输入表触发规则运算至输出表超过max_size导致失败，在func中验证输出delta数据回滚，主动回滚
TEST_F(DatalogRunEnhance_test, DISABLED_DataLog_007_015_010) // (DTS2022092407262) DTS2023010700714
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "run_prefile/pubsubTable.d";
    char libName[] = "run_prefile/pubsubTable.so";
    char nsName[] = "pubsubTable";
    char labelName_in1[] = "ns1.A";
    char labelName_in2[] = "ns1.A2";
    char labelName_in3[] = "ns1.A3";
    char labelName_mid[] = "ns1.B";
    char labelName_out[] = "ns1.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"scheam_file/pubsubTable.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 0;
    userData->readIdFunc = DoubleInt4_getId;
    userData->checkIdFunc= DoubleInt4_checkId;
    userData->startid = 0;
    userData->endid = 110;
    userData->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = 0;
    int endid = 110;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startid = 0, endid = 50, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in2, startid, endid, count, SingleInt4_setId, true, false, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId(conn, stmt, labelName_in2, startid, endid, count, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startid = 0, endid = 110, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in1, startid, endid, count, DoubleInt4_setId, true, false, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startid = 0, endid = 10, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in3, startid, endid, count, ThreeInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 120, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData->data);
    free(userData);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(nsName));
    }
}
