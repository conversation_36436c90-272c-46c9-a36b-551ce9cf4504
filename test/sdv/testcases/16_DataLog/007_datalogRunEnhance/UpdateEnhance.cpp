/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: UpdateEnhance.cpp
 * Description: DATALOG SUPPORT fast_insert
 * Author: youwanyong ywx1157510
 * Create: 2024-03-07
 */
#include "UniversalTools.h"
#include "t_datacom_lite.h"

using namespace std;
class UpdateEnhance : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void UpdateEnhance::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void UpdateEnhance::TearDown()
{
    AW_CHECK_LOG_END();
}
// 001.不支持普通输入表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持普通输入表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_001";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.不支持过期非可更新输入表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持过期非可更新输入表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_002";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.不支持TransientTuple输入表表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持TransientTuple输入表表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_003";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.不支持TransientFinish输入表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持TransientFinish输入表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_004";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.不支持普通中间表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持普通中间表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_005";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"mid\" near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.不支持固定资源表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持固定资源表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_006";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option in resource \"rel\": fast_insert near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.不支持pubsub资源表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持pubsub资源表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_007";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option in resource \"mid3\": fast_insert near line 9.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.不支持状态表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持状态表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_008";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "state table only supports index/state/max_size/index0_type/upgrade_version option near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.不支持TransientTuple中间表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持TransientTuple中间表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_009";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"mid\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.不支持TransientFinish中间表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持TransientFinish中间表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_010";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"mid\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.不支持TransientField中间表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持TransientField中间表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_011";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"mid\" near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.不支持普通输出表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持普通输出表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_012";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"out\" near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.不支持外部表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持外部表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_013";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "external table only supports index or external option near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.不支持pubsub表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持pubsub表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_014";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "\"update\" and \"fast_insert\" must occur at the same time in the option of table \"out\" near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.不支持tbm表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持tbm表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_015";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option 'fast_insert' in tbm table \"out\" near line 4.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.不支持消息通知表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持消息通知表含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_016";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option 'fast_insert' in msg notify table \"out\" near line 4.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.不支持.d中同一张表含两个fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持.d中同一张表含两个fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_017";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "duplicate option in table \"inp\": \"fast_insert\"  near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.不支持init uinit中含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持init uinit中含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_018";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret =
        executeCommand(command, "function \"init\" only support access_kv, actually we have fast_insert. near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.不支持function中含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持function中含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_019";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option in function \"func\": fast_insert near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.不支持聚合函数中含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持聚合函数中含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_020";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option in aggregate \"agg\": fast_insert near line 4.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.不支持状态函数中含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持状态函数中含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_021";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option in function \"transfer\": fast_insert near line 4.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.不支持fastinsert关键字
TEST_F(UpdateEnhance, DataLog_007_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持fastinsert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_022";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option in table \"inp\": fastinsert near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.不支持fast insert关键字
TEST_F(UpdateEnhance, DataLog_007_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持fast insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_023";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'insert' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.不支持fast_insertfast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持fast_insertfast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_024";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "invalid option in table \"inp\": fast_insertfast_insert near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.不支持#fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持fast_insertfast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_025";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, invalid character: '#' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.不支持升级时新增输入表含.d中支持多个输入表含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，预期升级成功，阻塞  相关视图，离线及在线";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_026";
    char upgradeFileName[FILE_PATH] = "DataLog_007_026";

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the fast insert table \"New\" is not supported in upgrade create near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.不支持升级时，新增function实现新增fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，预期升级成功，阻塞  相关视图，离线及在线";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_027";
    char upgradeFileName[FILE_PATH] = "DataLog_007_027";

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "invalid option in function \"func\": fast_insert near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.不支持可更新输出表含含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持可更新输出表含含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_028";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "table \"out\" with option \"fast_insert\" should be an input table near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029..d中支持多个输入表（含可更新，部分可更新）含fast_insert关键字
TEST_F(UpdateEnhance, DataLog_007_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = ".d中支持多个输入表（含可更新，部分可更新）含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_029";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030..d中支持多个输入表（含可更新，部分可更新）含fast_insert关键字（带namespace）
TEST_F(UpdateEnhance, DataLog_007_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[150] = ".d中支持多个输入表（含可更新，部分可更新）含fast_insert关键字（带namespace）";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_030";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int8"}
        ]
    } ])";

const char *g_schemaJson1 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int32"}
        ]
    } ])";
// 031.投影规则表中支持含fast_insert关键字：(【输入表：完全可更新表】【 中间表：普通表】【 输出表：普通表】）
TEST_F(UpdateEnhance, DataLog_007_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.join规则表中支持支持含fast_insert关键字：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表】）
TEST_F(UpdateEnhance, DataLog_007_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "join规则表中支持支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_032";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_032.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.笛卡尔规则表中支持含支持含fast_insert关键字:(【输入表：完全可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(UpdateEnhance, DataLog_007_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "笛卡尔规则表中支持含支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_033";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 034.忽略规则表中支持含fast_insert关键字:【输入表：完全可更新】,【中间表: triansent field】【输出表：消息通知表】
TEST_F(UpdateEnhance, DataLog_007_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "忽略规则表中支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_034";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    sleep(1);
    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeBIsInt4TableStruct *objIn1 =
        (Int1TypeBIsInt4TableStruct *)malloc(sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Int1TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1AndBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record inp");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1AndBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadInt1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "3542df29d7a10c951ccd557f59ad4337");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "9c29f193c9a035d3e90ee8a04728a938");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "47abc7e723a58a7aeb3d9b97e6aa9b35");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "abf8aefb53bf4a9156d0c0b41098da10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    sleep(3);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.not join规则表中支持支持含fast_insert关键字:【输入表：完全可更新过期表】,【中间表: tuple表】【输出表：普通表】
TEST_F(UpdateEnhance, DataLog_007_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "not join规则表中支持支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_035";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.srvMemCtxLimit = 2;
    subConnOptions.connName = subConnName;  // MB
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./schema_file/update_pubsub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeBIsInt8TableStruct *objIn1 =
        (Int1TypeBIsInt8TableStruct *)malloc(sizeof(Int1TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(4);
    ret = readRecord("inp", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(4);
    ret = readRecord("inp", objIn1, 10, Int1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Int1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    sleep(4);
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Int1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(4);
    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadInt1AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    ret = GmcUnSubscribe(stmt,subNameUpdate);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.常量规则表中支持支持含fast_insert关键字:【输入表：完全可更新表】,【中间表: finish】【输出表：完全可更新表】
TEST_F(UpdateEnhance, DataLog_007_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "常量规则表中支持支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_036";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeBIsInt4TableStruct *objIn1 =
        (Int1TypeBIsInt4TableStruct *)malloc(sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除成功
    ret = readRecordId("inp", objIn1, 10, ReadInt1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadInt1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = readRecordId("out", objIn1, 10, ReadInt1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.表中支持支持含fast_insert关键字含输入表 join function， access_delta,access_current,access_kv
TEST_F(UpdateEnhance, DataLog_007_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持支持含fast_insert关键字含输入表 join function";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_037";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 038.表中支持支持含fast_insert关键字支持含聚合规则access_delta,access_current,access_kv
TEST_F(UpdateEnhance, DataLog_007_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持支持含fast_insert关键字支持含聚合规则";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_038";
    GmcStmtT *stmt;
    GmcConnT *conn;

    (void)CreateKvTable();

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson1, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeBIsInt4TableStruct *objIn1 =
        (Int1TypeBIsInt4TableStruct *)malloc(sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadInt1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId("out", objIn1, 10, ReadInt1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.阻塞支持升级规则中含fast_insert该关键字表重做查补丁状态视图  PTL_DATALOG_PATCH_INFO
TEST_F(UpdateEnhance, DataLog_007_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = -1;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "DataLog_007_039";
    char soName1[FILE_PATH] = "DataLog_007_039_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";

    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 1024;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].d1 = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 512; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        objIn1[i].gLen = 10;
        objIn1[i].g = strT;
        (void)snprintf((char *)objIn1[i].g, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写表A 1024条数据
    ret = writeRecord(conn, stmt, "A", objIn1, recordNum, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    system("gmsysview count");
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级重做成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = CheckSoState(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t getUpgradeVersion = -1;
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 1;
    }
    GetTableUpgradeVersionByApi((char *)"A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);

    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testGmcGetLastError();
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.非阻塞支持升级规则中含fast_insert该关键字表重做
TEST_F(UpdateEnhance, DataLog_007_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = -1;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "DataLog_007_040";
    char soName1[FILE_PATH] = "DataLog_007_040_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";

    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 1024;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].d1 = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 512; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        objIn1[i].gLen = 10;
        objIn1[i].g = strT;
        (void)snprintf((char *)objIn1[i].g, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写表A 1024条数据
    ret = writeRecord(conn, stmt, "A", objIn1, recordNum, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级重做成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = CheckSoState(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].c = i + 1;
    }
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testGmcGetLastError();
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.非阻塞支持升级udf中含fast_insert该关键字表重做
TEST_F(UpdateEnhance, DataLog_007_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = -1;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "DataLog_007_041";
    char soName1[FILE_PATH] = "DataLog_007_041_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";

    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 1024;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].d1 = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 512; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        objIn1[i].gLen = 10;
        objIn1[i].g = strT;
        (void)snprintf((char *)objIn1[i].g, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写表A 1024条数据
    ret = writeRecord(conn, stmt, "A", objIn1, recordNum, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级重做成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = CheckSoState(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 1;
        objIn1[i].upgradeVersion = 1;
    }

    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 1;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testGmcGetLastError();
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.阻塞支持升级udf中含fast_insert该关键字表重做
TEST_F(UpdateEnhance, DataLog_007_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = -1;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "DataLog_007_042";
    char soName1[FILE_PATH] = "DataLog_007_042_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";

    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 1024;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].d1 = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 512; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        objIn1[i].gLen = 10;
        objIn1[i].g = strT;
        (void)snprintf((char *)objIn1[i].g, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写表A 1024条数据
    ret = writeRecord(conn, stmt, "A", objIn1, recordNum, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级重做成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = CheckSoState(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 1;
    }
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 1;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);

    testGmcGetLastError();
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.非阻塞模式：新增可更新输入表join 带关键字可更新表
TEST_F(UpdateEnhance, DataLog_007_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = -1;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 加载so
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "DataLog_007_043";
    char soName1[FILE_PATH] = "DataLog_007_043_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";

    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 1024;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].d1 = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 512; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        objIn1[i].gLen = 10;
        objIn1[i].g = strT;
        (void)snprintf((char *)objIn1[i].g, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写表A 1024条数据
    ret = writeRecord(conn, stmt, "A", objIn1, recordNum, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级重做成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = CheckSoState(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 1;
    }
    ret = writeRecord(conn, stmt, "A1", objIn1, recordNum, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);

    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("A1", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testGmcGetLastError();
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    free(objIn1);
    // // 卸载so
    ret = TestUninstallDatalog(soName, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.预置数据，对含fast_insert关键字输入表单写写入主键相同其余字段数据相同数据count>0,预期主键冲突，数据写入失败
TEST_F(UpdateEnhance, DataLog_007_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "单写写入主键相同其余字段数据相同数据count>0,预期主键冲突";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 预置5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写5条数据主键冲突
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");
    free(objIn1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.预置数据，对含fast_insert关键字输入表单写写入主键相同其余字段数据不相同数据count>0,预期主键冲突，数据写入失败
TEST_F(UpdateEnhance, DataLog_007_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "单写写入主键相同其余字段数据不相同数据count>0,预期主键冲突";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 预置5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 2) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写5条数据主键冲突
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.预置数据，对含fast_insert关键字输入表批写写入主键相同数据count>0,预期主键冲突，数据写入失败
TEST_F(UpdateEnhance, DataLog_007_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "批写写入主键相同其余字段数据相同count>0,预期主键冲突";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 预置5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写5条数据主键冲突
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.预置数据，对含fast_insert关键字输入表批写写入主键相同其余字段数据不相同数据count>0,预期主键冲突，数据写入失败
TEST_F(UpdateEnhance, DataLog_007_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "批写写入主键相同其余字段数据不相同数据count>0,预期主键冲突";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 预置5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写5条数据主键冲突
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 2) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.预置数据，对含fast_insert关键字输入表单写写入主键相同数据count<0,预期同主键数据被删除
TEST_F(UpdateEnhance, DataLog_007_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "单写写入主键相同数据count<0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 预置5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写5条数据主键冲突
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.预置数据，对含fast_insert关键字输入表批写写入主键相同数据count<0,预期同主键数据被删除
TEST_F(UpdateEnhance, DataLog_007_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "单写写入主键相同数据count<0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 预置5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写5条数据主键冲突
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 050.预置数据，对含fast_insert关键字输入表批写写入主键相同数据count一正一负,预期同主键数据写入成功**
TEST_F(UpdateEnhance, DataLog_007_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "写入主键相同数据count一正一负";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255 % 5;
        objIn1[i].b = (i + 1) % 65535 % 5;
        if (i < 5) {
            objIn1[i].dtlReservedCount = 1;
        } else {
            objIn1[i].dtlReservedCount = -1;
        }
        objIn1[i].upgradeVersion = 0;
    }
    // 预置5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255 % 5;
        objIn1[i].b = (i + 1) % 65535 % 5;
        if (i < 5) {
            objIn1[i].dtlReservedCount = 1;
        } else {
            objIn1[i].dtlReservedCount = -1;
        }
        objIn1[i].upgradeVersion = 0;
    }
    // 批写10条数据release写入冲突，master写入成功
    ret = writeRecord(conn, stmt, "inp", objIn1, 10, Int1TypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.对含fast_insert关键字输入表进行更新操作，后删除，预期成功
TEST_F(UpdateEnhance, DataLog_007_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "含fast_insert关键字输入表进行更新操作，后删除";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键更新数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
template <typename StructObjT>
void readOut(GmcStmtT *stmt, const char *outLabel, int record, StructObjT *obj)
{
    int ret = 0;
    // out表 C
    Int1TypeTableStruct *checkObj = (Int1TypeTableStruct *)obj;
    Int1TypeTableStruct objOut = (Int1TypeTableStruct){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {(char *)outLabel, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);
    int cnt = 0;
    // scan 全表扫
    for (int32_t i = 0; i < record; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t upgradeVersion = 0;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t valueA = i + 1;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &valueA, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(0, ret);
            AW_MACRO_EXPECT_EQ_INT(checkObj[i].b, objOut.b);
            cnt++;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    deSeriFreeDynMem(&deseriCtx, true);
}
// 052.对含fast_insert关键字输入表进行结构化写，结构化主建读操作，后删除，预期成功
TEST_F(UpdateEnhance, DataLog_007_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "输入表进行结构化写，结构化主建读操作，后删除";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_031";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构化主键读
    readOut(stmt, "inp", 5, objIn1);

    // 主键删除数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.可更新表，含fast_insert关键字，通过比较函数进行更新，留大(实际报错)
TEST_F(UpdateEnhance, DataLog_007_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    char testcasesInfo[100] = "忽略规则表中支持含支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_034";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    sleep(1);
    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeBIsInt4TableStruct *objIn1 =
        (Int1TypeBIsInt4TableStruct *)malloc(sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 10) % 255;
        objIn1[i].b = (i + 10) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    system("gmsysview record inp");
    system("gmsysview record inp1");
    ret = readRecord("inp", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 10) % 255;
        objIn1[i].b = (i + 10) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1, 5, UpdateInt1AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record inp");
    system("gmsysview record inp1");
    // 读数据
    ret = readRecord("inp1", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.可更新表，含fast_insert关键字，通过比较函数进行更新，留小(实际报错)
TEST_F(UpdateEnhance, DataLog_007_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "忽略规则表中支持含支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_034";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    sleep(1);
    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeBIsInt4TableStruct *objIn1 =
        (Int1TypeBIsInt4TableStruct *)malloc(sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 10) % 255;
        objIn1[i].b = (i + 10) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    system("gmsysview record inp");
    system("gmsysview record inp1");
    ret = readRecord("inp", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 10) % 255;
        objIn1[i].b = (i + 10) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1, 5, UpdateInt1AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record inp");
    system("gmsysview record inp1");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.不支持 可更新输入表含fast_insert关键字 union_delete规则
TEST_F(UpdateEnhance, DataLog_007_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持 可更新输入表含fast_insert关键字 union_delete规则";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_055";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "\"union_delete\" and \"fast_insert\" can not occur at the same time in the option "
                                  "of table \"inp1\" near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056.批写4K条数据，预期成功，再次批写4K条数据含同主键count>0数据，预期主键冲突
TEST_F(UpdateEnhance, DataLog_007_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "忽略规则表中支持含支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_056";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    sleep(1);
    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 4 * 1024;
    char strT[10] = {0};
    Int1TypeBIsInt4TableStruct *objIn1 =
        (Int1TypeBIsInt4TableStruct *)malloc(sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 10) % 255;
        objIn1[i].b = (i + 10) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 批写4K条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, Int1TypeAndBIsInt4TableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, recordNum, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次批写相同数据报主键冲突
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, Int1TypeAndBIsInt4TableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 读数据
    ret = readRecord("inp", objIn1, recordNum, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, recordNum, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.支持使用gmimport导入含fast_insert关键字输入表数据
TEST_F(UpdateEnhance, DataLog_007_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "忽略规则表中支持含支持含fast_insert关键字";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_056";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    sleep(1);
    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 10;
    char strT[10] = {0};
    Int1TypeBIsInt4TableStruct *objIn1 =
        (Int1TypeBIsInt4TableStruct *)malloc(sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = 0;
        objIn1[i].b = i % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 批写4K条数据
    (void)snprintf(command, MAX_CMD_SIZE,
        "%s/gmimport -c vdata -f ./schema_file/DataLog_007_057.gmdata -t inp -s %s -ns %s", g_toolPath, g_connServer,
        g_testNameSpace);
    ret = executeCommand(command, (char *)"Command type: import_vdata", (char *)"successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, recordNum, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, recordNum, Int1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.支持gmconvert转换含fast_insert关键字.d为gmjson
TEST_F(UpdateEnhance, DataLog_007_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // gmconvert支持转换含所有数据类型.d为gmjson文件
    char inPath[256] = "./datalog_file/DataLog_007_031.d";
    char outPath[256] = "out_Convert_gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "gmconvert -i %s -o %s", inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    int ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验inp.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/inp.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson1, result));
    free(result);

    // 校验mid.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/mid.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson2, result));
    free(result);

    // 校验out.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/out.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson3, result));
    free(result);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *ThreadWriteData(void *args)
{
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    GmcStmtT *stmt;
    GmcConnT *conn;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int32_t g_cycle = 10;
void *QueryView(void *args)
{
    char command[MAX_CMD_SIZE] = {0};
    sleep(3);
    // 需确保该so在执行才能查到该视图
    char const *viewName = "V\\$COM_DTL_UDF_DYN_CTX";
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s", viewName);
    int ret = executeCommand(
        command, "CTX_NAME: dtl_ext_func_func", "PARENT_CTX_NAME: datalog", "CTX_ID", "THRESHOLD", "TOTAL_PHY_SIZE");
    if (ret == 0 || g_cycle == 0) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_cycle = 0;
    }

    ret = executeCommand(command, "TOTAL_PHY_SIZE", "TOTAL_ALLOC_SIZE", "MEMORY_USAGE", NULL, NULL);
    if (ret == 0) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
// 059.加载含udf so  V\$COM_DTL_UDF_DYN_CTX
TEST_F(UpdateEnhance, DataLog_007_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "加载含udf so  V\\$COM_DTL_UDF_DYN_CTX";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_059";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    g_cycle = 3;
    // 加载so
    while (g_cycle > 0) {
        g_cycle--;
        AW_FUN_Log(LOG_STEP, "g_cycle is %d.", g_cycle);
        ret = LoadSoFile(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestYangGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char schemaJson[1024] = "";
        (void)sprintf(schemaJson, g_schemaJson, "inp");
        ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        int recordNum = 100;
        char strT[10] = {0};
        Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
        if (objIn1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
        for (int i = 0; i < recordNum; i++) {
            objIn1[i].a = (i + 1) % 255;
            objIn1[i].b = (i + 1) % 65535;
            objIn1[i].dtlReservedCount = 1;
            objIn1[i].upgradeVersion = 0;
        }

        Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
        if (out1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
        }
        memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
        for (int i = 0; i < recordNum; i++) {
            out1[i].a = (i + 2) % 255;
            out1[i].b = (i + 1) % 65535;
            out1[i].dtlReservedCount = 1;
            out1[i].upgradeVersion = 0;
        }
        // 普通单写5条数据
        // 多线程并发
        int32_t threadNum = 2;
        pthread_t thr_arr[threadNum];
        void *thr_ret[threadNum];
        int index[threadNum];
        // 写so1
        pthread_create(&thr_arr[0], NULL, ThreadWriteData, NULL);
        pthread_create(&thr_arr[1], NULL, QueryView, NULL);
        for (int i = 0; i < threadNum; i++) {
            pthread_join(thr_arr[i], &thr_ret[i]);
        }
        system("gmsysview count");
        ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 结构单写5条数据
        ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("gmsysview count");
        ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键删除前5条数据
        ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键读预期删除成功
        ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int i = 0; i < recordNum; i++) {
            objIn1[i].a = (i + 1) % 255;
            objIn1[i].b = (i + 1) % 5;
            objIn1[i].dtlReservedCount = 1;
            objIn1[i].upgradeVersion = 0;
        }

        for (int i = 0; i < recordNum; i++) {
            out1[i].a = (i + 2) % 255;
            out1[i].b = (i + 1) % 5;
            out1[i].dtlReservedCount = 1;
            out1[i].upgradeVersion = 0;
        }

        // 主键更新后5条数据
        ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 读数据
        ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 结构批写
        ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  二级索引删除
        ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 二级索引读,预期数据都被删除
        ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char cmdView[100] = "gmsysview -q V\\$COM_DTL_UDF_DYN_CTX";
        system(cmdView);
        ret = executeCommand(cmdView, "fetched all records, finish!");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        system("gmsysview count inp");
        system("gmsysview count out");
        free(objIn1);
        free(out1);

        // 卸载so
        ret = TestUninstallDatalog(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060.加载so   V\$CATA_VERTEX_LABEL_INFO
TEST_F(UpdateEnhance, DataLog_007_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "加载含udf so  V\\$CATA_VERTEX_LABEL_INFO";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_037";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmdView[100] = "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO";
    system(cmdView);
    ret = executeCommand(cmdView, "VERTEX_LABEL_NAME: inp", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE",
        "VERTEX_LABEL_TYPE: VERTEX_TYPE_DATALOG");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061载so 写入数据查询   V\$QRY_DML_INFO          V\$QRY_DML_OPER_STATIS
TEST_F(UpdateEnhance, DataLog_007_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "加载含udf so  V\\$QRY_DML_INFO, QRY_DML_OPER_STATIS";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_037";
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    GmcStmtT *stmt;
    GmcConnT *conn;
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmdView[100] = "gmsysview -q V\\$QRY_DML_OPER_STATIS";
    system(cmdView);
    ret = executeCommand(cmdView, "LABEL_NAME: inp", "DML_TYPE: DELETE_VERTEX", "SUCCESS_COUNT: 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = executeCommand(cmdView, "LABEL_NAME: inp", "DML_TYPE: UPDATE_VERTEX", "SUCCESS_COUNT: 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = executeCommand(cmdView, "LABEL_NAME: inp", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmdView1[100] = "gmsysview -q V\\$QRY_DML_INFO";
    system(cmdView1);
    ret = executeCommand(cmdView1, "sysview get records unsucc, ret = 1012000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062.加载so 查询（包含离线）执行计划视图  V\$DATALOG_PLAN_EXPLAIN_INFO
TEST_F(UpdateEnhance, DataLog_007_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "加载含udf so  V\\$DATALOG_PLAN_EXPLAIN_INFO";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_037";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmdView[100] = "gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO";
    system(cmdView);
    ret = executeCommand(
        cmdView, "TABLE_NAME: inp", "InsertTable on Label(fake_out)", "Output: dtlReservedCount, upgradeVersion, a, b");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063.写入数据，记录数视图：gmsysview count
TEST_F(UpdateEnhance, DataLog_007_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "加载含udf so  V\\$count";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_037";
    GmcStmtT *stmt;
    GmcConnT *conn;
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmdView[100] = "gmsysview count inp";
    system(cmdView);
    ret = executeCommand(cmdView, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064.写入数据，查询表中数据的视图  gmsysview record
TEST_F(UpdateEnhance, DataLog_007_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "加载含udf so  V\\$record";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_037";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview record inp -s %s -ns %s", g_toolPath, g_connServer, g_testNameSpace);
    system(command);
    ret = executeCommand(command, "\"dtlReservedCount\": 1", "\"upgradeVersion\": 0", "\"a\": 10,", "\"b\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065.写入数据，查询表中数据的视图 V\$STORAGE_VERTEX_COUNT
TEST_F(UpdateEnhance, DataLog_007_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "加载含udf so  V\\$STORAGE_VERTEX_COUNT";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_037";
    GmcStmtT *stmt;
    GmcConnT *conn;

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_VERTEX_COUNT -s %s", g_toolPath, g_connServer);
    system(command);
    ret = executeCommand(command, "inp", "record count: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 066.加载so，查询so视图  PTL_DATALOG_SO_INFO
TEST_F(UpdateEnhance, DataLog_007_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "加载含udf so  V\\$PTL_DATALOG_SO_INFO";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_007_037";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_007_037.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Int1TypeTableStruct *objIn1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Int1TypeTableStruct *out1 = (Int1TypeTableStruct *)malloc(sizeof(Int1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Int1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Int1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadInt1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateInt1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Int1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Int1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Int1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteInt1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadInt1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmdView[100] = "gmsysview -q V\\$PTL_DATALOG_SO_INFO>test.txt";
    system(cmdView);
    ret = executeCommand("cat test.txt", "SO_NAME: DataLog_007_037", "TABLE_NAME: inp", "TABLE_TYPE: DM_DTL_UPDATE");
    if (ret != GMERR_OK) {
        system("cat test.txt");
        ret = executeCommand("cat test.txt", "SO_NAME: DataLog_007_037", "TABLE_NAME: fake_out,inp", "TABLE_TYPE: DM_DTL_EXTERN,DM_DTL_UPDATE");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf  ./test.txt");

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *ThreadWriteSameData(void *args)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    GmcStmtT *stmt;
    GmcConnT *conn;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 10;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].d1 = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 512; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        objIn1[i].gLen = 10;
        objIn1[i].g = strT;
        (void)snprintf((char *)objIn1[i].g, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写表A 10条数据
    ret = writeRecord(conn, stmt, "A", objIn1, recordNum, AllTypeTableSet, false);
#ifdef RUN_SIMULATE
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
#elif defined ENV_RTOSV2X
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
#endif
    free(objIn1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067.非阻塞模式下，多线程，重做时，DMl线程写主键相同数据，预期DML写入失败
TEST_F(UpdateEnhance, DataLog_007_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg, errorMsg1, errorMsg2);

    int ret = -1;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "DataLog_007_067";
    char soName1[FILE_PATH] = "DataLog_007_067_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";

    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 10;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].d1 = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 512; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        objIn1[i].gLen = 10;
        objIn1[i].g = strT;
        (void)snprintf((char *)objIn1[i].g, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写表A 10条数据
    ret = writeRecord(conn, stmt, "A", objIn1, recordNum, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    system("gmsysview count");
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级重做成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    // 多线程并发
    int32_t threadNum = 1;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];
    // 写so1
    pthread_create(&thr_arr[0], NULL, ThreadWriteSameData, NULL);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = CheckSoState(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t getUpgradeVersion = -1;
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 1;
        objIn1[i].upgradeVersion = 1;
    }
    GetTableUpgradeVersionByApi((char *)"A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);

    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testGmcGetLastError();
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 068.非阻塞模式下，允许并发 多线程，重做时，DMl线程写主键相同数据，预期DML写入失败
TEST_F(UpdateEnhance, DataLog_007_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg, errorMsg1, errorMsg2);

    int ret = -1;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // 加载so
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "DataLog_007_067";
    char soName1[FILE_PATH] = "DataLog_007_067_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";

    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 10;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].d1 = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 512; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        objIn1[i].gLen = 10;
        objIn1[i].g = strT;
        (void)snprintf((char *)objIn1[i].g, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 单写表A 10条数据
    ret = writeRecord(conn, stmt, "A", objIn1, recordNum, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    system("gmsysview count");
    ret = readRecord("A", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].c = i + 2;
    }
    ret = readRecord("C", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count A -ns %s -s %s", g_toolPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级重做成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    // 多线程并发
    int32_t threadNum = 1;
    int index[threadNum];
    // 写so1
    ret = -1;
    while (ret) {
        ret = writeRecord(conn, stmt, "A", objIn1 + 5, 1, AllTypeTableSet, false);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = CheckSoState(cmd, "sysview connect unsucc, ret = 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
