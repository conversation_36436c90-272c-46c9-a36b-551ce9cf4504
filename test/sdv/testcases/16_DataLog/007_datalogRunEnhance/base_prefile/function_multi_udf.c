/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#include "gm_udf.h"

#pragma pack(1)

typedef struct A {
    int32_t count;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} A;

typedef struct B {
    int32_t count;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} B;

typedef struct Func {
    int32_t count;
    int32_t a;
    int32_t b;
} Func;

#pragma pack(0)


int32_t dtl_ext_func_ns1_f00(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f01(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f02(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f03(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f04(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f05(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f06(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f07(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f08(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f09(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f10(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f11(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f12(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f13(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f14(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;

    return GMERR_OK;
}
int32_t dtl_ext_func_ns1_f15(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_ns2_f00(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_ns3_f00(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;

    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {};
    a0.a = funcB->a + 10;
    a0.b = funcB->a;
    a0.c = funcB->a;
    a0.count = 1;
    a0.upgradeVersion = 0;
    ret = GmUdfAppend(writer, sizeof(A), &a0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}
int32_t dtl_ext_func_ns3_f01(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;

    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {};
    a0.a = funcB->a;
    a0.b = funcB->a + 10;
    a0.c = funcB->a;
    a0.count = 1;
    a0.upgradeVersion = 0;
    ret = GmUdfAppend(writer, sizeof(A), &a0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}
int32_t dtl_ext_func_ns3_f02(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;

    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {};
    a0.a = funcB->a;
    a0.b = funcB->a;
    a0.c = funcB->a + 10;
    a0.count = 1;
    a0.upgradeVersion = 0;
    ret = GmUdfAppend(writer, sizeof(A), &a0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

int32_t dtl_ext_func_ns4_f00(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns4_f01(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns4_f02(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns4_f03(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns4_f04(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns4_f05(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns4_f06(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
int32_t dtl_ext_func_ns4_f07(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a;
    return GMERR_OK;
}
