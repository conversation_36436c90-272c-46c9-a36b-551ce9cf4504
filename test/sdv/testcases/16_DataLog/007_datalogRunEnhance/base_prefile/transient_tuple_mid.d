// 1.project
namespace ns1{
%table A(a: int4, b: int4)
%table B(a: int4, b: int4){ transient(tuple) }
%table C(a: int4, b: int4)

B(a, b) :- C(a, b).
A(a, b) :- B(a, b).
}

// 2.not join
namespace ns2{
%table A(a: int4, b: int4)
%table B(a: int4, b: int4){ transient(tuple) }
%table C(a: int4, b: int4)
%table D(a: int4, b: int4)

B(a, b) :- D(a, b).
A(a, b) :- B(a, b), NOT C(a, b).
}

// 3.aggregate
namespace ns3{
%table A(a: int4, b: int4)
%table B(a: int4, b: int4){ transient(tuple) }
%aggregate funcA(a: int4 -> b: int4)
%table C(a: int4, b: int4)

B(a, b) :- C(a, b).
A(a, c) :- B(a, b) GROUP-BY (a) funcA(b, c).
null(0) :- A(a, c) .
}

// 4.join table
namespace ns4{
%table A(a: int4, b: int4)
%table B(a: int4, b: int4){ transient(tuple) }
%table C(a: int4, b: int4)
%table D(a: int4, b: int4)
%table E(a: int4, b: int4)

B(a, b) :- E(a, b).
A(a, b) :- B(a, b), C(a, b), D(a, b).
}

// 5.join function
namespace ns5{
%table A(a: int4, b: int4)
%table B(a: int4, b: int4){ transient(tuple) }
%table C(a: int4, b: int4)
%function func(a: int4 -> b: int4)

B(a, b) :- C(a, b).
A(a, c) :- B(a, b), func(b, c).
}
