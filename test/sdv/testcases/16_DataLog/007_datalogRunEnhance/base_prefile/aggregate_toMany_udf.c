/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t count;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t count;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} B;

typedef struct C {
    int32_t count;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} C;

typedef struct FuncAggIn {
    int32_t count;
    int32_t a;
} FuncAggIn;

typedef struct FuncAggOut {
    int32_t b;
} FuncAggOut;

#pragma pack(0)

// 1.many_to_many with only access_delta, opera input's input table
int32_t dtl_agg_func_ns1_w_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }

    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // write input's input table
    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(A), &a);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

int32_t dtl_agg_func_ns1_r_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read input's input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_ns1_wr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read and write input's input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
        sum_a += a->a;
        sum_b += a->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(A), &a0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

// 2.many_to_many with only access_delta, opera input table
int32_t dtl_agg_func_ns2_w_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // write input table
    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    B b = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(B), &b);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

int32_t dtl_agg_func_ns2_r_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_ns2_wr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read and write input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
        sum_a += b->a;
        sum_b += b->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    B b0 = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(B), &b0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

// 3.many_to_many with only access_delta, opera left table
int32_t dtl_agg_func_ns3_w_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // write left table
    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    C c = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(C), &c);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

int32_t dtl_agg_func_ns3_r_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read left table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    C *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_ns3_wr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read and write left table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    C *c = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
        sum_a += c->a;
        sum_b += c->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    C c0 = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(C), &c0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

// 4.many_to_many with only access_current, opera input's input table
int32_t dtl_agg_func_ns4_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read input's input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 5.many_to_many with only access_current, opera input table
int32_t dtl_agg_func_ns5_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 6.many_to_many with only access_current, opera left table
int32_t dtl_agg_func_ns6_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read left table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    C *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 7.many_to_many with same table, opera input's input table
int32_t dtl_agg_func_ns7_wr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read and write input's input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
        sum_a += a->a;
        sum_b += a->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(A), &a0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

int32_t dtl_agg_func_ns7_rr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read input's input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 8.many_to_many with same table, opera input table
int32_t dtl_agg_func_ns8_wr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read and write input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
        sum_a += b->a;
        sum_b += b->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    B b0 = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(B), &b0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

int32_t dtl_agg_func_ns8_rr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 9.many_to_many with same table, opera left table
int32_t dtl_agg_func_ns9_wr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read and write left table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    C *c = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
        sum_a += c->a;
        sum_b += c->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    C c0 = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(C), &c0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

int32_t dtl_agg_func_ns9_rr_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read and write left table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    C *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 10.many_to_many with diff table, write delta input's input table, other read org
int32_t dtl_agg_func_ns10_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read org input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;

    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
        sum_a += b->a;
        sum_b += b->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    // read org left table
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    C *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
        sum_a += c->a;
        sum_b += c->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    // write input's input table
    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10};
    ret = GmUdfAppend(writer, sizeof(A), &a0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

// 11.many_to_many with diff table, write delta left table, other read org
int32_t dtl_agg_func_ns11_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read org input's input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;

    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
        sum_a += a->a;
        sum_b += a->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    // read org input table
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
        sum_a += b->a;
        sum_b += b->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    // write left table
    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    C c = {.count = 1, .upgradeVersion = 0, .a = -10, .b = -10, .count = 1};
    ret = GmUdfAppend(writer, sizeof(C), &c);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

// 12.many_to_many with diff table, read org input's input table, other read delta
int32_t dtl_agg_func_ns12_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read delta input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    // read delta left table
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    C *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    // read org input's input table
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 13.many_to_many with diff table, read org left table, other read delta
int32_t dtl_agg_func_ns13_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    // read delta input table
    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&b), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    // read delta input's input table
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    C *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    // read org left table
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

// 14.many_to_many with ordered
int32_t dtl_agg_compare_ns14_max_funcA(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    FuncAggIn *inp1 = (FuncAggIn *)tuple1;
    FuncAggIn *inp2 = (FuncAggIn *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}

int32_t dtl_agg_func_ns14_max_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

int32_t dtl_agg_compare_ns14_min_funcA(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    FuncAggIn *inp1 = (FuncAggIn *)tuple1;
    FuncAggIn *inp2 = (FuncAggIn *)tuple2;
    if (inp1->a > inp2->a) {
        return -1;
    } else if (inp1->a < inp2->a) {
        return 1;
    } else {
        return 0;
    }
}

int32_t dtl_agg_func_ns14_min_funcA(GmUdfReaderT *input, GmUdfWriterT *output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    int32_t sum, max, min;
    if (ret == GMERR_NO_DATA) {
        sum = max = min = 0;
    } else {
        sum = max = min = inpStruct->a;
    }
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
        max = (max >= inpStruct->a) ? max : inpStruct->a;
        min = (min <= inpStruct->a) ? min : inpStruct->a;
    }
    outStruct->b = sum;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = max;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    outStruct->b = min;
    ret = GmUdfAppend(output, sizeof(FuncAggOut), outStruct);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}
