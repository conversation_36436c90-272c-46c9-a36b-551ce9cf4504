/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: UniversalTools.h
 * Description: tool
 * Author: youwanyong ywx1157510
 * Create: 2024-01-04
 */

#ifndef __UNIVERSALTOOL_H__
#define __UNIVERSALTOOL_H__

#include "t_datacom_lite.h"
#include "StructDatalogTable.h"

#define FILE_PATH 512
#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 512

char g_command[MAX_CMD_SIZE] = {0};
char g_tableName[128] = "cap";
char g_outputDir[FILE_PATH] = "datalog_file";
char g_gmconvertPath[256] = "${TEST_HOME}/../../build/bin";
char g_config<PERSON><PERSON>[128] = "{\"max_record_count\":100000, \"isFastReadUncommitted\": false}";
typedef int (*FuncWrite)(GmcStmtT *stmt, void *t, bool isStateTableInput);
typedef int (*FuncWriteId)(GmcStmtT *stmt, int64_t value, int32_t count);
typedef int (*FuncRead)(GmcStmtT *stmt, void *t, int len, bool external, bool isResource);
typedef int (*FuncReadId)(GmcStmtT *stmt, int startid, int endid, int32_t count);
typedef int (*FuncReadSub)(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc);

// 加载so文件
int LoadSoFile(const char *soName)
{
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, FILE_PATH, "./%s/%s.so", g_outputDir, soName);
    return TestLoadDatalog(g_command);
}

#pragma pack(1)
typedef struct {
    int32_t tabelMemtexValue;
    int32_t udfMemtexValue;
    int32_t planListMemtexValue;
} DatalogCtxValue;
#pragma pack(0)

/*
获取datalog 相关ctx value
表:COM_SHMEM_CTX catalog share memory context
udf:COM_DYN_CTX catalog dynamic memory context
plan list:COM_DYN_CTX datalog plan list memCtx
*/
void GetDatalogMemCtxValue(int32_t *tabelMemtexValue, int32_t *udfMemtexValue, int32_t *planListMemtexValue)
{
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME='catalog share memory context'|grep TOTAL_ALLOC_SIZE:|sed "
        "'s/[^0-9]//g'");
    TestGetResultCommand(command, tabelMemtexValue, NULL, 0);
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME='catalog share memory context'");
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='catalog dynamic memory context'|grep TOTAL_ALLOC_SIZE:|sed "
        "'s/[^0-9]//g'");
    TestGetResultCommand(command, udfMemtexValue, NULL, 0);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog plan list memCtx'|grep TOTAL_ALLOC_SIZE:|sed 's/[^0-9]//g'");
    TestGetResultCommand(command, planListMemtexValue, NULL, 0);
    return;
}

int CompareMemCtxValue(DatalogCtxValue *memctxValue1, DatalogCtxValue *memctxValue2)
{
    char command[MAX_CMD_SIZE] = {0};
    if (memctxValue1->tabelMemtexValue != memctxValue2->tabelMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "【ERROR】tableMemctx is InCrease!!!");
        return 1;
    }
    if (memctxValue1->udfMemtexValue != memctxValue2->udfMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "【ERROR】udfMemtex is InCrease!!!");
        return 1;
    }
    if (memctxValue1->planListMemtexValue != memctxValue2->planListMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "【ERROR】planListMemtex is InCrease!!!");
        return 1;
    }
    return GMERR_OK;
}

void SystemSnprintf(const char *format, ...)
{
    va_list p;
    va_start(p, format);
    (void)vsnprintf(g_command, MAX_CMD_SIZE, format, p);
    va_end(p);
    system(g_command);
}

// 设置keyID
typedef int32_t (*FuncDeleteByKeyId)(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion);
// read by keyID
typedef int32_t (*FuncReadByKeyId)(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion);

template <typename StructObjT>
int writeRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, int res1 = GMERR_OK, bool isStateTabelInput = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t maxBatchOpNum = 4;  // 暂时降到4k
        ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, maxBatchOpNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    for (int i = 0; i < objLen; i++) {
        if (isStruct) {
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i), isStateTabelInput);
        }
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = GmcExecute(stmt);
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

template <typename StructObjT>
int writeRecordAsync(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, int res1 = GMERR_OK, bool isStateTabelInput = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AllTypeTableStruct *objIn = (AllTypeTableStruct *)obj;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    for (int i = 0; i < objLen; i++) {
        if (isStruct) {
            ret = testStructSetVertexWithBuf(stmt, (objIn + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i), isStateTabelInput);
        }
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = GmcExecuteAsync(stmt, &insertRequestCtx);
            ret = testWaitAsyncRecv(&dataRev);
            if (dataRev.status != GMERR_OK) {
                ret = dataRev.status;
                break;
            }
            EXPECT_EQ(GMERR_OK, dataRev.status);
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &dataRev);
        ret = testWaitAsyncRecv(&dataRev);
        if (dataRev.status != GMERR_OK) {
            ret = dataRev.status;
        } else {
            EXPECT_EQ(GMERR_OK, dataRev.status);
            AW_MACRO_EXPECT_EQ_INT(objLen, dataRev.totalNum);
            AW_MACRO_EXPECT_EQ_INT(objLen, dataRev.succNum);
        }

        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

int UpdateUint1ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint1TypeTableStruct *obj = (Uint1TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateInt1ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Int1TypeTableStruct *obj = (Int1TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint1_8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint18TypeTableStruct *obj = (Uint18TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 无法直接回去位域地址
    uint8_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD8, &value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint2_16ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint216TypeTableStruct *obj = (Uint216TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "a =  %d", obj->a);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "b =  %d", obj->b);
    // 无法直接回去位域地址
    uint16_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD16, &value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint4_32ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint432TypeTableStruct *obj = (Uint432TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "a =  %d", obj->a);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "b =  %d", obj->b);
    // 无法直接回去位域地址
    uint32_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint8_64ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint864TypeTableStruct *obj = (Uint864TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "a =  %d", obj->a);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "b =  %d", obj->b);
    // 无法直接回去位域地址
    uint64_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD64, &value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateByteByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    ByteTypeTableStruct *obj = (ByteTypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "a =  %d", obj->a);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t len = obj->len;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BYTES, obj->buf, len);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateByte4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Byte4TypeTableStruct *obj = (Byte4TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "a =  %d", obj->a);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "b =  %d", obj->b);
    uint32_t len = obj->len1;
    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_BYTES, obj->buf1, len);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    len = obj->len2;
    ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_BYTES, obj->buf2, len);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    len = obj->len3;
    ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_BYTES, obj->buf3, len);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    len = obj->len4;
    ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_BYTES, obj->buf4, len);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint2ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint2TypeTableStruct *obj = (Uint2TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &obj->a, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint4TypeTableStruct *obj = (Uint4TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &obj->a, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint8TypeTableStruct *obj = (Uint8TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &obj->a, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// update 31 pk uint1
int UpdateUint1PkHave31PropertyByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint1Have63TypeTableStruct *obj = (Uint1Have63TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 1; j < 32; j++) {
        ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, "a32", GMC_DATATYPE_UINT8, &obj->a31, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// update 31 pk uint2
int UpdateUint2PkHave31PropertyByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint2Have63TypeTableStruct *obj = (Uint2Have63TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 1; j < 32; j++) {
        ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT16, &obj->a, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, "a32", GMC_DATATYPE_UINT16, &obj->a31, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// update 31 pk uint4
int UpdateUint4PkHave31PropertyByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint4Have63TypeTableStruct *obj = (Uint4Have63TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 1; j < 32; j++) {
        ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT32, &obj->a, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, "a32", GMC_DATATYPE_UINT32, &obj->a31, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// update 31 pk uint8
int UpdateUint8PkHave31PropertyByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint8Have63TypeTableStruct *obj = (Uint8Have63TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 1; j < 32; j++) {
        ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT64, &obj->a, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, "a32", GMC_DATATYPE_UINT64, &obj->a31, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// update 63 uint1
int UpdateUint1Have63ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint1Have63TypeTableStruct *obj = (Uint1Have63TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char propertyName[10] = "a";
    for (int i = 0; i < 62; i++) {
        (void)sprintf(propertyName, "a%d", i + 2);
        ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_UINT8, &obj->a1, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// update 63 uint2
int UpdateUint2Have63ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint2Have63TypeTableStruct *obj = (Uint2Have63TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &obj->a, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char propertyName[10] = "a";
    for (int i = 0; i < 62; i++) {
        (void)sprintf(propertyName, "a%d", i + 2);
        ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_UINT16, &obj->a1, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// update 63 uint4
int UpdateUint4Have63ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint4Have63TypeTableStruct *obj = (Uint4Have63TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &obj->a, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char propertyName[10] = "a";
    for (int i = 0; i < 62; i++) {
        (void)sprintf(propertyName, "a%d", i + 2);
        ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_UINT32, &obj->a1, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// update 63 uint8
int UpdateUint8Have63ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint8Have63TypeTableStruct *obj = (Uint8Have63TypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &obj->a, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char propertyName[10] = "a";
    for (int i = 0; i < 62; i++) {
        (void)sprintf(propertyName, "a%d", i + 2);
        ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_UINT64, &obj->a1, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
int UpdateUint1AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint1TypeBIsInt4TableStruct *obj = (Uint1TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint1_8AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint18TypeBIsInt4TableStruct *obj = (Uint18TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint2_16AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint216TypeBIsInt4TableStruct *obj = (Uint216TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint4_32AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint432TypeBIsInt4TableStruct *obj = (Uint432TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint8_64AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint864TypeBIsInt4TableStruct *obj = (Uint864TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateByteAndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    ByteTypeBIsInt4TableStruct *obj = (ByteTypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateByte3AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Byte3TypeBIsInt4TableStruct *obj = (Byte3TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint2AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint2TypeBIsInt4TableStruct *obj = (Uint2TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &obj->a, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint4AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint4TypeBIsInt4TableStruct *obj = (Uint4TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &obj->a, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int UpdateUint8AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint8TypeBIsInt4TableStruct *obj = (Uint8TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &obj->a, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// uint1  update pk
int UpdateUint1AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint1TypeBIsInt8TableStruct *obj = (Uint1TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// int1  update pk
int UpdateInt1AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Int1TypeBIsInt8TableStruct *obj = (Int1TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// uint1_8  update pk
int UpdateUint1_8AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint18TypeBIsInt8TableStruct *obj = (Uint18TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    uint8_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD8, &value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// uint2_16  update pk
int UpdateUint2_16AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint216TypeBIsInt8TableStruct *obj = (Uint216TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    uint16_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD16, &value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// uint4_32  update pk
int UpdateUint4_32AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint432TypeBIsInt8TableStruct *obj = (Uint432TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// uint8_64  update pk
int UpdateUint8_64AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint864TypeBIsInt8TableStruct *obj = (Uint864TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD64, &value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// byte  update pk
int UpdateByteAndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    ByteTypeBIsInt8TableStruct *obj = (ByteTypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &obj->a, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// uint2  update pk
int UpdateUint2AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint2TypeBIsInt8TableStruct *obj = (Uint2TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &obj->a, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// uint4  update pk
int UpdateUint4AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint4TypeBIsInt8TableStruct *obj = (Uint4TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &obj->a, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// uint8  update pk
int UpdateUint8AndBIsInt8ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Uint8TypeBIsInt8TableStruct *obj = (Uint8TypeBIsInt8TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &obj->a, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

template <typename StructObjT>
int UpdateRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, int res1 = GMERR_OK, bool isStateTabelInput = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    for (int i = 0; i < objLen; i++) {
        if (isStruct) {
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i), isStateTabelInput);
        }
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = GmcExecute(stmt);
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

int writeRecordId(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int64_t startid, int64_t endid, int32_t count,
    FuncWriteId func, bool isBatch = true, bool isStruct = false, int queueLeve = -1, bool isFlowCtr = true)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置批写的数据的记录数
        ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    if (queueLeve != -1) {
        int getValue = 0;
        AW_MACRO_EXPECT_EQ_INT(0, TestGetConfigValueInt("datalogQueueNum", &getValue));
    }

    for (int i = startid; i < endid; i++) {
        ret = func(stmt, i, count);
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecordId] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecordId] GmcBatchAddDML fail, set tRet, i: %d, ret = %d.", i, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = isFlowCtr ? GmcExecute(stmt) : GmcExecute(stmt);
            if (ret) {
                AW_FUN_Log(LOG_INFO, "[writeRecordId] GmcExecute fail, ret = %d.", ret);
                break;
            }
        }
    }

    if (isBatch) {
        ret = isFlowCtr ? GmcBatchExecute(batch, &batchRet) : GmcBatchExecute(batch, &batchRet);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "[writeRecordId] GmcBatchExecute fail, ret = %d.", ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }

    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecordId] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

template <typename StructObjT>
int readRecord(const char *labelName, StructObjT *obj, int objLen, FuncRead func, bool isExternal = false,
    bool checkRecord = true, int *outCnt = NULL, bool isResource = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish || ret) {
            break;
        }
        ret = func(stmt, (void *)obj, objLen, isExternal, isResource);
        if ((checkRecord) && (ret)) {
            AW_FUN_Log(LOG_ERROR, "[readRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        cnt++;
    }
    int32_t ret1 = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    if ((cnt != objLen) || (tRet == -1)) {
        return cnt;
    }
    return ret;
}

// read uint1
int32_t ReadUint1ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint1TypeTableStruct *obj = (Uint1TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->b), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}


// read int1
int32_t ReadInt1ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Int1TypeTableStruct *obj = (Int1TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &((obj)->a), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->b), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1_8
int32_t ReadUint1_8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint18TypeTableStruct *obj = (Uint18TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->b), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint2_16
int32_t ReadUint2_16ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint216TypeTableStruct *obj = (Uint216TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->b), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
// read uint4_32
int32_t ReadUint4_32ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint432TypeTableStruct *obj = (Uint432TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->b), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint8_64
int32_t ReadUint8_64ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint864TypeTableStruct *obj = (Uint864TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->b), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read byte
int32_t ReadByteByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    ByteTypeTableStruct *obj = (ByteTypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->b), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read 4个byte
int32_t ReadByte4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Byte4TypeTableStruct *obj = (Byte4TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->b), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
// read uint2
int32_t ReadUint2ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint2TypeTableStruct *obj = (Uint2TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((obj)->a), sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &((obj)->b), sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint4
int32_t ReadUint4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint4TypeTableStruct *obj = (Uint4TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &((obj)->a), sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint8
int32_t ReadUint8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint8TypeTableStruct *obj = (Uint8TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &((obj)->a), sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read pk have 31 uint1
int32_t ReadUint1PkHave31PropertyByKeyID(
    GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint1Have63TypeTableStruct *obj = (Uint1Have63TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t j = 1; j < 32; j++) {
            ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((obj)->a32), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read pk have 31 uint2
int32_t ReadUint2PkHave31PropertyByKeyID(
    GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint2Have63TypeTableStruct *obj = (Uint2Have63TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t j = 1; j < 32; j++) {
            ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT16, &((obj)->a), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &((obj)->a32), sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read pk have 31 uint4
int32_t ReadUint4PkHave31PropertyByKeyID(
    GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint4Have63TypeTableStruct *obj = (Uint4Have63TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t j = 1; j < 32; j++) {
            ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT32, &((obj)->a), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->a32), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read pk have 31 uint8
int32_t ReadUint8PkHave31PropertyByKeyID(
    GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint8Have63TypeTableStruct *obj = (Uint8Have63TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t j = 1; j < 32; j++) {
            ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT64, &((obj)->a), sizeof(uint64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->a32), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
// read 63 uint1
int32_t ReadUint1Have63ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint1Have63TypeTableStruct *obj = (Uint1Have63TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &((obj)->a32), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read 63 uint2
int32_t ReadUint2Have63ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint2Have63TypeTableStruct *obj = (Uint2Have63TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((obj)->a), sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &((obj)->a32), sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read 63 uint4
int32_t ReadUint4Have63ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint4Have63TypeTableStruct *obj = (Uint4Have63TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &((obj)->a), sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &((obj)->a32), sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read 63 uint8
int32_t ReadUint8Have63ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint8Have63TypeTableStruct *obj = (Uint8Have63TypeTableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &((obj)->a), sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &((obj)->a32), sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1 b int4
int32_t ReadUint1AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint1TypeBIsInt4TableStruct *obj = (Uint1TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1 b int4 c uint1_8
int32_t ReadUint1_8AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint18TypeBIsInt4TableStruct *obj = (Uint18TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_FUN_Log(LOG_INFO, "b字段的值为%d", ((obj)->b));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1 b int4 c uint2_16
int32_t ReadUint2_16AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint216TypeBIsInt4TableStruct *obj = (Uint216TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1 b int4 c uint4_32
int32_t ReadUint4_32AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint432TypeBIsInt4TableStruct *obj = (Uint432TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1 b int4 c uint8_64
int32_t ReadUint8_64AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint864TypeBIsInt4TableStruct *obj = (Uint864TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1 b int4 c byte
int32_t ReadByteAndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    ByteTypeBIsInt4TableStruct *obj = (ByteTypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1 b int4 c byte3
int32_t ReadByte3AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Byte3TypeBIsInt4TableStruct *obj = (Byte3TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint2 b int4
int32_t ReadUint2AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint2TypeBIsInt4TableStruct *obj = (Uint2TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((obj)->a), sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint4 b int4
int32_t ReadUint4AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint4TypeBIsInt4TableStruct *obj = (Uint4TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &((obj)->a), sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint8 b int4
int32_t ReadUint8AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint8TypeBIsInt4TableStruct *obj = (Uint8TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &((obj)->a), sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1 b int8
int32_t ReadUint1AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint1TypeBIsInt8TableStruct *obj = (Uint1TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read int1 b int8
int32_t ReadInt1AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Int1TypeBIsInt8TableStruct *obj = (Int1TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &((obj)->a), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint1_8 b int8
int32_t ReadUint1_8AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint18TypeBIsInt8TableStruct *obj = (Uint18TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint2_16 b int8
int32_t ReadUint2_16AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint216TypeBIsInt8TableStruct *obj = (Uint216TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint4_32 b int8
int32_t ReadUint4_32AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint432TypeBIsInt8TableStruct *obj = (Uint432TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint8_64 b int8
int32_t ReadUint8_64AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint864TypeBIsInt8TableStruct *obj = (Uint864TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read Byte b int8
int32_t ReadByteAndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    ByteTypeBIsInt8TableStruct *obj = (ByteTypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((obj)->a), sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint2 b int8
int32_t ReadUint2AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint2TypeBIsInt8TableStruct *obj = (Uint2TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((obj)->a), sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint4 b int8
int32_t ReadUint4AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint4TypeBIsInt8TableStruct *obj = (Uint4TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &((obj)->a), sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// read uint8 b int8
int32_t ReadUint8AndBIsInt8ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Uint8TypeBIsInt8TableStruct *obj = (Uint8TypeBIsInt8TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &((obj)->a), sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((obj)->b), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

template <typename StructObjT>
int readRecordId(const char *labelName, StructObjT *obj, int objLen, FuncReadByKeyId readRecordById, uint32_t keyId,
    int32_t upgradeVersion = 0, bool isExternal = false, bool checkRecord = true, int *outCnt = NULL,
    bool isResource = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int cnt = 0;

    // 设置主键和key
    for (int i = 0; i < objLen; i++) {
        ret = readRecordById(stmt, keyId, obj + i, 1, upgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish;

        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish || ret) {
                break;
            }
            cnt++;
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return cnt;
}

int Debug_executeCommand(char *cmd, const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL,
    const char *v4 = NULL, const char *v5 = NULL)
{
    int ret = 0;
    ret = executeCommand(cmd, v1, v2, v3, v4, v5);
    return ret;
}

// 加载升级的so
int TestLoadUpgradeDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    int32_t ret = 0;
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(loadCommand, commandSize, "%s/gmimport -s %s -c datalog -upgrade ./datalog_file/%s.so -ns %s",
        g_toolPath, g_connServer, soName, g_testNameSpace);
    ret = Debug_executeCommand(loadCommand, "successfully");
    if (ret) {
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int32_t CheckSoState(char *cmd, char *v1 = (char *)"PATCH_STATE: SUCCESS", char *v2 = NULL, char *v3 = NULL,
    char *v4 = NULL, char *v5 = NULL)
{
    int32_t ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, v1, v2, v3, v4, v5);
        if (ret == -1) {
            sleep(2);
        }
    }
    return ret;
}
/*********************************DML*****************************************/
// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte512, g:str
int AllTypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    AllTypeTableStruct *obj = (AllTypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &obj->d1, sizeof(obj->d1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] d1: %d, ret = %d.", obj->d1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, sizeof(obj->e));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] e: %d, ret = %d.", obj->e, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, sizeof(obj->f));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] f: %d, ret = %d.", obj->f, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_STRING, obj->g, strlen(obj->g));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] g: %d, ret = %d.", obj->g, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int1
int Uint1TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint1TypeTableStruct *obj = (Uint1TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:int1, b:int1
int Int1TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Int1TypeTableStruct *obj = (Int1TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int1,c:uint1_8
int Uint1_8TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint18TypeTableStruct *obj = (Uint18TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    // 无法直接回去位域地址
    uint8_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD8, &value, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int1,c:uint2_16
int Uint216TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint216TypeTableStruct *obj = (Uint216TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    // 无法直接回去位域地址
    uint16_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD16, &value, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int1,c:uint4_32
int Uint432TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint432TypeTableStruct *obj = (Uint432TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    // 无法直接回去位域地址
    uint32_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD32, &value, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int1,c:uint8_64
int Uint864TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint864TypeTableStruct *obj = (Uint864TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    // 无法直接回去位域地址
    uint64_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD64, &value, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int1,c:byte
int ByteTypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    ByteTypeTableStruct *obj = (ByteTypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BYTES, obj->buf, obj->len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableSet] c: %s, ret = %d.", obj->buf, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:byte512, 4个byte
int MaxTypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    MaxTypeTableStruct *obj = (MaxTypeTableStruct *)t;
    int ret = 0;
    char propertyName[10] = "a";
    for (uint32_t i = 1; i < 64; i++) {
        (void)sprintf(propertyName, "a%d", i);
        if (i < 6 && i > 1) {
            ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_BYTES, obj->buf, obj->len);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[ByteTypeTableSet] %s: %s, ret = %d.", propertyName, obj->buf, ret);
                return ret;
            }
        } else {
            ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_FIXED, obj->a, sizeof(obj->a));
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[MaxTypeTableSet] %d: %u, ret = %d.", propertyName, obj->a, ret);
                return ret;
            }
        }
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[MaxTypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[MaxTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// 含所有数据类型
int NewAlLTypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    NewALLTypeTableStruct *obj = (NewALLTypeTableStruct *)t;
    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a: %s, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] b: %s, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] c: %s, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] d: %s, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] e: %u, ret = %d.", obj->e, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, 128);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] f: %u, ret = %d.", obj->f, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, obj->g, 256);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] g: %u, ret = %d.", obj->g, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT8, &obj->a1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AlLTypeTableSet] a1: %s, ret = %d.", obj->a1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_UINT16, &obj->b1, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] b1: %s, ret = %d.", obj->b1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_UINT32, &obj->c1, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] c1: %s, ret = %d.", obj->c1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_UINT64, &obj->d1, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] d1: %s, ret = %d.", obj->d1, ret);
        return ret;
    }
    uint8_t value1 = obj->e1;
    ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_BITFIELD8, &value1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] e1: %d, ret = %d.", value1, ret);
        return ret;
    }

    uint16_t value2 = obj->f1;
    ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_BITFIELD16, &value2, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] f1: %d, ret = %d.", value2, ret);
        return ret;
    }

    uint32_t value3 = obj->g1;
    ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_BITFIELD32, &value3, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] g1: %d, ret = %d.", value3, ret);
        return ret;
    }

    uint64_t value4 = obj->a2;
    ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_BITFIELD64, &value4, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a2: %d, ret = %d.", value4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_STRING, obj->a3buf, (strlen((char *)obj->a3buf)));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a3: %s, ret = %d.", obj->a3buf, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_BYTES, obj->a4buf, obj->a4len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a4: %s, ret = %d.", obj->a4buf, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int1,c1:byte,c2:byte,c3:byte,c4:byte
int Byte4TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Byte4TypeTableStruct *obj = (Byte4TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT8, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_BYTES, obj->buf1, obj->len1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableSet] c1: %s, ret = %d.", obj->buf1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_BYTES, obj->buf2, obj->len2);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableSet] c2: %s, ret = %d.", obj->buf2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_BYTES, obj->buf3, obj->len3);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableSet] c3: %s, ret = %d.", obj->buf3, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_BYTES, obj->buf4, obj->len4);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableSet] c4: %s, ret = %d.", obj->buf4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint4, b:int4
int Uint4TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint4TypeTableStruct *obj = (Uint4TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint8, b:int8
int Uint8TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint8TypeTableStruct *obj = (Uint8TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint2, b:int2
int Uint2TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint2TypeTableStruct *obj = (Uint2TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT16, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1,63 uint1
int Uint1TypeHave63TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint1Have63TypeTableStruct *obj = (Uint1Have63TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeHave63TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    char propertyName[10] = "a";
    for (int i = 0; i < 62; i++) {
        (void)sprintf(propertyName, "a%d", i + 2);
        ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeHave63TableSet] a: %d, ret = %d.", obj->a, ret);
            return ret;
        }
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeHave63TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeHave63TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint2,63 uint2
int Uint2TypeHave63TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint2Have63TypeTableStruct *obj = (Uint2Have63TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT16, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeHave63TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    char propertyName[10] = "a";
    for (int i = 0; i < 62; i++) {
        (void)sprintf(propertyName, "a%d", i + 2);
        ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_UINT16, &obj->a, sizeof(obj->a));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeHave63TableSet] a: %d, ret = %d.", obj->a, ret);
            return ret;
        }
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeHave63TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeHave63TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint4,63 uint4
int Uint4TypeHave63TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint4Have63TypeTableStruct *obj = (Uint4Have63TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeHave63TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    char propertyName[10] = "a";
    for (int i = 0; i < 62; i++) {
        (void)sprintf(propertyName, "a%d", i + 2);
        ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_UINT32, &obj->a, sizeof(obj->a));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeHave63TableSet] a: %d, ret = %d.", obj->a, ret);
            return ret;
        }
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeHave63TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeHave63TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint8,63 uint8
int Uint8TypeHave63TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint8Have63TypeTableStruct *obj = (Uint8Have63TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeHave63TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    char propertyName[10] = "a";
    for (int i = 0; i < 62; i++) {
        (void)sprintf(propertyName, "a%d", i + 2);
        ret = GmcSetVertexProperty(stmt, propertyName, GMC_DATATYPE_UINT64, &obj->a, sizeof(obj->a));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeHave63TableSet] a: %d, ret = %d.", obj->a, ret);
            return ret;
        }
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeHave63TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeHave63TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int4
int Uint1TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint1TypeBIsInt4TableStruct *obj = (Uint1TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int4, c:uint1_8
int Uint1_8TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    int ret = 0;
    Uint18TypeBIsInt4TableStruct *obj = (Uint18TypeBIsInt4TableStruct *)t;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeAndBIsInt4TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    uint8_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD8, &value, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint1_8TypeAndBIsInt4TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint1_8TypeAndBIsInt4TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int4, c:uint2_16
int Uint216TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint216TypeBIsInt4TableStruct *obj = (Uint216TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeAndBIsInt4TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    uint16_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD16, &value, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeAndBIsInt4TableSet] c: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint216TypeAndBIsInt4TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint216TypeAndBIsInt4TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int4, c:uint4_32
int Uint432TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint432TypeBIsInt4TableStruct *obj = (Uint432TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeAndBIsInt4TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    uint32_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD32, &value, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeAndBIsInt4TableSet] c: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint432TypeAndBIsInt4TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint432TypeAndBIsInt4TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int4, c:uint8_64
int Uint864TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint864TypeBIsInt4TableStruct *obj = (Uint864TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeAndBIsInt4TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    uint64_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD64, &value, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeAndBIsInt4TableSet] c: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint864TypeAndBIsInt4TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint864TypeAndBIsInt4TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int4, c:byte
int ByteTypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    ByteTypeBIsInt4TableStruct *obj = (ByteTypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeAndBIsInt4TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BYTES, obj->buf, obj->len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeAndBIsInt4TableSet] c: %u, ret = %d.", obj->buf, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeAndBIsInt4TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[ByteTypeAndBIsInt4TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int4, c:byte3
int Byte3TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Byte3TypeBIsInt4TableStruct *obj = (Byte3TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte3TypeAndBIsInt4TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte3TypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_FIXED, obj->c, 3);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte3TypeAndBIsInt4TableSet] c: %u, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Byte3TypeAndBIsInt4TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Byte3TypeAndBIsInt4TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint2, b:int4
int Uint2TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint2TypeBIsInt4TableStruct *obj = (Uint2TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT16, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint4, b:int4
int Uint4TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint4TypeBIsInt4TableStruct *obj = (Uint4TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeAndBIsInt4TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeAndBIsInt4TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint4TypeAndBIsInt4TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint8, b:int4
int Uint8TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint8TypeBIsInt4TableStruct *obj = (Uint8TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeAndBIsInt4TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeAndBIsInt4TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeAndBIsInt4TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint8TypeAndBIsInt4TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int8
int Uint1TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint1TypeBIsInt8TableStruct *obj = (Uint1TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:int1, b:int8
int Int1TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Int1TypeBIsInt8TableStruct *obj = (Int1TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int8, c:uint1_8
int Uint1_8TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint18TypeBIsInt8TableStruct *obj = (Uint18TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    uint8_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD8, &value, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int8, c:uint2_16
int Uint216TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint216TypeBIsInt8TableStruct *obj = (Uint216TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    uint16_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD16, &value, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int8, c:uint4_32
int Uint432TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint432TypeBIsInt8TableStruct *obj = (Uint432TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    uint32_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD32, &value, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int8, c:uint8_64
int Uint864TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint864TypeBIsInt8TableStruct *obj = (Uint864TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    uint64_t value = obj->c;
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BITFIELD64, &value, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint1, b:int8, c:byte
int ByteTypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    ByteTypeBIsInt8TableStruct *obj = (ByteTypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeAndBIsInt8TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeAndBIsInt8TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_BYTES, obj->buf, obj->len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeAndBIsInt8TableSet] c: %u, ret = %d.", obj->buf, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeAndBIsInt8TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[ByteTypeAndBIsInt8TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint2, b:int8
int Uint2TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint2TypeBIsInt8TableStruct *obj = (Uint2TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT16, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeAndBIsInt8TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeAndBIsInt8TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeAndBIsInt8TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint2TypeAndBIsInt8TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint4, b:int8
int Uint4TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint4TypeBIsInt8TableStruct *obj = (Uint4TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeAndBIsInt8TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeAndBIsInt8TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeAndBIsInt8TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint2TypeAndBIsInt8TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:uint8, b:int8
int Uint8TypeAndBIsInt8TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Uint8TypeBIsInt8TableStruct *obj = (Uint8TypeBIsInt8TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_UINT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeAndBIsInt8TableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeAndBIsInt8TableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeAndBIsInt8TableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(
            LOG_ERROR, "[Uint8TypeAndBIsInt8TableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

/*********************************DML*****************************************/
// a:int1, b:int2, c:int4, d:int64
int IntTypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    IntTypeTableStruct *obj = (IntTypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:int4
int Int4TypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Int4TypeTableStruct *obj = (Int4TypeTableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &obj->d, sizeof(obj->d));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[IntTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

template <typename StructObjT>
int arrycmp(const StructObjT *arry1, const StructObjT *arry2, int len)
{
    for (int i = 0; i < len; i++) {
        if (arry1[i] > arry2[i]) {
            return 1;
        } else if (arry1[i] < arry2[i]) {
            return -1;
        }
    }
    return 0;
}

int AllTypeTableCmp(
    const AllTypeTableStruct *st1, const AllTypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            break;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
            break;
        }

        if (st1->d != st2->d) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] d, st1: %d, st2: %d.", st1->d, st2->d);
            }
            break;
        }
        if (st1->d1 != st2->d1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] d1, st1: %d, st2: %d.", st1->d1, st2->d1);
            }
            break;
        }

        if (st1->e[0] != st2->e[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] e, st1: %d, st2: %d.", st1->e[0], st2->e[0]);
            }
            break;
        }

        if (st1->f[0] != st2->f[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] f, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
            break;
        }

        if (arrycmp(st1->g, st2->g, st1->gLen) != 0) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] b, st1: %s, st2: %s.", st1->g, st2->g);
            }
            break;
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] dtlReservedCount, st1: %d, st2: %d.", st1->dtlReservedCount,
                        st2->dtlReservedCount);
                }
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int NewAllTypeTableCmp(
    const NewALLTypeTableStruct *st1, const NewALLTypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (st1->d != st2->d) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d, st1: %d, st2: %d.", st1->d, st2->d);
            }
        }

        if (st1->e[0] != st2->e[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] e, st1: %d, st2: %d.", st1->e[0], st2->e[0]);
            }
        }

        if (st1->f[0] != st2->f[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] f, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->g[0] != st2->g[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] g, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->a1 != st2->a1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a1, st1: %d, st2: %d.", st1->a1, st2->a1);
            }
        }

        if (st1->b1 != st2->b1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b1, st1: %d, st2: %d.", st1->b1, st2->b1);
            }
        }

        if (st1->c1 != st2->c1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c1, st1: %d, st2: %d.", st1->c1, st2->c1);
            }
        }

        if (st1->d1 != st2->d1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d1, st1: %d, st2: %d.", st1->d1, st2->d1);
            }
        }

        if (st1->a1 != st2->e1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] e1, st1: %d, st2: %d.", st1->e1, st2->e1);
            }
        }

        if (st1->f1 != st2->f1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] f1, st1: %d, st2: %d.", st1->f1, st2->f1);
            }
        }

        if (st1->g1 != st2->g1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] g1, st1: %d, st2: %d.", st1->g1, st2->g1);
            }
        }

        if (st1->a2 != st2->a2) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a2, st1: %d, st2: %d.", st1->a2, st2->a2);
            }
        }

        if (arrycmp(st1->a3buf, st2->a3buf, st1->a3len) != 0) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a3, st1: %s, st2: %s.", st1->a3buf, st2->a3buf);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint1TypeTableCmp(
    const Uint1TypeTableStruct *st1, const Uint1TypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Int1TypeTableCmp(
    const Int1TypeTableStruct *st1, const Int1TypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Int1TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Int1TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Int1TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Int1TypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint1_8TypeTableCmp(
    const Uint18TypeTableStruct *st1, const Uint18TypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint216TypeTableCmp(const Uint216TypeTableStruct *st1, const Uint216TypeTableStruct *st2, bool isPrint = false,
    bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint216TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
                break;
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint216TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
                break;
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint216TypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
                break;
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint216TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint216TypeTableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint432TypeTableCmp(const Uint432TypeTableStruct *st1, const Uint432TypeTableStruct *st2, bool isPrint = false,
    bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint432TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
                break;
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint432TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
                break;
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint432TypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
                break;
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint432TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint432TypeTableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint864TypeTableCmp(const Uint864TypeTableStruct *st1, const Uint864TypeTableStruct *st2, bool isPrint = false,
    bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint864TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
                break;
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint864TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
                break;
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Uint864TypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
                break;
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint864TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint864TypeTableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}
// 生成byte变长字段
Status Generate_Bytes(uint8_t **bytes, uint32_t byteLen)
{
    uint8_t *byteArray = (uint8_t *)malloc(byteLen + 1);
    if (byteArray == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    for (uint32_t i = 0; i < byteLen; i++) {
        int num = i;
        while (num >= 10) {
            num = num % 10;
        }
        byteArray[i] = (uint8_t)num;
    }
    byteArray[byteLen] = '\n';
    *bytes = byteArray;
    return GMERR_OK;
}
int ByteTypeTableCmp(
    const ByteTypeTableStruct *st1, const ByteTypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[ByteTypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
                break;
            }
            break;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[ByteTypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
                break;
            }
            break;
        }
        uint8_t *expectBValue = NULL;
        ret = Generate_Bytes(&expectBValue, st1->len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = memcmp((char *)st1->buf, (char *)expectBValue, st1->len);
        if (ret) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[ByteTypeTableCmp] byte, st1: %u, st2: %u.", st1->buf, expectBValue);
            }
            free(expectBValue);
            break;
        }
        free(expectBValue);
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[ByteTypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[ByteTypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Byte4TypeTableCmp(
    const Byte4TypeTableStruct *st1, const Byte4TypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Byte4TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
                break;
            }
            break;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "【ERROR】[Byte4TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
                break;
            }
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Byte4TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Byte4TypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint4TypeTableCmp(
    const Uint4TypeTableStruct *st1, const Uint4TypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint4TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint4TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint4TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint4TypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint8TypeTableCmp(
    const Uint8TypeTableStruct *st1, const Uint8TypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint8TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint8TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint8TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint8TypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint2TypeTableCmp(
    const Uint2TypeTableStruct *st1, const Uint2TypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint2TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint2TypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint2TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint2TypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint1TypeHave63TableCmp(const Uint1Have63TypeTableStruct *st1, const Uint1Have63TypeTableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint2TypeHave63TableCmp(const Uint2Have63TypeTableStruct *st1, const Uint2Have63TypeTableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint2TypeHave63TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint2TypeHave63TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint2TypeHave63TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint4TypeHave63TableCmp(const Uint4Have63TypeTableStruct *st1, const Uint4Have63TypeTableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint4TypeHave63TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint4TypeHave63TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint4TypeHave63TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint8TypeHave63TableCmp(const Uint8Have63TypeTableStruct *st1, const Uint8Have63TypeTableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint8TypeHave63TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint8TypeHave63TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint8TypeHave63TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint1TypeBIsInt4TableCmp(const Uint1TypeBIsInt4TableStruct *st1, const Uint1TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Int1TypeBIsInt4TableCmp(const Int1TypeBIsInt4TableStruct *st1, const Int1TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Int1TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Int1TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Int1TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Int1TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint1_8TypeBIsInt4TableCmp(const Uint18TypeBIsInt4TableStruct *st1, const Uint18TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint216TypeBIsInt4TableCmp(const Uint216TypeBIsInt4TableStruct *st1, const Uint216TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint432TypeBIsInt4TableCmp(const Uint432TypeBIsInt4TableStruct *st1, const Uint432TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint864TypeBIsInt4TableCmp(const Uint864TypeBIsInt4TableStruct *st1, const Uint864TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int ByteTypeBIsInt4TableCmp(const ByteTypeBIsInt4TableStruct *st1, const ByteTypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[ByteTypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            break;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[ByteTypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
            break;
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[ByteTypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[ByteTypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Byte3TypeBIsInt4TableCmp(const Byte3TypeBIsInt4TableStruct *st1, const Byte3TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Byte3TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            break;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Byte3TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
            break;
        }

        if (st1->c[0] != st2->c[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Byte3TypeBIsInt4TableCmp] c, st1: %d, st2: %d.", st1->c[0], st2->c[0]);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Byte3TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Byte3TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint2TypeBIsInt4TableCmp(const Uint2TypeBIsInt4TableStruct *st1, const Uint2TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint4TypeBIsInt4TableCmp(const Uint4TypeBIsInt4TableStruct *st1, const Uint4TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint4TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint4TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint4TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint4TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint8TypeBIsInt4TableCmp(const Uint8TypeBIsInt4TableStruct *st1, const Uint8TypeBIsInt4TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint8TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint8TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint8TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint8TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint1TypeBIsInt8TableCmp(const Uint1TypeBIsInt8TableStruct *st1, const Uint1TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1TypeBIsInt4TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint1_8TypeBIsInt8TableCmp(const Uint18TypeBIsInt8TableStruct *st1, const Uint18TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt8TableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint1_8TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint216TypeBIsInt8TableCmp(const Uint216TypeBIsInt8TableStruct *st1, const Uint216TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt8TableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint216TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint432TypeBIsInt8TableCmp(const Uint432TypeBIsInt8TableStruct *st1, const Uint432TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt8TableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint432TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint864TypeBIsInt8TableCmp(const Uint864TypeBIsInt8TableStruct *st1, const Uint864TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int ByteTypeBIsInt8TableCmp(const ByteTypeBIsInt8TableStruct *st1, const ByteTypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            break;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
            break;
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint864TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint2TypeBIsInt8TableCmp(const Uint2TypeBIsInt8TableStruct *st1, const Uint2TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint2TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint2TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint2TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint2TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint4TypeBIsInt8TableCmp(const Uint4TypeBIsInt8TableStruct *st1, const Uint4TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint4TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint4TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint4TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint4TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int Uint8TypeBIsInt8TableCmp(const Uint8TypeBIsInt8TableStruct *st1, const Uint8TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint8TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Uint8TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint8TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Uint8TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}
// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte512, g:str
int AllTypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    AllTypeTableStruct *checkObj = (AllTypeTableStruct *)t;
    AllTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d1", &getObj.d1, sizeof(getObj.d1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'd1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    getObj.gLen = checkObj[0].gLen;
    getObj.g = (char *)malloc(getObj.gLen * sizeof(char));
    if (getObj.g == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    // 获取字符串
    ret = GmcGetVertexPropertyByName(stmt, "g", getObj.g, getObj.gLen, &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (AllTypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)AllTypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    // 释放内存
    free(getObj.g);
    return ret;
}

// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte128, g:byte256,uint1-uint8,uint1_8-uint8_64,str,byte
int NewTypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    NewALLTypeTableStruct *checkObj = (NewALLTypeTableStruct *)t;
    NewALLTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "g", getObj.g, sizeof(getObj.g), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a1, sizeof(getObj.a1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b1", &getObj.b1, sizeof(getObj.b1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c1", &getObj.c1, sizeof(getObj.c1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d1", &getObj.d1, sizeof(getObj.d1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }
    uint8_t value1 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "e1", &value1, sizeof(uint8_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'e1' fail, ret = %d.", ret);
        return ret;
    }
    getObj.e1 = value1;

    uint16_t value2 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "f1", &value2, sizeof(uint16_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'f1' fail, ret = %d.", ret);
        return ret;
    }
    getObj.f1 = value2;

    uint32_t value3 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "g1", &value3, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g1' fail, ret = %d.", ret);
        return ret;
    }
    getObj.g1 = value3;

    uint64_t value4 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "a2", &value4, sizeof(uint64_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a2' fail, ret = %d.", ret);
        return ret;
    }
    getObj.a2 = value4;

    getObj.a3len = checkObj[0].a3len;
    getObj.a3buf = (char *)malloc(getObj.a3len * sizeof(char));
    if (getObj.a3buf == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    // 获取字符串
    ret = GmcGetVertexPropertyByName(stmt, "a3", getObj.a3buf, getObj.a3len, &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "a4", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4len = length;
    uint8_t *expectBValue = NULL;
    ret = Generate_Bytes(&expectBValue, length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4buf = (uint8_t *)malloc(getObj.a4len + 1);
    if (getObj.a4buf == NULL) {
        free(expectBValue);
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "a4", getObj.a4buf, getObj.a4len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
    if (expectBValue) {
        ret = memcmp((char *)expectBValue, (char *)getObj.a4buf, length);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(expectBValue);
    } else {
        free(expectBValue);
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (NewAllTypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)NewAllTypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[NewAllTypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    // 释放内存
    free(getObj.a3buf);
    return ret;
}

// a:uint1, b:int1
int Uint1TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint1TypeTableStruct *checkObj = (Uint1TypeTableStruct *)t;
    Uint1TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint1TypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint1TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}


// a:int1, b:int1
int Int1TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Int1TypeTableStruct *checkObj = (Int1TypeTableStruct *)t;
    Int1TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Int1TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Int1TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Int1TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Int1TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Int1TypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Int1TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Int1TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int1,c:uint1_8
int Uint1_8TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint18TypeTableStruct *checkObj = (Uint18TypeTableStruct *)t;
    Uint18TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    // 无法直接回去位域地址
    uint8_t value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint8_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    getObj.b = value;

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint1_8TypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint1_8TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int1,c:uint2_16
int Uint216TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint216TypeTableStruct *checkObj = (Uint216TypeTableStruct *)t;
    Uint216TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    // 无法直接回去位域地址
    uint16_t value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint16_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    getObj.c = value;

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint216TypeTableCmp(&getObj, &checkObj[i], false, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint216TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int1,c:uint4_32
int Uint432TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint432TypeTableStruct *checkObj = (Uint432TypeTableStruct *)t;
    Uint432TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    // 无法直接回去位域地址
    uint32_t value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    getObj.c = value;

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint432TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint432TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint432TypeTableCmp(&getObj, &checkObj[i], false, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint432TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int1,c:uint8_64
int Uint864TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint864TypeTableStruct *checkObj = (Uint864TypeTableStruct *)t;
    Uint864TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    // 无法直接回去位域地址
    uint64_t value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint64_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    getObj.c = value;

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint864TypeTableCmp(&getObj, &checkObj[i], false, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint864TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int1,c:byte
int ByteTypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    ByteTypeTableStruct *checkObj = (ByteTypeTableStruct *)t;
    ByteTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "c", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len = length;
    getObj.buf = (uint8_t *)malloc(getObj.len + 1);
    if (getObj.buf == NULL) {
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "c", getObj.buf, getObj.len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            free(getObj.buf);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            free(getObj.buf);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (ByteTypeTableCmp(&getObj, &checkObj[i], false, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)ByteTypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    free(getObj.buf);
    return ret;
}

// MAX
int MaxTypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    MaxTypeTableStruct *checkObj = (MaxTypeTableStruct *)t;
    MaxTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a1", getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[MaxTypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "a2", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len = length;
    getObj.buf = (uint8_t *)malloc(getObj.len + 1);
    if (getObj.buf == NULL) {
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "a2", getObj.buf, getObj.len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[MaxTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            free(getObj.buf);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[MaxTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            free(getObj.buf);
            return ret;
        }
    }
    free(getObj.buf);
    return ret;
}

// a:uint1, b:int1,c1:byte,c2:byte,c3:byte,c4:byte
int Byte4TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Byte4TypeTableStruct *checkObj = (Byte4TypeTableStruct *)t;
    Byte4TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "c1", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len1 = length;
    getObj.buf1 = (uint8_t *)malloc(getObj.len1 + 1);
    if (getObj.buf1 == NULL) {
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "c1", getObj.buf1, getObj.len1, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);

    ret = GmcGetVertexPropertySizeByName(stmt, "c2", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len2 = length;
    getObj.buf2 = (uint8_t *)malloc(getObj.len2 + 1);
    if (getObj.buf2 == NULL) {
        free(getObj.buf1);
        return -1;
    }
    eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "c2", getObj.buf2, getObj.len2, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);

    ret = GmcGetVertexPropertySizeByName(stmt, "c3", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len3 = length;
    getObj.buf3 = (uint8_t *)malloc(getObj.len3 + 1);
    if (getObj.buf3 == NULL) {
        free(getObj.buf1);
        free(getObj.buf2);
        return -1;
    }
    eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "c3", getObj.buf3, getObj.len3, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);

    ret = GmcGetVertexPropertySizeByName(stmt, "c4", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len4 = length;
    getObj.buf4 = (uint8_t *)malloc(getObj.len4 + 1);
    if (getObj.buf4 == NULL) {
        free(getObj.buf1);
        free(getObj.buf2);
        free(getObj.buf3);
        return -1;
    }
    eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "c4", getObj.buf4, getObj.len4, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            free(getObj.buf1);
            free(getObj.buf2);
            free(getObj.buf3);
            free(getObj.buf4);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            free(getObj.buf1);
            free(getObj.buf2);
            free(getObj.buf3);
            free(getObj.buf4);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Byte4TypeTableCmp(&getObj, &checkObj[i], true, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Byte4TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Byte4TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    free(getObj.buf1);
    free(getObj.buf2);
    free(getObj.buf3);
    free(getObj.buf4);
    return ret;
}

// a:uint4, b:int4
int Uint4TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint4TypeTableStruct *checkObj = (Uint4TypeTableStruct *)t;
    Uint4TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint4TypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint4TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint8, b:int8
int Uint8TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint8TypeTableStruct *checkObj = (Uint8TypeTableStruct *)t;
    Uint8TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint8TypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint8TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint2, b:int2
int Uint2TypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint2TypeTableStruct *checkObj = (Uint2TypeTableStruct *)t;
    Uint2TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint2TypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint2TypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, 63 uint1
int Uint1TypeHave63TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint1Have63TypeTableStruct *checkObj = (Uint1Have63TypeTableStruct *)t;
    Uint1Have63TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint1TypeHave63TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint1TypeHave63TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint2, 63 uint2
int Uint2TypeHave63TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint2Have63TypeTableStruct *checkObj = (Uint2Have63TypeTableStruct *)t;
    Uint2Have63TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeHave63TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint2TypeHave63TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint2TypeHave63TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint2TypeHave63TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint2TypeHave63TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeHave63TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint4, 63 uint4
int Uint4TypeHave63TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint4Have63TypeTableStruct *checkObj = (Uint4Have63TypeTableStruct *)t;
    Uint4Have63TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeHave63TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeHave63TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeHave63TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint4TypeHave63TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint4TypeHave63TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeHave63TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint8, 63 uint8
int Uint8TypeHave63TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint8Have63TypeTableStruct *checkObj = (Uint8Have63TypeTableStruct *)t;
    Uint8Have63TypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeHave63TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeHave63TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeHave63TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint8TypeHave63TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint8TypeHave63TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeHave63TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int4
int Uint1TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint1TypeBIsInt4TableStruct *checkObj = (Uint1TypeBIsInt4TableStruct *)t;
    Uint1TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint1TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint1TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int4, c:uint1_8
int Uint1_8TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint18TypeBIsInt4TableStruct *checkObj = (Uint18TypeBIsInt4TableStruct *)t;
    Uint18TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint8_t value = getObj.c;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint8_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint1_8TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint1_8TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int4, c:uint2_16
int Uint216TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint216TypeBIsInt4TableStruct *checkObj = (Uint216TypeBIsInt4TableStruct *)t;
    Uint216TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint16_t value = getObj.c;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint16_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt4TableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint216TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint216TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int4, c:uint4_32
int Uint432TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint432TypeBIsInt4TableStruct *checkObj = (Uint432TypeBIsInt4TableStruct *)t;
    Uint432TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t value = getObj.c;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt4TableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint432TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint432TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int4, c:uint8_64
int Uint864TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint864TypeBIsInt4TableStruct *checkObj = (Uint864TypeBIsInt4TableStruct *)t;
    Uint864TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint64_t value = getObj.c;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint64_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt4TableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint864TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint864TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int4, c:byte
int ByteTypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    ByteTypeBIsInt4TableStruct *checkObj = (ByteTypeBIsInt4TableStruct *)t;
    ByteTypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "c", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len = length;
    getObj.buf = (uint8_t *)malloc(getObj.len + 1);
    if (getObj.buf == NULL) {
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "c", getObj.buf, getObj.len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (ByteTypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)ByteTypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int4, c:byte3
int Byte3TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Byte3TypeBIsInt4TableStruct *checkObj = (Byte3TypeBIsInt4TableStruct *)t;
    Byte3TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInct4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInct4TableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Byte3TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Byte3TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Byte3TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}
// a:uint2, b:int4
int Uint2TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint2TypeBIsInt4TableStruct *checkObj = (Uint2TypeBIsInt4TableStruct *)t;
    Uint2TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint2TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint2TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint4, b:int4
int Uint4TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint4TypeBIsInt4TableStruct *checkObj = (Uint4TypeBIsInt4TableStruct *)t;
    Uint4TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint4TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint4TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint8, b:int4
int Uint8TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint8TypeBIsInt4TableStruct *checkObj = (Uint8TypeBIsInt4TableStruct *)t;
    Uint8TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint8TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint8TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int8
int Uint1TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint1TypeBIsInt8TableStruct *checkObj = (Uint1TypeBIsInt8TableStruct *)t;
    Uint1TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint1TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint1TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}
int Int1TypeBIsInt8TableCmp(const Int1TypeBIsInt8TableStruct *st1, const Int1TypeBIsInt8TableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Int1TypeBIsInt8TableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[Int1TypeBIsInt8TableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Int1TypeBIsInt8TableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[Int1TypeBIsInt8TableCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

// a:int1, b:int8
int Int1TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Int1TypeBIsInt8TableStruct *checkObj = (Int1TypeBIsInt8TableStruct *)t;
    Int1TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Int1TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Int1TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Int1TypeBIsInt8TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int8,c:uint1_8
int Uint1_8TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint18TypeBIsInt8TableStruct *checkObj = (Uint18TypeBIsInt8TableStruct *)t;
    Uint18TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt8TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt8TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint8_t value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint8_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt8TableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    getObj.c = value;
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt8TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt8TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint1_8TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint1_8TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint1_8TypeBIsInt8TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int8,c:uint2_16
int Uint216TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint216TypeBIsInt8TableStruct *checkObj = (Uint216TypeBIsInt8TableStruct *)t;
    Uint216TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt8TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt8TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint16_t value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint16_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt8TableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    getObj.c = value;
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt8TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt8TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint216TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint216TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint216TypeBIsInt8TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int8,c:uint4_32
int Uint432TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint432TypeBIsInt8TableStruct *checkObj = (Uint432TypeBIsInt8TableStruct *)t;
    Uint432TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt8TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt8TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt8TableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    getObj.c = value;
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt8TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt8TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint432TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint432TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint432TypeBIsInt8TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int8,c:uint8_64
int Uint864TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint864TypeBIsInt8TableStruct *checkObj = (Uint864TypeBIsInt8TableStruct *)t;
    Uint864TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt8TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt8TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint64_t value = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(uint64_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt8TableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    getObj.c = value;
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt8TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt8TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint864TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint864TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint864TypeBIsInt8TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint1, b:int8,c:byte
int ByteTypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    ByteTypeBIsInt8TableStruct *checkObj = (ByteTypeBIsInt8TableStruct *)t;
    ByteTypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt8TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt8TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "c", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len = length;
    getObj.buf = (uint8_t *)malloc(getObj.len + 1);
    if (getObj.buf == NULL) {
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "c", getObj.buf, getObj.len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt8TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt8TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (ByteTypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)ByteTypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[ByteTypeBIsInt8TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint2, b:int8
int Uint2TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint2TypeBIsInt8TableStruct *checkObj = (Uint2TypeBIsInt8TableStruct *)t;
    Uint2TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint2TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint2TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint2TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint4, b:int8
int Uint4TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint4TypeBIsInt8TableStruct *checkObj = (Uint4TypeBIsInt8TableStruct *)t;
    Uint4TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt8TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt8TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt8TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt8TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint4TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint4TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint4TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:uint8, b:int8
int Uint8TypeBIsInt8TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Uint8TypeBIsInt8TableStruct *checkObj = (Uint8TypeBIsInt8TableStruct *)t;
    Uint8TypeBIsInt8TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt8TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt8TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt8TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt8TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Uint8TypeBIsInt8TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Uint8TypeBIsInt8TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Uint8TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// set keyId b：int1
int32_t DeleteUint1ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint1TypeTableStruct *t = (Uint1TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int1
int32_t DeleteInt1ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Int1TypeTableStruct *t = (Int1TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}


// set keyId b：int1_8
int32_t DeleteUint1_8ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint18TypeTableStruct *t = (Uint18TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int2_16
int32_t DeleteUint2_16ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint216TypeTableStruct *t = (Uint216TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4_32
int32_t DeleteUint4_32ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint432TypeTableStruct *t = (Uint432TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8_64
int32_t DeleteUint8_64ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint864TypeTableStruct *t = (Uint864TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：byte
int32_t DeleteByteByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    ByteTypeTableStruct *t = (ByteTypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：byte 4个byte
int32_t DeleteByte4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Byte4TypeTableStruct *t = (Byte4TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int2
int32_t DeleteUint2ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint2TypeTableStruct *t = (Uint2TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((t + i)->a), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &((t + i)->b), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4
int32_t DeleteUint4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint4TypeTableStruct *t = (Uint4TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &((t + i)->a), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8
int32_t DeleteUint8ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint8TypeTableStruct *t = (Uint8TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &((t + i)->a), sizeof(uint64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(uint64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId pk have 31 property uint1
int32_t DeleteUint1PkHave31PropertyByKeyId(
    GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint1Have63TypeTableStruct *t = (Uint1Have63TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t j = 1; j < 32; j++) {
                ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->a32), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId pk have 31 property uint2
int32_t DeleteUint2PkHave31PropertyByKeyId(
    GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint2Have63TypeTableStruct *t = (Uint2Have63TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t j = 1; j < 32; j++) {
                ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT16, &((t + i)->a), sizeof(uint16_t));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &((t + i)->a32), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId pk have 31 property uint4
int32_t DeleteUint4PkHave31PropertyByKeyId(
    GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint4Have63TypeTableStruct *t = (Uint4Have63TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t j = 1; j < 32; j++) {
                ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT32, &((t + i)->a), sizeof(uint32_t));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->a32), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId pk have 31 property uint8
int32_t DeleteUint8PkHave31PropertyByKeyId(
    GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint8Have63TypeTableStruct *t = (Uint8Have63TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t j = 1; j < 32; j++) {
                ret = GmcSetIndexKeyValue(stmt, j, GMC_DATATYPE_UINT64, &((t + i)->a), sizeof(uint64_t));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->a32), sizeof(uint64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId 63 int1
int32_t DeleteUint1Have63ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint1Have63TypeTableStruct *t = (Uint1Have63TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &((t + i)->a31), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId 63 int2
int32_t DeleteUint2Have63ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint2Have63TypeTableStruct *t = (Uint2Have63TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((t + i)->a), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &((t + i)->a31), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId 63 int4
int32_t DeleteUint4Have63ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint4Have63TypeTableStruct *t = (Uint4Have63TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &((t + i)->a), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &((t + i)->a31), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId 63 int8
int32_t DeleteUint8Have63ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint8Have63TypeTableStruct *t = (Uint8Have63TypeTableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &((t + i)->a), sizeof(uint64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &((t + i)->a31), sizeof(uint64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 uint1
int32_t DeleteUint1AndBIsInt8ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint1TypeBIsInt8TableStruct *t = (Uint1TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int64 int1
int32_t DeleteInt1AndBIsInt8ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Int1TypeBIsInt8TableStruct *t = (Int1TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &((t + i)->a), sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int32 int1
int32_t DeleteInt1AndBIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Int1TypeBIsInt4TableStruct *t = (Int1TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &((t + i)->a), sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 uint1_8
int32_t DeleteUint1_8AndBIsInt8ByKeyId(
    GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint18TypeBIsInt8TableStruct *t = (Uint18TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 uint2_16
int32_t DeleteUint2_16AndBIsInt8ByKeyId(
    GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint216TypeBIsInt8TableStruct *t = (Uint216TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 uint4_32
int32_t DeleteUint4_32AndBIsInt8ByKeyId(
    GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint432TypeBIsInt8TableStruct *t = (Uint432TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 uint8_64
int32_t DeleteUint8_64AndBIsInt8ByKeyId(
    GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint864TypeBIsInt8TableStruct *t = (Uint864TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 byte
int32_t DeleteByteAndBIsInt8ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    ByteTypeBIsInt8TableStruct *t = (ByteTypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 uint2
int32_t DeleteUint2AndBIsInt8ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint2TypeBIsInt8TableStruct *t = (Uint2TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((t + i)->a), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 uint4
int32_t DeleteUint4AndBIsInt8ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint4TypeBIsInt8TableStruct *t = (Uint4TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &((t + i)->a), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int8 uint8
int32_t DeleteUint8AndBIsInt8ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint8TypeBIsInt8TableStruct *t = (Uint8TypeBIsInt8TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &((t + i)->a), sizeof(uint64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &((t + i)->b), sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4
int32_t DeleteUint1AndBIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint1TypeBIsInt4TableStruct *t = (Uint1TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));

            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_FUN_Log(LOG_STEP, "B = %d", (t + i)->b);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4
int32_t DeleteUint2AndBIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint2TypeBIsInt4TableStruct *t = (Uint2TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((t + i)->a), sizeof(uint16_t));

            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_FUN_Log(LOG_STEP, "B = %d", (t + i)->b);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4
int32_t DeleteUint1BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint1TypeBIsInt4TableStruct *t = (Uint1TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &((t + i)->b), sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4 c:uint1_8
int32_t DeleteUint1_8BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint18TypeBIsInt4TableStruct *t = (Uint18TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4 c:uint2_16
int32_t DeleteUint2_16BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint216TypeBIsInt4TableStruct *t = (Uint216TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4 c:uint4_32
int32_t DeleteUint4_32BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint432TypeBIsInt4TableStruct *t = (Uint432TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4 c:uint8_64
int32_t DeleteUint8_64BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint864TypeBIsInt4TableStruct *t = (Uint864TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4 c:byte
int32_t DeleteByteBIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    ByteTypeBIsInt4TableStruct *t = (ByteTypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4 c:byte3
int32_t DeleteByte3BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Byte3TypeBIsInt4TableStruct *t = (Byte3TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &((t + i)->a), sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}
// set keyId b：int4
int32_t DeleteUint2BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint2TypeBIsInt4TableStruct *t = (Uint2TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &((t + i)->a), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &((t + i)->b), sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4
int32_t DeleteUint4BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint4TypeBIsInt4TableStruct *t = (Uint4TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &((t + i)->a), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// set keyId b：int4
int32_t DeleteUint8BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Uint8TypeBIsInt4TableStruct *t = (Uint8TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &((t + i)->a), sizeof(uint64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}
// keyId删除数据
template <typename StructObjT>
int32_t DeletedataBykeyId(GmcStmtT *stmt, const char *labelName, uint32_t keyId, StructObjT *obj, int objLen,
    FuncDeleteByKeyId func, int32_t upgradeVersion = 0)
{
    int32_t ret = 1000;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = func(stmt, keyId, obj, objLen, upgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

template <typename StructObjT>
int32_t DeleteAlldataByPkAsync(
    GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1000;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.insertCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &((obj + i)->d1), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &((obj + i)->d), sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        ret = testWaitAsyncRecv(&dataRev);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataRev.status);
        if (dataRev.status) {
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// 比较文件一致性
int32_t CompateFileIsSame(const char *actFile, const char *expectFile)
{
    char command1[MAX_CMD_SIZE] = {0};
    char command2[MAX_CMD_SIZE] = {0};

    int fileContent1 = 10;
    int fileContent2 = -1;
    (void)memset(command1, 0, sizeof(char) * (MAX_CMD_SIZE));
    (void)memset(command2, 0, sizeof(char) * (MAX_CMD_SIZE));
    (void)snprintf(command1, MAX_CMD_SIZE, "md5sum %s", actFile);
    (void)snprintf(command2, MAX_CMD_SIZE, "md5sum %s", expectFile);
    system(command1);
    system(command2);
    EXPECT_EQ(0, TestGetResultCommand(command1, &fileContent1, NULL, 0));
    EXPECT_EQ(0, TestGetResultCommand(command2, &fileContent2, NULL, 0));
    AW_FUN_Log(LOG_INFO, "验证文件内容.");

    AW_MACRO_EXPECT_EQ_INT(fileContent2, fileContent1);
    if (fileContent2 != fileContent1) {
        AW_FUN_Log(LOG_INFO, "file is not same");
        AW_FUN_Log(LOG_DEBUG, "----------actul-----------");
        SystemSnprintf("cat %s", actFile);
        AW_FUN_Log(LOG_DEBUG, "---------------------");

        AW_FUN_Log(LOG_DEBUG, "---------- expect-----------");
        SystemSnprintf("cat %s", expectFile);
        AW_FUN_Log(LOG_DEBUG, "---------------------");
        return -1;
    }
    return GMERR_OK;
}

// 交互gmconvert转换工具
int TestViewData(char *cmd, char **result)
{
    int ret = 0;
    FILE *fd = popen(cmd, "r");
    int size = 10240;
    *result = (char *)malloc(sizeof(char) * size);
    memset(*result, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat((char *)*result, buf);
    }

    ret = pclose(fd);
    return ret;
}
// 视图函数
int32_t GetLimitMemValue(const char *PName = (char *)"MemLimByConn")
{
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    char command[MAX_CMD_SIZE];
    (void)snprintf(command, MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=$(gmsysview -q V\\$QRY_SESSION -f CONN_ID=$(gmsysview -q "
        "V\\$DRT_CONN_STAT -f CLT_PROC_NAME=%s|grep CONN_ID| awk 'NR==1{print $2}')|grep MEMCTX_NAME| awk 'NR==1{print "
        "$2}')|grep THRESHOLD_ON_TREE| sed 's/[^0-9]//g'",
        PName);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_INFO, "popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, 64, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

// 获取表upgradeVersion
void GetTableUpgradeVersionByApi(const char *labelName, int32_t *getUpgradeVersion)
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AW_FUN_Log(LOG_INFO, "异步获取表%s upgradeVersion值.", labelName);

    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetUpgradeVersion(stmt, getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

#define MAX_NAME_LENGTH 512

typedef int (*FuncCheck)(GmcStmtT *stmt, void *id, void *df, int32_t c);

typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    int tableType;  // 0:out 1:resource
    int funcType;   // 0:id 1:struct
    char *nsName;
    union {
        struct {
            FuncReadSub readIdFunc;
            int startid;
            int endid;
            int32_t count;
        };
    };
} SnUserDataWithFuncT;
int snCallbackCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    char *nsName = userDefinedData->nsName;
    int tableType = userDefinedData->tableType;
    int funcType = userDefinedData->funcType;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);
    if (userDefinedData->tableType == 0) {  // notify输出表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        RETURN_IFERR(ret);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
        ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        RETURN_IFERR(ret);
    } else if (userDefinedData->tableType == 1) {  // pubsub型资源表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
        RETURN_IFERR(ret);
    }

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    if (tableType == 0) {  // out table
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 0);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    } else if (tableType == 1) {  // pubsubresource
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 1);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcSubAddRespDML(response, subStmt);
                        RETURN_IFERR(ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    if (userDefinedData->tableType != 2) {
        ret = GmcSendResp(subStmt, response);
        RETURN_IFERR(ret);
        ret = GmcDestroyResp(subStmt, response);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

void snCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = snCallbackCheck(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
struct DoubleInt4St {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
};

struct DoubleInt8St {
    uint64_t a;
    int64_t b;
    int32_t dtlReservedCount;
};
int DoubleInt4_getId(GmcStmtT *stmt, int startid, int endid, int32_t count)
{
    DoubleInt4St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = startid; i < endid; i++) {
        if ((getObj.a == i) && (getObj.b == i) && (getObj.dtlReservedCount == count)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[DoubleInt4_getId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }

    return ret;
}

int MaxgetId(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc = false)
{
    MaxTypeTableStruct getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a1", getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[MaxgetId] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "a2", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.len = length;
    getObj.buf = (uint8_t *)malloc(getObj.len + 1);
    if (getObj.buf == NULL) {
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "a2", getObj.buf, getObj.len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[MaxgetId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    return ret;
}

int DoubleInt8_getId(GmcStmtT *stmt, int startid, int endid, int32_t count)
{
    DoubleInt8St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt8_getId] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt8_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt8_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = startid; i < endid; i++) {
        if ((getObj.a == i) && (getObj.b == i) && (getObj.dtlReservedCount == count)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[DoubleInt8_getId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }

    return ret;
}

// 订阅相关(byte)
int DoubleInt4_getId(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc = false)
{
    DoubleInt4St getObj = {};
    int ret = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t bByteLength = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "b", &bByteLength);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t *buf = (uint8_t *)malloc(bByteLength + 1);
    if (buf == NULL) {
        return -1;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", buf, bByteLength, &isNull);
    AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
    free(buf);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[DoubleInt4_getId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }
    return ret;
}

// 订阅相关(byte)
int DoubleInt8_getId(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc = false)
{
    DoubleInt8St getObj = {};
    int ret = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt8_getId] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t bByteLength = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "b", &bByteLength);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t *buf = (uint8_t *)malloc(bByteLength + 1);
    if (buf == NULL) {
        return -1;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", buf, bByteLength, &isNull);
    AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
    free(buf);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt8_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt8_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[DoubleInt8_getId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }
    return ret;
}

// 建表
void CreateTestTable(const char *labelJsonPath, const char *configJson, const char *labelName)
{
    int32_t ret = -1;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "建%s表", labelName);
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)GmcDropVertexLabel(stmt, labelName);
    char *schema = NULL;
    readJanssonFile(labelJsonPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 删表
int32_t DropTestTable(const char *labelName)
{
    int32_t ret = -1;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "删%s表", labelName);
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        testGmcGetLastError();
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int CreateKvTable()
{
    int ret = 0;
    // 创建kv表
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvDropTable(stmt, g_tableName);
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "para1";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char key1[32] = "para2";
    char value1[32] = "aaaaaa";
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1) + 1;
    kvInfo1.value = value1;
    kvInfo1.valueLen = strlen(value1) + 1;
    ret = GmcKvSet(stmt, kvInfo1.key, kvInfo1.keyLen, kvInfo1.value, kvInfo1.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo2 = {0};
    char key2[32] = "para3";
    char value2[64] = {0};
    memset(value2, 'c', 63);
    kvInfo2.key = key2;
    kvInfo2.keyLen = strlen(key2) + 1;
    kvInfo2.value = value2;
    kvInfo2.valueLen = strlen(value2) + 1;
    ret = GmcKvSet(stmt, kvInfo2.key, kvInfo2.keyLen, kvInfo2.value, kvInfo2.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo3 = {0};
    char key3[32] = "para4";
    int64_t value3 = 1000;
    kvInfo3.key = key3;
    kvInfo3.keyLen = strlen(key3) + 1;
    kvInfo3.value = &value3;
    kvInfo3.valueLen = sizeof(int64_t);
    ret = GmcKvSet(stmt, kvInfo3.key, kvInfo3.keyLen, kvInfo3.value, kvInfo3.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview show cap058");
    return ret;
}

const char *g_expectGmjson1 = R"({
    "version": "2.0",
    "type": "record",
    "name": "inp",
    "ylog_type": "update_table",
    "inout_type": "input",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "a",
            "type": "int8"
        },
        {
            "name": "b",
            "type": "int8"
        }
    ],
    "keys": [
        {
            "name": "pk",
            "index_name": "0",
            "index": {
                "type": "primary"
            },
            "node": "inp",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "a"
            ]
        },
        {
            "name": "k1",
            "index_name": "1",
            "index": {
                "type": "hashcluster"
            },
            "node": "inp",
            "constraints": {
                "unique": false
            },
            "fields": [
                "b"
            ]
        }
    ]
})";
const char *g_expectGmjson2 = R"({
    "version": "2.0",
    "type": "record",
    "name": "mid",
    "ylog_type": "normal_table",
    "inout_type": "intermediate",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "a",
            "type": "int8"
        },
        {
            "name": "b",
            "type": "int8"
        }
    ],
    "keys": [
        {
            "name": "pk",
            "index_name": "0",
            "index": {
                "type": "primary"
            },
            "node": "mid",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "a"
            ]
        },
        {
            "name": "k1",
            "index_name": "1",
            "index": {
                "type": "hashcluster"
            },
            "node": "mid",
            "constraints": {
                "unique": false
            },
            "fields": [
                "b"
            ]
        }
    ]
})";

const char *g_expectGmjson3 = R"({
    "version": "2.0",
    "type": "record",
    "name": "out",
    "ylog_type": "normal_table",
    "inout_type": "output",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "a",
            "type": "int8"
        },
        {
            "name": "b",
            "type": "int8"
        }
    ],
    "keys": [
        {
            "name": "pk",
            "index_name": "0",
            "index": {
                "type": "primary"
            },
            "node": "out",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "a"
            ]
        },
        {
            "name": "k1",
            "index_name": "1",
            "index": {
                "type": "hashcluster"
            },
            "node": "out",
            "constraints": {
                "unique": false
            },
            "fields": [
                "b"
            ]
        }
    ]
})";
// a:int1, b:int4
int Int1TypeAndBIsInt4TableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    Int1TypeBIsInt4TableStruct *obj = (Int1TypeBIsInt4TableStruct *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Uint1TypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// a:int1, b:int4
int Int1TypeBIsInt4TableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    Int1TypeBIsInt4TableStruct *checkObj = (Int1TypeBIsInt4TableStruct *)t;
    Int1TypeBIsInt4TableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Int1TypeBIsInt4TableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Int1TypeBIsInt4TableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Int1TypeBIsInt4TableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[Int1TypeBIsInt4TableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (Int1TypeBIsInt4TableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)Int1TypeBIsInt4TableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[Int1TypeBIsInt4TableCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// set keyId b：int4
int32_t DeleteInt1BIsInt4ByKeyId(GmcStmtT *stmt, uint32_t keyId, void *obj, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 0;
    Int1TypeBIsInt4TableStruct *t = (Int1TypeBIsInt4TableStruct *)obj;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 0) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &((t + i)->a), sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((t + i)->b), sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// read int1 b int4
int32_t ReadInt1AndBIsInt4ByKeyID(GmcStmtT *stmt, uint32_t keyId, void *t, int objLen, int32_t upgradeVersion = 0)
{
    int32_t ret = 1;
    Int1TypeBIsInt4TableStruct *obj = (Int1TypeBIsInt4TableStruct *)t;
    ret = GmcSetIndexKeyId(stmt, keyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &((obj)->a), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &((obj)->b), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

int UpdateInt1AndBIsInt4ByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Int1TypeBIsInt4TableStruct *obj = (Int1TypeBIsInt4TableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_FUN_Log(LOG_STEP, "B = %d", obj->b);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
#endif /* __STRUCTDATALOGTABLE_H__ */
