/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#ifndef DATALOGRUN_H
#define DATALOGRUN_H

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include <time.h>
#include <stdarg.h>
#include "t_datacom_lite.h"

typedef int (*FuncTCmp)(const void *st1, const void *st2, bool isPrint);
typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);
typedef int (*FuncWriteId)(GmcStmtT *stmt, int64_t value, int32_t count);
typedef int (*FuncRead)(GmcStmtT *stmt, void *t, int len);
typedef int (*FuncReadId)(GmcStmtT *stmt, int startid, int endid, int32_t count);
// 读pubsub相关的
typedef int (*FuncReadResId)(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isResourcePubSub);
typedef int (*FuncReadRes)(GmcStmtT *stmt, void *t, int len, bool isResourcePubSub);

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

/*
0 : check
1 : debug
2 : dinfo
*/
int g_usrMode = 0;

void SystemSnprintf(const char *format, ...)
{
    va_list p;
    va_start(p, format);
    (void)vsnprintf(g_command, MAX_CMD_SIZE, format, p);
    va_end(p);

    if (g_usrMode == 1) {
        printf("cmd: %s \n", g_command);
    }
    system(g_command);
}

int Debug_executeCommand(char *cmd, const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL,
    const char *v4 = NULL, const char *v5 = NULL)
{
    int ret = 0;
    if (g_usrMode == 0) {
        ret = executeCommand(cmd, v1, v2, v3, v4, v5);
    } else if (g_usrMode == 1) {
        printf("cmd: %s \n", cmd);
        system(cmd);
    } else {
        ret = -1;
    }
    return ret;
}

typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);

template <typename StructObjT>
int writeRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, const char * labelSchemaJson = NULL, int queueLeve = -1,
    int res1 = GMERR_OK)
{
    int ret = 0;
    int tRet = GMERR_OK;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    if (isStruct) {
        char schemaJson[10240] = {0};
        (void)sprintf(schemaJson, labelSchemaJson, labelName, labelName);
        ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    if (queueLeve != -1) {
        int getValue = 0;
        AW_MACRO_EXPECT_EQ_INT(0, TestGetConfigValueInt("datalogQueueNum", &getValue));
    }

    for (int i = 0; i < objLen; i++) {
        ret = (isStruct) ? testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo) : func(stmt, (void *)(obj + i));
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = GmcExecute(stmt);
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcDtlQueueExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcDtlBatchQueueExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

// 主要用到这个，用这个来写表记录
int writeRecordId(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int64_t startid, int64_t endid, int32_t count,
    FuncWriteId func, bool isBatch = true, bool isStruct = false, int queueLeve = -1, bool isFlowCtr = true)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置批写的数据的记录数
        ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    if (queueLeve != -1) {
        int getValue = 0;
        AW_MACRO_EXPECT_EQ_INT(0, TestGetConfigValueInt("datalogQueueNum", &getValue));
    }

    for (int i = startid; i < endid; i++) {
        ret = func(stmt, i, count);
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecordId] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecordId] GmcBatchAddDML fail, set tRet, i: %d, ret = %d.", i, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = isFlowCtr ? GmcExecute(stmt) : GmcExecute(stmt);
            if (ret) {
                AW_FUN_Log(LOG_INFO, "[writeRecordId] GmcExecute fail, ret = %d.", ret);
                break;
            }
        }
    }

    if (isBatch) {
        ret = isFlowCtr ? GmcBatchExecute(batch, &batchRet) : GmcBatchExecute(batch, &batchRet);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "[writeRecordId] GmcBatchExecute fail, ret = %d.", ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }

    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecordId] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

template <typename StructObjT>
int readRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncRead func,
    bool checkRecord = true, int *outCnt = NULL)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = func(stmt, (void *)obj, objLen);
        if ((checkRecord) && (ret)) {
            AW_FUN_Log(LOG_ERROR, "[readRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        cnt++;
    }
    if (outCnt) {
        *outCnt = cnt;
    }
    if ((checkRecord) && (objLen != cnt)) {
        tRet = (tRet == GMERR_OK) ? -1 : tRet;
        AW_FUN_Log(LOG_ERROR, "[readRecord]  set tRet, cnt = %d, objLen = %d, tRet = %d.", cnt, objLen, tRet);
    } else if (g_usrMode == 1) {
        AW_FUN_Log(LOG_DEBUG, "[readRecord] fetch %s finish, cnt: %d, objLen: %d.", labelName, cnt, objLen);
    }

    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[readRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

// 用这个接口来进行读数据
int readRecordId(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int startid, int endid, int32_t count,
    FuncReadId func, bool checkRecord = true, int *outCnt = NULL)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = func(stmt, startid, endid, count);
        if (checkRecord) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        cnt++;
    }
    if (checkRecord) {
        EXPECT_EQ((endid - startid), cnt);
    } else {
        if (outCnt) {
            *outCnt = cnt;
        }
        if (g_usrMode == 1) {
            printf("[readRecord] fetch finish, cnt: %d. \n", cnt);
        }
    }

    return ret;
}

#define MAX_NAME_LENGTH 512

typedef int (*FuncCheck)(GmcStmtT *stmt, void *id, void *df, int32_t c);

typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    int tableType;  // 0:out 1:resource
    int funcType;   // 0:id 1:struct
    char *nsName;
    bool isResourcePubSub;  // false:notify表， true：pubsub资源表
    union {
        struct {
            FuncCheck checkIdFunc;
            FuncReadResId readResIdFunc;
            FuncWriteId writeIdFunc;
            int startid;
            int endid;
            int32_t count;
        };
        struct {
            FuncRead readFunc;
            FuncReadRes readResFunc;
            FuncWrite writeFunc;
            int objLen;
            union {
                void *obj;
            };
        };
    };
} SnUserDataWithFuncT;
int snCallbackCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    char *nsName = userDefinedData->nsName;
    // 区分是id还是struct
    int funcType = userDefinedData->funcType;
    bool isResourcePubSub = userDefinedData->isResourcePubSub;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);

    // 区分notify表和pubsub资源表
    if (!isResourcePubSub) {  // notify表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        RETURN_IFERR(ret);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
        ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        RETURN_IFERR(ret);
    } else {  // pubsub型资源表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
        RETURN_IFERR(ret);
    }
    printf("funcType = %d\n", funcType);
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 区分id还是struct
                    if (funcType == 0) {
                        // notify表
                        if (!isResourcePubSub) {
                            ret = userDefinedData->readResIdFunc(subStmt, userDefinedData->startid,
                                userDefinedData->endid, userDefinedData->count, isResourcePubSub);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        } else {
                            // pubsub资源表
                            ret = userDefinedData->readResIdFunc(subStmt, userDefinedData->startid,
                                userDefinedData->endid, userDefinedData->count, isResourcePubSub);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcSubAddRespDML(response, subStmt);
                            RETURN_IFERR(ret);
                        }
                    } else {
                        // notify表
                        if (!isResourcePubSub) {
                            ret = userDefinedData->readResFunc(
                                subStmt, userDefinedData->obj, userDefinedData->objLen, isResourcePubSub);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        } else {
                            // pubsub资源表
                            ret = userDefinedData->readResFunc(
                                subStmt, userDefinedData->obj, userDefinedData->objLen, isResourcePubSub);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcSubAddRespDML(response, subStmt);
                            RETURN_IFERR(ret);
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }

    // 发送消息
    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return GMERR_OK;
}

void snCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = snCallbackCheck(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

#endif
