/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <string.h>
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#define MAX_CMD_SIZE 1024

#pragma pack(1)
typedef struct B {
    int64_t a;
    int64_t b;
    int64_t c;
} B;
#pragma pack(0)

const char *g_errorLogName = "/root/_datalog_/func.txt";

int TestViewData(char *cmd, const char *filepath)
{
    int ret = 0;
    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        return -1;
    }
    FILE *fp = fopen(filepath, "w+");
    if (fp == NULL) {
        return -1;
    }
    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        (void)fprintf(fp, "%s", buf);
    }
    pclose(fd);
    (void)fclose(fp);
    return ret;
}

int32_t dtl_ext_func_readCapKV(void *tuple, GmUdfCtxT *ctx)
{
    int ret = 0;
    B *b = (B *)tuple;
    b->c = b->a + b->b;

    FILE *fp = fopen(g_errorLogName, "w+");
    if (fp == NULL) {
        return -1;
    }
    // 查找不存在的key
    char key[32] = "para5";
    uint32_t keylen = strlen(key) + 1;
    int32_t value = 0;
    uint32_t valuelen = sizeof(int32_t);
    // 需要校验返回的值
    ret = GmUdfGetAccessKV(ctx, key, keylen, &value, &valuelen);
    (void)fprintf(fp, "[%s] errorCode is %d.\n", __FILE__, ret);
    (void)fclose(fp);
    if (ret != GMERR_OK) {
        return -1;
    }

    return GMERR_OK;
}
