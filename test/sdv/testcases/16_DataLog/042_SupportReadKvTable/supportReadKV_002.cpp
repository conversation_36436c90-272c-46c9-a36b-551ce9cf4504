/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDB 503.1.0 迭代一支持规则读取能力集KV表
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2023.07.14]
*****************************************************************************/
#include "DatalogReadKvSubFunc.h"
#include "DatalogRun.h"

using namespace std;

class supportReadKV_002_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void supportReadKV_002_test::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    AW_CHECK_LOG_BEGIN(); 
}
void supportReadKV_002_test::TearDown()
{
    AW_CHECK_LOG_END();
    system("rm -rf ./datalogFile/planStrFile/*");
} 

/* ****************************************************************************
 Description  : 001.access_kv关键字无下划线
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error01.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error01.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in function \"readCapsetKV\": accesskv near line 3.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.access_kv关键字全部为大写
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error02.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error02.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in function \"readCapsetKV\": ACCESS_KV near line 3.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.access_kv关键字含大小混合
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error03.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error03.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in function \"readCapsetKV\": Access_Kv near line 3.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.access_kv关键字拼写错误
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error04.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error04.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in function \"readCapsetKV\": acess_kv near line 3.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.access_kv关键字中间含空格
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error05.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error05.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: '_kv' near line 4.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.access_kv关键字中间为空格
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error06.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error06.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: 'kv' near line 4.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.access_kv关键字少1个小括号
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error07.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error07.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: '}' near line 5.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.access_kv关键字小括号为大括号
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error08.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error08.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: '{' near line 4.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 疑问
/* ****************************************************************************
 Description  : 009.access_kv关键字小括号为中括号
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error09.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error09.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, unknown character near line 4.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.access_kv关键字中声明的表后面有个逗号
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error10.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error10.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: ')' near line 4.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 疑问
/* ****************************************************************************
 Description  : 011.access_kv关键字中未声明表
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error11.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error11.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: ')' near line 4.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.access_kv关键字中声明多个表
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error12.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error12.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: \"access_kv\" parameter num should be one in "
        "relation \"readCapsetKV\" near line 4.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.access_kv关键字中含数字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error13.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error13.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in function \"readCapsetKV\": access_kv12 near line 3.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.access_kv关键字中含特殊符号
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error14.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error14.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, invalid character: '@' near line 4.",
        g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.普通表中定义access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error01.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error01.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"inp1\": access_kv near line 1.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.全部可更新表中未在update_by_rank后面定义access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error02.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error02.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"inp1\": access_kv near line 1.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.部分可更新表中未在update_by_rank后面定义access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error03.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error03.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"inp1\": access_kv near line 1.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.transient(tuple)中定义access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error04.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error04.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"inp1\": access_kv near line 1.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.transient(field)中定义access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error05.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error05.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"B\": access_kv near line 5.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.在没有回调函数的过期表添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error06.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error06.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: the second parameter of timeout should be \"state_function\" "
        "in table \"A\" near line 1.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.在含回调函数的过期表中，未回调函数中添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error07.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error07.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"A\": access_kv near line 1.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.在tbm表中，添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error08.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error08.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"outA\": access_kv near line 2.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.在msg_notify表中，添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error09.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error09.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"out1\": access_kv near line 2.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.在external外部表中，添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error10.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error10.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"outA01\": access_kv "
        "near line 2.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.在状态表中，添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error11.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error11.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"B\": access_kv near line 2.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.在pubsub普通表中，添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error12.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error12.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in table \"out1\": access_kv near line 2.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.在固定资源表中，添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error13.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error13.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in resource \"A\": access_kv near line 1.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.在pubsub资源型表中，添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error14.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error14.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: invalid option in resource \"A\": access_kv near line 1.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.在namespace中，添加access_kv关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/namespace_error01.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/namespace_error01.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: 'out1' near line 5.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 030.access_kv关键字声明表的名字为513字符
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error15.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error15.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: table name length 513 exceeded the upper limit 512 in "
        "\"access_kv\" of relation \"readCapsetKV\"", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.access_kv关键字声明表的名字为512字符
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/success01.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/success01.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.在TBM表中的init函数添加关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/func_error01.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/func_error01.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    // 预期失败
    // 2024年1月24日变更；init函数中可以添加access_kv
    int ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.在TBM表中的uninit函数添加关键字
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/func_error02.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/func_error02.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    // 预期失败
    // 2024年1月24日变更；init函数中可以添加access_kv
    int ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.在普通函数中，capset_kv关键字声明的kv表名含非法字符
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error16.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error16.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, invalid character: '@' near line 4.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.在普通函数中，capset_kv关键字声明的kv表名以数字为开头
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error17.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error17.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d.", GMERR_SYNTAX_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, "
        "syntax error: 'capset' near line 4.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2023071807749
/* ****************************************************************************
 Description  : 036.在普通函数中，capset_kv关键字声明的kv表名含大小写数字以及特殊字符
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/success02.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/success02.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.在过期表的回调函数中，使用access_kv关键字声明kv表
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/success03.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/success03.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.在更新表中的update_by_rank中添加access_kv关键字声明kv表
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/updatetable_error01.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/updatetable_error01.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: syntax error for the option of relation \"inp1\": "
        "\"update_by_rank\" accepts no parameter near line 4." , g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.在agg函数中，添加access_kv关键字声明kv表
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/success04.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/success04.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 040.在状态转移函数中，添加access_kv关键字声明的kv表
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/success05.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/success05.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.普通函数中，定义两个access_kv关键字声明不同的表
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/keyword_error15.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/keyword_error15.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: duplicate option in relation \"readCapsetKV\": \"access_kv\" "
        "near line 4." , g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 042.access_kv关键字中，定义的表名为9
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error18.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error18.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: table name illegal in \"access_kv\" of relation "
        "\"readCapsetKV\": 9 near line 4." , g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 043.access_kv关键中，定义的表名为字符串"cap"
**************************************************************************** */
TEST_F(supportReadKV_002_test, DataLog_042_002_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFile[FILE_PATH] = "./datalogFile/compiler/table_error19.d";
    char outputFile[FILE_PATH] = "./datalogFile/planStrFile/table_error19.c";
    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, outputFile);
    system(g_command);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期失败
    int ret = executeCommand(g_command, "Error: table name illegal in \"access_kv\" of relation "
        "\"readCapsetKV\": \"capset\" near line 4." , g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
