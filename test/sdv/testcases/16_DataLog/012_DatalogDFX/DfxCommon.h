/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: pubsub.h
 * Description: datalog pubsub
 * Author: yuh<PERSON>lei y30033205
 * Create: 2022-09-14
 */

#ifndef __DFXCOMM0N_H__
#define __DFXCOMM0N_H__

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <pthread.h>
#include <vector>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
#define FILE_PATH 512
#define LABEL_NAME 512
#define PRINT_INFO 0
#define THR_NUM 50

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_tht[THR_NUM * 2];
GmcStmtT *g_stmt_tht[THR_NUM * 2];

char g_hFile[FILE_PATH] = "../../../../../pub/include/";

const char *g_schemaJson1 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

/*---------------------------------------------------------编译-------------------------------------------------------*/
void TestCompile(char *datalogFile, char *outputFile, char *udfFile, char *libName)
{
    char command[MAX_CMD_SIZE] = {0};
    // .d->.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, datalogFile, outputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    if (udfFile != NULL) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared -g %s %s -o %s \n", g_hFile,
            outputFile, udfFile, libName);
    } else {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared -g %s -o %s \n", g_hFile,
            outputFile, libName);
    }
    system(command);
}

/*--------------------------------------表结构------------------------------------*/
// table(a, b, c, d)
#pragma pack(1)
typedef struct {
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
} TEST_INPUT;
#pragma pack()

// table(a, b, c, d)
#pragma pack(1)
typedef struct {
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
} TEST_OUTPUT;
#pragma pack()

/*--------------------------------------set value------------------------------------*/
void setPkValue(GmcStmtT *stmt, int64_t value[])
{
    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &value[0], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &value[1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void setOtherValue(GmcStmtT *stmt, int64_t value[])
{
    int ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &value[2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void setTimeoutValue(GmcStmtT *stmt, int64_t value[], int64_t current)
{
    // 过期字段
    int64_t dValue = current + (int64_t)(value[3] * 60 * 60 * 1000);
    int ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &dValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = (int32_t)value[4];
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void StructSetValue(TEST_INPUT *d, int64_t value[], int64_t current)
{
    d->a = value[0];
    d->b = value[1];
    d->c = value[2];
    // 过期字段
    int64_t dValue = current + (int64_t)(value[3] * 60 * 60 * 1000);
    d->d = dValue;
    int32_t count = (int32_t)value[4];
    d->dtlReservedCount = count;
}
/*--------------------------------------write table------------------------------------*/
// 删除指定记录，这里只是往前移动数组，并未实现真正的删除，后续需要完善
void DeleteElement(int64_t arr[][5], int index, int len)
{
    for (int i = 0; i < len - 1; i++) {
        if (i < index) {
            for (int j = 0; j < 5; j++) {
                arr[i][j] = arr[i][j];
            }
        } else {
            for (int j = 0; j < 5; j++) {
                arr[i][j] = arr[i + 1][j];
            }
        }
    }
}

// 单写(a, b, c, d)
int singleWriteTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        setPkValue(stmt, value[i]);
        setOtherValue(stmt, value[i]);
        setTimeoutValue(stmt, value[i], current);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
// 结构化单写(a, b, c, d)
int structWriteTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    TEST_INPUT objIn = (TEST_INPUT){0};
    for (int i = 0; i < dataNum; i++) {
        // set value
        StructSetValue(&objIn, value[i], current);
        ret = testStructSetVertexWithBuf(stmt, &objIn, labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
// 批写(a, b, c, d)
int batchWriteTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    // 批写
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        setPkValue(stmt, value[i]);
        setOtherValue(stmt, value[i]);
        setTimeoutValue(stmt, value[i], current);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchDestroy(batch);
    return ret;
}
// 结构化批写(a, b, c, d)
int structBatchWriteTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret;
    // 批写
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构化
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TEST_INPUT objIn = (TEST_INPUT){0};
    for (int i = 0; i < dataNum; i++) {
        // set value
        StructSetValue(&objIn, value[i], current);
        ret = testStructSetVertexWithBuf(stmt, &objIn, labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化读表(a, b, c, d)
int structReadTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    TEST_OUTPUT objOut = (TEST_OUTPUT){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, labelInfo);

    int record = dataNum;
    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 获取的是时间字段，需要转化一下
        objOut.d = (objOut.d - current) / (int64_t)(60 * 60 * 1000);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d", objOut.a, objOut.b, objOut.c, objOut.d,
                objOut.dtlReservedCount);
        }
        // 校验值
        int loop = 0;
        for (int i = 0; i < record; i++) {
            if (value[i][0] == objOut.a && value[i][1] == objOut.b && value[i][2] == objOut.c &&
                value[i][3] == objOut.d && (int32_t)value[i][4] == objOut.dtlReservedCount) {
                // 已经匹配上，那么value数组删除该条记录
                DeleteElement(value, i, record);
                loop += 1;
                sum++;
                break;
            }
        }
        if (loop == 1) {
            record -= 1;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);
    AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

/*-------------------------------------update table------------------------------------*/
// 单写，更新数据(a, b, c, d)，可更新表，非主键字段可以不设置
int singleUpdateTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        setPkValue(stmt, value[i]);
        setTimeoutValue(stmt, value[i], current);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return ret;
}
// 批写，更新数据(a, b, c, d)，可更新表，非主键字段可以不设置
int batchUpdateTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    // 批写
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        setPkValue(stmt, value[i]);
        setTimeoutValue(stmt, value[i], current);
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            return ret;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return ret;
}
/*-------------------------------------write table but not all property was set------------------------------------*/
int singleErrorWriteTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        setOtherValue(stmt, value[i]);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

int batchErrorWriteTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    // 批写
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < dataNum; i++) {
        // set value
        setOtherValue(stmt, value[i]);
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            return ret;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return ret;
}

/*--------------------------------------read table------------------------------------*/
// 非结构化读表(a, b, c, d)
int readTable(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo)
{
    int ret = 0;
    int record = dataNum;
    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    bool isNull;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int64_t readA;
        ret = GmcGetVertexPropertyByName(stmt, "a", &readA, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t readB;
        ret = GmcGetVertexPropertyByName(stmt, "b", &readB, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t readC;
        ret = GmcGetVertexPropertyByName(stmt, "c", &readC, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 过期字段
        int64_t readD;
        ret = GmcGetVertexPropertyByName(stmt, "d", &readD, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t readCount;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &readCount, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 获取的是时间字段，需要转化一下
        readD = (readD - current) / (int64_t)(60 * 60 * 1000);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld %ld %d", readA, readB, readC, readD, readCount);
        }

        // 校验值
        int loop = 0;
        for (int i = 0; i < record; i++) {
            if (value[i][0] == readA && value[i][1] == readB && value[i][2] == readC && value[i][3] == readD &&
                (int32_t)value[i][4] == readCount) {
                // 已经匹配上，那么value数组删除该条记录
                DeleteElement(value, i, record);
                loop += 1;
                sum++;
                break;
            }
        }
        if (loop == 1) {
            record -= 1;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);
    AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    return ret;
}

/*--------------------------------------读/写函数入口------------------------------------*/
// 函数指针
typedef int (*OperFuncHandlePtr)(GmcConnT *conn, GmcStmtT *stmt, int64_t value[][5], int dataNum, int64_t current,
    TestLabelInfoT *labelInfo);

typedef struct {
    const char *desc;
    OperFuncHandlePtr phandle;
} OperType;

// 操作类型
typedef enum {
    SINGLE_INSERT,
    SINGLE_STRUCT_INSERT,
    BATCH_INSERT,
    BATCH_STRUCT_INSERT,
    SINGLE_UPDATE,
    BATCH_UPDATE,
    SINGLE_ERR0R_WRITE,
    BATCH_ERR0R_WRITE,
    READ,
    STRUCT_READ,
} OperTypeE;

// 函数驱动表
static OperType g_eventMap[] = {{"single insert", singleWriteTable}, {"single struct insert", structWriteTable},
    {"batch insert", batchWriteTable}, {"batch struct insert", structBatchWriteTable},
    {"single update", singleUpdateTable}, {"batch update", batchUpdateTable},
    {"some property not set single", singleErrorWriteTable}, {"some property not set batch", batchErrorWriteTable},
    {"read table", readTable}, {"struct read table", structReadTable}};
int testTable(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, OperTypeE type, int64_t value[][5], int dataNum, int64_t current)
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, labelName, labelName);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (type < SINGLE_INSERT || type > STRUCT_READ) {
        return -1;
    }
    bool walkDataService;
    if (type >= SINGLE_INSERT && type <= BATCH_ERR0R_WRITE) {
        // 写操作
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        // 读操作
        AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    OperType handle = g_eventMap[type];
    ret = handle.phandle(conn, stmt, value, dataNum, current, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    return ret;
}

/*--------------------------------------表字段(a:int1, b:int2, c:int4, d:int8)------------------------------------*/
// 找元素
int FindElement(
    int64_t arr[][5], int64_t valueA, int64_t valueB, int64_t valueC, int64_t valueD, int32_t valueCount, int len)
{
    for (int i = 0; i < len; i++) {
        if (valueA == arr[i][0] && valueB == arr[i][1] && valueC == arr[i][2] && valueD == arr[i][3] &&
            valueCount == arr[i][4]) {
            return 1;
        }
    }
    return -1;
}
// 单写(a:int4, b:int2, c:int1, d:int8)
int singleWriteTableAllInt(
    GmcConnT *conn, GmcStmtT *stmt, char *labelName, int64_t value[][5], int dataNum, int64_t current)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < dataNum; i++) {
        // set value
        int32_t aValue = value[i][0] % 128;
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &aValue, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int16_t bValue = value[i][1] % 32768;
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &bValue, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t cValue = value[i][2];
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT8, &cValue, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 过期字段
        int64_t dValue = current + (int64_t)(value[i][3] * 60 * 60 * 1000);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &dValue, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t countValue = value[i][4];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &countValue, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
// 读(a:int4, b:int2, c:int1, d:int8)
int readTableAllInt(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int64_t value[][5], int dataNum, int64_t current)
{
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    bool isNull;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int8_t readA;
        ret = GmcGetVertexPropertyByName(stmt, "a", &readA, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int16_t readB;
        ret = GmcGetVertexPropertyByName(stmt, "b", &readB, sizeof(int16_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t readC;
        ret = GmcGetVertexPropertyByName(stmt, "c", &readC, sizeof(int8_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 过期字段
        int64_t readD;
        ret = GmcGetVertexPropertyByName(stmt, "d", &readD, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t readCount;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &readCount, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 获取的是时间字段，需要转化一下
        readD = (readD - current) / (int64_t)(60 * 60 * 1000);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %ld %d", readA, readB, readC, readD, readCount);
        }
        // 校验值
        ret = FindElement(value, (int64_t)readA, (int64_t)readB, (int64_t)readC, readD, readCount, dataNum);
        if (ret == 1) {
            sum++;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);
    AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    return ret;
}
/*-----------------------------------read HPR table (a:int4, b:int2, c:int1, d:int8)---------------------------------*/
// hpr table(a, b, c, d)
#pragma pack(1)
typedef struct {
    int32_t a;
    int16_t b;
    int8_t c;
    int64_t d;
} TEST_OUTPUT_03;
#pragma pack()
// 找元素
void GetHpeTime(char *result, int resLen)
{  // HPE使用世界协调时间UTC 而不是CST
    FILE *pf;
    if (g_envType == 2) {
        pf = popen("date \"+%Y-%m-%d-%H:%M:%S\" ", "r");
    } else {
        pf = popen("date \"+%Y-%m-%d-%H:%M:%S\" ", "r");
    }
    if (pf == NULL) {
        printf("popen time error.\n");
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(result, resLen, pf)) {
    };
    int tee = pclose(pf);
    if (tee != 0) {
        printf("error");
    }
}
void GetHpeTimeInterval(char *result, int resLen)
{
    // HPE使用世界协调时间UTC 而不是CST
    FILE *pf;
    if (g_envType == 2) {
        pf = popen("date \"+%Y-%m-%d-%H:%M:%S\" ", "r");
    } else {
        pf = popen("date -d \"+1 min\" \"+%Y-%m-%d-%H:%M:%S\" ", "r");
    }
    if (pf == NULL) {
        printf("popen time error.\n");
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(result, resLen, pf)) {
    };
    int tee = pclose(pf);
    if (tee != 0) {
        printf("error");
    }
}
void TestUdfView()
{
    char command[MAX_CMD_SIZE] = {0};
    char const *viewName = "V\\$CATA_UDF_INFO";
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s", viewName);
    system(command);
    int ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int GetPrintBycmd(char *cmd, int *value)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }
    char cmdOutput[64] = {0};
    while (fgets(cmdOutput, 64, pf) != NULL) {
    }
    *value = atoi(cmdOutput);
    pclose(pf);
    return 0;
}
#endif
