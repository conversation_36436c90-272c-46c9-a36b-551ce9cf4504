/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: DlgSupUint.cpp
 * Description: DATALOG SUPPORT UINT TYPE
 * Author: youwanyong ywx1157510
 * Create: 2024-02-07
 */
#include "UniversalTools.h"
#include "t_datacom_lite.h"

using namespace std;
class DlgSupUint : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        system("rm -rf /root/_datalog_/");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void DlgSupUint::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void DlgSupUint::TearDown()
{
    AW_CHECK_LOG_END();
}

// 197.不支持.d中外部表byte 长度4 对应外部表fixed长度4
TEST_F(DlgSupUint, DataLog_068_197)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持.d中外部表byte 长度4 对应外部表fixed长度4";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0197";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0197.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 198.不支持写数据byte 数据类型长度4使用byte4进行设置
TEST_F(DlgSupUint, DataLog_068_198)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据byte 数据类型长度4使用byte4进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0198";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valueA = 2;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_FIXED, &valueA, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 199.byte不支持byte 长度4投影到byte4
TEST_F(DlgSupUint, DataLog_068_199)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "byte不支持byte 长度4投影到byte4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0199";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": FIXED vs BYTES near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 200.byte不支持作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_200)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "byte不支持作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0200";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 201.byte不支持作为固定资源表字段
TEST_F(DlgSupUint, DataLog_068_201)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "byte不支持作为固定资源表字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0201";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid\" doesn't support variable-length byte fields near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 202.byte不支持作为pubsub资源表字段
TEST_F(DlgSupUint, DataLog_068_202)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "byte不支持作为pubsub资源表字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0202";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid3\" doesn't support variable-length byte fields near line 9.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 203.不支持常量超过10K
TEST_F(DlgSupUint, DataLog_068_203)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0203";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "不支持常量超过10K.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "byte length 10241 exceeds limit (10240) near line 3.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 204.trisent field 字段不支持为byte
TEST_F(DlgSupUint, DataLog_068_204)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "trisent field 字段不支持为byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0204";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 205.所有表定义不支持定义5个变长byte*******************************************************
TEST_F(DlgSupUint, DataLog_068_205)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "所有表定义不支持定义5个变长byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0205";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "there are 5 variable-length byte fields in relation \"I000\" which exceeds the limit (4) near line 63.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 206.表中不支持63个字段均为byte，不显示定义主键
TEST_F(DlgSupUint, DataLog_068_206)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中不支持63个字段均为byte，不显示定义主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0206";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 207.byte不支持定义为二级索引
TEST_F(DlgSupUint, DataLog_068_207)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "byte不支持定义为二级索引";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0207";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any variable-length byte field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 208.byte字段不支持定义为主键
TEST_F(DlgSupUint, DataLog_068_208)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "byte字段不支持定义为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0208";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any variable-length byte field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 209.聚合函数支持含byte字段,group-by不支持
TEST_F(DlgSupUint, DataLog_068_209)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "聚合函数支持含byte字段,group-by不支持";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0209";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "group_by contains variable-length byte fields for table \"mid\" near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 210.group-by字段为byte
TEST_F(DlgSupUint, DataLog_068_210)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "group-by字段为byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0210";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "group_by contains variable-length byte fields for table \"mid\" near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson211 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int8"},
            {"name" : "c", "type" : "bytes"}
        ]
    } ])";
// 211.投影规则表中支持含byte：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】）
TEST_F(DlgSupUint, DataLog_068_211)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0211";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson211, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    ByteTypeTableStruct *objIn1 = (ByteTypeTableStruct *)malloc(sizeof(ByteTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(ByteTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].len = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
        ret = Generate_Bytes(&objIn1[i].buf, objIn1[i].len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteByteByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadByteByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        if (i > 4) {
            objIn1[i].a = (i + 1) % 255;
            objIn1[i].b = (i + 1) % 5;
            objIn1[i].dtlReservedCount = 1;
            objIn1[i].upgradeVersion = 0;
        }
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateByteByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, ByteTypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteByteByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf);
    }
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 212.join规则表中支持含byte：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表】）
TEST_F(DlgSupUint, DataLog_068_212)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0212";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0212.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson211, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson211, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    ByteTypeTableStruct *objIn1 = (ByteTypeTableStruct *)malloc(sizeof(ByteTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(ByteTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].len = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
        ret = Generate_Bytes(&objIn1[i].buf, objIn1[i].len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, ByteTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, ByteTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteByteByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadByteByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateByteByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, ByteTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteByteByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf);
    }
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 213.笛卡尔规则表中支持含byte:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_213)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0213";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson211, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson211, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    ByteTypeTableStruct *objIn1 = (ByteTypeTableStruct *)malloc(sizeof(ByteTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(ByteTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
        objIn1[i].len = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf, objIn1[i].len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteByteByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadByteByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateByteByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteByteByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");

    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf);
    }
    free(objIn1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson214 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "bytes"}
        ]
    } ])";
// 214.忽略规则表中支持含byte:【输入表：含update_by_rank可更新】,【中间表: triansent field】【出表：消息通知表】
TEST_F(DlgSupUint, DataLog_068_214)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0214";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson214, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson214, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    ByteTypeBIsInt4TableStruct *objIn1 =
        (ByteTypeBIsInt4TableStruct *)malloc(sizeof(ByteTypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(ByteTypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].len = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf, objIn1[i].len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, ByteTypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, ByteTypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(5, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, ByteTypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteByteBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadByteAndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateByteAndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(5, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteByteBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadByteAndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadByteAndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "87025d9a198bbf1d80bea7ac27b146f3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "47abc7e723a58a7aeb3d9b97e6aa9b35");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "abf8aefb53bf4a9156d0c0b41098da10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf);
    }
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson215 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int64"},
            {"name" : "c", "type" : "bytes"}
        ]
    } ])";
// 215.not join规则表中支持含byte:【输入表：过期表】,【中间表: tuple表】【输出表：普通表】
TEST_F(DlgSupUint, DataLog_068_215)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0215";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson215, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson215, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    ByteTypeBIsInt8TableStruct *objIn1 =
        (ByteTypeBIsInt8TableStruct *)malloc(sizeof(ByteTypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(ByteTypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].len = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf, objIn1[i].len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, ByteTypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(4);
    ret = readRecord("inp", objIn1, 5, ByteTypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, ByteTypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(4);
    ret = readRecord("inp", objIn1, 10, ByteTypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, ByteTypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteByteAndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadByteAndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateByteAndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    sleep(4);
    ret = readRecord("inp", objIn1 + 5, 5, ByteTypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, ByteTypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, ByteTypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(4);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteByteAndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadByteAndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadByteAndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf);
    }
    free(objIn1);

    // // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 216.常量规则表中支持含byte:【输入表：tuple表】,【中间表: finish表】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_216)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含byte";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0216";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson214, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson214, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    ByteTypeBIsInt4TableStruct *objIn1 =
        (ByteTypeBIsInt4TableStruct *)malloc(sizeof(ByteTypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(ByteTypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].len = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf, objIn1[i].len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, ByteTypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, ByteTypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", objIn1, 5, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, ByteTypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteByteBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadByteAndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateByteAndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("out", objIn1 + 5, 5, ByteTypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, ByteTypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteByteBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("out", objIn1, 10, ReadByteAndBIsInt4ByKeyID, 1, 0, true);
    AW_MACRO_EXPECT_EQ_INT(2, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf);
    }
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson217 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int8"},
            {"name" : "c1", "type" : "bytes"},
            {"name" : "c2", "type" : "bytes"},
            {"name" : "c3", "type" : "bytes"},
            {"name" : "c4", "type" : "bytes"}
        ]
    } ])";
// 217.表中支持含byte支持含外部表，输入表finish join function function含5个byte字段
TEST_F(DlgSupUint, DataLog_068_217)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含byte支持含外部表";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0217";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0217.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson217, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson217, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Byte4TypeTableStruct *objIn1 = (Byte4TypeTableStruct *)malloc(sizeof(Byte4TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Byte4TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].len1 = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf1, objIn1[i].len1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].len2 = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf2, objIn1[i].len2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].len3 = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf3, objIn1[i].len3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].len4 = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf4, objIn1[i].len4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Byte4TypeTableStruct *out1 = (Byte4TypeTableStruct *)malloc(sizeof(Byte4TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Byte4TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].len1 = (i + 1) % 255;
        ret = Generate_Bytes(&out1[i].buf1, out1[i].len1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        out1[i].len2 = (i + 1) % 255;
        ret = Generate_Bytes(&out1[i].buf2, out1[i].len2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        out1[i].len3 = (i + 1) % 255;
        ret = Generate_Bytes(&out1[i].buf3, out1[i].len3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        out1[i].len4 = (i + 1) % 255;
        ret = Generate_Bytes(&out1[i].buf4, out1[i].len4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Byte4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Byte4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Byte4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Byte4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Byte4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Byte4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Byte4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Byte4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteByte4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除失败
    ret = readRecordId("inp1", objIn1, 10, ReadByte4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    for (int i = 0; i < recordNum; i++) {
        if (i > 4) {
            objIn1[i].a = (i + 1) % 255;
            objIn1[i].b = (i + 1) % 5;
            objIn1[i].dtlReservedCount = 1;
            objIn1[i].upgradeVersion = 0;
        }
    }

    for (int i = 0; i < recordNum; i++) {
        if (i > 4) {
            out1[i].a = (i + 1) % 255;
            out1[i].b = (i + 1) % 5;
            out1[i].dtlReservedCount = 1;
            out1[i].upgradeVersion = 0;
        }
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateByte4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Byte4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Byte4TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteByte4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadByte4ByKeyID, 1, 0, true);
    AW_MACRO_EXPECT_EQ_INT(9, ret);
    system("gmsysview count out");

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf1);
        free(objIn1[i].buf2);
        free(objIn1[i].buf3);
        free(objIn1[i].buf4);
    }

    for (int i = 0; i < recordNum; i++) {
        free(out1[i].buf1);
        free(out1[i].buf2);
        free(out1[i].buf3);
        free(out1[i].buf4);
    }
    free(objIn1);
    free(out1);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 218.表中不支持含byte支持.d中62个字段为byte，显示定义主键
TEST_F(DlgSupUint, DataLog_068_218)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中不支持含byte支持.d中62个字段为byte，显示定义主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0218";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        " there are 62 variable-length byte fields in relation \"inp\" which exceeds the limit (4) near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 219.表中支持含byte，含 precedence规则及级联删除
TEST_F(DlgSupUint, DataLog_068_219)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含byte，含 precedence规则及级联删除";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0219";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson211, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson211, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson211, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    ByteTypeTableStruct *objIn1 = (ByteTypeTableStruct *)malloc(sizeof(ByteTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(ByteTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].len = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf, objIn1[i].len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, ByteTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateByteByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateByteByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateByteByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, ByteTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, ByteTypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, ByteTypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, ByteTypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadByteByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf);
    }
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 220.表中含4个byte，其余字段为byte512，规则为笛卡尔积，批写数据，订阅接收
TEST_F(DlgSupUint, DataLog_068_220)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中含4个byte，其余字段为byte512，规则为笛卡尔积，批写数据，订阅接收";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0220";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;  // MB
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./schema_file/update_pubsub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = MaxgetId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    MaxTypeTableStruct *objIn1 = (MaxTypeTableStruct *)malloc(sizeof(MaxTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(ByteTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[i].a[j] = (i + 1) % 50;
            objIn1[i].a[10] = (i + 1) % 3;
            objIn1[i].a[20] = (i + 1) % 5;
        }
        objIn1[i].len = (i + 1) % 255;
        ret = Generate_Bytes(&objIn1[i].buf, objIn1[i].len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    int num = 50;
#if defined ENV_RTOSV2X
    num = 10;
#endif
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=out");
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, num, MaxTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, num, MaxTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, num, MaxTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp", objIn1, num, MaxTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, num * num, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt,subNameUpdate);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    // 卸载so
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].buf);
    }
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 221.so升级新增表含所有数据类型，预期升级成功，阻塞  相关视图，离线及在线
TEST_F(DlgSupUint, DataLog_068_221)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，预期升级成功，阻塞  相关视图，离线及在线";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char patchSoName[FILE_PATH] = "DataLog_068_0221_patchV2";
    char rollBackSoName[FILE_PATH] = "./datalog_file/DataLog_068_0221_rollbackV2.so";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[i].a = (i + 1) % 255;
            objIn1[i].b = (i + 1) % 255;
            objIn1[i].c = 0;
            objIn1[i].d = (i + 1) % 255;
            for (int j = 0; j < 1; j++) {
                objIn1[i].e[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 128; j++) {
                objIn1[i].f[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 256; j++) {
                objIn1[i].g[j] = (i + 1) % 50;
            }
            objIn1[i].a1 = (i + 1) % 255;
            objIn1[i].b1 = (i + 1) % 255;
            objIn1[i].c1 = (i + 1) % 255;
            objIn1[i].d1 = (i + 1) % 255;

            objIn1[i].e1 = (i + 1) % 255;
            objIn1[i].f1 = (i + 1) % 255;
            objIn1[i].g1 = (i + 1) % 255;
            objIn1[i].a2 = (i + 1) % 255;
        }
        objIn1[i].a3len = (i + 1) % 255;
        objIn1[i].a4len = (i + 1) % 255;
        objIn1[i].a3len = 10;
        objIn1[i].a3buf = strT;
        (void)snprintf((char *)objIn1[i].a3buf, 10, "s%08d", (i + 1) % 10);
        ret = Generate_Bytes(&objIn1[i].a4buf, objIn1[i].a4len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写10条数据
    ret = writeRecord(conn, stmt, "C000", objIn1, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("B000", objIn1, 10, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("A000", objIn1, 10, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据

    ret = readRecord("B000", objIn1, 10, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("A000", objIn1, 10, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "New", objIn1 + 10, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "C000", objIn1 + 10, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        if (i >= 10) {
            objIn1[i].upgradeVersion = 1;
        }
    }

    ret = readRecord("B000", objIn1, 20, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("A000", objIn1, 20, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("New", objIn1 + 10, 10, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 降级so
    AW_FUN_Log(LOG_STEP, "降级so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo((char *)rollBackSoName));
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    ret = writeRecord(conn, stmt, "C000", objIn1 + 20, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        if (i >= 10) {
            objIn1[i].upgradeVersion = 0;
        }
    }
    ret = readRecord("B000", objIn1, 30, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("A000", objIn1, 30, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询物理计划视图
    memset(cmd, 0, MAX_CMD_SIZE);
#ifndef RTOSV2X
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmprecompiler -s ./datalog_file/DataLog_068_0221.c", soName1);
    ret = Debug_executeCommand(cmd, "The first option \"-s\" is undefined");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    memset(cmd, 0, MAX_CMD_SIZE);
    snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO -f TABLE_NAME=%s", "C000");
    ret = Debug_executeCommand(cmd, "DeltaTransientTupleMerge on Label(C000)",
        "Output: dtlReservedCount, upgradeVersion, a, b, c, d, e, f, g, a1, b1, c1, d1, e1, f1, g1, a2, a3, a4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);
    snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_068_0221");
    ret = Debug_executeCommand(cmd, "C000", "DM_DTL_TRANSIENT_TUPLE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "A000", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "B000", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].a4buf);
    }
    free(objIn1);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 222.so升级新增表含所有数据类型，预期升级成功，非阻塞 相关视图，离线及在线
TEST_F(DlgSupUint, DataLog_068_222)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，预期升级成功，阻塞  相关视图，离线及在线";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char patchSoName[FILE_PATH] = "DataLog_068_0221_patchV2";
    char rollBackSoName[FILE_PATH] = "./datalog_file/DataLog_068_0221_rollbackV2.so";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[i].a = (i + 1) % 255;
            objIn1[i].b = (i + 1) % 255;
            objIn1[i].c = 0;
            objIn1[i].d = (i + 1) % 255;
            for (int j = 0; j < 1; j++) {
                objIn1[i].e[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 128; j++) {
                objIn1[i].f[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 256; j++) {
                objIn1[i].g[j] = (i + 1) % 50;
            }
            objIn1[i].a1 = (i + 1) % 255;
            objIn1[i].b1 = (i + 1) % 255;
            objIn1[i].c1 = (i + 1) % 255;
            objIn1[i].d1 = (i + 1) % 255;

            objIn1[i].e1 = (i + 1) % 255;
            objIn1[i].f1 = (i + 1) % 255;
            objIn1[i].g1 = (i + 1) % 255;
            objIn1[i].a2 = (i + 1) % 255;
        }
        objIn1[i].a3len = (i + 1) % 255;
        objIn1[i].a4len = (i + 1) % 255;
        objIn1[i].a3len = 10;
        objIn1[i].a3buf = strT;
        (void)snprintf((char *)objIn1[i].a3buf, 10, "s%08d", (i + 1) % 10);
        ret = Generate_Bytes(&objIn1[i].a4buf, objIn1[i].a4len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写10条数据
    ret = writeRecord(conn, stmt, "C000", objIn1, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("B000", objIn1, 10, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("A000", objIn1, 10, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据

    ret = readRecord("B000", objIn1, 10, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("A000", objIn1, 10, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "New", objIn1 + 10, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "C000", objIn1 + 10, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        if (i >= 10) {
            objIn1[i].upgradeVersion = 1;
        }
    }

    ret = readRecord("B000", objIn1, 20, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("A000", objIn1, 20, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("New", objIn1 + 10, 10, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 降级so
    AW_FUN_Log(LOG_STEP, "降级so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo((char *)rollBackSoName));
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    ret = writeRecord(conn, stmt, "C000", objIn1 + 20, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        if (i >= 10) {
            objIn1[i].upgradeVersion = 0;
        }
    }
    ret = readRecord("B000", objIn1, 30, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("A000", objIn1, 30, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询物理计划视图
    memset(cmd, 0, MAX_CMD_SIZE);
#ifndef RTOSV2X
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmprecompiler -s ./datalog_file/DataLog_068_0221.c", soName1);
    ret = Debug_executeCommand(cmd, "The first option \"-s\" is undefined");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    memset(cmd, 0, MAX_CMD_SIZE);
    snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO -f TABLE_NAME=%s", "C000");
    ret = Debug_executeCommand(cmd, "DeltaTransientTupleMerge on Label(C000)",
        "Output: dtlReservedCount, upgradeVersion, a, b, c, d, e, f, g, a1, b1, c1, d1, e1, f1, g1, a2, a3, a4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, MAX_CMD_SIZE);
    snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_068_0221");
    ret = Debug_executeCommand(cmd, "C000", "DM_DTL_TRANSIENT_TUPLE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "A000", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "B000", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].a4buf);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 223.so升级新增表含所有数据类型，定义位域字段int1_8为主键预期报错
TEST_F(DlgSupUint, DataLog_068_223)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，预期升级成功，阻塞  相关视图，离线及在线";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_001";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 224.so升级新增表含所有数据类型，定义位域字段int1_8为二级索引预期报错
TEST_F(DlgSupUint, DataLog_068_224)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义位域字段int1_8为二级索引预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_002";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 225.so升级新增表含所有数据类型，定义位域字段int2_8为主键预期报错
TEST_F(DlgSupUint, DataLog_068_225)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义位域字段int2_8为主键预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_003";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 226.so升级新增表含所有数据类型，定义位域字段int2_8为二级索引预期报错
TEST_F(DlgSupUint, DataLog_068_226)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义位域字段int2_8为二级索引预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_004";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 227.so升级新增表含所有数据类型，定义位域字段int4_8为主键预期报错
TEST_F(DlgSupUint, DataLog_068_227)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义位域字段int4_8为主键预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_005";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 228.so升级新增表含所有数据类型，定义位域字段int4_8为二级索引预期报错
TEST_F(DlgSupUint, DataLog_068_228)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义位域字段int4_8为二级索引预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_006";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 229.so升级新增表含所有数据类型，定义位域字段int8_8为主键预期报错
TEST_F(DlgSupUint, DataLog_068_229)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义位域字段int8_8为主键预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_007";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 230.so升级新增表含所有数据类型，定义位域字段int8_8为二级索引预期报错
TEST_F(DlgSupUint, DataLog_068_230)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义位域字段int8_8为二级索引预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_008";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any variable-length byte field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 231.so升级新增表含所有数据类型，定义byte字段为主键预期报错
TEST_F(DlgSupUint, DataLog_068_231)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义byte字段为主键预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_009";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 232.so升级新增表含所有数据类型，定义byte字段为二级索引预期报错
TEST_F(DlgSupUint, DataLog_068_232)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，定义byte字段为二级索引预期报错";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    char upgradeFileName[FILE_PATH] = "DataLog_068_0221_010";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3.编译加载升级so
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./datalog_file/%s_rule.d", soName1);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./datalog_file/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "indexes shall not contain any variable-length byte field near line 4.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 233.含%namespace。表中含所有字段，进行DMl操作，预期加载及DML均成功
TEST_F(DlgSupUint, DataLog_068_233)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "so升级新增表含所有数据类型，预期升级成功，阻塞  相关视图，离线及在线";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0233";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[i].a = (i + 1) % 255;
            objIn1[i].b = (i + 1) % 255;
            objIn1[i].c = 0;
            objIn1[i].d = (i + 1) % 255;
            for (int j = 0; j < 1; j++) {
                objIn1[i].e[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 128; j++) {
                objIn1[i].f[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 256; j++) {
                objIn1[i].g[j] = (i + 1) % 50;
            }
            objIn1[i].a1 = (i + 1) % 255;
            objIn1[i].b1 = (i + 1) % 255;
            objIn1[i].c1 = (i + 1) % 255;
            objIn1[i].d1 = (i + 1) % 255;
            objIn1[i].e1 = (i + 1) % 255;
            objIn1[i].f1 = (i + 1) % 255;
            objIn1[i].g1 = (i + 1) % 255;
            objIn1[i].a2 = (i + 1) % 255;
        }
        objIn1[i].a4len = (i + 1) % 255;
        objIn1[i].a3len = 10;
        objIn1[i].a3buf = strT;
        (void)snprintf((char *)objIn1[i].a3buf, 10, "s%08d", (i + 1) % 10);
        ret = Generate_Bytes(&objIn1[i].a4buf, objIn1[i].a4len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // // 普通单写10条数据
    ret = writeRecord(conn, stmt, "ns1.C000", objIn1, 10, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = readRecord("ns1.B000", objIn1, 10, MaxTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("ns1.A000", objIn1, 10, MaxTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].a4buf);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 234.d中含所有表，表中含所有支持的数据类型，预期编译加载成功
TEST_F(DlgSupUint, DataLog_068_234)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载so
    char testcasesInfo[200] = "d中含所有表，表中含所有支持的数据类型，预期编译加载成功";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "N000";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0221";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0221.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson235 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "fixed", "size": 3}
        ]
    } ])";
// 235.byte3规则为常量（8进制），写入数据，预期能够正常过滤数据
TEST_F(DlgSupUint, DataLog_068_235)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "byte3规则为常量";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0235";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Byte3TypeBIsInt4TableStruct *objIn1 =
        (Byte3TypeBIsInt4TableStruct *)malloc(sizeof(Byte3TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Byte3TypeBIsInt4TableStruct) * recordNum);

    Byte3TypeBIsInt4TableStruct *out1 =
        (Byte3TypeBIsInt4TableStruct *)malloc(sizeof(Byte3TypeBIsInt4TableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Byte3TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        for (int j = 0; j < 3; j++) {
            objIn1[i].c[j] = 0;
            objIn1[i].c[2] = 8;
        }
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 65535;
        for (int j = 0; j < 3; j++) {
            out1[i].c[j] = 0;
            out1[i].c[2] = 8;
        }
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson235, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson235, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Byte3TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Byte3TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", objIn1, 5, Byte3TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Byte3TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Byte3TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Byte3TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Byte3TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Byte3TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteByte3BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadByte3AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateByte3AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("out", out1, 10, Byte3TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Byte3TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteByte3BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("out", out1, 10, ReadByte3AndBIsInt4ByKeyID, 1, 0, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 236.gmconvert支持转换含所有数据类型.d为gmjson文件
TEST_F(DlgSupUint, DataLog_068_236)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // gmconvert支持转换含所有数据类型.d为gmjson文件
    char inPath[256] = "./datalog_file/DataLog_068_0236.d";
    char outPath[256] = "out_Convert_gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "gmconvert -i %s -o %s", inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    int ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验A000.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/A000.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson1, result));
    free(result);

    // 校验C000.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/C000.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson2, result));
    free(result);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 237.gmconvert支持转换含所有数据类型rule.d文件为gmjson
TEST_F(DlgSupUint, DataLog_068_237)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // gmconvert支持转换含所有数据类型.d为gmjson文件
    char inPath1[256] = "./datalog_file/DataLog_068_0236_rule.d";
    char inPath2[256] = "./datalog_file/DataLog_068_0236_patch.d";
    char outPath[256] = "out_Convert_gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "gmconvert -upgrade %s %s -o %s", inPath1, inPath2, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    int ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验New.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/New.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson3, result));
    free(result);
    AW_FUN_Log(LOG_STEP, "test end.");
}
