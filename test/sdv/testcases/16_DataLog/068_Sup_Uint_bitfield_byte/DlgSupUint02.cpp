/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: DlgSupUint.cpp
 * Description: DATALOG SUPPORT UINT TYPE
 * Author: youwanyong ywx1157510
 * Create: 2024-02-07
 */
#include "UniversalTools.h"
#include "t_datacom_lite.h"

using namespace std;
class DlgSupUint : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        system("rm -rf /root/_datalog_/");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void DlgSupUint::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void DlgSupUint::TearDown()
{
    AW_CHECK_LOG_END();
}
// 081.不支持写数据int1_8数据类型使用其它位域类型进行设置
TEST_F(DlgSupUint, DataLog_068_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据int1_8数据类型使用其它位域类型进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_081";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t valueA = 2;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_BITFIELD16, &valueA, sizeof(valueA));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 082.uint1_8不支持作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "uint1_8不支持作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_082";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 083.trisent field 字段不支持为uint1_8
TEST_F(DlgSupUint, DataLog_068_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "trisent field 字段不支持为uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_083";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 084.不支持位域字段作为主键，.d中仅含位域字段
TEST_F(DlgSupUint, DataLog_068_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持位域字段作为主键，.d中仅含位域字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_084";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 085.不支持位域字段作为主键，显示定义位域字段为主键
TEST_F(DlgSupUint, DataLog_068_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持位域字段作为主键，显示定义位域字段为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_085";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 086.不支持pubsub表中含位域字段uint1_8
TEST_F(DlgSupUint, DataLog_068_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持pubsub表中含位域字段uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_086";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "table \"out\" doesn't support bitfield fields near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 087.不支持固定资源表含位域字段uint1_8
TEST_F(DlgSupUint, DataLog_068_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持固定资源表含位域字段uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_087";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid\" doesn't support bitfield fields near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 088.不支持pubsub资源表含位域字段uint1_8
TEST_F(DlgSupUint, DataLog_068_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持pubsub资源表含位域字段uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_088";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid3\" doesn't support bitfield fields near line 9.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 089.不支持%aggregate含位域字段uint1_8
TEST_F(DlgSupUint, DataLog_068_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持%aggregate含位域字段uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_089";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "aggregate \"agg\" doesn't support bitfield fields near line 4.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 090.规则中不支持group-by字段为位域字段
TEST_F(DlgSupUint, DataLog_068_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "规则中不支持group-by字段为位域字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_090";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "group_by contains bitfield for table \"mid\" near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 091.不支持uint1_8投影到uint8_8
TEST_F(DlgSupUint, DataLog_068_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint1_8投影到uint8_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_091";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD64 vs BITFIELD8 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 092.不支持uint1_8投影到uint4_8
TEST_F(DlgSupUint, DataLog_068_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint1_8投影到uint4_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_092";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD32 vs BITFIELD8 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 093.不支持uint1_8投影到uint2_8
TEST_F(DlgSupUint, DataLog_068_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint1_8投影到uint2_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_093";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD16 vs BITFIELD8 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 094.不支持uint1_8投影到uint1_7
TEST_F(DlgSupUint, DataLog_068_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint1_8投影到uint1_7";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_094";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(
        command, "length of type bitfield8 of field \"a\" in left and right table are different near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 095.不支持外部表中位域长度和.d中不同
TEST_F(DlgSupUint, DataLog_068_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持外部表中位域长度和.d中不同";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_095";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_095.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 096.不支持外部表中位域数据类型和.d中不同
TEST_F(DlgSupUint, DataLog_068_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持外部表中位域数据类型和.d中不同";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_096";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_095.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 097.不支持.d中位域长度设置为0
TEST_F(DlgSupUint, DataLog_068_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中位域长度设置为0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_097";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, unspported bit width: 'uint1_0' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 098.不支持.d中位域长度设置超过uint1对应类型上限
TEST_F(DlgSupUint, DataLog_068_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持.d中位域长度设置超过uint1对应类型上限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_098";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, unspported bit width: 'uint1_9' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 099.不支持.d中只定义uint1_8类型字段
TEST_F(DlgSupUint, DataLog_068_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中只定义uint1_8类型字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_099";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 100.表中不支持63个字段均为uint1_8，不显示定义主键
TEST_F(DlgSupUint, DataLog_068_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中不支持63个字段均为uint1_8，不显示定义主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0100";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 101.位域字段uint1_8不支持定义为二级索引
TEST_F(DlgSupUint, DataLog_068_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "位域字段uint1_8不支持定义为二级索引";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0101";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson102 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int8"},
            {"name" : "c", "type" : "uint8:8"}
        ]
    } ])";
// 102.投影规则表中支持含uint1_8：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】）
TEST_F(DlgSupUint, DataLog_068_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0102";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson102, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint18TypeTableStruct *objIn1 = (Uint18TypeTableStruct *)malloc(sizeof(Uint18TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint18TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1_8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1_8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1_8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1_8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 103.join规则表中支持含uint1_8：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表】）
TEST_F(DlgSupUint, DataLog_068_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0103";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0103.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson102, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint18TypeTableStruct *objIn1 = (Uint18TypeTableStruct *)malloc(sizeof(Uint18TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint18TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1_8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1_8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1_8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1_8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1_8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint1_8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1_8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 104.笛卡尔规则表中支持含uint1_8:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0104";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson102, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson102, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint18TypeTableStruct *objIn1 = (Uint18TypeTableStruct *)malloc(sizeof(Uint18TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint18TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1_8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1_8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1_8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1_8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson105 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int32"},
            {"name" : "b", "type" : "uint8:8"}
        ]
    } ])";
// 105.忽略规则表中支持含uint1_8:【输入表：含update_by_rank可更新】,【中间表: triansent field】【输出表：消息通知表】
TEST_F(DlgSupUint, DataLog_068_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0105";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint18TypeBIsInt4TableStruct *objIn1 =
        (Uint18TypeBIsInt4TableStruct *)malloc(sizeof(Uint18TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint18TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 255;
        objIn1[i].c = i % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson105, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson105, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint1_8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1_8TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1_8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1_8AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].c = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1_8AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1_8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1_8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1_8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "87025d9a198bbf1d80bea7ac27b146f3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "47abc7e723a58a7aeb3d9b97e6aa9b35");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "abf8aefb53bf4a9156d0c0b41098da10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson106 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int64"},
            {"name" : "b", "type" : "uint8:8"}
        ]
    } ])";
// 106.not join规则表中支持含uint1_8:【输入表：过期表】,【中间表: tuple表】【输出表：普通表】
TEST_F(DlgSupUint, DataLog_068_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0106";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint18TypeBIsInt8TableStruct *objIn1 =
        (Uint18TypeBIsInt8TableStruct *)malloc(sizeof(Uint18TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint18TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535 + 100000;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson106, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeAndBIsInt8TableSet, false);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1_8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1_8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1_8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1_8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1_8AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1_8AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5 + 100000;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1_8AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1_8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1_8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1_8AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1_8AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1_8AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);

    // // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 107.常量规则表中支持含uint1_8:【输入表：tuple表】,【中间表: finish】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0107";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson105, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson105, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint18TypeBIsInt4TableStruct *objIn1 =
        (Uint18TypeBIsInt4TableStruct *)malloc(sizeof(Uint18TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint18TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint1_8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", objIn1, 5, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1_8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(6, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1_8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1_8AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1_8AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("out", objIn1 + 5, 5, Uint1_8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(6, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1_8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("out", objIn1, 10, ReadUint1_8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(2, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 108.表中支持含uint1_8支持外部表含输入表finish join function
TEST_F(DlgSupUint, DataLog_068_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint1_8支持外部表含(uint1_8 uint1_8)";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0108";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0108.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson102, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint18TypeTableStruct *objIn1 = (Uint18TypeTableStruct *)malloc(sizeof(Uint18TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint18TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Uint18TypeTableStruct *out1 = (Uint18TypeTableStruct *)malloc(sizeof(Uint18TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Uint18TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Uint1_8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Uint1_8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1_8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1_8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1_8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Uint1_8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1_8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1_8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    free(out1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 109.表中支持含uint1_8，输出表为tbm表含 precedence及级联删除规则
TEST_F(DlgSupUint, DataLog_068_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint1_8，输出表为tbm表含 precedence及级联删除规则";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0109";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson102, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson102, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson102, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint18TypeTableStruct *objIn1 = (Uint18TypeTableStruct *)malloc(sizeof(Uint18TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint18TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint1_8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateUint1_8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateUint1_8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateUint1_8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, Uint1_8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1_8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint1_8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint1_8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadUint1_8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 110.不支持写数据uint2_16数据类型使用其它位域类型进行设置
TEST_F(DlgSupUint, DataLog_068_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据uint2_16数据类型使用其它位域类型进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0110";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t valueA = 2;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_BITFIELD8, &valueA, sizeof(valueA));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 111.uint2_16不支持作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "uint2_8不支持作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0111";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 112.trisent field 字段不支持为uint2_16
TEST_F(DlgSupUint, DataLog_068_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "trisent field 字段不支持为uint2_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0112";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 113.不支持位域字段uint2_16作为主键，.d中仅含位域字段
TEST_F(DlgSupUint, DataLog_068_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持位域字段uint2_16作为主键，.d中仅含位域字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0113";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 114.不支持位域字段uint2_16作为主键，显示定义位域字段为主键
TEST_F(DlgSupUint, DataLog_068_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持位域字段uint2_16作为主键，显示定义位域字段为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0114";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 115.不支持pubsub表中含位域字段uint2_16
TEST_F(DlgSupUint, DataLog_068_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持pubsub表中含位域字段uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0115";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "table \"out\" doesn't support bitfield fields near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 116.不支持固定资源表含位域字段uint2_16
TEST_F(DlgSupUint, DataLog_068_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持固定资源表含位域字段uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0116";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid\" doesn't support bitfield fields near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 117.不支持pubsub资源表含位域字段uint2_16
TEST_F(DlgSupUint, DataLog_068_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持pubsub资源表含位域字段uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0117";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid3\" doesn't support bitfield fields near line 9.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 118.不支持%aggregate含位域字段uint2_16
TEST_F(DlgSupUint, DataLog_068_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持%aggregate含位域字段uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0118";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "aggregate \"agg\" doesn't support bitfield fields near line 4.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 119.规则中不支持group-by字段为位域字段uint2_16
TEST_F(DlgSupUint, DataLog_068_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "规则中不支持group-by字段为位域字段uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0119";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "group_by contains bitfield for table \"mid\" near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 120.不支持uint2_8投影到uint8_8
TEST_F(DlgSupUint, DataLog_068_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint2_8投影到uint8_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0120";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD64 vs BITFIELD16 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 121.不支持uint2_8投影到uint4_8
TEST_F(DlgSupUint, DataLog_068_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint2_8投影到uint4_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0121";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD32 vs BITFIELD16 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 122.不支持uint2_8投影到uint1_8
TEST_F(DlgSupUint, DataLog_068_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint2_8投影到uint1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0122";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD8 vs BITFIELD16 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 123.不支持uint2_8投影到uint2_7
TEST_F(DlgSupUint, DataLog_068_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint2_8投影到uint2_7";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0123";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(
        command, "length of type bitfield16 of field \"a\" in left and right table are different near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 124.不支持外部表中位域uint2_16长度和.d中不同
TEST_F(DlgSupUint, DataLog_068_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持外部表中位域uint2_16长度和.d中不同";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0124";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0124.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 125.不支持外部表中位域数据类型uint2_16和.d中不同
TEST_F(DlgSupUint, DataLog_068_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持外部表中位域数据类型和.d中不同";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0125";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0125.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 126.不支持.d中位域长度uint2_16设置为0
TEST_F(DlgSupUint, DataLog_068_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中位域长度uint2_16设置为0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0126";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, unspported bit width: 'uint2_0' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 127.不支持.d中位域uint2_16长度设置超过对应类型上限
TEST_F(DlgSupUint, DataLog_068_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持.d中位域uint2_16长度设置超过对应类型上限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0127";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, unspported bit width: 'uint2_17' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 128.不支持.d中定义uint2_16类型字段
TEST_F(DlgSupUint, DataLog_068_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中定义uint2_16类型字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0128";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 129.表中不支持63个字段均为uint2_8，不显示定义主键
TEST_F(DlgSupUint, DataLog_068_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中不支持63个字段均为uint2_8，不显示定义主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0129";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 130.位域字段uint2_8不支持定义为二级索引
TEST_F(DlgSupUint, DataLog_068_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "位域字段uint2_8不支持定义为二级索引";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0130";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson131 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int8"},
            {"name" : "c", "type" : "uint16:16"}
        ]
    } ])";
// 131.投影规则表中支持含uint2_16：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】）
TEST_F(DlgSupUint, DataLog_068_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0131";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson131, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint216TypeTableStruct *objIn1 = (Uint216TypeTableStruct *)malloc(sizeof(Uint216TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint216TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2_16ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2_16ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2_16ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2_16ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 132.join规则表中支持含uint2_16：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表】）
TEST_F(DlgSupUint, DataLog_068_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0132";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0132.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson131, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson131, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint216TypeTableStruct *objIn1 = (Uint216TypeTableStruct *)malloc(sizeof(Uint216TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint216TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint216TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint216TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2_16ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2_16ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2_16ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint216TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2_16ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 133.笛卡尔规则表中支持含uint2_16:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0133";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson131, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson131, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint216TypeTableStruct *objIn1 = (Uint216TypeTableStruct *)malloc(sizeof(Uint216TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint216TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2_16ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2_16ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2_16ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2_16ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson134 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int32"},
            {"name" : "b", "type" : "uint16:16"}
        ]
    } ])";
// 134.忽略规则表中支持含uint2_16:【输入表：含update_by_rank可更新】,【中间表: triansent field】【输出表：消息通知表】
TEST_F(DlgSupUint, DataLog_068_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0134";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson134, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson134, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint216TypeBIsInt4TableStruct *objIn1 =
        (Uint216TypeBIsInt4TableStruct *)malloc(sizeof(Uint216TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint216TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].c = i % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint216TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint216TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2_16BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2_16AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].c = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2_16AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2_16BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2_16AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2_16AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "87025d9a198bbf1d80bea7ac27b146f3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "47abc7e723a58a7aeb3d9b97e6aa9b35");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "abf8aefb53bf4a9156d0c0b41098da10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson135 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int64"},
            {"name" : "b", "type" : "uint16:16"}
        ]
    } ])";
// 135.not join规则表中支持含uint2_16:【输入表：过期表】,【中间表: tuple表】【输出表：普通表】
TEST_F(DlgSupUint, DataLog_068_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0135";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson135, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint216TypeBIsInt8TableStruct *objIn1 =
        (Uint216TypeBIsInt8TableStruct *)malloc(sizeof(Uint216TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint216TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535 + 10000;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint216TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint216TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint216TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint216TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2_16AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2_16AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5 + 10000;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2_16AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint216TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint216TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2_16AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2_16AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2_16AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);

    // // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson136 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int32"},
            {"name" : "b", "type" : "uint16:16"}
        ]
    } ])";
// 136.常量规则表中支持含uint2_16:【输入表：tuple表】,【中间表: finish】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含uint2_16";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0136";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson136, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson136, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint216TypeBIsInt4TableStruct *objIn1 =
        (Uint216TypeBIsInt4TableStruct *)malloc(sizeof(Uint216TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint216TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint216TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", objIn1, 5, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint216TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(6, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2_16BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2_16AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        if (i > 4) {
            objIn1[i].b = (i + 1) % 5;
            objIn1[i].dtlReservedCount = 1;
            objIn1[i].upgradeVersion = 0;
        }
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2_16AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("out", objIn1 + 5, 5, Uint216TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(6, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2_16BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("out", objIn1, 10, ReadUint2_16AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(2, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 137.表中支持含uint2_16支持外部表含输入表finish join function
TEST_F(DlgSupUint, DataLog_068_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint2_16支持外部表含输入表finish join function";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0137";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0137.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson131, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint216TypeTableStruct *objIn1 = (Uint216TypeTableStruct *)malloc(sizeof(Uint216TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint216TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Uint216TypeTableStruct *out1 = (Uint216TypeTableStruct *)malloc(sizeof(Uint216TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Uint216TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].c = 1;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].c = 1 + 1;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", out1, 5, Uint216TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Uint216TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2_16ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2_16ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2_16ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Uint216TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint216TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2_16ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    free(out1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 138.表中支持含uint2_16，输出表为tbm表含 precedence及级联删除规则
TEST_F(DlgSupUint, DataLog_068_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint2_16，输出表为tbm表含 precedence及级联删除规则";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0138";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson131, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson131, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson131, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint216TypeTableStruct *objIn1 = (Uint216TypeTableStruct *)malloc(sizeof(Uint216TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint216TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint216TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateUint2_16ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateUint2_16ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateUint2_16ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, Uint216TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint216TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint216TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint216TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadUint2_16ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 139.不支持写数据uint4_32数据类型使用其它位域类型进行设置
TEST_F(DlgSupUint, DataLog_068_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据uint4_32数据类型使用其它位域类型进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0139";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t valueA = 2;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_BITFIELD16, &valueA, sizeof(valueA));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 140.uint4_32不支持作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "uint4_32不支持作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0140";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 141.trisent field 字段不支持为uint4_32
TEST_F(DlgSupUint, DataLog_068_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "trisent field 字段不支持为uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0141";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 142.不支持位域字段uint4_32作为主键，.d中仅含位域字段
TEST_F(DlgSupUint, DataLog_068_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持位域字段uint4_32作为主键，.d中仅含位域字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0142";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 143.不支持位域字段uint4_32作为主键，显示定义位域字段为主键
TEST_F(DlgSupUint, DataLog_068_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持位域字段uint4_32作为主键，显示定义位域字段为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0143";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 144.不支持pubsub表中含位域字段uint4_32
TEST_F(DlgSupUint, DataLog_068_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持pubsub表中含位域字段uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0144";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "table \"out\" doesn't support bitfield fields near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 145.不支持固定资源表含位域字段uint4_32
TEST_F(DlgSupUint, DataLog_068_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持固定资源表含位域字段uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0145";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid\" doesn't support bitfield fields near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 146.不支持pubsub资源表含位域字段uint4_32
TEST_F(DlgSupUint, DataLog_068_146)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持pubsub资源表含位域字段uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0146";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid3\" doesn't support bitfield fields near line 9.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 147.不支持%aggregate含位域字段uint4_32
TEST_F(DlgSupUint, DataLog_068_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持%aggregate含位域字段uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0147";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "aggregate \"agg\" doesn't support bitfield fields near line 4.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 148.规则中不支持group-by字段为位域字段uint4_32
TEST_F(DlgSupUint, DataLog_068_148)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "规则中不支持group-by字段为位域字段uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0148";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "group_by contains bitfield for table \"mid\" near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 149.不支持uint4_8投影到int8_8
TEST_F(DlgSupUint, DataLog_068_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint4_8投影到int8_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0149";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD64 vs BITFIELD32 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 150.不支持uint4_8投影到int2_8
TEST_F(DlgSupUint, DataLog_068_150)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint4_8投影到int2_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0150";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD16 vs BITFIELD32 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 151.不支持uint4_8投影到int1_8
TEST_F(DlgSupUint, DataLog_068_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint4_8投影到int1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0151";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD8 vs BITFIELD32 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 152.不支持uint4_8投影到uint4_7
TEST_F(DlgSupUint, DataLog_068_152)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint4_8投影到uint4_7";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0152";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(
        command, "length of type bitfield32 of field \"a\" in left and right table are different near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 153.不支持外部表中位域uint4_32长度和.d中不同
TEST_F(DlgSupUint, DataLog_068_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持外部表中位域uint4_32长度和.d中不同";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0153";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0153.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 154.不支持外部表中位域数据类型uint4_32和.d中不同
TEST_F(DlgSupUint, DataLog_068_154)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持外部表中位域数据类型uint4_32和.d中不同";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0154";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0154.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 155.不支持.d中位域长度uint4_32设置为0
TEST_F(DlgSupUint, DataLog_068_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中位域长度uint4_32设置为0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0155";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, unspported bit width: 'uint4_0' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 156.不支持.d中位域uint4_32长度设置超过对应类型上限
TEST_F(DlgSupUint, DataLog_068_156)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持.d中位域uint4_32长度设置超过对应类型上限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0156";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, unspported bit width: 'uint4_33' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 157.不支持.d中定义uint4_32类型字段
TEST_F(DlgSupUint, DataLog_068_157)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中定义uint4_32类型字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0157";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 158.表中不支持63个字段均为uint4_8，不显示定义主键
TEST_F(DlgSupUint, DataLog_068_158)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中不支持63个字段均为uint4_8，不显示定义主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0158";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 159.位域字段uint4_8不支持定义为二级索引
TEST_F(DlgSupUint, DataLog_068_159)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "位域字段uint4_8不支持定义为二级索引";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0159";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson160 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int8"},
            {"name" : "c", "type" : "uint32:32"}
        ]
    } ])";
// 160.投影规则表中支持含uint4_32：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】）
TEST_F(DlgSupUint, DataLog_068_160)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0160";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson160, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint432TypeTableStruct *objIn1 = (Uint432TypeTableStruct *)malloc(sizeof(Uint432TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint432TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4_32ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4_32ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4_32ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4_32ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 161.join规则表中支持含uint4_32：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表】）
TEST_F(DlgSupUint, DataLog_068_161)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0161";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0161.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson160, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson160, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint432TypeTableStruct *objIn1 = (Uint432TypeTableStruct *)malloc(sizeof(Uint432TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint432TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint432TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint432TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4_32ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4_32ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4_32ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint432TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4_32ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 162.笛卡尔规则表中支持含uint4_32:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_162)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0162";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson160, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson160, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint432TypeTableStruct *objIn1 = (Uint432TypeTableStruct *)malloc(sizeof(Uint432TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint432TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4_32ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4_32ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4_32ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4_32ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson163 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int32"},
            {"name" : "b", "type" : "uint32:32"}
        ]
    } ])";
// 163.忽略规则表中支持含uint4_32:【输入表：含update_by_rank可更新】,【中间表: triansent field】【输出表：消息通知表】
TEST_F(DlgSupUint, DataLog_068_163)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0163";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson163, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson163, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint432TypeBIsInt4TableStruct *objIn1 =
        (Uint432TypeBIsInt4TableStruct *)malloc(sizeof(Uint432TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint432TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].b = i % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint432TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint432TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4_32BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4_32AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].c = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4_32AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4_32BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4_32AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4_32AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "87025d9a198bbf1d80bea7ac27b146f3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "47abc7e723a58a7aeb3d9b97e6aa9b35");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "abf8aefb53bf4a9156d0c0b41098da10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson164 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int64"},
            {"name" : "b", "type" : "uint32:32"}
        ]
    } ])";
// 164.not join规则表中支持含uint4_32:【输入表：过期表】,【中间表: tuple表】【输出表：普通表】
TEST_F(DlgSupUint, DataLog_068_164)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0164";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson164, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint432TypeBIsInt8TableStruct *objIn1 =
        (Uint432TypeBIsInt8TableStruct *)malloc(sizeof(Uint432TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint432TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535 + 10000;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint432TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint432TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint432TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint432TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4_32AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4_32AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5 + 10000;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4_32AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint432TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint432TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4_32AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4_32AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4_32AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 165.常量规则表中支持含uint4_32:【输入表：tuple表】,【中间表: finish】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_165)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含uint4_32";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0165";
    GmcStmtT *stmt;
    GmcConnT *conn;

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson163, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson163, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint432TypeBIsInt4TableStruct *objIn1 =
        (Uint432TypeBIsInt4TableStruct *)malloc(sizeof(Uint432TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint432TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint432TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", objIn1, 5, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint432TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(6, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4_32BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4_32AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        if (i > 4) {
            objIn1[i].a = (i + 1) % 255;
            objIn1[i].b = (i + 1) % 5;
            objIn1[i].dtlReservedCount = 1;
            objIn1[i].upgradeVersion = 0;
        }
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4_32AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("out", objIn1 + 5, 5, Uint432TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(6, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4_32BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("out", objIn1, 10, ReadUint4_32AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(2, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 166.表中支持含uint4_32支持外部表含输入表finish join function
TEST_F(DlgSupUint, DataLog_068_166)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint4_32支持外部表含输入表finish join function";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0166";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0166.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson160, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson160, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint432TypeTableStruct *objIn1 = (Uint432TypeTableStruct *)malloc(sizeof(Uint432TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint432TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Uint432TypeTableStruct *out1 = (Uint432TypeTableStruct *)malloc(sizeof(Uint432TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Uint432TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].c = 1;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].c = 1 + 1;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", out1, 5, Uint432TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Uint432TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4_32ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4_32ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4_32ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Uint432TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint432TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4_32ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    free(out1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 167.表中支持含uint4_32，输出表为tbm表含 precedence及级联删除规则
TEST_F(DlgSupUint, DataLog_068_167)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint4_32，输出表为tbm表含 precedence及级联删除规则";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0167";
    GmcStmtT *stmt;
    GmcConnT *conn;

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson131, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson131, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson131, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint432TypeTableStruct *objIn1 = (Uint432TypeTableStruct *)malloc(sizeof(Uint432TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint432TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint432TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateUint4_32ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateUint4_32ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateUint4_32ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, Uint432TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint432TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint432TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint432TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadUint4_32ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 168.不支持写数据uint8_64数据类型使用其它位域类型进行设置
TEST_F(DlgSupUint, DataLog_068_168)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据uint8_64数据类型使用其它位域类型进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0168";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t valueA = 2;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_BITFIELD16, &valueA, sizeof(valueA));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 169.uint8_64不支持作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_169)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "uint8_64不支持作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0169";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 170.trisent field 字段不支持为uint8_64
TEST_F(DlgSupUint, DataLog_068_170)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "trisent field 字段不支持为uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0170";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 171.不支持位域字段uint8_64作为主键，.d中仅含位域字段
TEST_F(DlgSupUint, DataLog_068_171)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持位域字段uint8_64作为主键，.d中仅含位域字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0171";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 172.不支持位域字段uint8_64作为主键，显示定义位域字段为主键
TEST_F(DlgSupUint, DataLog_068_172)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持位域字段uint8_64作为主键，显示定义位域字段为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0172";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 173.不支持pubsub表中含位域字段uint8_64
TEST_F(DlgSupUint, DataLog_068_173)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持pubsub表中含位域字段uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0173";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "table \"out\" doesn't support bitfield fields near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 174.不支持固定资源表含位域字段uint8_64
TEST_F(DlgSupUint, DataLog_068_174)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持固定资源表含位域字段uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0174";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid\" doesn't support bitfield fields near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 175.不支持pubsub资源表含位域字段uint8_64
TEST_F(DlgSupUint, DataLog_068_175)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持pubsub资源表含位域字段uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0175";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "resource \"mid3\" doesn't support bitfield fields near line 9.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 176.不支持%aggregate含位域字段uint8_64
TEST_F(DlgSupUint, DataLog_068_176)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持%aggregate含位域字段uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0176";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "aggregate \"agg\" doesn't support bitfield fields near line 4.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 177.规则中不支持group-by字段为位域字段uint8_64
TEST_F(DlgSupUint, DataLog_068_177)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "规则中不支持group-by字段为位域字段uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0177";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "group_by contains bitfield for table \"mid\" near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 178.不支持uint8_8投影到int4_8
TEST_F(DlgSupUint, DataLog_068_178)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint8_8投影到int4_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0178";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD32 vs BITFIELD64 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 179.不支持uint8_8投影到int2_8
TEST_F(DlgSupUint, DataLog_068_179)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint8_8投影到int2_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0179";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD16 vs BITFIELD64 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 180.不支持uint8_8投影到int1_8
TEST_F(DlgSupUint, DataLog_068_180)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint8_8投影到int1_8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0180";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": "
                                  "BITFIELD8 vs BITFIELD64 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 181.不支持uint8_8投影到uint4_7
TEST_F(DlgSupUint, DataLog_068_181)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint8_8投影到uint4_7";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0181";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(
        command, "length of type bitfield64 of field \"a\" in left and right table are different near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 182.不支持外部表中位域uint8_64长度和.d中不同
TEST_F(DlgSupUint, DataLog_068_182)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持外部表中位域uint8_64长度和.d中不同";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0182";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0182.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 183.不支持外部表中位域数据类型uint8_64和.d中不同
TEST_F(DlgSupUint, DataLog_068_183)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持外部表中位域数据类型uint8_64和.d中不同";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0183";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0183.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 184.不支持.d中位域长度uint8_64设置为0
TEST_F(DlgSupUint, DataLog_068_184)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中位域长度uint8_64设置为0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0184";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, unspported bit width: 'uint8_0' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 185.不支持.d中位域uint8_64长度设置超过对应类型上限
TEST_F(DlgSupUint, DataLog_068_185)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持.d中位域uint8_64长度设置超过对应类型上限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0185";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Failed to parse datalog program, unspported bit width: 'uint8_65' near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 186.不支持.d中定义uint8_64类型字段
TEST_F(DlgSupUint, DataLog_068_186)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中定义uint8_64类型字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0186";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 187.表中不支持63个字段均为uint8_8，不显示定义主键
TEST_F(DlgSupUint, DataLog_068_187)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中不支持63个字段均为uint8_8，不显示定义主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0187";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "index0 should be defined explicitly for table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 188.位域字段uint8_8不支持定义为二级索引
TEST_F(DlgSupUint, DataLog_068_188)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "位域字段uint4_8不支持定义为二级索引";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0188";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "indexes shall not contain any bitfield field near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson190 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int8"},
            {"name" : "c", "type" : "uint64:64"}
        ]
    } ])";
// 189.投影规则表中支持含uint8_64：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】）
TEST_F(DlgSupUint, DataLog_068_189)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0189";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson190, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson190, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint864TypeTableStruct *objIn1 = (Uint864TypeTableStruct *)malloc(sizeof(Uint864TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint864TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8_64ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8_64ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8_64ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8_64ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 190.join规则表中支持含uint8_64：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表】）
TEST_F(DlgSupUint, DataLog_068_190)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0190";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0190.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson190, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson190, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint864TypeTableStruct *objIn1 = (Uint864TypeTableStruct *)malloc(sizeof(Uint864TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint864TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint864TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint864TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8_64ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8_64ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8_64ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint864TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8_64ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 191.笛卡尔规则表中支持含uint8_64:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_191)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0191";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson190, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson190, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint864TypeTableStruct *objIn1 = (Uint864TypeTableStruct *)malloc(sizeof(Uint864TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint864TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8_64ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8_64ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8_64ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8_64ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson192 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int32"},
            {"name" : "b", "type" : "uint64:64"}
        ]
    } ])";
// 192.忽略规则表中支持含uint8_64:【输入表：含update_by_rank可更新】,【中间表: triansent field】【输出表：消息通知表】
TEST_F(DlgSupUint, DataLog_068_192)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0192";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson192, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson192, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint864TypeBIsInt4TableStruct *objIn1 =
        (Uint864TypeBIsInt4TableStruct *)malloc(sizeof(Uint864TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint864TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].b = i % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint864TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint864TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8_64BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8_64AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].c = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8_64AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8_64BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8_64AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8_64AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "87025d9a198bbf1d80bea7ac27b146f3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "47abc7e723a58a7aeb3d9b97e6aa9b35");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "abf8aefb53bf4a9156d0c0b41098da10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson193 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int8"},
            {"name" : "b", "type" : "int64"},
            {"name" : "b", "type" : "uint64:64"}
        ]
    } ])";
// 193.not join规则表中支持含uint8_64:【输入表：过期表】,【中间表: tuple表】【输出表：普通表】
TEST_F(DlgSupUint, DataLog_068_193)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0193";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson193, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint864TypeBIsInt8TableStruct *objIn1 =
        (Uint864TypeBIsInt8TableStruct *)malloc(sizeof(Uint864TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint864TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535 + 100000;
        objIn1[i].c = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint864TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint864TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint864TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint864TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8_64AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8_64AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5 + 100000;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8_64AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint864TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint864TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8_64AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8_64AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8_64AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 194.常量规则表中支持含uint8_64:【输入表：tuple表】,【中间表: finish】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_194)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含uint8_64";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0194";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson192, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson192, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint864TypeBIsInt4TableStruct *objIn1 =
        (Uint864TypeBIsInt4TableStruct *)malloc(sizeof(Uint864TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint864TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint864TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", objIn1, 5, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint864TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(6, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8_64BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8_64AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8_64AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("out", objIn1 + 5, 5, Uint864TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(6, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8_64BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("out", objIn1, 10, ReadUint8_64AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(2, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 195.表中支持含uint8_64支持外部表含输入表finish join function
TEST_F(DlgSupUint, DataLog_068_195)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint8_64支持外部表含输入表finish join function";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0195";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_0195.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson190, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson190, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint864TypeTableStruct *objIn1 = (Uint864TypeTableStruct *)malloc(sizeof(Uint864TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint864TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].c = 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Uint864TypeTableStruct *out1 = (Uint864TypeTableStruct *)malloc(sizeof(Uint864TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Uint864TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].c = 1;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].c = 1 + 1;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    ret = readRecord("out", out1, 5, Uint864TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Uint864TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8_64ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8_64ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 1) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8_64ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Uint864TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint864TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8_64ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    free(out1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 196.表中支持含uint8_64，输出表为tbm表含 precedence及级联删除规则
TEST_F(DlgSupUint, DataLog_068_196)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint8_64，输出表为tbm表含 precedence及级联删除规则";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_0196";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson160, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson160, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson160, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint864TypeTableStruct *objIn1 = (Uint864TypeTableStruct *)malloc(sizeof(Uint864TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint864TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint864TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateUint8_64ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateUint8_64ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateUint8_64ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, Uint864TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint864TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint864TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint864TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadUint8_64ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
