/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: DlgSupUint.cpp
 * Description: DATALOG SUPPORT UINT TYPE
 * Author: youwanyong ywx1157510
 * Create: 2024-02-07
 */
#include "UniversalTools.h"
#include "t_datacom_lite.h"

using namespace std;
class DlgSupUint : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        system("rm -rf /root/_datalog_/");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void DlgSupUint::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void DlgSupUint::TearDown()
{
    AW_CHECK_LOG_END();
}
// 001.不支持uint1投影到int1
TEST_F(DlgSupUint, DataLog_068_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint1投影到int1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_001";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": INT1 vs UINT1 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.不支持.d中外部表uint1对应外部表int1
TEST_F(DlgSupUint, DataLog_068_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中外部表uint1对应外部表int1";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_002";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_002.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.不支持写数据uint1数据类型使用int1进行设置
TEST_F(DlgSupUint, DataLog_068_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据uint1数据类型使用int1进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_003";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t valueA = 10;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &valueA, sizeof(valueA));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.不支持int1投影到uint1
TEST_F(DlgSupUint, DataLog_068_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持int1投影到uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_004";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": INT1 vs UINT1 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.不支持作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_005";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.不支持作为固定资源表输出字段
TEST_F(DlgSupUint, DataLog_068_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持作为固定资源表输出字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_006";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "the output field of sequential resource \"rel\" should be int4 near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.不支持常量超过255
TEST_F(DlgSupUint, DataLog_068_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持常量超过255";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_007";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "can not convert 256 to uint8 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.transient(field) 字段不支持为uint1
TEST_F(DlgSupUint, DataLog_068_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "transient(field) 字段不支持为uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_008";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson9 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int8"}
        ]
    } ])";

const char *g_schemaJson12 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int32"}
        ]
    } ])";
const char *g_schemaJson13 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int64"}
        ]
    } ])";

    const char *g_schemaJson17 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "a1", "type" : "uint8"},
            {"name" : "a2", "type" : "uint8"},
            {"name" : "a3", "type" : "uint8"},
            {"name" : "a4", "type" : "uint8"},
            {"name" : "a5", "type" : "uint8"},
            {"name" : "a6", "type" : "uint8"},
            {"name" : "a7", "type" : "uint8"},
            {"name" : "a8", "type" : "uint8"},
            {"name" : "a9", "type" : "uint8"},
            {"name" : "a10", "type" : "uint8"},
            {"name" : "a11", "type" : "uint8"},
            {"name" : "a12", "type" : "uint8"},
            {"name" : "a13", "type" : "uint8"},
            {"name" : "a14", "type" : "uint8"},
            {"name" : "a15", "type" : "uint8"},
            {"name" : "a16", "type" : "uint8"},
            {"name" : "a17", "type" : "uint8"},
            {"name" : "a18", "type" : "uint8"},
            {"name" : "a19", "type" : "uint8"},
            {"name" : "a20", "type" : "uint8"},
            {"name" : "a21", "type" : "uint8"},
            {"name" : "a22", "type" : "uint8"},
            {"name" : "a23", "type" : "uint8"},
            {"name" : "a24", "type" : "uint8"},
            {"name" : "a25", "type" : "uint8"},
            {"name" : "a26", "type" : "uint8"},
            {"name" : "a27", "type" : "uint8"},
            {"name" : "a28", "type" : "uint8"},
            {"name" : "a29", "type" : "uint8"},
            {"name" : "a30", "type" : "uint8"},
            {"name" : "a31", "type" : "uint8"},
            {"name" : "a32", "type" : "uint8"},
            {"name" : "a33", "type" : "uint8"},
            {"name" : "a34", "type" : "uint8"},
            {"name" : "a35", "type" : "uint8"},
            {"name" : "a36", "type" : "uint8"},
            {"name" : "a37", "type" : "uint8"},
            {"name" : "a38", "type" : "uint8"},
            {"name" : "a39", "type" : "uint8"},
            {"name" : "a40", "type" : "uint8"},
            {"name" : "a41", "type" : "uint8"},
            {"name" : "a42", "type" : "uint8"},
            {"name" : "a43", "type" : "uint8"},
            {"name" : "a44", "type" : "uint8"},
            {"name" : "a45", "type" : "uint8"},
            {"name" : "a46", "type" : "uint8"},
            {"name" : "a47", "type" : "uint8"},
            {"name" : "a48", "type" : "uint8"},
            {"name" : "a49", "type" : "uint8"},
            {"name" : "a50", "type" : "uint8"},
            {"name" : "a51", "type" : "uint8"},
            {"name" : "a52", "type" : "uint8"},
            {"name" : "a53", "type" : "uint8"},
            {"name" : "a54", "type" : "uint8"},
            {"name" : "a55", "type" : "uint8"},
            {"name" : "a56", "type" : "uint8"},
            {"name" : "a57", "type" : "uint8"},
            {"name" : "a58", "type" : "uint8"},
            {"name" : "a59", "type" : "uint8"},
            {"name" : "a60", "type" : "uint8"},
            {"name" : "a61", "type" : "uint8"},
            {"name" : "a62", "type" : "uint8"}
        ]
    } ])";
// 009.投影规则表中支持含uint1：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】)
TEST_F(DlgSupUint, DataLog_068_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_009";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson9, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1TypeTableStruct *objIn1 = (Uint1TypeTableStruct *)malloc(sizeof(Uint1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.join规则表中支持含uint1：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表(外uint1:.d:int1)】）
TEST_F(DlgSupUint, DataLog_068_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_010";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_010.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1TypeTableStruct *objIn1 = (Uint1TypeTableStruct *)malloc(sizeof(Uint1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson9, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson9, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.笛卡尔规则表中支持含uint1:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_011";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson9, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson9, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Uint1TypeTableStruct *objIn1 = (Uint1TypeTableStruct *)malloc(sizeof(Uint1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012.忽略规则表中支持含uint1:【输入表：含update_by_rank可更新】,【中间表: triansent field】【输出表：消息通知表】
// 部分可更新表删除数据,预期删除5条实际删除3条
TEST_F(DlgSupUint, DataLog_068_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_012";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson12, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson12, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1TypeBIsInt4TableStruct *objIn1 =
        (Uint1TypeBIsInt4TableStruct *)malloc(sizeof(Uint1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1AndBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "87025d9a198bbf1d80bea7ac27b146f3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "47abc7e723a58a7aeb3d9b97e6aa9b35");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "abf8aefb53bf4a9156d0c0b41098da10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.not join规则表中支持含uint1:【输入表：过期表】,【中间表: tuple表】【输出表：pubsub表】
TEST_F(DlgSupUint, DataLog_068_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_013";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.srvMemCtxLimit = 2;
    subConnOptions.connName = subConnName;  // MB
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./schema_file/update_pubsub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson13, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1TypeBIsInt8TableStruct *objIn1 =
        (Uint1TypeBIsInt8TableStruct *)malloc(sizeof(Uint1TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(4);
    ret = readRecord("inp", objIn1, 5, Uint1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(4);
    ret = readRecord("inp", objIn1, 10, Uint1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    sleep(4);
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(4);
    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    ret = GmcUnSubscribe(stmt,subNameUpdate);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.常量规则表中支持含uint1:【输入表：tuple表】,【中间表: 固定资源表】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_014";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson12, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson12, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1TypeBIsInt4TableStruct *objIn1 =
        (Uint1TypeBIsInt4TableStruct *)malloc(sizeof(Uint1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1AndBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("inp", objIn1, 10, ReadUint1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.表中支持含uint1group-by字段为uint1(access_current,access_delta,access_kv)
TEST_F(DlgSupUint, DataLog_068_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = ".表中支持含uint1group-by字段为uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_015";
    GmcStmtT *stmt;
    GmcConnT *conn;

    (void)CreateKvTable();

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson12, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson12, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1TypeBIsInt4TableStruct *objIn1 =
        (Uint1TypeBIsInt4TableStruct *)malloc(sizeof(Uint1TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint1TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1AndBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId("out", objIn1, 10, ReadUint1AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.表中支持含uint1支持外部表含(uint1:uint1)输入表finish join function
TEST_F(DlgSupUint, DataLog_068_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint1支持外部表含(uint1 uint1)";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_016";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_016.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson9, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson9, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1TypeTableStruct *objIn1 = (Uint1TypeTableStruct *)malloc(sizeof(Uint1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Uint1TypeTableStruct *out1 = (Uint1TypeTableStruct *)malloc(sizeof(Uint1TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Uint1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Uint1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Uint1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Uint1TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    free(out1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.表中支持含uint1支持.d中63个字段为uint1
TEST_F(DlgSupUint, DataLog_068_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint1支持.d中63个字段为uint1";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_017";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson17, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[4096] = "";
    (void)sprintf(schemaJson1, g_schemaJson17, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1Have63TypeTableStruct *objIn1 =
        (Uint1Have63TypeTableStruct *)malloc(sizeof(Uint1Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1Have63ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 5;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1Have63ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint1Have63ByKeyID, 1, 0, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.表中支持含uint1中间表为pubsub资源表，含 precedence规则及级联删除
TEST_F(DlgSupUint, DataLog_068_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint1中间表为pubsub资源表，含 precedence规则及级联删除";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_018";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson9, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson9, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson9, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1TypeTableStruct *objIn1 = (Uint1TypeTableStruct *)malloc(sizeof(Uint1TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint1TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateUint1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateUint1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateUint1ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, Uint1TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint1TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadUint1ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.支持设置31个uint1字段为主键
TEST_F(DlgSupUint, DataLog_068_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "支持设置31个uint1字段为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_019";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson17, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[4096] = "";
    (void)sprintf(schemaJson1, g_schemaJson17, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1Have63TypeTableStruct *objIn1 =
        (Uint1Have63TypeTableStruct *)malloc(sizeof(Uint1Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1PkHave31PropertyByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1PkHave31PropertyByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1PkHave31PropertyByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("out", objIn1 + 5, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint1Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.支持设置31个含uint1字段二级索引，并显示定义主键索引类型
TEST_F(DlgSupUint, DataLog_068_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "支持设置31个含uint1字段二级索引，并显示定义主键索引类型";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_020";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson17, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[4096] = "";
    (void)sprintf(schemaJson1, g_schemaJson17, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint1Have63TypeTableStruct *objIn1 =
        (Uint1Have63TypeTableStruct *)malloc(sizeof(Uint1Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint1Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint1TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint1PkHave31PropertyByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint1PkHave31PropertyByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint1PkHave31PropertyByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("out", objIn1 + 5, 5, Uint1TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint1TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint1Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint1Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint1Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.不支持uint2投影到int2
TEST_F(DlgSupUint, DataLog_068_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint2投影到int2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_021";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": INT2 vs UINT2 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.不支持.d中外部表uint2对应外部表int2
TEST_F(DlgSupUint, DataLog_068_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中外部表uint2对应外部表int2";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_022";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_022.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.不支持写数据uint2数据类型使用int2进行设置
TEST_F(DlgSupUint, DataLog_068_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据uint2数据类型使用int2进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_023";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t valueA = 10;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT16, &valueA, sizeof(valueA));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.不支持int2投影到uint2
TEST_F(DlgSupUint, DataLog_068_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持int2投影到uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_024";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": INT2 vs UINT2 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.不支持uint2作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_025";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.不支持uint2作为固定资源表输出字段
TEST_F(DlgSupUint, DataLog_068_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint2作为固定资源表输出字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_026";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "the output field of sequential resource \"rel\" should be int4 near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.不支持常量超过uint2上限
TEST_F(DlgSupUint, DataLog_068_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持常量超过uint2上限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_027";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "can not convert 65536 to uint16 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.trisent field 字段不支持为uint2
TEST_F(DlgSupUint, DataLog_068_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "trisent field 字段不支持为uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_028";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson29 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint16"},
            {"name" : "b", "type" : "int16"}
        ]
    } ])";
// 029.投影规则表中支持含uint2：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】）
TEST_F(DlgSupUint, DataLog_068_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_029";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson29, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeTableStruct *objIn1 = (Uint2TypeTableStruct *)malloc(sizeof(Uint2TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.join规则表中支持含uint2：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表(外uint2:.d:int2)】）
TEST_F(DlgSupUint, DataLog_068_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_030";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_030.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson29, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeTableStruct *objIn1 = (Uint2TypeTableStruct *)malloc(sizeof(Uint2TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint2TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint2TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint2TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.笛卡尔规则表中支持含uint2:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_031";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson29, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeTableStruct *objIn1 = (Uint2TypeTableStruct *)malloc(sizeof(Uint2TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson32 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint16"},
            {"name" : "b", "type" : "int32"}
        ]
    } ])";
// 032.忽略规则表中支持含uint2:【输入表：含update_by_rank可更新】,【中间表: triansent field】【出表：消息通知表】
TEST_F(DlgSupUint, DataLog_068_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_032";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson32, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson32, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeBIsInt4TableStruct *objIn1 =
        (Uint2TypeBIsInt4TableStruct *)malloc(sizeof(Uint2TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint2TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint2TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2AndBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "8ab9759ff55e46f956ed3ebbd8afa3f7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "84eb4fb9df2c52537e089ce4271d4eda");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "d6112770d4a878d1a34aa7edbd2c6997");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson33 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint16"},
            {"name" : "b", "type" : "int64"}
        ]
    } ])";
// 033.not join规则表中支持含uint2:【输入表：过期表】,【中间表: tuple表】【输出表：pubsub表】
TEST_F(DlgSupUint, DataLog_068_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_033";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson33, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson33, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.srvMemCtxLimit = 2;
    subConnOptions.connName = subConnName;  // MB
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./schema_file/update_pubsub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeBIsInt8TableStruct *objIn1 =
        (Uint2TypeBIsInt8TableStruct *)malloc(sizeof(Uint2TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535 + 100000;;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint2TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 5, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint2TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 5, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 5, RECV_TIMEOUT/6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5 + 100000;;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint2TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 5, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    ret = GmcUnSubscribe(stmt,subNameUpdate);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.常量规则表中支持含uint2:【输入表：tuple表】,【中间表: 固定资源表】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_034";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson32, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson32, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeBIsInt4TableStruct *objIn1 =
        (Uint2TypeBIsInt4TableStruct *)malloc(sizeof(Uint2TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint2TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint2TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2AndBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("inp", objIn1, 10, ReadUint2AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint2AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.表中支持含uint1group-by字段为uint2(access_current,access_delta,access_kv)
TEST_F(DlgSupUint, DataLog_068_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = ".表中支持含uint2group-by字段为uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_035";
    GmcStmtT *stmt;
    GmcConnT *conn;

    (void)CreateKvTable();

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson32, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson32, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeBIsInt4TableStruct *objIn1 =
        (Uint2TypeBIsInt4TableStruct *)malloc(sizeof(Uint2TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint2TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2AndBIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint2AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId("out", objIn1, 10, ReadUint2AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.表中支持含uint1支持外部表含(uint2:uint2)输入表finish join function
TEST_F(DlgSupUint, DataLog_068_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint1支持外部表含(uint1 uint1)";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_036";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_036.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson29, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson29, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeTableStruct *objIn1 = (Uint2TypeTableStruct *)malloc(sizeof(Uint2TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Uint2TypeTableStruct *out1 = (Uint2TypeTableStruct *)malloc(sizeof(Uint2TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Uint2TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Uint2TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Uint2TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Uint2TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    // 卸载so
    free(objIn1);
    free(out1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson37 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint16"},
            {"name" : "a1", "type" : "uint16"},
            {"name" : "a2", "type" : "uint16"},
            {"name" : "a3", "type" : "uint16"},
            {"name" : "a4", "type" : "uint16"},
            {"name" : "a5", "type" : "uint16"},
            {"name" : "a6", "type" : "uint16"},
            {"name" : "a7", "type" : "uint16"},
            {"name" : "a8", "type" : "uint16"},
            {"name" : "a9", "type" : "uint16"},
            {"name" : "a10", "type" : "uint16"},
            {"name" : "a11", "type" : "uint16"},
            {"name" : "a12", "type" : "uint16"},
            {"name" : "a13", "type" : "uint16"},
            {"name" : "a14", "type" : "uint16"},
            {"name" : "a15", "type" : "uint16"},
            {"name" : "a16", "type" : "uint16"},
            {"name" : "a17", "type" : "uint16"},
            {"name" : "a18", "type" : "uint16"},
            {"name" : "a19", "type" : "uint16"},
            {"name" : "a20", "type" : "uint16"},
            {"name" : "a21", "type" : "uint16"},
            {"name" : "a22", "type" : "uint16"},
            {"name" : "a23", "type" : "uint16"},
            {"name" : "a24", "type" : "uint16"},
            {"name" : "a25", "type" : "uint16"},
            {"name" : "a26", "type" : "uint16"},
            {"name" : "a27", "type" : "uint16"},
            {"name" : "a28", "type" : "uint16"},
            {"name" : "a29", "type" : "uint16"},
            {"name" : "a30", "type" : "uint16"},
            {"name" : "a31", "type" : "uint16"},
            {"name" : "a32", "type" : "uint16"},
            {"name" : "a33", "type" : "uint16"},
            {"name" : "a34", "type" : "uint16"},
            {"name" : "a35", "type" : "uint16"},
            {"name" : "a36", "type" : "uint16"},
            {"name" : "a37", "type" : "uint16"},
            {"name" : "a38", "type" : "uint16"},
            {"name" : "a39", "type" : "uint16"},
            {"name" : "a40", "type" : "uint16"},
            {"name" : "a41", "type" : "uint16"},
            {"name" : "a42", "type" : "uint16"},
            {"name" : "a43", "type" : "uint16"},
            {"name" : "a44", "type" : "uint16"},
            {"name" : "a45", "type" : "uint16"},
            {"name" : "a46", "type" : "uint16"},
            {"name" : "a47", "type" : "uint16"},
            {"name" : "a48", "type" : "uint16"},
            {"name" : "a49", "type" : "uint16"},
            {"name" : "a50", "type" : "uint16"},
            {"name" : "a51", "type" : "uint16"},
            {"name" : "a52", "type" : "uint16"},
            {"name" : "a53", "type" : "uint16"},
            {"name" : "a54", "type" : "uint16"},
            {"name" : "a55", "type" : "uint16"},
            {"name" : "a56", "type" : "uint16"},
            {"name" : "a57", "type" : "uint16"},
            {"name" : "a58", "type" : "uint16"},
            {"name" : "a59", "type" : "uint16"},
            {"name" : "a60", "type" : "uint16"},
            {"name" : "a61", "type" : "uint16"},
            {"name" : "a62", "type" : "uint16"}
        ]
    } ])";
// 037.表中支持含uint1支持.d中63个字段为uint2
TEST_F(DlgSupUint, DataLog_068_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint2支持.d中63个字段为uint2";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_037";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2Have63TypeTableStruct *objIn1 =
        (Uint2Have63TypeTableStruct *)malloc(sizeof(Uint2Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson37, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[4096] = "";
    (void)sprintf(schemaJson1, g_schemaJson37, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2Have63ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 5;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2Have63ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint2Have63ByKeyID, 1, 0, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.表中支持含uint2中间表为pubsub资源表，含 precedence规则及级联删除
TEST_F(DlgSupUint, DataLog_068_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint2中间表为pubsub资源表，含 precedence规则及级联删除";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_038";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson29, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson29, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson29, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2TypeTableStruct *objIn1 = (Uint2TypeTableStruct *)malloc(sizeof(Uint2TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint2TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateUint2ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateUint2ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateUint2ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, Uint2TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint2TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint2TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint2TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读
    ret = readRecordId("inp1", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadUint2ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.支持设置31个uint2字段为主键
TEST_F(DlgSupUint, DataLog_068_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "支持设置31个uint2字段为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_039";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2Have63TypeTableStruct *objIn1 =
        (Uint2Have63TypeTableStruct *)malloc(sizeof(Uint2Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson37, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[4096] = "";
    (void)sprintf(schemaJson1, g_schemaJson37, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2PkHave31PropertyByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2PkHave31PropertyByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2PkHave31PropertyByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("out", objIn1 + 5, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint2Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.支持设置31个含uint2字段二级索引，并显示定义主键索引类型
TEST_F(DlgSupUint, DataLog_068_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "支持设置31个含uint2字段二级索引，并显示定义主键索引类型";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_040";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson37, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[4096] = "";
    (void)sprintf(schemaJson1, g_schemaJson37, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint2Have63TypeTableStruct *objIn1 =
        (Uint2Have63TypeTableStruct *)malloc(sizeof(Uint2Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint2Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint2TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint2PkHave31PropertyByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint2PkHave31PropertyByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint2PkHave31PropertyByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("out", objIn1 + 5, 5, Uint2TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint2TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint2Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint2Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint2Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.不支持uint4投影到int4
TEST_F(DlgSupUint, DataLog_068_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint4投影到int4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_041";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": INT4 vs UINT4 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.不支持.d中外部表uint4对应外部表int4
TEST_F(DlgSupUint, DataLog_068_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中外部表uint4对应外部表int4";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_042";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_042.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.不支持写数据uint4数据类型使用int4进行设置
TEST_F(DlgSupUint, DataLog_068_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据uint4数据类型使用int4进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_043";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valueA = 10;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &valueA, sizeof(valueA));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.不支持int4投影到uint4
TEST_F(DlgSupUint, DataLog_068_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持int4投影到uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_044";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": INT4 vs UINT4 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.不支持uint4作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint4作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_045";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.不支持uint4作为固定资源表输出字段
TEST_F(DlgSupUint, DataLog_068_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint4作为固定资源表输出字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_046";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "the output field of sequential resource \"rel\" should be int4 near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.不支持常量超过uint4上限
TEST_F(DlgSupUint, DataLog_068_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持常量超过uint4上限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_047";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "can not convert 4294967296 to uint32 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.trisent field 字段不支持为uint4
TEST_F(DlgSupUint, DataLog_068_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "trisent field 字段不支持为uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_048";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson49 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint32"},
            {"name" : "b", "type" : "int32"}
        ]
    } ])";
// 049.投影规则表中支持含uint4：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】)
TEST_F(DlgSupUint, DataLog_068_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_049";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson49, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeTableStruct *objIn1 = (Uint4TypeTableStruct *)malloc(sizeof(Uint4TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.join规则表中支持含uint4：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表(外uint4:.d:int4)】）
TEST_F(DlgSupUint, DataLog_068_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_050";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_050.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson49, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson49, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeTableStruct *objIn1 = (Uint4TypeTableStruct *)malloc(sizeof(Uint4TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.笛卡尔规则表中支持含uint4:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_051";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson49, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson49, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeTableStruct *objIn1 = (Uint4TypeTableStruct *)malloc(sizeof(Uint4TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 052.忽略规则表中支持含uint4:【输入表：含update_by_rank可更新】,【中间表: triansent field】【输出表：消息通知表】
// 部分可更新表删除数据,预期删除5条实际删除3条
TEST_F(DlgSupUint, DataLog_068_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_052";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson49, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson49, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeBIsInt4TableStruct *objIn1 =
        (Uint4TypeBIsInt4TableStruct *)malloc(sizeof(Uint4TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint4TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint4TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "fe23fafdd8e02fd6360820983c52df8c");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "e328c94f65961174dc9f59fa080b47aa");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "a9334e2666224f5fbf55cd4f47bdf735");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson53 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint32"},
            {"name" : "b", "type" : "int64"}
        ]
    } ])";
// 053.not join规则表中支持含uint4:【输入表：过期表】,【中间表: tuple表】【输出表：pubsub表】
TEST_F(DlgSupUint, DataLog_068_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_053";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.srvMemCtxLimit = 2;
    subConnOptions.connName = subConnName;  // MB
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./schema_file/update_pubsub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson53, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeBIsInt8TableStruct *objIn1 =
        (Uint4TypeBIsInt8TableStruct *)malloc(sizeof(Uint4TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, 5, Uint4TypeBIsInt8TableGet);
    ret = readRecord("mid", objIn1, 5, Uint4TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(4);
    ret = readRecord("inp", objIn1, 10, Uint4TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint4TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint4TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(4);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    ret = GmcUnSubscribe(stmt,subNameUpdate);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.常量规则表中支持含uint4:【输入表：tuple表】,【中间表: 固定资源表】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_054";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson49, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson49, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeBIsInt4TableStruct *objIn1 =
        (Uint4TypeBIsInt4TableStruct *)malloc(sizeof(Uint4TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint4TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint4TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("inp", objIn1, 10, ReadUint4AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint4AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.表中支持含uint4group-by字段为uint4(access_current,access_delta,access_kv)
TEST_F(DlgSupUint, DataLog_068_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = ".表中支持含uint1group-by字段为uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_055";
    GmcStmtT *stmt;
    GmcConnT *conn;

    (void)CreateKvTable();

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson49, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeBIsInt4TableStruct *objIn1 =
        (Uint4TypeBIsInt4TableStruct *)malloc(sizeof(Uint4TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint4TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint4AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId("out", objIn1, 10, ReadUint4AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056.表中支持含uint4支持外部表含(uint4:uint4)输入表finish join function
TEST_F(DlgSupUint, DataLog_068_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint4支持外部表含(uint4 uint4)";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_056";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_056.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeTableStruct *objIn1 = (Uint4TypeTableStruct *)malloc(sizeof(Uint4TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Uint4TypeTableStruct *out1 = (Uint4TypeTableStruct *)malloc(sizeof(Uint4TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Uint4TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson49, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Uint4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Uint4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Uint4TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    // 卸载so
    free(objIn1);
    free(out1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson57 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint32"},
            {"name" : "a1", "type" : "uint32"},
            {"name" : "a2", "type" : "uint32"},
            {"name" : "a3", "type" : "uint32"},
            {"name" : "a4", "type" : "uint32"},
            {"name" : "a5", "type" : "uint32"},
            {"name" : "a6", "type" : "uint32"},
            {"name" : "a7", "type" : "uint32"},
            {"name" : "a8", "type" : "uint32"},
            {"name" : "a9", "type" : "uint32"},
            {"name" : "a10", "type" : "uint32"},
            {"name" : "a11", "type" : "uint32"},
            {"name" : "a12", "type" : "uint32"},
            {"name" : "a13", "type" : "uint32"},
            {"name" : "a14", "type" : "uint32"},
            {"name" : "a15", "type" : "uint32"},
            {"name" : "a16", "type" : "uint32"},
            {"name" : "a17", "type" : "uint32"},
            {"name" : "a18", "type" : "uint32"},
            {"name" : "a19", "type" : "uint32"},
            {"name" : "a20", "type" : "uint32"},
            {"name" : "a21", "type" : "uint32"},
            {"name" : "a22", "type" : "uint32"},
            {"name" : "a23", "type" : "uint32"},
            {"name" : "a24", "type" : "uint32"},
            {"name" : "a25", "type" : "uint32"},
            {"name" : "a26", "type" : "uint32"},
            {"name" : "a27", "type" : "uint32"},
            {"name" : "a28", "type" : "uint32"},
            {"name" : "a29", "type" : "uint32"},
            {"name" : "a30", "type" : "uint32"},
            {"name" : "a31", "type" : "uint32"},
            {"name" : "a32", "type" : "uint32"},
            {"name" : "a33", "type" : "uint32"},
            {"name" : "a34", "type" : "uint32"},
            {"name" : "a35", "type" : "uint32"},
            {"name" : "a36", "type" : "uint32"},
            {"name" : "a37", "type" : "uint32"},
            {"name" : "a38", "type" : "uint32"},
            {"name" : "a39", "type" : "uint32"},
            {"name" : "a40", "type" : "uint32"},
            {"name" : "a41", "type" : "uint32"},
            {"name" : "a42", "type" : "uint32"},
            {"name" : "a43", "type" : "uint32"},
            {"name" : "a44", "type" : "uint32"},
            {"name" : "a45", "type" : "uint32"},
            {"name" : "a46", "type" : "uint32"},
            {"name" : "a47", "type" : "uint32"},
            {"name" : "a48", "type" : "uint32"},
            {"name" : "a49", "type" : "uint32"},
            {"name" : "a50", "type" : "uint32"},
            {"name" : "a51", "type" : "uint32"},
            {"name" : "a52", "type" : "uint32"},
            {"name" : "a53", "type" : "uint32"},
            {"name" : "a54", "type" : "uint32"},
            {"name" : "a55", "type" : "uint32"},
            {"name" : "a56", "type" : "uint32"},
            {"name" : "a57", "type" : "uint32"},
            {"name" : "a58", "type" : "uint32"},
            {"name" : "a59", "type" : "uint32"},
            {"name" : "a60", "type" : "uint32"},
            {"name" : "a61", "type" : "uint32"},
            {"name" : "a62", "type" : "uint32"}
        ]
    } ])";
// 057.表中支持含uint4支持.d中63个字段为uint4
TEST_F(DlgSupUint, DataLog_068_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint4支持.d中63个字段为uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_057";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4Have63TypeTableStruct *objIn1 =
        (Uint4Have63TypeTableStruct *)malloc(sizeof(Uint4Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson57, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4Have63ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 5;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4Have63ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint4Have63ByKeyID, 1, 0, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.表中支持含uint4中间表为pubsub资源表，含 precedence规则及级联删除
TEST_F(DlgSupUint, DataLog_068_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint1中间表为pubsub资源表，含 precedence规则及级联删除";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_058";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4TypeTableStruct *objIn1 = (Uint4TypeTableStruct *)malloc(sizeof(Uint4TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson49, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson49, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson49, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint4TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateUint4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateUint4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateUint4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, Uint4TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint4TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint4TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint4TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadUint4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059.支持设置31个uint4字段为主键
TEST_F(DlgSupUint, DataLog_068_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "支持设置31个uint4字段为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_059";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson57, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4Have63TypeTableStruct *objIn1 =
        (Uint4Have63TypeTableStruct *)malloc(sizeof(Uint4Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4PkHave31PropertyByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4PkHave31PropertyByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4PkHave31PropertyByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("out", objIn1 + 5, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint4Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060.支持设置31个含uint4字段二级索引，并显示定义主键索引类型
TEST_F(DlgSupUint, DataLog_068_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "支持设置31个含uint4字段二级索引，并显示定义主键索引类型";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_060";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson57, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint4Have63TypeTableStruct *objIn1 =
        (Uint4Have63TypeTableStruct *)malloc(sizeof(Uint4Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint4Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint4TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint4PkHave31PropertyByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint4PkHave31PropertyByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint4PkHave31PropertyByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("out", objIn1 + 5, 5, Uint4TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint4TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint4Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint4Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint4Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061.不支持uint8投影到int8
TEST_F(DlgSupUint, DataLog_068_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint8投影到int8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_061";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": INT8 vs UINT8 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062.不支持.d中外部表uint8对应外部表int8
TEST_F(DlgSupUint, DataLog_068_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持.d中外部表uint8对应外部表int8";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_062";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_062.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    AW_FUN_Log(LOG_STEP, testcasesInfo);
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.so", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s", g_toolPath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);

    ret = executeCommand(command, "Import datalog file unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063.不支持写数据uint8数据类型使用int8进行设置
TEST_F(DlgSupUint, DataLog_068_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "不支持写数据uint8数据类型使用int8进行设置";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_063";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    ret = GmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t valueA = 10;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &valueA, sizeof(valueA));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064.不支持int8投影到uint8
TEST_F(DlgSupUint, DataLog_068_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持int8投影到uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_064";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command,
        "type mismatch for left table \"out\" and right table \"inp\"'s field \"a\": INT8 vs UINT8 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065.不支持uint8作为timeout表过期字段
TEST_F(DlgSupUint, DataLog_068_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint8作为timeout表过期字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_045";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "timeout field \"a\" of table \"inp\" should be int8 near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 066.不支持uint8作为固定资源表输出字段
TEST_F(DlgSupUint, DataLog_068_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持uint8作为固定资源表输出字段";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_066";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "the output field of sequential resource \"rel\" should be int4 near line 2.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067.不支持常量超过uint8上限
TEST_F(DlgSupUint, DataLog_068_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持常量超过uint8上限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_067";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "can not convert 18446744073709551616 to uint64 near line 3.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 068.trisent field 字段不支持为uint8
TEST_F(DlgSupUint, DataLog_068_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "trisent field 字段不支持为uint4";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_068";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "transient field should be int4 in table \"inp\" near line 1.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson69 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint64"},
            {"name" : "b", "type" : "int32"}
        ]
    } ])";
// 069.投影规则表中支持含uint8：(【输入表：普通表】【 中间表：普通表】【 输出表：普通表】)
TEST_F(DlgSupUint, DataLog_068_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "投影规则表中支持含uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_069";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson69, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeTableStruct *objIn1 = (Uint8TypeTableStruct *)malloc(sizeof(Uint8TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeTableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 070.join规则表中支持含uint8：(【输入表：完全可更新表】 【中间表：状态表】 【输出表：外部表(外uint8:.d:int8)】）
TEST_F(DlgSupUint, DataLog_068_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "join规则表中支持含uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_070";

    GmcStmtT *stmt;
    GmcConnT *conn;

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_070.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson69, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson69, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeTableStruct *objIn1 = (Uint8TypeTableStruct *)malloc(sizeof(Uint8TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 128;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 10, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    system("gmsysview record mid");
    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count inp1");
    system("gmsysview count state");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 071.笛卡尔规则表中支持含uint8:(【输入表：部分可更新表】 【中间表：finish表】 【输出表：tbm表】）
TEST_F(DlgSupUint, DataLog_068_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "笛卡尔规则表中支持含uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_071";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson69, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson69, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeTableStruct *objIn1 = (Uint8TypeTableStruct *)malloc(sizeof(Uint8TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 25, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 100, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除的语义
    system("cat /root/_datalog_/TbmRunLog.txt");

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview -q V\\$CATA_TBM_TABLE_INFO");
    // 校验tbm文本

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *g_schemaJson72 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint64"},
            {"name" : "b", "type" : "int32"}
        ]
    } ])";
// 072.忽略规则表中支持含uint8:【输入表：含update_by_rank可更新】,【中间表: triansent field】【输出表：消息通知表】
// 部分可更新表删除数据,预期删除5条实际删除3条
TEST_F(DlgSupUint, DataLog_068_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "忽略规则表中支持含uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_072";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson72, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson72, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeBIsInt4TableStruct *objIn1 =
        (Uint8TypeBIsInt4TableStruct *)malloc(sizeof(Uint8TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint8TypeAndBIsInt4TableSet, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新的值比原数据小更新成功
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i % 255;
        objIn1[i].b = i % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogInsert.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogInsert.txt", "14ad12a7bd4ca10bec8a8f521f5fb495");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogDelete.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogDelete.txt", "6f8d50f693a3cb5ce4596eee23b436d0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateNew.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateNew.txt", "e328c94f65961174dc9f59fa080b47aa");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("md5sum /root/_datalog_/RunLogUpdateold.txt");
    ret = executeCommand((char *)"md5sum /root/_datalog_/RunLogUpdateold.txt", "a9334e2666224f5fbf55cd4f47bdf735");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson73 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint8"},
            {"name" : "b", "type" : "int64"}
        ]
    } ])";
// 073.not join规则表中支持含uint8:【输入表：过期表】,【中间表: tuple表】【输出表：pubsub表】
TEST_F(DlgSupUint, DataLog_068_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "not join规则表中支持含uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_073";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.srvMemCtxLimit = 2;
    subConnOptions.connName = subConnName;  // MB
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./schema_file/update_pubsub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt8_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson73, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeBIsInt8TableStruct *objIn1 =
        (Uint8TypeBIsInt8TableStruct *)malloc(sizeof(Uint8TypeBIsInt8TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeBIsInt8TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535 + 10000;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 5, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeAndBIsInt8TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 5, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8AndBIsInt8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8AndBIsInt8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint8TypeBIsInt8TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeAndBIsInt8TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8AndBIsInt8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8AndBIsInt8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 30, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    ret = GmcUnSubscribe(stmt,subNameUpdate);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 074.常量规则表中支持含uint8:【输入表：tuple表】,【中间表: 固定资源表】【输出表：完全可更新表】
TEST_F(DlgSupUint, DataLog_068_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "常量规则表中支持含uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_074";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson72, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson72, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeBIsInt4TableStruct *objIn1 =
        (Uint8TypeBIsInt4TableStruct *)malloc(sizeof(Uint8TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // //  二级索引删除，输入表为tuple表
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据删除失败
    ret = readRecordId("inp", objIn1, 10, ReadUint8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 075.表中支持含uint8group-by字段为uint8(access_current,access_delta,access_kv)
TEST_F(DlgSupUint, DataLog_068_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = ".表中支持含uint1group-by字段为uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_075";
    GmcStmtT *stmt;
    GmcConnT *conn;

    (void)CreateKvTable();

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeBIsInt4TableStruct *objIn1 =
        (Uint8TypeBIsInt4TableStruct *)malloc(sizeof(Uint8TypeBIsInt4TableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeBIsInt4TableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson72, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeAndBIsInt4TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1, 10, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8AndBIsInt4ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8AndBIsInt4ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("mid", objIn1 + 5, 5, Uint8TypeBIsInt4TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeAndBIsInt4TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8BIsInt4ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("mid", objIn1, 10, ReadUint8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordId("out", objIn1, 10, ReadUint8AndBIsInt4ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count mid");
    system("gmsysview count out");

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 076.表中支持含uint8支持外部表含(uint8:uint8)输入表finish join function
TEST_F(DlgSupUint, DataLog_068_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint8支持外部表含(uint8 uint8)";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char tableName[10] = "out";
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_076";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 创建外部表
    char lableJsonpath[100] = "./schema_file/DataLog_068_076.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson69, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeTableStruct *objIn1 = (Uint8TypeTableStruct *)malloc(sizeof(Uint8TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 65535;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    Uint8TypeTableStruct *out1 = (Uint8TypeTableStruct *)malloc(sizeof(Uint8TypeTableStruct) * recordNum);
    if (out1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "out1 malloc failed !!!");
    }
    memset(out1, 0, sizeof(Uint8TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 65535;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 5, Uint8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1, 10, Uint8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        out1[i].a = (i + 2) % 255;
        out1[i].b = (i + 1) % 5;
        out1[i].dtlReservedCount = 1;
        out1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", out1 + 5, 5, Uint8TypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    // 卸载so
    free(objIn1);
    free(out1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_schemaJson77 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "uint64"},
            {"name" : "a1", "type" : "uint64"},
            {"name" : "a2", "type" : "uint64"},
            {"name" : "a3", "type" : "uint64"},
            {"name" : "a4", "type" : "uint64"},
            {"name" : "a5", "type" : "uint64"},
            {"name" : "a6", "type" : "uint64"},
            {"name" : "a7", "type" : "uint64"},
            {"name" : "a8", "type" : "uint64"},
            {"name" : "a9", "type" : "uint64"},
            {"name" : "a10", "type" : "uint64"},
            {"name" : "a11", "type" : "uint64"},
            {"name" : "a12", "type" : "uint64"},
            {"name" : "a13", "type" : "uint64"},
            {"name" : "a14", "type" : "uint64"},
            {"name" : "a15", "type" : "uint64"},
            {"name" : "a16", "type" : "uint64"},
            {"name" : "a17", "type" : "uint64"},
            {"name" : "a18", "type" : "uint64"},
            {"name" : "a19", "type" : "uint64"},
            {"name" : "a20", "type" : "uint64"},
            {"name" : "a21", "type" : "uint64"},
            {"name" : "a22", "type" : "uint64"},
            {"name" : "a23", "type" : "uint64"},
            {"name" : "a24", "type" : "uint64"},
            {"name" : "a25", "type" : "uint64"},
            {"name" : "a26", "type" : "uint64"},
            {"name" : "a27", "type" : "uint64"},
            {"name" : "a28", "type" : "uint64"},
            {"name" : "a29", "type" : "uint64"},
            {"name" : "a30", "type" : "uint64"},
            {"name" : "a31", "type" : "uint64"},
            {"name" : "a32", "type" : "uint64"},
            {"name" : "a33", "type" : "uint64"},
            {"name" : "a34", "type" : "uint64"},
            {"name" : "a35", "type" : "uint64"},
            {"name" : "a36", "type" : "uint64"},
            {"name" : "a37", "type" : "uint64"},
            {"name" : "a38", "type" : "uint64"},
            {"name" : "a39", "type" : "uint64"},
            {"name" : "a40", "type" : "uint64"},
            {"name" : "a41", "type" : "uint64"},
            {"name" : "a42", "type" : "uint64"},
            {"name" : "a43", "type" : "uint64"},
            {"name" : "a44", "type" : "uint64"},
            {"name" : "a45", "type" : "uint64"},
            {"name" : "a46", "type" : "uint64"},
            {"name" : "a47", "type" : "uint64"},
            {"name" : "a48", "type" : "uint64"},
            {"name" : "a49", "type" : "uint64"},
            {"name" : "a50", "type" : "uint64"},
            {"name" : "a51", "type" : "uint64"},
            {"name" : "a52", "type" : "uint64"},
            {"name" : "a53", "type" : "uint64"},
            {"name" : "a54", "type" : "uint64"},
            {"name" : "a55", "type" : "uint64"},
            {"name" : "a56", "type" : "uint64"},
            {"name" : "a57", "type" : "uint64"},
            {"name" : "a58", "type" : "uint64"},
            {"name" : "a59", "type" : "uint64"},
            {"name" : "a60", "type" : "uint64"},
            {"name" : "a61", "type" : "uint64"},
            {"name" : "a62", "type" : "uint64"}
        ]
    } ])";
// 077.表中支持含uint8支持.d中63个字段为uint8
TEST_F(DlgSupUint, DataLog_068_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint8支持.d中63个字段为uint8";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_077";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8Have63TypeTableStruct *objIn1 =
        (Uint8Have63TypeTableStruct *)malloc(sizeof(Uint8Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson77, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8Have63ByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 5;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8Have63ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1 + 5, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint8Have63ByKeyID, 1, 0, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 078.表中支持含uint8中间表为pubsub资源表，含 precedence规则及级联删除
TEST_F(DlgSupUint, DataLog_068_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "表中支持含uint8中间表为pubsub资源表，含 precedence规则及级联删除";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_078";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson69, "inp1");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson1[1024] = "";
    (void)sprintf(schemaJson1, g_schemaJson69, "inp2");
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson2[1024] = "";
    (void)sprintf(schemaJson2, g_schemaJson69, "inp3");
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8TypeTableStruct *objIn1 = (Uint8TypeTableStruct *)malloc(sizeof(Uint8TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = 10;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp2", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inp3", objIn1, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint8TypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp1", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp2", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp3", objIn1, 10, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // count为负的数据不支持写入
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].b = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp1", objIn1 + 5, 5, UpdateUint8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp2", objIn1 + 5, 5, UpdateUint8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateRecord(conn, stmt, "inp3", objIn1 + 5, 5, UpdateUint8ByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp1", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp2", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = readRecord("inp3", objIn1 + 5, 5, Uint8TypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp1", objIn1 + 5, 5, Uint8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1 + 5, 5, Uint8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1 + 5, 5, Uint8TypeTableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp1", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp2", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = readRecordId("inp3", objIn1, 10, ReadUint8ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 079.支持设置31个uint8字段为主键
TEST_F(DlgSupUint, DataLog_068_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "支持设置31个uint8字段为主键";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_079";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8Have63TypeTableStruct *objIn1 =
        (Uint8Have63TypeTableStruct *)malloc(sizeof(Uint8Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson77, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8PkHave31PropertyByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8PkHave31PropertyByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8PkHave31PropertyByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("out", objIn1 + 5, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint8Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    free(objIn1);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 080.支持设置31个含uint8字段二级索引，并显示定义主键索引类型
TEST_F(DlgSupUint, DataLog_068_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[100] = "支持设置31个含uint8字段二级索引，并显示定义主键索引类型";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_068_080";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    int recordNum = 100;
    char strT[10] = {0};
    Uint8Have63TypeTableStruct *objIn1 =
        (Uint8Have63TypeTableStruct *)malloc(sizeof(Uint8Have63TypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(Uint8Have63TypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a1 = (i + 1) % 255;
        objIn1[i].a2 = (i + 1) % 255;
        objIn1[i].a3 = (i + 1) % 255;
        objIn1[i].a4 = (i + 1) % 255;
        objIn1[i].a5 = (i + 1) % 255;
        objIn1[i].a6 = (i + 1) % 255;
        objIn1[i].a7 = (i + 1) % 255;
        objIn1[i].a8 = (i + 1) % 255;
        objIn1[i].a9 = (i + 1) % 255;
        objIn1[i].a10 = (i + 1) % 255;
        objIn1[i].a11 = (i + 1) % 255;
        objIn1[i].a12 = (i + 1) % 255;
        objIn1[i].a13 = (i + 1) % 255;
        objIn1[i].a14 = (i + 1) % 255;
        objIn1[i].a15 = (i + 1) % 255;
        objIn1[i].a16 = (i + 1) % 255;
        objIn1[i].a17 = (i + 1) % 255;
        objIn1[i].a18 = (i + 1) % 255;
        objIn1[i].a19 = (i + 1) % 255;
        objIn1[i].a20 = (i + 1) % 255;
        objIn1[i].a21 = (i + 1) % 255;
        objIn1[i].a22 = (i + 1) % 255;
        objIn1[i].a23 = (i + 1) % 255;
        objIn1[i].a24 = (i + 1) % 255;
        objIn1[i].a25 = (i + 1) % 255;
        objIn1[i].a26 = (i + 1) % 255;
        objIn1[i].a27 = (i + 1) % 255;
        objIn1[i].a28 = (i + 1) % 255;
        objIn1[i].a29 = (i + 1) % 255;
        objIn1[i].a30 = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 255;
        objIn1[i].a32 = (i + 1) % 255;
        objIn1[i].a33 = (i + 1) % 255;
        objIn1[i].a34 = (i + 1) % 255;
        objIn1[i].a35 = (i + 1) % 255;
        objIn1[i].a36 = (i + 1) % 255;
        objIn1[i].a37 = (i + 1) % 255;
        objIn1[i].a38 = (i + 1) % 255;
        objIn1[i].a39 = (i + 1) % 255;
        objIn1[i].a40 = (i + 1) % 255;
        objIn1[i].a41 = (i + 1) % 255;
        objIn1[i].a42 = (i + 1) % 255;
        objIn1[i].a43 = (i + 1) % 255;
        objIn1[i].a44 = (i + 1) % 255;
        objIn1[i].a45 = (i + 1) % 255;
        objIn1[i].a46 = (i + 1) % 255;
        objIn1[i].a47 = (i + 1) % 255;
        objIn1[i].a48 = (i + 1) % 255;
        objIn1[i].a49 = (i + 1) % 255;
        objIn1[i].a50 = (i + 1) % 255;
        objIn1[i].a51 = (i + 1) % 255;
        objIn1[i].a52 = (i + 1) % 255;
        objIn1[i].a53 = (i + 1) % 255;
        objIn1[i].a54 = (i + 1) % 255;
        objIn1[i].a55 = (i + 1) % 255;
        objIn1[i].a56 = (i + 1) % 255;
        objIn1[i].a57 = (i + 1) % 255;
        objIn1[i].a58 = (i + 1) % 255;
        objIn1[i].a59 = (i + 1) % 255;
        objIn1[i].a60 = (i + 1) % 255;
        objIn1[i].a61 = (i + 1) % 255;
        objIn1[i].a62 = (i + 1) % 255;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char schemaJson[4096] = "";
    (void)sprintf(schemaJson, g_schemaJson77, "inp");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构单写5条数据
    ret = writeRecord(conn, stmt, "inp", objIn1 + 5, 5, Uint8TypeHave63TableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = readRecord("inp", objIn1, 10, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("out", objIn1, 10, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除前5条数据
    ret = DeletedataBykeyId(stmt, "inp", 0, objIn1, 5, DeleteUint8PkHave31PropertyByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键读预期删除成功
    ret = readRecordId("inp", objIn1, 5, ReadUint8PkHave31PropertyByKeyID, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 255;
        objIn1[i].a31 = (i + 1) % 5;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 主键更新后5条数据
    ret = UpdateRecord(conn, stmt, "inp", objIn1 + 5, 5, UpdateUint8PkHave31PropertyByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读数据
    ret = readRecord("inp", objIn1 + 5, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("out", objIn1 + 5, 5, Uint8TypeHave63TableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构批写
    ret = writeRecord(conn, stmt, "inp", objIn1, 5, Uint8TypeHave63TableSet, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  二级索引删除
    ret = DeletedataBykeyId(stmt, "inp", 1, objIn1, 10, DeleteUint8Have63ByKeyId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引读,预期数据都被删除
    ret = readRecordId("inp", objIn1, 10, ReadUint8Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecordId("out", objIn1, 10, ReadUint8Have63ByKeyID, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count inp");
    system("gmsysview count out");
    free(objIn1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
