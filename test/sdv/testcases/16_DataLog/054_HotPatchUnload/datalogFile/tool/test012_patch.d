%version v1.0.0 -> v2.0.0

%alter function funcA001(a:int4, b:int4 -> c:int4) {}
%alter rule r0 B1(a, b, c) :- A1(a, b, c), funcA001(a, b, c).
%alter function funcA002(a:int4, b:int4 -> c:int4) {}
%alter rule r1 B1(a, b, c) :- A1(a, b, c), funcA002(a, b, c).
%alter function funcA003(a:int4, b:int4 -> c:int4) {}
%alter rule r2 B1(a, b, c) :- A1(a, b, c), funcA003(a, b, c).
%alter function funcA004(a:int4, b:int4 -> c:int4) {}
%alter rule r3 B1(a, b, c) :- A1(a, b, c), funcA004(a, b, c).
%alter function funcA005(a:int4, b:int4 -> c:int4) {}
%alter rule r4 B1(a, b, c) :- A1(a, b, c), funcA005(a, b, c).
%alter function funcA006(a:int4, b:int4 -> c:int4) {}
%alter rule r5 B1(a, b, c) :- A1(a, b, c), funcA006(a, b, c).
%alter function funcA007(a:int4, b:int4 -> c:int4) {}
%alter rule r6 B1(a, b, c) :- A1(a, b, c), funcA007(a, b, c).
%alter function funcA008(a:int4, b:int4 -> c:int4) {}
%alter rule r7 B1(a, b, c) :- A1(a, b, c), funcA008(a, b, c).
%alter function funcA009(a:int4, b:int4 -> c:int4) {}
%alter rule r8 B1(a, b, c) :- A1(a, b, c), funcA009(a, b, c).
%alter function funcA010(a:int4, b:int4 -> c:int4) {}
%alter rule r9 B1(a, b, c) :- A1(a, b, c), funcA010(a, b, c).
%alter function funcA011(a:int4, b:int4 -> c:int4) {}
%alter rule r10 B1(a, b, c) :- A1(a, b, c), funcA011(a, b, c).
%alter function funcA012(a:int4, b:int4 -> c:int4) {}
%alter rule r11 B1(a, b, c) :- A1(a, b, c), funcA012(a, b, c).
%alter function funcA013(a:int4, b:int4 -> c:int4) {}
%alter rule r12 B1(a, b, c) :- A1(a, b, c), funcA013(a, b, c).
%alter function funcA014(a:int4, b:int4 -> c:int4) {}
%alter rule r13 B1(a, b, c) :- A1(a, b, c), funcA014(a, b, c).
%alter function funcA015(a:int4, b:int4 -> c:int4) {}
%alter rule r14 B1(a, b, c) :- A1(a, b, c), funcA015(a, b, c).
%alter function funcA016(a:int4, b:int4 -> c:int4) {}
%alter rule r15 B1(a, b, c) :- A1(a, b, c), funcA016(a, b, c).
%alter function funcA017(a:int4, b:int4 -> c:int4) {}
%alter rule r16 B1(a, b, c) :- A1(a, b, c), funcA017(a, b, c).
%alter function funcA018(a:int4, b:int4 -> c:int4) {}
%alter rule r17 B1(a, b, c) :- A1(a, b, c), funcA018(a, b, c).
%alter function funcA019(a:int4, b:int4 -> c:int4) {}
%alter rule r18 B1(a, b, c) :- A1(a, b, c), funcA019(a, b, c).
%alter function funcA020(a:int4, b:int4 -> c:int4) {}
%alter rule r19 B1(a, b, c) :- A1(a, b, c), funcA020(a, b, c).
%alter function funcA021(a:int4, b:int4 -> c:int4) {}
%alter rule r20 B1(a, b, c) :- A1(a, b, c), funcA021(a, b, c).
%alter function funcA022(a:int4, b:int4 -> c:int4) {}
%alter rule r21 B1(a, b, c) :- A1(a, b, c), funcA022(a, b, c).
%alter function funcA023(a:int4, b:int4 -> c:int4) {}
%alter rule r22 B1(a, b, c) :- A1(a, b, c), funcA023(a, b, c).
%alter function funcA024(a:int4, b:int4 -> c:int4) {}
%alter rule r23 B1(a, b, c) :- A1(a, b, c), funcA024(a, b, c).
%alter function funcA025(a:int4, b:int4 -> c:int4) {}
%alter rule r24 B1(a, b, c) :- A1(a, b, c), funcA025(a, b, c).
%alter function funcA026(a:int4, b:int4 -> c:int4) {}
%alter rule r25 B1(a, b, c) :- A1(a, b, c), funcA026(a, b, c).
%alter function funcA027(a:int4, b:int4 -> c:int4) {}
%alter rule r26 B1(a, b, c) :- A1(a, b, c), funcA027(a, b, c).
%alter function funcA028(a:int4, b:int4 -> c:int4) {}
%alter rule r27 B1(a, b, c) :- A1(a, b, c), funcA028(a, b, c).
%alter function funcA029(a:int4, b:int4 -> c:int4) {}
%alter rule r28 B1(a, b, c) :- A1(a, b, c), funcA029(a, b, c).
%alter function funcA030(a:int4, b:int4 -> c:int4) {}
%alter rule r29 B1(a, b, c) :- A1(a, b, c), funcA030(a, b, c).
%alter function funcA031(a:int4, b:int4 -> c:int4) {}
%alter rule r30 B1(a, b, c) :- A1(a, b, c), funcA031(a, b, c).
%alter function funcA032(a:int4, b:int4 -> c:int4) {}
%alter rule r31 B1(a, b, c) :- A1(a, b, c), funcA032(a, b, c).
%alter function funcA033(a:int4, b:int4 -> c:int4) {}
%alter rule r32 B1(a, b, c) :- A1(a, b, c), funcA033(a, b, c).
%alter function funcA034(a:int4, b:int4 -> c:int4) {}
%alter rule r33 B1(a, b, c) :- A1(a, b, c), funcA034(a, b, c).
%alter function funcA035(a:int4, b:int4 -> c:int4) {}
%alter rule r34 B1(a, b, c) :- A1(a, b, c), funcA035(a, b, c).
%alter function funcA036(a:int4, b:int4 -> c:int4) {}
%alter rule r35 B1(a, b, c) :- A1(a, b, c), funcA036(a, b, c).
%alter function funcA037(a:int4, b:int4 -> c:int4) {}
%alter rule r36 B1(a, b, c) :- A1(a, b, c), funcA037(a, b, c).
%alter function funcA038(a:int4, b:int4 -> c:int4) {}
%alter rule r37 B1(a, b, c) :- A1(a, b, c), funcA038(a, b, c).
%alter function funcA039(a:int4, b:int4 -> c:int4) {}
%alter rule r38 B1(a, b, c) :- A1(a, b, c), funcA039(a, b, c).
%alter function funcA040(a:int4, b:int4 -> c:int4) {}
%alter rule r39 B1(a, b, c) :- A1(a, b, c), funcA040(a, b, c).
%alter function funcA041(a:int4, b:int4 -> c:int4) {}
%alter rule r40 B1(a, b, c) :- A1(a, b, c), funcA041(a, b, c).
%alter function funcA042(a:int4, b:int4 -> c:int4) {}
%alter rule r41 B1(a, b, c) :- A1(a, b, c), funcA042(a, b, c).
%alter function funcA043(a:int4, b:int4 -> c:int4) {}
%alter rule r42 B1(a, b, c) :- A1(a, b, c), funcA043(a, b, c).
%alter function funcA044(a:int4, b:int4 -> c:int4) {}
%alter rule r43 B1(a, b, c) :- A1(a, b, c), funcA044(a, b, c).
%alter function funcA045(a:int4, b:int4 -> c:int4) {}
%alter rule r44 B1(a, b, c) :- A1(a, b, c), funcA045(a, b, c).
%alter function funcA046(a:int4, b:int4 -> c:int4) {}
%alter rule r45 B1(a, b, c) :- A1(a, b, c), funcA046(a, b, c).
%alter function funcA047(a:int4, b:int4 -> c:int4) {}
%alter rule r46 B1(a, b, c) :- A1(a, b, c), funcA047(a, b, c).
%alter function funcA048(a:int4, b:int4 -> c:int4) {}
%alter rule r47 B1(a, b, c) :- A1(a, b, c), funcA048(a, b, c).
%alter function funcA049(a:int4, b:int4 -> c:int4) {}
%alter rule r48 B1(a, b, c) :- A1(a, b, c), funcA049(a, b, c).
%alter function funcA050(a:int4, b:int4 -> c:int4) {}
%alter rule r49 B1(a, b, c) :- A1(a, b, c), funcA050(a, b, c).
%alter function funcA051(a:int4, b:int4 -> c:int4) {}
%alter rule r50 B1(a, b, c) :- A1(a, b, c), funcA051(a, b, c).
%alter function funcA052(a:int4, b:int4 -> c:int4) {}
%alter rule r51 B1(a, b, c) :- A1(a, b, c), funcA052(a, b, c).
%alter function funcA053(a:int4, b:int4 -> c:int4) {}
%alter rule r52 B1(a, b, c) :- A1(a, b, c), funcA053(a, b, c).
%alter function funcA054(a:int4, b:int4 -> c:int4) {}
%alter rule r53 B1(a, b, c) :- A1(a, b, c), funcA054(a, b, c).
%alter function funcA055(a:int4, b:int4 -> c:int4) {}
%alter rule r54 B1(a, b, c) :- A1(a, b, c), funcA055(a, b, c).
%alter function funcA056(a:int4, b:int4 -> c:int4) {}
%alter rule r55 B1(a, b, c) :- A1(a, b, c), funcA056(a, b, c).
%alter function funcA057(a:int4, b:int4 -> c:int4) {}
%alter rule r56 B1(a, b, c) :- A1(a, b, c), funcA057(a, b, c).
%alter function funcA058(a:int4, b:int4 -> c:int4) {}
%alter rule r57 B1(a, b, c) :- A1(a, b, c), funcA058(a, b, c).
%alter function funcA059(a:int4, b:int4 -> c:int4) {}
%alter rule r58 B1(a, b, c) :- A1(a, b, c), funcA059(a, b, c).
%alter function funcA060(a:int4, b:int4 -> c:int4) {}
%alter rule r59 B1(a, b, c) :- A1(a, b, c), funcA060(a, b, c).
%alter function funcA061(a:int4, b:int4 -> c:int4) {}
%alter rule r60 B1(a, b, c) :- A1(a, b, c), funcA061(a, b, c).
%alter function funcA062(a:int4, b:int4 -> c:int4) {}
%alter rule r61 B1(a, b, c) :- A1(a, b, c), funcA062(a, b, c).
%alter function funcA063(a:int4, b:int4 -> c:int4) {}
%alter rule r62 B1(a, b, c) :- A1(a, b, c), funcA063(a, b, c).
%alter function funcA064(a:int4, b:int4 -> c:int4) {}
%alter rule r63 B1(a, b, c) :- A1(a, b, c), funcA064(a, b, c).
%alter function funcA065(a:int4, b:int4 -> c:int4) {}
%alter rule r64 B1(a, b, c) :- A1(a, b, c), funcA065(a, b, c).
%alter function funcA066(a:int4, b:int4 -> c:int4) {}
%alter rule r65 B1(a, b, c) :- A1(a, b, c), funcA066(a, b, c).
%alter function funcA067(a:int4, b:int4 -> c:int4) {}
%alter rule r66 B1(a, b, c) :- A1(a, b, c), funcA067(a, b, c).
%alter function funcA068(a:int4, b:int4 -> c:int4) {}
%alter rule r67 B1(a, b, c) :- A1(a, b, c), funcA068(a, b, c).
%alter function funcA069(a:int4, b:int4 -> c:int4) {}
%alter rule r68 B1(a, b, c) :- A1(a, b, c), funcA069(a, b, c).
%alter function funcA070(a:int4, b:int4 -> c:int4) {}
%alter rule r69 B1(a, b, c) :- A1(a, b, c), funcA070(a, b, c).
%alter function funcA071(a:int4, b:int4 -> c:int4) {}
%alter rule r70 B1(a, b, c) :- A1(a, b, c), funcA071(a, b, c).
%alter function funcA072(a:int4, b:int4 -> c:int4) {}
%alter rule r71 B1(a, b, c) :- A1(a, b, c), funcA072(a, b, c).
%alter function funcA073(a:int4, b:int4 -> c:int4) {}
%alter rule r72 B1(a, b, c) :- A1(a, b, c), funcA073(a, b, c).
%alter function funcA074(a:int4, b:int4 -> c:int4) {}
%alter rule r73 B1(a, b, c) :- A1(a, b, c), funcA074(a, b, c).
%alter function funcA075(a:int4, b:int4 -> c:int4) {}
%alter rule r74 B1(a, b, c) :- A1(a, b, c), funcA075(a, b, c).
%alter function funcA076(a:int4, b:int4 -> c:int4) {}
%alter rule r75 B1(a, b, c) :- A1(a, b, c), funcA076(a, b, c).
%alter function funcA077(a:int4, b:int4 -> c:int4) {}
%alter rule r76 B1(a, b, c) :- A1(a, b, c), funcA077(a, b, c).
%alter function funcA078(a:int4, b:int4 -> c:int4) {}
%alter rule r77 B1(a, b, c) :- A1(a, b, c), funcA078(a, b, c).
%alter function funcA079(a:int4, b:int4 -> c:int4) {}
%alter rule r78 B1(a, b, c) :- A1(a, b, c), funcA079(a, b, c).
%alter function funcA080(a:int4, b:int4 -> c:int4) {}
%alter rule r79 B1(a, b, c) :- A1(a, b, c), funcA080(a, b, c).
%alter function funcA081(a:int4, b:int4 -> c:int4) {}
%alter rule r80 B1(a, b, c) :- A1(a, b, c), funcA081(a, b, c).
%alter function funcA082(a:int4, b:int4 -> c:int4) {}
%alter rule r81 B1(a, b, c) :- A1(a, b, c), funcA082(a, b, c).
%alter function funcA083(a:int4, b:int4 -> c:int4) {}
%alter rule r82 B1(a, b, c) :- A1(a, b, c), funcA083(a, b, c).
%alter function funcA084(a:int4, b:int4 -> c:int4) {}
%alter rule r83 B1(a, b, c) :- A1(a, b, c), funcA084(a, b, c).
%alter function funcA085(a:int4, b:int4 -> c:int4) {}
%alter rule r84 B1(a, b, c) :- A1(a, b, c), funcA085(a, b, c).
%alter function funcA086(a:int4, b:int4 -> c:int4) {}
%alter rule r85 B1(a, b, c) :- A1(a, b, c), funcA086(a, b, c).
%alter function funcA087(a:int4, b:int4 -> c:int4) {}
%alter rule r86 B1(a, b, c) :- A1(a, b, c), funcA087(a, b, c).
%alter function funcA088(a:int4, b:int4 -> c:int4) {}
%alter rule r87 B1(a, b, c) :- A1(a, b, c), funcA088(a, b, c).
%alter function funcA089(a:int4, b:int4 -> c:int4) {}
%alter rule r88 B1(a, b, c) :- A1(a, b, c), funcA089(a, b, c).
%alter function funcA090(a:int4, b:int4 -> c:int4) {}
%alter rule r89 B1(a, b, c) :- A1(a, b, c), funcA090(a, b, c).
%alter function funcA091(a:int4, b:int4 -> c:int4) {}
%alter rule r90 B1(a, b, c) :- A1(a, b, c), funcA091(a, b, c).
%alter function funcA092(a:int4, b:int4 -> c:int4) {}
%alter rule r91 B1(a, b, c) :- A1(a, b, c), funcA092(a, b, c).
%alter function funcA093(a:int4, b:int4 -> c:int4) {}
%alter rule r92 B1(a, b, c) :- A1(a, b, c), funcA093(a, b, c).
%alter function funcA094(a:int4, b:int4 -> c:int4) {}
%alter rule r93 B1(a, b, c) :- A1(a, b, c), funcA094(a, b, c).
%alter function funcA095(a:int4, b:int4 -> c:int4) {}
%alter rule r94 B1(a, b, c) :- A1(a, b, c), funcA095(a, b, c).
%alter function funcA096(a:int4, b:int4 -> c:int4) {}
%alter rule r95 B1(a, b, c) :- A1(a, b, c), funcA096(a, b, c).
%alter function funcA097(a:int4, b:int4 -> c:int4) {}
%alter rule r96 B1(a, b, c) :- A1(a, b, c), funcA097(a, b, c).
%alter function funcA098(a:int4, b:int4 -> c:int4) {}
%alter rule r97 B1(a, b, c) :- A1(a, b, c), funcA098(a, b, c).
%alter function funcA099(a:int4, b:int4 -> c:int4) {}
%alter rule r98 B1(a, b, c) :- A1(a, b, c), funcA099(a, b, c).
%alter function funcA100(a:int4, b:int4 -> c:int4) {}
%alter rule r99 B1(a, b, c) :- A1(a, b, c), funcA100(a, b, c).
%alter function funcA101(a:int4, b:int4 -> c:int4) {}
%alter rule r100 B1(a, b, c) :- A1(a, b, c), funcA101(a, b, c).
%alter function funcA102(a:int4, b:int4 -> c:int4) {}
%alter rule r101 B1(a, b, c) :- A1(a, b, c), funcA102(a, b, c).
%alter function funcA103(a:int4, b:int4 -> c:int4) {}
%alter rule r102 B1(a, b, c) :- A1(a, b, c), funcA103(a, b, c).
%alter function funcA104(a:int4, b:int4 -> c:int4) {}
%alter rule r103 B1(a, b, c) :- A1(a, b, c), funcA104(a, b, c).
%alter function funcA105(a:int4, b:int4 -> c:int4) {}
%alter rule r104 B1(a, b, c) :- A1(a, b, c), funcA105(a, b, c).
%alter function funcA106(a:int4, b:int4 -> c:int4) {}
%alter rule r105 B1(a, b, c) :- A1(a, b, c), funcA106(a, b, c).
%alter function funcA107(a:int4, b:int4 -> c:int4) {}
%alter rule r106 B1(a, b, c) :- A1(a, b, c), funcA107(a, b, c).
%alter function funcA108(a:int4, b:int4 -> c:int4) {}
%alter rule r107 B1(a, b, c) :- A1(a, b, c), funcA108(a, b, c).
%alter function funcA109(a:int4, b:int4 -> c:int4) {}
%alter rule r108 B1(a, b, c) :- A1(a, b, c), funcA109(a, b, c).
%alter function funcA110(a:int4, b:int4 -> c:int4) {}
%alter rule r109 B1(a, b, c) :- A1(a, b, c), funcA110(a, b, c).
%alter function funcA111(a:int4, b:int4 -> c:int4) {}
%alter rule r110 B1(a, b, c) :- A1(a, b, c), funcA111(a, b, c).
%alter function funcA112(a:int4, b:int4 -> c:int4) {}
%alter rule r111 B1(a, b, c) :- A1(a, b, c), funcA112(a, b, c).
%alter function funcA113(a:int4, b:int4 -> c:int4) {}
%alter rule r112 B1(a, b, c) :- A1(a, b, c), funcA113(a, b, c).
%alter function funcA114(a:int4, b:int4 -> c:int4) {}
%alter rule r113 B1(a, b, c) :- A1(a, b, c), funcA114(a, b, c).
%alter function funcA115(a:int4, b:int4 -> c:int4) {}
%alter rule r114 B1(a, b, c) :- A1(a, b, c), funcA115(a, b, c).
%alter function funcA116(a:int4, b:int4 -> c:int4) {}
%alter rule r115 B1(a, b, c) :- A1(a, b, c), funcA116(a, b, c).
%alter function funcA117(a:int4, b:int4 -> c:int4) {}
%alter rule r116 B1(a, b, c) :- A1(a, b, c), funcA117(a, b, c).
%alter function funcA118(a:int4, b:int4 -> c:int4) {}
%alter rule r117 B1(a, b, c) :- A1(a, b, c), funcA118(a, b, c).
%alter function funcA119(a:int4, b:int4 -> c:int4) {}
%alter rule r118 B1(a, b, c) :- A1(a, b, c), funcA119(a, b, c).
%alter function funcA120(a:int4, b:int4 -> c:int4) {}
%alter rule r119 B1(a, b, c) :- A1(a, b, c), funcA120(a, b, c).
%alter function funcA121(a:int4, b:int4 -> c:int4) {}
%alter rule r120 B1(a, b, c) :- A1(a, b, c), funcA121(a, b, c).
%alter function funcA122(a:int4, b:int4 -> c:int4) {}
%alter rule r121 B1(a, b, c) :- A1(a, b, c), funcA122(a, b, c).
%alter function funcA123(a:int4, b:int4 -> c:int4) {}
%alter rule r122 B1(a, b, c) :- A1(a, b, c), funcA123(a, b, c).
%alter function funcA124(a:int4, b:int4 -> c:int4) {}
%alter rule r123 B1(a, b, c) :- A1(a, b, c), funcA124(a, b, c).
%alter function funcA125(a:int4, b:int4 -> c:int4) {}
%alter rule r124 B1(a, b, c) :- A1(a, b, c), funcA125(a, b, c).
%alter function funcA126(a:int4, b:int4 -> c:int4) {}
%alter rule r125 B1(a, b, c) :- A1(a, b, c), funcA126(a, b, c).
%alter function funcA127(a:int4, b:int4 -> c:int4) {}
%alter rule r126 B1(a, b, c) :- A1(a, b, c), funcA127(a, b, c).
%alter function funcA128(a:int4, b:int4 -> c:int4) {}
%alter rule r127 B1(a, b, c) :- A1(a, b, c), funcA128(a, b, c).
%alter function funcA129(a:int4, b:int4 -> c:int4) {}
%alter rule r128 B1(a, b, c) :- A1(a, b, c), funcA129(a, b, c).
%alter function funcA130(a:int4, b:int4 -> c:int4) {}
%alter rule r129 B1(a, b, c) :- A1(a, b, c), funcA130(a, b, c).
%alter function funcA131(a:int4, b:int4 -> c:int4) {}
%alter rule r130 B1(a, b, c) :- A1(a, b, c), funcA131(a, b, c).
%alter function funcA132(a:int4, b:int4 -> c:int4) {}
%alter rule r131 B1(a, b, c) :- A1(a, b, c), funcA132(a, b, c).
%alter function funcA133(a:int4, b:int4 -> c:int4) {}
%alter rule r132 B1(a, b, c) :- A1(a, b, c), funcA133(a, b, c).
%alter function funcA134(a:int4, b:int4 -> c:int4) {}
%alter rule r133 B1(a, b, c) :- A1(a, b, c), funcA134(a, b, c).
%alter function funcA135(a:int4, b:int4 -> c:int4) {}
%alter rule r134 B1(a, b, c) :- A1(a, b, c), funcA135(a, b, c).
%alter function funcA136(a:int4, b:int4 -> c:int4) {}
%alter rule r135 B1(a, b, c) :- A1(a, b, c), funcA136(a, b, c).
%alter function funcA137(a:int4, b:int4 -> c:int4) {}
%alter rule r136 B1(a, b, c) :- A1(a, b, c), funcA137(a, b, c).
%alter function funcA138(a:int4, b:int4 -> c:int4) {}
%alter rule r137 B1(a, b, c) :- A1(a, b, c), funcA138(a, b, c).
%alter function funcA139(a:int4, b:int4 -> c:int4) {}
%alter rule r138 B1(a, b, c) :- A1(a, b, c), funcA139(a, b, c).
%alter function funcA140(a:int4, b:int4 -> c:int4) {}
%alter rule r139 B1(a, b, c) :- A1(a, b, c), funcA140(a, b, c).
%alter function funcA141(a:int4, b:int4 -> c:int4) {}
%alter rule r140 B1(a, b, c) :- A1(a, b, c), funcA141(a, b, c).
%alter function funcA142(a:int4, b:int4 -> c:int4) {}
%alter rule r141 B1(a, b, c) :- A1(a, b, c), funcA142(a, b, c).
%alter function funcA143(a:int4, b:int4 -> c:int4) {}
%alter rule r142 B1(a, b, c) :- A1(a, b, c), funcA143(a, b, c).
%alter function funcA144(a:int4, b:int4 -> c:int4) {}
%alter rule r143 B1(a, b, c) :- A1(a, b, c), funcA144(a, b, c).
%alter function funcA145(a:int4, b:int4 -> c:int4) {}
%alter rule r144 B1(a, b, c) :- A1(a, b, c), funcA145(a, b, c).
%alter function funcA146(a:int4, b:int4 -> c:int4) {}
%alter rule r145 B1(a, b, c) :- A1(a, b, c), funcA146(a, b, c).
%alter function funcA147(a:int4, b:int4 -> c:int4) {}
%alter rule r146 B1(a, b, c) :- A1(a, b, c), funcA147(a, b, c).
%alter function funcA148(a:int4, b:int4 -> c:int4) {}
%alter rule r147 B1(a, b, c) :- A1(a, b, c), funcA148(a, b, c).
%alter function funcA149(a:int4, b:int4 -> c:int4) {}
%alter rule r148 B1(a, b, c) :- A1(a, b, c), funcA149(a, b, c).
%alter function funcA150(a:int4, b:int4 -> c:int4) {}
%alter rule r149 B1(a, b, c) :- A1(a, b, c), funcA150(a, b, c).
%alter function funcA151(a:int4, b:int4 -> c:int4) {}
%alter rule r150 B1(a, b, c) :- A1(a, b, c), funcA151(a, b, c).
%alter function funcA152(a:int4, b:int4 -> c:int4) {}
%alter rule r151 B1(a, b, c) :- A1(a, b, c), funcA152(a, b, c).
%alter function funcA153(a:int4, b:int4 -> c:int4) {}
%alter rule r152 B1(a, b, c) :- A1(a, b, c), funcA153(a, b, c).
%alter function funcA154(a:int4, b:int4 -> c:int4) {}
%alter rule r153 B1(a, b, c) :- A1(a, b, c), funcA154(a, b, c).
%alter function funcA155(a:int4, b:int4 -> c:int4) {}
%alter rule r154 B1(a, b, c) :- A1(a, b, c), funcA155(a, b, c).
%alter function funcA156(a:int4, b:int4 -> c:int4) {}
%alter rule r155 B1(a, b, c) :- A1(a, b, c), funcA156(a, b, c).
%alter function funcA157(a:int4, b:int4 -> c:int4) {}
%alter rule r156 B1(a, b, c) :- A1(a, b, c), funcA157(a, b, c).
%alter function funcA158(a:int4, b:int4 -> c:int4) {}
%alter rule r157 B1(a, b, c) :- A1(a, b, c), funcA158(a, b, c).
%alter function funcA159(a:int4, b:int4 -> c:int4) {}
%alter rule r158 B1(a, b, c) :- A1(a, b, c), funcA159(a, b, c).
%alter function funcA160(a:int4, b:int4 -> c:int4) {}
%alter rule r159 B1(a, b, c) :- A1(a, b, c), funcA160(a, b, c).
%alter function funcA161(a:int4, b:int4 -> c:int4) {}
%alter rule r160 B1(a, b, c) :- A1(a, b, c), funcA161(a, b, c).
%alter function funcA162(a:int4, b:int4 -> c:int4) {}
%alter rule r161 B1(a, b, c) :- A1(a, b, c), funcA162(a, b, c).
%alter function funcA163(a:int4, b:int4 -> c:int4) {}
%alter rule r162 B1(a, b, c) :- A1(a, b, c), funcA163(a, b, c).
%alter function funcA164(a:int4, b:int4 -> c:int4) {}
%alter rule r163 B1(a, b, c) :- A1(a, b, c), funcA164(a, b, c).
%alter function funcA165(a:int4, b:int4 -> c:int4) {}
%alter rule r164 B1(a, b, c) :- A1(a, b, c), funcA165(a, b, c).
%alter function funcA166(a:int4, b:int4 -> c:int4) {}
%alter rule r165 B1(a, b, c) :- A1(a, b, c), funcA166(a, b, c).
%alter function funcA167(a:int4, b:int4 -> c:int4) {}
%alter rule r166 B1(a, b, c) :- A1(a, b, c), funcA167(a, b, c).
%alter function funcA168(a:int4, b:int4 -> c:int4) {}
%alter rule r167 B1(a, b, c) :- A1(a, b, c), funcA168(a, b, c).
%alter function funcA169(a:int4, b:int4 -> c:int4) {}
%alter rule r168 B1(a, b, c) :- A1(a, b, c), funcA169(a, b, c).
%alter function funcA170(a:int4, b:int4 -> c:int4) {}
%alter rule r169 B1(a, b, c) :- A1(a, b, c), funcA170(a, b, c).
%alter function funcA171(a:int4, b:int4 -> c:int4) {}
%alter rule r170 B1(a, b, c) :- A1(a, b, c), funcA171(a, b, c).
%alter function funcA172(a:int4, b:int4 -> c:int4) {}
%alter rule r171 B1(a, b, c) :- A1(a, b, c), funcA172(a, b, c).
%alter function funcA173(a:int4, b:int4 -> c:int4) {}
%alter rule r172 B1(a, b, c) :- A1(a, b, c), funcA173(a, b, c).
%alter function funcA174(a:int4, b:int4 -> c:int4) {}
%alter rule r173 B1(a, b, c) :- A1(a, b, c), funcA174(a, b, c).
%alter function funcA175(a:int4, b:int4 -> c:int4) {}
%alter rule r174 B1(a, b, c) :- A1(a, b, c), funcA175(a, b, c).
%alter function funcA176(a:int4, b:int4 -> c:int4) {}
%alter rule r175 B1(a, b, c) :- A1(a, b, c), funcA176(a, b, c).
%alter function funcA177(a:int4, b:int4 -> c:int4) {}
%alter rule r176 B1(a, b, c) :- A1(a, b, c), funcA177(a, b, c).
%alter function funcA178(a:int4, b:int4 -> c:int4) {}
%alter rule r177 B1(a, b, c) :- A1(a, b, c), funcA178(a, b, c).
%alter function funcA179(a:int4, b:int4 -> c:int4) {}
%alter rule r178 B1(a, b, c) :- A1(a, b, c), funcA179(a, b, c).
%alter function funcA180(a:int4, b:int4 -> c:int4) {}
%alter rule r179 B1(a, b, c) :- A1(a, b, c), funcA180(a, b, c).
%alter function funcA181(a:int4, b:int4 -> c:int4) {}
%alter rule r180 B1(a, b, c) :- A1(a, b, c), funcA181(a, b, c).
%alter function funcA182(a:int4, b:int4 -> c:int4) {}
%alter rule r181 B1(a, b, c) :- A1(a, b, c), funcA182(a, b, c).
%alter function funcA183(a:int4, b:int4 -> c:int4) {}
%alter rule r182 B1(a, b, c) :- A1(a, b, c), funcA183(a, b, c).
%alter function funcA184(a:int4, b:int4 -> c:int4) {}
%alter rule r183 B1(a, b, c) :- A1(a, b, c), funcA184(a, b, c).
%alter function funcA185(a:int4, b:int4 -> c:int4) {}
%alter rule r184 B1(a, b, c) :- A1(a, b, c), funcA185(a, b, c).
%alter function funcA186(a:int4, b:int4 -> c:int4) {}
%alter rule r185 B1(a, b, c) :- A1(a, b, c), funcA186(a, b, c).
%alter function funcA187(a:int4, b:int4 -> c:int4) {}
%alter rule r186 B1(a, b, c) :- A1(a, b, c), funcA187(a, b, c).
%alter function funcA188(a:int4, b:int4 -> c:int4) {}
%alter rule r187 B1(a, b, c) :- A1(a, b, c), funcA188(a, b, c).
%alter function funcA189(a:int4, b:int4 -> c:int4) {}
%alter rule r188 B1(a, b, c) :- A1(a, b, c), funcA189(a, b, c).
%alter function funcA190(a:int4, b:int4 -> c:int4) {}
%alter rule r189 B1(a, b, c) :- A1(a, b, c), funcA190(a, b, c).
%alter function funcA191(a:int4, b:int4 -> c:int4) {}
%alter rule r190 B1(a, b, c) :- A1(a, b, c), funcA191(a, b, c).
%alter function funcA192(a:int4, b:int4 -> c:int4) {}
%alter rule r191 B1(a, b, c) :- A1(a, b, c), funcA192(a, b, c).
%alter function funcA193(a:int4, b:int4 -> c:int4) {}
%alter rule r192 B1(a, b, c) :- A1(a, b, c), funcA193(a, b, c).
%alter function funcA194(a:int4, b:int4 -> c:int4) {}
%alter rule r193 B1(a, b, c) :- A1(a, b, c), funcA194(a, b, c).
%alter function funcA195(a:int4, b:int4 -> c:int4) {}
%alter rule r194 B1(a, b, c) :- A1(a, b, c), funcA195(a, b, c).
%alter function funcA196(a:int4, b:int4 -> c:int4) {}
%alter rule r195 B1(a, b, c) :- A1(a, b, c), funcA196(a, b, c).
%alter function funcA197(a:int4, b:int4 -> c:int4) {}
%alter rule r196 B1(a, b, c) :- A1(a, b, c), funcA197(a, b, c).
%alter function funcA198(a:int4, b:int4 -> c:int4) {}
%alter rule r197 B1(a, b, c) :- A1(a, b, c), funcA198(a, b, c).
%alter function funcA199(a:int4, b:int4 -> c:int4) {}
%alter rule r198 B1(a, b, c) :- A1(a, b, c), funcA199(a, b, c).
%alter function funcA200(a:int4, b:int4 -> c:int4) {}
%alter rule r199 B1(a, b, c) :- A1(a, b, c), funcA200(a, b, c).
%alter function funcA201(a:int4, b:int4 -> c:int4) {}
%alter rule r200 B1(a, b, c) :- A1(a, b, c), funcA201(a, b, c).
%alter function funcA202(a:int4, b:int4 -> c:int4) {}
%alter rule r201 B1(a, b, c) :- A1(a, b, c), funcA202(a, b, c).
%alter function funcA203(a:int4, b:int4 -> c:int4) {}
%alter rule r202 B1(a, b, c) :- A1(a, b, c), funcA203(a, b, c).
%alter function funcA204(a:int4, b:int4 -> c:int4) {}
%alter rule r203 B1(a, b, c) :- A1(a, b, c), funcA204(a, b, c).
%alter function funcA205(a:int4, b:int4 -> c:int4) {}
%alter rule r204 B1(a, b, c) :- A1(a, b, c), funcA205(a, b, c).
%alter function funcA206(a:int4, b:int4 -> c:int4) {}
%alter rule r205 B1(a, b, c) :- A1(a, b, c), funcA206(a, b, c).
%alter function funcA207(a:int4, b:int4 -> c:int4) {}
%alter rule r206 B1(a, b, c) :- A1(a, b, c), funcA207(a, b, c).
%alter function funcA208(a:int4, b:int4 -> c:int4) {}
%alter rule r207 B1(a, b, c) :- A1(a, b, c), funcA208(a, b, c).
%alter function funcA209(a:int4, b:int4 -> c:int4) {}
%alter rule r208 B1(a, b, c) :- A1(a, b, c), funcA209(a, b, c).
%alter function funcA210(a:int4, b:int4 -> c:int4) {}
%alter rule r209 B1(a, b, c) :- A1(a, b, c), funcA210(a, b, c).
%alter function funcA211(a:int4, b:int4 -> c:int4) {}
%alter rule r210 B1(a, b, c) :- A1(a, b, c), funcA211(a, b, c).
%alter function funcA212(a:int4, b:int4 -> c:int4) {}
%alter rule r211 B1(a, b, c) :- A1(a, b, c), funcA212(a, b, c).
%alter function funcA213(a:int4, b:int4 -> c:int4) {}
%alter rule r212 B1(a, b, c) :- A1(a, b, c), funcA213(a, b, c).
%alter function funcA214(a:int4, b:int4 -> c:int4) {}
%alter rule r213 B1(a, b, c) :- A1(a, b, c), funcA214(a, b, c).
%alter function funcA215(a:int4, b:int4 -> c:int4) {}
%alter rule r214 B1(a, b, c) :- A1(a, b, c), funcA215(a, b, c).
%alter function funcA216(a:int4, b:int4 -> c:int4) {}
%alter rule r215 B1(a, b, c) :- A1(a, b, c), funcA216(a, b, c).
%alter function funcA217(a:int4, b:int4 -> c:int4) {}
%alter rule r216 B1(a, b, c) :- A1(a, b, c), funcA217(a, b, c).
%alter function funcA218(a:int4, b:int4 -> c:int4) {}
%alter rule r217 B1(a, b, c) :- A1(a, b, c), funcA218(a, b, c).
%alter function funcA219(a:int4, b:int4 -> c:int4) {}
%alter rule r218 B1(a, b, c) :- A1(a, b, c), funcA219(a, b, c).
%alter function funcA220(a:int4, b:int4 -> c:int4) {}
%alter rule r219 B1(a, b, c) :- A1(a, b, c), funcA220(a, b, c).
%alter function funcA221(a:int4, b:int4 -> c:int4) {}
%alter rule r220 B1(a, b, c) :- A1(a, b, c), funcA221(a, b, c).
%alter function funcA222(a:int4, b:int4 -> c:int4) {}
%alter rule r221 B1(a, b, c) :- A1(a, b, c), funcA222(a, b, c).
%alter function funcA223(a:int4, b:int4 -> c:int4) {}
%alter rule r222 B1(a, b, c) :- A1(a, b, c), funcA223(a, b, c).
%alter function funcA224(a:int4, b:int4 -> c:int4) {}
%alter rule r223 B1(a, b, c) :- A1(a, b, c), funcA224(a, b, c).
%alter function funcA225(a:int4, b:int4 -> c:int4) {}
%alter rule r224 B1(a, b, c) :- A1(a, b, c), funcA225(a, b, c).
%alter function funcA226(a:int4, b:int4 -> c:int4) {}
%alter rule r225 B1(a, b, c) :- A1(a, b, c), funcA226(a, b, c).
%alter function funcA227(a:int4, b:int4 -> c:int4) {}
%alter rule r226 B1(a, b, c) :- A1(a, b, c), funcA227(a, b, c).
%alter function funcA228(a:int4, b:int4 -> c:int4) {}
%alter rule r227 B1(a, b, c) :- A1(a, b, c), funcA228(a, b, c).
%alter function funcA229(a:int4, b:int4 -> c:int4) {}
%alter rule r228 B1(a, b, c) :- A1(a, b, c), funcA229(a, b, c).
%alter function funcA230(a:int4, b:int4 -> c:int4) {}
%alter rule r229 B1(a, b, c) :- A1(a, b, c), funcA230(a, b, c).
%alter function funcA231(a:int4, b:int4 -> c:int4) {}
%alter rule r230 B1(a, b, c) :- A1(a, b, c), funcA231(a, b, c).
%alter function funcA232(a:int4, b:int4 -> c:int4) {}
%alter rule r231 B1(a, b, c) :- A1(a, b, c), funcA232(a, b, c).
%alter function funcA233(a:int4, b:int4 -> c:int4) {}
%alter rule r232 B1(a, b, c) :- A1(a, b, c), funcA233(a, b, c).
%alter function funcA234(a:int4, b:int4 -> c:int4) {}
%alter rule r233 B1(a, b, c) :- A1(a, b, c), funcA234(a, b, c).
%alter function funcA235(a:int4, b:int4 -> c:int4) {}
%alter rule r234 B1(a, b, c) :- A1(a, b, c), funcA235(a, b, c).
%alter function funcA236(a:int4, b:int4 -> c:int4) {}
%alter rule r235 B1(a, b, c) :- A1(a, b, c), funcA236(a, b, c).
%alter function funcA237(a:int4, b:int4 -> c:int4) {}
%alter rule r236 B1(a, b, c) :- A1(a, b, c), funcA237(a, b, c).
%alter function funcA238(a:int4, b:int4 -> c:int4) {}
%alter rule r237 B1(a, b, c) :- A1(a, b, c), funcA238(a, b, c).
%alter function funcA239(a:int4, b:int4 -> c:int4) {}
%alter rule r238 B1(a, b, c) :- A1(a, b, c), funcA239(a, b, c).
%alter function funcA240(a:int4, b:int4 -> c:int4) {}
%alter rule r239 B1(a, b, c) :- A1(a, b, c), funcA240(a, b, c).
%alter function funcA241(a:int4, b:int4 -> c:int4) {}
%alter rule r240 B1(a, b, c) :- A1(a, b, c), funcA241(a, b, c).
%alter function funcA242(a:int4, b:int4 -> c:int4) {}
%alter rule r241 B1(a, b, c) :- A1(a, b, c), funcA242(a, b, c).
%alter function funcA243(a:int4, b:int4 -> c:int4) {}
%alter rule r242 B1(a, b, c) :- A1(a, b, c), funcA243(a, b, c).
%alter function funcA244(a:int4, b:int4 -> c:int4) {}
%alter rule r243 B1(a, b, c) :- A1(a, b, c), funcA244(a, b, c).
%alter function funcA245(a:int4, b:int4 -> c:int4) {}
%alter rule r244 B1(a, b, c) :- A1(a, b, c), funcA245(a, b, c).
%alter function funcA246(a:int4, b:int4 -> c:int4) {}
%alter rule r245 B1(a, b, c) :- A1(a, b, c), funcA246(a, b, c).
%alter function funcA247(a:int4, b:int4 -> c:int4) {}
%alter rule r246 B1(a, b, c) :- A1(a, b, c), funcA247(a, b, c).
%alter function funcA248(a:int4, b:int4 -> c:int4) {}
%alter rule r247 B1(a, b, c) :- A1(a, b, c), funcA248(a, b, c).
%alter function funcA249(a:int4, b:int4 -> c:int4) {}
%alter rule r248 B1(a, b, c) :- A1(a, b, c), funcA249(a, b, c).
%alter function funcA250(a:int4, b:int4 -> c:int4) {}
%alter rule r249 B1(a, b, c) :- A1(a, b, c), funcA250(a, b, c).
%alter function funcA251(a:int4, b:int4 -> c:int4) {}
%alter rule r250 B1(a, b, c) :- A1(a, b, c), funcA251(a, b, c).
%alter function funcA252(a:int4, b:int4 -> c:int4) {}
%alter rule r251 B1(a, b, c) :- A1(a, b, c), funcA252(a, b, c).
%alter function funcA253(a:int4, b:int4 -> c:int4) {}
%alter rule r252 B1(a, b, c) :- A1(a, b, c), funcA253(a, b, c).
%alter function funcA254(a:int4, b:int4 -> c:int4) {}
%alter rule r253 B1(a, b, c) :- A1(a, b, c), funcA254(a, b, c).
%alter function funcA255(a:int4, b:int4 -> c:int4) {}
%alter rule r254 B1(a, b, c) :- A1(a, b, c), funcA255(a, b, c).
%alter function funcA256(a:int4, b:int4 -> c:int4) {}
%alter rule r255 B1(a, b, c) :- A1(a, b, c), funcA256(a, b, c).
%alter function funcA257(a:int4, b:int4 -> c:int4) {}
%alter rule r256 B1(a, b, c) :- A1(a, b, c), funcA257(a, b, c).
%alter function funcA258(a:int4, b:int4 -> c:int4) {}
%alter rule r257 B1(a, b, c) :- A1(a, b, c), funcA258(a, b, c).
%alter function funcA259(a:int4, b:int4 -> c:int4) {}
%alter rule r258 B1(a, b, c) :- A1(a, b, c), funcA259(a, b, c).
%alter function funcA260(a:int4, b:int4 -> c:int4) {}
%alter rule r259 B1(a, b, c) :- A1(a, b, c), funcA260(a, b, c).
%alter function funcA261(a:int4, b:int4 -> c:int4) {}
%alter rule r260 B1(a, b, c) :- A1(a, b, c), funcA261(a, b, c).
%alter function funcA262(a:int4, b:int4 -> c:int4) {}
%alter rule r261 B1(a, b, c) :- A1(a, b, c), funcA262(a, b, c).
%alter function funcA263(a:int4, b:int4 -> c:int4) {}
%alter rule r262 B1(a, b, c) :- A1(a, b, c), funcA263(a, b, c).
%alter function funcA264(a:int4, b:int4 -> c:int4) {}
%alter rule r263 B1(a, b, c) :- A1(a, b, c), funcA264(a, b, c).
%alter function funcA265(a:int4, b:int4 -> c:int4) {}
%alter rule r264 B1(a, b, c) :- A1(a, b, c), funcA265(a, b, c).
%alter function funcA266(a:int4, b:int4 -> c:int4) {}
%alter rule r265 B1(a, b, c) :- A1(a, b, c), funcA266(a, b, c).
%alter function funcA267(a:int4, b:int4 -> c:int4) {}
%alter rule r266 B1(a, b, c) :- A1(a, b, c), funcA267(a, b, c).
%alter function funcA268(a:int4, b:int4 -> c:int4) {}
%alter rule r267 B1(a, b, c) :- A1(a, b, c), funcA268(a, b, c).
%alter function funcA269(a:int4, b:int4 -> c:int4) {}
%alter rule r268 B1(a, b, c) :- A1(a, b, c), funcA269(a, b, c).
%alter function funcA270(a:int4, b:int4 -> c:int4) {}
%alter rule r269 B1(a, b, c) :- A1(a, b, c), funcA270(a, b, c).
%alter function funcA271(a:int4, b:int4 -> c:int4) {}
%alter rule r270 B1(a, b, c) :- A1(a, b, c), funcA271(a, b, c).
%alter function funcA272(a:int4, b:int4 -> c:int4) {}
%alter rule r271 B1(a, b, c) :- A1(a, b, c), funcA272(a, b, c).
%alter function funcA273(a:int4, b:int4 -> c:int4) {}
%alter rule r272 B1(a, b, c) :- A1(a, b, c), funcA273(a, b, c).
%alter function funcA274(a:int4, b:int4 -> c:int4) {}
%alter rule r273 B1(a, b, c) :- A1(a, b, c), funcA274(a, b, c).
%alter function funcA275(a:int4, b:int4 -> c:int4) {}
%alter rule r274 B1(a, b, c) :- A1(a, b, c), funcA275(a, b, c).
%alter function funcA276(a:int4, b:int4 -> c:int4) {}
%alter rule r275 B1(a, b, c) :- A1(a, b, c), funcA276(a, b, c).
%alter function funcA277(a:int4, b:int4 -> c:int4) {}
%alter rule r276 B1(a, b, c) :- A1(a, b, c), funcA277(a, b, c).
%alter function funcA278(a:int4, b:int4 -> c:int4) {}
%alter rule r277 B1(a, b, c) :- A1(a, b, c), funcA278(a, b, c).
%alter function funcA279(a:int4, b:int4 -> c:int4) {}
%alter rule r278 B1(a, b, c) :- A1(a, b, c), funcA279(a, b, c).
%alter function funcA280(a:int4, b:int4 -> c:int4) {}
%alter rule r279 B1(a, b, c) :- A1(a, b, c), funcA280(a, b, c).
%alter function funcA281(a:int4, b:int4 -> c:int4) {}
%alter rule r280 B1(a, b, c) :- A1(a, b, c), funcA281(a, b, c).
%alter function funcA282(a:int4, b:int4 -> c:int4) {}
%alter rule r281 B1(a, b, c) :- A1(a, b, c), funcA282(a, b, c).
%alter function funcA283(a:int4, b:int4 -> c:int4) {}
%alter rule r282 B1(a, b, c) :- A1(a, b, c), funcA283(a, b, c).
%alter function funcA284(a:int4, b:int4 -> c:int4) {}
%alter rule r283 B1(a, b, c) :- A1(a, b, c), funcA284(a, b, c).
%alter function funcA285(a:int4, b:int4 -> c:int4) {}
%alter rule r284 B1(a, b, c) :- A1(a, b, c), funcA285(a, b, c).
%alter function funcA286(a:int4, b:int4 -> c:int4) {}
%alter rule r285 B1(a, b, c) :- A1(a, b, c), funcA286(a, b, c).
%alter function funcA287(a:int4, b:int4 -> c:int4) {}
%alter rule r286 B1(a, b, c) :- A1(a, b, c), funcA287(a, b, c).
%alter function funcA288(a:int4, b:int4 -> c:int4) {}
%alter rule r287 B1(a, b, c) :- A1(a, b, c), funcA288(a, b, c).
%alter function funcA289(a:int4, b:int4 -> c:int4) {}
%alter rule r288 B1(a, b, c) :- A1(a, b, c), funcA289(a, b, c).
%alter function funcA290(a:int4, b:int4 -> c:int4) {}
%alter rule r289 B1(a, b, c) :- A1(a, b, c), funcA290(a, b, c).
%alter function funcA291(a:int4, b:int4 -> c:int4) {}
%alter rule r290 B1(a, b, c) :- A1(a, b, c), funcA291(a, b, c).
%alter function funcA292(a:int4, b:int4 -> c:int4) {}
%alter rule r291 B1(a, b, c) :- A1(a, b, c), funcA292(a, b, c).
%alter function funcA293(a:int4, b:int4 -> c:int4) {}
%alter rule r292 B1(a, b, c) :- A1(a, b, c), funcA293(a, b, c).
%alter function funcA294(a:int4, b:int4 -> c:int4) {}
%alter rule r293 B1(a, b, c) :- A1(a, b, c), funcA294(a, b, c).
%alter function funcA295(a:int4, b:int4 -> c:int4) {}
%alter rule r294 B1(a, b, c) :- A1(a, b, c), funcA295(a, b, c).
%alter function funcA296(a:int4, b:int4 -> c:int4) {}
%alter rule r295 B1(a, b, c) :- A1(a, b, c), funcA296(a, b, c).
%alter function funcA297(a:int4, b:int4 -> c:int4) {}
%alter rule r296 B1(a, b, c) :- A1(a, b, c), funcA297(a, b, c).
%alter function funcA298(a:int4, b:int4 -> c:int4) {}
%alter rule r297 B1(a, b, c) :- A1(a, b, c), funcA298(a, b, c).
%alter function funcA299(a:int4, b:int4 -> c:int4) {}
%alter rule r298 B1(a, b, c) :- A1(a, b, c), funcA299(a, b, c).
%alter function funcA300(a:int4, b:int4 -> c:int4) {}
%alter rule r299 B1(a, b, c) :- A1(a, b, c), funcA300(a, b, c).
%alter function funcA301(a:int4, b:int4 -> c:int4) {}
%alter rule r300 B1(a, b, c) :- A1(a, b, c), funcA301(a, b, c).
%alter function funcA302(a:int4, b:int4 -> c:int4) {}
%alter rule r301 B1(a, b, c) :- A1(a, b, c), funcA302(a, b, c).
%alter function funcA303(a:int4, b:int4 -> c:int4) {}
%alter rule r302 B1(a, b, c) :- A1(a, b, c), funcA303(a, b, c).
%alter function funcA304(a:int4, b:int4 -> c:int4) {}
%alter rule r303 B1(a, b, c) :- A1(a, b, c), funcA304(a, b, c).
%alter function funcA305(a:int4, b:int4 -> c:int4) {}
%alter rule r304 B1(a, b, c) :- A1(a, b, c), funcA305(a, b, c).
%alter function funcA306(a:int4, b:int4 -> c:int4) {}
%alter rule r305 B1(a, b, c) :- A1(a, b, c), funcA306(a, b, c).
%alter function funcA307(a:int4, b:int4 -> c:int4) {}
%alter rule r306 B1(a, b, c) :- A1(a, b, c), funcA307(a, b, c).
%alter function funcA308(a:int4, b:int4 -> c:int4) {}
%alter rule r307 B1(a, b, c) :- A1(a, b, c), funcA308(a, b, c).
%alter function funcA309(a:int4, b:int4 -> c:int4) {}
%alter rule r308 B1(a, b, c) :- A1(a, b, c), funcA309(a, b, c).
%alter function funcA310(a:int4, b:int4 -> c:int4) {}
%alter rule r309 B1(a, b, c) :- A1(a, b, c), funcA310(a, b, c).
%alter function funcA311(a:int4, b:int4 -> c:int4) {}
%alter rule r310 B1(a, b, c) :- A1(a, b, c), funcA311(a, b, c).
%alter function funcA312(a:int4, b:int4 -> c:int4) {}
%alter rule r311 B1(a, b, c) :- A1(a, b, c), funcA312(a, b, c).
%alter function funcA313(a:int4, b:int4 -> c:int4) {}
%alter rule r312 B1(a, b, c) :- A1(a, b, c), funcA313(a, b, c).
%alter function funcA314(a:int4, b:int4 -> c:int4) {}
%alter rule r313 B1(a, b, c) :- A1(a, b, c), funcA314(a, b, c).
%alter function funcA315(a:int4, b:int4 -> c:int4) {}
%alter rule r314 B1(a, b, c) :- A1(a, b, c), funcA315(a, b, c).
%alter function funcA316(a:int4, b:int4 -> c:int4) {}
%alter rule r315 B1(a, b, c) :- A1(a, b, c), funcA316(a, b, c).
%alter function funcA317(a:int4, b:int4 -> c:int4) {}
%alter rule r316 B1(a, b, c) :- A1(a, b, c), funcA317(a, b, c).
%alter function funcA318(a:int4, b:int4 -> c:int4) {}
%alter rule r317 B1(a, b, c) :- A1(a, b, c), funcA318(a, b, c).
%alter function funcA319(a:int4, b:int4 -> c:int4) {}
%alter rule r318 B1(a, b, c) :- A1(a, b, c), funcA319(a, b, c).
%alter function funcA320(a:int4, b:int4 -> c:int4) {}
%alter rule r319 B1(a, b, c) :- A1(a, b, c), funcA320(a, b, c).
%alter function funcA321(a:int4, b:int4 -> c:int4) {}
%alter rule r320 B1(a, b, c) :- A1(a, b, c), funcA321(a, b, c).
%alter function funcA322(a:int4, b:int4 -> c:int4) {}
%alter rule r321 B1(a, b, c) :- A1(a, b, c), funcA322(a, b, c).
%alter function funcA323(a:int4, b:int4 -> c:int4) {}
%alter rule r322 B1(a, b, c) :- A1(a, b, c), funcA323(a, b, c).
%alter function funcA324(a:int4, b:int4 -> c:int4) {}
%alter rule r323 B1(a, b, c) :- A1(a, b, c), funcA324(a, b, c).
%alter function funcA325(a:int4, b:int4 -> c:int4) {}
%alter rule r324 B1(a, b, c) :- A1(a, b, c), funcA325(a, b, c).
%alter function funcA326(a:int4, b:int4 -> c:int4) {}
%alter rule r325 B1(a, b, c) :- A1(a, b, c), funcA326(a, b, c).
%alter function funcA327(a:int4, b:int4 -> c:int4) {}
%alter rule r326 B1(a, b, c) :- A1(a, b, c), funcA327(a, b, c).
%alter function funcA328(a:int4, b:int4 -> c:int4) {}
%alter rule r327 B1(a, b, c) :- A1(a, b, c), funcA328(a, b, c).
%alter function funcA329(a:int4, b:int4 -> c:int4) {}
%alter rule r328 B1(a, b, c) :- A1(a, b, c), funcA329(a, b, c).
%alter function funcA330(a:int4, b:int4 -> c:int4) {}
%alter rule r329 B1(a, b, c) :- A1(a, b, c), funcA330(a, b, c).
%alter function funcA331(a:int4, b:int4 -> c:int4) {}
%alter rule r330 B1(a, b, c) :- A1(a, b, c), funcA331(a, b, c).
%alter function funcA332(a:int4, b:int4 -> c:int4) {}
%alter rule r331 B1(a, b, c) :- A1(a, b, c), funcA332(a, b, c).
%alter function funcA333(a:int4, b:int4 -> c:int4) {}
%alter rule r332 B1(a, b, c) :- A1(a, b, c), funcA333(a, b, c).
%alter function funcA334(a:int4, b:int4 -> c:int4) {}
%alter rule r333 B1(a, b, c) :- A1(a, b, c), funcA334(a, b, c).
%alter function funcA335(a:int4, b:int4 -> c:int4) {}
%alter rule r334 B1(a, b, c) :- A1(a, b, c), funcA335(a, b, c).
%alter function funcA336(a:int4, b:int4 -> c:int4) {}
%alter rule r335 B1(a, b, c) :- A1(a, b, c), funcA336(a, b, c).
%alter function funcA337(a:int4, b:int4 -> c:int4) {}
%alter rule r336 B1(a, b, c) :- A1(a, b, c), funcA337(a, b, c).
%alter function funcA338(a:int4, b:int4 -> c:int4) {}
%alter rule r337 B1(a, b, c) :- A1(a, b, c), funcA338(a, b, c).
%alter function funcA339(a:int4, b:int4 -> c:int4) {}
%alter rule r338 B1(a, b, c) :- A1(a, b, c), funcA339(a, b, c).
%alter function funcA340(a:int4, b:int4 -> c:int4) {}
%alter rule r339 B1(a, b, c) :- A1(a, b, c), funcA340(a, b, c).
%alter function funcA341(a:int4, b:int4 -> c:int4) {}
%alter rule r340 B1(a, b, c) :- A1(a, b, c), funcA341(a, b, c).
%alter function funcA342(a:int4, b:int4 -> c:int4) {}
%alter rule r341 B1(a, b, c) :- A1(a, b, c), funcA342(a, b, c).
%alter function funcA343(a:int4, b:int4 -> c:int4) {}
%alter rule r342 B1(a, b, c) :- A1(a, b, c), funcA343(a, b, c).
%alter function funcA344(a:int4, b:int4 -> c:int4) {}
%alter rule r343 B1(a, b, c) :- A1(a, b, c), funcA344(a, b, c).
%alter function funcA345(a:int4, b:int4 -> c:int4) {}
%alter rule r344 B1(a, b, c) :- A1(a, b, c), funcA345(a, b, c).
%alter function funcA346(a:int4, b:int4 -> c:int4) {}
%alter rule r345 B1(a, b, c) :- A1(a, b, c), funcA346(a, b, c).
%alter function funcA347(a:int4, b:int4 -> c:int4) {}
%alter rule r346 B1(a, b, c) :- A1(a, b, c), funcA347(a, b, c).
%alter function funcA348(a:int4, b:int4 -> c:int4) {}
%alter rule r347 B1(a, b, c) :- A1(a, b, c), funcA348(a, b, c).
%alter function funcA349(a:int4, b:int4 -> c:int4) {}
%alter rule r348 B1(a, b, c) :- A1(a, b, c), funcA349(a, b, c).
%alter function funcA350(a:int4, b:int4 -> c:int4) {}
%alter rule r349 B1(a, b, c) :- A1(a, b, c), funcA350(a, b, c).
%alter function funcA351(a:int4, b:int4 -> c:int4) {}
%alter rule r350 B1(a, b, c) :- A1(a, b, c), funcA351(a, b, c).
%alter function funcA352(a:int4, b:int4 -> c:int4) {}
%alter rule r351 B1(a, b, c) :- A1(a, b, c), funcA352(a, b, c).
%alter function funcA353(a:int4, b:int4 -> c:int4) {}
%alter rule r352 B1(a, b, c) :- A1(a, b, c), funcA353(a, b, c).
%alter function funcA354(a:int4, b:int4 -> c:int4) {}
%alter rule r353 B1(a, b, c) :- A1(a, b, c), funcA354(a, b, c).
%alter function funcA355(a:int4, b:int4 -> c:int4) {}
%alter rule r354 B1(a, b, c) :- A1(a, b, c), funcA355(a, b, c).
%alter function funcA356(a:int4, b:int4 -> c:int4) {}
%alter rule r355 B1(a, b, c) :- A1(a, b, c), funcA356(a, b, c).
%alter function funcA357(a:int4, b:int4 -> c:int4) {}
%alter rule r356 B1(a, b, c) :- A1(a, b, c), funcA357(a, b, c).
%alter function funcA358(a:int4, b:int4 -> c:int4) {}
%alter rule r357 B1(a, b, c) :- A1(a, b, c), funcA358(a, b, c).
%alter function funcA359(a:int4, b:int4 -> c:int4) {}
%alter rule r358 B1(a, b, c) :- A1(a, b, c), funcA359(a, b, c).
%alter function funcA360(a:int4, b:int4 -> c:int4) {}
%alter rule r359 B1(a, b, c) :- A1(a, b, c), funcA360(a, b, c).
%alter function funcA361(a:int4, b:int4 -> c:int4) {}
%alter rule r360 B1(a, b, c) :- A1(a, b, c), funcA361(a, b, c).
%alter function funcA362(a:int4, b:int4 -> c:int4) {}
%alter rule r361 B1(a, b, c) :- A1(a, b, c), funcA362(a, b, c).
%alter function funcA363(a:int4, b:int4 -> c:int4) {}
%alter rule r362 B1(a, b, c) :- A1(a, b, c), funcA363(a, b, c).
%alter function funcA364(a:int4, b:int4 -> c:int4) {}
%alter rule r363 B1(a, b, c) :- A1(a, b, c), funcA364(a, b, c).
%alter function funcA365(a:int4, b:int4 -> c:int4) {}
%alter rule r364 B1(a, b, c) :- A1(a, b, c), funcA365(a, b, c).
%alter function funcA366(a:int4, b:int4 -> c:int4) {}
%alter rule r365 B1(a, b, c) :- A1(a, b, c), funcA366(a, b, c).
%alter function funcA367(a:int4, b:int4 -> c:int4) {}
%alter rule r366 B1(a, b, c) :- A1(a, b, c), funcA367(a, b, c).
%alter function funcA368(a:int4, b:int4 -> c:int4) {}
%alter rule r367 B1(a, b, c) :- A1(a, b, c), funcA368(a, b, c).
%alter function funcA369(a:int4, b:int4 -> c:int4) {}
%alter rule r368 B1(a, b, c) :- A1(a, b, c), funcA369(a, b, c).
%alter function funcA370(a:int4, b:int4 -> c:int4) {}
%alter rule r369 B1(a, b, c) :- A1(a, b, c), funcA370(a, b, c).
%alter function funcA371(a:int4, b:int4 -> c:int4) {}
%alter rule r370 B1(a, b, c) :- A1(a, b, c), funcA371(a, b, c).
%alter function funcA372(a:int4, b:int4 -> c:int4) {}
%alter rule r371 B1(a, b, c) :- A1(a, b, c), funcA372(a, b, c).
%alter function funcA373(a:int4, b:int4 -> c:int4) {}
%alter rule r372 B1(a, b, c) :- A1(a, b, c), funcA373(a, b, c).
%alter function funcA374(a:int4, b:int4 -> c:int4) {}
%alter rule r373 B1(a, b, c) :- A1(a, b, c), funcA374(a, b, c).
%alter function funcA375(a:int4, b:int4 -> c:int4) {}
%alter rule r374 B1(a, b, c) :- A1(a, b, c), funcA375(a, b, c).
%alter function funcA376(a:int4, b:int4 -> c:int4) {}
%alter rule r375 B1(a, b, c) :- A1(a, b, c), funcA376(a, b, c).
%alter function funcA377(a:int4, b:int4 -> c:int4) {}
%alter rule r376 B1(a, b, c) :- A1(a, b, c), funcA377(a, b, c).
%alter function funcA378(a:int4, b:int4 -> c:int4) {}
%alter rule r377 B1(a, b, c) :- A1(a, b, c), funcA378(a, b, c).
%alter function funcA379(a:int4, b:int4 -> c:int4) {}
%alter rule r378 B1(a, b, c) :- A1(a, b, c), funcA379(a, b, c).
%alter function funcA380(a:int4, b:int4 -> c:int4) {}
%alter rule r379 B1(a, b, c) :- A1(a, b, c), funcA380(a, b, c).
%alter function funcA381(a:int4, b:int4 -> c:int4) {}
%alter rule r380 B1(a, b, c) :- A1(a, b, c), funcA381(a, b, c).
%alter function funcA382(a:int4, b:int4 -> c:int4) {}
%alter rule r381 B1(a, b, c) :- A1(a, b, c), funcA382(a, b, c).
%alter function funcA383(a:int4, b:int4 -> c:int4) {}
%alter rule r382 B1(a, b, c) :- A1(a, b, c), funcA383(a, b, c).
%alter function funcA384(a:int4, b:int4 -> c:int4) {}
%alter rule r383 B1(a, b, c) :- A1(a, b, c), funcA384(a, b, c).
%alter function funcA385(a:int4, b:int4 -> c:int4) {}
%alter rule r384 B1(a, b, c) :- A1(a, b, c), funcA385(a, b, c).
%alter function funcA386(a:int4, b:int4 -> c:int4) {}
%alter rule r385 B1(a, b, c) :- A1(a, b, c), funcA386(a, b, c).
%alter function funcA387(a:int4, b:int4 -> c:int4) {}
%alter rule r386 B1(a, b, c) :- A1(a, b, c), funcA387(a, b, c).
%alter function funcA388(a:int4, b:int4 -> c:int4) {}
%alter rule r387 B1(a, b, c) :- A1(a, b, c), funcA388(a, b, c).
%alter function funcA389(a:int4, b:int4 -> c:int4) {}
%alter rule r388 B1(a, b, c) :- A1(a, b, c), funcA389(a, b, c).
%alter function funcA390(a:int4, b:int4 -> c:int4) {}
%alter rule r389 B1(a, b, c) :- A1(a, b, c), funcA390(a, b, c).
%alter function funcA391(a:int4, b:int4 -> c:int4) {}
%alter rule r390 B1(a, b, c) :- A1(a, b, c), funcA391(a, b, c).
%alter function funcA392(a:int4, b:int4 -> c:int4) {}
%alter rule r391 B1(a, b, c) :- A1(a, b, c), funcA392(a, b, c).
%alter function funcA393(a:int4, b:int4 -> c:int4) {}
%alter rule r392 B1(a, b, c) :- A1(a, b, c), funcA393(a, b, c).
%alter function funcA394(a:int4, b:int4 -> c:int4) {}
%alter rule r393 B1(a, b, c) :- A1(a, b, c), funcA394(a, b, c).
%alter function funcA395(a:int4, b:int4 -> c:int4) {}
%alter rule r394 B1(a, b, c) :- A1(a, b, c), funcA395(a, b, c).
%alter function funcA396(a:int4, b:int4 -> c:int4) {}
%alter rule r395 B1(a, b, c) :- A1(a, b, c), funcA396(a, b, c).
%alter function funcA397(a:int4, b:int4 -> c:int4) {}
%alter rule r396 B1(a, b, c) :- A1(a, b, c), funcA397(a, b, c).
%alter function funcA398(a:int4, b:int4 -> c:int4) {}
%alter rule r397 B1(a, b, c) :- A1(a, b, c), funcA398(a, b, c).
%alter function funcA399(a:int4, b:int4 -> c:int4) {}
%alter rule r398 B1(a, b, c) :- A1(a, b, c), funcA399(a, b, c).
%alter function funcA400(a:int4, b:int4 -> c:int4) {}
%alter rule r399 B1(a, b, c) :- A1(a, b, c), funcA400(a, b, c).
%alter function funcA401(a:int4, b:int4 -> c:int4) {}
%alter rule r400 B1(a, b, c) :- A1(a, b, c), funcA401(a, b, c).
%alter function funcA402(a:int4, b:int4 -> c:int4) {}
%alter rule r401 B1(a, b, c) :- A1(a, b, c), funcA402(a, b, c).
%alter function funcA403(a:int4, b:int4 -> c:int4) {}
%alter rule r402 B1(a, b, c) :- A1(a, b, c), funcA403(a, b, c).
%alter function funcA404(a:int4, b:int4 -> c:int4) {}
%alter rule r403 B1(a, b, c) :- A1(a, b, c), funcA404(a, b, c).
%alter function funcA405(a:int4, b:int4 -> c:int4) {}
%alter rule r404 B1(a, b, c) :- A1(a, b, c), funcA405(a, b, c).
%alter function funcA406(a:int4, b:int4 -> c:int4) {}
%alter rule r405 B1(a, b, c) :- A1(a, b, c), funcA406(a, b, c).
%alter function funcA407(a:int4, b:int4 -> c:int4) {}
%alter rule r406 B1(a, b, c) :- A1(a, b, c), funcA407(a, b, c).
%alter function funcA408(a:int4, b:int4 -> c:int4) {}
%alter rule r407 B1(a, b, c) :- A1(a, b, c), funcA408(a, b, c).
%alter function funcA409(a:int4, b:int4 -> c:int4) {}
%alter rule r408 B1(a, b, c) :- A1(a, b, c), funcA409(a, b, c).
%alter function funcA410(a:int4, b:int4 -> c:int4) {}
%alter rule r409 B1(a, b, c) :- A1(a, b, c), funcA410(a, b, c).
%alter function funcA411(a:int4, b:int4 -> c:int4) {}
%alter rule r410 B1(a, b, c) :- A1(a, b, c), funcA411(a, b, c).
%alter function funcA412(a:int4, b:int4 -> c:int4) {}
%alter rule r411 B1(a, b, c) :- A1(a, b, c), funcA412(a, b, c).
%alter function funcA413(a:int4, b:int4 -> c:int4) {}
%alter rule r412 B1(a, b, c) :- A1(a, b, c), funcA413(a, b, c).
%alter function funcA414(a:int4, b:int4 -> c:int4) {}
%alter rule r413 B1(a, b, c) :- A1(a, b, c), funcA414(a, b, c).
%alter function funcA415(a:int4, b:int4 -> c:int4) {}
%alter rule r414 B1(a, b, c) :- A1(a, b, c), funcA415(a, b, c).
%alter function funcA416(a:int4, b:int4 -> c:int4) {}
%alter rule r415 B1(a, b, c) :- A1(a, b, c), funcA416(a, b, c).
%alter function funcA417(a:int4, b:int4 -> c:int4) {}
%alter rule r416 B1(a, b, c) :- A1(a, b, c), funcA417(a, b, c).
%alter function funcA418(a:int4, b:int4 -> c:int4) {}
%alter rule r417 B1(a, b, c) :- A1(a, b, c), funcA418(a, b, c).
%alter function funcA419(a:int4, b:int4 -> c:int4) {}
%alter rule r418 B1(a, b, c) :- A1(a, b, c), funcA419(a, b, c).
%alter function funcA420(a:int4, b:int4 -> c:int4) {}
%alter rule r419 B1(a, b, c) :- A1(a, b, c), funcA420(a, b, c).
%alter function funcA421(a:int4, b:int4 -> c:int4) {}
%alter rule r420 B1(a, b, c) :- A1(a, b, c), funcA421(a, b, c).
%alter function funcA422(a:int4, b:int4 -> c:int4) {}
%alter rule r421 B1(a, b, c) :- A1(a, b, c), funcA422(a, b, c).
%alter function funcA423(a:int4, b:int4 -> c:int4) {}
%alter rule r422 B1(a, b, c) :- A1(a, b, c), funcA423(a, b, c).
%alter function funcA424(a:int4, b:int4 -> c:int4) {}
%alter rule r423 B1(a, b, c) :- A1(a, b, c), funcA424(a, b, c).
%alter function funcA425(a:int4, b:int4 -> c:int4) {}
%alter rule r424 B1(a, b, c) :- A1(a, b, c), funcA425(a, b, c).
%alter function funcA426(a:int4, b:int4 -> c:int4) {}
%alter rule r425 B1(a, b, c) :- A1(a, b, c), funcA426(a, b, c).
%alter function funcA427(a:int4, b:int4 -> c:int4) {}
%alter rule r426 B1(a, b, c) :- A1(a, b, c), funcA427(a, b, c).
%alter function funcA428(a:int4, b:int4 -> c:int4) {}
%alter rule r427 B1(a, b, c) :- A1(a, b, c), funcA428(a, b, c).
%alter function funcA429(a:int4, b:int4 -> c:int4) {}
%alter rule r428 B1(a, b, c) :- A1(a, b, c), funcA429(a, b, c).
%alter function funcA430(a:int4, b:int4 -> c:int4) {}
%alter rule r429 B1(a, b, c) :- A1(a, b, c), funcA430(a, b, c).
%alter function funcA431(a:int4, b:int4 -> c:int4) {}
%alter rule r430 B1(a, b, c) :- A1(a, b, c), funcA431(a, b, c).
%alter function funcA432(a:int4, b:int4 -> c:int4) {}
%alter rule r431 B1(a, b, c) :- A1(a, b, c), funcA432(a, b, c).
%alter function funcA433(a:int4, b:int4 -> c:int4) {}
%alter rule r432 B1(a, b, c) :- A1(a, b, c), funcA433(a, b, c).
%alter function funcA434(a:int4, b:int4 -> c:int4) {}
%alter rule r433 B1(a, b, c) :- A1(a, b, c), funcA434(a, b, c).
%alter function funcA435(a:int4, b:int4 -> c:int4) {}
%alter rule r434 B1(a, b, c) :- A1(a, b, c), funcA435(a, b, c).
%alter function funcA436(a:int4, b:int4 -> c:int4) {}
%alter rule r435 B1(a, b, c) :- A1(a, b, c), funcA436(a, b, c).
%alter function funcA437(a:int4, b:int4 -> c:int4) {}
%alter rule r436 B1(a, b, c) :- A1(a, b, c), funcA437(a, b, c).
%alter function funcA438(a:int4, b:int4 -> c:int4) {}
%alter rule r437 B1(a, b, c) :- A1(a, b, c), funcA438(a, b, c).
%alter function funcA439(a:int4, b:int4 -> c:int4) {}
%alter rule r438 B1(a, b, c) :- A1(a, b, c), funcA439(a, b, c).
%alter function funcA440(a:int4, b:int4 -> c:int4) {}
%alter rule r439 B1(a, b, c) :- A1(a, b, c), funcA440(a, b, c).
%alter function funcA441(a:int4, b:int4 -> c:int4) {}
%alter rule r440 B1(a, b, c) :- A1(a, b, c), funcA441(a, b, c).
%alter function funcA442(a:int4, b:int4 -> c:int4) {}
%alter rule r441 B1(a, b, c) :- A1(a, b, c), funcA442(a, b, c).
%alter function funcA443(a:int4, b:int4 -> c:int4) {}
%alter rule r442 B1(a, b, c) :- A1(a, b, c), funcA443(a, b, c).
%alter function funcA444(a:int4, b:int4 -> c:int4) {}
%alter rule r443 B1(a, b, c) :- A1(a, b, c), funcA444(a, b, c).
%alter function funcA445(a:int4, b:int4 -> c:int4) {}
%alter rule r444 B1(a, b, c) :- A1(a, b, c), funcA445(a, b, c).
%alter function funcA446(a:int4, b:int4 -> c:int4) {}
%alter rule r445 B1(a, b, c) :- A1(a, b, c), funcA446(a, b, c).
%alter function funcA447(a:int4, b:int4 -> c:int4) {}
%alter rule r446 B1(a, b, c) :- A1(a, b, c), funcA447(a, b, c).
%alter function funcA448(a:int4, b:int4 -> c:int4) {}
%alter rule r447 B1(a, b, c) :- A1(a, b, c), funcA448(a, b, c).
%alter function funcA449(a:int4, b:int4 -> c:int4) {}
%alter rule r448 B1(a, b, c) :- A1(a, b, c), funcA449(a, b, c).
%alter function funcA450(a:int4, b:int4 -> c:int4) {}
%alter rule r449 B1(a, b, c) :- A1(a, b, c), funcA450(a, b, c).
%alter function funcA451(a:int4, b:int4 -> c:int4) {}
%alter rule r450 B1(a, b, c) :- A1(a, b, c), funcA451(a, b, c).
%alter function funcA452(a:int4, b:int4 -> c:int4) {}
%alter rule r451 B1(a, b, c) :- A1(a, b, c), funcA452(a, b, c).
%alter function funcA453(a:int4, b:int4 -> c:int4) {}
%alter rule r452 B1(a, b, c) :- A1(a, b, c), funcA453(a, b, c).
%alter function funcA454(a:int4, b:int4 -> c:int4) {}
%alter rule r453 B1(a, b, c) :- A1(a, b, c), funcA454(a, b, c).
%alter function funcA455(a:int4, b:int4 -> c:int4) {}
%alter rule r454 B1(a, b, c) :- A1(a, b, c), funcA455(a, b, c).
%alter function funcA456(a:int4, b:int4 -> c:int4) {}
%alter rule r455 B1(a, b, c) :- A1(a, b, c), funcA456(a, b, c).
%alter function funcA457(a:int4, b:int4 -> c:int4) {}
%alter rule r456 B1(a, b, c) :- A1(a, b, c), funcA457(a, b, c).
%alter function funcA458(a:int4, b:int4 -> c:int4) {}
%alter rule r457 B1(a, b, c) :- A1(a, b, c), funcA458(a, b, c).
%alter function funcA459(a:int4, b:int4 -> c:int4) {}
%alter rule r458 B1(a, b, c) :- A1(a, b, c), funcA459(a, b, c).
%alter function funcA460(a:int4, b:int4 -> c:int4) {}
%alter rule r459 B1(a, b, c) :- A1(a, b, c), funcA460(a, b, c).
%alter function funcA461(a:int4, b:int4 -> c:int4) {}
%alter rule r460 B1(a, b, c) :- A1(a, b, c), funcA461(a, b, c).
%alter function funcA462(a:int4, b:int4 -> c:int4) {}
%alter rule r461 B1(a, b, c) :- A1(a, b, c), funcA462(a, b, c).
%alter function funcA463(a:int4, b:int4 -> c:int4) {}
%alter rule r462 B1(a, b, c) :- A1(a, b, c), funcA463(a, b, c).
%alter function funcA464(a:int4, b:int4 -> c:int4) {}
%alter rule r463 B1(a, b, c) :- A1(a, b, c), funcA464(a, b, c).
%alter function funcA465(a:int4, b:int4 -> c:int4) {}
%alter rule r464 B1(a, b, c) :- A1(a, b, c), funcA465(a, b, c).
%alter function funcA466(a:int4, b:int4 -> c:int4) {}
%alter rule r465 B1(a, b, c) :- A1(a, b, c), funcA466(a, b, c).
%alter function funcA467(a:int4, b:int4 -> c:int4) {}
%alter rule r466 B1(a, b, c) :- A1(a, b, c), funcA467(a, b, c).
%alter function funcA468(a:int4, b:int4 -> c:int4) {}
%alter rule r467 B1(a, b, c) :- A1(a, b, c), funcA468(a, b, c).
%alter function funcA469(a:int4, b:int4 -> c:int4) {}
%alter rule r468 B1(a, b, c) :- A1(a, b, c), funcA469(a, b, c).
%alter function funcA470(a:int4, b:int4 -> c:int4) {}
%alter rule r469 B1(a, b, c) :- A1(a, b, c), funcA470(a, b, c).
%alter function funcA471(a:int4, b:int4 -> c:int4) {}
%alter rule r470 B1(a, b, c) :- A1(a, b, c), funcA471(a, b, c).
%alter function funcA472(a:int4, b:int4 -> c:int4) {}
%alter rule r471 B1(a, b, c) :- A1(a, b, c), funcA472(a, b, c).
%alter function funcA473(a:int4, b:int4 -> c:int4) {}
%alter rule r472 B1(a, b, c) :- A1(a, b, c), funcA473(a, b, c).
%alter function funcA474(a:int4, b:int4 -> c:int4) {}
%alter rule r473 B1(a, b, c) :- A1(a, b, c), funcA474(a, b, c).
%alter function funcA475(a:int4, b:int4 -> c:int4) {}
%alter rule r474 B1(a, b, c) :- A1(a, b, c), funcA475(a, b, c).
%alter function funcA476(a:int4, b:int4 -> c:int4) {}
%alter rule r475 B1(a, b, c) :- A1(a, b, c), funcA476(a, b, c).
%alter function funcA477(a:int4, b:int4 -> c:int4) {}
%alter rule r476 B1(a, b, c) :- A1(a, b, c), funcA477(a, b, c).
%alter function funcA478(a:int4, b:int4 -> c:int4) {}
%alter rule r477 B1(a, b, c) :- A1(a, b, c), funcA478(a, b, c).
%alter function funcA479(a:int4, b:int4 -> c:int4) {}
%alter rule r478 B1(a, b, c) :- A1(a, b, c), funcA479(a, b, c).
%alter function funcA480(a:int4, b:int4 -> c:int4) {}
%alter rule r479 B1(a, b, c) :- A1(a, b, c), funcA480(a, b, c).
%alter function funcA481(a:int4, b:int4 -> c:int4) {}
%alter rule r480 B1(a, b, c) :- A1(a, b, c), funcA481(a, b, c).
%alter function funcA482(a:int4, b:int4 -> c:int4) {}
%alter rule r481 B1(a, b, c) :- A1(a, b, c), funcA482(a, b, c).
%alter function funcA483(a:int4, b:int4 -> c:int4) {}
%alter rule r482 B1(a, b, c) :- A1(a, b, c), funcA483(a, b, c).
%alter function funcA484(a:int4, b:int4 -> c:int4) {}
%alter rule r483 B1(a, b, c) :- A1(a, b, c), funcA484(a, b, c).
%alter function funcA485(a:int4, b:int4 -> c:int4) {}
%alter rule r484 B1(a, b, c) :- A1(a, b, c), funcA485(a, b, c).
%alter function funcA486(a:int4, b:int4 -> c:int4) {}
%alter rule r485 B1(a, b, c) :- A1(a, b, c), funcA486(a, b, c).
%alter function funcA487(a:int4, b:int4 -> c:int4) {}
%alter rule r486 B1(a, b, c) :- A1(a, b, c), funcA487(a, b, c).
%alter function funcA488(a:int4, b:int4 -> c:int4) {}
%alter rule r487 B1(a, b, c) :- A1(a, b, c), funcA488(a, b, c).
%alter function funcA489(a:int4, b:int4 -> c:int4) {}
%alter rule r488 B1(a, b, c) :- A1(a, b, c), funcA489(a, b, c).
%alter function funcA490(a:int4, b:int4 -> c:int4) {}
%alter rule r489 B1(a, b, c) :- A1(a, b, c), funcA490(a, b, c).
%alter function funcA491(a:int4, b:int4 -> c:int4) {}
%alter rule r490 B1(a, b, c) :- A1(a, b, c), funcA491(a, b, c).
%alter function funcA492(a:int4, b:int4 -> c:int4) {}
%alter rule r491 B1(a, b, c) :- A1(a, b, c), funcA492(a, b, c).
%alter function funcA493(a:int4, b:int4 -> c:int4) {}
%alter rule r492 B1(a, b, c) :- A1(a, b, c), funcA493(a, b, c).
%alter function funcA494(a:int4, b:int4 -> c:int4) {}
%alter rule r493 B1(a, b, c) :- A1(a, b, c), funcA494(a, b, c).
%alter function funcA495(a:int4, b:int4 -> c:int4) {}
%alter rule r494 B1(a, b, c) :- A1(a, b, c), funcA495(a, b, c).
%alter function funcA496(a:int4, b:int4 -> c:int4) {}
%alter rule r495 B1(a, b, c) :- A1(a, b, c), funcA496(a, b, c).
%alter function funcA497(a:int4, b:int4 -> c:int4) {}
%alter rule r496 B1(a, b, c) :- A1(a, b, c), funcA497(a, b, c).
%alter function funcA498(a:int4, b:int4 -> c:int4) {}
%alter rule r497 B1(a, b, c) :- A1(a, b, c), funcA498(a, b, c).
%alter function funcA499(a:int4, b:int4 -> c:int4) {}
%alter rule r498 B1(a, b, c) :- A1(a, b, c), funcA499(a, b, c).
%alter function funcA500(a:int4, b:int4 -> c:int4) {}
%alter rule r499 B1(a, b, c) :- A1(a, b, c), funcA500(a, b, c).
%alter function funcA501(a:int4, b:int4 -> c:int4) {}
%alter rule r500 B1(a, b, c) :- A1(a, b, c), funcA501(a, b, c).
%alter function funcA502(a:int4, b:int4 -> c:int4) {}
%alter rule r501 B1(a, b, c) :- A1(a, b, c), funcA502(a, b, c).
%alter function funcA503(a:int4, b:int4 -> c:int4) {}
%alter rule r502 B1(a, b, c) :- A1(a, b, c), funcA503(a, b, c).
%alter function funcA504(a:int4, b:int4 -> c:int4) {}
%alter rule r503 B1(a, b, c) :- A1(a, b, c), funcA504(a, b, c).
%alter function funcA505(a:int4, b:int4 -> c:int4) {}
%alter rule r504 B1(a, b, c) :- A1(a, b, c), funcA505(a, b, c).
%alter function funcA506(a:int4, b:int4 -> c:int4) {}
%alter rule r505 B1(a, b, c) :- A1(a, b, c), funcA506(a, b, c).
%alter function funcA507(a:int4, b:int4 -> c:int4) {}
%alter rule r506 B1(a, b, c) :- A1(a, b, c), funcA507(a, b, c).
%alter function funcA508(a:int4, b:int4 -> c:int4) {}
%alter rule r507 B1(a, b, c) :- A1(a, b, c), funcA508(a, b, c).
%alter function funcA509(a:int4, b:int4 -> c:int4) {}
%alter rule r508 B1(a, b, c) :- A1(a, b, c), funcA509(a, b, c).
%alter function funcA510(a:int4, b:int4 -> c:int4) {}
%alter rule r509 B1(a, b, c) :- A1(a, b, c), funcA510(a, b, c).
%alter function funcA511(a:int4, b:int4 -> c:int4) {}
%alter rule r510 B1(a, b, c) :- A1(a, b, c), funcA511(a, b, c).
%alter function funcA512(a:int4, b:int4 -> c:int4) {}
%alter rule r511 B1(a, b, c) :- A1(a, b, c), funcA512(a, b, c).
%alter function funcA513(a:int4, b:int4 -> c:int4) {}
%alter rule r512 B1(a, b, c) :- A1(a, b, c), funcA513(a, b, c).
%alter function funcA514(a:int4, b:int4 -> c:int4) {}
%alter rule r513 B1(a, b, c) :- A1(a, b, c), funcA514(a, b, c).
%alter function funcA515(a:int4, b:int4 -> c:int4) {}
%alter rule r514 B1(a, b, c) :- A1(a, b, c), funcA515(a, b, c).
%alter function funcA516(a:int4, b:int4 -> c:int4) {}
%alter rule r515 B1(a, b, c) :- A1(a, b, c), funcA516(a, b, c).
%alter function funcA517(a:int4, b:int4 -> c:int4) {}
%alter rule r516 B1(a, b, c) :- A1(a, b, c), funcA517(a, b, c).
%alter function funcA518(a:int4, b:int4 -> c:int4) {}
%alter rule r517 B1(a, b, c) :- A1(a, b, c), funcA518(a, b, c).
%alter function funcA519(a:int4, b:int4 -> c:int4) {}
%alter rule r518 B1(a, b, c) :- A1(a, b, c), funcA519(a, b, c).
%alter function funcA520(a:int4, b:int4 -> c:int4) {}
%alter rule r519 B1(a, b, c) :- A1(a, b, c), funcA520(a, b, c).
%alter function funcA521(a:int4, b:int4 -> c:int4) {}
%alter rule r520 B1(a, b, c) :- A1(a, b, c), funcA521(a, b, c).
%alter function funcA522(a:int4, b:int4 -> c:int4) {}
%alter rule r521 B1(a, b, c) :- A1(a, b, c), funcA522(a, b, c).
%alter function funcA523(a:int4, b:int4 -> c:int4) {}
%alter rule r522 B1(a, b, c) :- A1(a, b, c), funcA523(a, b, c).
%alter function funcA524(a:int4, b:int4 -> c:int4) {}
%alter rule r523 B1(a, b, c) :- A1(a, b, c), funcA524(a, b, c).
%alter function funcA525(a:int4, b:int4 -> c:int4) {}
%alter rule r524 B1(a, b, c) :- A1(a, b, c), funcA525(a, b, c).
%alter function funcA526(a:int4, b:int4 -> c:int4) {}
%alter rule r525 B1(a, b, c) :- A1(a, b, c), funcA526(a, b, c).
%alter function funcA527(a:int4, b:int4 -> c:int4) {}
%alter rule r526 B1(a, b, c) :- A1(a, b, c), funcA527(a, b, c).
%alter function funcA528(a:int4, b:int4 -> c:int4) {}
%alter rule r527 B1(a, b, c) :- A1(a, b, c), funcA528(a, b, c).
%alter function funcA529(a:int4, b:int4 -> c:int4) {}
%alter rule r528 B1(a, b, c) :- A1(a, b, c), funcA529(a, b, c).
%alter function funcA530(a:int4, b:int4 -> c:int4) {}
%alter rule r529 B1(a, b, c) :- A1(a, b, c), funcA530(a, b, c).
%alter function funcA531(a:int4, b:int4 -> c:int4) {}
%alter rule r530 B1(a, b, c) :- A1(a, b, c), funcA531(a, b, c).
%alter function funcA532(a:int4, b:int4 -> c:int4) {}
%alter rule r531 B1(a, b, c) :- A1(a, b, c), funcA532(a, b, c).
%alter function funcA533(a:int4, b:int4 -> c:int4) {}
%alter rule r532 B1(a, b, c) :- A1(a, b, c), funcA533(a, b, c).
%alter function funcA534(a:int4, b:int4 -> c:int4) {}
%alter rule r533 B1(a, b, c) :- A1(a, b, c), funcA534(a, b, c).
%alter function funcA535(a:int4, b:int4 -> c:int4) {}
%alter rule r534 B1(a, b, c) :- A1(a, b, c), funcA535(a, b, c).
%alter function funcA536(a:int4, b:int4 -> c:int4) {}
%alter rule r535 B1(a, b, c) :- A1(a, b, c), funcA536(a, b, c).
%alter function funcA537(a:int4, b:int4 -> c:int4) {}
%alter rule r536 B1(a, b, c) :- A1(a, b, c), funcA537(a, b, c).
%alter function funcA538(a:int4, b:int4 -> c:int4) {}
%alter rule r537 B1(a, b, c) :- A1(a, b, c), funcA538(a, b, c).
%alter function funcA539(a:int4, b:int4 -> c:int4) {}
%alter rule r538 B1(a, b, c) :- A1(a, b, c), funcA539(a, b, c).
%alter function funcA540(a:int4, b:int4 -> c:int4) {}
%alter rule r539 B1(a, b, c) :- A1(a, b, c), funcA540(a, b, c).
%alter function funcA541(a:int4, b:int4 -> c:int4) {}
%alter rule r540 B1(a, b, c) :- A1(a, b, c), funcA541(a, b, c).
%alter function funcA542(a:int4, b:int4 -> c:int4) {}
%alter rule r541 B1(a, b, c) :- A1(a, b, c), funcA542(a, b, c).
%alter function funcA543(a:int4, b:int4 -> c:int4) {}
%alter rule r542 B1(a, b, c) :- A1(a, b, c), funcA543(a, b, c).
%alter function funcA544(a:int4, b:int4 -> c:int4) {}
%alter rule r543 B1(a, b, c) :- A1(a, b, c), funcA544(a, b, c).
%alter function funcA545(a:int4, b:int4 -> c:int4) {}
%alter rule r544 B1(a, b, c) :- A1(a, b, c), funcA545(a, b, c).
%alter function funcA546(a:int4, b:int4 -> c:int4) {}
%alter rule r545 B1(a, b, c) :- A1(a, b, c), funcA546(a, b, c).
%alter function funcA547(a:int4, b:int4 -> c:int4) {}
%alter rule r546 B1(a, b, c) :- A1(a, b, c), funcA547(a, b, c).
%alter function funcA548(a:int4, b:int4 -> c:int4) {}
%alter rule r547 B1(a, b, c) :- A1(a, b, c), funcA548(a, b, c).
%alter function funcA549(a:int4, b:int4 -> c:int4) {}
%alter rule r548 B1(a, b, c) :- A1(a, b, c), funcA549(a, b, c).
%alter function funcA550(a:int4, b:int4 -> c:int4) {}
%alter rule r549 B1(a, b, c) :- A1(a, b, c), funcA550(a, b, c).
%alter function funcA551(a:int4, b:int4 -> c:int4) {}
%alter rule r550 B1(a, b, c) :- A1(a, b, c), funcA551(a, b, c).
%alter function funcA552(a:int4, b:int4 -> c:int4) {}
%alter rule r551 B1(a, b, c) :- A1(a, b, c), funcA552(a, b, c).
%alter function funcA553(a:int4, b:int4 -> c:int4) {}
%alter rule r552 B1(a, b, c) :- A1(a, b, c), funcA553(a, b, c).
%alter function funcA554(a:int4, b:int4 -> c:int4) {}
%alter rule r553 B1(a, b, c) :- A1(a, b, c), funcA554(a, b, c).
%alter function funcA555(a:int4, b:int4 -> c:int4) {}
%alter rule r554 B1(a, b, c) :- A1(a, b, c), funcA555(a, b, c).
%alter function funcA556(a:int4, b:int4 -> c:int4) {}
%alter rule r555 B1(a, b, c) :- A1(a, b, c), funcA556(a, b, c).
%alter function funcA557(a:int4, b:int4 -> c:int4) {}
%alter rule r556 B1(a, b, c) :- A1(a, b, c), funcA557(a, b, c).
%alter function funcA558(a:int4, b:int4 -> c:int4) {}
%alter rule r557 B1(a, b, c) :- A1(a, b, c), funcA558(a, b, c).
%alter function funcA559(a:int4, b:int4 -> c:int4) {}
%alter rule r558 B1(a, b, c) :- A1(a, b, c), funcA559(a, b, c).
%alter function funcA560(a:int4, b:int4 -> c:int4) {}
%alter rule r559 B1(a, b, c) :- A1(a, b, c), funcA560(a, b, c).
%alter function funcA561(a:int4, b:int4 -> c:int4) {}
%alter rule r560 B1(a, b, c) :- A1(a, b, c), funcA561(a, b, c).
%alter function funcA562(a:int4, b:int4 -> c:int4) {}
%alter rule r561 B1(a, b, c) :- A1(a, b, c), funcA562(a, b, c).
%alter function funcA563(a:int4, b:int4 -> c:int4) {}
%alter rule r562 B1(a, b, c) :- A1(a, b, c), funcA563(a, b, c).
%alter function funcA564(a:int4, b:int4 -> c:int4) {}
%alter rule r563 B1(a, b, c) :- A1(a, b, c), funcA564(a, b, c).
%alter function funcA565(a:int4, b:int4 -> c:int4) {}
%alter rule r564 B1(a, b, c) :- A1(a, b, c), funcA565(a, b, c).
%alter function funcA566(a:int4, b:int4 -> c:int4) {}
%alter rule r565 B1(a, b, c) :- A1(a, b, c), funcA566(a, b, c).
%alter function funcA567(a:int4, b:int4 -> c:int4) {}
%alter rule r566 B1(a, b, c) :- A1(a, b, c), funcA567(a, b, c).
%alter function funcA568(a:int4, b:int4 -> c:int4) {}
%alter rule r567 B1(a, b, c) :- A1(a, b, c), funcA568(a, b, c).
%alter function funcA569(a:int4, b:int4 -> c:int4) {}
%alter rule r568 B1(a, b, c) :- A1(a, b, c), funcA569(a, b, c).
%alter function funcA570(a:int4, b:int4 -> c:int4) {}
%alter rule r569 B1(a, b, c) :- A1(a, b, c), funcA570(a, b, c).
%alter function funcA571(a:int4, b:int4 -> c:int4) {}
%alter rule r570 B1(a, b, c) :- A1(a, b, c), funcA571(a, b, c).
%alter function funcA572(a:int4, b:int4 -> c:int4) {}
%alter rule r571 B1(a, b, c) :- A1(a, b, c), funcA572(a, b, c).
%alter function funcA573(a:int4, b:int4 -> c:int4) {}
%alter rule r572 B1(a, b, c) :- A1(a, b, c), funcA573(a, b, c).
%alter function funcA574(a:int4, b:int4 -> c:int4) {}
%alter rule r573 B1(a, b, c) :- A1(a, b, c), funcA574(a, b, c).
%alter function funcA575(a:int4, b:int4 -> c:int4) {}
%alter rule r574 B1(a, b, c) :- A1(a, b, c), funcA575(a, b, c).
%alter function funcA576(a:int4, b:int4 -> c:int4) {}
%alter rule r575 B1(a, b, c) :- A1(a, b, c), funcA576(a, b, c).
%alter function funcA577(a:int4, b:int4 -> c:int4) {}
%alter rule r576 B1(a, b, c) :- A1(a, b, c), funcA577(a, b, c).
%alter function funcA578(a:int4, b:int4 -> c:int4) {}
%alter rule r577 B1(a, b, c) :- A1(a, b, c), funcA578(a, b, c).
%alter function funcA579(a:int4, b:int4 -> c:int4) {}
%alter rule r578 B1(a, b, c) :- A1(a, b, c), funcA579(a, b, c).
%alter function funcA580(a:int4, b:int4 -> c:int4) {}
%alter rule r579 B1(a, b, c) :- A1(a, b, c), funcA580(a, b, c).
%alter function funcA581(a:int4, b:int4 -> c:int4) {}
%alter rule r580 B1(a, b, c) :- A1(a, b, c), funcA581(a, b, c).
%alter function funcA582(a:int4, b:int4 -> c:int4) {}
%alter rule r581 B1(a, b, c) :- A1(a, b, c), funcA582(a, b, c).
%alter function funcA583(a:int4, b:int4 -> c:int4) {}
%alter rule r582 B1(a, b, c) :- A1(a, b, c), funcA583(a, b, c).
%alter function funcA584(a:int4, b:int4 -> c:int4) {}
%alter rule r583 B1(a, b, c) :- A1(a, b, c), funcA584(a, b, c).
%alter function funcA585(a:int4, b:int4 -> c:int4) {}
%alter rule r584 B1(a, b, c) :- A1(a, b, c), funcA585(a, b, c).
%alter function funcA586(a:int4, b:int4 -> c:int4) {}
%alter rule r585 B1(a, b, c) :- A1(a, b, c), funcA586(a, b, c).
%alter function funcA587(a:int4, b:int4 -> c:int4) {}
%alter rule r586 B1(a, b, c) :- A1(a, b, c), funcA587(a, b, c).
%alter function funcA588(a:int4, b:int4 -> c:int4) {}
%alter rule r587 B1(a, b, c) :- A1(a, b, c), funcA588(a, b, c).
%alter function funcA589(a:int4, b:int4 -> c:int4) {}
%alter rule r588 B1(a, b, c) :- A1(a, b, c), funcA589(a, b, c).
%alter function funcA590(a:int4, b:int4 -> c:int4) {}
%alter rule r589 B1(a, b, c) :- A1(a, b, c), funcA590(a, b, c).
%alter function funcA591(a:int4, b:int4 -> c:int4) {}
%alter rule r590 B1(a, b, c) :- A1(a, b, c), funcA591(a, b, c).
%alter function funcA592(a:int4, b:int4 -> c:int4) {}
%alter rule r591 B1(a, b, c) :- A1(a, b, c), funcA592(a, b, c).
%alter function funcA593(a:int4, b:int4 -> c:int4) {}
%alter rule r592 B1(a, b, c) :- A1(a, b, c), funcA593(a, b, c).
%alter function funcA594(a:int4, b:int4 -> c:int4) {}
%alter rule r593 B1(a, b, c) :- A1(a, b, c), funcA594(a, b, c).
%alter function funcA595(a:int4, b:int4 -> c:int4) {}
%alter rule r594 B1(a, b, c) :- A1(a, b, c), funcA595(a, b, c).
%alter function funcA596(a:int4, b:int4 -> c:int4) {}
%alter rule r595 B1(a, b, c) :- A1(a, b, c), funcA596(a, b, c).
%alter function funcA597(a:int4, b:int4 -> c:int4) {}
%alter rule r596 B1(a, b, c) :- A1(a, b, c), funcA597(a, b, c).
%alter function funcA598(a:int4, b:int4 -> c:int4) {}
%alter rule r597 B1(a, b, c) :- A1(a, b, c), funcA598(a, b, c).
%alter function funcA599(a:int4, b:int4 -> c:int4) {}
%alter rule r598 B1(a, b, c) :- A1(a, b, c), funcA599(a, b, c).
%alter function funcA600(a:int4, b:int4 -> c:int4) {}
%alter rule r599 B1(a, b, c) :- A1(a, b, c), funcA600(a, b, c).
%alter function funcA601(a:int4, b:int4 -> c:int4) {}
%alter rule r600 B1(a, b, c) :- A1(a, b, c), funcA601(a, b, c).
%alter function funcA602(a:int4, b:int4 -> c:int4) {}
%alter rule r601 B1(a, b, c) :- A1(a, b, c), funcA602(a, b, c).
%alter function funcA603(a:int4, b:int4 -> c:int4) {}
%alter rule r602 B1(a, b, c) :- A1(a, b, c), funcA603(a, b, c).
%alter function funcA604(a:int4, b:int4 -> c:int4) {}
%alter rule r603 B1(a, b, c) :- A1(a, b, c), funcA604(a, b, c).
%alter function funcA605(a:int4, b:int4 -> c:int4) {}
%alter rule r604 B1(a, b, c) :- A1(a, b, c), funcA605(a, b, c).
%alter function funcA606(a:int4, b:int4 -> c:int4) {}
%alter rule r605 B1(a, b, c) :- A1(a, b, c), funcA606(a, b, c).
%alter function funcA607(a:int4, b:int4 -> c:int4) {}
%alter rule r606 B1(a, b, c) :- A1(a, b, c), funcA607(a, b, c).
%alter function funcA608(a:int4, b:int4 -> c:int4) {}
%alter rule r607 B1(a, b, c) :- A1(a, b, c), funcA608(a, b, c).
%alter function funcA609(a:int4, b:int4 -> c:int4) {}
%alter rule r608 B1(a, b, c) :- A1(a, b, c), funcA609(a, b, c).
%alter function funcA610(a:int4, b:int4 -> c:int4) {}
%alter rule r609 B1(a, b, c) :- A1(a, b, c), funcA610(a, b, c).
%alter function funcA611(a:int4, b:int4 -> c:int4) {}
%alter rule r610 B1(a, b, c) :- A1(a, b, c), funcA611(a, b, c).
%alter function funcA612(a:int4, b:int4 -> c:int4) {}
%alter rule r611 B1(a, b, c) :- A1(a, b, c), funcA612(a, b, c).
%alter function funcA613(a:int4, b:int4 -> c:int4) {}
%alter rule r612 B1(a, b, c) :- A1(a, b, c), funcA613(a, b, c).
%alter function funcA614(a:int4, b:int4 -> c:int4) {}
%alter rule r613 B1(a, b, c) :- A1(a, b, c), funcA614(a, b, c).
%alter function funcA615(a:int4, b:int4 -> c:int4) {}
%alter rule r614 B1(a, b, c) :- A1(a, b, c), funcA615(a, b, c).
%alter function funcA616(a:int4, b:int4 -> c:int4) {}
%alter rule r615 B1(a, b, c) :- A1(a, b, c), funcA616(a, b, c).
%alter function funcA617(a:int4, b:int4 -> c:int4) {}
%alter rule r616 B1(a, b, c) :- A1(a, b, c), funcA617(a, b, c).
%alter function funcA618(a:int4, b:int4 -> c:int4) {}
%alter rule r617 B1(a, b, c) :- A1(a, b, c), funcA618(a, b, c).
%alter function funcA619(a:int4, b:int4 -> c:int4) {}
%alter rule r618 B1(a, b, c) :- A1(a, b, c), funcA619(a, b, c).
%alter function funcA620(a:int4, b:int4 -> c:int4) {}
%alter rule r619 B1(a, b, c) :- A1(a, b, c), funcA620(a, b, c).
%alter function funcA621(a:int4, b:int4 -> c:int4) {}
%alter rule r620 B1(a, b, c) :- A1(a, b, c), funcA621(a, b, c).
%alter function funcA622(a:int4, b:int4 -> c:int4) {}
%alter rule r621 B1(a, b, c) :- A1(a, b, c), funcA622(a, b, c).
%alter function funcA623(a:int4, b:int4 -> c:int4) {}
%alter rule r622 B1(a, b, c) :- A1(a, b, c), funcA623(a, b, c).
%alter function funcA624(a:int4, b:int4 -> c:int4) {}
%alter rule r623 B1(a, b, c) :- A1(a, b, c), funcA624(a, b, c).
%alter function funcA625(a:int4, b:int4 -> c:int4) {}
%alter rule r624 B1(a, b, c) :- A1(a, b, c), funcA625(a, b, c).
%alter function funcA626(a:int4, b:int4 -> c:int4) {}
%alter rule r625 B1(a, b, c) :- A1(a, b, c), funcA626(a, b, c).
%alter function funcA627(a:int4, b:int4 -> c:int4) {}
%alter rule r626 B1(a, b, c) :- A1(a, b, c), funcA627(a, b, c).
%alter function funcA628(a:int4, b:int4 -> c:int4) {}
%alter rule r627 B1(a, b, c) :- A1(a, b, c), funcA628(a, b, c).
%alter function funcA629(a:int4, b:int4 -> c:int4) {}
%alter rule r628 B1(a, b, c) :- A1(a, b, c), funcA629(a, b, c).
%alter function funcA630(a:int4, b:int4 -> c:int4) {}
%alter rule r629 B1(a, b, c) :- A1(a, b, c), funcA630(a, b, c).
%alter function funcA631(a:int4, b:int4 -> c:int4) {}
%alter rule r630 B1(a, b, c) :- A1(a, b, c), funcA631(a, b, c).
%alter function funcA632(a:int4, b:int4 -> c:int4) {}
%alter rule r631 B1(a, b, c) :- A1(a, b, c), funcA632(a, b, c).
%alter function funcA633(a:int4, b:int4 -> c:int4) {}
%alter rule r632 B1(a, b, c) :- A1(a, b, c), funcA633(a, b, c).
%alter function funcA634(a:int4, b:int4 -> c:int4) {}
%alter rule r633 B1(a, b, c) :- A1(a, b, c), funcA634(a, b, c).
%alter function funcA635(a:int4, b:int4 -> c:int4) {}
%alter rule r634 B1(a, b, c) :- A1(a, b, c), funcA635(a, b, c).
%alter function funcA636(a:int4, b:int4 -> c:int4) {}
%alter rule r635 B1(a, b, c) :- A1(a, b, c), funcA636(a, b, c).
%alter function funcA637(a:int4, b:int4 -> c:int4) {}
%alter rule r636 B1(a, b, c) :- A1(a, b, c), funcA637(a, b, c).
%alter function funcA638(a:int4, b:int4 -> c:int4) {}
%alter rule r637 B1(a, b, c) :- A1(a, b, c), funcA638(a, b, c).
%alter function funcA639(a:int4, b:int4 -> c:int4) {}
%alter rule r638 B1(a, b, c) :- A1(a, b, c), funcA639(a, b, c).
%alter function funcA640(a:int4, b:int4 -> c:int4) {}
%alter rule r639 B1(a, b, c) :- A1(a, b, c), funcA640(a, b, c).
%alter function funcA641(a:int4, b:int4 -> c:int4) {}
%alter rule r640 B1(a, b, c) :- A1(a, b, c), funcA641(a, b, c).
%alter function funcA642(a:int4, b:int4 -> c:int4) {}
%alter rule r641 B1(a, b, c) :- A1(a, b, c), funcA642(a, b, c).
%alter function funcA643(a:int4, b:int4 -> c:int4) {}
%alter rule r642 B1(a, b, c) :- A1(a, b, c), funcA643(a, b, c).
%alter function funcA644(a:int4, b:int4 -> c:int4) {}
%alter rule r643 B1(a, b, c) :- A1(a, b, c), funcA644(a, b, c).
%alter function funcA645(a:int4, b:int4 -> c:int4) {}
%alter rule r644 B1(a, b, c) :- A1(a, b, c), funcA645(a, b, c).
%alter function funcA646(a:int4, b:int4 -> c:int4) {}
%alter rule r645 B1(a, b, c) :- A1(a, b, c), funcA646(a, b, c).
%alter function funcA647(a:int4, b:int4 -> c:int4) {}
%alter rule r646 B1(a, b, c) :- A1(a, b, c), funcA647(a, b, c).
%alter function funcA648(a:int4, b:int4 -> c:int4) {}
%alter rule r647 B1(a, b, c) :- A1(a, b, c), funcA648(a, b, c).
%alter function funcA649(a:int4, b:int4 -> c:int4) {}
%alter rule r648 B1(a, b, c) :- A1(a, b, c), funcA649(a, b, c).
%alter function funcA650(a:int4, b:int4 -> c:int4) {}
%alter rule r649 B1(a, b, c) :- A1(a, b, c), funcA650(a, b, c).
%alter function funcA651(a:int4, b:int4 -> c:int4) {}
%alter rule r650 B1(a, b, c) :- A1(a, b, c), funcA651(a, b, c).
%alter function funcA652(a:int4, b:int4 -> c:int4) {}
%alter rule r651 B1(a, b, c) :- A1(a, b, c), funcA652(a, b, c).
%alter function funcA653(a:int4, b:int4 -> c:int4) {}
%alter rule r652 B1(a, b, c) :- A1(a, b, c), funcA653(a, b, c).
%alter function funcA654(a:int4, b:int4 -> c:int4) {}
%alter rule r653 B1(a, b, c) :- A1(a, b, c), funcA654(a, b, c).
%alter function funcA655(a:int4, b:int4 -> c:int4) {}
%alter rule r654 B1(a, b, c) :- A1(a, b, c), funcA655(a, b, c).
%alter function funcA656(a:int4, b:int4 -> c:int4) {}
%alter rule r655 B1(a, b, c) :- A1(a, b, c), funcA656(a, b, c).
%alter function funcA657(a:int4, b:int4 -> c:int4) {}
%alter rule r656 B1(a, b, c) :- A1(a, b, c), funcA657(a, b, c).
%alter function funcA658(a:int4, b:int4 -> c:int4) {}
%alter rule r657 B1(a, b, c) :- A1(a, b, c), funcA658(a, b, c).
%alter function funcA659(a:int4, b:int4 -> c:int4) {}
%alter rule r658 B1(a, b, c) :- A1(a, b, c), funcA659(a, b, c).
%alter function funcA660(a:int4, b:int4 -> c:int4) {}
%alter rule r659 B1(a, b, c) :- A1(a, b, c), funcA660(a, b, c).
%alter function funcA661(a:int4, b:int4 -> c:int4) {}
%alter rule r660 B1(a, b, c) :- A1(a, b, c), funcA661(a, b, c).
%alter function funcA662(a:int4, b:int4 -> c:int4) {}
%alter rule r661 B1(a, b, c) :- A1(a, b, c), funcA662(a, b, c).
%alter function funcA663(a:int4, b:int4 -> c:int4) {}
%alter rule r662 B1(a, b, c) :- A1(a, b, c), funcA663(a, b, c).
%alter function funcA664(a:int4, b:int4 -> c:int4) {}
%alter rule r663 B1(a, b, c) :- A1(a, b, c), funcA664(a, b, c).
%alter function funcA665(a:int4, b:int4 -> c:int4) {}
%alter rule r664 B1(a, b, c) :- A1(a, b, c), funcA665(a, b, c).
%alter function funcA666(a:int4, b:int4 -> c:int4) {}
%alter rule r665 B1(a, b, c) :- A1(a, b, c), funcA666(a, b, c).
%alter function funcA667(a:int4, b:int4 -> c:int4) {}
%alter rule r666 B1(a, b, c) :- A1(a, b, c), funcA667(a, b, c).
%alter function funcA668(a:int4, b:int4 -> c:int4) {}
%alter rule r667 B1(a, b, c) :- A1(a, b, c), funcA668(a, b, c).
%alter function funcA669(a:int4, b:int4 -> c:int4) {}
%alter rule r668 B1(a, b, c) :- A1(a, b, c), funcA669(a, b, c).
%alter function funcA670(a:int4, b:int4 -> c:int4) {}
%alter rule r669 B1(a, b, c) :- A1(a, b, c), funcA670(a, b, c).
%alter function funcA671(a:int4, b:int4 -> c:int4) {}
%alter rule r670 B1(a, b, c) :- A1(a, b, c), funcA671(a, b, c).
%alter function funcA672(a:int4, b:int4 -> c:int4) {}
%alter rule r671 B1(a, b, c) :- A1(a, b, c), funcA672(a, b, c).
%alter function funcA673(a:int4, b:int4 -> c:int4) {}
%alter rule r672 B1(a, b, c) :- A1(a, b, c), funcA673(a, b, c).
%alter function funcA674(a:int4, b:int4 -> c:int4) {}
%alter rule r673 B1(a, b, c) :- A1(a, b, c), funcA674(a, b, c).
%alter function funcA675(a:int4, b:int4 -> c:int4) {}
%alter rule r674 B1(a, b, c) :- A1(a, b, c), funcA675(a, b, c).
%alter function funcA676(a:int4, b:int4 -> c:int4) {}
%alter rule r675 B1(a, b, c) :- A1(a, b, c), funcA676(a, b, c).
%alter function funcA677(a:int4, b:int4 -> c:int4) {}
%alter rule r676 B1(a, b, c) :- A1(a, b, c), funcA677(a, b, c).
%alter function funcA678(a:int4, b:int4 -> c:int4) {}
%alter rule r677 B1(a, b, c) :- A1(a, b, c), funcA678(a, b, c).
%alter function funcA679(a:int4, b:int4 -> c:int4) {}
%alter rule r678 B1(a, b, c) :- A1(a, b, c), funcA679(a, b, c).
%alter function funcA680(a:int4, b:int4 -> c:int4) {}
%alter rule r679 B1(a, b, c) :- A1(a, b, c), funcA680(a, b, c).
%alter function funcA681(a:int4, b:int4 -> c:int4) {}
%alter rule r680 B1(a, b, c) :- A1(a, b, c), funcA681(a, b, c).
%alter function funcA682(a:int4, b:int4 -> c:int4) {}
%alter rule r681 B1(a, b, c) :- A1(a, b, c), funcA682(a, b, c).
%alter function funcA683(a:int4, b:int4 -> c:int4) {}
%alter rule r682 B1(a, b, c) :- A1(a, b, c), funcA683(a, b, c).
%alter function funcA684(a:int4, b:int4 -> c:int4) {}
%alter rule r683 B1(a, b, c) :- A1(a, b, c), funcA684(a, b, c).
%alter function funcA685(a:int4, b:int4 -> c:int4) {}
%alter rule r684 B1(a, b, c) :- A1(a, b, c), funcA685(a, b, c).
%alter function funcA686(a:int4, b:int4 -> c:int4) {}
%alter rule r685 B1(a, b, c) :- A1(a, b, c), funcA686(a, b, c).
%alter function funcA687(a:int4, b:int4 -> c:int4) {}
%alter rule r686 B1(a, b, c) :- A1(a, b, c), funcA687(a, b, c).
%alter function funcA688(a:int4, b:int4 -> c:int4) {}
%alter rule r687 B1(a, b, c) :- A1(a, b, c), funcA688(a, b, c).
%alter function funcA689(a:int4, b:int4 -> c:int4) {}
%alter rule r688 B1(a, b, c) :- A1(a, b, c), funcA689(a, b, c).
%alter function funcA690(a:int4, b:int4 -> c:int4) {}
%alter rule r689 B1(a, b, c) :- A1(a, b, c), funcA690(a, b, c).
%alter function funcA691(a:int4, b:int4 -> c:int4) {}
%alter rule r690 B1(a, b, c) :- A1(a, b, c), funcA691(a, b, c).
%alter function funcA692(a:int4, b:int4 -> c:int4) {}
%alter rule r691 B1(a, b, c) :- A1(a, b, c), funcA692(a, b, c).
%alter function funcA693(a:int4, b:int4 -> c:int4) {}
%alter rule r692 B1(a, b, c) :- A1(a, b, c), funcA693(a, b, c).
%alter function funcA694(a:int4, b:int4 -> c:int4) {}
%alter rule r693 B1(a, b, c) :- A1(a, b, c), funcA694(a, b, c).
%alter function funcA695(a:int4, b:int4 -> c:int4) {}
%alter rule r694 B1(a, b, c) :- A1(a, b, c), funcA695(a, b, c).
%alter function funcA696(a:int4, b:int4 -> c:int4) {}
%alter rule r695 B1(a, b, c) :- A1(a, b, c), funcA696(a, b, c).
%alter function funcA697(a:int4, b:int4 -> c:int4) {}
%alter rule r696 B1(a, b, c) :- A1(a, b, c), funcA697(a, b, c).
%alter function funcA698(a:int4, b:int4 -> c:int4) {}
%alter rule r697 B1(a, b, c) :- A1(a, b, c), funcA698(a, b, c).
%alter function funcA699(a:int4, b:int4 -> c:int4) {}
%alter rule r698 B1(a, b, c) :- A1(a, b, c), funcA699(a, b, c).
%alter function funcA700(a:int4, b:int4 -> c:int4) {}
%alter rule r699 B1(a, b, c) :- A1(a, b, c), funcA700(a, b, c).
%alter function funcA701(a:int4, b:int4 -> c:int4) {}
%alter rule r700 B1(a, b, c) :- A1(a, b, c), funcA701(a, b, c).
%alter function funcA702(a:int4, b:int4 -> c:int4) {}
%alter rule r701 B1(a, b, c) :- A1(a, b, c), funcA702(a, b, c).
%alter function funcA703(a:int4, b:int4 -> c:int4) {}
%alter rule r702 B1(a, b, c) :- A1(a, b, c), funcA703(a, b, c).
%alter function funcA704(a:int4, b:int4 -> c:int4) {}
%alter rule r703 B1(a, b, c) :- A1(a, b, c), funcA704(a, b, c).
%alter function funcA705(a:int4, b:int4 -> c:int4) {}
%alter rule r704 B1(a, b, c) :- A1(a, b, c), funcA705(a, b, c).
%alter function funcA706(a:int4, b:int4 -> c:int4) {}
%alter rule r705 B1(a, b, c) :- A1(a, b, c), funcA706(a, b, c).
%alter function funcA707(a:int4, b:int4 -> c:int4) {}
%alter rule r706 B1(a, b, c) :- A1(a, b, c), funcA707(a, b, c).
%alter function funcA708(a:int4, b:int4 -> c:int4) {}
%alter rule r707 B1(a, b, c) :- A1(a, b, c), funcA708(a, b, c).
%alter function funcA709(a:int4, b:int4 -> c:int4) {}
%alter rule r708 B1(a, b, c) :- A1(a, b, c), funcA709(a, b, c).
%alter function funcA710(a:int4, b:int4 -> c:int4) {}
%alter rule r709 B1(a, b, c) :- A1(a, b, c), funcA710(a, b, c).
%alter function funcA711(a:int4, b:int4 -> c:int4) {}
%alter rule r710 B1(a, b, c) :- A1(a, b, c), funcA711(a, b, c).
%alter function funcA712(a:int4, b:int4 -> c:int4) {}
%alter rule r711 B1(a, b, c) :- A1(a, b, c), funcA712(a, b, c).
%alter function funcA713(a:int4, b:int4 -> c:int4) {}
%alter rule r712 B1(a, b, c) :- A1(a, b, c), funcA713(a, b, c).
%alter function funcA714(a:int4, b:int4 -> c:int4) {}
%alter rule r713 B1(a, b, c) :- A1(a, b, c), funcA714(a, b, c).
%alter function funcA715(a:int4, b:int4 -> c:int4) {}
%alter rule r714 B1(a, b, c) :- A1(a, b, c), funcA715(a, b, c).
%alter function funcA716(a:int4, b:int4 -> c:int4) {}
%alter rule r715 B1(a, b, c) :- A1(a, b, c), funcA716(a, b, c).
%alter function funcA717(a:int4, b:int4 -> c:int4) {}
%alter rule r716 B1(a, b, c) :- A1(a, b, c), funcA717(a, b, c).
%alter function funcA718(a:int4, b:int4 -> c:int4) {}
%alter rule r717 B1(a, b, c) :- A1(a, b, c), funcA718(a, b, c).
%alter function funcA719(a:int4, b:int4 -> c:int4) {}
%alter rule r718 B1(a, b, c) :- A1(a, b, c), funcA719(a, b, c).
%alter function funcA720(a:int4, b:int4 -> c:int4) {}
%alter rule r719 B1(a, b, c) :- A1(a, b, c), funcA720(a, b, c).
%alter function funcA721(a:int4, b:int4 -> c:int4) {}
%alter rule r720 B1(a, b, c) :- A1(a, b, c), funcA721(a, b, c).
%alter function funcA722(a:int4, b:int4 -> c:int4) {}
%alter rule r721 B1(a, b, c) :- A1(a, b, c), funcA722(a, b, c).
%alter function funcA723(a:int4, b:int4 -> c:int4) {}
%alter rule r722 B1(a, b, c) :- A1(a, b, c), funcA723(a, b, c).
%alter function funcA724(a:int4, b:int4 -> c:int4) {}
%alter rule r723 B1(a, b, c) :- A1(a, b, c), funcA724(a, b, c).
%alter function funcA725(a:int4, b:int4 -> c:int4) {}
%alter rule r724 B1(a, b, c) :- A1(a, b, c), funcA725(a, b, c).
%alter function funcA726(a:int4, b:int4 -> c:int4) {}
%alter rule r725 B1(a, b, c) :- A1(a, b, c), funcA726(a, b, c).
%alter function funcA727(a:int4, b:int4 -> c:int4) {}
%alter rule r726 B1(a, b, c) :- A1(a, b, c), funcA727(a, b, c).
%alter function funcA728(a:int4, b:int4 -> c:int4) {}
%alter rule r727 B1(a, b, c) :- A1(a, b, c), funcA728(a, b, c).
%alter function funcA729(a:int4, b:int4 -> c:int4) {}
%alter rule r728 B1(a, b, c) :- A1(a, b, c), funcA729(a, b, c).
%alter function funcA730(a:int4, b:int4 -> c:int4) {}
%alter rule r729 B1(a, b, c) :- A1(a, b, c), funcA730(a, b, c).
%alter function funcA731(a:int4, b:int4 -> c:int4) {}
%alter rule r730 B1(a, b, c) :- A1(a, b, c), funcA731(a, b, c).
%alter function funcA732(a:int4, b:int4 -> c:int4) {}
%alter rule r731 B1(a, b, c) :- A1(a, b, c), funcA732(a, b, c).
%alter function funcA733(a:int4, b:int4 -> c:int4) {}
%alter rule r732 B1(a, b, c) :- A1(a, b, c), funcA733(a, b, c).
%alter function funcA734(a:int4, b:int4 -> c:int4) {}
%alter rule r733 B1(a, b, c) :- A1(a, b, c), funcA734(a, b, c).
%alter function funcA735(a:int4, b:int4 -> c:int4) {}
%alter rule r734 B1(a, b, c) :- A1(a, b, c), funcA735(a, b, c).
%alter function funcA736(a:int4, b:int4 -> c:int4) {}
%alter rule r735 B1(a, b, c) :- A1(a, b, c), funcA736(a, b, c).
%alter function funcA737(a:int4, b:int4 -> c:int4) {}
%alter rule r736 B1(a, b, c) :- A1(a, b, c), funcA737(a, b, c).
%alter function funcA738(a:int4, b:int4 -> c:int4) {}
%alter rule r737 B1(a, b, c) :- A1(a, b, c), funcA738(a, b, c).
%alter function funcA739(a:int4, b:int4 -> c:int4) {}
%alter rule r738 B1(a, b, c) :- A1(a, b, c), funcA739(a, b, c).
%alter function funcA740(a:int4, b:int4 -> c:int4) {}
%alter rule r739 B1(a, b, c) :- A1(a, b, c), funcA740(a, b, c).
%alter function funcA741(a:int4, b:int4 -> c:int4) {}
%alter rule r740 B1(a, b, c) :- A1(a, b, c), funcA741(a, b, c).
%alter function funcA742(a:int4, b:int4 -> c:int4) {}
%alter rule r741 B1(a, b, c) :- A1(a, b, c), funcA742(a, b, c).
%alter function funcA743(a:int4, b:int4 -> c:int4) {}
%alter rule r742 B1(a, b, c) :- A1(a, b, c), funcA743(a, b, c).
%alter function funcA744(a:int4, b:int4 -> c:int4) {}
%alter rule r743 B1(a, b, c) :- A1(a, b, c), funcA744(a, b, c).
%alter function funcA745(a:int4, b:int4 -> c:int4) {}
%alter rule r744 B1(a, b, c) :- A1(a, b, c), funcA745(a, b, c).
%alter function funcA746(a:int4, b:int4 -> c:int4) {}
%alter rule r745 B1(a, b, c) :- A1(a, b, c), funcA746(a, b, c).
%alter function funcA747(a:int4, b:int4 -> c:int4) {}
%alter rule r746 B1(a, b, c) :- A1(a, b, c), funcA747(a, b, c).
%alter function funcA748(a:int4, b:int4 -> c:int4) {}
%alter rule r747 B1(a, b, c) :- A1(a, b, c), funcA748(a, b, c).
%alter function funcA749(a:int4, b:int4 -> c:int4) {}
%alter rule r748 B1(a, b, c) :- A1(a, b, c), funcA749(a, b, c).
%alter function funcA750(a:int4, b:int4 -> c:int4) {}
%alter rule r749 B1(a, b, c) :- A1(a, b, c), funcA750(a, b, c).
%alter function funcA751(a:int4, b:int4 -> c:int4) {}
%alter rule r750 B1(a, b, c) :- A1(a, b, c), funcA751(a, b, c).
%alter function funcA752(a:int4, b:int4 -> c:int4) {}
%alter rule r751 B1(a, b, c) :- A1(a, b, c), funcA752(a, b, c).
%alter function funcA753(a:int4, b:int4 -> c:int4) {}
%alter rule r752 B1(a, b, c) :- A1(a, b, c), funcA753(a, b, c).
%alter function funcA754(a:int4, b:int4 -> c:int4) {}
%alter rule r753 B1(a, b, c) :- A1(a, b, c), funcA754(a, b, c).
%alter function funcA755(a:int4, b:int4 -> c:int4) {}
%alter rule r754 B1(a, b, c) :- A1(a, b, c), funcA755(a, b, c).
%alter function funcA756(a:int4, b:int4 -> c:int4) {}
%alter rule r755 B1(a, b, c) :- A1(a, b, c), funcA756(a, b, c).
%alter function funcA757(a:int4, b:int4 -> c:int4) {}
%alter rule r756 B1(a, b, c) :- A1(a, b, c), funcA757(a, b, c).
%alter function funcA758(a:int4, b:int4 -> c:int4) {}
%alter rule r757 B1(a, b, c) :- A1(a, b, c), funcA758(a, b, c).
%alter function funcA759(a:int4, b:int4 -> c:int4) {}
%alter rule r758 B1(a, b, c) :- A1(a, b, c), funcA759(a, b, c).
%alter function funcA760(a:int4, b:int4 -> c:int4) {}
%alter rule r759 B1(a, b, c) :- A1(a, b, c), funcA760(a, b, c).
%alter function funcA761(a:int4, b:int4 -> c:int4) {}
%alter rule r760 B1(a, b, c) :- A1(a, b, c), funcA761(a, b, c).
%alter function funcA762(a:int4, b:int4 -> c:int4) {}
%alter rule r761 B1(a, b, c) :- A1(a, b, c), funcA762(a, b, c).
%alter function funcA763(a:int4, b:int4 -> c:int4) {}
%alter rule r762 B1(a, b, c) :- A1(a, b, c), funcA763(a, b, c).
%alter function funcA764(a:int4, b:int4 -> c:int4) {}
%alter rule r763 B1(a, b, c) :- A1(a, b, c), funcA764(a, b, c).
%alter function funcA765(a:int4, b:int4 -> c:int4) {}
%alter rule r764 B1(a, b, c) :- A1(a, b, c), funcA765(a, b, c).
%alter function funcA766(a:int4, b:int4 -> c:int4) {}
%alter rule r765 B1(a, b, c) :- A1(a, b, c), funcA766(a, b, c).
%alter function funcA767(a:int4, b:int4 -> c:int4) {}
%alter rule r766 B1(a, b, c) :- A1(a, b, c), funcA767(a, b, c).
%alter function funcA768(a:int4, b:int4 -> c:int4) {}
%alter rule r767 B1(a, b, c) :- A1(a, b, c), funcA768(a, b, c).
%alter function funcA769(a:int4, b:int4 -> c:int4) {}
%alter rule r768 B1(a, b, c) :- A1(a, b, c), funcA769(a, b, c).
%alter function funcA770(a:int4, b:int4 -> c:int4) {}
%alter rule r769 B1(a, b, c) :- A1(a, b, c), funcA770(a, b, c).
%alter function funcA771(a:int4, b:int4 -> c:int4) {}
%alter rule r770 B1(a, b, c) :- A1(a, b, c), funcA771(a, b, c).
%alter function funcA772(a:int4, b:int4 -> c:int4) {}
%alter rule r771 B1(a, b, c) :- A1(a, b, c), funcA772(a, b, c).
%alter function funcA773(a:int4, b:int4 -> c:int4) {}
%alter rule r772 B1(a, b, c) :- A1(a, b, c), funcA773(a, b, c).
%alter function funcA774(a:int4, b:int4 -> c:int4) {}
%alter rule r773 B1(a, b, c) :- A1(a, b, c), funcA774(a, b, c).
%alter function funcA775(a:int4, b:int4 -> c:int4) {}
%alter rule r774 B1(a, b, c) :- A1(a, b, c), funcA775(a, b, c).
%alter function funcA776(a:int4, b:int4 -> c:int4) {}
%alter rule r775 B1(a, b, c) :- A1(a, b, c), funcA776(a, b, c).
%alter function funcA777(a:int4, b:int4 -> c:int4) {}
%alter rule r776 B1(a, b, c) :- A1(a, b, c), funcA777(a, b, c).
%alter function funcA778(a:int4, b:int4 -> c:int4) {}
%alter rule r777 B1(a, b, c) :- A1(a, b, c), funcA778(a, b, c).
%alter function funcA779(a:int4, b:int4 -> c:int4) {}
%alter rule r778 B1(a, b, c) :- A1(a, b, c), funcA779(a, b, c).
%alter function funcA780(a:int4, b:int4 -> c:int4) {}
%alter rule r779 B1(a, b, c) :- A1(a, b, c), funcA780(a, b, c).
%alter function funcA781(a:int4, b:int4 -> c:int4) {}
%alter rule r780 B1(a, b, c) :- A1(a, b, c), funcA781(a, b, c).
%alter function funcA782(a:int4, b:int4 -> c:int4) {}
%alter rule r781 B1(a, b, c) :- A1(a, b, c), funcA782(a, b, c).
%alter function funcA783(a:int4, b:int4 -> c:int4) {}
%alter rule r782 B1(a, b, c) :- A1(a, b, c), funcA783(a, b, c).
%alter function funcA784(a:int4, b:int4 -> c:int4) {}
%alter rule r783 B1(a, b, c) :- A1(a, b, c), funcA784(a, b, c).
%alter function funcA785(a:int4, b:int4 -> c:int4) {}
%alter rule r784 B1(a, b, c) :- A1(a, b, c), funcA785(a, b, c).
%alter function funcA786(a:int4, b:int4 -> c:int4) {}
%alter rule r785 B1(a, b, c) :- A1(a, b, c), funcA786(a, b, c).
%alter function funcA787(a:int4, b:int4 -> c:int4) {}
%alter rule r786 B1(a, b, c) :- A1(a, b, c), funcA787(a, b, c).
%alter function funcA788(a:int4, b:int4 -> c:int4) {}
%alter rule r787 B1(a, b, c) :- A1(a, b, c), funcA788(a, b, c).
%alter function funcA789(a:int4, b:int4 -> c:int4) {}
%alter rule r788 B1(a, b, c) :- A1(a, b, c), funcA789(a, b, c).
%alter function funcA790(a:int4, b:int4 -> c:int4) {}
%alter rule r789 B1(a, b, c) :- A1(a, b, c), funcA790(a, b, c).
%alter function funcA791(a:int4, b:int4 -> c:int4) {}
%alter rule r790 B1(a, b, c) :- A1(a, b, c), funcA791(a, b, c).
%alter function funcA792(a:int4, b:int4 -> c:int4) {}
%alter rule r791 B1(a, b, c) :- A1(a, b, c), funcA792(a, b, c).
%alter function funcA793(a:int4, b:int4 -> c:int4) {}
%alter rule r792 B1(a, b, c) :- A1(a, b, c), funcA793(a, b, c).
%alter function funcA794(a:int4, b:int4 -> c:int4) {}
%alter rule r793 B1(a, b, c) :- A1(a, b, c), funcA794(a, b, c).
%alter function funcA795(a:int4, b:int4 -> c:int4) {}
%alter rule r794 B1(a, b, c) :- A1(a, b, c), funcA795(a, b, c).
%alter function funcA796(a:int4, b:int4 -> c:int4) {}
%alter rule r795 B1(a, b, c) :- A1(a, b, c), funcA796(a, b, c).
%alter function funcA797(a:int4, b:int4 -> c:int4) {}
%alter rule r796 B1(a, b, c) :- A1(a, b, c), funcA797(a, b, c).
%alter function funcA798(a:int4, b:int4 -> c:int4) {}
%alter rule r797 B1(a, b, c) :- A1(a, b, c), funcA798(a, b, c).
%alter function funcA799(a:int4, b:int4 -> c:int4) {}
%alter rule r798 B1(a, b, c) :- A1(a, b, c), funcA799(a, b, c).
%alter function funcA800(a:int4, b:int4 -> c:int4) {}
%alter rule r799 B1(a, b, c) :- A1(a, b, c), funcA800(a, b, c).
%alter function funcA801(a:int4, b:int4 -> c:int4) {}
%alter rule r800 B1(a, b, c) :- A1(a, b, c), funcA801(a, b, c).
%alter function funcA802(a:int4, b:int4 -> c:int4) {}
%alter rule r801 B1(a, b, c) :- A1(a, b, c), funcA802(a, b, c).
%alter function funcA803(a:int4, b:int4 -> c:int4) {}
%alter rule r802 B1(a, b, c) :- A1(a, b, c), funcA803(a, b, c).
%alter function funcA804(a:int4, b:int4 -> c:int4) {}
%alter rule r803 B1(a, b, c) :- A1(a, b, c), funcA804(a, b, c).
%alter function funcA805(a:int4, b:int4 -> c:int4) {}
%alter rule r804 B1(a, b, c) :- A1(a, b, c), funcA805(a, b, c).
%alter function funcA806(a:int4, b:int4 -> c:int4) {}
%alter rule r805 B1(a, b, c) :- A1(a, b, c), funcA806(a, b, c).
%alter function funcA807(a:int4, b:int4 -> c:int4) {}
%alter rule r806 B1(a, b, c) :- A1(a, b, c), funcA807(a, b, c).
%alter function funcA808(a:int4, b:int4 -> c:int4) {}
%alter rule r807 B1(a, b, c) :- A1(a, b, c), funcA808(a, b, c).
%alter function funcA809(a:int4, b:int4 -> c:int4) {}
%alter rule r808 B1(a, b, c) :- A1(a, b, c), funcA809(a, b, c).
%alter function funcA810(a:int4, b:int4 -> c:int4) {}
%alter rule r809 B1(a, b, c) :- A1(a, b, c), funcA810(a, b, c).
%alter function funcA811(a:int4, b:int4 -> c:int4) {}
%alter rule r810 B1(a, b, c) :- A1(a, b, c), funcA811(a, b, c).
%alter function funcA812(a:int4, b:int4 -> c:int4) {}
%alter rule r811 B1(a, b, c) :- A1(a, b, c), funcA812(a, b, c).
%alter function funcA813(a:int4, b:int4 -> c:int4) {}
%alter rule r812 B1(a, b, c) :- A1(a, b, c), funcA813(a, b, c).
%alter function funcA814(a:int4, b:int4 -> c:int4) {}
%alter rule r813 B1(a, b, c) :- A1(a, b, c), funcA814(a, b, c).
%alter function funcA815(a:int4, b:int4 -> c:int4) {}
%alter rule r814 B1(a, b, c) :- A1(a, b, c), funcA815(a, b, c).
%alter function funcA816(a:int4, b:int4 -> c:int4) {}
%alter rule r815 B1(a, b, c) :- A1(a, b, c), funcA816(a, b, c).
%alter function funcA817(a:int4, b:int4 -> c:int4) {}
%alter rule r816 B1(a, b, c) :- A1(a, b, c), funcA817(a, b, c).
%alter function funcA818(a:int4, b:int4 -> c:int4) {}
%alter rule r817 B1(a, b, c) :- A1(a, b, c), funcA818(a, b, c).
%alter function funcA819(a:int4, b:int4 -> c:int4) {}
%alter rule r818 B1(a, b, c) :- A1(a, b, c), funcA819(a, b, c).
%alter function funcA820(a:int4, b:int4 -> c:int4) {}
%alter rule r819 B1(a, b, c) :- A1(a, b, c), funcA820(a, b, c).
%alter function funcA821(a:int4, b:int4 -> c:int4) {}
%alter rule r820 B1(a, b, c) :- A1(a, b, c), funcA821(a, b, c).
%alter function funcA822(a:int4, b:int4 -> c:int4) {}
%alter rule r821 B1(a, b, c) :- A1(a, b, c), funcA822(a, b, c).
%alter function funcA823(a:int4, b:int4 -> c:int4) {}
%alter rule r822 B1(a, b, c) :- A1(a, b, c), funcA823(a, b, c).
%alter function funcA824(a:int4, b:int4 -> c:int4) {}
%alter rule r823 B1(a, b, c) :- A1(a, b, c), funcA824(a, b, c).
%alter function funcA825(a:int4, b:int4 -> c:int4) {}
%alter rule r824 B1(a, b, c) :- A1(a, b, c), funcA825(a, b, c).
%alter function funcA826(a:int4, b:int4 -> c:int4) {}
%alter rule r825 B1(a, b, c) :- A1(a, b, c), funcA826(a, b, c).
%alter function funcA827(a:int4, b:int4 -> c:int4) {}
%alter rule r826 B1(a, b, c) :- A1(a, b, c), funcA827(a, b, c).
%alter function funcA828(a:int4, b:int4 -> c:int4) {}
%alter rule r827 B1(a, b, c) :- A1(a, b, c), funcA828(a, b, c).
%alter function funcA829(a:int4, b:int4 -> c:int4) {}
%alter rule r828 B1(a, b, c) :- A1(a, b, c), funcA829(a, b, c).
%alter function funcA830(a:int4, b:int4 -> c:int4) {}
%alter rule r829 B1(a, b, c) :- A1(a, b, c), funcA830(a, b, c).
%alter function funcA831(a:int4, b:int4 -> c:int4) {}
%alter rule r830 B1(a, b, c) :- A1(a, b, c), funcA831(a, b, c).
%alter function funcA832(a:int4, b:int4 -> c:int4) {}
%alter rule r831 B1(a, b, c) :- A1(a, b, c), funcA832(a, b, c).
%alter function funcA833(a:int4, b:int4 -> c:int4) {}
%alter rule r832 B1(a, b, c) :- A1(a, b, c), funcA833(a, b, c).
%alter function funcA834(a:int4, b:int4 -> c:int4) {}
%alter rule r833 B1(a, b, c) :- A1(a, b, c), funcA834(a, b, c).
%alter function funcA835(a:int4, b:int4 -> c:int4) {}
%alter rule r834 B1(a, b, c) :- A1(a, b, c), funcA835(a, b, c).
%alter function funcA836(a:int4, b:int4 -> c:int4) {}
%alter rule r835 B1(a, b, c) :- A1(a, b, c), funcA836(a, b, c).
%alter function funcA837(a:int4, b:int4 -> c:int4) {}
%alter rule r836 B1(a, b, c) :- A1(a, b, c), funcA837(a, b, c).
%alter function funcA838(a:int4, b:int4 -> c:int4) {}
%alter rule r837 B1(a, b, c) :- A1(a, b, c), funcA838(a, b, c).
%alter function funcA839(a:int4, b:int4 -> c:int4) {}
%alter rule r838 B1(a, b, c) :- A1(a, b, c), funcA839(a, b, c).
%alter function funcA840(a:int4, b:int4 -> c:int4) {}
%alter rule r839 B1(a, b, c) :- A1(a, b, c), funcA840(a, b, c).
%alter function funcA841(a:int4, b:int4 -> c:int4) {}
%alter rule r840 B1(a, b, c) :- A1(a, b, c), funcA841(a, b, c).
%alter function funcA842(a:int4, b:int4 -> c:int4) {}
%alter rule r841 B1(a, b, c) :- A1(a, b, c), funcA842(a, b, c).
%alter function funcA843(a:int4, b:int4 -> c:int4) {}
%alter rule r842 B1(a, b, c) :- A1(a, b, c), funcA843(a, b, c).
%alter function funcA844(a:int4, b:int4 -> c:int4) {}
%alter rule r843 B1(a, b, c) :- A1(a, b, c), funcA844(a, b, c).
%alter function funcA845(a:int4, b:int4 -> c:int4) {}
%alter rule r844 B1(a, b, c) :- A1(a, b, c), funcA845(a, b, c).
%alter function funcA846(a:int4, b:int4 -> c:int4) {}
%alter rule r845 B1(a, b, c) :- A1(a, b, c), funcA846(a, b, c).
%alter function funcA847(a:int4, b:int4 -> c:int4) {}
%alter rule r846 B1(a, b, c) :- A1(a, b, c), funcA847(a, b, c).
%alter function funcA848(a:int4, b:int4 -> c:int4) {}
%alter rule r847 B1(a, b, c) :- A1(a, b, c), funcA848(a, b, c).
%alter function funcA849(a:int4, b:int4 -> c:int4) {}
%alter rule r848 B1(a, b, c) :- A1(a, b, c), funcA849(a, b, c).
%alter function funcA850(a:int4, b:int4 -> c:int4) {}
%alter rule r849 B1(a, b, c) :- A1(a, b, c), funcA850(a, b, c).
%alter function funcA851(a:int4, b:int4 -> c:int4) {}
%alter rule r850 B1(a, b, c) :- A1(a, b, c), funcA851(a, b, c).
%alter function funcA852(a:int4, b:int4 -> c:int4) {}
%alter rule r851 B1(a, b, c) :- A1(a, b, c), funcA852(a, b, c).
%alter function funcA853(a:int4, b:int4 -> c:int4) {}
%alter rule r852 B1(a, b, c) :- A1(a, b, c), funcA853(a, b, c).
%alter function funcA854(a:int4, b:int4 -> c:int4) {}
%alter rule r853 B1(a, b, c) :- A1(a, b, c), funcA854(a, b, c).
%alter function funcA855(a:int4, b:int4 -> c:int4) {}
%alter rule r854 B1(a, b, c) :- A1(a, b, c), funcA855(a, b, c).
%alter function funcA856(a:int4, b:int4 -> c:int4) {}
%alter rule r855 B1(a, b, c) :- A1(a, b, c), funcA856(a, b, c).
%alter function funcA857(a:int4, b:int4 -> c:int4) {}
%alter rule r856 B1(a, b, c) :- A1(a, b, c), funcA857(a, b, c).
%alter function funcA858(a:int4, b:int4 -> c:int4) {}
%alter rule r857 B1(a, b, c) :- A1(a, b, c), funcA858(a, b, c).
%alter function funcA859(a:int4, b:int4 -> c:int4) {}
%alter rule r858 B1(a, b, c) :- A1(a, b, c), funcA859(a, b, c).
%alter function funcA860(a:int4, b:int4 -> c:int4) {}
%alter rule r859 B1(a, b, c) :- A1(a, b, c), funcA860(a, b, c).
%alter function funcA861(a:int4, b:int4 -> c:int4) {}
%alter rule r860 B1(a, b, c) :- A1(a, b, c), funcA861(a, b, c).
%alter function funcA862(a:int4, b:int4 -> c:int4) {}
%alter rule r861 B1(a, b, c) :- A1(a, b, c), funcA862(a, b, c).
%alter function funcA863(a:int4, b:int4 -> c:int4) {}
%alter rule r862 B1(a, b, c) :- A1(a, b, c), funcA863(a, b, c).
%alter function funcA864(a:int4, b:int4 -> c:int4) {}
%alter rule r863 B1(a, b, c) :- A1(a, b, c), funcA864(a, b, c).
%alter function funcA865(a:int4, b:int4 -> c:int4) {}
%alter rule r864 B1(a, b, c) :- A1(a, b, c), funcA865(a, b, c).
%alter function funcA866(a:int4, b:int4 -> c:int4) {}
%alter rule r865 B1(a, b, c) :- A1(a, b, c), funcA866(a, b, c).
%alter function funcA867(a:int4, b:int4 -> c:int4) {}
%alter rule r866 B1(a, b, c) :- A1(a, b, c), funcA867(a, b, c).
%alter function funcA868(a:int4, b:int4 -> c:int4) {}
%alter rule r867 B1(a, b, c) :- A1(a, b, c), funcA868(a, b, c).
%alter function funcA869(a:int4, b:int4 -> c:int4) {}
%alter rule r868 B1(a, b, c) :- A1(a, b, c), funcA869(a, b, c).
%alter function funcA870(a:int4, b:int4 -> c:int4) {}
%alter rule r869 B1(a, b, c) :- A1(a, b, c), funcA870(a, b, c).
%alter function funcA871(a:int4, b:int4 -> c:int4) {}
%alter rule r870 B1(a, b, c) :- A1(a, b, c), funcA871(a, b, c).
%alter function funcA872(a:int4, b:int4 -> c:int4) {}
%alter rule r871 B1(a, b, c) :- A1(a, b, c), funcA872(a, b, c).
%alter function funcA873(a:int4, b:int4 -> c:int4) {}
%alter rule r872 B1(a, b, c) :- A1(a, b, c), funcA873(a, b, c).
%alter function funcA874(a:int4, b:int4 -> c:int4) {}
%alter rule r873 B1(a, b, c) :- A1(a, b, c), funcA874(a, b, c).
%alter function funcA875(a:int4, b:int4 -> c:int4) {}
%alter rule r874 B1(a, b, c) :- A1(a, b, c), funcA875(a, b, c).
%alter function funcA876(a:int4, b:int4 -> c:int4) {}
%alter rule r875 B1(a, b, c) :- A1(a, b, c), funcA876(a, b, c).
%alter function funcA877(a:int4, b:int4 -> c:int4) {}
%alter rule r876 B1(a, b, c) :- A1(a, b, c), funcA877(a, b, c).
%alter function funcA878(a:int4, b:int4 -> c:int4) {}
%alter rule r877 B1(a, b, c) :- A1(a, b, c), funcA878(a, b, c).
%alter function funcA879(a:int4, b:int4 -> c:int4) {}
%alter rule r878 B1(a, b, c) :- A1(a, b, c), funcA879(a, b, c).
%alter function funcA880(a:int4, b:int4 -> c:int4) {}
%alter rule r879 B1(a, b, c) :- A1(a, b, c), funcA880(a, b, c).
%alter function funcA881(a:int4, b:int4 -> c:int4) {}
%alter rule r880 B1(a, b, c) :- A1(a, b, c), funcA881(a, b, c).
%alter function funcA882(a:int4, b:int4 -> c:int4) {}
%alter rule r881 B1(a, b, c) :- A1(a, b, c), funcA882(a, b, c).
%alter function funcA883(a:int4, b:int4 -> c:int4) {}
%alter rule r882 B1(a, b, c) :- A1(a, b, c), funcA883(a, b, c).
%alter function funcA884(a:int4, b:int4 -> c:int4) {}
%alter rule r883 B1(a, b, c) :- A1(a, b, c), funcA884(a, b, c).
%alter function funcA885(a:int4, b:int4 -> c:int4) {}
%alter rule r884 B1(a, b, c) :- A1(a, b, c), funcA885(a, b, c).
%alter function funcA886(a:int4, b:int4 -> c:int4) {}
%alter rule r885 B1(a, b, c) :- A1(a, b, c), funcA886(a, b, c).
%alter function funcA887(a:int4, b:int4 -> c:int4) {}
%alter rule r886 B1(a, b, c) :- A1(a, b, c), funcA887(a, b, c).
%alter function funcA888(a:int4, b:int4 -> c:int4) {}
%alter rule r887 B1(a, b, c) :- A1(a, b, c), funcA888(a, b, c).
%alter function funcA889(a:int4, b:int4 -> c:int4) {}
%alter rule r888 B1(a, b, c) :- A1(a, b, c), funcA889(a, b, c).
%alter function funcA890(a:int4, b:int4 -> c:int4) {}
%alter rule r889 B1(a, b, c) :- A1(a, b, c), funcA890(a, b, c).
%alter function funcA891(a:int4, b:int4 -> c:int4) {}
%alter rule r890 B1(a, b, c) :- A1(a, b, c), funcA891(a, b, c).
%alter function funcA892(a:int4, b:int4 -> c:int4) {}
%alter rule r891 B1(a, b, c) :- A1(a, b, c), funcA892(a, b, c).
%alter function funcA893(a:int4, b:int4 -> c:int4) {}
%alter rule r892 B1(a, b, c) :- A1(a, b, c), funcA893(a, b, c).
%alter function funcA894(a:int4, b:int4 -> c:int4) {}
%alter rule r893 B1(a, b, c) :- A1(a, b, c), funcA894(a, b, c).
%alter function funcA895(a:int4, b:int4 -> c:int4) {}
%alter rule r894 B1(a, b, c) :- A1(a, b, c), funcA895(a, b, c).
%alter function funcA896(a:int4, b:int4 -> c:int4) {}
%alter rule r895 B1(a, b, c) :- A1(a, b, c), funcA896(a, b, c).
%alter function funcA897(a:int4, b:int4 -> c:int4) {}
%alter rule r896 B1(a, b, c) :- A1(a, b, c), funcA897(a, b, c).
%alter function funcA898(a:int4, b:int4 -> c:int4) {}
%alter rule r897 B1(a, b, c) :- A1(a, b, c), funcA898(a, b, c).
%alter function funcA899(a:int4, b:int4 -> c:int4) {}
%alter rule r898 B1(a, b, c) :- A1(a, b, c), funcA899(a, b, c).
%alter function funcA900(a:int4, b:int4 -> c:int4) {}
%alter rule r899 B1(a, b, c) :- A1(a, b, c), funcA900(a, b, c).
%alter function funcA901(a:int4, b:int4 -> c:int4) {}
%alter rule r900 B1(a, b, c) :- A1(a, b, c), funcA901(a, b, c).
%alter function funcA902(a:int4, b:int4 -> c:int4) {}
%alter rule r901 B1(a, b, c) :- A1(a, b, c), funcA902(a, b, c).
%alter function funcA903(a:int4, b:int4 -> c:int4) {}
%alter rule r902 B1(a, b, c) :- A1(a, b, c), funcA903(a, b, c).
%alter function funcA904(a:int4, b:int4 -> c:int4) {}
%alter rule r903 B1(a, b, c) :- A1(a, b, c), funcA904(a, b, c).
%alter function funcA905(a:int4, b:int4 -> c:int4) {}
%alter rule r904 B1(a, b, c) :- A1(a, b, c), funcA905(a, b, c).
%alter function funcA906(a:int4, b:int4 -> c:int4) {}
%alter rule r905 B1(a, b, c) :- A1(a, b, c), funcA906(a, b, c).
%alter function funcA907(a:int4, b:int4 -> c:int4) {}
%alter rule r906 B1(a, b, c) :- A1(a, b, c), funcA907(a, b, c).
%alter function funcA908(a:int4, b:int4 -> c:int4) {}
%alter rule r907 B1(a, b, c) :- A1(a, b, c), funcA908(a, b, c).
%alter function funcA909(a:int4, b:int4 -> c:int4) {}
%alter rule r908 B1(a, b, c) :- A1(a, b, c), funcA909(a, b, c).
%alter function funcA910(a:int4, b:int4 -> c:int4) {}
%alter rule r909 B1(a, b, c) :- A1(a, b, c), funcA910(a, b, c).
%alter function funcA911(a:int4, b:int4 -> c:int4) {}
%alter rule r910 B1(a, b, c) :- A1(a, b, c), funcA911(a, b, c).
%alter function funcA912(a:int4, b:int4 -> c:int4) {}
%alter rule r911 B1(a, b, c) :- A1(a, b, c), funcA912(a, b, c).
%alter function funcA913(a:int4, b:int4 -> c:int4) {}
%alter rule r912 B1(a, b, c) :- A1(a, b, c), funcA913(a, b, c).
%alter function funcA914(a:int4, b:int4 -> c:int4) {}
%alter rule r913 B1(a, b, c) :- A1(a, b, c), funcA914(a, b, c).
%alter function funcA915(a:int4, b:int4 -> c:int4) {}
%alter rule r914 B1(a, b, c) :- A1(a, b, c), funcA915(a, b, c).
%alter function funcA916(a:int4, b:int4 -> c:int4) {}
%alter rule r915 B1(a, b, c) :- A1(a, b, c), funcA916(a, b, c).
%alter function funcA917(a:int4, b:int4 -> c:int4) {}
%alter rule r916 B1(a, b, c) :- A1(a, b, c), funcA917(a, b, c).
%alter function funcA918(a:int4, b:int4 -> c:int4) {}
%alter rule r917 B1(a, b, c) :- A1(a, b, c), funcA918(a, b, c).
%alter function funcA919(a:int4, b:int4 -> c:int4) {}
%alter rule r918 B1(a, b, c) :- A1(a, b, c), funcA919(a, b, c).
%alter function funcA920(a:int4, b:int4 -> c:int4) {}
%alter rule r919 B1(a, b, c) :- A1(a, b, c), funcA920(a, b, c).
%alter function funcA921(a:int4, b:int4 -> c:int4) {}
%alter rule r920 B1(a, b, c) :- A1(a, b, c), funcA921(a, b, c).
%alter function funcA922(a:int4, b:int4 -> c:int4) {}
%alter rule r921 B1(a, b, c) :- A1(a, b, c), funcA922(a, b, c).
%alter function funcA923(a:int4, b:int4 -> c:int4) {}
%alter rule r922 B1(a, b, c) :- A1(a, b, c), funcA923(a, b, c).
%alter function funcA924(a:int4, b:int4 -> c:int4) {}
%alter rule r923 B1(a, b, c) :- A1(a, b, c), funcA924(a, b, c).
%alter function funcA925(a:int4, b:int4 -> c:int4) {}
%alter rule r924 B1(a, b, c) :- A1(a, b, c), funcA925(a, b, c).
%alter function funcA926(a:int4, b:int4 -> c:int4) {}
%alter rule r925 B1(a, b, c) :- A1(a, b, c), funcA926(a, b, c).
%alter function funcA927(a:int4, b:int4 -> c:int4) {}
%alter rule r926 B1(a, b, c) :- A1(a, b, c), funcA927(a, b, c).
%alter function funcA928(a:int4, b:int4 -> c:int4) {}
%alter rule r927 B1(a, b, c) :- A1(a, b, c), funcA928(a, b, c).
%alter function funcA929(a:int4, b:int4 -> c:int4) {}
%alter rule r928 B1(a, b, c) :- A1(a, b, c), funcA929(a, b, c).
%alter function funcA930(a:int4, b:int4 -> c:int4) {}
%alter rule r929 B1(a, b, c) :- A1(a, b, c), funcA930(a, b, c).
%alter function funcA931(a:int4, b:int4 -> c:int4) {}
%alter rule r930 B1(a, b, c) :- A1(a, b, c), funcA931(a, b, c).
%alter function funcA932(a:int4, b:int4 -> c:int4) {}
%alter rule r931 B1(a, b, c) :- A1(a, b, c), funcA932(a, b, c).
%alter function funcA933(a:int4, b:int4 -> c:int4) {}
%alter rule r932 B1(a, b, c) :- A1(a, b, c), funcA933(a, b, c).
%alter function funcA934(a:int4, b:int4 -> c:int4) {}
%alter rule r933 B1(a, b, c) :- A1(a, b, c), funcA934(a, b, c).
%alter function funcA935(a:int4, b:int4 -> c:int4) {}
%alter rule r934 B1(a, b, c) :- A1(a, b, c), funcA935(a, b, c).
%alter function funcA936(a:int4, b:int4 -> c:int4) {}
%alter rule r935 B1(a, b, c) :- A1(a, b, c), funcA936(a, b, c).
%alter function funcA937(a:int4, b:int4 -> c:int4) {}
%alter rule r936 B1(a, b, c) :- A1(a, b, c), funcA937(a, b, c).
%alter function funcA938(a:int4, b:int4 -> c:int4) {}
%alter rule r937 B1(a, b, c) :- A1(a, b, c), funcA938(a, b, c).
%alter function funcA939(a:int4, b:int4 -> c:int4) {}
%alter rule r938 B1(a, b, c) :- A1(a, b, c), funcA939(a, b, c).
%alter function funcA940(a:int4, b:int4 -> c:int4) {}
%alter rule r939 B1(a, b, c) :- A1(a, b, c), funcA940(a, b, c).
%alter function funcA941(a:int4, b:int4 -> c:int4) {}
%alter rule r940 B1(a, b, c) :- A1(a, b, c), funcA941(a, b, c).
%alter function funcA942(a:int4, b:int4 -> c:int4) {}
%alter rule r941 B1(a, b, c) :- A1(a, b, c), funcA942(a, b, c).
%alter function funcA943(a:int4, b:int4 -> c:int4) {}
%alter rule r942 B1(a, b, c) :- A1(a, b, c), funcA943(a, b, c).
%alter function funcA944(a:int4, b:int4 -> c:int4) {}
%alter rule r943 B1(a, b, c) :- A1(a, b, c), funcA944(a, b, c).
%alter function funcA945(a:int4, b:int4 -> c:int4) {}
%alter rule r944 B1(a, b, c) :- A1(a, b, c), funcA945(a, b, c).
%alter function funcA946(a:int4, b:int4 -> c:int4) {}
%alter rule r945 B1(a, b, c) :- A1(a, b, c), funcA946(a, b, c).
%alter function funcA947(a:int4, b:int4 -> c:int4) {}
%alter rule r946 B1(a, b, c) :- A1(a, b, c), funcA947(a, b, c).
%alter function funcA948(a:int4, b:int4 -> c:int4) {}
%alter rule r947 B1(a, b, c) :- A1(a, b, c), funcA948(a, b, c).
%alter function funcA949(a:int4, b:int4 -> c:int4) {}
%alter rule r948 B1(a, b, c) :- A1(a, b, c), funcA949(a, b, c).
%alter function funcA950(a:int4, b:int4 -> c:int4) {}
%alter rule r949 B1(a, b, c) :- A1(a, b, c), funcA950(a, b, c).
%alter function funcA951(a:int4, b:int4 -> c:int4) {}
%alter rule r950 B1(a, b, c) :- A1(a, b, c), funcA951(a, b, c).
%alter function funcA952(a:int4, b:int4 -> c:int4) {}
%alter rule r951 B1(a, b, c) :- A1(a, b, c), funcA952(a, b, c).
%alter function funcA953(a:int4, b:int4 -> c:int4) {}
%alter rule r952 B1(a, b, c) :- A1(a, b, c), funcA953(a, b, c).
%alter function funcA954(a:int4, b:int4 -> c:int4) {}
%alter rule r953 B1(a, b, c) :- A1(a, b, c), funcA954(a, b, c).
%alter function funcA955(a:int4, b:int4 -> c:int4) {}
%alter rule r954 B1(a, b, c) :- A1(a, b, c), funcA955(a, b, c).
%alter function funcA956(a:int4, b:int4 -> c:int4) {}
%alter rule r955 B1(a, b, c) :- A1(a, b, c), funcA956(a, b, c).
%alter function funcA957(a:int4, b:int4 -> c:int4) {}
%alter rule r956 B1(a, b, c) :- A1(a, b, c), funcA957(a, b, c).
%alter function funcA958(a:int4, b:int4 -> c:int4) {}
%alter rule r957 B1(a, b, c) :- A1(a, b, c), funcA958(a, b, c).
%alter function funcA959(a:int4, b:int4 -> c:int4) {}
%alter rule r958 B1(a, b, c) :- A1(a, b, c), funcA959(a, b, c).
%alter function funcA960(a:int4, b:int4 -> c:int4) {}
%alter rule r959 B1(a, b, c) :- A1(a, b, c), funcA960(a, b, c).
%alter function funcA961(a:int4, b:int4 -> c:int4) {}
%alter rule r960 B1(a, b, c) :- A1(a, b, c), funcA961(a, b, c).
%alter function funcA962(a:int4, b:int4 -> c:int4) {}
%alter rule r961 B1(a, b, c) :- A1(a, b, c), funcA962(a, b, c).
%alter function funcA963(a:int4, b:int4 -> c:int4) {}
%alter rule r962 B1(a, b, c) :- A1(a, b, c), funcA963(a, b, c).
%alter function funcA964(a:int4, b:int4 -> c:int4) {}
%alter rule r963 B1(a, b, c) :- A1(a, b, c), funcA964(a, b, c).
%alter function funcA965(a:int4, b:int4 -> c:int4) {}
%alter rule r964 B1(a, b, c) :- A1(a, b, c), funcA965(a, b, c).
%alter function funcA966(a:int4, b:int4 -> c:int4) {}
%alter rule r965 B1(a, b, c) :- A1(a, b, c), funcA966(a, b, c).
%alter function funcA967(a:int4, b:int4 -> c:int4) {}
%alter rule r966 B1(a, b, c) :- A1(a, b, c), funcA967(a, b, c).
%alter function funcA968(a:int4, b:int4 -> c:int4) {}
%alter rule r967 B1(a, b, c) :- A1(a, b, c), funcA968(a, b, c).
%alter function funcA969(a:int4, b:int4 -> c:int4) {}
%alter rule r968 B1(a, b, c) :- A1(a, b, c), funcA969(a, b, c).
%alter function funcA970(a:int4, b:int4 -> c:int4) {}
%alter rule r969 B1(a, b, c) :- A1(a, b, c), funcA970(a, b, c).
%alter function funcA971(a:int4, b:int4 -> c:int4) {}
%alter rule r970 B1(a, b, c) :- A1(a, b, c), funcA971(a, b, c).
%alter function funcA972(a:int4, b:int4 -> c:int4) {}
%alter rule r971 B1(a, b, c) :- A1(a, b, c), funcA972(a, b, c).
%alter function funcA973(a:int4, b:int4 -> c:int4) {}
%alter rule r972 B1(a, b, c) :- A1(a, b, c), funcA973(a, b, c).
%alter function funcA974(a:int4, b:int4 -> c:int4) {}
%alter rule r973 B1(a, b, c) :- A1(a, b, c), funcA974(a, b, c).
%alter function funcA975(a:int4, b:int4 -> c:int4) {}
%alter rule r974 B1(a, b, c) :- A1(a, b, c), funcA975(a, b, c).
%alter function funcA976(a:int4, b:int4 -> c:int4) {}
%alter rule r975 B1(a, b, c) :- A1(a, b, c), funcA976(a, b, c).
%alter function funcA977(a:int4, b:int4 -> c:int4) {}
%alter rule r976 B1(a, b, c) :- A1(a, b, c), funcA977(a, b, c).
%alter function funcA978(a:int4, b:int4 -> c:int4) {}
%alter rule r977 B1(a, b, c) :- A1(a, b, c), funcA978(a, b, c).
%alter function funcA979(a:int4, b:int4 -> c:int4) {}
%alter rule r978 B1(a, b, c) :- A1(a, b, c), funcA979(a, b, c).
%alter function funcA980(a:int4, b:int4 -> c:int4) {}
%alter rule r979 B1(a, b, c) :- A1(a, b, c), funcA980(a, b, c).
%alter function funcA981(a:int4, b:int4 -> c:int4) {}
%alter rule r980 B1(a, b, c) :- A1(a, b, c), funcA981(a, b, c).
%alter function funcA982(a:int4, b:int4 -> c:int4) {}
%alter rule r981 B1(a, b, c) :- A1(a, b, c), funcA982(a, b, c).
%alter function funcA983(a:int4, b:int4 -> c:int4) {}
%alter rule r982 B1(a, b, c) :- A1(a, b, c), funcA983(a, b, c).
%alter function funcA984(a:int4, b:int4 -> c:int4) {}
%alter rule r983 B1(a, b, c) :- A1(a, b, c), funcA984(a, b, c).
%alter function funcA985(a:int4, b:int4 -> c:int4) {}
%alter rule r984 B1(a, b, c) :- A1(a, b, c), funcA985(a, b, c).
%alter function funcA986(a:int4, b:int4 -> c:int4) {}
%alter rule r985 B1(a, b, c) :- A1(a, b, c), funcA986(a, b, c).
%alter function funcA987(a:int4, b:int4 -> c:int4) {}
%alter rule r986 B1(a, b, c) :- A1(a, b, c), funcA987(a, b, c).
%alter function funcA988(a:int4, b:int4 -> c:int4) {}
%alter rule r987 B1(a, b, c) :- A1(a, b, c), funcA988(a, b, c).
%alter function funcA989(a:int4, b:int4 -> c:int4) {}
%alter rule r988 B1(a, b, c) :- A1(a, b, c), funcA989(a, b, c).
%alter function funcA990(a:int4, b:int4 -> c:int4) {}
%alter rule r989 B1(a, b, c) :- A1(a, b, c), funcA990(a, b, c).
%alter function funcA991(a:int4, b:int4 -> c:int4) {}
%alter rule r990 B1(a, b, c) :- A1(a, b, c), funcA991(a, b, c).
%alter function funcA992(a:int4, b:int4 -> c:int4) {}
%alter rule r991 B1(a, b, c) :- A1(a, b, c), funcA992(a, b, c).
%alter function funcA993(a:int4, b:int4 -> c:int4) {}
%alter rule r992 B1(a, b, c) :- A1(a, b, c), funcA993(a, b, c).
%alter function funcA994(a:int4, b:int4 -> c:int4) {}
%alter rule r993 B1(a, b, c) :- A1(a, b, c), funcA994(a, b, c).
%alter function funcA995(a:int4, b:int4 -> c:int4) {}
%alter rule r994 B1(a, b, c) :- A1(a, b, c), funcA995(a, b, c).
%alter function funcA996(a:int4, b:int4 -> c:int4) {}
%alter rule r995 B1(a, b, c) :- A1(a, b, c), funcA996(a, b, c).
%alter function funcA997(a:int4, b:int4 -> c:int4) {}
%alter rule r996 B1(a, b, c) :- A1(a, b, c), funcA997(a, b, c).
%alter function funcA998(a:int4, b:int4 -> c:int4) {}
%alter rule r997 B1(a, b, c) :- A1(a, b, c), funcA998(a, b, c).
%alter function funcA999(a:int4, b:int4 -> c:int4) {}
%alter rule r998 B1(a, b, c) :- A1(a, b, c), funcA999(a, b, c).
%alter function funcA1000(a:int4, b:int4 -> c:int4) {}
%alter rule r999 B1(a, b, c) :- A1(a, b, c), funcA1000(a, b, c).
%alter function funcA1001(a:int4, b:int4 -> c:int4) {}
%alter rule s0 B1(a, b, c) :- A1(a, b, c), funcA1001(a, b, c).
%alter function funcA1002(a:int4, b:int4 -> c:int4) {}
%alter rule s1 B1(a, b, c) :- A1(a, b, c), funcA1002(a, b, c).
%alter function funcA1003(a:int4, b:int4 -> c:int4) {}
%alter rule s2 B1(a, b, c) :- A1(a, b, c), funcA1003(a, b, c).
%alter function funcA1004(a:int4, b:int4 -> c:int4) {}
%alter rule s3 B1(a, b, c) :- A1(a, b, c), funcA1004(a, b, c).
%alter function funcA1005(a:int4, b:int4 -> c:int4) {}
%alter rule s4 B1(a, b, c) :- A1(a, b, c), funcA1005(a, b, c).
%alter function funcA1006(a:int4, b:int4 -> c:int4) {}
%alter rule s5 B1(a, b, c) :- A1(a, b, c), funcA1006(a, b, c).
%alter function funcA1007(a:int4, b:int4 -> c:int4) {}
%alter rule s6 B1(a, b, c) :- A1(a, b, c), funcA1007(a, b, c).
%alter function funcA1008(a:int4, b:int4 -> c:int4) {}
%alter rule s7 B1(a, b, c) :- A1(a, b, c), funcA1008(a, b, c).
%alter function funcA1009(a:int4, b:int4 -> c:int4) {}
%alter rule s8 B1(a, b, c) :- A1(a, b, c), funcA1009(a, b, c).
%alter function funcA1010(a:int4, b:int4 -> c:int4) {}
%alter rule s9 B1(a, b, c) :- A1(a, b, c), funcA1010(a, b, c).
%alter function funcA1011(a:int4, b:int4 -> c:int4) {}
%alter rule s10 B1(a, b, c) :- A1(a, b, c), funcA1011(a, b, c).
%alter function funcA1012(a:int4, b:int4 -> c:int4) {}
%alter rule s11 B1(a, b, c) :- A1(a, b, c), funcA1012(a, b, c).
%alter function funcA1013(a:int4, b:int4 -> c:int4) {}
%alter rule s12 B1(a, b, c) :- A1(a, b, c), funcA1013(a, b, c).
%alter function funcA1014(a:int4, b:int4 -> c:int4) {}
%alter rule s13 B1(a, b, c) :- A1(a, b, c), funcA1014(a, b, c).
%alter function funcA1015(a:int4, b:int4 -> c:int4) {}
%alter rule s14 B1(a, b, c) :- A1(a, b, c), funcA1015(a, b, c).
%alter function funcA1016(a:int4, b:int4 -> c:int4) {}
%alter rule s15 B1(a, b, c) :- A1(a, b, c), funcA1016(a, b, c).
%alter function funcA1017(a:int4, b:int4 -> c:int4) {}
%alter rule s16 B1(a, b, c) :- A1(a, b, c), funcA1017(a, b, c).
%alter function funcA1018(a:int4, b:int4 -> c:int4) {}
%alter rule s17 B1(a, b, c) :- A1(a, b, c), funcA1018(a, b, c).
%alter function funcA1019(a:int4, b:int4 -> c:int4) {}
%alter rule s18 B1(a, b, c) :- A1(a, b, c), funcA1019(a, b, c).
%alter function funcA1020(a:int4, b:int4 -> c:int4) {}
%alter rule s19 B1(a, b, c) :- A1(a, b, c), funcA1020(a, b, c).
%alter function funcA1021(a:int4, b:int4 -> c:int4) {}
%alter rule s20 B1(a, b, c) :- A1(a, b, c), funcA1021(a, b, c).
%alter function funcA1022(a:int4, b:int4 -> c:int4) {}
%alter rule s21 B1(a, b, c) :- A1(a, b, c), funcA1022(a, b, c).
%alter function funcA1023(a:int4, b:int4 -> c:int4) {}
%alter rule s22 B1(a, b, c) :- A1(a, b, c), funcA1023(a, b, c).
%alter function funcA1024(a:int4, b:int4 -> c:int4) {}
%alter rule s23 B1(a, b, c) :- A1(a, b, c), funcA1024(a, b, c).
