/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDB 503.1.0 迭代四热补丁卸载-执行测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2023.10.16]
*****************************************************************************/
#include "hotpatchunload.h"
#include "DatalogRun.h"

using namespace std;

class hotpatchrol_002_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatchrol_002_test::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void hotpatchrol_002_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
}

class hotpatchrol_002_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatchrol_002_test2::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void hotpatchrol_002_test2::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 001.patch.d新增可更新输入表（触发重做），加载原so，写入数据，
 加载升级so，写入数据，加载回滚so，校验数据
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2023103008678
// DTS2023103106201
// 第一次新增1个输入表投影到null(0)表，upgradeVersion不变
/* ****************************************************************************
 Description  : 002.分两个步升级新增输入表，第一次升级新增1个输入表null(0):-C,
 对C表写入数据，第二次升级修改规则out1:-inp1,C,校验数据，写入数据，依次加载回滚so，校验数据
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 2);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 查热补丁视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn5[recordNum] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}};
    // 校验数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01));
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 查看新增的表inp2不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "inp2",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.patch.d新增普通udf，加载原so，写入数据，加载升级so，写入数据，加载回滚so，校验数据
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addudf001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 对升级后的输入表写入数据
    C3Int8T objIn5[2] = {{10, 100, 5, 5, 10}, {3, 100, 6, 6, 6}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn5, 2, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    C3Int8T objIn6[recordNum + 2] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}, {10, upVerVal, 5, 5, 10}, {3, upVerVal, 6, 6, 6}};
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 5, 10}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read two complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}, {10, upVerVal2, 5, 5, 10}, {3, upVerVal2, 6, 6, 6}};
    C3Int8T objIn9[recordNum + 2] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}, {1, upVerVal3, 5, 5, 10}, {1, upVerVal3, 6, 6, 6}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn8, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn9, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.patch.d修改普通udf的实现，加载原so，写入数据，加载升级so，写入数据，加载回滚so，校验数据
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 3] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 2, 2, 4}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 对升级后的输入表写入数据
    C3Int8T objIn5[2] = {{10, 100, 5, 5, 10}, {3, 100, 6, 6, 36}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn5, 2, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    C3Int8T objIn6[recordNum + 2] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}, {10, upVerVal, 5, 5, 10}, {3, upVerVal, 6, 6, 36}};
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 6, 6, 36}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read two complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}, {10, upVerVal2, 5, 5, 10}, {3, upVerVal2, 6, 6, 36}};
    C3Int8T objIn9[recordNum - 1] = {{1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4}, {1, upVerVal3, 4, 4, 8},
        {1, upVerVal3, 5, 5, 10}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn8, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn9, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.patch.d修改规则，加载原so，写入数据，加载升级so，写入数据，加载回滚so，校验数据
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 2}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 6}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 对升级后的输入表写入数据
    C3Int8T objIn5[2] = {{10, 100, 5, 5, 10}, {3, 100, 6, 6, 36}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn5, 2, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    C3Int8T objIn6[recordNum + 2] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}, {10, upVerVal, 5, 5, 10}, {3, upVerVal, 6, 6, 36}};
    C3Int8T objIn7[recordNum + 2] = {{1, upVerVal1, 1, 1, 2}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 6}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 10}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read two complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}, {10, upVerVal2, 5, 5, 10}, {3, upVerVal2, 6, 6, 36}};
    C3Int8T objIn9[recordNum - 1] = {{1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4}, {1, upVerVal3, 4, 4, 8},
        {1, upVerVal3, 5, 5, 10}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn8, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn9, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.旧的so含所有类型的表和udf，patch.d触发所有能够重做的场景，加载原so，
 写入数据，加载升级so，写入数据，加载回滚so，校验数据
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_006)
{   
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alltype001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;
    int32_t upVerVal5 = -1;
    int32_t upVerVal6 = -1;
    int32_t upVerVal7 = -1;
    int32_t upVerVal8 = -1;
    int32_t upVerVal9 = -1;
    int32_t upVerVal10 = -1;
    int32_t upVerVal11 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[25] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8},
        {-1, 0, 1, 1, 2}, {-1, 0, 1, 2, 3}, {-1, 0, 2, 2, 4}, {-1, 0, 3, 3, 6}, {-1, 0, 4, 4, 8}, {1, 1, 1, 1, 2},
        {1, 1, 2, 2, 4}, {1, 1, 4, 4, 8}, {1, 1, 5, 4, 9}, {-1, 1, 1, 1, 2}, {1, 0, 1, 1, 2}, {1, 0, 1, 2, 3},
        {-1, 1, 2, 2, 4}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {-1, 1, 4, 4, 8}, {1, 0, 4, 4, 8}, {1, 0, 5, 5, 10},
        {-1, 1, 5, 4, 9}, {1, 0, 5, 4, 9}};
    // pubsub资源型表推送的数据
    C3Int8C1Int4T objPub2[14] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 2, 2, 1}, {1, 0, 2, 2, 4, 1}, {1, 0, 3, 3, 3, 1},
        {1, 0, 4, 4, 8, 1}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifyrs2";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfo023.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 25;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系, pubsub资源表
    char *sub_info02 = NULL;
    readJanssonFile("./schema_file/subInfors2.json", &sub_info02);
    EXPECT_NE((void *)NULL, sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C3Int8C1Int4RescGet;
    userData02->funcType = 1;
    userData02->objLen = 5;
    userData02->obj = objPub2;
    userData02->isResourcePubSub = true;
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallback, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 1}, {1, 0, 2, 2, 1}, {1, 0, 3, 3, 1}, {1, 0, 4, 4, 1}};
    C3Int8C1Int4T objIn4[8] = {{1, 0, 1, 1, 2, 0}, {1, 0, 1, 2, 2, 1}, {1, 0, 2, 2, 4, 2}, {1, 0, 3, 3, 3, 3},
        {1, 0, 4, 4, 8, 4}, {1, 0, 2, 2, 2, 5}, {1, 0, 4, 4, 4, 6}, {1, 0, 8, 8, 8, 7}};
    C3Int8T objTimeout[recordNum] = {{1, 0, 1, 1, -1}, {1, 0, 1, 2, -3}, {2, 0, 2, 6, 4}, {3, 0, 3, 3, 3},
        {4, 0, 4, 4, 8}};
    C3Int8T objTimeout2[recordNum] = {{2, 0, 2, 2, 9}, {2, 0, 2, 4, 7}, {2, 0, 2, 6, 4}, {3, 0, 3, 3, 3},
        {4, 0, 4, 4, 8}};
    // 校验输入表
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp7", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp8", objTimeout, recordNum, C3Int8TimeoutSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待过期表过期
    sleep(3);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp8", objTimeout2, recordNum, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp8 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out4 read complete!!!");
    // 校验out5输出表
    ret = readRecord(g_conn, g_stmt, "out5", objIn4, 8, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out5 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal3);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out4", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal4);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out5", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal4);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp6", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal6);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal7);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "extern", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 报错
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AW_MACRO_EXPECT_EQ_INT(-1, upVerVal8);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp8", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal9);

    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 2, 2}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 2, 4}, {1, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[0] = {};
    C3Int8T objIn8[recordNum] = {{1, upVerVal4, 1, 1, 1}, {1, upVerVal4, 1, 2, 1}, {1, upVerVal4, 2, 2, 1},
        {1, upVerVal4, 3, 3, 1}, {1, upVerVal4, 4, 4, 1}};
    C3Int8T objIn9[recordNum - 2] = {{1, upVerVal5, 1, 1, 2}, {1, upVerVal5, 2, 2, 4}, {1, upVerVal5, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn7, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out4 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out5", objIn9, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out5 read complete!!!");

    // 插入数据
    // 升级后对输入表插入数据
    ret = writeRecord(g_conn, g_stmt, "tb1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn10[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    C3Int8T objIn11[recordNum] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 2}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验out3输出表的数据
    ret = readRecord(g_conn, g_stmt, "out3", objIn11, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 two read complete!!!");
    // 主键读写入的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out2", 5, 4, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTablePKScan(g_conn, g_stmt, "out4", 5, 4, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal10);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal11);

    C3Int8T objIn12[recordNum + 2] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3},
        {1, 0, 4, 4, 8}, {1, 0, 5, 5, 5}, {1, 0, 5, 4, 9}};
    
    ret = readRecord(g_conn, g_stmt, "out2", objIn12, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn12, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out3 read complete!!!");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);

    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    int tableCnt01 = 0;
    // 查看catalog视图
    ret = GetCataDatalogCount(&tableCnt01, "out1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验表的数目
    AW_FUN_Log(LOG_DEBUG, "卸载so之后，tableCnt01 value：%d", tableCnt01);
    AW_MACRO_EXPECT_EQ_INT(tableCnt01, 0);

    // 查看tbm表记录
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_tbmlogName);
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_msglogName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.升级1成功，升级2成功，直接卸载1，预期失败
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 2);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");

    // 加载回滚rollbackV2.so,预期失败
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.升级1成功，升级2成功，卸载2成功，升级3成功，预期upgradeversion字段为2
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 2);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");

    // 加载回滚rollbackV3.so,预期成功
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    (void)ThreadScanPatchView((void *)soName);
    // 加载升级so，预期成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    (void)ThreadScanPatchView((void *)soName);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal2);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.升级1成功，升级2成功，升级3成功，卸载2失败，卸载3成功，再升级4成功，
 预期upgradeversion字段为3
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char patchSoName03[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    char rollbackSoName03[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName03, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName03, "%s/%s_rollbackV4.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 2);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 3);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");
    // 加载第三次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName03));
    AW_FUN_Log(LOG_DEBUG, "after upgrade three scan table");
    (void)ThreadScanPatchView((void *)soName);
    // 加载回滚rollbackV3.so,预期报错
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02));
    AW_FUN_Log(LOG_DEBUG, "after rollback failed scan table");
    // 加载回滚rollbackV4.so,预期成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName03));
    AW_FUN_Log(LOG_DEBUG, "after rollback success scan table");
    (void)ThreadScanPatchView((void *)soName);
    // 加载升级so，预期成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName03));
    AW_FUN_Log(LOG_DEBUG, "after upgrade success scan table");
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, upVerVal2);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 目前有问题，等拉最新代码验证
/* ****************************************************************************
 Description  : 010.升级1成功，升级2失败，升级3成功，卸载3失败，卸载4成功，预期upgradeversion字段值为1
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char patchSoName03[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    char rollbackSoName03[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName03, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName03, "%s/%s_rollbackV4.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 2);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 3);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载第二次升级so，失败
    AW_MACRO_ASSERT_NE_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName03));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two failed scan table");
    // 加载第三次升级so成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade three scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade three out1 read complete!!!");
    
    // 加载回滚rollbackV4.so,预期报错
    AW_MACRO_ASSERT_NE_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName03));
    AW_FUN_Log(LOG_DEBUG, "after rollback failed scan table");
    // 加载回滚rollbackV3.so，预期成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02));
    AW_FUN_Log(LOG_DEBUG, "after rollback success scan table");
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal2);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.升级1新增输入表，升级2新增udf，升级3修改udf实现，升级4修改规则，依次按顺序卸载成功
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char patchSoName03[FILE_PATH] = {0};
    char patchSoName04[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    char rollbackSoName03[FILE_PATH] = {0};
    char rollbackSoName04[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName03, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(patchSoName04, "%s/%s_patchV5.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName03, "%s/%s_rollbackV4.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName04, "%s/%s_rollbackV5.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 2);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 3);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 4);

    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn5[recordNum] = {{2, upVerVal, 1, 1, 1}, {2, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {2, upVerVal, 3, 3, 3}, {2, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对inp2和inp3写入数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 two read complete!!!");
    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal1);
    C3Int8T objIn6[recordNum] = {{1, upVerVal1, 1, 1, 1}, {2, upVerVal1, 1, 2, 3}, {2, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {2, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");
    // 加载第三次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName03));
    AW_FUN_Log(LOG_DEBUG, "after upgrade three scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, upVerVal2);
    C3Int8T objIn7[recordNum] = {{2, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {1, upVerVal2, 3, 3, 3}, {1, upVerVal2, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade three out1 read complete!!!");
    // 加载第四次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName04));
    AW_FUN_Log(LOG_DEBUG, "after upgrade four scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(4, upVerVal3);
    C3Int8T objIn8[recordNum + 3] = {{2, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 1, 2, 2},
        {2, upVerVal3, 2, 2, 4}, {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 3, 3, 9}, {1, upVerVal3, 4, 4, 8},
        {1, upVerVal3, 4, 4, 16}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum + 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade four out1 read complete!!!");
    // 加载回滚rollbackV3.so,预期报错
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02));
    AW_FUN_Log(LOG_DEBUG, "after rollback failed scan table");
    // 加载回滚rollbackV5.so,预期成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName04));
    (void)ThreadScanPatchView((void *)soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName03));
    (void)ThreadScanPatchView((void *)soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02));
    (void)ThreadScanPatchView((void *)soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01));
    (void)ThreadScanPatchView((void *)soName);
    AW_FUN_Log(LOG_DEBUG, "after rollback success scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal4);
    C3Int8T objIn9[recordNum] = {{2, upVerVal4, 1, 1, 1}, {2, upVerVal4, 1, 2, 3}, {2, upVerVal4, 2, 2, 4},
        {2, upVerVal4, 3, 3, 3}, {2, upVerVal4, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn9, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 联调用例补充
// DTS2023102616181
// 需要补充视图字段校验
/* ****************************************************************************
 Description  : 012.升级1新增多个表并修改规则，重做过程中查询视图，升级2新增多个udf并修改规则，重做过程中查询视图，
 第一次降级，重做过程中查询视图，第二次降级，重做过程中查询视图
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    char patchState[128] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview record out1");
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 2);

    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    sleep(1);
    // 查看重做过程中的视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s", g_toolPath, g_viewName,
        g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "VERSION: [v1.0.0]->[v2.0.0]", "ADD_TABLES", "TABLE_NAME: add003");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看视图
    for (int i = 0; i < 10; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "patch state is %s", patchState);
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "first scan view");
    ret = writeRecord(g_conn, g_stmt, "add001", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "add002", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "add003", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "add004", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "add005", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "add006", objIn1, recordNum, C3Int8Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    sleep(1);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s", g_toolPath, g_viewName,
        g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "VERSION: [v2.0.0]->[v3.0.0]", "ADD_UDFS", "UDF_NAME: dtl_ext_func_addfunc006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "patch state is %s", patchState);
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "second scan view");
    // 加载回滚rollbackV5.so,预期成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02));
    sleep(1);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s", g_toolPath, g_viewName,
        g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "VERSION: [v3.0.0]->[v2.0.0]", "DROP_UDFS", "UDF_NAME: dtl_ext_func_addfunc006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "patch state is %s", patchState);
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "third scan view");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01));
    sleep(1);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s", g_toolPath, g_viewName,
        g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "VERSION: [v2.0.0]->[v1.0.0]", "DROP_TABLES", "TABLE_NAME: add006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "patch state is %s", patchState);
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "fourth scan view");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 恢复配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 联调用例，仅在设备上执行
// 执行用例之前，需要更新ylog.d
// soho设备场景(13~18)
/* ****************************************************************************
 Description  : 013.联调用例1，加载升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看热补丁视图直到重做完成,查看tbm表的数据 1550，卸载so，
 查看热补丁视图直到重做完成，查看tbm表的记录数1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog01";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep GE0/0/5中ifIndex的值
    ret = TestGetGE005IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show GE0/0/5 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    // while 循环90次，查找视图是否成功success
    // 后面进行修改
    // 最多重试90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1550);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1550, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt2);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.联调用例2，加载2个升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看tbm表的数据 1560，卸载所有降级so，查询热补丁视图，查看tbm表的记录数
1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog01";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep GE0/0/5中ifIndex的值
    ret = TestGetGE005IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show GE0/0/5 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1560);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1560, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt2);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.联调用例3，加载2个升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看tbm表的数据 1560，卸载所有降级so，查询热补丁视图，查看tbm表的记录数
1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog01";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int cnt3 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep GE0/0/5中ifIndex的值
    ret = TestGetGE005IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show GE0/0/5 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));
    // 和下游约定90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1560);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1560, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1550);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback two hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1550, cnt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt3, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback two hppTable record cnt is %d", cnt3);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt3);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 新增输入表分两个so来加载
/* ****************************************************************************
 Description  : 016.联调用例4，加载升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看热补丁视图直到重做完成,查看tbm表的数据 1550，卸载so，
 查看热补丁视图直到重做完成，查看tbm表的记录数1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog02";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep GE0/0/5中ifIndex的值
    ret = TestGetGE005IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show GE0/0/5 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    // while 循环90次，查找视图是否成功success
    // 后面进行修改
    // 最多重试90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载第二个升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1550);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1550, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt2);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.联调用例5，加载2个升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看tbm表的数据 1560，卸载所有降级so，查询热补丁视图，查看tbm表的记录数
1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog02";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char patchSoName03[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    char rollbackSoName03[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName03, "%s/%s_patchV4.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName03, "%s/%s_rollbackV4.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep GE0/0/5中ifIndex的值
    ret = TestGetGE005IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show GE0/0/5 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName03, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade third patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade third patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1560);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1560, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName03, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback third patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback third patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt2);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.联调用例6，加载2个升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看tbm表的数据 1560，卸载所有降级so，查询热补丁视图，查看tbm表的记录数
1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog02";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char patchSoName03[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    char rollbackSoName03[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int cnt3 = 0;
    int cnt4 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName03, "%s/%s_patchV4.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName03, "%s/%s_rollbackV4.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep GE0/0/5中ifIndex的值
    ret = TestGetGE005IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show GE0/0/5 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));

    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));

    // 和下游约定90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName03, "public"));
    // 和下游约定90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade third patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade third patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1560);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1560, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName03, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1550);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback one hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1550, cnt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt3, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback two hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback third patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback third patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt4, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback three hppTable record cnt is %d", cnt4);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt4);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev GE0/0/5", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 补充测试场景,举一反三补充加载降级so等等
/* ****************************************************************************
 Description  : 019.先加载正常so，再加载错误so报1017000错误码，再加载升级so
**************************************************************************** */
TEST_F(hotpatchrol_002_test, DataLog_054_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char libName2[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)SystemSnprintf("rm -rf %s/xx.so", outputFilePath);
    (void)SystemSnprintf("touch %s/xx.so", outputFilePath);
    (void)SystemSnprintf("chmod 777 %s/xx.so", outputFilePath);
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(libName2, "%s/%s.so", outputFilePath, "xx");
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 加载错误的so，返回1017000错误码
    TestLoadDatalog(libName2);
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// ap款型，热补丁的联调用例
// ap设备场景(20~25)
/* ****************************************************************************
 Description  : 020.联调用例1，加载升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看热补丁视图直到重做完成,查看tbm表的数据 1550，卸载so，
 查看热补丁视图直到重做完成，查看tbm表的记录数1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog03";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpAPStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep Vlanif1中ifIndex的值
    ret = TestGetVlanif1IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show Vlanif1 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    // while 循环90次，查找视图是否成功success
    // 后面进行修改
    // 最多重试90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1550);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1550, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt2);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.联调用例2，加载2个升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看tbm表的数据 1560，卸载所有降级so，查询热补丁视图，查看tbm表的记录数
1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog03";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpAPStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep Vlanif1中ifIndex的值
    ret = TestGetVlanif1IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show Vlanif1 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1560);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1560, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt2);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.联调用例3，加载2个升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看tbm表的数据 1560，卸载所有降级so，查询热补丁视图，查看tbm表的记录数
1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog03";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int cnt3 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpAPStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep Vlanif1中ifIndex的值
    ret = TestGetVlanif1IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show Vlanif1 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));
    // 和下游约定90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1560);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1560, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1550);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback two hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1550, cnt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt3, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback two hppTable record cnt is %d", cnt3);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt3);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 新增输入表分两个so来加载
/* ****************************************************************************
 Description  : 023.联调用例4，加载升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看热补丁视图直到重做完成,查看tbm表的数据 1550，卸载so，
 查看热补丁视图直到重做完成，查看tbm表的记录数1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog04";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
   //  nctl addr show查看配网的ip
    ret = TestGetAddrIpAPStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep Vlanif1中ifIndex的值
    ret = TestGetVlanif1IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show Vlanif1 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    // while 循环90次，查找视图是否成功success
    // 后面进行修改
    // 最多重试90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载第二个升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1550);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1550, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt2);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.联调用例5，加载2个升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看tbm表的数据 1560，卸载所有降级so，查询热补丁视图，查看tbm表的记录数
1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog04";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char patchSoName03[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    char rollbackSoName03[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName03, "%s/%s_patchV4.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName03, "%s/%s_rollbackV4.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpAPStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep Vlanif1中ifIndex的值
    ret = TestGetVlanif1IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show Vlanif1 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName03, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade third patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade third patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1560);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1560, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName03, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback third patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback third patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt2);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.联调用例6，加载2个升级so，查询热补丁视图，查询新增的表存在，对新增的表导入数据，
 查询新增输入表中的数据，查看tbm表的数据 1560，卸载所有降级so，查询热补丁视图，查看tbm表的记录数
1500
**************************************************************************** */
TEST_F(hotpatchrol_002_test2, DataLog_054_002_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
    int ret = 0;
    char outputFilePath[FILE_PATH] = "./datalogFile/ylog04";
    char soName[FILE_PATH] = "ylog";
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char patchSoName03[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};
    char rollbackSoName03[FILE_PATH] = {0};
    int cnt1 = 0;
    int cnt2 = 0;
    int cnt3 = 0;
    int cnt4 = 0;
    int ifIndexVal = 0;
    char addrip[30] = {0};
    char patchState[128] = {0};
    int recordNum = 1;
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName03, "%s/%s_patchV4.so", outputFilePath, soName);

    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName03, "%s/%s_rollbackV4.so", outputFilePath, soName);
    // 新增输入表的结构
    // PortAttrChgEx(ifIndex: int4, attr: int1, value: int4)
    //  nctl addr show查看配网的ip
    ret = TestGetAddrIpAPStr(addrip, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl addr show ip is %s", addrip);
    // 获取nctl if show | grep Vlanif1中ifIndex的值
    ret = TestGetVlanif1IfIndex(&ifIndexVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "nctl if show Vlanif1 ifIndexVal is %d", ifIndexVal);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, "public"));

    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    C1Int1C2Int4T *obj1 = (C1Int1C2Int4T *)malloc(sizeof(C1Int1C2Int4T) * recordNum);
    if (obj1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(obj1, 0, sizeof(C1Int1C2Int4T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        obj1[i].ifIndex = (int32_t)(ifIndexVal);
        obj1[i].attr = 1;
        obj1[i].value = 1550;
        obj1[i].dtlReservedCount = 1;
        obj1[i].upgradeVersion = 1;
    }
    // 对新增的表写入数据Hpf.PortAttrChgEx
    ret = GmcUseNamespace(g_stmt, "public");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Hpf.PortAttrChgEx", obj1, recordNum, C1Int1C2Int4Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表的记录
    system("gmsysview record Hpf.PortAttrChgEx");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "Hpf.PortAttrChgEx", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, "public"));

    // 和下游约定90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName03, "public"));
    // 和下游约定90s
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "upgrade third patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "upgrade third patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GetTbmRecordCnt(&cnt1, ifIndexVal, 1560);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade hppTable record cnt is %d", cnt1);
    AW_MACRO_EXPECT_EQ_INT(1560, cnt1);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName03, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback first patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt2, ifIndexVal, 1550);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback one hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1550, cnt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback second patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt3, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback two hppTable record cnt is %d", cnt2);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, "public"));
    for (int i = 0; i < 90; i++) {
        //  查看热补丁视图的状态
        ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "rollback third patch state is %s", patchState);
        // 重做成功跳出循环
        if (strstr(patchState, "SUCCESS") != NULL) {
            AW_FUN_Log(LOG_STEP, "rollback third patch state is %s", patchState);
            break;
        }
        sleep(1);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep \"PATCH_STATE\" -C 20",
        g_toolPath, g_viewName, g_connServer, soName);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看Hpf.PortAttrChgEx表不存在
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "Hpf.PortAttrChgEx",
        g_connServer);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, " Open vertex label for record unsuccessful", g_errorMsg,
        "ErrorCodeDescription: Undefined table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetTbmRecordCnt(&cnt4, ifIndexVal, 1500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback three hppTable record cnt is %d", cnt4);
    AW_MACRO_EXPECT_EQ_INT(1500, cnt4);
    // 配网
    (void)snprintf(g_command, MAX_CMD_SIZE, "nctl address add %s dev Vlanif1", addrip);
    system(g_command);
    if (obj1 != NULL) {
        free(obj1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
