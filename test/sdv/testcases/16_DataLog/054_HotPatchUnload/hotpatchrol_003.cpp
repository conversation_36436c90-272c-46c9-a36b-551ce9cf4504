/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDB 503.1.0 迭代四热补丁卸载-可靠性测试和权限测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2023.10.16]
*****************************************************************************/
#include "hotpatchunload.h"
#include "DatalogRun.h"

using namespace std;

class hotpatchrol_003_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatchrol_003_test::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    AW_CHECK_LOG_BEGIN();
}
void hotpatchrol_003_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

// userPolicyMode设置为2
class hotpatchrol_003_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};
void hotpatchrol_003_test2::SetUp()
{
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh \"userPolicyMode=2\" ");
    system("sh ${TEST_HOME}/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
}
void hotpatchrol_003_test2::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
}

// userPolicyMode设置为1
class hotpatchrol_003_test3 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};
void hotpatchrol_003_test3::SetUp()
{
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh \"userPolicyMode=1\" ");
    system("sh ${TEST_HOME}/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
}
void hotpatchrol_003_test3::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
}

class hotpatchrol_003_test4 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};
void hotpatchrol_003_test4::SetUp()
{
    system("sh ${TEST_HOME}/tools/modifyCfg.sh \"enableClusterHash=1\"");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void hotpatchrol_003_test4::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 重启服务,避免受其他用例影响
class hotpatchrol_003_test5 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatchrol_003_test5::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    AW_CHECK_LOG_BEGIN();
}
void hotpatchrol_003_test5::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 001.升级1成功，升级2成功，反复卸载升级2多次（2000次）不加sleep，
 查询动态内存/共享内存（完全一致）
**************************************************************************** */
TEST_F(hotpatchrol_003_test5, DataLog_054_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 2000;
#endif

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 2);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");
    // 查热补丁视图，查询视图会导致内存增加，gmsysview查视图要先建视图表
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > /dev/null 2>&1", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    // 返回卸载rollbackV3.so和加载patchV3.so看内存
#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    pthread_t thr_arr[1];
    // 增加热升级视图校验，循环2000次，避免构建超时
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(2);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 查视图看是否已经重做完成，再进行后续操作，需要整改
/* ****************************************************************************
 Description  : 002.将enableDatalogDmlWhenUpgrading设置为0，完成1次升级后，
 线程1批写10条数据（10s），线程2加载卸载so(3s内拿到锁)，预期卸载成功，数据重做完成
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    sleep(11);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 一个线程写datalog输入表，执行20s，降级线程拿到锁(3s内，串行)
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行20s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    sleep(18);
    pthread_create(&thr_arr[1], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不上设备
/* ****************************************************************************
 Description  : 003.将enableDatalogDmlWhenUpgrading设置为0，完成1次升级后，
 线程1批写10条数据（10s），线程2加载卸载so(3s内拿不到锁)，预期卸载失败，数据重做完成
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    sleep(11);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 一个线程写datalog输入表，执行20s，降级线程拿不到锁(3s内，串行)
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行20s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    sleep(1);
    // datalog的DML操作，持锁19s，保证加载升降so 3s内拿不到dataservice锁；仿真15s内拿不到dataservice锁
    pthread_create(&thr_arr[1], NULL, ThreadLoadRollbackSo1, (void *)rollbackSoName);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DML在5*一级hung死时间内，拿不到upgradeInfo锁
/* ****************************************************************************
 Description  : 004.将enableDatalogDmlWhenUpgrading设置为0，完成1次升级后,加载卸载so，
 线程1并发执行insert操作，预期报错
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
#ifdef ENV_RTOSV2X
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode02, g_errorCode03);
#endif
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 15;
    // 写表
    C3Int8T objIn1[recordNum] = {0};
    C3Int8T objIn2[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].c = 2 * i + 2;
        objIn1[i].upgradeVersion = 0;
        objIn1[i].dtlReservedCount = i + 1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].c = 2 * i + 2;
        objIn2[i].upgradeVersion = 0;
        objIn2[i].dtlReservedCount = 1;
    }

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 对输入表写数据
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "insert datalog table test end.");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = upVerVal;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = upVerVal;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so，重做90s
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(1);
    // 一个线程写datalog输入表
    pthread_t thr_arr[1];
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable, NULL);
    pthread_join(thr_arr[0], NULL);
    // 仿真和设备sleep 40s；euler sleep 80s;
#ifdef ENV_RTOSV2X
    sleep(40);
#else
    sleep(80);
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.将enableDatalogDmlWhenUpgrading设置为1，完成1次升级后,加载卸载so，
 线程1并发卡时间执行insert操作，预期报事务锁超时，查看降级线程的后台重做线程重做过程中的视图
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 修改datalogUpgradeFetchSize为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    sleep(31);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 再写7条数据
    C3Int8T objIn5[7] = {0};
    for (int i = 0; i < 7; i++) {
        objIn5[i].a = i + 5;
        objIn5[i].b = i + 5;
        objIn5[i].c = i + 5;
        objIn5[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn5, 7, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(1);
    // 一个线程写datalog输入表
    pthread_t thr_arr[1];
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable1, NULL);
    pthread_join(thr_arr[0], NULL);
    // 查看视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: REDOING");
    // 校验新增rule或者新增udf应该是空的

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 区分环境
    sleep(71);
    AW_FUN_Log(LOG_STEP, "after wait.");
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.将enableDatalogDmlWhenUpgrading设置为0，加载升级so，
 后台升级重做线程执行5s，开启线程加载回滚so，预期直接报错
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    // 修改datalogUpgradeFetchSize为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(1);
    // 加载回滚so，拿不到TimedWLock upgradeInfo锁
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(30);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.2个so，加载升级so，后台升级重做线程执行30s，开启线程加载自身回滚so报错，
 加载另外1个回滚so预期成功
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    char soName1[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName1[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName1[FILE_PATH] = {0};

    // 修改datalogUpgradeFetchSize为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(libName1, "%s/%s.so", outputFilePath, soName1);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName1, "%s/%s_patchV2.so", outputFilePath, soName1);
    (void)sprintf(rollbackSoName1, "%s/%s_rollbackV2.so", outputFilePath, soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName1));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp11", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName1, false, 1);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    sleep(1);
    // 加载回滚so，拿不到TimedWLock upgradeInfo锁
    // 2月19号变更，加载升级so无upgradeInfo锁
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName1));
    // 查看视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "SO_NAME: reliab004", "VERSION: [v2.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(30);
    // 查看加载失败回滚成功中，so的数据是否符合预期
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// Datalog增加长事务处理监控，60S超时自动回滚事务特性合入，设备上无法卡住
/* ****************************************************************************
 Description  : 008.将enableDatalogDmlWhenUpgrading设置为1，完成1次升级后,加载卸载so，
 线程1并发卡时间拿到事务锁批写数据（54s），导致卸载重做线程拿不到事务锁，查看降级线程的后台重做线程重做失败的视图
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(31);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载回滚so，后台重做线程拿不到锁，升级失败
    // 后台重做线程会重试，总共执行6次(事务锁时间10s*6 + 100us + 200us + 400us + 800us)
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    // 2024年3月19日变更：undo和redo合并成1个事务
    usleep(1000 * 5500);
    // 一个线程写datalog输入表
    pthread_t thr_arr[1];
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    pthread_join(thr_arr[0], NULL);

    // euler重做失败，默认时间1s*6 超过7s拿不到
    // 1月29号变更，重试次数为：长事务监控超时默认时间 (60s)  / 事务超时配置时间 + 2
    sleep(28);
    // 设备上，查看热补丁视图，重做成功, 能拿到锁
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.定义2个相同旧.d,两个patch.d不同，加载第一个so以及对应的升级so，
 加载第二个对应的卸载so，预期报错
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab005";
    char soName1[FILE_PATH] = "reliab006";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName1[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName1[FILE_PATH] = {0};

    // 修改datalogUpgradeFetchSize为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(libName1, "%s/%s.so", outputFilePath, soName1);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName1, "%s/%s_patchV2.so", outputFilePath, soName1);
    (void)sprintf(rollbackSoName1, "%s/%s_rollbackV2.so", outputFilePath, soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName1, false, 1);
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(1);
    (void)SystemSnprintf("rm -rf %s", rollbackSoName);
    // 将rollbackSoName1的内容拷到rollbackSoName里面
    (void)SystemSnprintf("cp -f %s %s", rollbackSoName1, rollbackSoName);
    // 加载reliab006回滚so，预期报错
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    // 查看视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.userPolicyMode设置为2，升级1新增输入表，加载回滚so时，
 未赋drop系统权限，报18004无权限
**************************************************************************** */
TEST_F(hotpatchrol_003_test2, DataLog_054_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysVertexnodrop.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");
    // 撤销原先系统权限，加入未含drop的权限，加载报无权限
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile1);
    system(g_command);
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1018004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 有问题，赋对象权限还是加载不了so
/* ****************************************************************************
 Description  : 011.userPolicyMode设置为2，升级1新增输入表，加载回滚so时，
 未赋drop对象权限，报18004无权限
**************************************************************************** */
TEST_F(hotpatchrol_003_test2, DataLog_054_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysnoVertex.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");
    // 撤销原先系统权限，加入未含drop的权限的对象权限啊，加载报无权限
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile1);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, ObjPrivFile);
    system(g_command);
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    system(g_command);
    ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1018004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.userPolicyMode设置为2，升级1新增输入表，加载回滚so时，
 赋drop系统权限，加载成功，无18004告警日志
**************************************************************************** */
TEST_F(hotpatchrol_003_test2, DataLog_054_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysnoVertex.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Command type: import_datalog", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.userPolicyMode设置为1，升级1新增输入表，加载回滚so时，
 未赋drop系统权限，能够加载成功，日志报18004无权限
**************************************************************************** */
TEST_F(hotpatchrol_003_test3, DataLog_054_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysVertexnodrop.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");
    // 撤销原先系统权限，加入未含drop的权限，加载报无权限
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile1);
    system(g_command);
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Command type: import_datalog", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.userPolicyMode设置为1，升级1新增输入表，加载回滚so时，
 未赋drop对象权限，能够加载成功，日志报18004无权限
**************************************************************************** */
TEST_F(hotpatchrol_003_test3, DataLog_054_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysnoVertex.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");
    // 撤销原先系统权限，加入未含drop的权限的对象权限啊，加载报无权限
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile1);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, ObjPrivFile);
    system(g_command);
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Command type: import_datalog", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.userPolicyMode设置为1，升级1新增输入表，加载回滚so时，
 赋drop系统权限，加载成功，无18004告警日志
**************************************************************************** */
TEST_F(hotpatchrol_003_test3, DataLog_054_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysnoVertex.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Command type: import_datalog", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.userPolicyMode设置为2，升级1新增udf修改udf实现修改rule，
 加载回滚so时，未赋系统权限，报18004无权限
**************************************************************************** */
TEST_F(hotpatchrol_003_test2, DataLog_054_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab008";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysVertexnodrop.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum] = {{2, 0, 1, 1, 1}, {2, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {2, 0, 3, 3, 3}, {2, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal2);
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn5[recordNum - 3] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 2, 2, 4}};
    C3Int8T objIn6[recordNum - 1] = {
        {1, upVerVal2, 1, 1, 10}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 2, 4}, {1, upVerVal2, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    // 撤销原先系统权限，加载报无权限
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1018004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.userPolicyMode设置为2，升级1新增udf修改udf实现修改rule，
 加载回滚so时，赋系统权限，加载成功，无18004告警日志
**************************************************************************** */
TEST_F(hotpatchrol_003_test2, DataLog_054_003_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab008";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysnoVertex.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;
    int32_t upVerVal5 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum] = {{2, 0, 1, 1, 1}, {2, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {2, 0, 3, 3, 3}, {2, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal2);
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn5[recordNum - 3] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 2, 2, 4}};
    C3Int8T objIn6[recordNum - 1] = {
        {1, upVerVal2, 1, 1, 10}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 2, 4}, {1, upVerVal2, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Command type: import_datalog", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal4);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal5);
    // 校验加载回滚so之后的数据
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {2, upVerVal3, 2, 2, 4},
        {3, upVerVal3, 3, 3, 3}, {4, upVerVal3, 4, 4, 8}};
    C3Int8T objIn8[recordNum - 2] = {{1, upVerVal4, 1, 2, 3}, {1, upVerVal4, 2, 2, 4}, {1, upVerVal4, 4, 4, 8}};
    C3Int8T objIn9[recordNum] = {{2, upVerVal5, 1, 1, 1}, {2, upVerVal5, 1, 2, 3}, {2, upVerVal5, 2, 2, 4},
        {2, upVerVal5, 3, 3, 3}, {2, upVerVal5, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn9, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.userPolicyMode设置为1，升级1新增udf修改udf实现修改rule，
 加载回滚so时，未赋系统权限，能够加载成功，日志报18004无权限
**************************************************************************** */
TEST_F(hotpatchrol_003_test3, DataLog_054_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab008";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysVertexnodrop.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum] = {{2, 0, 1, 1, 1}, {2, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {2, 0, 3, 3, 3}, {2, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal2);
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn5[recordNum - 3] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 2, 2, 4}};
    C3Int8T objIn6[recordNum - 1] = {
        {1, upVerVal2, 1, 1, 10}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 2, 4}, {1, upVerVal2, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    // 撤销原先系统权限，加载报无权限
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Command type: import_datalog", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.userPolicyMode设置为1，升级1新增udf修改udf实现修改rule，
 加载回滚so时，赋系统权限，加载成功，无18004告警日志
**************************************************************************** */
TEST_F(hotpatchrol_003_test3, DataLog_054_003_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab008";
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char SysPrivFile1[128] = "privFile/sysnoVertex.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;
    int32_t upVerVal5 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum] = {{2, 0, 1, 1, 1}, {2, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {2, 0, 3, 3, 3}, {2, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, true, 1);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal2);
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn5[recordNum - 3] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 2, 2, 4}};
    C3Int8T objIn6[recordNum - 1] = {
        {1, upVerVal2, 1, 1, 10}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 2, 4}, {1, upVerVal2, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    // 加载回滚so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer,
        rollbackSoName);
    ret = executeCommand(g_command, "Command type: import_datalog", "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal4);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal5);
    // 校验加载回滚so之后的数据
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {2, upVerVal3, 2, 2, 4},
        {3, upVerVal3, 3, 3, 3}, {4, upVerVal3, 4, 4, 8}};
    C3Int8T objIn8[recordNum - 2] = {{1, upVerVal4, 1, 2, 3}, {1, upVerVal4, 2, 2, 4}, {1, upVerVal4, 4, 4, 8}};
    C3Int8T objIn9[recordNum] = {{2, upVerVal5, 1, 1, 1}, {2, upVerVal5, 1, 2, 3}, {2, upVerVal5, 2, 2, 4},
        {2, upVerVal5, 3, 3, 3}, {2, upVerVal5, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn9, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2023111009024
// 下游问题
/* ****************************************************************************
 Description  : 020.对聚簇表先循环进行批量插入(insert)1条记录，正好达到扩容时，对聚簇表循环批量插入(replace)1条记录
**************************************************************************** */
TEST_F(hotpatchrol_003_test4, DataLog_054_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, g_ip4forwardName);
    int cnt01 = -1;
    int cnt02 = -1;
    int writeNum = 0;
    //创表、预置数据
    ret = CreateTestforwardLabel();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看视图，什么时候触发扩容
    for (int i = 0; i < 1000; i++) {
        writeip4fowardDataSync(conn, stmt, i, i + 1, 1);
        ret = TestGetSegmentPageCnt(&cnt02, g_ip4forwardName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 刚写入1条数据时，SEGMENT_PAGE_COUNT值
        if (i == 0) {
            cnt01 = cnt02;
        }
        // 正好触发扩容
        if (cnt02 != cnt01) {
            writeNum = i;
            break;
        }
    }
    // 当前soho、ap不支持聚簇容器，AC支持聚簇容器
    AW_FUN_Log(LOG_DEBUG, "扩容时，writeNum is %d", writeNum);
    AW_MACRO_EXPECT_NE_INT(0, writeNum);
    GmcDropVertexLabel(stmt, g_ip4forwardName);
    //创表、预置数据
    ret = CreateTestforwardLabel();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 循环10000次，每次批写1条数据
    for (int i = 0; i < 10000; i++) {
        if (i < writeNum) {
            writeip4fowardDataSync(conn, stmt, i, i + 1, 1);
        } else {
            writeip4fowardDataInsertSync(conn, stmt, i, i + 1, 1);
        }
    }
    ret = GmcDropVertexLabel(stmt, g_ip4forwardName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DB启动会占用3个后台线程，后台线程隐性规格14个；创建过期表会创建1个；所以最多对10个so同时进行升级
/* ****************************************************************************
 Description  : 021.加载12个原始so，分别对12个原始so升级,后台线程会超上限报1010000
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char inputFilePath[FILE_PATH] = "./datalogFile/worker";
    char outputFilePath[FILE_PATH] = "./datalogFile/worker";
    char libName1[FILE_PATH] = {0};
    char soName1[FILE_PATH] = "test010";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    // 修改datalogUpgradeFetchSize为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);
    (void)sprintf(libName1, "%s/%s.so", outputFilePath, "test010");

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);

    // 循环执行5次，2次不是完全相等，报错
    while (cycleCnt < totalCycleCnt) {
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 加载12个so
        for (int i = 1; i < 13; i++) {
            char libName[FILE_PATH] = {0};
            (void)sprintf(libName, "%s/%s%03d.so", outputFilePath, "test", i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        }
        // 插入数据
        int recordNum = 5;
        C3Int8T objIn1[recordNum] = {
            {1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
        for (int i = 1; i < 13; i++) {
            char tablename[512] = {0};
            snprintf(tablename, 512, "inpA%d", i);
            ret = writeRecord(conn, stmt, tablename, objIn1, recordNum, C3Int8Set, false);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 让过期表中的数据全部过期掉，对应的升级后台线程很快释放
        // 要是不过期的话，后面需要等待20s+20s+3s才能让其过期完成
        sleep(4);
        // 加载升级so之前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 加载升级so
        for (int i = 1; i < 13; i++) {
            char patchSoName[512] = {0};
            snprintf(patchSoName, 512, "%s/%s%03d_patchV2.so", outputFilePath, "test", i);
            ret = TestLoadUpgradeSo(patchSoName);
        }
        sleep(20);
        // 加载降级so
        for (int i = 1; i < 13; i++) {
            char rollbackSoName[512] = {0};
            snprintf(rollbackSoName, 512, "%s/%s%03d_rollbackV2.so", outputFilePath, "test", i);
            ret = TestLoadRollbackSo(rollbackSoName);
        }
        sleep(20);
        // 加载降级so之后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "after load rollback so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planCacheDynMem01, planCacheDynMem01) != 0) {
            failCnt++;
        }
        // 卸载
        for (int i = 1; i < 13; i++) {
            char soName[FILE_PATH] = {0};
            (void)sprintf(soName, "%s%03d", "test", i);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(soName));
        }
        cycleCnt++;
        // 清除catalog资源数据
        sleep(10);
        // 断开连接
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 并发加固
// DML在5*一级hung死时间内，拿到upgradeInfo锁
/* ****************************************************************************
 Description  : 022.将enableDatalogDmlWhenUpgrading设置为0，升级重做过程中dml拿到锁,
 降级重做过程中dml拿到锁
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 创建4个线程  线程1：升级 线程2：dml操作 线程3：降级 线程4：dml操作
    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载第一次升级so
    pthread_t thr_arr[4];
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    sleep(1);
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable2, NULL);
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    sleep(12);
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum + 1] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}, {1, upVerVal, 5, 5, 5}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so
    pthread_create(&thr_arr[2], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);
    sleep(1);
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable3, NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);
    sleep(14);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 线程1升级，线程2不停地查热补丁视图，线程3降级，线程4不停地查热补丁视图
/* ****************************************************************************
 Description  : 022.将enableDatalogDmlWhenUpgrading设置为0，升级重做过程中dml拿到锁,
 降级重做过程中dml拿到锁
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 创建4个线程：线程1升级，线程2不停地查热补丁视图，线程3降级，线程4不停地查热补丁视图
    // 编译生成升级so和回滚so
    // 屏蔽CompileUpgradeAndRollBackTest(inputFilePath, outputFilePath, soName, false, 1);
    // 加载第一次升级so
    pthread_t thr_arr[4];
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    sleep(2);
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[1], NULL, ThreadScanPatchView, (void *)soName);
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so
    pthread_create(&thr_arr[2], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);
    sleep(2);
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[3], NULL, ThreadScanPatchView, (void *)soName);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

bool g_datalog054InjectMode = false;  // 是否为故障注入模式 0 否 -1 是
char g_datalog054InjectModeSo[256] = "../../../../../inject/injection_tools/datalog_upgrade_inject/"
                                     "libdatalog_upgrade_inject.so";  // 故障模式对应需替换函数so路径
void IsFileExist()
{
    FILE *fp;
    fp = fopen(g_datalog054InjectModeSo, "r");
    if (fp == NULL) {
        g_datalog054InjectMode = false;
        printf("File %s does not exist.", g_datalog054InjectModeSo);
    } else {
        printf("File %s exists.", g_datalog054InjectModeSo);
        g_datalog054InjectMode = true;
        fclose(fp);
    }
}

/* ****************************************************************************
 Description  : 024.线程创建失败，加载热升级so报错
 （通过inject注入，错误码由 datalog_upgrade_inject.c 打桩函数决定，无注入时，加载热升级so不报错）
**************************************************************************** */
TEST_F(hotpatchrol_003_test, DataLog_054_003_024)
{
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_MAX_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    IsFileExist();

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    (void)snprintf(
        loadCommand, commandSize, "%s/gmimport -s %s -c datalog -upgrade %s", g_toolPath, g_connServer, patchSoName);
    if (g_datalog054InjectMode) {
        TestInjectCommand();
        // inject function return 1029033
        ret = Debug_executeCommand(loadCommand, "Import datalog file unsucc, filePath", "ret = 1029033");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = Debug_executeCommand(loadCommand, "Import datalog file unsucc, filePath", "ret = 1029033");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestUnInjectCommand();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    }

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
