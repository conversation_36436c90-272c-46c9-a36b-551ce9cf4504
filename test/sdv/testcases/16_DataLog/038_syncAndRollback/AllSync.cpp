/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDBV503 迭代一Datalog支持全同步请求和事务回滚
 History      :
 03 特性交互
               001  使用gmimport导入数据失败导致回滚
 04 可靠性
               001  卸载和加载的过程中并发执行DML操作
               002  pubsub返回多次响应（100+），构造大数据，查看内存的变化
 Author       : youwanyong/ywx1157510
 Create       : [2023.04.11]
*****************************************************************************/

#include "gtest/gtest.h"
#include "t_datacom_lite.h"

using namespace std;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
#define FILE_PATH 512
#define MAX_NAME_LENGTH 512
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char g_outputDir[FILE_PATH] = "AllSyncDataLogFile";

class outTableIsOrd : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void outTableIsOrd::SetUp()
{
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(0);
}
void outTableIsOrd::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 加载so文件
int LoadSoFile(const char *soName)
{
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, FILE_PATH, "./%s/%s.so", g_outputDir, soName);
    return TestLoadDatalog(g_command);
}

int TestViewData(char *cmd, char **result)
{
    int ret = 0;
    FILE *fd = popen(cmd, "r");
    int size = 102400;
    *result = (char *)malloc(sizeof(char) * size);
    memset(*result, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat((char *)*result, buf);
    }

    ret = pclose(fd);
    return ret;
}

// 校验数据
int ScanTableData(const char *expectOutput, char *tableName, const char *tableNameSpace = g_testNameSpace)
{
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s > test.txt", g_toolPath, tableName, g_testNameSpace);
    printf("%s\n", g_command);
    int ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    char *actualResult = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat test.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &actualResult);
    printf("%s\n", actualResult);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预期值比实际值多一个字符
    const char *result = strstr(actualResult, expectOutput);
    if (result != NULL) {
        EXPECT_STRNE(NULL, result);
        free(actualResult);
    } else {
        free(actualResult);
        AW_FUN_Log(LOG_STEP, "There is no data what you need!");
        return -1;
    }
    return ret;
}

/* ****************************************************************************
 Description  : 001  使用gmimport导入数据失败导致回滚
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_038_003_001)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A1";
    char tableB[] = "rsc0";

    AW_FUN_Log(LOG_STEP, "1.加载so");
    char nsName[128] = "AllSync003_001";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "2.导入异常数据事务回滚");

    // 向输入表导入数据，查询数据
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f ./AllSyncDataLogFile/A1.gmdata -t A1 -ns public", g_toolPath);
    ret = executeCommand(g_command, "insert data failed", "totalNum: 12", "successNum: 7", "ret = 1009021");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "3.查表中数据");
    const char *expect_output = R"(index = 0, check_version = 0
{
    "a": 1,
    "b": 1,
    "dtlReservedCount": 1
}
index = 1, check_version = 0
{
    "a": 2,
    "b": 2,
    "dtlReservedCount": 1
}
index = 2, check_version = 0
{
    "a": 3,
    "b": 1,
    "dtlReservedCount": 1
}
index = 3, check_version = 0
{
    "a": 4,
    "b": 1,
    "dtlReservedCount": 1
}
index = 4, check_version = 0
{
    "a": 5,
    "b": 2,
    "dtlReservedCount": 1
}
index = 5, check_version = 0
{
    "a": 6,
    "b": 1,
    "dtlReservedCount": 1
}
index = 6, check_version = 0
{
    "a": 7,
    "b": 1,
    "dtlReservedCount": 1
}
)";
    ret = ScanTableData(expect_output, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end");
}

// 批量写 表  (a , b)
int BatchInsert(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][3], int dataNum)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        // a
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        value1 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);

        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    if (ret) {
        testGmcGetLastError();
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int PkUpdate(GmcStmtT *stmt, char *labelName, int32_t count[][3], int32_t dataNum, int32_t realAffectRows = 1)
{
    int32_t affectRows = 0;
    int32_t ret = 0;
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        int32_t value0 = count[i][0];
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value0, sizeof(int32_t));
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        int32_t value1 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        testGmcGetLastError();
        return GMERR_OK;
    }
}

bool isStop = false;
void *INORUNSTALLSO(void *args)
{
    char nsName[128] = "AllSync004_001";
    int cycle = 10;
    int ret;
    while (cycle > 0) {
        // 卸载同名datalog.so
        TestUninstallDatalog(nsName, NULL, false);

        // .d文件加载：创建表和连接规则
        ret = LoadSoFile(nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        // 卸载datalog.so
        ret = TestUninstallDatalog(nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        cycle--;
    }
    isStop = true;
}

void *DMLINTOINPUT(void *args)
{

    int ret;
    char tableA[] = "A1";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    while (!isStop) {
        // 批量插入一批数据，count+
        int32_t count1[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}};

        // 写入数据
        ret = BatchInsert(conn, stmt, tableA, count1, 3);
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        sleep(1);
        // 更新
        int32_t count2[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}};
        int record = 3;
        ret = PkUpdate(stmt, tableA, count2, record);
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        sleep(1);
        //  批量插入一批数据，count-
        int32_t count3[][3] = {{1, 2, -1}, {2, 3, -1}, {3, 2, -1}};
        ret = BatchInsert(conn, stmt, tableA, count3, 3);
        AW_MACRO_EXPECT_NE_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *SCANINTOOUTPUT(void *args)
{
    while (!isStop) {
        system("gmsysview record A1");
        sleep(1);
        system("gmsysview record C1");
        sleep(1);
        system("gmsysview record rsc0");
    }
}

// PTL_DATALOG_SO_INFO视图校验方式
int querySoView(
    const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL, const char *v4 = NULL, const char *v5 = NULL)
{
    char *viewName = (char *)"V\\$PTL_DATALOG_SO_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q %s -f SO_NAME=%s -view_fmt json ", viewName, v1);
    system(g_command);
    return executeCommand(g_command, v1, v2, v3, v4, v5);
}

/* ****************************************************************************
 Description  : 001  卸载和加载的过程中并发执行DML操作// 预期服务在即可
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_038_004_001)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A1";
    char tableB[] = "rsc0";

    AW_FUN_Log(LOG_STEP, "1.加载so");
    char nsName[128] = "AllSync004_001";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // 开启多个线程
    // 线程一加载so卸载so
    // 线程二向表中写入数据
    // 线程三查询表中数据
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    int ret = pthread_create(&client_thr[0], NULL, INORUNSTALLSO, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, DMLINTOINPUT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_create(&client_thr[2], NULL, SCANINTOOUTPUT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = querySoView("AllSync004_001");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record A1", g_toolPath);
    ret = executeCommand(g_command, "ret = 1009010");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record C1", g_toolPath);
    ret = executeCommand(g_command, "ret = 1009010");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record rsc0", g_toolPath);
    ret = executeCommand(g_command, "ret = 1009010");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}
typedef int (*FuncReadId)(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc);
typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    int tableType;  // 0:out 1:resource
    int funcType;   // 0:id 1:struct
    char *nsName;
    union {
        struct {
            FuncReadId readIdFunc;
            int startid;
            int endid;
            int32_t count;
        };
    };
} SnUserDataWithFuncT;
int snCallbackCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    char *nsName = userDefinedData->nsName;
    int tableType = userDefinedData->tableType;
    int funcType = userDefinedData->funcType;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);
    if (userDefinedData->tableType == 0) {  // notify输出表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        RETURN_IFERR(ret);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
        ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        RETURN_IFERR(ret);
    } else if (userDefinedData->tableType == 1) {  // pubsub型资源表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
        RETURN_IFERR(ret);
    }

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    int32_t valueA;
                    int32_t valueB;
                    int32_t valueC;

                    ret = GmcGetVertexPropertyByName(subStmt, "a", &valueA, sizeof(valueA), &eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "b", &valueB, sizeof(valueB), &eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "dtlReservedCount", &valueC, sizeof(valueC), &eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    AW_FUN_Log(LOG_STEP, "labelName: %s  %d  %d  %d", labelName, valueA, valueB, valueC);
                    if (tableType == 0) {  // out table
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 0);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    } else if (tableType == 1) {  // pubsubresource
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 1);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcSubAddRespDML(response, subStmt);
                        RETURN_IFERR(ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    if (userDefinedData->tableType != 2) {
        ret = GmcSendResp(subStmt, response);
        RETURN_IFERR(ret);
        ret = GmcDestroyResp(subStmt, response);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

void snCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = snCallbackCheck(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 订阅相关
#pragma pack(1)
struct DoubleInt4St {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
};
#pragma pack()

int DoubleInt4_getId(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc = false)
{
    DoubleInt4St getObj = {};
    int ret = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'a[DoubleInt4_getId]' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = startid; i < endid; i++) {
        if ((getObj.b == getObj.a)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[DoubleInt4_getId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }
    return ret;
}

int TestCreateSub(GmcConnT *subConn, SnUserDataWithFuncT **userData, const GmcSubConfigT *config, int tableType = 0)
{
    *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    (*userData)->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((*userData)->data, 0, sizeof(SnUserDataT));
    (*userData)->funcType = 0;
    (*userData)->tableType = tableType;
    (*userData)->readIdFunc = DoubleInt4_getId;
    (*userData)->startid = 0;
    (*userData)->endid = 1000;
    (*userData)->count = 1;
    int ret = GmcSubscribe(g_stmt, config, subConn, snCallback, *userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} A;
A g_bufTest[1000] = {0};
// 全表扫描查询
char *g_schemaJson1 = (char *)R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"}
        ]
    } ])";
int ReadTable(GmcStmtT *stmt, char *tableOut)
{
    int ret = 0;
    // scan
    A objOut = (A){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson1, tableOut);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {tableOut, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);

    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, tableOut, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        // out
        g_bufTest[cnt].a = objOut.a;
        g_bufTest[cnt].b = objOut.b;
        g_bufTest[cnt].dtlReservedCount = objOut.dtlReservedCount;
        cnt++;
    }
    deSeriFreeDynMem(&deseriCtx, true);

    return cnt;
}

// 批量写 表  (a , b)
int BatchInsert1000(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][3], int dataNum)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        // a
        int32_t value1 = count[i][0];
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        value1 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        testGmcGetLastError();
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int PkUpdate1000(GmcStmtT *stmt, char *labelName, int32_t count[][3], int32_t dataNum, int32_t realAffectRows = 1)
{
    int32_t affectRows = 0;
    int32_t ret = 0;
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t value0 = count[i][0];
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value0, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t value1 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testGmcGetLastError();
        return GMERR_OK;
    }
}

/* ****************************************************************************
Description  :002  pubsub返回多次响应（100+），构造大数据，查看内存的变化
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_038_004_002)
{
    AW_FUN_Log(LOG_STEP, "test begin");

    // 加载so
    char tableA[] = "A1";
    char tableB[] = "C1";
    char nsName[128] = "AllSync004_002";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    AW_FUN_Log(LOG_STEP, "1.加载so");

    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#ifdef ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB");
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅
    AW_FUN_Log(LOG_STEP, "申请B表订阅");
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    SnUserDataWithFuncT *userData;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./AllSyncDataLogFile/pubsub_precedence.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConn, &userData, &tmp_sub_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_info);

    // 向表中写入数据，订阅接收数据返回
    int32_t count1[1000][3];
    for (int i = 0; i < 1000; i++) {
        for (int j = 0; j < 3; j++) {
            count1[i][j] = i + 1;
        }
    }
    int cycle = 10;
    while (cycle > 0) {

        // 写入2条数据
        for (int i = 0; i < 1000; i++) {
            count1[i][2] = i + 1;
        }
        ret = BatchInsert1000(g_conn, g_stmt, tableA, count1, 1000);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 全表扫描验证数据
        int record = 1000;
        ret = ReadTable(g_stmt, tableA);
        AW_MACRO_EXPECT_EQ_INT(record, ret);
        memset(g_bufTest, 0, 100);

        ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 1000, RECV_TIMEOUT / 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int i = 0; i < 1000; i++) {
            count1[i][2] = -(i + 1);
        }
        // 写入多条数据
        ret = BatchInsert1000(g_conn, g_stmt, tableA, count1, 1000);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 全表扫描验证数据
        record = 0;
        ret = ReadTable(g_stmt, tableA);
        AW_MACRO_EXPECT_EQ_INT(record, ret);
        memset(g_bufTest, 0, 100);

        ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 1000, RECV_TIMEOUT / 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        cycle--;
    }
    ret = GmcUnSubscribe(g_stmt, subName);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, subStmt);
    EXPECT_EQ(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    free(userData);
#ifdef ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB");
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end");
}
