/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : 支持变长5-10K变长byte字段
 Notes        :
 History      :
 Author       : youwanyong/ywx1157510
 Create       : [2023.07.11]
*****************************************************************************/
#include "pubSupByte.h"
#include "t_datacom_lite.h"
// DataLog_041_020 含有隐式规则0x必须加双引号

using namespace std;
class pubSupByte : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void pubSupByte::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN(0);
}
void pubSupByte::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  : 001.transient tuple表（包含是中间表时）支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_001_1";
    char soName2[FILE_PATH] = "DataLog_041_001_2";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "tuple表输入表，支持byte变长字段.");
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表中间表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "tuple表中间表表，支持byte变长字段.");
    ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.可更新表（包含为非pubsub输出表时）支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_002_1";
    char soName2[FILE_PATH] = "DataLog_041_002_2";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "可更新非pubsub输出表，支持byte变长字段.");
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName2);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表中间表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "可更新表输入表，支持byte变长字段.");
    ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.普通输入表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_003";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "普通表输入表，支持byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.timeout表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_004";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，支持byte变长字段
    AW_FUN_Log(LOG_STEP, "timeout表输入表，支持byte变长字段.");
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.固定资源表不支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_005";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "固定资源表，不支持byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "resource \"J000\" doesn't support variable-length byte fields near line 19.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.pubsub资源表不支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_006";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "pubsub资源表，不支持byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "the output field:\"a9\" of pubsub resource \"K000\" should not be variable-length data type near line 19.",
        errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.transient field表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_007";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "transient field中间表，支持byte变长字段.");
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.普通中间表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_008";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "普通中间表，支持byte变长字段.");
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.状态表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_009";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，支持byte变长字段
    AW_FUN_Log(LOG_STEP, "状态中间表，支持byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.普通输出表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_010";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，支持byte变长字段
    AW_FUN_Log(LOG_STEP, "普通输出表，支持byte变长字段.");
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.外部表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_011";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，支持byte变长字段
    AW_FUN_Log(LOG_STEP, "外部输出表，支持byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.tbm表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_012";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，支持byte变长字段
    AW_FUN_Log(LOG_STEP, "tbm输出表，支持byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.消息通知表支持byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_013";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "消息通知输出表，支持byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.支持agg中含变长byte(2025.3.4当前支持)
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_014";
    char datalogFile[FILE_PATH] = "./datalog_ddL/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // agg聚合函数，支持byte变长字段
    AW_FUN_Log(LOG_STEP, "agg聚合函数，支持byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    int ret = executeCommand(command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015..d中function输入和输出字段含变长byte字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_015)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";
    int ret = 0;

    char nsName[128] = "DataLog_041_015";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 016.pubsub输出表不支持忽略变长byte字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_016)
{

    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_016";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "pubsub输出表不支持忽略变长byte字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "wrong \'-\' field in left table \"A\": only resource's output field could be \'-\' near line 3.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.pubsub输出表支持变长byte字段为常量
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_017)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_017";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    AW_FUN_Log(LOG_STEP, "申请B表订阅");
    GmcConnT *subConnB;
    SnUserDataWithFuncT *userDataB;
    const char *subConnNameB = "testSubB";
    char *sub_infoB = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_B.gmjson", &sub_infoB);
    EXPECT_NE((void *)NULL, sub_infoB);
    const char *subNameB = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_infoB;
    tmp_sub_infoB.subsName = subNameB;
    tmp_sub_infoB.configJson = sub_infoB;
    ret = TestCreateSub(subConn, &userDataB, &tmp_sub_infoB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoB);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);
    testSnFreeUserData(userDataB->data);
    free(userDataB);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 018.pubsub输出表支持变长byte字段值为int型
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_018)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_018";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 019.pubsub型输出表byte字段不支持设置的值长度为0
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_019)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_019";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2, GMERR_DATA_EXCEPTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 全表扫描验证数据
    int record = 0;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 020.投影规则中，byte字段长度等于10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_020)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_020";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 021.join规则中，byte字段长度等于10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_021)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG1[] = "G1";
    char tableG2[] = "G2";

    char nsName[128] = "DataLog_041_021";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG1, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsert(g_conn, g_stmt, tableG2, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = ReadTable(g_stmt, tableG2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 022.笛卡尔积规则中，byte字段长度等于10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_022)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG1[] = "G1";
    char tableG2[] = "G2";

    char nsName[128] = "DataLog_041_022";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG1, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsert(g_conn, g_stmt, tableG2, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = ReadTable(g_stmt, tableG2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 023.udf中accessdelata写byte变长字段等于10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_023)
{

    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_023";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);
    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 024.function中输入和输出变长字段等于10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_024)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_024";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 025.投影规则中，byte字段长度大于10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_025";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "投影规则中，byte字段长度大于10240字节.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "byte length 10241 exceeds limit (10240) near line 3.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.join规则中中，byte字段长度大于10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_026";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "join规则中中，byte字段长度大于10240字节.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "byte length 10241 exceeds limit (10240) near line 4.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.笛卡尔积规则则中，byte字段长度大于10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_027";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "笛卡尔积规则中，byte字段长度大·于10240字节.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "byte length 10241 exceeds limit (10240) near line 4.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.udf中accessdelata写超过10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_028)
{

    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_028";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 30, 1}, {2, 30, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2, GMERR_DATA_EXCEPTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 全表扫描验证数据
    int record = 0;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 029.function中变长字段join超过10240字节
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_029)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_029";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 30, 1}, {2, 30, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2, GMERR_DATA_EXCEPTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    testGmcGetLastError();  // lasterror需要

    // 全表扫描验证数据
    int record = 0;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 030.不支持写输入表投影到仅含一个字段且为变长byte字段的pubsub输出表
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_030";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "投影规则中，byte字段长度大于10240字节.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "indexes shall not contain any variable-length byte field near line 1.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.支持pubsub表中定义一个byte变长字段(用例场景重复但是数据产生不同)
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_031)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_031";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsertByte4(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG, true);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 032.支持pubsub表中定义4个byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_032)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A32";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_032";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG, true);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 033.不支持pubsb表定义超过4个byte变长字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_033";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "不支持pubsb表定义超过4个byte变长字段.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "there are 5 variable-length byte fields in relation \"A\" which exceeds the limit (4) near line 1.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.pubsub表中不支持显示定义byte变长字段为主键（二级)索引
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_034";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "pubsub表中不支持显示定义byte变长字段为主键（二级)索引.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "indexes shall not contain any variable-length byte field near line 1.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.pubsub表中显示定义byte变长字段为主键索引
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_041_035";
    char soName2[FILE_PATH] = "DataLog_041_035_001";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "pubsub表中不支持显示定义byte变长字段为主键（二级)索引.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "indexes shall not contain any variable-length byte field near line 1.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "含byte变长字段, 未显示定义index(0)");
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName2);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    ret = executeCommand(command, "Error: index0 should be defined explicitly for table \"A\" near line 1.", errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.udf中写byte变长字段长度小于实际值的长度，可能存在踩存的风险值不确定
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_036)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";
    g_is036 = true;

    char nsName[128] = "DataLog_041_036";
    // 实际值小2
    g_isNeedReduceLength = 2;
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

class pubSupByte3 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=1\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
    }
};

void pubSupByte3::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN(0);
}
void pubSupByte3::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}
/* ****************************************************************************
 Description  : 037.支持pubsub订阅超时时间设置为1s
**************************************************************************** */
TEST_F(pubSupByte3, DataLog_041_037)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    // 订阅超时用例默认订阅回调中阻塞应答
    char tableA[] = "A";
    char tableG[] = "G";
    char nsName[128] = "DataLog_041_015";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不支持在线修改

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 修改订阅超时时间为2s,预期在线修改失败
    char command[MAX_CMD_SIZE] = {0};
    int32_t expectTimeoutTime = 2;
    (void)snprintf(
        command, MAX_CMD_SIZE, "gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal %d", expectTimeoutTime);

    // 不支持pubsub订阅超时时间设置为0s
    AW_FUN_Log(LOG_STEP, "不支持pubsub订阅超时时间设置为0s");
    char errorMsg1[MAX_CMD_SIZE] = {0};
    char errorMsg2[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg1, MAX_CMD_SIZE, "[ADMIN] set config");
    (void)snprintf(errorMsg2, MAX_CMD_SIZE, "[ADMIN] execute gmadmin corresponding function, ret = %d!",
        GMERR_DATA_EXCEPTION);
    ret = executeCommand(command, errorMsg1, errorMsg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isNeedSleep = 1.5;

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2, GMERR_REQUEST_TIME_OUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    // 全表扫描验证数据
    int record = 0;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 包含两条失败，两条成功
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不阻塞订阅回调,预期订阅不超时
    g_isNeedSleep = 0;
    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal 2");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 038.不支持pubsub订阅超时时间设置为0s
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    int32_t expectTimeoutTime = 0;
    (void)snprintf(
        command, MAX_CMD_SIZE, "gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal %d", expectTimeoutTime);

    // 不支持pubsub订阅超时时间设置为0s
    AW_FUN_Log(LOG_STEP, "不支持pubsub订阅超时时间设置为0s");
    char errorMsg1[MAX_CMD_SIZE] = {0};
    char errorMsg2[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg1, MAX_CMD_SIZE, "[ADMIN] set config");
    (void)snprintf(errorMsg2, MAX_CMD_SIZE, "[ADMIN] execute gmadmin corresponding function, ret = %d!",
        GMERR_DATA_EXCEPTION);
    int ret = executeCommand(command, errorMsg1, errorMsg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
class pubSupByte2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=10\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
    }
};

void pubSupByte2::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN(0);
}
void pubSupByte2::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}
/* ****************************************************************************
 Description  : 039.支持pubsub订阅超时时间设置为10s
**************************************************************************** */
TEST_F(pubSupByte2, DataLog_041_039)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    // 订阅超时用例默认订阅回调中阻塞应答
    char tableA[] = "A";
    char tableG[] = "G";
    char nsName[128] = "DataLog_041_015";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 修改订阅超时时间为8s,预期在线修改订阅超时时间失败
    char command[MAX_CMD_SIZE] = {0};
    int32_t expectTimeoutTime = 8;
    (void)snprintf(
        command, MAX_CMD_SIZE, "gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal %d", expectTimeoutTime);

    // 不支持pubsub订阅超时时间设置为0s
    AW_FUN_Log(LOG_STEP, "不支持pubsub订阅超时时间设置为0s");
    char errorMsg1[MAX_CMD_SIZE] = {0};
    char errorMsg2[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg1, MAX_CMD_SIZE, "[ADMIN] set config");
    (void)snprintf(errorMsg2, MAX_CMD_SIZE, "[ADMIN] execute gmadmin corresponding function, ret = %d!",
        GMERR_DATA_EXCEPTION);
    ret = executeCommand(command, errorMsg1, errorMsg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isNeedSleep = 11;

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2, GMERR_REQUEST_TIME_OUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    // 全表扫描验证数据
    int record = 0;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 包含两条失败，两条成功
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不阻塞订阅回调,预期订阅不超时
    g_isNeedSleep = 9;
    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal 2");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 040.不支持pubsub订阅超时时间设置为11S
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    int32_t expectTimeoutTime = 11;
    (void)snprintf(
        command, MAX_CMD_SIZE, "gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal %d", expectTimeoutTime);

    // 不支持pubsub订阅超时时间设置为0s
    AW_FUN_Log(LOG_STEP, "不支持pubsub订阅超时时间设置为11s");
    char errorMsg1[MAX_CMD_SIZE] = {0};
    char errorMsg2[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg1, MAX_CMD_SIZE, "[ADMIN] set config.");
    (void)snprintf(errorMsg2, MAX_CMD_SIZE, "[ADMIN] execute gmadmin corresponding function, ret = %d!",
        GMERR_DATA_EXCEPTION);
    int ret = executeCommand(command, errorMsg1, errorMsg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.不支持超时订阅时间值设置为异常值（负数，小数，字符串，1s）
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    uint32_t expectTimeoutTime = -1;
    (void)snprintf(
        command, MAX_CMD_SIZE, "gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal %d", expectTimeoutTime);

    // 不支持pubsub订阅超时时间设置为0s
    AW_FUN_Log(LOG_STEP, "不支持超时订阅时间值设置为异常值:负数");
    char errorMsg1[MAX_CMD_SIZE] = {0};
    char errorMsg2[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg1, MAX_CMD_SIZE, "[ADMIN] set config.");
    (void)snprintf(errorMsg2, MAX_CMD_SIZE, "[ADMIN] execute gmadmin corresponding function, ret = %d!",
        GMERR_DATA_EXCEPTION);
    int ret = executeCommand(command, errorMsg1, errorMsg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "不支持超时订阅时间值设置为异常值:小数");
    float expectTimeoutTime1 = 1.1;
    (void)snprintf(
        command, MAX_CMD_SIZE, "gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal %lf", expectTimeoutTime1);

    executeCommand(command, errorMsg1, errorMsg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "不支持超时订阅时间值设置为异常值:字符串");
    char expectTimeoutTime2[10] = "timeout";
    (void)snprintf(
        command, MAX_CMD_SIZE, "gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal %d", expectTimeoutTime2);

    executeCommand(command, errorMsg1, errorMsg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "不支持超时订阅时间值设置为异常值:1s");
    char expectTimeoutTime3[10] = "1s";
    (void)snprintf(
        command, MAX_CMD_SIZE, "gmadmin -cfgName datalogCallBackTimeoutThreshold -cfgVal %d", expectTimeoutTime3);

    executeCommand(command, errorMsg1, errorMsg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 042.输入表写超过28条数据，pubsub表订阅接收/28次
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_042)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_015";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入28条数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[28][3] = {};
    for (int i = 0; i < 28; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
    }
    // 写入28条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 28);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 28;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 28);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预期每28条数据触发一次回调
    int32_t expectInvokeCallBackCount = 1;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);
    g_isInvokeCallBackCount = 0;
    int32_t count2[29][3] = {};
    for (int i = 0; i < 29; i++) {
        count2[i][0] = i + 1;
        count2[i][1] = i + 1;
        count2[i][2] = -1;
    }
    // 写入28条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count2, 29);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 1;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 29);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expectInvokeCallBackCount = 1;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 043.输入表批写4个变长1000条回滚验证udf申请内存上限
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_043)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_043";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入28条数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[1000][3] = {};
    for (int i = 0; i < 1000; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
    }
    // 写入28条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 1000, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testGmcGetLastError("The third-party library's function execute unsucc. invoke UDF, "
                              "retVal=1015004, name=dtl_ext_func_funcA01.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 全表扫描验证数据
    int record = 0;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 044.输入表批写含1个变长128条消息大小大于2M数据被回滚，外部表触发为1条消息一次
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_044)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";
    int ret = 0;

    char nsName[128] = "DataLog_041_044";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "创建外部表");
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F44.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(schema1);

    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    ret = testSubConnect(&subConnA, NULL, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConnA, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据每128数据为一批
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int record = 200;
    int32_t count1[record][3] = {};
    for (int i = 0; i < record; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
    }

    // 写入200条数据
    ret = BatchInsert44(g_conn, g_stmt, tableG, count1, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, record, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, record, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t expectInvokeCallBackCount = 2;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);
    testSnFreeUserData(userDataF->data);
    free(userDataF);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "F");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 045.gmconvert支持转换含byte字段.d为gmjson
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 指定一个含自定义主键的普通表的.d文件，使用gmconvert 转换为gmjson文件
    char inPath[256] = "./datalogDml/DataLog_041_015.d";
    char outPath[256] = "out_Convert_gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "gmconvert -i %s -o %s", inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    int ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验A.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/A.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson1, result));
    free(result);

    // 校验G.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/G.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson2, result));
    free(result);

    // 校验funcA01.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/funcA01.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson3, result));
    free(result);

    // 校验funcA02.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat out_Convert_gmjson/funcA02.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(g_expectGmjson4, result));
    free(result);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 046.支持在线及离线查看pubsub表含变长字段的物理执行计划
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_046)
{
    // V$DATALOG_PLAN_EXPLAIN_INFO
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "DataLog_041_021";
    char datalogFile[FILE_PATH] = "./datalogDml/";
    char completePath[MAX_CMD_SIZE] = {0};
#if defined(ENV_RTOSV2X)
#else
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);
    (void)memset(command, 0, sizeof(char) * (MAX_CMD_SIZE));
    // tuple表输入表，不支持byte变长字段
    AW_FUN_Log(LOG_STEP, "在线及离线查看pubsub表含变长字段的物理执行计划.");
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -s plan_str.c", g_toolPath);
    ret = executeCommand(command, "[gmprecompiler] init param failed. Exit with code 1009006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset(command, 0, sizeof(char) * (MAX_CMD_SIZE));
#endif
    // 加载so，在线查询物理执行计划
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset(command, 0, sizeof(char) * (MAX_CMD_SIZE));
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q  V\\$DATALOG_PLAN_EXPLAIN_INFO | wc -l", g_toolPath);
    int fileSize = 28000;
    EXPECT_EQ(0, TestGetResultCommand(command, &fileSize, NULL, 0));
    // 视图显示小于13k
#if (defined ENV_RTOSV2X) && (!defined RUN_INDEPENDENT)
    EXPECT_GT(40000, fileSize);
#else
    EXPECT_GT(28000, fileSize);
#endif
    system(command);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 047.支持写一张输入表到10张pubsub表中，且pubsub表含4个10240长度byte字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_047)
{

    AW_FUN_Log(LOG_STEP, "test begin");
    char tableG[] = "K";
    int ret = 0;

    char nsName[128] = "DataLog_041_047";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 组合subinfoJson
    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char subInfoPart1[30] = {0};
    SnUserDataWithFuncT *userDataUpdate;
    const char *subInfoPart2 = R"(
    "comment":"VertexLabel subscription",
    "events":
        [
            {"type":"insert", "msgTypes":["new object"]}
        ],
    "is_reliable": true}
        )";
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 0;
    userData->readIdFunc = DoubleInt4_getId;
    userData->startid = 0;
    userData->endid = 1;
    userData->count = 1;
    char subInfo[1024] = {0};
    char subName[30] = {0};
    char labelName = 'A';
    for (int i = 0; i < 10; i++) {
        sprintf(subInfoPart1, "\"%c\",", labelName + i);
        sprintf(subInfo, "{\"label_name\":%s%s", subInfoPart1, subInfoPart2);
        sprintf(subName, "subVertexLabel%d", i);
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        int ret = GmcSubscribe(g_stmt, &tmp_subInfo, subConn, snCallback, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 20, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 10; i++) {
        sprintf(subName, "subVertexLabel%d", i);
        ret = GmcUnSubscribe(g_stmt, subName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    free(userData);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 048.开启10个线程订阅10张pubsub表线程外写一张输入表到10张pubsub表，且pubsub表含4个10240长度byte字段
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_048)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableG[] = "K";
    int ret = 0;

    char nsName[128] = "DataLog_041_047";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建多个线程,订阅不同pubsub表
    int32_t thread_num = 10;
#ifdef ENV_RTOSV2X
    thread_num = 4;
#endif
    int32_t dataNum[thread_num];
    pthread_t sub_thr[thread_num];
    for (int i = 0; i < thread_num; i++) {
        dataNum[i] = i;
        // 不要用临时变量去给线程传数据容易出现值相同的情况
        ret = pthread_create(&sub_thr[i], NULL, ThreadSubPubsub, (void *)&dataNum[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 确保订阅关系已经创建完成
    sleep(10);
    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, 1}, {2, 2, 1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(sub_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 2024.8.10 notify表回滚逻辑验证
/* ****************************************************************************
 Description  : 1.notify表failedDataNum不为0，failedIndexes索引下标不为0回滚当前批.
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_049)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_015";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入256条数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int record = 256;
    int32_t count1[record*2][3] = {};
    for (int i = 0; i < record*2; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
    }
    // a.写入256条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预期每128条数据触发一次回调
    int32_t expectInvokeCallBackCount = 2;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);
    g_isInvokeCallBackCount = 0;
    g_isRollBack = 1;

    // b.写入256条数据失败
    ret = BatchInsert(g_conn, g_stmt, tableG, count1+record, record,GMERR_DATA_EXCEPTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, record+60+128,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expectInvokeCallBackCount = 4;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);

    // c.写入256条数据失败
    g_isInvokeCallBackCount = 0;
    g_isRollBack = 1;
    ret = BatchInsert(g_conn, g_stmt, tableG, count1+record, record,GMERR_DATA_EXCEPTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, record+60+128,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expectInvokeCallBackCount = 4;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);

    // d.写入256条数据成功
    g_isInvokeCallBackCount = 0;
    g_isRollBack = 0;
    ret = BatchInsert(g_conn, g_stmt, tableG, count1+record, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, record,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expectInvokeCallBackCount = 2;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);
    

    // 全表扫描验证数据
    record = 512;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 2.notify表failedDataNum不为0，failedIndexes索引下标大于128当前批全部回滚
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_050)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";

    char nsName[128] = "DataLog_041_015";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入256条数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int record = 256;
    int32_t count1[record*2][3] = {};
    for (int i = 0; i < record*2; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
    }
    // a.写入256条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预期每128条数据触发一次回调
    int32_t expectInvokeCallBackCount = 2;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);
    g_isInvokeCallBackCount = 0;
    g_isRollBack = 2;

    // b.写入254条数据失败回调内异常数据下标是第二批数据第127条大于第二批推送条数（推送128，126，回滚126，128）
    ret = BatchInsert(g_conn, g_stmt, tableG, count1+record, record-2,GMERR_DATA_EXCEPTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, record*2-4,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expectInvokeCallBackCount = 4;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);
    

    // 全表扫描验证数据
    record = 256;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 3.报文大于2M，notify表failedDataNum不为0，failedIndexes索引下标不为0回滚当前批
**************************************************************************** */
TEST_F(pubSupByte, DataLog_041_051)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableG[] = "G";
    int ret = 0;

    char nsName[128] = "DataLog_041_044";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "创建外部表");
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F44.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(schema1);

    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    ret = testSubConnect(&subConnA, NULL, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConnA, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    // 向表中写入数据每128条数据为一批
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int record = 200;
    int32_t count1[record * 2][3] = {};
    for (int i = 0; i < record * 2; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
    }

    // 1.写入200条数据
    ret = BatchInsert44(g_conn, g_stmt, tableG, count1, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, record);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, record, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, record, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t expectInvokeCallBackCount = 2;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);

    g_isInvokeCallBackCount = 0;
    g_isRollBack = 3;
    // 2.写入200条数据失败
    ret = BatchInsert44(g_conn, g_stmt, tableG, count1+record, record,GMERR_DATA_EXCEPTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 全表扫描验证数据
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 正向200条 128 + 72
    // 回滚failedIndexes为30  30 + 128
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 128 + 72 + 30 + 128, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expectInvokeCallBackCount = 4;
    AW_MACRO_EXPECT_EQ_INT(expectInvokeCallBackCount, g_isInvokeCallBackCount);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);
    testSnFreeUserData(userDataF->data);
    free(userDataF);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "F");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end");
}
