%table A000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:byte)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)),index(31(d3))}

// transient  (field)
%table B000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:byte)
{
index(0(a)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)),index(31(d3)), transient(field(c))
}

%table C000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:byte)
{
index(0(a)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)),index(31(d3))
}
B000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,g8) :- C000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,g8).
A000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,g8) :- B000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,g8).
