/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDB 503.1.0 迭代三热补丁升级-可靠性测试
 History      :
 Author       : luyang/l00618033
 Create       : [2023.10.17]
*****************************************************************************/
#include "hotpatch.h"
#include "DatalogRun.h"

using namespace std;

class hotpatch_005_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatch_005_test::SetUp()
{}
void hotpatch_005_test::TearDown()
{}

/* ****************************************************************************
Description  : 1.新增1个部分可更新表，并新增1个规则rulename与前面的相同，预期报错
%table tb1{update}
%rule r0 null(0) :- tb1
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system(command);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    AW_MACRO_ASSERT_EQ_INT(
        GMERR_OK, executeCommand(command2, "Error: new rule \"r0\" already exist in old datalog", "Exit with code 1004009"));
    system(command2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 2.新增1个部分可更新表，并新增1个规则不带rulename，预期报错
%table tb1{update}
null(0) :- tb1
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch2";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2, "Error: rule name must be defined", "exit code 1004009"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 3.新增1个部分可更新表，并新增1个规则rulename与前面的不同，预期成功
%table tb1{update}
%rule ra111 null(0) :- tb1
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch3";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2, "Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 4.新增1个部分可更新表，并新增1个规则rulename与前面的不同，预期成功
%table tb1{update}
%rule r5 out1 :- tb1
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch4";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    // 2024年12月28号 特性合入约束变更
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2, "Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 5.新增1个普通表，并新增1个null(0)规则，预期报错
%table tb1
%rule r5null(0) :- tb1
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch5";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(
        GMERR_OK, executeCommand(command2, "Error: the upgraded table \"inpC\" should be input updatable table",
                      "Exit with code 1004009"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 6.新增1个过期表，并新增1个null(0)规则，预期报错
%table tb1{timeout}
%rule r5null(0) :- tb1
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch6";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(
        GMERR_OK, executeCommand(command2, "Error: the upgraded table \"inpC\" should be input updatable table",
                      "exit code 1004009"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 7.新增1个transient_tuple表，并新增1个null(0)规则，预期报错
%table tb1
%rule r5null(0) :- tb1
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch7";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(
        GMERR_OK, executeCommand(command2, "Error: the upgraded table \"inpC\" should be input updatable table",
                      "exit code 1004009"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 8.新增1个transient_finish表，并新增1个null(0)规则，预期报错
%table tb1
%rule r5null(0) :- tb1
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch8";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,
        executeCommand(command2, "Error: table \"inpC\" with transient field should be an intermediate table ",
            "exit code 1004009"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新表与过期表进行join，预期报错(最新代码会成功)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_1";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2,"Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新表与tranTuple表进行join，预期报错(最新代码会成功)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_2";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2,"Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新表与tranFinsh，预期报错(最新代码会成功)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_3";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2,"Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新输入表规则中间表类型为pubsub资源表，预期报错
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_4";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.",
        GMERR_SEMANTIC_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2, "Error: redo was not supported on intermediate "
        "table \"resourceA\", redo intermediate table only support normal, sequential resource, transient table.",
        g_errorMsg));
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新输入表规则中间表类型为tranTuple，预期报错(最新代码会成功)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_5";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2,
                                         "Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新输入表规则中间表类型为tranTuple，预期报错(最新代码会成功)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_6";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2,"Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新输入表规则中间表类型为tranTuple，预期报错(最新代码会成功)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_7";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2,"Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新输入表规则中间表类型为tranTuple，预期报错
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_8";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,
        executeCommand(command2,
            "Error: not allowed to upgrade due to relation \"C8\" and state_transfer UDF \"transfer\" in same rule topo",
            "Exit with code 1004009"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新输入表规则中输出表类型含pubsub型可更新表，预期报错(最新代码会成功)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_9";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2,"Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新输入表规则中输出表类型含msgNotify表，预期报错
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_10";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    // 5.29号，约束变更，msgNotify表支持重做
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2, "Serialize done"));
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 新增可更新输入表规则中输出表类型含外部表，预期报错(最新代码会成功)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_2_11";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(
        GMERR_OK, executeCommand(command2,"Serialize done"));

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
Description  : 修改原先规则的结构预期报错A1:-B1,C1-->A1:-B1,D(新增D)
**************************************************************************** */
TEST_F(hotpatch_005_test, DataLog_050_005_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "rollback_compile1";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f ./datalogFile/rollback/%s.d", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char inputFile2[FILE_PATH] = "rollback_compile1_patch_3_1";
    char command2[MAX_CMD_SIZE] = {0};
    (void)snprintf(command2, MAX_CMD_SIZE,
        "%s/gmprecompiler -u ./datalogFile/rollback/%s_rule.d ./datalogFile/rollback/%s.d", g_toolPath, inputFile,
        inputFile2);
    system(command2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, executeCommand(command2, "Error: right structure of rule \"r21\" has "
        "changed: origin name \"A12\" ==> new name \"inpC\" near line 4.", "Error: rule r21 should not change "
        "struct except add one table, add function or delete function near line 4.", "exit code 1004009"));
    AW_FUN_Log(LOG_STEP, "test end.");
}
