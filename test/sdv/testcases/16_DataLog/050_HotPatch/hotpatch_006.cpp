/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDB 503.1.0 迭代三热补丁升级-可靠性测试
 History      :
 Author       : luyang/l00618033
 Create       : [2023.10.17]
*****************************************************************************/
#include "hotpatch.h"
#include "DatalogRun.h"

using namespace std;

class hotpatch_006_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatch_006_test::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void hotpatch_006_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;

    // 恢复修改的配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
}

static void CompilerWithUdf(const char *compilerPath, const char *libPath, const char *oldfileName,
    const char *udfFileName, const char *soFileName)
{
    char cmd[DB_INVALID_UINT16];
    int cmdLength = sprintf_s(cmd, DB_INVALID_UINT16,
        "%s/gmprecompiler -supressSpeErr -f "       // path   compilerPath
        "./datalogFile/addNewTable/%s.d "           // old.d  oldfileName.d
        "./datalogFile/addNewTable/%s.c && "        // patch.c  oldfileName.c
        "gcc -Wl,-Bsymbolic -fPIC -I %s --shared "  // lib
        "./datalogFile/addNewTable/%s.c "           // patch.c      oldfileName.c
        "./datalogFile/addNewTable/%s.c -o "        // function.c   udfFileName.c
        "./datalogFile/addNewTable/%s.so ",         // _patch.so    soFileName.so
        compilerPath, oldfileName, oldfileName, libPath, oldfileName, udfFileName, soFileName);
    EXPECT_GT(cmdLength, 0);
    printf("%s\n", cmd);
    Status ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
}

static void CompilerWithoutUdf(
    const char *compilerPath, const char *libPath, const char *oldFileName, const char *soFileName)
{
    char cmd[MAX_CMD_SIZE];
    int cmdLength = sprintf_s(cmd, MAX_CMD_SIZE,
        "%s/gmprecompiler -supressSpeErr -f "       // path   compilerPath
        "./datalogFile/addNewTable/%s.d "           // old.d  oldfileName.d
        "./datalogFile/addNewTable/%s.c && "        // patch.c  oldfileName.c
        "gcc -Wl,-Bsymbolic -fPIC -I %s --shared "  // libPath
        "./datalogFile/addNewTable/%s.c "           // patch.c      oldfileName.c
        " -o ./datalogFile/addNewTable/%s.so",      // _patch.so    soFileName.so
        compilerPath, oldFileName, oldFileName, libPath, oldFileName, soFileName);
    EXPECT_GT(cmdLength, 0);
    printf("%s\n", cmd);
    Status ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
}

static void CompilerWithUdf4Upgrade(const char *compilerPath, const char *libPath, const char *oldfileName,
    const char *patchFileName, const char *outputFileName, const char *udfFileName, const char *upSoFile,
    const char *rbSoFile)
{
    char cmd[DB_INVALID_UINT16];
    int cmdLength = sprintf_s(cmd, DB_INVALID_UINT16,
        "%s/gmprecompiler -supressSpeErr -u "          // path   compilerPath
        "./datalogFile/addNewTable/%s.d "              // old.d  oldfileName.d
        "./datalogFile/addNewTable/%s.d "              // patch.d  patchFileName.c
        "./datalogFile/addNewTable/%s.c "              // patch.c  patchFileName.c
        "./datalogFile/addNewTable/%s.d && "           // fullDtl.d  outputFileName.d
        "gcc -Wl,-Bsymbolic -fPIC -I %s --shared "     // lib
        "./datalogFile/addNewTable/%s.c "              // patch.c      patchFileName.c
        "./datalogFile/addNewTable/%s.c -o "           // function.c   udfFileName.c
        "./datalogFile/addNewTable/%s.so && "          // _patch.so    upSoFile.so
        "gcc -Wl,-Bsymbolic -fPIC  -I %s  --shared "   // lib
        "./datalogFile/addNewTable/%s_rollback.c -o "  // rollback.c    patchFileName.c
        "./datalogFile/addNewTable/%s.so",             // _rollback.so  rbSoFile.so
        compilerPath, oldfileName, patchFileName, patchFileName, outputFileName, libPath, patchFileName, udfFileName,
        upSoFile, libPath, patchFileName, rbSoFile);
    EXPECT_GT(cmdLength, 0);
    printf("%s\n", cmd);
    Status ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
}

static void CompilerWithoutUdf4Upgrade(const char *compilerPath, const char *libPath, const char *oldfileName,
    const char *patchFileName, const char *outputFileName, const char *upSoFile, const char *rbSoFile)
{
    char cmd[DB_INVALID_UINT16];
    int cmdLength = sprintf_s(cmd, DB_INVALID_UINT16,
        "%s/gmprecompiler -supressSpeErr -u "          // path
        "./datalogFile/addNewTable/%s.d "              // old.d
        "./datalogFile/addNewTable/%s.d "              // patch.d
        "./datalogFile/addNewTable/%s.c "              // patch.c
        "./datalogFile/addNewTable/%s.d && "           // fullDtl.d
        "gcc -Wl,-Bsymbolic -fPIC -I %s --shared "     // lib
        "./datalogFile/addNewTable/%s.c -o "           // patch.c
        "./datalogFile/addNewTable/%s.so && "          // _patch.so
        "gcc -Wl,-Bsymbolic -fPIC  -I %s  --shared "   // lib
        "./datalogFile/addNewTable/%s_rollback.c -o "  // rollback.c
        "./datalogFile/addNewTable/%s.so",             // _rollback.so
        compilerPath, oldfileName, patchFileName, patchFileName, outputFileName, libPath, patchFileName, upSoFile,
        libPath, patchFileName, rbSoFile);
    EXPECT_GT(cmdLength, 0);
    printf("%s\n", cmd);
    Status ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);
}

static Status ImportCmd(const char *importPath, const char *soName)
{
    char importCmd[DB_INVALID_UINT8];
    int cmdLen = sprintf_s(importCmd, DB_INVALID_UINT8,
        "%s/gmimport -c datalog -f ./datalogFile/addNewTable/%s.so -ns %s", importPath, soName, g_testNameSpace);
    EXPECT_GT(cmdLen, 0);
    Status ret = system(importCmd);
    return ret;
}

static Status ImportCmd4Upgrade(const char *importPath, const char *soName)
{
    char importCmd[DB_INVALID_UINT8];
    int cmdLen = sprintf_s(importCmd, DB_INVALID_UINT8,
        "%s/gmimport -c datalog -upgrade ./datalogFile/addNewTable/%s.so -ns %s", importPath, soName, g_testNameSpace);
    EXPECT_GT(cmdLen, 0);
    Status ret = system(importCmd);
    return ret;
}

static Status ImportCmd4Rollback(const char *importPath, const char *soName)
{
    char importCmd[DB_INVALID_UINT8];
    int cmdLen = sprintf_s(importCmd, DB_INVALID_UINT8,
        "%s/gmimport -c datalog -rollback ./datalogFile/addNewTable/%s.so -ns %s", importPath, soName, g_testNameSpace);
    EXPECT_GT(cmdLen, 0);
    Status ret = system(importCmd);
    return ret;
}

static Status UnloadCmd(const char *importPath, const char *soName)
{
    char importCmd[DB_INVALID_UINT8];
    int cmdLen = sprintf_s(
        importCmd, DB_INVALID_UINT8, "%s/gmimport -c datalog -d %s -ns %s", importPath, soName, g_testNameSpace);
    EXPECT_GT(cmdLen, 0);
    Status ret = system(importCmd);
    return ret;
}
// 001.新增部分可更新表（不含比较函数），null(0):-up1,升级2中up1与原规则中的表进行join
TEST_F(hotpatch_006_test, DataLog_050_006_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_1";
    char *oldRuleFileName = (char *)"add_new_table_1_rule";
    char *oldSoName = (char *)"add_new_table_1";
    char *upSoName1 = (char *)"add_new_table_1_patchV2";
    char *upSoName2 = (char *)"add_new_table_1_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_1_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_1_rollbackV2";
    char *patchName1 = (char *)"add_new_table_1_patch1";
    char *patchName2 = (char *)"add_new_table_1_patch2";
    char *patchOutputName1 = (char *)"add_new_table_1_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_1_patch2_full";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));

    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }
    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    // 升级1，新增输入表，投影到null表，不重做
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);
    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp0", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp0", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp0", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp0 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);

    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp0", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "newInp0", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp0 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn11);
    free(objIn22);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002.新增部分可更新表（含比较函数），null(0):-up1,升级2中up1与原规则中的表进行join
TEST_F(hotpatch_006_test, DataLog_050_006_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_2";
    char *oldRuleFileName = (char *)"add_new_table_2_rule";
    char *oldSoName = (char *)"add_new_table_2";
    char *upSoName1 = (char *)"add_new_table_2_patchV2";
    char *upSoName2 = (char *)"add_new_table_2_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_2_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_2_rollbackV2";
    char *patchName1 = (char *)"add_new_table_2_patch";
    char *patchName2 = (char *)"add_new_table_2_ruleV2_patch";
    char *patchOutputName1 = (char *)"add_new_table_2_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_2_patch2_full";

    char *patchFuncName1 = (char *)"add_new_table_2_patch_udf";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);

    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);
    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp1", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);

    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "newInp1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn11);
    free(objIn22);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003.新增可更新表（不含比较函数），null(0):-up1,升级2中up1与原规则中的表进行join
TEST_F(hotpatch_006_test, DataLog_050_006_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_3";
    char *oldRuleFileName = (char *)"add_new_table_3_rule";
    char *oldSoName = (char *)"add_new_table_3";
    char *upSoName1 = (char *)"add_new_table_3_patchV2";
    char *upSoName2 = (char *)"add_new_table_3_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_3_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_3_rollbackV2";
    char *patchName1 = (char *)"add_new_table_3_patch";
    char *patchName2 = (char *)"add_new_table_3_ruleV2_patch";
    char *patchOutputName1 = (char *)"add_new_table_3_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_3_patch2_full";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);

    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);
    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp2", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp2", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp2 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);

    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "newInp2", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn11);
    free(objIn22);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004.新增可更新表（含比较函数），null(0):-up1,升级2中up1与原规则中的表进行join
TEST_F(hotpatch_006_test, DataLog_050_006_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_4";
    char *oldRuleFileName = (char *)"add_new_table_4_rule";
    char *oldSoName = (char *)"add_new_table_4";
    char *upSoName1 = (char *)"add_new_table_4_patchV2";
    char *upSoName2 = (char *)"add_new_table_4_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_4_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_4_rollbackV2";
    char *patchName1 = (char *)"add_new_table_4_patch";
    char *patchName2 = (char *)"add_new_table_4_ruleV2_patch";
    char *patchOutputName1 = (char *)"add_new_table_4_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_4_patch2_full";

    char *patchFuncName1 = (char *)"add_new_table_4_patch_udf";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);

    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);
    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp3", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp3", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp3 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);

    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "newInp3", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn11);
    free(objIn22);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005.新增可更新表，null(0):-up1,升级2中up1与原规则中的func进行join
TEST_F(hotpatch_006_test, DataLog_050_006_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_5";
    char *oldRuleFileName = (char *)"add_new_table_5_rule";
    char *oldFuncName = (char *)"add_new_table_5_udf";

    char *oldSoName = (char *)"add_new_table_5";
    char *upSoName1 = (char *)"add_new_table_5_patchV2";
    char *upSoName2 = (char *)"add_new_table_5_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_5_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_5_rollbackV2";
    char *patchName1 = (char *)"add_new_table_5_patch";
    char *patchName2 = (char *)"add_new_table_5_ruleV2_patch";
    char *patchOutputName1 = (char *)"add_new_table_5_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_5_patch2_full";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);

    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1 + 10;
        objIn2[i].dtlReservedCount = 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1 + 10;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1 + 10;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);
    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp4", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp4", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp4", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp4 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);

    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp4", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "newInp4", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp4 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn11);
    free(objIn22);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006.新增可更新表，null(0):-up1,升级2中up1与原规则中的表进行not join
TEST_F(hotpatch_006_test, DataLog_050_006_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_6";
    char *oldRuleFileName = (char *)"add_new_table_6_rule";
    char *oldSoName = (char *)"add_new_table_6";
    char *upSoName1 = (char *)"add_new_table_6_patchV2";
    char *upSoName2 = (char *)"add_new_table_6_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_6_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_6_rollbackV2";
    char *patchName1 = (char *)"add_new_table_6_patch";
    char *patchName2 = (char *)"add_new_table_6_ruleV2_patch";
    char *patchOutputName1 = (char *)"add_new_table_6_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_6_patch2_full";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);

    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);
    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp5", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp5", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp5", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp5 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);

    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp5", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "newInp5", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp5 read complete!!!");
    C1Int4C1Int8C1StrC1ByteT *objIn = &objIn2[4];
    ret = readRecord(g_conn, g_stmt, "mid1", objIn, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmt, "mid1", NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn11);
    free(objIn22);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007.新增可更新表，null(0):-up1,升级2中up1与原规则中的表进行join，触发规则重做
TEST_F(hotpatch_006_test, DataLog_050_006_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_7";
    char *oldRuleFileName = (char *)"add_new_table_7_rule";
    char *oldSoName = (char *)"add_new_table_7";
    char *upSoName1 = (char *)"add_new_table_7_patchV2";
    char *upSoName2 = (char *)"add_new_table_7_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_7_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_7_rollbackV2";
    char *patchName1 = (char *)"add_new_table_7_patch";
    char *patchName2 = (char *)"add_new_table_7_ruleV2_patch";
    char *patchOutputName1 = (char *)"add_new_table_7_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_7_patch2_full";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);

    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid2", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid2 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);
    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp6", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid2", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid2 read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp6", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp6", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp6 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);

    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp6", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "newInp6", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp6 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid2", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp2 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn11);
    free(objIn22);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008.新增可更新表，null(0):-up1,升级2中up1与原规则中的表进行join，触发各种表重做
TEST_F(hotpatch_006_test, DataLog_050_006_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_8";
    char *oldRuleFileName = (char *)"add_new_table_8_rule";
    char *oldFuncName = (char *)"add_new_table_8_udf";
    char *oldSoName = (char *)"add_new_table_8";
    char *upSoName1 = (char *)"add_new_table_8_patchV2";
    char *upSoName2 = (char *)"add_new_table_8_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_8_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_8_rollbackV2";
    char *patchName1 = (char *)"add_new_table_8_patch";
    char *patchName2 = (char *)"add_new_table_8_ruleV2_patch";
    char *patchOutputName1 = (char *)"add_new_table_8_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_8_patch2_full";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);

    // 构造数据
    int recordNum = 5;

    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }

    C3Int8T *objIn3 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn3, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].dtlReservedCount = i + 1;
        objIn3[i].a = i % 2;
        objIn3[i].b = i % 2;
        objIn3[i].c = i + 1;
    }

    C3Int8T *objIn4 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn4, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn4[i].dtlReservedCount = 1;
        objIn4[i].a = i % 2;
        objIn4[i].b = i % 2;
        objIn4[i].c = i + 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "A", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "A", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "A read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "B", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "B read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);

    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "A", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "C", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "A", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "A read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "B", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "B read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp7", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp7", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp7 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);
    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "A", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "C", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn4[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "newInp7", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp7 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "mid1", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "A", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "A read complete!!!");
    ret = readRecord(g_conn, g_stmt, "B", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "B read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn3);
    free(objIn4);
    free(objIn11);
    free(objIn22);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.升级1修改规则，用已有的（部分）可更新表与表进行join
TEST_F(hotpatch_006_test, DataLog_050_006_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_9";
    char *oldRuleFileName = (char *)"add_new_table_9_rule";
    char *oldFuncName = (char *)"add_new_table_9_udf";
    char *oldSoName = (char *)"add_new_table_9";
    char *upSoName1 = (char *)"add_new_table_9_patchV2";
    char *upSoName2 = (char *)"add_new_table_9_patchV3";
    char *rbUpSoName1 = (char *)"add_new_table_9_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_9_rollbackV2";
    char *patchName1 = (char *)"add_new_table_9_patch";
    char *patchName2 = (char *)"add_new_table_9_ruleV2_patch";
    char *patchOutputName1 = (char *)"add_new_table_9_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_9_patch2_full";

    // pubsub普通表推送的数据
    C3Int8T objPub[14] = {
        // 加载后写数据
        {+1, 0, 1, 2, 2023},
        {+1, 0, 2, 3, 2023},
        {+1, 0, 3, 4, 2023},
        {+1, 0, 4, 5, 2023},
        {+1, 0, 5, 6, 2023},
        // 第一次升级不重做
        // 第二次升级
        {-1, 0, 1, 2, 2023},
        {-1, 0, 2, 3, 2023},
        {-1, 0, 3, 4, 2023},
        {-1, 0, 4, 5, 2023},
        {-1, 0, 5, 6, 2023},
        {+1, 1, 1, 2, 2023},
        {+1, 1, 2, 3, 2023},
        {+1, 1, 3, 4, 2023},
        {+1, 1, 4, 5, 2023},
    };
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifyrs2";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfo024.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 14;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01));
    free(sub_info01);

    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }

    C3Int8T *objIn3 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn3, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].dtlReservedCount = i + 1;
        objIn3[i].a = i + 1;
        objIn3[i].b = i + 2;
        objIn3[i].c = i + 3;
    }

    C3Int8T *objIn4 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn4, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn4[i].dtlReservedCount = 1;
        objIn4[i].a = i + 1;
        objIn4[i].b = i + 2;
        objIn4[i].c = 2023;
    }

    C3Int8T *objIn5 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn5 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn5, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn5[i].dtlReservedCount = 1;
        objIn5[i].a = i + 1;
        objIn5[i].b = i + 2;
        objIn5[i].c = i + 3;
    }

    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpA", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpB", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpC", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "inpA", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpA read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpB", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpB read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpC", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpC read complete!!!");
    ret = readRecord(g_conn, g_stmt, "outA", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "outA read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);

    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "outA", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpA", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpB", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpC", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn4[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn5[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "inpA", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpA read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpB", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpB read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpC", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpC read complete!!!");
    ret = readRecord(g_conn, g_stmt, "outA", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "outA read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp7", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp7", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp7 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);
    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "outA", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpA", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpB", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpC", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn4[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn5[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "inpA", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpA read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpB", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpB read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpC", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpC read complete!!!");
    ret = readRecord(g_conn, g_stmt, "outA", objIn4, recordNum2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "outA read complete!!!");

    ret = writeRecord(g_conn, g_stmt, "newInp7", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp7", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp7 read complete!!!");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);
    free(objIn1);
    free(objIn2);
    free(objIn3);
    free(objIn4);
    free(objIn5);
    free(objIn11);
    free(objIn22);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.升级1修改规则，用已有的（部分）可更新表与func进行join
TEST_F(hotpatch_006_test, DataLog_050_006_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *oldFileName = (char *)"add_new_table_10";
    char *oldRuleFileName = (char *)"add_new_table_10_rule";
    char *oldSoName = (char *)"add_new_table_10";
    char *upSoName1 = (char *)"add_new_table_10_patchV2";
    char *upSoName2 = (char *)"add_new_table_10_patchV3";
    char *upSoName3 = (char *)"add_new_table_10_patchV4";
    char *upSoName4 = (char *)"add_new_table_10_patchV5";
    char *rbUpSoName1 = (char *)"add_new_table_10_rollbackV1";
    char *rbUpSoName2 = (char *)"add_new_table_10_rollbackV2";
    char *rbUpSoName3 = (char *)"add_new_table_10_rollbackV3";
    char *rbUpSoName4 = (char *)"add_new_table_10_rollbackV4";
    char *patchName1 = (char *)"add_new_table_10_patch";
    char *patchName2 = (char *)"add_new_table_10_ruleV2_patch";
    char *patchName3 = (char *)"add_new_table_10_ruleV3_patch";
    char *patchName4 = (char *)"add_new_table_10_ruleV4_patch";
    char *patchOutputName1 = (char *)"add_new_table_10_patch1_full";
    char *patchOutputName2 = (char *)"add_new_table_10_patch2_full";
    char *patchOutputName3 = (char *)"add_new_table_10_patch3_full";
    char *patchOutputName4 = (char *)"add_new_table_10_patch4_full";

    char *patchFuncName1 = (char *)"add_new_table_10_patch1_udf";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd(g_toolPath, oldSoName));
    sleep(1);
    // 构造数据
    int recordNum = 5;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn3 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn3 malloc failed !!!");
    }
    memset(objIn3, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].a = i + 1;
        objIn3[i].bLen = 10;
        objIn3[i].b = strT;
        (void)snprintf((char *)objIn3[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn3[i].c[j] = i + 1;
        }
        objIn3[i].d = i + 1;
        objIn3[i].dtlReservedCount = 1;
    }

    int recordNum2 = 4;
    C1Int4C1Int8C1StrC1ByteT *objIn11 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn11 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn11 malloc failed !!!");
    }
    memset(objIn11, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn11[i].a = i + 1;
        objIn11[i].bLen = 10;
        objIn11[i].b = strT;
        (void)snprintf((char *)objIn11[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn11[i].c[j] = i + 1;
        }
        objIn11[i].d = i + 1;
        objIn11[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn22 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn22 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn22 malloc failed !!!");
    }
    memset(objIn22, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].a = i + 1;
        objIn22[i].bLen = 10;
        objIn22[i].b = strT;
        (void)snprintf((char *)objIn22[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn22[i].c[j] = i + 1;
        }
        objIn22[i].d = i + 1;
        objIn22[i].dtlReservedCount = 1;
    }
    // 插入数据
    Status ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName1));
    sleep(1);

    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a += 1;
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName2));
    sleep(1);
    expectUpVerVal1 = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn3[i] = objIn2[i];
        objIn3[i].a = 1;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName3));
    sleep(1);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    ret = writeRecord(g_conn, g_stmt, "newInp7", objIn11, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "newInp7", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp7 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ImportCmd4Upgrade(g_toolPath, upSoName4));
    sleep(1);

    expectUpVerVal1 = 3;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "newInp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn3[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn22[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "newInp7", objIn22, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "newInp7 read complete!!!");

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, UnloadCmd(g_toolPath, oldSoName));
    sleep(1);

    free(objIn1);
    free(objIn2);
    free(objIn3);
    free(objIn11);
    free(objIn22);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 变更：notify表、Tbm表热补丁升级，upgradeVersion字段值始终为0
// 011.升级1修改规则，用已有的（部分）可更新表与表进行not join
TEST_F(hotpatch_006_test, DataLog_050_006_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/addNewTable";
    char outputFilePath[FILE_PATH] = "./datalogFile/addNewTable";
    char soName[FILE_PATH] = "add_new_table_11";
    char soName2[FILE_PATH] = "add_new_table_12";

    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV2.so", outputFilePath, soName2);
    (void)sprintf(patchSoName3, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)SystemSnprintf("cp %s %s", patchSoName2, patchSoName3);


    // pubsub普通表推送的数据
    C3Int8T objPub[45] = {
        // 加载后写入数据
        {+1, 0, 1, 2, 2023},
        {+1, 0, 2, 3, 2023},
        {+1, 0, 3, 4, 2023},
        {+1, 0, 4, 5, 2023},
        {+1, 0, 5, 6, 2023},
        // 第一次升级
        {-1, 0, 1, 2, 2023},
        {-1, 0, 2, 3, 2023},
        {-1, 0, 3, 4, 2023},
        {-1, 0, 4, 5, 2023},
        {-1, 0, 5, 6, 2023},
        {+1, 0, 1, 2, 10},
        {+1, 0, 2, 3, 10},
        {+1, 0, 3, 4, 10},
        {+1, 0, 4, 5, 10},
        {+1, 0, 5, 6, 10},
        // 升级后卸载
        {-1, 0, 1, 2, 10},
        {-1, 0, 2, 3, 10},
        {-1, 0, 3, 4, 10},
        {-1, 0, 4, 5, 10},
        {-1, 0, 5, 6, 10},
        {+1, 0, 1, 2, 2023},
        {+1, 0, 2, 3, 2023},
        {+1, 0, 3, 4, 2023},
        {+1, 0, 4, 5, 2023},
        {+1, 0, 5, 6, 2023},
        // 第二次升级
        {-1, 0, 1, 2, 2023},
        {-1, 0, 2, 3, 2023},
        {-1, 0, 3, 4, 2023},
        {-1, 0, 4, 5, 2023},
        {-1, 0, 5, 6, 2023},
        {+1, 0, 1, 2, 20},
        {+1, 0, 2, 3, 20},
        {+1, 0, 3, 4, 20},
        {+1, 0, 4, 5, 20},
        {+1, 0, 5, 6, 20},
    };
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifyrs2";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfo024.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 45;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    sleep(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01));
    free(sub_info01);

    // 构造数据
    int recordNum2 = 4;
    char strT[10] = {0};
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum2);
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].bLen = 10;
        objIn2[i].b = strT;
        (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn2[i].c[j] = i + 1;
        }
        objIn2[i].d = i + 1;
        objIn2[i].dtlReservedCount = 1;
    }

    int recordNum = 5;

    C3Int8T *objIn3 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn3, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].dtlReservedCount = i + 1;
        objIn3[i].a = i + 1;
        objIn3[i].b = i + 2;
        objIn3[i].c = i + 3;
    }

    C3Int8T *objIn4 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn4 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn4, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn4[i].dtlReservedCount = 1;
        objIn4[i].a = i + 1;
        objIn4[i].b = i + 2;
        objIn4[i].c = 2023;
    }

    C3Int8T *objIn5 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn5 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn5, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn5[i].dtlReservedCount = 1;
        objIn5[i].a = i + 1;
        objIn5[i].b = i + 2;
        objIn5[i].c = i + 3;
    }

    // 插入数据
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum2, C1Int4C1Int8C1StrC1ByteSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpA", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpB", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpC", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表和中间表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp2", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "inpA", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpA read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpB", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpB read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpC", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpC read complete!!!");
    ret = readRecord(g_conn, g_stmt, "outA", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "outA read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(1);

    int32_t upVerVal = 0;
    int32_t expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "outA", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpA", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpB", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpC", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    // 校验输入表和中间表
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn4[i].c = 10;
        objIn4[i].upgradeVersion = expectUpVerVal1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn5[i].upgradeVersion = expectUpVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp2", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "inpA", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpA read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpB", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpB read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpC", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpC read complete!!!");
    ret = readRecord(g_conn, g_stmt, "outA", objIn4, recordNum2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "outA read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(1);

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));

    sleep(1);
    expectUpVerVal1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, "outA", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpA", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpB", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inpC", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "mid1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectUpVerVal1, upVerVal);

    for (int i = 0; i < recordNum; i++) {
        objIn4[i].c = 20;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp2", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "mid1", objIn2, recordNum2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "mid1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "inpA", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpA read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpB", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpB read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inpC", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inpC read complete!!!");
    ret = readRecord(g_conn, g_stmt, "outA", objIn4, recordNum2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "outA read complete!!!");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放分配的内存
    free(userData01->data);
    free(userData01);

    free(objIn1);
    free(objIn2);
    free(objIn3);
    free(objIn4);
    free(objIn5);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
