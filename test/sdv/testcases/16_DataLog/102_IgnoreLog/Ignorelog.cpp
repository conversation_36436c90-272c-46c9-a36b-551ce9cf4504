/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: Ignorelog.cpp
 * Description: test for Ignore Log
 * Author: qibingsen 00880292
 * Create: 2025-05-06
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "Sup.h"

int ret = 0;

class IgnoreLog : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void IgnoreLog::SetUpTestCase()
{
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void IgnoreLog::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void IgnoreLog::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
#ifndef RUN_INDEPENDENT
    system("echo \"\" > /opt/vrpv8/home/<USER>/diag.log");
    sleep(3);
#endif
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void IgnoreLog::TearDown()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    printf("\n======================TEST:END========================\n");
}


/* ****************************************************************************
创建普通输入表，status_merge_sub，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表批写10条count为0的数据，校验数据结果和日志，预期输入表无数据且有1次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    int32_t dtlReservedCount = 0;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}


/* ****************************************************************************
创建普通输入表，status_merge_sub，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表批写10条count为1的数据，校验数据结果和日志，预期输出表无数据且有1次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    int32_t dtlReservedCount = 1;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}


/* ****************************************************************************
创建普通输入表，status_merge_sub，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表批写10条count为0的数据，校验数据结果和日志，预期输出表无数据且有1次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    int32_t dtlReservedCount = -1;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表批写1K条count为0的数据，校验数据结果和日志，预期输出表无数据且有1次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_002.so";
    char dname[MAX_FILE_PATH] = "datalog_102_002";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 1000;
    int32_t dtlReservedCount = 0;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表批写10条count为-2的数据，校验数据结果和日志，预期输出表有10条数据且有1次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = -2;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表批写10条count为2的数据，校验数据结果和日志，预期输出表10条数据且有1次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = 2;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表单写10条count为0的数据，校验数据结果和日志，预期输出表无数据且有10次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = 0;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(writeCount, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(writeCount, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
同一batch中向输入表批写数据相同，但是10条count=-1的数据和count=1的数据，校验数据结果和日志，预期输入表无数据且有0次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = -1, dtlReservedCount1 = 1;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, 0);
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn1, vertexCfg, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
同一batch中向输入表批写数据相同，但是10条count=-2的数据和count=2的数据，校验数据结果和日志，预期输出表无数据且有0次日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = -2, dtlReservedCount1 = 2;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, 0);
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn1, vertexCfg, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建2个普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
同一批向两个普通表中，分别插入10条count为0的数据，和count为1的数据，校验数据结果和日志，
预期输入表1无数据且有输入表有1次日志打印，输入表2有10条数据且输入表无日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_001.so";
    char dname[MAX_FILE_PATH] = "datalog_102_001";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = 0, dtlReservedCount1 = 1;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    GtlabelCfgT vertexCfg1 = { GMC_OPERATION_INSERT, g_inp2, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, 0);
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn, vertexCfg1, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp2, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建可更新输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
然后向输入表中批写20条数据，其中包含10条count=-2的数据和10条count=1的数据，
校验数据结果和日志，预期输入表10条数据且有1条日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_011.so";
    char dname[MAX_FILE_PATH] = "datalog_102_011";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = -2, dtlReservedCount1 = 1;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, 0);
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn, vertexCfg, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建部分可更新输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
然后向输入表中批写20条数据，数据不相同，其中包含10条count=2的数据和10条count=1的数据，
校验数据结果和日志，预期输入表20条数据且有1条日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_012.so";
    char dname[MAX_FILE_PATH] = "datalog_102_012";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = 2, dtlReservedCount1 = 1;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, writeCount);
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn, vertexCfg, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建部分可更新输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
然后向输入表中批写20条数据，10条数据相同，但是分别为count=1和count=2，
校验数据结果和日志，预期输入表10条数据且有1条日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_012.so";
    char dname[MAX_FILE_PATH] = "datalog_102_012";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = 2, dtlReservedCount1 = 1;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, 0);
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn, vertexCfg, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp1, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}


/* ****************************************************************************
创建transient tuple输入表，投影到普通表和合并订阅表的.d文件，编译生成so，向输入表中写入10条数据，
然后向输入表中同一batch中批写20条数据，其中包含10条与输入表相同数据但是count = -1和count为0的相同数据，
校验数据结果和日志，预期0条数据且没有日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_014.so";
    char dname[MAX_FILE_PATH] = "datalog_102_014";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = -1, dtlReservedCount1 = 0;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < writeCount; i++) {
        objIn[i].dtlReservedCount = -1;
    } 
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn, vertexCfg, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_out11, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建transient tuple输入表，投影到普通表和合并订阅表的.d文件，编译生成so，向输入表中写入10条数据，
然后向输入表中同一batch中批写20条数据，其中包含10条与输入表相同数据但是count = -1和count为-2的相同数据，
校验数据结果和日志，预期0条数据且没有日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_014.so";
    char dname[MAX_FILE_PATH] = "datalog_102_014";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = -1, dtlReservedCount1 = -2;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < writeCount; i++) {
        objIn[i].dtlReservedCount = -1;
    } 
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn, vertexCfg, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_out11, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建transient finish输入表，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表中写入10条数据，然后向输入表中同一batch中批写20条数据，其中包含10条与输入表相同数据但是count = -1和count为0的相同数据，
校验数据结果和日志，预期输出表么有数据且没有日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_015.so";
    char dname[MAX_FILE_PATH] = "datalog_102_015";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = -1, dtlReservedCount1 = 0;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount1, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < writeCount; i++) {
        objIn[i].dtlReservedCount = dtlReservedCount;
    } 
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn, vertexCfg, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_out11, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建transient finish输入表，投影到普通表和合并订阅表的.d文件，编译生成so，向输入表中写入10条数据，
然后向输入表中同一batch中批写20条数据，其中包含10条与输入表相同数据但是count = -1和count为-2的相同数据，
校验数据结果和日志，预期输出没有数据且1条日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_015.so";
    char dname[MAX_FILE_PATH] = "datalog_102_015";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = -1, dtlReservedCount1 = -2;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    Inp1TableStructT *objIn1= (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount);
    Setinp1TableValue(objIn1, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < writeCount; i++) {
        objIn[i].dtlReservedCount = dtlReservedCount1;
    } 
    ret = WriteTwiceData(g_conn, g_stmt, Inp1TableSet, vertexCfg, objIn, vertexCfg, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount + dtlReservedCount1, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_out11, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    for (int i = 0; i < writeCount; i++) {
        free(objIn1[i].d10);
    }
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建transient finish输入表,variant，投影到普通表和合并订阅表的.d文件，编译生成so，
向输入表中写入10条count = 0的数据，预期输出表没有数据且无日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_017.so";
    char dname[MAX_FILE_PATH] = "datalog_102_017";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    int32_t dtlReservedCount = 0;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1TableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(0, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_out11, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，向输入表批写10条count为1的数据，通过aggregate写入count=0数据，
校验数据结果和日志，预期输入表1有10条数据，输入表2无数据且有1条日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_002.so";
    char dname[MAX_FILE_PATH] = "datalog_102_002";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = 0;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp2, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp2, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，向输入表批写10条count为1的数据，通过aggregate写入count=0数据，
校验数据结果和日志，预期输入表无数据且没有日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_020.so";
    char dname[MAX_FILE_PATH] = "datalog_102_020";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = 0;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp2, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp2, g_testNameSpace, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}


/* ****************************************************************************
创建普通输入表，投影到普通表和合并订阅表的.d文件，编译生成so，向输入表批写10条count为0的数据，通过aggregate写入count=0数据，
校验数据结果和日志，预期输入表无数据且没有日志打印，预期校验成功
**************************************************************************** */
TEST_F(IgnoreLog, DataLog_102_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/datalog_102_020.so";
    char dname[MAX_FILE_PATH] = "datalog_102_020";
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    int32_t dtlReservedCount = 0;
    Inp1TableStructT *objIn = (Inp1TableStructT *)malloc(sizeof(Inp1TableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp2, writeCount, isBatch, isStruct };
    Setinp1TableValue(objIn, writeCount, dtlReservedCount, 0);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, Inp1SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDeltaCountLog(1, dtlReservedCount, g_inp1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareBothNum(g_inp2, g_testNameSpace, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}
