/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: DLSupUpdFunc.cpp
 * Description: 支持根据索引key更新数据触发计算
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2023-03-09
 */
#include "DLSupUpd.h"
#include "pubsub.h"
#include "t_datacom_lite.h"
#define MAX_CMD_SIZE 1024

char g_command[MAX_CMD_SIZE];

using namespace std;

class DLSupUpdFunc : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DLSupUpdFunc::SetUp()
{
    AW_ADD_ERR_WHITE_LIST(5, "GMERR-1001000", "GMERR-1004000", "GMERR-1005002", "GMERR-1010001", "GMERR-1025024018");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    // 创建连接
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void DLSupUpdFunc::TearDown()
{
    int ret;
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 001.显式定义主键索引，使用主键索引更新数据
TEST_F(DLSupUpdFunc, DataLog_030_001)
{
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableA2[] = "A2";
    char tableB2[] = "B2";
    char tableA4[] = "A4";
    char tableB4[] = "B4";
    char tableA8[] = "A8";
    char tableB8[] = "B8";
    char tableAbyte[] = "Abyte";
    char tableBbyte[] = "Bbyte";
    char tableAstring[] = "Astring";
    char tableBstring[] = "Bstring";
    char ns[] = "public";
    char libName[] = "project_pk_001";
    int affectRows = 0;
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_001.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};

    ret = batchA1(g_stmt, tableA1, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = batchA2(g_stmt, tableA2, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = batchA4(g_stmt, tableA4, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = batchA8(g_stmt, tableA8, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = batchAbyte(g_stmt, tableAbyte, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = batchAstring(g_stmt, tableAstring, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA2, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB2, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA4, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB4, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA8, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB8, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableAbyte, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableBbyte, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableAstring,
        g_connServer, g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableBstring,
        g_connServer, g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索引更新数据");
    AW_FUN_Log(LOG_STEP, "更新一条+count数据");
    int8_t indexValue1 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT8, &indexValue1, sizeof(indexValue1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value1 = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新一条-count数据");
    int16_t indexValue2 = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA2, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT16, &indexValue2, sizeof(indexValue2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value2 = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新一条原有的数据");
    int32_t indexValue3 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA4, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &indexValue3, sizeof(indexValue3));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value3 = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新一条冲突数据");
    int64_t indexValue4 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA8, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue4, sizeof(indexValue4));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value4 = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    AW_FUN_Log(LOG_STEP, "更新string");
    char AString[10] = "1";
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableAstring, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_STRING, AString, strlen(AString));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char AString2[10] = "6";
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_STRING, AString2, strlen(AString2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_STRING, AString2, strlen(AString2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新byte");
    uint8_t Abyte[1] = {0};
    Abyte[0] = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableAbyte, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_FIXED, Abyte, sizeof(Abyte));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t Abyte2[1] = {0};
    Abyte2[0] = 7;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_FIXED, Abyte2, sizeof(Abyte2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_FIXED, Abyte2, sizeof(Abyte2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA2, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB2, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据为原有的数据
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA4, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A4", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB4, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B4", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA4, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(
        g_command, "\"dtlReservedCount\": 4", "\"a\": 3", "\"b\": 3");  // 输入表相同数据count+1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB4, g_connServer,
        g_testNameSpace, g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新数据为原有的数据

    // 更新数据为冲突数据
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA8, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A8", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB8, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B8", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA8, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 3", "\"a\": 3", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB8, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新数据为冲突数据

    // string
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableAstring,
        g_connServer, g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": \"6\"", "\"b\": \"6\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableBstring,
        g_connServer, g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": \"6\"", "\"b\": \"6\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // byte
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableAbyte, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": \"0x07\"", "\"b\": \"0x07\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableBbyte, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": \"0x07\"", "\"b\": \"0x07\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(libName);
}

// 002.隐式定义主键索引，按主键索引更新数据
TEST_F(DLSupUpdFunc, DataLog_030_002)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "public";
    char libName[] = "project_pk_002";
    TestLoadDatalog("./project_file/project_pk_002.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};

    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索引更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(libName);
}

// 003.主键索引定义在多个字段上，只设置一个索引字段更新数据，再设置所有索引字段更新数据
TEST_F(DLSupUpdFunc, DataLog_030_003)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_003";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_003.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "只设置一个主键索引字段更新数据，接口报错");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 2;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    AW_FUN_Log(LOG_STEP, "设全主键索引字段更新数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 004.主键索引，GmcSetIndexKeyValue先设置一个数据，再使用GmcSetIndexKeyValue设置一个数据，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_004)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_003";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_003.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用GmcSetIndexKeyValue先设置一个数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "再使用GmcSetIndexKeyValue设置一个数据后GmcExecute");
    indexValue = 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 005.过滤字段作为主键索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_005)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_004";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_004.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "主键a=1更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "主键a=2更新数据");
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 006.忽略字段作为主键索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_006)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_005";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_005.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用忽略字段作为主键索引更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 3", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 007.开启可串行化事务，按主键索引更新数据，回滚事务（用例下架）
TEST_F(DLSupUpdFunc, DISABLED_DataLog_030_007)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_003";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_003.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "开启事务");
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "主键索引字段更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "回滚后查询数据");
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 2", "\"b\": 2", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 2", "\"b\": 2", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 008.开启可串行化事务，可更新表插入数据，再更新数据(验证插入更新、处理顺序)（用例下架）
TEST_F(DLSupUpdFunc, DISABLED_DataLog_030_008)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    char ns[] = "project_pk_006";

    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_006.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "开启事务");
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    AW_FUN_Log(LOG_STEP, "主键索引字段更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 009.按主键索引进行结构化更新
TEST_F(DLSupUpdFunc, DataLog_030_009)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_003";
    TestLoadDatalog("./project_file/project_pk_003.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索结构化更新数据");
    TEST_INPUT_011 obj1 = (TEST_INPUT_011){0};
    ret = TestGetVertexLabelFromSchema((char *)g_schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {tableA10, 0, g_testNameSpace};
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestInput011Value(&obj1, indexValue);
    ret = testStructSetIndexKeyWithBuf(g_stmt, &obj1, 0, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestInput011Value(&obj1, indexValue);
    ret = testStructSetIndexKeyWithBuf(g_stmt, &obj1, 0, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 010.定义二级索引，按二级索引更新数据
TEST_F(DLSupUpdFunc, DataLog_030_010)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "public";
    char libName[] = "project_index_007";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_index_007.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}, {3, 3, 1, 1}, {3, 3, 3, -3}, {7, 7, 7, 2}};
    ret = batchA10(g_stmt, tableA10, count1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用二级索引更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新一条记录抵消原有记录");
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 7;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);  // 更新完再触发

    indexValue = 3;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 6;
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 3", "\"b\": 3", "\"c\": 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 3", "\"b\": 3", "\"c\": 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(libName);
}

// 011.一个字段同时设置主键和二级索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_011)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_index_008";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_index_008.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}, {3, 3, 3, 1}};
    ret = batchA10(g_stmt, tableA10, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索引更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 5;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "使用二级索引更新数据");
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 6;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexValue = 3;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 7;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 5", "\"b\": 5", "\"c\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 6", "\"b\": 6", "\"c\": 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 7", "\"b\": 7", "\"c\": 7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 5", "\"b\": 5", "\"c\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 6", "\"b\": 6", "\"c\": 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 7", "\"b\": 7", "\"c\": 7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 012.根据二级索引，同时找到多条数据更新
TEST_F(DLSupUpdFunc, DataLog_030_012)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "public";
    char libName[] = "project_index_007";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_index_007.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {1, 1, 2, -2}, {1, 1, 3, 1}, {1, 1, 4, -1}};
    ret = batchA10(g_stmt, tableA10, count1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用二级索引更新多条数据为不同数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 5;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(4, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用二级索引更新多条数据为同一条数据");
    indexValue = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(4, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据条数");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 5", "\"b\": 5", "\"c\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 5", "\"b\": 5", "\"c\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(libName);
}

// 013.同一个二级索引定义在多个字段上，只设置一个二级索引字段更新数据，再设置所有二级索引字段更新数据
TEST_F(DLSupUpdFunc, DataLog_030_013)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_index_007";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_index_007.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}};
    ret = batchA10(g_stmt, tableA10, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "只设置一个二级索引字段更新数据，接口报错");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 2;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    AW_FUN_Log(LOG_STEP, "设置所有二级索引字段更新数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 2;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 014.二级索引，GmcSetIndexKeyValue先设置一个数据，再使用GmcSetIndexKeyValue后设置一个数据，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_014)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_index_007";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_index_007.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcSetIndexKeyValue先设置一个数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "再使用GmcSetIndexKeyValue后设置一个数据");
    indexValue = 2;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 015.过滤字段作为二级索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_015)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_index_009";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_index_009.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用过滤字段作为二级索引更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 016.忽略字段作为二级索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_016)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_index_010";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_index_010.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用忽略字段作为二级索引更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 017.开启可串行化事务，使用二级索引更新数据，回滚事务（用例下架）
TEST_F(DLSupUpdFunc, DISABLED_DataLog_030_017)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_index_007";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_index_007.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "开启事务");
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "二级索引字段更新数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "回滚后查询数据");
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 2", "\"b\": 2", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 2", "\"b\": 2", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 018.二级索引进行结构化更新
TEST_F(DLSupUpdFunc, DataLog_030_018)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_index_007";
    TestLoadDatalog("./project_file/project_index_007.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};
    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "二级索结构化更新数据");
    TEST_INPUT_011 obj1 = (TEST_INPUT_011){0};
    ret = TestGetVertexLabelFromSchema((char *)g_schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {tableA10, 0, g_testNameSpace};
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestInput011Value(&obj1, indexValue);
    ret = testStructSetIndexKeyWithBuf(g_stmt, &obj1, 1, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestInput011Value(&obj1, indexValue);
    ret = testStructSetIndexKeyWithBuf(g_stmt, &obj1, 1, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -2", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 019.可更新表设置主键索引，二级索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_019)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_update_011";
    TestLoadDatalog("./project_file/project_update_011.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}, {3, 1, 1, 2}};

    ret = batchA10(g_stmt, tableA10, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "可更新表使用主键索引更新一条与主键冲突的数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "可更新表使用二级索引更新数据");
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据条数");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "部分可更新表使用二级索引更新一条同样的数据");
    indexValue = 3;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据条数");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 020.部分可更新表设置主键索引，二级索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_020)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_partialupdate_012";
    TestLoadDatalog("./project_file/project_partialupdate_012.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}, {3, 1, 1, 2}};

    ret = batchA10(g_stmt, tableA10, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "部分可更新表使用主键索引更新一条与主键冲突的数据");
    int64_t indexValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "部分可更新表使用二级索引更新数据");
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据条数");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 021.过期表的过期字段设置主键索引，二级索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_021)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_timeout_013";
    TestLoadDatalog("./project_file/project_timeout_013.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int64_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    int64_t count2[][3] = {{3, 3, 3}, {4, 4, 4}};

    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t dValue1 = currentTime + (int64_t)(count1[0][0] * 60 * 60 * 1000);
    int64_t dValue2 = currentTime + (int64_t)(count1[1][0] * 60 * 60 * 1000);
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &dValue1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count1[0][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count1[0][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count1[0][3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &dValue2, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count1[1][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count1[1][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count1[1][3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "查询索引数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isNull = 0;
    bool isFinish = false;
    int64_t aValue1 = 0;
    int64_t aValue2 = 0;
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt, "a", &aValue1, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt, "a", &aValue2, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);

    AW_FUN_Log(LOG_STEP, "过期表使用主键索引更新数据");
    int64_t indexValue = aValue1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count2[0][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count2[0][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "过期表使用二级索引更新数据");
    indexValue = aValue2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count2[1][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count2[1][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据条数");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 022.使用过期表的自身创建的索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_022)
{
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_timeout_014";
    TestLoadDatalog("./project_file/project_timeout_014.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int64_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    int64_t count2[][3] = {{3, 3, 3}, {4, 4, 4}};

    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t dValue1 = currentTime + (int64_t)(count1[0][0] * 60 * 60 * 1000);
    int64_t dValue2 = currentTime + (int64_t)(count1[1][0] * 60 * 60 * 1000);
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &dValue1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count1[0][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count1[0][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count1[0][3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &dValue2, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count1[1][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count1[1][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count1[1][3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "查询索引数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isNull = 0;
    bool isFinish = false;
    int64_t aValue1 = 0;
    int64_t aValue2 = 0;
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt, "a", &aValue1, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt, "a", &aValue2, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);

    AW_FUN_Log(LOG_STEP, "过期表使用自身创建的索引更新数据");
    int64_t indexValue = aValue1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count2[0][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count2[0][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexValue = aValue2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count2[1][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count2[1][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据条数");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 023.transient(field)表作为中间表，输入表设置主键索引，二级索引，更新输入表数据
TEST_F(DLSupUpdFunc, DataLog_030_023)
{
    char tableA12[] = "A12";
    char tableB12[] = "B12";
    char tableC12[] = "C12";
    int affectRows = 0;
    char ns[] = "project_transient_015";
    TestLoadDatalog("./project_file/project_transient_015.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{0, 2, 2, 2}, {2, 1, 1, 1}};

    ret = batchA12(g_stmt, tableA12, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 按规则，transient(field)表只会插入一条{0, 2, 2, 1}的数据
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A12", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B12", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableC12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "C12", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "transient(field)表使用主键索引更新数据");
    int32_t indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA12, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据条数");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A12", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B12", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableC12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "C12", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "transient(field)表使用二级索引更新数据");
    int64_t indexValue2 = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA12, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue2, sizeof(indexValue2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据条数");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A12", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B12", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableC12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "C12", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA12, g_connServer,
        g_testNameSpace, g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 1", "\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 2", "\"a\": 4", "\"b\": 2", "\"c\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 024.transient(tuple)表设置主键索引，二级索引，更新数据
TEST_F(DLSupUpdFunc, DataLog_030_024)
{
    char tableA12[] = "A12";
    char tableB12[] = "B12";
    char tableC12[] = "C12";
    int affectRows = 0;
    char ns[] = "project_transient_016";
    TestLoadDatalog("./project_file/project_transient_016.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{0, 2, 2, 2}, {1, 1, 1, 1}};

    ret = batchA12(g_stmt, tableA12, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // transient(tuple)中不会有数据
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A12", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B12", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableC12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "C12", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "输入表使用主键索引更新数据");
    int32_t indexValue = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA12, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value1 = 2;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value2 = 4;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value2, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value2, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "输入表使用二级索引更新数据");
    int64_t indexValue2 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA12, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue2, sizeof(indexValue2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value1 = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value2 = 3;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value2, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value2, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 2", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B12", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableC12, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 3", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 025.使用Groupby字段自动创建的索引更新数据
TEST_F(DLSupUpdFunc, DataLog_030_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char ns[] = "datalog_udf_min_max";
    TestLoadDatalog("./project_file/datalog_udf_min_max.so");
    int ret = 0;
    char tableA[] = "A";
    char tableB[] = "B";
    int affectRows = 0;
    int32_t count1[5][3] = {{1, 1, 1}, {1, 2, -1}, {2, 1, 1}, {2, 2, 1}, {3, 1, 1}};

    ret = batchAgg(g_stmt, tableA, count1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用GroupBy创建的索引更新输入表的数据");
    int32_t indexVal = 3;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &indexVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value1 = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    indexVal = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &indexVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value1 = 5;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, affectRows);
    indexVal = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &indexVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value1 = 6;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 5", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 5", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 1", "\"c\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4", "\"c\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog("datalog_udf_min_max", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.A10B10(a, c) :- A10(a, b) , B10(b, c).
// 定义主键索引，二级索引，使用主键索引，二级索引更新连接规则中输入表的数据
TEST_F(DLSupUpdFunc, DataLog_030_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char ns[] = "join_int_001";
    TestLoadDatalog("./join_file/join_int_001.so");
    int ret = 0;
    char tableA[] = "A1";
    char tableB[] = "B1";
    char tableAB[] = "A1B1";
    int affectRows = 0;
    int32_t count1[][3] = {{1, 2, 1}, {10, 11, -1}};
    int32_t count2[][3] = {{2, 3, 1}, {11, 10, -1}};

    // A1B1表输出1 3 1, 10 10 1
    ret = batchA(g_stmt, tableA, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableAB, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A1B1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "连接规则，使用主键索引更新连接规则输入表A1的数据");
    int64_t indexVal = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexVal, sizeof(indexVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "连接规则，使用二级索引更新连接规则输入表B1的数据");
    indexVal = 10;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableB, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexVal, sizeof(indexVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 10", "\"b\": 11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 3", "\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableAB, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 1", "\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog("join_int_001", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.
// B1(a, b) :- A1(a, b).
// A1B1(a, c) :- A1(a, b) , B1(b, c).
// 定义主键索引，二级索引，使用主键索引，二级索引更新连接规则中输入表的数据
TEST_F(DLSupUpdFunc, DataLog_030_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char ns[] = "join_int_002";
    TestLoadDatalog("./join_file/join_int_002.so");
    int ret = 0;
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableA1B1[] = "A1B1";
    int affectRows = 0;
    int32_t count1[][3] = {{1, 2, 1}, {2, 1, -1}, {3, 2, 1}};

    ret = batchA(g_stmt, tableA1, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 输出表记录{3 1 -1}, {2 2 -1}, {1 1 -1}
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A1", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B1", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA1B1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A1B1", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索引更新连接规则输入表的数据");
    int64_t indexVal = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexVal, sizeof(indexVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "使用二级索引更新连接规则输入表的数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexVal, sizeof(indexVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 3", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": -1", "\"a\": 4", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA1B1, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 4", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog("join_int_002", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *UpdateData1(void *args)
{
    int ret = 0;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    char tableA10[] = "A10";
    char ns[] = "project_pk_003";
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    for (int64_t i = 0; i < 5000; i++) {
        int64_t indexValue = i;
        ret = testGmcPrepareStmtByLabelName(stmt1, tableA10, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt1, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t value = i + 10000;
        ret = GmcSetVertexProperty(stmt1, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
}

void *UpdateData2(void *args)
{
    int ret = 0;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    char tableA10[] = "A10";
    char ns[] = "project_pk_003";
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    for (int64_t i = 5000; i < 10000; i++) {
        int64_t indexValue = i;
        ret = testGmcPrepareStmtByLabelName(stmt1, tableA10, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt1, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &indexValue, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t value = i + 10000;
        ret = GmcSetVertexProperty(stmt1, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 032.创建100个线程更新1W记录
TEST_F(DLSupUpdFunc, DataLog_030_032)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA10[] = "A10";
    char tableB10[] = "B10";
    char ns[] = "project_pk_003";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_003.so");

    AW_FUN_Log(LOG_STEP, "插入1w条数据");
    for (int i = 0; i < 10000; i++) {
        int64_t value = i;
        int32_t value1 = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    int trhNum = MAX_CONN_SIZE - existConnNum;
    AW_FUN_Log(LOG_STEP, "创建%d个线程更新数据", trhNum);
#else
    int trhNum = 100;
    AW_FUN_Log(LOG_STEP, "创建%d个线程更新数据", trhNum);
#endif
    pthread_t thr_arr[trhNum];

    for (int32_t i = 0; i < trhNum - 1; i += 2) {
        pthread_create(&thr_arr[i], NULL, UpdateData1, NULL);
        pthread_create(&thr_arr[i + 1], NULL, UpdateData2, NULL);
    }

    for (int32_t i = 0; i < trhNum - 1; i += 2) {
        pthread_join(thr_arr[i], NULL);
        pthread_join(thr_arr[i + 1], NULL);
    }

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int cnt = 0;
    bool isNull = false;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int64_t aValue = 0;
        int64_t bValue = 0;
        int64_t cValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "a", &aValue, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = GmcGetVertexPropertyByName(g_stmt, "b", &bValue, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(aValue, bValue);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = GmcGetVertexPropertyByName(g_stmt, "c", &cValue, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(aValue, cValue);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(10000, cnt);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableB10, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    cnt = 0;
    isNull = false;
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int64_t aValue = 0;
        int64_t bValue = 0;
        int64_t cValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "a", &aValue, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = GmcGetVertexPropertyByName(g_stmt, "b", &bValue, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(aValue, bValue);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = GmcGetVertexPropertyByName(g_stmt, "c", &cValue, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(aValue, cValue);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(10000, cnt);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 048.Datalog创建订阅关系，进行更新操作
TEST_F(DLSupUpdFunc, DataLog_030_048)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_007";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_007.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    for (int i = 0; i < 100; i++) {
        int64_t value = i;
        int32_t value1 = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建订阅关系");
    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(
        g_stmt, g_connSub, (char *)"schema_file/B10Sub.gmjson", &userData1, 200, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "设全主键索引字段更新数据");
    for (int64_t i = 0; i < 100; i++) {
        int64_t indexValue = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t value = i + 100;
        ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    }

    // 等待insert事件推送完成
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 100 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

// 100.可更新表GMC_STMT_ATTR_AFFECTED_ROWS联调问题
TEST_F(DLSupUpdFunc, DataLog_030_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char ns[] = "project_update_100";
    TestLoadDatalog("./project_file/project_update_100.so");
    int ret = 0;
    char tableA1[] = "A1";
    char tableA2[] = "A2";
    char tableA3[] = "A3";
    int affectRows = 0;

    AW_FUN_Log(LOG_STEP, "单写数据，获取GMC_STMT_ATTR_AFFECTED_ROWS");
    int64_t value = 300;
    int32_t dtlReservedCountValue = 1;
    ret = singleRecordInsert(g_stmt, tableA1, value, dtlReservedCountValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    affectRows = 0;
    ret = singleRecordInsert(g_stmt, tableA2, value, dtlReservedCountValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    affectRows = 0;
    ret = singleRecordInsert(g_stmt, tableA3, value, dtlReservedCountValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "单写dtlReservedCountValue为0的数据，获取GMC_STMT_ATTR_AFFECTED_ROWS");
    value = 200;
    dtlReservedCountValue = 0;
    affectRows = 0;
    ret = singleRecordInsert(g_stmt, tableA1, value, dtlReservedCountValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    ret = singleRecordInsert(g_stmt, tableA2, value, dtlReservedCountValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    ret = singleRecordInsert(g_stmt, tableA3, value, dtlReservedCountValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "可更新表，部分可更新表，fast_insert表使用主键索引更新一条不存在的数据");
    int64_t indexVal = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t updateValue = 3;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA2, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA3, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    AW_FUN_Log(LOG_STEP, "可更新表，部分可更新表，fast_insert表使用二级索引更新一条不存在的数据");
    indexVal = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    updateValue = 3;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);

    AW_FUN_Log(LOG_STEP, "可更新表，部分可更新表，fast_insert表使用主键索引更新非主键数据");
    indexVal = 300;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    updateValue = 3;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &updateValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA2, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &updateValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA3, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &updateValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "可更新表，部分可更新表，fast_insert表使用二级索引更新非主键数据");
    indexVal = 3;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    updateValue = 4;
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &updateValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA2, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &updateValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA3, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexVal, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &updateValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 200.更新timeout字段
TEST_F(DLSupUpdFunc, DataLog_030_200)
{
    char tableA10[] = "A200";
    char tableB10[] = "B200";
    int affectRows = 0;
    char ns[] = "project_timeout_200";
    TestLoadDatalog("./project_file/project_timeout_200.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    int64_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    int64_t count2[][3] = {{3, 3, 3}, {4, 4, 4}};

    // 预置过期字段时间值
    int64_t currentTime;
    GetTimeMs(&currentTime);
    int64_t dValue1 = currentTime + (int64_t)(count1[0][0] * 60 * 60 * 1000);
    int64_t dValue2 = currentTime + (int64_t)(count1[1][0] * 60 * 60 * 1000);
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &dValue1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count1[0][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count1[0][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count1[0][3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &dValue2, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count1[1][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count1[1][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &count1[1][3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    AW_FUN_Log(LOG_STEP, "查询索引数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isNull = 0;
    bool isFinish = false;
    int64_t aValue1 = 0;
    int64_t aValue2 = 0;
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt, "a", &aValue1, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(g_stmt, "a", &aValue2, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);

    int32_t u = 0;
    AW_FUN_Log(LOG_STEP, "过期表使用主键索引更新数据");
    int64_t indexValue = aValue1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count2[0][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count2[0][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "过期表使用二级索引更新数据");
    indexValue = aValue2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &count2[1][1], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &count2[1][2], sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
}

#if defined(RUN_DATACOM_DAP)  // SOHO环境不跑该用例
#else
class DLSupUpdFunc2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
#ifndef ENV_RTOSV2X
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");  // 内存大小改小，减少单个用例执行时间
#endif
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DLSupUpdFunc2::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    g_conn = NULL;
    g_stmt = NULL;
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void DLSupUpdFunc2::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 031.写满内存，Datalog输入表进行更新
TEST_F(DLSupUpdFunc2, DataLog_030_031)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    char tableA10[] = "A10";
    char tableB10[] = "B10";
    char ns[] = "project_pk_003";
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./project_file/project_pk_003.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入1k条数据");
    for (int i = 0; i < 1000; i++) {
        int64_t value = i;
        int32_t value1 = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "A10", "1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "B10", "1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建Vertex写满内存");
    char *schema = NULL;
    char labelName[] = "vertex_01_Memory";
    const char *vertexConfig = "{\"max_record_count\" : 100000}";
    readJanssonFile("schema_file/Vertex_01_Memory.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, vertexConfig);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    // 插入顶点
    uint32_t i = 0;
    uint32_t value = 0;
    while (ret == 0) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        // 写数据
        value = i;
        ret = GmcSetVertexProperty(g_stmt, "PK", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 写string数据
        uint32_t SuperSize = 256;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'B', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(g_stmt, "P0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "P15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        free(SuperValue);
        ret = GmcExecute(g_stmt);

        i++;
        if ((i % 50000) == 0) {
            AW_FUN_Log(LOG_STEP, "till now:insert records %d", i);
        }
    }
    AW_FUN_Log(LOG_STEP, "actul insert records is:%d", i);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "满内存后，更新1k条数据");
    for (int64_t i = 0; i < 1000; i++) {
        int64_t indexValue = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &g_updateVersionIndexValue, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &indexValue, sizeof(indexValue));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t value = i + 1000;
        ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "更新数据后查询数据");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1000", "\"b\": 1000", "\"c\": 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1999", "\"b\": 1999", "\"c\": 1999");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1000", "\"b\": 1000", "\"c\": 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeRemoveSpacesCommand(g_command, "\"dtlReservedCount\": 1", "\"a\": 1999", "\"b\": 1999", "\"c\": 1999");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
#endif
