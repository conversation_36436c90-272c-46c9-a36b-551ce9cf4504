/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDBV503 迭代一程序加载/卸载-定位定界能力增强
 Notes        : 执行相关的用例
 History      :
 Author       : youwanyong/ywx1157510
 Create       : [2023.04.15]
*****************************************************************************/
// 外部表增加资料说明：DTS2023041712831
// DataLog_040_021 固定资源表表表名不能大于64k  ：DTS2023041717931
// DataLog_040_018 过滤字段长度为128字节，报错4004：DTS2023042017781
#include "soLogEnhance.h"
#include "t_datacom_lite.h"

using namespace std;
class soLogEnhance : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void soLogEnhance::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
#ifdef ENV_RTOSV2X
    bool envAnos = false;
    if (0 == access("/hpe/libdblog_adapter.so", 0)) {
        envAnos = false;
    } else {
        envAnos = true;
    }
    if (!envAnos) {
        system(" echo \"\" > /opt/vrpv8/home/<USER>/diag.current.log");
    } else {
        system(" echo \"\" > /opt/vrpv8/home/<USER>/hpe_log.csv ");
    }
#else
    system(" echo \"\" > ../../../log/run/rgmserver/rgmserver.log ");
#endif
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(0);
}
void soLogEnhance::TearDown()
{
    system("rm -rf /root/_datalog_/");
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    AW_CHECK_LOG_END();
}

using namespace std;
class soLogEnhance1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void soLogEnhance1::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(0);
}
void soLogEnhance1::TearDown()
{
    system("rm -rf /root/_datalog_/");
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  : 不带过滤字段的，json格式打印
001.C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)

可更新表A自然连接普通表B，向插入一条数据投影到B查视图，插入多条数据查视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        "function", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 3", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "B1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "C1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "A1", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************向表A1插入一条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    AW_FUN_Log(LOG_STEP, "写入数据之后再次查询视图");
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    AW_FUN_Log(LOG_STEP, "写入数据之后再次查询视图");
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);
    AW_FUN_Log(LOG_STEP, "写入数据之后再次查询视图");
    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);
    AW_FUN_Log(LOG_STEP, "写入数据之后再次查询视图");
    ret = querySoView(
        "function", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 3", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "B1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "C1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "A1", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        "function", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 3", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "B1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "C1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("function", "A1", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView("function", "id: 8", "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.带过滤字段的,flat_full打印
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    char *viewName = (char *)"V\\$PTL_DATALOG_SO_INFO";
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -view_fmt flat_full ", viewName);
    system(command);
    char result10[100] = "";
    (void)sprintf(result10, "sysview get records unsucc, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************向表A1插入一条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);
    AW_FUN_Log(LOG_STEP, "向表中插入数据后查询视图");
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -view_fmt flat_full ", viewName);
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图 没有数据时查询正常，但是有数据该显示格式就会报错
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -view_fmt flat_full ", viewName);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  003.视图查询 带过滤字段的,flat_truncate打印
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    char *viewName = (char *)"V\\$PTL_DATALOG_SO_INFO";
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -view_fmt flat_truncate", viewName);
    system(command);
    char result10[100] = "";
    (void)sprintf(result10, "sysview get records unsucc, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************向表A1插入一条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);
    AW_FUN_Log(LOG_STEP, "向表中插入数据后查询视图");
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -view_fmt flat_truncate ", viewName);
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图 没有数据时查询正常，但是有数据该显示格式就会报错
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -view_fmt flat_truncate ", viewName);
    ret = executeCommand(command, "fetched all records, finish!");  // 4.17 校验元数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  004.不带过滤字段的asc排序
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }

    char nsName[128] = "function";
    char nsName1[128] = "resource";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    char *viewName = (char *)"V\\$PTL_DATALOG_SO_INFO";
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -o SO_ID asc -l 2", viewName);
    system(command);
    char result10[100] = "";
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************向表A1插入一条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);
    AW_FUN_Log(LOG_STEP, "向表中插入数据后查询视图");
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -o SO_ID asc -l 2", viewName);
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图 没有数据时查询正常，但是有数据该显示格式就会报错
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -o SO_ID asc -l 2", viewName);
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  005.带过滤字段的desc排序
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }

    char nsName[128] = "function";
    char nsName1[128] = "resource";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    char *viewName = (char *)"V\\$PTL_DATALOG_SO_INFO";
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -f NAMESPACE_NAME=public -o SO_ID desc -l 2", viewName);
    system(command);
    char result10[100] = "";
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************向表A1插入一条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);
    AW_FUN_Log(LOG_STEP, "向表中插入数据后查询视图");
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -f NAMESPACE_NAME=public -o SO_ID asc -l 2", viewName);
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图 没有数据时查询正常，但是有数据该显示格式就会报错
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s -f NAMESPACE_NAME=public -o SO_ID asc -l 2", viewName);
    ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  006.带预留连接rc
                .C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }

    char nsName[128] = "function";
    char nsName1[128] = "resource";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // 修改配置项 导入用户白名单
    system(" gmrule -c import_allowlist -f ./datalog_file/root.gmuser ");

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    char *viewName = (char *)"V\\$PTL_DATALOG_SO_INFO";
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -rc -q %s", viewName);
    system(command);
    ret = executeCommand(command, "SO_NAME: function");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "SO_NAME: resource");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************向表A1插入一条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);
    AW_FUN_Log(LOG_STEP, "向表中插入数据后查询视图");
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -rc -q %s ", viewName);
    ret = executeCommand(command, "SO_NAME: resource");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "SO_NAME: function");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图 没有数据时查询正常，但是有数据该显示格式就会报错
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -rc -q %s", viewName);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    // 删除用户白名单
    system(" gmrule -c remove_allowlist -f ./datalog_file/root.gmuser ");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  007.不加载任何so查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_007)
{
    AW_FUN_Log(LOG_STEP, "test end.");
    // 查询PTL_DATALOG_SO_INFO视图
    char command[MAX_CMD_SIZE] = {0};
    char *viewName = (char *)"V\\$PTL_DATALOG_SO_INFO";
    (void)snprintf(command, MAX_CMD_SIZE, "gmsysview -q %s", viewName);
    system(command);
    char result10[100] = "";
    (void)sprintf(result10, "fetched all records, finish!");
    int ret = executeCommand(command, result10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.加载so后，卸载so查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        "function", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 3", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView("function", "B1", "DM_DTL_NORMAL",
        "C1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView("function", "A1", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView("function", "id: 8", "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.加载普通so后查询视图，卸载so后查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "normal";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        "normal", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("normal", "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("normal", "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        "normal", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("normal", "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("normal", "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("normal", "id: 8", "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.加载so后查询视图含tbm表，卸载so后查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "tbm_010";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        "tbm_010", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("tbm_010", "D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("tbm_010", "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("tbm_010", "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("tbm_010", "C", "DM_DTL_TBM");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView("tbm_010", "\"UDF_NAME\": \"dtl_tbm_tbl_C\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView("tbm_010", "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView("tbm_010", "\"UDF_NAME\": \"dtl_ext_func_init\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView("tbm_010", "\"UDF_NAME\": \"dtl_ext_func_uninit\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        "tbm_010", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 4");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("tbm_010", "D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("tbm_010", "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("tbm_010", "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("tbm_010", "C", "DM_DTL_TBM");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView("tbm_010", "\"UDF_NAME\": \"dtl_tbm_tbl_C\"", "\"UDF_NAME\": \"dtl_ext_func_tran\"",
        "\"UDF_NAME\": \"dtl_ext_func_init\"", "\"UDF_NAME\": \"dtl_ext_func_uninit\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.加载so后查询视图含state表，卸载so后查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_with_agg";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_agg_func_ns1_funcA\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_agg_func_ns1_funcA\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.加载含pusub资源表   输入表为trisent tuple  so后查询视图，卸载so后查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "pubsubResource";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "\"DM_DTL_TRANSIENT_TUPLE\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_RESOURCE_PUBSUB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "\"DM_DTL_TRANSIENT_TUPLE\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_RESOURCE_PUBSUB");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.加载so后查询视图含固定资源表,trisent field ，卸载so后查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "resource_with_filed";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 1", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.rsc0", "DM_DTL_RESOURCE_SEQUENTIAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 1", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.rsc0", "DM_DTL_RESOURCE_SEQUENTIAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(resPoolName1);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.加载so后查询视图含可更新表，卸载so后查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "resource_with_update";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 1", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.rsc0", "DM_DTL_RESOURCE_SEQUENTIAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 1", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.rsc0", "DM_DTL_RESOURCE_SEQUENTIAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(resPoolName1);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.加载so后查询视图含timeout表，卸载so后查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "out_with_timeout";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 3", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_timeout_callback_A\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 3", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_timeout_callback_A\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.加载so 含外部表，查询视图，卸载so后查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_with_outTable";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // 创建外部表
    int ret = 0;
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "C");
    readJanssonFile("./datalog_file/C.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    // .d文件加载：创建表和连接规则
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "fake_C", "DM_DTL_EXTERN");  // fake_c outtable
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, "C");
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "fake_C", "DM_DTL_EXTERN");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.加载so 含消息通知表，查询视图，卸载so后查询视图
*************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "msg_notify";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "inA1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "inA2", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "inA3", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "outA", "DM_DTL_MSG_NOTIFY");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_msg_notify_outA\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "inA1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "inA2", "DM_DTL_STATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "inA3", "DM_DTL_EXTERN");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "outA", "DM_DTL_MSG_NOTIFY");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\": \"dtl_msg_notify_outA\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.加载so name长度为256字符so，查询udf及so视图
*************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb"
                       "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb";

    // 卸载同名datalog.so
    system("mv ./datalog_file/multid.so "
           "./datalog_file/"
           "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb"
           "bbbbbbbbbbbbbbbbbbbb.so");
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 3", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A1", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "C1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 3", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A1", "DM_DTL_UPDATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "C1", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.加载so table name长度为512字符so，查询udf及so视图
*************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "longestTableName";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName,
        ""
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
        "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName,
        ""
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB",
        "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName,
        ""
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
        "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName,
        ""
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB",
        "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.加载so respoolName长度为64字符so，查询udf及so视图
*************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "longestResourceTableName";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 1", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(
        nsName, "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "DM_DTL_RESOURCE_SEQUENTIAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 1", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(
        nsName, "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "DM_DTL_RESOURCE_SEQUENTIAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(resPoolName1);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.加载so respoolName长度为65字符so，查询udf及so视图    fail
*************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 当前固定资源表表名长度收到资源池名称规格限制，当前只能创建64字节长度表名
    char nsName[128] = "outlongestResourceTableName";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 1", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
        "DM_DTL_RESOURCE_SEQUENTIAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 1", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(
        nsName, "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "DM_DTL_RESOURCE_SEQUENTIAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(resPoolName1);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.加载so中含200个固定资源表。查询udf及so视图
*************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "maxcountresourcetable";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 202", "\"RESPOOL_NUM\": 200", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "ns1.A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "ns1.B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 0;
    while (i < 200) {
        char tableName[128] = "\"reB\"";
        (void)sprintf(tableName, "ns1.reB%d", i);
        char resPoolName[64] = "\"RESPOOL_NAME\": \"reB\"";
        ret = CheckDatalogTableInfo(nsName, tableName, "DM_DTL_RESOURCE_SEQUENTIAL");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;
    }
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    bool ctlSearch = true;
    char *resPoolName1 = NULL;
    ret = GtExecSysviewCmd(
        &resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==200'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = querySoView(nsName, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(resPoolName1);
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 202", "\"RESPOOL_NUM\": 200", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    i = 0;
    while (i < 200) {
        char tableName[128] = "\"reB\"";
        (void)sprintf(tableName, "ns1.reB%d", i);
        char resPoolName[64] = "\"RESPOOL_NAME\": \"reB\"";
        (void)sprintf(resPoolName, "\"RESPOOL_NAME\": \"ns1.reB%d\"", i);
        ret = CheckDatalogTableInfo(nsName, tableName, "DM_DTL_RESOURCE_SEQUENTIAL");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        ret = querySoView(nsName, resPoolName);
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        i++;
    }
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.加载so中含200个pubsub表，查询udf及so视图
*************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "maxcountpubsubresource";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 202", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns2.A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns2.B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 0;
    while (i < 200) {
        char tableName[128] = "\"reB\"";
        (void)sprintf(tableName, "ns2.reB%d", i);
        ret = CheckDatalogTableInfo(nsName, tableName, "DM_DTL_RESOURCE_PUBSUB");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;
    }
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 202", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns2.A", "DM_DTL_TRANSIENT_FIELD");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns2.B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns2.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    i = 0;
    while (i < 200) {
        char tableName[128] = "\"reB\"";
        (void)sprintf(tableName, "ns2.reB%d", i);
        ret = CheckDatalogTableInfo(nsName, tableName, "DM_DTL_RESOURCE_PUBSUB");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        i++;
    }
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 24.加载udf名字长度为640字节so,查视图
*************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "longestUdfName";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 0;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName,
        "\"UDF_NAME\": "
        "\"funcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\"");
    ret = querySoView(nsName,
        "\"UDF_NAME\": "
        "\"dtl_ext_func_"
        "tranBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "ns1.C", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName,
        "\"UDF_NAME\": "
        "\"funcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName,
        "\"UDF_NAME\": "
        "\"dtl_ext_func_"
        "tranBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB"
        "BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 25.支持空的so视图验证  当前空的so已经不支持编译成功
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "nulltable";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    system("gmprecompiler -f ./datalog_file/nulltable.d ./datalog_file/nulltable.c");
    char command[MAX_CMD_SIZE] = {0};
    char soName[FILE_PATH] = "nulltable";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);

    // 不含state关键字不能使用状态转移函数
    AW_FUN_Log(LOG_STEP, "状态函数只能用于状态表计算.");
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "there is no table/resource defined in the datalog program", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "\"NAMESPACE_NAME\": \"public\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "id: 8", "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(nsName, "\"NAMESPACE_NAME\": \"public\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "id: 8", "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 26.加载不同so查询视图，直到表数量达到上限
**************************************************************************** */
TEST_F(soLogEnhance1, DataLog_040_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 加载so
    char nsName1[128] = "tablenum1000_1";
    char nsName2[128] = "tablenum1000_2";
    char nsName3[128] = "normal";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    TestUninstallDatalog(nsName3, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(nsName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(nsName3);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // 加载成功2000张表后查询视图
    ret = querySoView(
        nsName2, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 1000", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 1;
    while (i < 1001) {
        char tableName[128] = "\"ns1001.A00\"";
        (void)sprintf(tableName, "ns1001.A00%d", i);
        ret = CheckDatalogTableInfo(nsName2, tableName, "DM_DTL_NORMAL");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;
    }
    i = 1;
    while (i < 1024) {
        char udfName[128] = "\"UDF_NAME\": \"dtl_msg_notify_outA\"";
        (void)sprintf(udfName, "\"UDF_NAME\": \"dtl_ext_func_ns1001_funcA00%d\"", i);
        ret = querySoView(nsName2, udfName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    ret = querySoView(
        nsName1, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 1000", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    i = 1;
    while (i < 1001) {
        char tableName[128] = "\"ns100.A00\"";
        (void)sprintf(tableName, "ns1000.A00%d", i);
        ret = CheckDatalogTableInfo(nsName1, tableName, "DM_DTL_NORMAL");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;
    }
    ret = querySoView(nsName1, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName1, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(nsName3, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();
    ret = querySoView(nsName3, "\"NAMESPACE_NAME\": \"public\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    TestUninstallDatalog(nsName2, NULL, false);
    // normal
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName3, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName3, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName3, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    TestUninstallDatalog(nsName3, NULL, false);
    ret = querySoView(nsName3, "\"NAMESPACE_NAME\": \"public\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"NAMESPACE_NAME\": \"public\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName1, "\"NAMESPACE_NAME\": \"public\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.加载超规格so，查询视图
**************************************************************************** */
TEST_F(soLogEnhance1, DataLog_040_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 加载so
    char nsName1[128] = "tablenum1000_1";
    char nsName2[128] = "tablenum1000_2";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(nsName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载成功2000张表后查询视图
    ret = querySoView(
        nsName2, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 1000", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 1;
    while (i < 1001) {
        char tableName[128] = "\"ns1001.A00\"";
        (void)sprintf(tableName, "ns1001.A00%d", i);
        ret = CheckDatalogTableInfo(nsName2, tableName, "DM_DTL_NORMAL");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;
    }
    i = 1;
    while (i < 1024) {
        char udfName[128] = "\"UDF_NAME\": \"dtl_msg_notify_outA\"";
        (void)sprintf(udfName, "\"UDF_NAME\": \"dtl_ext_func_ns1001_funcA00%d\"", i);
        ret = querySoView(nsName2, udfName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    ret = querySoView(
        nsName1, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 1000", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    i = 1;
    while (i < 1001) {
        char tableName[128] = "\"ns100.A00\"";
        (void)sprintf(tableName, "ns1000.A00%d", i);
        ret = CheckDatalogTableInfo(nsName1, tableName, "DM_DTL_NORMAL");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;
    }
    ret = querySoView(nsName1, "\"UDF_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName1, "\"RESPOOL_NAME\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();

    // 卸载so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    ret = querySoView(nsName2, "\"NAMESPACE_NAME\": \"public\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName1, "\"NAMESPACE_NAME\": \"public\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.加载不同名但是含同名表so
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 加载so
    char nsName1[128] = "normal";
    char nsName2[128] = "tbm_010";
    system("gmsysview -q V\\$PTL_DATALOG_SO_INFO");

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(nsName2);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    testGmcGetLastError();

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName1, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName1, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName1, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = querySoView(
        nsName2, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 4");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "C", "DM_DTL_TBM");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"UDF_NAME\": \"dtl_tbm_tbl_C\"", "\"UDF_NAME\": \"dtl_ext_func_tran\"",
        "\"UDF_NAME\": \"dtl_ext_func_init\"", "\"UDF_NAME\": \"dtl_ext_func_uninit\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(nsName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(
        nsName2, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "C", "DM_DTL_TBM");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"UDF_NAME\": \"dtl_tbm_tbl_C\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"UDF_NAME\": \"dtl_ext_func_uninit\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"UDF_NAME\": \"dtl_ext_func_init\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName1, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName1, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName1, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName1, "id: 8", "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // DATALOG_PLAN_EXPLAIN_INFO视图
    queryPlanView();

    ret = TestUninstallDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(
        nsName2, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 4", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 4");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "D", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "B", "DM_DTL_STATE");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName2, "C", "DM_DTL_TBM");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"UDF_NAME\": \"dtl_tbm_tbl_C\"", "\"UDF_NAME\": \"dtl_ext_func_tran\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName2, "\"UDF_NAME\": \"dtl_ext_func_init\"", "\"UDF_NAME\": \"dtl_ext_func_uninit\"");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 17000  hpe环境
       029.加载第三方库失败报错日志校验
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "thirdError";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    int ret;
    char command[MAX_CMD_SIZE] = {0};

    // .d文件加载：创建表和连接规则
    (void)snprintf(
        command, MAX_CMD_SIZE, "gcc -fPIC --shared ./datalog_file/%s.c -o ./datalog_file/%s.so", nsName, nsName);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName);
    ret = executeCommand(command, "ret = 1017000");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        "normal", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("normal", "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("normal", "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo("normal", "id: 8", "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 030.function函数17001错误日志校验
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName1[128] = "function_error01";
    char nsName2[128] = "function_error02";
    char nsName3[128] = "function_error03";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    TestUninstallDatalog(nsName3, NULL, false);
    // 加载函数未定义so
    AW_FUN_Log(LOG_STEP, "加载函数未定义so.");
    int ret;
    // .d文件加载：创建表和连接规则
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName1);
    ret = executeCommand(command, "function_error01.so", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验错误日志

    // 加载函数定义错误so
    AW_FUN_Log(LOG_STEP, "加载函数定义错误so.");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName2);
    ret =
        executeCommand(command, "find function's address", "name=dtl_ext_func_tran", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志

    // 加载只包含udf.c so
    if (g_envType == 0) {
        AW_FUN_Log(LOG_STEP, "加载只包含udf.c so.");
        (void)snprintf(command, MAX_CMD_SIZE,
            "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared ./datalog_file/%s_udf.c -o "
            "./datalog_file/%s.so \n",
            nsName3, nsName3);
        system(command);
        snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName3);
        ret = executeCommand(command, "ret = 1017001");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 校验错误日志
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.aggregate函数17001错误日志校验
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName1[128] = "agg_error01";
    char nsName2[128] = "agg_error02";
    char nsName3[128] = "agg_error03";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    TestUninstallDatalog(nsName3, NULL, false);
    // 加载函数未定义so
    AW_FUN_Log(LOG_STEP, "加载函数未定义so.");
    int ret;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName1);
    ret = executeCommand(command, "agg_error01.so", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志

    // 加载函数定义错误so
    AW_FUN_Log(LOG_STEP, "加载函数定义错误so.");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "agg_error02.so", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志

    // 加载只包含udf.c so
    AW_FUN_Log(LOG_STEP, "加载只包含udf.c so.");
    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared ./datalog_file/%s_udf.c -o "
        "./datalog_file/%s.so \n",
        nsName3, nsName3);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "Load functions");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.超时回调函数17001错误日志校验
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName1[128] = "timeout_error01";
    char nsName2[128] = "timeout_error02";
    char nsName3[128] = "timeout_error03";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    TestUninstallDatalog(nsName3, NULL, false);
    // 加载函数未定义so
    AW_FUN_Log(LOG_STEP, "加载函数未定义so.");
    int ret;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName1);
    ret = executeCommand(command, "timeout_error01.so", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验错误日志

    // 加载函数定义错误so
    AW_FUN_Log(LOG_STEP, "加载函数定义错误so.");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(
        command, "find function's address, name=dtl_timeout_callback_A", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志

    // 加载只包含udf.c so
    AW_FUN_Log(LOG_STEP, "加载只包含udf.c so.");
    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared ./datalog_file/%s_udf.c -o "
        "./datalog_file/%s.so \n",
        nsName3, nsName3);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "Load functions");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.init/uninit 函数17001错误日志校验
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName1[128] = "tbm_error01";
    char nsName2[128] = "tbm_error02";
    char nsName3[128] = "tbm_error03";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    TestUninstallDatalog(nsName3, NULL, false);
    // 加载函数未定义so
    AW_FUN_Log(LOG_STEP, "加载函数未定义so.");
    int ret;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName1);
    ret = executeCommand(command, "tbm_error01.so", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志

    // 加载函数定义错误so
    AW_FUN_Log(LOG_STEP, "加载函数定义错误so.");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "find function's address, name=dtl_tbm_tbl_C", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验错误日志

    // 加载只包含udf.c so
    AW_FUN_Log(LOG_STEP, "加载只包含udf.c so.");
    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared ./datalog_file/%s_udf.c -o "
        "./datalog_file/%s.so \n",
        nsName3, nsName3);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "Load functions");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.消息通知表udf函数17001错误日志校验
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName1[128] = "msg_error01";
    char nsName2[128] = "msg_error02";
    char nsName3[128] = "msg_error03";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    TestUninstallDatalog(nsName3, NULL, false);
    // 加载函数未定义so
    AW_FUN_Log(LOG_STEP, "加载函数未定义so.");
    int ret;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName1);
    ret = executeCommand(command, "msg_error01.so", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志

    // 加载函数定义错误so
    AW_FUN_Log(LOG_STEP, "加载函数定义错误so.");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName2);
    ret =
        executeCommand(command, "find function's address, name=dtl_msg_notify_outA", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志

    // 加载只包含udf.c so
    AW_FUN_Log(LOG_STEP, "加载只包含udf.c so.");
    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared ./datalog_file/%s_udf.c -o "
        "./datalog_file/%s.so \n",
        nsName3, nsName3);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "Load functions");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.状态转移函数17001错误日志校验
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName1[128] = "state_error01";
    char nsName2[128] = "state_error02";
    char nsName3[128] = "state_error03";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);
    TestUninstallDatalog(nsName3, NULL, false);
    // 加载函数未定义so
    AW_FUN_Log(LOG_STEP, "加载函数未定义so.");
    int ret;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName1);
    ret = executeCommand(command, "state_error01.so", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验错误日志

    // 加载函数定义错误so
    AW_FUN_Log(LOG_STEP, "加载函数定义错误so.");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "find function's address, name=dtl_ext_func_tran", "ret = 1017001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志

    // 加载只包含udf.c so
    AW_FUN_Log(LOG_STEP, "加载只包含udf.c so.");
    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared ./datalog_file/%s_udf.c -o "
        "./datalog_file/%s.so \n",
        nsName3, nsName3);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "Load functions");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验错误日志
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.17000序列化和反序列化错误,
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_036)
{
    // 当前版本不一致不报错，只记录warning日志
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "thirdError";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    int ret;
    char cmdExe[128] = "sed -i 's/dbVersion = \"GMDB 5/dbVersion = \"GMDB 6/g' ./datalog_file/thirdError.c";
    // .d文件加载：创建表和连接规则
    system(cmdExe);
    AW_FUN_Log(LOG_STEP, "加载只包含.c so.");
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared ./datalog_file/%s.c -o "
        "./datalog_file/%s.so \n",
        nsName, nsName);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./datalog_file/%s.so", g_toolPath, nsName);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = CheckLog(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询PTL_DATALOG_SO_INFO视图
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // DATALOG_PLAN_EXPLAIN_INFO视图
    ret = queryPlanView();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(nsName, NULL, false);
    ret = querySoView(
        nsName, "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "B", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckDatalogTableInfo(nsName, "A", "DM_DTL_NORMAL");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = querySoView(nsName, "name: UDF_INFO");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.循环10000次，加载so查询视图，卸载so查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "normal";

    // 卸载同名datalog.so
    int cycle = 0;
    // 环境性能波动，用例执行超时，用例优化，降低循环次数（1000->500)
    while (cycle < 500) {
        TestUninstallDatalog(nsName, NULL, false);

        // .d文件加载：创建表和连接规则
        int ret = LoadSoFile(nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 查询PTL_DATALOG_SO_INFO视图
        ret = querySoView(
            "normal", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CheckDatalogTableInfo("normal", "B", "DM_DTL_NORMAL");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CheckDatalogTableInfo("normal", "A", "DM_DTL_NORMAL");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // DATALOG_PLAN_EXPLAIN_INFO视图
        ret = queryPlanView();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 卸载datalog.so
        ret = TestUninstallDatalog(nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 查询PTL_DATALOG_SO_INFO视图
        ret = querySoView(
            "normal", "\"NAMESPACE_NAME\": \"public\"", "\"TABLE_NUM\": 2", "\"RESPOOL_NUM\": 0", "\"UDF_NUM\": 0");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        ret = CheckDatalogTableInfo("normal", "B", "DM_DTL_NORMAL");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        ret = CheckDatalogTableInfo("normal", "A", "DM_DTL_NORMAL");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        ret = querySoView("normal", "id: 8", "name: UDF_INFO");
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        // DATALOG_PLAN_EXPLAIN_INFO视图
        queryPlanView();
        cycle++;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
bool isStop = false;
void *INORUNSTALLSO(void *args)
{
    char nsName[128] = "normal";
    int cycle = 100;
    int ret;
    while (cycle > 0) {
        // 卸载同名datalog.so
        TestUninstallDatalog(nsName, NULL, false);

        // .d文件加载：创建表和连接规则
        ret = LoadSoFile(nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 卸载datalog.so
        ret = TestUninstallDatalog(nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        cycle--;
    }
    isStop = true;
}

void *QUERYSOVIEW(void *args)
{
    while (!isStop) {
        querySoView("normal", "\"NAMESPACE_NAME\": \"public\"");
    }
}

void *INSERTSO(void *args)
{
    int ret = 0;
    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************向表A1插入一条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);
}

void *QUERYSOVIEWONCE(void *args)
{
    querySoView("normal", "\"NAMESPACE_NAME\": \"public\"");
}

/* ****************************************************************************
 Description  : 038.多线程，一个线程循环加载，卸载so10000次，另一个线程一直查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    int ret = pthread_create(&client_thr[0], NULL, INORUNSTALLSO, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, QUERYSOVIEW, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.加载so后开启多个线程，一个线程写入数据，另外9个线程查询视图
**************************************************************************** */
TEST_F(soLogEnhance, DataLog_040_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程
    int32_t thread_num = 10;
    pthread_t client_thr[thread_num];
    // 写入数据
    ret = pthread_create(&client_thr[0], NULL, INSERTSO, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询视图
    for (int i = 1; i < 10; i++) {
        ret = pthread_create(&client_thr[i], NULL, QUERYSOVIEWONCE, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}
