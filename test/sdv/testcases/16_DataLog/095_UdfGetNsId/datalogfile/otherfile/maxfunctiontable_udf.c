/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: tbm_udf
 * Author: qibingsen 00880292
 * Create: 2024-12-23
 */

#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct Func {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
    int32_t d;
} Func;

typedef struct FuncAggIn {
    int32_t dtlReservedCount;
    int64_t a;
} FuncAggIn;

typedef struct FuncAggOut {
    int64_t b;
} FuncAggOut;

typedef struct INP3 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} INP3;

typedef struct INP5 {
    int64_t a;
} INP5;

typedef struct INP6 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} INP6;

typedef struct STATTuple {
    int32_t count;
    int64_t a;
    int64_t b;
} STATTupleT;

typedef struct MSG {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} MSG;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t write_into_file(GmUdfCtxT *ctx, const char *fileName, const char *msg)
{
    FILE *fp = fopen(fileName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "%s. the namespaceid is %d\n", msg, namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t compare(const void *tuple1, const void *tuple2)
{
    INP6 *inp1 = (INP6 *)tuple1;
    INP6 *inp2 = (INP6 *)tuple2;
    if (inp1->c < inp2->c) {
        return -1;
    } else if (inp1->c > inp2->c) {
        return 1;
    } else {
        return 0;
    }
}

int32_t dtl_ext_func_funcinp1(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp1";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp2(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp2";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp3(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp3";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp4(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp4";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp5(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp5";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp6(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp6";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp7(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp7";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp8(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp8";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp9(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp9";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp10(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp10";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp11(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp11";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp12(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp12";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp13(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp13";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp14(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp14";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp15(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp15";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp16(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp16";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp17(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp17";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp18(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp18";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp19(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp19";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp20(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp20";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp21(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp21";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp22(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp22";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp23(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp23";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp24(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp24";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp25(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp25";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp26(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp26";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp27(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp27";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp28(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp28";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp29(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp29";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp30(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp30";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp31(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp31";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp32(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp32";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp33(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp33";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp34(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp34";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp35(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp35";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp36(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp36";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp37(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp37";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp38(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp38";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp39(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp39";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp40(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp40";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp41(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp41";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp42(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp42";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp43(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp43";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp44(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp44";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp45(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp45";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp46(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp46";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp47(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp47";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp48(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp48";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp49(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp49";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp50(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp50";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp51(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp51";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp52(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp52";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp53(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp53";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp54(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp54";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp55(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp55";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp56(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp56";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp57(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp57";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp58(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp58";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp59(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp59";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp60(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp60";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp61(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp61";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp62(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp62";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp63(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp63";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp64(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp64";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp65(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp65";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp66(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp66";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp67(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp67";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp68(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp68";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp69(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp69";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp70(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp70";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp71(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp71";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp72(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp72";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp73(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp73";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp74(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp74";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp75(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp75";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp76(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp76";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp77(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp77";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp78(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp78";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp79(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp79";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp80(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp80";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp81(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp81";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp82(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp82";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp83(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp83";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp84(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp84";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp85(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp85";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp86(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp86";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp87(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp87";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp88(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp88";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp89(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp89";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp90(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp90";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp91(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp91";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp92(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp92";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp93(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp93";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp94(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp94";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp95(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp95";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp96(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp96";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp97(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp97";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp98(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp98";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp99(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp99";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp100(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp100";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp101(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp101";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp102(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp102";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp103(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp103";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp104(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp104";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp105(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp105";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp106(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp106";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp107(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp107";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp108(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp108";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp109(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp109";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp110(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp110";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp111(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp111";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp112(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp112";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp113(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp113";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp114(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp114";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp115(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp115";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp116(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp116";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp117(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp117";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp118(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp118";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp119(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp119";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp120(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp120";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp121(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp121";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp122(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp122";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp123(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp123";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp124(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp124";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp125(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp125";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp126(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp126";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp127(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp127";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp128(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp128";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp129(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp129";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp130(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp130";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp131(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp131";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp132(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp132";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp133(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp133";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp134(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp134";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp135(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp135";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp136(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp136";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp137(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp137";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp138(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp138";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp139(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp139";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp140(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp140";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp141(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp141";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp142(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp142";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp143(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp143";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp144(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp144";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp145(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp145";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp146(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp146";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp147(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp147";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp148(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp148";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp149(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp149";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp150(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp150";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp151(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp151";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp152(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp152";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp153(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp153";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp154(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp154";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp155(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp155";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp156(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp156";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp157(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp157";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp158(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp158";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp159(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp159";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp160(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp160";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp161(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp161";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp162(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp162";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp163(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp163";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp164(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp164";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp165(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp165";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp166(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp166";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp167(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp167";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp168(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp168";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp169(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp169";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp170(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp170";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp171(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp171";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp172(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp172";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp173(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp173";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp174(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp174";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp175(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp175";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp176(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp176";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp177(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp177";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp178(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp178";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp179(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp179";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp180(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp180";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp181(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp181";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp182(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp182";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp183(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp183";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp184(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp184";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp185(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp185";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp186(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp186";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp187(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp187";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp188(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp188";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp189(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp189";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp190(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp190";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp191(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp191";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp192(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp192";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp193(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp193";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp194(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp194";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp195(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp195";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp196(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp196";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp197(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp197";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp198(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp198";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp199(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp199";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp200(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp200";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp201(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp201";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp202(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp202";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp203(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp203";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp204(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp204";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp205(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp205";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp206(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp206";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp207(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp207";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp208(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp208";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp209(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp209";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp210(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp210";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp211(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp211";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp212(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp212";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp213(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp213";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp214(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp214";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp215(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp215";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp216(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp216";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp217(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp217";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp218(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp218";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp219(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp219";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp220(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp220";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp221(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp221";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp222(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp222";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp223(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp223";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp224(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp224";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp225(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp225";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp226(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp226";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp227(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp227";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp228(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp228";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp229(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp229";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp230(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp230";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp231(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp231";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp232(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp232";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp233(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp233";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp234(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp234";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp235(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp235";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp236(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp236";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp237(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp237";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp238(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp238";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp239(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp239";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp240(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp240";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp241(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp241";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp242(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp242";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp243(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp243";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp244(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp244";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp245(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp245";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp246(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp246";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp247(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp247";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp248(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp248";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp249(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp249";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp250(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp250";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp251(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp251";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp252(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp252";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp253(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp253";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp254(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp254";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp255(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp255";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp256(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp256";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp257(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp257";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp258(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp258";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp259(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp259";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp260(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp260";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp261(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp261";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp262(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp262";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp263(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp263";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp264(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp264";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp265(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp265";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp266(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp266";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp267(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp267";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp268(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp268";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp269(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp269";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp270(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp270";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp271(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp271";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp272(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp272";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp273(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp273";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp274(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp274";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp275(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp275";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp276(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp276";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp277(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp277";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp278(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp278";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp279(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp279";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp280(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp280";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp281(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp281";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp282(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp282";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp283(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp283";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp284(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp284";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp285(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp285";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp286(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp286";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp287(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp287";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp288(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp288";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp289(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp289";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp290(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp290";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp291(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp291";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp292(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp292";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp293(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp293";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp294(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp294";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp295(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp295";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp296(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp296";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp297(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp297";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp298(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp298";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp299(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp299";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp300(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp300";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp301(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp301";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp302(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp302";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp303(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp303";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp304(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp304";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp305(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp305";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp306(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp306";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp307(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp307";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp308(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp308";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp309(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp309";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp310(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp310";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp311(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp311";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp312(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp312";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp313(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp313";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp314(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp314";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp315(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp315";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp316(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp316";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp317(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp317";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp318(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp318";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp319(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp319";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp320(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp320";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp321(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp321";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp322(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp322";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp323(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp323";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp324(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp324";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp325(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp325";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp326(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp326";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp327(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp327";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp328(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp328";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp329(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp329";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp330(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp330";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp331(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp331";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp332(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp332";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp333(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp333";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp334(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp334";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp335(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp335";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp336(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp336";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp337(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp337";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp338(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp338";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp339(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp339";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp340(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp340";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp341(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp341";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp342(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp342";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp343(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp343";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp344(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp344";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp345(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp345";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp346(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp346";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp347(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp347";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp348(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp348";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp349(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp349";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp350(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp350";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp351(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp351";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp352(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp352";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp353(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp353";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp354(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp354";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp355(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp355";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp356(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp356";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp357(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp357";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp358(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp358";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp359(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp359";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp360(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp360";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp361(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp361";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp362(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp362";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp363(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp363";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp364(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp364";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp365(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp365";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp366(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp366";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp367(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp367";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp368(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp368";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp369(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp369";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp370(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp370";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp371(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp371";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp372(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp372";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp373(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp373";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp374(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp374";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp375(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp375";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp376(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp376";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp377(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp377";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp378(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp378";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp379(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp379";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp380(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp380";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp381(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp381";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp382(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp382";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp383(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp383";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp384(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp384";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp385(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp385";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp386(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp386";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp387(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp387";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp388(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp388";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp389(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp389";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp390(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp390";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp391(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp391";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp392(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp392";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp393(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp393";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp394(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp394";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp395(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp395";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp396(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp396";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp397(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp397";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp398(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp398";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp399(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp399";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp400(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp400";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp401(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp401";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp402(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp402";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp403(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp403";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp404(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp404";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp405(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp405";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp406(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp406";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp407(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp407";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp408(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp408";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp409(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp409";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp410(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp410";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp411(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp411";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp412(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp412";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp413(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp413";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp414(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp414";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp415(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp415";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp416(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp416";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp417(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp417";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp418(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp418";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp419(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp419";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp420(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp420";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp421(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp421";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp422(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp422";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp423(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp423";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp424(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp424";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp425(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp425";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp426(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp426";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp427(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp427";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp428(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp428";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp429(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp429";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp430(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp430";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp431(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp431";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp432(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp432";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp433(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp433";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp434(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp434";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp435(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp435";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp436(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp436";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp437(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp437";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp438(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp438";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp439(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp439";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp440(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp440";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp441(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp441";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp442(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp442";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp443(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp443";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp444(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp444";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp445(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp445";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp446(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp446";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp447(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp447";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp448(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp448";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp449(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp449";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp450(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp450";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp451(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp451";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp452(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp452";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp453(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp453";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp454(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp454";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp455(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp455";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp456(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp456";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp457(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp457";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp458(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp458";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp459(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp459";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp460(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp460";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp461(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp461";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp462(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp462";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp463(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp463";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp464(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp464";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp465(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp465";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp466(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp466";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp467(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp467";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp468(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp468";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp469(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp469";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp470(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp470";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp471(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp471";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp472(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp472";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp473(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp473";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp474(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp474";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp475(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp475";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp476(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp476";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp477(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp477";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp478(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp478";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp479(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp479";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp480(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp480";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp481(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp481";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp482(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp482";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp483(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp483";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp484(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp484";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp485(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp485";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp486(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp486";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp487(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp487";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp488(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp488";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp489(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp489";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp490(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp490";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp491(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp491";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp492(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp492";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp493(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp493";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp494(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp494";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp495(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp495";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp496(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp496";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp497(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp497";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp498(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp498";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp499(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp499";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcinp500(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp500";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid1(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid1";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid2(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid2";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid3(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid3";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid4(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid4";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid5(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid5";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid6(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid6";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid7(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid7";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid8(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid8";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid9(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid9";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid10(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid10";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid11(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid11";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid12(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid12";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid13(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid13";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid14(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid14";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid15(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid15";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid16(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid16";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid17(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid17";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid18(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid18";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid19(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid19";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid20(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid20";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid21(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid21";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid22(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid22";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid23(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid23";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid24(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid24";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid25(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid25";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid26(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid26";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid27(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid27";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid28(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid28";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid29(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid29";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid30(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid30";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid31(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid31";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid32(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid32";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid33(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid33";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid34(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid34";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid35(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid35";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid36(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid36";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid37(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid37";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid38(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid38";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid39(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid39";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid40(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid40";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid41(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid41";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid42(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid42";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid43(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid43";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid44(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid44";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid45(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid45";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid46(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid46";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid47(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid47";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid48(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid48";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid49(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid49";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid50(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid50";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid51(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid51";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid52(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid52";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid53(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid53";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid54(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid54";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid55(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid55";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid56(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid56";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid57(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid57";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid58(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid58";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid59(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid59";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid60(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid60";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid61(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid61";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid62(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid62";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid63(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid63";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid64(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid64";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid65(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid65";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid66(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid66";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid67(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid67";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid68(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid68";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid69(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid69";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid70(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid70";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid71(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid71";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid72(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid72";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid73(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid73";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid74(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid74";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid75(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid75";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid76(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid76";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid77(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid77";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid78(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid78";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid79(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid79";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid80(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid80";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid81(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid81";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid82(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid82";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid83(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid83";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid84(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid84";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid85(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid85";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid86(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid86";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid87(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid87";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid88(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid88";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid89(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid89";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid90(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid90";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid91(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid91";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid92(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid92";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid93(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid93";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid94(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid94";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid95(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid95";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid96(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid96";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid97(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid97";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid98(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid98";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid99(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid99";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid100(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid100";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid101(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid101";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid102(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid102";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid103(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid103";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid104(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid104";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid105(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid105";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid106(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid106";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid107(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid107";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid108(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid108";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid109(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid109";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid110(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid110";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid111(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid111";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid112(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid112";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid113(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid113";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid114(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid114";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid115(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid115";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid116(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid116";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid117(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid117";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid118(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid118";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid119(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid119";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid120(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid120";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid121(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid121";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid122(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid122";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid123(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid123";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid124(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid124";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid125(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid125";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid126(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid126";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid127(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid127";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid128(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid128";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid129(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid129";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid130(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid130";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid131(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid131";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid132(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid132";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid133(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid133";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid134(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid134";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid135(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid135";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid136(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid136";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid137(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid137";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid138(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid138";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid139(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid139";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid140(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid140";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid141(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid141";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid142(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid142";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid143(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid143";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid144(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid144";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid145(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid145";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid146(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid146";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid147(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid147";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid148(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid148";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid149(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid149";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid150(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid150";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid151(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid151";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid152(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid152";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid153(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid153";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid154(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid154";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid155(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid155";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid156(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid156";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid157(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid157";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid158(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid158";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid159(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid159";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid160(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid160";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid161(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid161";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid162(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid162";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid163(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid163";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid164(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid164";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid165(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid165";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid166(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid166";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid167(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid167";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid168(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid168";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid169(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid169";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid170(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid170";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid171(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid171";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid172(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid172";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid173(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid173";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid174(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid174";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid175(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid175";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid176(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid176";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid177(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid177";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid178(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid178";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid179(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid179";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid180(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid180";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid181(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid181";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid182(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid182";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid183(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid183";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid184(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid184";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid185(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid185";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid186(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid186";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid187(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid187";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid188(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid188";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid189(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid189";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid190(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid190";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid191(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid191";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid192(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid192";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid193(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid193";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid194(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid194";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid195(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid195";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid196(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid196";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid197(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid197";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid198(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid198";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid199(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid199";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid200(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid200";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid201(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid201";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid202(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid202";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid203(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid203";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid204(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid204";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid205(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid205";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid206(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid206";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid207(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid207";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid208(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid208";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid209(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid209";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid210(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid210";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid211(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid211";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid212(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid212";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid213(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid213";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid214(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid214";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid215(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid215";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid216(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid216";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid217(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid217";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid218(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid218";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid219(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid219";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid220(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid220";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid221(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid221";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid222(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid222";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid223(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid223";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid224(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid224";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid225(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid225";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid226(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid226";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid227(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid227";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid228(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid228";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid229(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid229";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid230(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid230";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid231(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid231";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid232(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid232";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid233(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid233";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid234(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid234";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid235(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid235";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid236(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid236";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid237(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid237";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid238(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid238";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid239(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid239";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid240(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid240";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid241(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid241";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid242(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid242";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid243(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid243";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid244(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid244";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid245(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid245";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid246(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid246";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid247(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid247";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid248(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid248";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid249(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid249";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_ext_func_funcmid250(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcmid250";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_compare_tuple_inp1(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp1";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp2(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp2";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp3(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp3";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp4(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp4";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp5(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp5";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp6(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp6";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp7(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp7";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp8(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp8";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp9(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp9";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp10(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp10";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp11(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp11";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp12(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp12";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp13(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp13";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp14(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp14";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp15(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp15";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp16(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp16";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp17(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp17";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp18(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp18";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp19(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp19";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp20(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp20";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp21(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp21";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp22(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp22";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp23(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp23";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp24(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp24";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp25(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp25";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp26(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp26";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp27(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp27";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp28(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp28";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp29(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp29";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp30(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp30";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp31(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp31";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp32(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp32";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp33(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp33";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp34(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp34";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp35(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp35";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp36(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp36";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp37(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp37";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp38(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp38";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp39(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp39";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp40(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp40";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp41(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp41";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp42(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp42";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp43(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp43";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp44(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp44";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp45(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp45";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp46(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp46";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp47(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp47";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp48(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp48";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp49(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp49";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp50(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp50";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp51(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp51";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp52(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp52";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp53(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp53";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp54(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp54";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp55(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp55";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp56(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp56";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp57(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp57";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp58(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp58";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp59(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp59";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp60(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp60";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp61(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp61";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp62(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp62";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp63(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp63";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp64(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp64";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp65(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp65";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp66(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp66";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp67(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp67";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp68(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp68";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp69(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp69";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp70(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp70";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp71(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp71";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp72(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp72";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp73(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp73";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp74(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp74";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp75(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp75";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp76(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp76";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp77(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp77";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp78(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp78";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp79(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp79";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp80(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp80";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp81(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp81";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp82(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp82";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp83(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp83";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp84(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp84";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp85(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp85";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp86(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp86";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp87(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp87";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp88(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp88";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp89(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp89";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp90(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp90";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp91(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp91";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp92(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp92";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp93(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp93";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp94(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp94";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp95(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp95";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp96(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp96";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp97(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp97";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp98(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp98";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp99(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp99";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp100(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp100";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp101(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp101";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp102(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp102";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp103(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp103";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp104(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp104";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp105(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp105";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp106(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp106";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp107(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp107";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp108(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp108";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp109(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp109";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp110(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp110";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp111(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp111";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp112(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp112";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp113(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp113";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp114(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp114";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp115(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp115";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp116(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp116";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp117(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp117";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp118(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp118";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp119(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp119";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp120(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp120";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp121(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp121";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp122(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp122";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp123(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp123";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp124(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp124";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp125(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp125";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp126(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp126";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp127(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp127";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp128(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp128";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp129(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp129";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp130(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp130";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp131(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp131";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp132(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp132";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp133(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp133";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp134(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp134";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp135(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp135";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp136(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp136";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp137(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp137";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp138(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp138";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp139(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp139";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp140(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp140";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp141(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp141";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp142(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp142";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp143(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp143";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp144(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp144";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp145(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp145";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp146(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp146";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp147(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp147";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp148(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp148";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp149(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp149";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp150(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp150";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp151(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp151";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp152(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp152";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp153(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp153";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp154(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp154";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp155(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp155";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp156(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp156";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp157(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp157";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp158(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp158";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp159(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp159";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp160(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp160";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp161(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp161";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp162(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp162";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp163(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp163";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp164(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp164";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp165(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp165";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp166(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp166";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp167(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp167";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp168(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp168";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp169(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp169";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp170(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp170";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp171(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp171";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp172(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp172";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp173(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp173";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp174(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp174";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp175(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp175";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp176(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp176";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp177(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp177";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp178(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp178";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp179(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp179";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp180(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp180";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp181(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp181";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp182(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp182";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp183(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp183";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp184(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp184";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp185(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp185";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp186(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp186";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp187(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp187";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp188(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp188";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp189(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp189";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp190(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp190";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp191(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp191";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp192(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp192";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp193(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp193";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp194(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp194";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp195(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp195";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp196(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp196";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp197(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp197";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp198(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp198";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp199(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp199";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp200(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp200";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp201(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp201";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp202(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp202";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp203(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp203";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp204(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp204";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp205(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp205";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp206(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp206";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp207(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp207";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp208(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp208";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp209(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp209";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp210(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp210";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp211(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp211";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp212(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp212";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp213(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp213";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp214(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp214";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp215(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp215";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp216(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp216";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp217(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp217";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp218(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp218";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp219(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp219";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp220(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp220";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp221(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp221";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp222(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp222";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp223(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp223";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp224(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp224";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp225(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp225";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp226(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp226";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp227(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp227";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp228(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp228";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp229(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp229";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp230(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp230";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp231(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp231";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp232(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp232";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp233(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp233";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp234(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp234";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp235(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp235";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp236(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp236";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp237(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp237";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp238(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp238";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp239(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp239";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp240(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp240";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp241(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp241";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp242(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp242";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp243(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp243";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp244(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp244";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp245(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp245";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp246(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp246";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp247(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp247";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp248(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp248";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp249(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp249";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp250(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp250";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp251(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp251";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp252(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp252";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp253(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp253";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp254(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp254";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp255(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp255";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp256(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp256";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp257(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp257";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp258(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp258";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp259(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp259";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_compare_tuple_inp260(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp260";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_init. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_uninit. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_funcinp501(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_funcinp501. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_out501(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    GmUdfCtxT *ctx;
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out501. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_timeout_callback_inp501(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
                                    GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_timeout_callback_inp501. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    INP3 *input = (INP3 *)timeoutTuple;
    INP3 *output = GmUdfMemAlloc(ctx, sizeof(INP3));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a;
    output->b = input->b;
    output->c = input->c;
    output->dtlReservedCount = 0;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(INP3);

    return GMERR_OK;
}

int32_t dtl_agg_func_agginp504(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_agg_func_agginp504. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
    }
    outStruct->b = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    return GMERR_OK;
}

int32_t dtl_agg_func_agginp505(GmUdfReaderT *input, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_agg_func_agginp505. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
    }
    outStruct->b = sum;
    *output = (void *)outStruct;

    return GMERR_OK;
}

int32_t dtl_agg_compare_agginp505(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    INP5 *inp1 = (INP5 *)tuple1;
    INP5 *inp2 = (INP5 *)tuple2;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_agg_compare_agginp505. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}

int32_t dtl_compare_tuple_inp507(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp507";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_ext_func_pre_invoke()
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    GmUdfCtxT *ctx;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_pre_invoke. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_post_invoke()
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    GmUdfCtxT *ctx;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_post_invoke. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_funcstat(void *tuple, GmUdfCtxT *ctx)
{
    STATTupleT *out = (STATTupleT *)tuple;
    int64_t *a = &out->a;
    int64_t *b = &out->b;
    int32_t *count = &out->count;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_funcstat. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    if (*count == 1) {
        *b = 2;
        *count = -1;
        return GMERR_OK;
    } else if (*count == 0) {
        *b = 1;
        *count = 1;
        return GMERR_OK;
    } else {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_NO_DATA;
}

int32_t dtl_msg_notify_msg(GmMsgNotifyTupleChangeT *tups, uint32_t arrLen)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    GmUdfCtxT *ctx;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_msg_notify_msg. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_msg_notify_compare_msg(void *tup1, void *tup2)
{
    MSG *msg1 = (MSG *)tup1;
    MSG *msg2 = (MSG *)tup2;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    GmUdfCtxT *ctx;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_msg_notify_compare_msg. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    if (msg1->a == msg2->a) {
        if (msg1->b == msg2->b) {
            return msg2->c - msg1->c;
        } else {
            return msg2->b - msg1->b;
        }
    }

    return msg2->a - msg1->a;
}
