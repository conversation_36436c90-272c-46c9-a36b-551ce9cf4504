/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: tbm_udf
 * Author: qibingsen 00880292
 * Create: 2024-12-23
 */

#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct Func {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
    int32_t d;
} Func;

typedef struct FuncAggIn {
    int32_t dtlReservedCount;
    int64_t a;
} FuncAggIn;

typedef struct FuncAggOut {
    int64_t b;
} FuncAggOut;

typedef struct INP3 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} INP3;

typedef struct INP5 {
    int64_t a;
} INP5;

typedef struct INP6 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} INP6;

typedef struct STATTuple {
    int32_t count;
    int64_t a;
    int64_t b;
} STATTupleT;

typedef struct MSG {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} MSG;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

// tbm
int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_init. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_uninit. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t compare(const void *tuple1, const void *tuple2)
{
    INP6 *inp1 = (INP6 *)tuple1;
    INP6 *inp2 = (INP6 *)tuple2;
    if (inp1->c < inp2->c) {
        return -1;
    } else if (inp1->c > inp2->c) {
        return 1;
    } else {
        return 0;
    }
}

int32_t write_into_file(GmUdfCtxT *ctx, const char *fileName, const char *msg)
{
    FILE *fp = fopen(fileName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "%s. the namespaceid is %d\n", msg, namespaceid);
    (void)fclose(fp);
}

int32_t dtl_tbm_tbl_dnsp1_out1(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    GmUdfCtxT *ctx;
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_dnsp1_out1. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_funcinp1(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b + f->c;
    char *msg = "dtl_ext_func_funcinp1";
    int ret = write_into_file(ctx, g_logName, msg);
    return ret;
}

int32_t dtl_timeout_callback_dnsp1_inp1(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
                                        GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_timeout_callback_dnsp1_inp1. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    INP3 *input = (INP3 *)timeoutTuple;
    INP3 *output = GmUdfMemAlloc(ctx, sizeof(INP3));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a;
    output->b = input->b;
    output->c = input->c;
    output->dtlReservedCount = 0;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(INP3);

    return GMERR_OK;
}

int32_t dtl_agg_func_agginp4(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_agg_func_agginp4. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
    }
    outStruct->b = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    return GMERR_OK;
}

int32_t dtl_agg_func_agginp5(GmUdfReaderT *input, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_agg_func_agginp5. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum += inpStruct->a;
    }
    outStruct->b = sum;
    *output = (void *)outStruct;

    return GMERR_OK;
}

int32_t dtl_agg_compare_agginp5(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    INP5 *inp1 = (INP5 *)tuple1;
    INP5 *inp2 = (INP5 *)tuple2;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_agg_compare_agginp5. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}

int32_t dtl_compare_tuple_inp7(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    char *msg = "dtl_compare_tuple_inp7";
    int ret = write_into_file(ctx, g_logName, msg);
    if (ret != 0) {
        return ret;
    }
    return compare(tuple1, tuple2);
}

int32_t dtl_ext_func_pre_invoke()
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    GmUdfCtxT *ctx;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_pre_invoke. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_post_invoke()
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    GmUdfCtxT *ctx;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_post_invoke. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_funcstat(void *tuple, GmUdfCtxT *ctx)
{
    STATTupleT *out = (STATTupleT *)tuple;
    int64_t *a = &out->a;
    int64_t *b = &out->b;
    int32_t *count = &out->count;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_funcstat. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    if (*count == 1) {
        *b = 2;
        *count = -1;
        return GMERR_OK;
    } else if (*count == 0) {
        *b = 1;
        *count = 1;
        return GMERR_OK;
    } else {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_NO_DATA;
}

int32_t dtl_msg_notify_msg(GmMsgNotifyTupleChangeT *tups, uint32_t arrLen)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    GmUdfCtxT *ctx;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_msg_notify_msg. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_msg_notify_compare_msg(void *tup1, void *tup2)
{
    MSG *msg1 = (MSG *)tup1;
    MSG *msg2 = (MSG *)tup2;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    GmUdfCtxT *ctx;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_msg_notify_compare_msg. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    if (msg1->a == msg2->a) {
        if (msg1->b == msg2->b) {
            return msg2->c - msg1->c;
        } else {
            return msg2->b - msg1->b;
        }
    }

    return msg2->a - msg1->a;
}
