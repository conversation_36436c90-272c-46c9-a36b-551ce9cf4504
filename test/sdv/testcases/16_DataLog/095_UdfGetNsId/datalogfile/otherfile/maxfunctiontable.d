%table inp1(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp2(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp3(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp4(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp5(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp6(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp7(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp8(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp9(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp10(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp11(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp12(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp13(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp14(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp15(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp16(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp17(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp18(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp19(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp20(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp21(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp22(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp23(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp24(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp25(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp26(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp27(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp28(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp29(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp30(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp31(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp32(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp33(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp34(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp35(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp36(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp37(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp38(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp39(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp40(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp41(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp42(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp43(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp44(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp45(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp46(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp47(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp48(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp49(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp50(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp51(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp52(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp53(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp54(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp55(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp56(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp57(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp58(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp59(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp60(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp61(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp62(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp63(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp64(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp65(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp66(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp67(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp68(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp69(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp70(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp71(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp72(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp73(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp74(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp75(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp76(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp77(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp78(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp79(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp80(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp81(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp82(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp83(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp84(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp85(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp86(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp87(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp88(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp89(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp90(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp91(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp92(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp93(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp94(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp95(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp96(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp97(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp98(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp99(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp100(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table mid1(a:int8, b:int8, c:int8){index(0(a))}
%table mid2(a:int8, b:int8, c:int8){index(0(a))}
%table mid3(a:int8, b:int8, c:int8){index(0(a))}
%table mid4(a:int8, b:int8, c:int8){index(0(a))}
%table mid5(a:int8, b:int8, c:int8){index(0(a))}
%table mid6(a:int8, b:int8, c:int8){index(0(a))}
%table mid7(a:int8, b:int8, c:int8){index(0(a))}
%table mid8(a:int8, b:int8, c:int8){index(0(a))}
%table mid9(a:int8, b:int8, c:int8){index(0(a))}
%table mid10(a:int8, b:int8, c:int8){index(0(a))}
%table mid11(a:int8, b:int8, c:int8){index(0(a))}
%table mid12(a:int8, b:int8, c:int8){index(0(a))}
%table mid13(a:int8, b:int8, c:int8){index(0(a))}
%table mid14(a:int8, b:int8, c:int8){index(0(a))}
%table mid15(a:int8, b:int8, c:int8){index(0(a))}
%table mid16(a:int8, b:int8, c:int8){index(0(a))}
%table mid17(a:int8, b:int8, c:int8){index(0(a))}
%table mid18(a:int8, b:int8, c:int8){index(0(a))}
%table mid19(a:int8, b:int8, c:int8){index(0(a))}
%table mid20(a:int8, b:int8, c:int8){index(0(a))}
%table mid21(a:int8, b:int8, c:int8){index(0(a))}
%table mid22(a:int8, b:int8, c:int8){index(0(a))}
%table mid23(a:int8, b:int8, c:int8){index(0(a))}
%table mid24(a:int8, b:int8, c:int8){index(0(a))}
%table mid25(a:int8, b:int8, c:int8){index(0(a))}
%table mid26(a:int8, b:int8, c:int8){index(0(a))}
%table mid27(a:int8, b:int8, c:int8){index(0(a))}
%table mid28(a:int8, b:int8, c:int8){index(0(a))}
%table mid29(a:int8, b:int8, c:int8){index(0(a))}
%table mid30(a:int8, b:int8, c:int8){index(0(a))}
%table mid31(a:int8, b:int8, c:int8){index(0(a))}
%table mid32(a:int8, b:int8, c:int8){index(0(a))}
%table mid33(a:int8, b:int8, c:int8){index(0(a))}
%table mid34(a:int8, b:int8, c:int8){index(0(a))}
%table mid35(a:int8, b:int8, c:int8){index(0(a))}
%table mid36(a:int8, b:int8, c:int8){index(0(a))}
%table mid37(a:int8, b:int8, c:int8){index(0(a))}
%table mid38(a:int8, b:int8, c:int8){index(0(a))}
%table mid39(a:int8, b:int8, c:int8){index(0(a))}
%table mid40(a:int8, b:int8, c:int8){index(0(a))}
%table mid41(a:int8, b:int8, c:int8){index(0(a))}
%table mid42(a:int8, b:int8, c:int8){index(0(a))}
%table mid43(a:int8, b:int8, c:int8){index(0(a))}
%table mid44(a:int8, b:int8, c:int8){index(0(a))}
%table mid45(a:int8, b:int8, c:int8){index(0(a))}
%table mid46(a:int8, b:int8, c:int8){index(0(a))}
%table mid47(a:int8, b:int8, c:int8){index(0(a))}
%table mid48(a:int8, b:int8, c:int8){index(0(a))}
%table mid49(a:int8, b:int8, c:int8){index(0(a))}
%table mid50(a:int8, b:int8, c:int8){index(0(a))}
%table mid51(a:int8, b:int8, c:int8){index(0(a))}
%table mid52(a:int8, b:int8, c:int8){index(0(a))}
%table mid53(a:int8, b:int8, c:int8){index(0(a))}
%table mid54(a:int8, b:int8, c:int8){index(0(a))}
%table mid55(a:int8, b:int8, c:int8){index(0(a))}
%table mid56(a:int8, b:int8, c:int8){index(0(a))}
%table mid57(a:int8, b:int8, c:int8){index(0(a))}
%table mid58(a:int8, b:int8, c:int8){index(0(a))}
%table mid59(a:int8, b:int8, c:int8){index(0(a))}
%table mid60(a:int8, b:int8, c:int8){index(0(a))}
%table mid61(a:int8, b:int8, c:int8){index(0(a))}
%table mid62(a:int8, b:int8, c:int8){index(0(a))}
%table mid63(a:int8, b:int8, c:int8){index(0(a))}
%table mid64(a:int8, b:int8, c:int8){index(0(a))}
%table mid65(a:int8, b:int8, c:int8){index(0(a))}
%table mid66(a:int8, b:int8, c:int8){index(0(a))}
%table mid67(a:int8, b:int8, c:int8){index(0(a))}
%table mid68(a:int8, b:int8, c:int8){index(0(a))}
%table mid69(a:int8, b:int8, c:int8){index(0(a))}
%table mid70(a:int8, b:int8, c:int8){index(0(a))}
%table mid71(a:int8, b:int8, c:int8){index(0(a))}
%table mid72(a:int8, b:int8, c:int8){index(0(a))}
%table mid73(a:int8, b:int8, c:int8){index(0(a))}
%table mid74(a:int8, b:int8, c:int8){index(0(a))}
%table mid75(a:int8, b:int8, c:int8){index(0(a))}
%table mid76(a:int8, b:int8, c:int8){index(0(a))}
%table mid77(a:int8, b:int8, c:int8){index(0(a))}
%table mid78(a:int8, b:int8, c:int8){index(0(a))}
%table mid79(a:int8, b:int8, c:int8){index(0(a))}
%table mid80(a:int8, b:int8, c:int8){index(0(a))}
%table mid81(a:int8, b:int8, c:int8){index(0(a))}
%table mid82(a:int8, b:int8, c:int8){index(0(a))}
%table mid83(a:int8, b:int8, c:int8){index(0(a))}
%table mid84(a:int8, b:int8, c:int8){index(0(a))}
%table mid85(a:int8, b:int8, c:int8){index(0(a))}
%table mid86(a:int8, b:int8, c:int8){index(0(a))}
%table mid87(a:int8, b:int8, c:int8){index(0(a))}
%table mid88(a:int8, b:int8, c:int8){index(0(a))}
%table mid89(a:int8, b:int8, c:int8){index(0(a))}
%table mid90(a:int8, b:int8, c:int8){index(0(a))}
%table mid91(a:int8, b:int8, c:int8){index(0(a))}
%table mid92(a:int8, b:int8, c:int8){index(0(a))}
%table mid93(a:int8, b:int8, c:int8){index(0(a))}
%table mid94(a:int8, b:int8, c:int8){index(0(a))}
%table mid95(a:int8, b:int8, c:int8){index(0(a))}
%table mid96(a:int8, b:int8, c:int8){index(0(a))}
%table mid97(a:int8, b:int8, c:int8){index(0(a))}
%table mid98(a:int8, b:int8, c:int8){index(0(a))}
%table mid99(a:int8, b:int8, c:int8){index(0(a))}
%table mid100(a:int8, b:int8, c:int8){index(0(a))}
%table inp101(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp102(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp103(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp104(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp105(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp106(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp107(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp108(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp109(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp110(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp111(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp112(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp113(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp114(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp115(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp116(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp117(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp118(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp119(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp120(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp121(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp122(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp123(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp124(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp125(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp126(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp127(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp128(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp129(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp130(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp131(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp132(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp133(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp134(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp135(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp136(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp137(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp138(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp139(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp140(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp141(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp142(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp143(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp144(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp145(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp146(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp147(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp148(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp149(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp150(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp151(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp152(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp153(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp154(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp155(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp156(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp157(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp158(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp159(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp160(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp161(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp162(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp163(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp164(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp165(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp166(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp167(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp168(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp169(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp170(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp171(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp172(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp173(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp174(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp175(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp176(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp177(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp178(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp179(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp180(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp181(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp182(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp183(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp184(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp185(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp186(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp187(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp188(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp189(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp190(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp191(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp192(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp193(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp194(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp195(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp196(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp197(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp198(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp199(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp200(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table mid101(a:int8, b:int8, c:int8){index(0(a))}
%table mid102(a:int8, b:int8, c:int8){index(0(a))}
%table mid103(a:int8, b:int8, c:int8){index(0(a))}
%table mid104(a:int8, b:int8, c:int8){index(0(a))}
%table mid105(a:int8, b:int8, c:int8){index(0(a))}
%table mid106(a:int8, b:int8, c:int8){index(0(a))}
%table mid107(a:int8, b:int8, c:int8){index(0(a))}
%table mid108(a:int8, b:int8, c:int8){index(0(a))}
%table mid109(a:int8, b:int8, c:int8){index(0(a))}
%table mid110(a:int8, b:int8, c:int8){index(0(a))}
%table mid111(a:int8, b:int8, c:int8){index(0(a))}
%table mid112(a:int8, b:int8, c:int8){index(0(a))}
%table mid113(a:int8, b:int8, c:int8){index(0(a))}
%table mid114(a:int8, b:int8, c:int8){index(0(a))}
%table mid115(a:int8, b:int8, c:int8){index(0(a))}
%table mid116(a:int8, b:int8, c:int8){index(0(a))}
%table mid117(a:int8, b:int8, c:int8){index(0(a))}
%table mid118(a:int8, b:int8, c:int8){index(0(a))}
%table mid119(a:int8, b:int8, c:int8){index(0(a))}
%table mid120(a:int8, b:int8, c:int8){index(0(a))}
%table mid121(a:int8, b:int8, c:int8){index(0(a))}
%table mid122(a:int8, b:int8, c:int8){index(0(a))}
%table mid123(a:int8, b:int8, c:int8){index(0(a))}
%table mid124(a:int8, b:int8, c:int8){index(0(a))}
%table mid125(a:int8, b:int8, c:int8){index(0(a))}
%table mid126(a:int8, b:int8, c:int8){index(0(a))}
%table mid127(a:int8, b:int8, c:int8){index(0(a))}
%table mid128(a:int8, b:int8, c:int8){index(0(a))}
%table mid129(a:int8, b:int8, c:int8){index(0(a))}
%table mid130(a:int8, b:int8, c:int8){index(0(a))}
%table mid131(a:int8, b:int8, c:int8){index(0(a))}
%table mid132(a:int8, b:int8, c:int8){index(0(a))}
%table mid133(a:int8, b:int8, c:int8){index(0(a))}
%table mid134(a:int8, b:int8, c:int8){index(0(a))}
%table mid135(a:int8, b:int8, c:int8){index(0(a))}
%table mid136(a:int8, b:int8, c:int8){index(0(a))}
%table mid137(a:int8, b:int8, c:int8){index(0(a))}
%table mid138(a:int8, b:int8, c:int8){index(0(a))}
%table mid139(a:int8, b:int8, c:int8){index(0(a))}
%table mid140(a:int8, b:int8, c:int8){index(0(a))}
%table mid141(a:int8, b:int8, c:int8){index(0(a))}
%table mid142(a:int8, b:int8, c:int8){index(0(a))}
%table mid143(a:int8, b:int8, c:int8){index(0(a))}
%table mid144(a:int8, b:int8, c:int8){index(0(a))}
%table mid145(a:int8, b:int8, c:int8){index(0(a))}
%table mid146(a:int8, b:int8, c:int8){index(0(a))}
%table mid147(a:int8, b:int8, c:int8){index(0(a))}
%table mid148(a:int8, b:int8, c:int8){index(0(a))}
%table mid149(a:int8, b:int8, c:int8){index(0(a))}
%table mid150(a:int8, b:int8, c:int8){index(0(a))}
%table mid151(a:int8, b:int8, c:int8){index(0(a))}
%table mid152(a:int8, b:int8, c:int8){index(0(a))}
%table mid153(a:int8, b:int8, c:int8){index(0(a))}
%table mid154(a:int8, b:int8, c:int8){index(0(a))}
%table mid155(a:int8, b:int8, c:int8){index(0(a))}
%table mid156(a:int8, b:int8, c:int8){index(0(a))}
%table mid157(a:int8, b:int8, c:int8){index(0(a))}
%table mid158(a:int8, b:int8, c:int8){index(0(a))}
%table mid159(a:int8, b:int8, c:int8){index(0(a))}
%table mid160(a:int8, b:int8, c:int8){index(0(a))}
%table mid161(a:int8, b:int8, c:int8){index(0(a))}
%table mid162(a:int8, b:int8, c:int8){index(0(a))}
%table mid163(a:int8, b:int8, c:int8){index(0(a))}
%table mid164(a:int8, b:int8, c:int8){index(0(a))}
%table mid165(a:int8, b:int8, c:int8){index(0(a))}
%table mid166(a:int8, b:int8, c:int8){index(0(a))}
%table mid167(a:int8, b:int8, c:int8){index(0(a))}
%table mid168(a:int8, b:int8, c:int8){index(0(a))}
%table mid169(a:int8, b:int8, c:int8){index(0(a))}
%table mid170(a:int8, b:int8, c:int8){index(0(a))}
%table mid171(a:int8, b:int8, c:int8){index(0(a))}
%table mid172(a:int8, b:int8, c:int8){index(0(a))}
%table mid173(a:int8, b:int8, c:int8){index(0(a))}
%table mid174(a:int8, b:int8, c:int8){index(0(a))}
%table mid175(a:int8, b:int8, c:int8){index(0(a))}
%table mid176(a:int8, b:int8, c:int8){index(0(a))}
%table mid177(a:int8, b:int8, c:int8){index(0(a))}
%table mid178(a:int8, b:int8, c:int8){index(0(a))}
%table mid179(a:int8, b:int8, c:int8){index(0(a))}
%table mid180(a:int8, b:int8, c:int8){index(0(a))}
%table mid181(a:int8, b:int8, c:int8){index(0(a))}
%table mid182(a:int8, b:int8, c:int8){index(0(a))}
%table mid183(a:int8, b:int8, c:int8){index(0(a))}
%table mid184(a:int8, b:int8, c:int8){index(0(a))}
%table mid185(a:int8, b:int8, c:int8){index(0(a))}
%table mid186(a:int8, b:int8, c:int8){index(0(a))}
%table mid187(a:int8, b:int8, c:int8){index(0(a))}
%table mid188(a:int8, b:int8, c:int8){index(0(a))}
%table mid189(a:int8, b:int8, c:int8){index(0(a))}
%table mid190(a:int8, b:int8, c:int8){index(0(a))}
%table mid191(a:int8, b:int8, c:int8){index(0(a))}
%table mid192(a:int8, b:int8, c:int8){index(0(a))}
%table mid193(a:int8, b:int8, c:int8){index(0(a))}
%table mid194(a:int8, b:int8, c:int8){index(0(a))}
%table mid195(a:int8, b:int8, c:int8){index(0(a))}
%table mid196(a:int8, b:int8, c:int8){index(0(a))}
%table mid197(a:int8, b:int8, c:int8){index(0(a))}
%table mid198(a:int8, b:int8, c:int8){index(0(a))}
%table mid199(a:int8, b:int8, c:int8){index(0(a))}
%table mid200(a:int8, b:int8, c:int8){index(0(a))}
%table inp201(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp202(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp203(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp204(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp205(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp206(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp207(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp208(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp209(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp210(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp211(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp212(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp213(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp214(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp215(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp216(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp217(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp218(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp219(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp220(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp221(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp222(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp223(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp224(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp225(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp226(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp227(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp228(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp229(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp230(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp231(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp232(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp233(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp234(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp235(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp236(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp237(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp238(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp239(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp240(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp241(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp242(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp243(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp244(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp245(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp246(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp247(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp248(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp249(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp250(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table mid201(a:int8, b:int8, c:int8){index(0(a))}
%table mid202(a:int8, b:int8, c:int8){index(0(a))}
%table mid203(a:int8, b:int8, c:int8){index(0(a))}
%table mid204(a:int8, b:int8, c:int8){index(0(a))}
%table mid205(a:int8, b:int8, c:int8){index(0(a))}
%table mid206(a:int8, b:int8, c:int8){index(0(a))}
%table mid207(a:int8, b:int8, c:int8){index(0(a))}
%table mid208(a:int8, b:int8, c:int8){index(0(a))}
%table mid209(a:int8, b:int8, c:int8){index(0(a))}
%table mid210(a:int8, b:int8, c:int8){index(0(a))}
%table mid211(a:int8, b:int8, c:int8){index(0(a))}
%table mid212(a:int8, b:int8, c:int8){index(0(a))}
%table mid213(a:int8, b:int8, c:int8){index(0(a))}
%table mid214(a:int8, b:int8, c:int8){index(0(a))}
%table mid215(a:int8, b:int8, c:int8){index(0(a))}
%table mid216(a:int8, b:int8, c:int8){index(0(a))}
%table mid217(a:int8, b:int8, c:int8){index(0(a))}
%table mid218(a:int8, b:int8, c:int8){index(0(a))}
%table mid219(a:int8, b:int8, c:int8){index(0(a))}
%table mid220(a:int8, b:int8, c:int8){index(0(a))}
%table mid221(a:int8, b:int8, c:int8){index(0(a))}
%table mid222(a:int8, b:int8, c:int8){index(0(a))}
%table mid223(a:int8, b:int8, c:int8){index(0(a))}
%table mid224(a:int8, b:int8, c:int8){index(0(a))}
%table mid225(a:int8, b:int8, c:int8){index(0(a))}
%table mid226(a:int8, b:int8, c:int8){index(0(a))}
%table mid227(a:int8, b:int8, c:int8){index(0(a))}
%table mid228(a:int8, b:int8, c:int8){index(0(a))}
%table mid229(a:int8, b:int8, c:int8){index(0(a))}
%table mid230(a:int8, b:int8, c:int8){index(0(a))}
%table mid231(a:int8, b:int8, c:int8){index(0(a))}
%table mid232(a:int8, b:int8, c:int8){index(0(a))}
%table mid233(a:int8, b:int8, c:int8){index(0(a))}
%table mid234(a:int8, b:int8, c:int8){index(0(a))}
%table mid235(a:int8, b:int8, c:int8){index(0(a))}
%table mid236(a:int8, b:int8, c:int8){index(0(a))}
%table mid237(a:int8, b:int8, c:int8){index(0(a))}
%table mid238(a:int8, b:int8, c:int8){index(0(a))}
%table mid239(a:int8, b:int8, c:int8){index(0(a))}
%table mid240(a:int8, b:int8, c:int8){index(0(a))}
%table mid241(a:int8, b:int8, c:int8){index(0(a))}
%table mid242(a:int8, b:int8, c:int8){index(0(a))}
%table mid243(a:int8, b:int8, c:int8){index(0(a))}
%table mid244(a:int8, b:int8, c:int8){index(0(a))}
%table mid245(a:int8, b:int8, c:int8){index(0(a))}
%table mid246(a:int8, b:int8, c:int8){index(0(a))}
%table mid247(a:int8, b:int8, c:int8){index(0(a))}
%table mid248(a:int8, b:int8, c:int8){index(0(a))}
%table mid249(a:int8, b:int8, c:int8){index(0(a))}
%table mid250(a:int8, b:int8, c:int8){index(0(a))}
%table out1(a:int8, b:int8, c:int8){index(0(a))}
%table out2(a:int8, b:int8, c:int8){index(0(a))}
%table out3(a:int8, b:int8, c:int8){index(0(a))}
%table out4(a:int8, b:int8, c:int8){index(0(a))}
%table out5(a:int8, b:int8, c:int8){index(0(a))}
%table out6(a:int8, b:int8, c:int8){index(0(a))}
%table out7(a:int8, b:int8, c:int8){index(0(a))}
%table out8(a:int8, b:int8, c:int8){index(0(a))}
%table out9(a:int8, b:int8, c:int8){index(0(a))}
%table out10(a:int8, b:int8, c:int8){index(0(a))}
%table out11(a:int8, b:int8, c:int8){index(0(a))}
%table out12(a:int8, b:int8, c:int8){index(0(a))}
%table out13(a:int8, b:int8, c:int8){index(0(a))}
%table out14(a:int8, b:int8, c:int8){index(0(a))}
%table out15(a:int8, b:int8, c:int8){index(0(a))}
%table out16(a:int8, b:int8, c:int8){index(0(a))}
%table out17(a:int8, b:int8, c:int8){index(0(a))}
%table out18(a:int8, b:int8, c:int8){index(0(a))}
%table out19(a:int8, b:int8, c:int8){index(0(a))}
%table out20(a:int8, b:int8, c:int8){index(0(a))}
%table out21(a:int8, b:int8, c:int8){index(0(a))}
%table out22(a:int8, b:int8, c:int8){index(0(a))}
%table out23(a:int8, b:int8, c:int8){index(0(a))}
%table out24(a:int8, b:int8, c:int8){index(0(a))}
%table out25(a:int8, b:int8, c:int8){index(0(a))}
%table out26(a:int8, b:int8, c:int8){index(0(a))}
%table out27(a:int8, b:int8, c:int8){index(0(a))}
%table out28(a:int8, b:int8, c:int8){index(0(a))}
%table out29(a:int8, b:int8, c:int8){index(0(a))}
%table out30(a:int8, b:int8, c:int8){index(0(a))}
%table out31(a:int8, b:int8, c:int8){index(0(a))}
%table out32(a:int8, b:int8, c:int8){index(0(a))}
%table out33(a:int8, b:int8, c:int8){index(0(a))}
%table out34(a:int8, b:int8, c:int8){index(0(a))}
%table out35(a:int8, b:int8, c:int8){index(0(a))}
%table out36(a:int8, b:int8, c:int8){index(0(a))}
%table out37(a:int8, b:int8, c:int8){index(0(a))}
%table out38(a:int8, b:int8, c:int8){index(0(a))}
%table out39(a:int8, b:int8, c:int8){index(0(a))}
%table out40(a:int8, b:int8, c:int8){index(0(a))}
%table out41(a:int8, b:int8, c:int8){index(0(a))}
%table out42(a:int8, b:int8, c:int8){index(0(a))}
%table out43(a:int8, b:int8, c:int8){index(0(a))}
%table out44(a:int8, b:int8, c:int8){index(0(a))}
%table out45(a:int8, b:int8, c:int8){index(0(a))}
%table out46(a:int8, b:int8, c:int8){index(0(a))}
%table out47(a:int8, b:int8, c:int8){index(0(a))}
%table out48(a:int8, b:int8, c:int8){index(0(a))}
%table out49(a:int8, b:int8, c:int8){index(0(a))}
%table out50(a:int8, b:int8, c:int8){index(0(a))}
%table out51(a:int8, b:int8, c:int8){index(0(a))}
%table out52(a:int8, b:int8, c:int8){index(0(a))}
%table out53(a:int8, b:int8, c:int8){index(0(a))}
%table out54(a:int8, b:int8, c:int8){index(0(a))}
%table out55(a:int8, b:int8, c:int8){index(0(a))}
%table out56(a:int8, b:int8, c:int8){index(0(a))}
%table out57(a:int8, b:int8, c:int8){index(0(a))}
%table out58(a:int8, b:int8, c:int8){index(0(a))}
%table out59(a:int8, b:int8, c:int8){index(0(a))}
%table out60(a:int8, b:int8, c:int8){index(0(a))}
%table out61(a:int8, b:int8, c:int8){index(0(a))}
%table out62(a:int8, b:int8, c:int8){index(0(a))}
%table out63(a:int8, b:int8, c:int8){index(0(a))}
%table out64(a:int8, b:int8, c:int8){index(0(a))}
%table out65(a:int8, b:int8, c:int8){index(0(a))}
%table out66(a:int8, b:int8, c:int8){index(0(a))}
%table out67(a:int8, b:int8, c:int8){index(0(a))}
%table out68(a:int8, b:int8, c:int8){index(0(a))}
%table out69(a:int8, b:int8, c:int8){index(0(a))}
%table out70(a:int8, b:int8, c:int8){index(0(a))}
%table out71(a:int8, b:int8, c:int8){index(0(a))}
%table out72(a:int8, b:int8, c:int8){index(0(a))}
%table out73(a:int8, b:int8, c:int8){index(0(a))}
%table out74(a:int8, b:int8, c:int8){index(0(a))}
%table out75(a:int8, b:int8, c:int8){index(0(a))}
%table out76(a:int8, b:int8, c:int8){index(0(a))}
%table out77(a:int8, b:int8, c:int8){index(0(a))}
%table out78(a:int8, b:int8, c:int8){index(0(a))}
%table out79(a:int8, b:int8, c:int8){index(0(a))}
%table out80(a:int8, b:int8, c:int8){index(0(a))}
%table out81(a:int8, b:int8, c:int8){index(0(a))}
%table out82(a:int8, b:int8, c:int8){index(0(a))}
%table out83(a:int8, b:int8, c:int8){index(0(a))}
%table out84(a:int8, b:int8, c:int8){index(0(a))}
%table out85(a:int8, b:int8, c:int8){index(0(a))}
%table out86(a:int8, b:int8, c:int8){index(0(a))}
%table out87(a:int8, b:int8, c:int8){index(0(a))}
%table out88(a:int8, b:int8, c:int8){index(0(a))}
%table out89(a:int8, b:int8, c:int8){index(0(a))}
%table out90(a:int8, b:int8, c:int8){index(0(a))}
%table out91(a:int8, b:int8, c:int8){index(0(a))}
%table out92(a:int8, b:int8, c:int8){index(0(a))}
%table out93(a:int8, b:int8, c:int8){index(0(a))}
%table out94(a:int8, b:int8, c:int8){index(0(a))}
%table out95(a:int8, b:int8, c:int8){index(0(a))}
%table out96(a:int8, b:int8, c:int8){index(0(a))}
%table out97(a:int8, b:int8, c:int8){index(0(a))}
%table out98(a:int8, b:int8, c:int8){index(0(a))}
%table out99(a:int8, b:int8, c:int8){index(0(a))}
%table out100(a:int8, b:int8, c:int8){index(0(a))}
%table out101(a:int8, b:int8, c:int8){index(0(a))}
%table out102(a:int8, b:int8, c:int8){index(0(a))}
%table out103(a:int8, b:int8, c:int8){index(0(a))}
%table out104(a:int8, b:int8, c:int8){index(0(a))}
%table out105(a:int8, b:int8, c:int8){index(0(a))}
%table out106(a:int8, b:int8, c:int8){index(0(a))}
%table out107(a:int8, b:int8, c:int8){index(0(a))}
%table out108(a:int8, b:int8, c:int8){index(0(a))}
%table out109(a:int8, b:int8, c:int8){index(0(a))}
%table out110(a:int8, b:int8, c:int8){index(0(a))}
%table out111(a:int8, b:int8, c:int8){index(0(a))}
%table out112(a:int8, b:int8, c:int8){index(0(a))}
%table out113(a:int8, b:int8, c:int8){index(0(a))}
%table out114(a:int8, b:int8, c:int8){index(0(a))}
%table out115(a:int8, b:int8, c:int8){index(0(a))}
%table out116(a:int8, b:int8, c:int8){index(0(a))}
%table out117(a:int8, b:int8, c:int8){index(0(a))}
%table out118(a:int8, b:int8, c:int8){index(0(a))}
%table out119(a:int8, b:int8, c:int8){index(0(a))}
%table out120(a:int8, b:int8, c:int8){index(0(a))}
%table out121(a:int8, b:int8, c:int8){index(0(a))}
%table out122(a:int8, b:int8, c:int8){index(0(a))}
%table out123(a:int8, b:int8, c:int8){index(0(a))}
%table out124(a:int8, b:int8, c:int8){index(0(a))}
%table out125(a:int8, b:int8, c:int8){index(0(a))}
%table out126(a:int8, b:int8, c:int8){index(0(a))}
%table out127(a:int8, b:int8, c:int8){index(0(a))}
%table out128(a:int8, b:int8, c:int8){index(0(a))}
%table out129(a:int8, b:int8, c:int8){index(0(a))}
%table out130(a:int8, b:int8, c:int8){index(0(a))}
%table out131(a:int8, b:int8, c:int8){index(0(a))}
%table out132(a:int8, b:int8, c:int8){index(0(a))}
%table out133(a:int8, b:int8, c:int8){index(0(a))}
%table out134(a:int8, b:int8, c:int8){index(0(a))}
%table out135(a:int8, b:int8, c:int8){index(0(a))}
%table out136(a:int8, b:int8, c:int8){index(0(a))}
%table out137(a:int8, b:int8, c:int8){index(0(a))}
%table out138(a:int8, b:int8, c:int8){index(0(a))}
%table out139(a:int8, b:int8, c:int8){index(0(a))}
%table out140(a:int8, b:int8, c:int8){index(0(a))}
%table out141(a:int8, b:int8, c:int8){index(0(a))}
%table out142(a:int8, b:int8, c:int8){index(0(a))}
%table out143(a:int8, b:int8, c:int8){index(0(a))}
%table out144(a:int8, b:int8, c:int8){index(0(a))}
%table out145(a:int8, b:int8, c:int8){index(0(a))}
%table out146(a:int8, b:int8, c:int8){index(0(a))}
%table out147(a:int8, b:int8, c:int8){index(0(a))}
%table out148(a:int8, b:int8, c:int8){index(0(a))}
%table out149(a:int8, b:int8, c:int8){index(0(a))}
%table out150(a:int8, b:int8, c:int8){index(0(a))}
%table out151(a:int8, b:int8, c:int8){index(0(a))}
%table out152(a:int8, b:int8, c:int8){index(0(a))}
%table out153(a:int8, b:int8, c:int8){index(0(a))}
%table out154(a:int8, b:int8, c:int8){index(0(a))}
%table out155(a:int8, b:int8, c:int8){index(0(a))}
%table out156(a:int8, b:int8, c:int8){index(0(a))}
%table out157(a:int8, b:int8, c:int8){index(0(a))}
%table out158(a:int8, b:int8, c:int8){index(0(a))}
%table out159(a:int8, b:int8, c:int8){index(0(a))}
%table out160(a:int8, b:int8, c:int8){index(0(a))}
%table out161(a:int8, b:int8, c:int8){index(0(a))}
%table out162(a:int8, b:int8, c:int8){index(0(a))}
%table out163(a:int8, b:int8, c:int8){index(0(a))}
%table out164(a:int8, b:int8, c:int8){index(0(a))}
%table out165(a:int8, b:int8, c:int8){index(0(a))}
%table out166(a:int8, b:int8, c:int8){index(0(a))}
%table out167(a:int8, b:int8, c:int8){index(0(a))}
%table out168(a:int8, b:int8, c:int8){index(0(a))}
%table out169(a:int8, b:int8, c:int8){index(0(a))}
%table out170(a:int8, b:int8, c:int8){index(0(a))}
%table out171(a:int8, b:int8, c:int8){index(0(a))}
%table out172(a:int8, b:int8, c:int8){index(0(a))}
%table out173(a:int8, b:int8, c:int8){index(0(a))}
%table out174(a:int8, b:int8, c:int8){index(0(a))}
%table out175(a:int8, b:int8, c:int8){index(0(a))}
%table out176(a:int8, b:int8, c:int8){index(0(a))}
%table out177(a:int8, b:int8, c:int8){index(0(a))}
%table out178(a:int8, b:int8, c:int8){index(0(a))}
%table out179(a:int8, b:int8, c:int8){index(0(a))}
%table out180(a:int8, b:int8, c:int8){index(0(a))}
%table out181(a:int8, b:int8, c:int8){index(0(a))}
%table out182(a:int8, b:int8, c:int8){index(0(a))}
%table out183(a:int8, b:int8, c:int8){index(0(a))}
%table out184(a:int8, b:int8, c:int8){index(0(a))}
%table out185(a:int8, b:int8, c:int8){index(0(a))}
%table out186(a:int8, b:int8, c:int8){index(0(a))}
%table out187(a:int8, b:int8, c:int8){index(0(a))}
%table out188(a:int8, b:int8, c:int8){index(0(a))}
%table out189(a:int8, b:int8, c:int8){index(0(a))}
%table out190(a:int8, b:int8, c:int8){index(0(a))}
%table out191(a:int8, b:int8, c:int8){index(0(a))}
%table out192(a:int8, b:int8, c:int8){index(0(a))}
%table out193(a:int8, b:int8, c:int8){index(0(a))}
%table out194(a:int8, b:int8, c:int8){index(0(a))}
%table out195(a:int8, b:int8, c:int8){index(0(a))}
%table out196(a:int8, b:int8, c:int8){index(0(a))}
%table out197(a:int8, b:int8, c:int8){index(0(a))}
%table out198(a:int8, b:int8, c:int8){index(0(a))}
%table out199(a:int8, b:int8, c:int8){index(0(a))}
%table out200(a:int8, b:int8, c:int8){index(0(a))}
%table out201(a:int8, b:int8, c:int8){index(0(a))}
%table out202(a:int8, b:int8, c:int8){index(0(a))}
%table out203(a:int8, b:int8, c:int8){index(0(a))}
%table out204(a:int8, b:int8, c:int8){index(0(a))}
%table out205(a:int8, b:int8, c:int8){index(0(a))}
%table out206(a:int8, b:int8, c:int8){index(0(a))}
%table out207(a:int8, b:int8, c:int8){index(0(a))}
%table out208(a:int8, b:int8, c:int8){index(0(a))}
%table out209(a:int8, b:int8, c:int8){index(0(a))}
%table out210(a:int8, b:int8, c:int8){index(0(a))}
%table out211(a:int8, b:int8, c:int8){index(0(a))}
%table out212(a:int8, b:int8, c:int8){index(0(a))}
%table out213(a:int8, b:int8, c:int8){index(0(a))}
%table out214(a:int8, b:int8, c:int8){index(0(a))}
%table out215(a:int8, b:int8, c:int8){index(0(a))}
%table out216(a:int8, b:int8, c:int8){index(0(a))}
%table out217(a:int8, b:int8, c:int8){index(0(a))}
%table out218(a:int8, b:int8, c:int8){index(0(a))}
%table out219(a:int8, b:int8, c:int8){index(0(a))}
%table out220(a:int8, b:int8, c:int8){index(0(a))}
%table out221(a:int8, b:int8, c:int8){index(0(a))}
%table out222(a:int8, b:int8, c:int8){index(0(a))}
%table out223(a:int8, b:int8, c:int8){index(0(a))}
%table out224(a:int8, b:int8, c:int8){index(0(a))}
%table out225(a:int8, b:int8, c:int8){index(0(a))}
%table out226(a:int8, b:int8, c:int8){index(0(a))}
%table out227(a:int8, b:int8, c:int8){index(0(a))}
%table out228(a:int8, b:int8, c:int8){index(0(a))}
%table out229(a:int8, b:int8, c:int8){index(0(a))}
%table out230(a:int8, b:int8, c:int8){index(0(a))}
%table out231(a:int8, b:int8, c:int8){index(0(a))}
%table out232(a:int8, b:int8, c:int8){index(0(a))}
%table out233(a:int8, b:int8, c:int8){index(0(a))}
%table out234(a:int8, b:int8, c:int8){index(0(a))}
%table out235(a:int8, b:int8, c:int8){index(0(a))}
%table out236(a:int8, b:int8, c:int8){index(0(a))}
%table out237(a:int8, b:int8, c:int8){index(0(a))}
%table out238(a:int8, b:int8, c:int8){index(0(a))}
%table out239(a:int8, b:int8, c:int8){index(0(a))}
%table inp251(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp252(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp253(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp254(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp255(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp256(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp257(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp258(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp259(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp260(a:int8, b:int8, c:int8){index(0(a)), update_partial, update_by_rank}
%table inp261(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp262(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp263(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp264(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp265(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp266(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp267(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp268(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp269(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp270(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp271(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp272(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp273(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp274(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp275(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp276(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp277(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp278(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp279(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp280(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp281(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp282(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp283(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp284(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp285(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp286(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp287(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp288(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp289(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp290(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp291(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp292(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp293(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp294(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp295(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp296(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp297(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp298(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp299(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp300(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp301(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp302(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp303(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp304(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp305(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp306(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp307(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp308(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp309(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp310(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp311(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp312(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp313(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp314(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp315(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp316(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp317(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp318(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp319(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp320(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp321(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp322(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp323(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp324(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp325(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp326(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp327(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp328(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp329(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp330(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp331(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp332(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp333(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp334(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp335(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp336(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp337(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp338(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp339(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp340(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp341(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp342(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp343(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp344(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp345(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp346(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp347(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp348(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp349(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp350(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp351(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp352(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp353(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp354(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp355(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp356(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp357(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp358(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp359(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp360(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp361(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp362(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp363(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp364(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp365(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp366(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp367(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp368(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp369(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp370(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp371(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp372(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp373(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp374(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp375(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp376(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp377(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp378(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp379(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp380(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp381(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp382(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp383(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp384(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp385(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp386(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp387(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp388(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp389(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp390(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp391(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp392(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp393(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp394(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp395(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp396(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp397(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp398(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp399(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp400(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp401(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp402(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp403(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp404(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp405(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp406(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp407(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp408(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp409(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp410(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp411(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp412(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp413(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp414(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp415(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp416(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp417(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp418(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp419(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp420(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp421(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp422(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp423(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp424(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp425(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp426(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp427(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp428(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp429(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp430(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp431(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp432(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp433(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp434(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp435(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp436(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp437(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp438(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp439(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp440(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp441(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp442(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp443(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp444(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp445(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp446(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp447(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp448(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp449(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp450(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp451(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp452(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp453(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp454(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp455(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp456(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp457(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp458(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp459(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp460(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp461(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp462(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp463(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp464(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp465(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp466(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp467(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp468(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp469(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp470(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp471(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp472(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp473(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp474(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp475(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp476(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp477(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp478(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp479(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp480(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp481(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp482(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp483(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp484(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp485(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp486(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp487(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp488(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp489(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp490(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp491(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp492(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp493(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp494(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp495(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp496(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp497(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp498(a:int8, b:int8, c:int8){index(0(a, b, c))}
%table inp499(a:int8, b:int8, c:int8){index(0(a, b, c))}
mid1(a, b, f):-inp1(a,b,c), funcinp1(a,b,c,d),funcinp251(a,b,d,e),funcmid1(a,b,e,f).
mid2(a, b, f):-inp2(a,b,c), funcinp2(a,b,c,d),funcinp252(a,b,d,e),funcmid2(a,b,e,f).
mid3(a, b, f):-inp3(a,b,c), funcinp3(a,b,c,d),funcinp253(a,b,d,e),funcmid3(a,b,e,f).
mid4(a, b, f):-inp4(a,b,c), funcinp4(a,b,c,d),funcinp254(a,b,d,e),funcmid4(a,b,e,f).
mid5(a, b, f):-inp5(a,b,c), funcinp5(a,b,c,d),funcinp255(a,b,d,e),funcmid5(a,b,e,f).
mid6(a, b, f):-inp6(a,b,c), funcinp6(a,b,c,d),funcinp256(a,b,d,e),funcmid6(a,b,e,f).
mid7(a, b, f):-inp7(a,b,c), funcinp7(a,b,c,d),funcinp257(a,b,d,e),funcmid7(a,b,e,f).
mid8(a, b, f):-inp8(a,b,c), funcinp8(a,b,c,d),funcinp258(a,b,d,e),funcmid8(a,b,e,f).
mid9(a, b, f):-inp9(a,b,c), funcinp9(a,b,c,d),funcinp259(a,b,d,e),funcmid9(a,b,e,f).
mid10(a, b, f):-inp10(a,b,c), funcinp10(a,b,c,d),funcinp260(a,b,d,e),funcmid10(a,b,e,f).
mid11(a, b, f):-inp11(a,b,c), funcinp11(a,b,c,d),funcinp261(a,b,d,e),funcmid11(a,b,e,f).
mid12(a, b, f):-inp12(a,b,c), funcinp12(a,b,c,d),funcinp262(a,b,d,e),funcmid12(a,b,e,f).
mid13(a, b, f):-inp13(a,b,c), funcinp13(a,b,c,d),funcinp263(a,b,d,e),funcmid13(a,b,e,f).
mid14(a, b, f):-inp14(a,b,c), funcinp14(a,b,c,d),funcinp264(a,b,d,e),funcmid14(a,b,e,f).
mid15(a, b, f):-inp15(a,b,c), funcinp15(a,b,c,d),funcinp265(a,b,d,e),funcmid15(a,b,e,f).
mid16(a, b, f):-inp16(a,b,c), funcinp16(a,b,c,d),funcinp266(a,b,d,e),funcmid16(a,b,e,f).
mid17(a, b, f):-inp17(a,b,c), funcinp17(a,b,c,d),funcinp267(a,b,d,e),funcmid17(a,b,e,f).
mid18(a, b, f):-inp18(a,b,c), funcinp18(a,b,c,d),funcinp268(a,b,d,e),funcmid18(a,b,e,f).
mid19(a, b, f):-inp19(a,b,c), funcinp19(a,b,c,d),funcinp269(a,b,d,e),funcmid19(a,b,e,f).
mid20(a, b, f):-inp20(a,b,c), funcinp20(a,b,c,d),funcinp270(a,b,d,e),funcmid20(a,b,e,f).
mid21(a, b, f):-inp21(a,b,c), funcinp21(a,b,c,d),funcinp271(a,b,d,e),funcmid21(a,b,e,f).
mid22(a, b, f):-inp22(a,b,c), funcinp22(a,b,c,d),funcinp272(a,b,d,e),funcmid22(a,b,e,f).
mid23(a, b, f):-inp23(a,b,c), funcinp23(a,b,c,d),funcinp273(a,b,d,e),funcmid23(a,b,e,f).
mid24(a, b, f):-inp24(a,b,c), funcinp24(a,b,c,d),funcinp274(a,b,d,e),funcmid24(a,b,e,f).
mid25(a, b, f):-inp25(a,b,c), funcinp25(a,b,c,d),funcinp275(a,b,d,e),funcmid25(a,b,e,f).
mid26(a, b, f):-inp26(a,b,c), funcinp26(a,b,c,d),funcinp276(a,b,d,e),funcmid26(a,b,e,f).
mid27(a, b, f):-inp27(a,b,c), funcinp27(a,b,c,d),funcinp277(a,b,d,e),funcmid27(a,b,e,f).
mid28(a, b, f):-inp28(a,b,c), funcinp28(a,b,c,d),funcinp278(a,b,d,e),funcmid28(a,b,e,f).
mid29(a, b, f):-inp29(a,b,c), funcinp29(a,b,c,d),funcinp279(a,b,d,e),funcmid29(a,b,e,f).
mid30(a, b, f):-inp30(a,b,c), funcinp30(a,b,c,d),funcinp280(a,b,d,e),funcmid30(a,b,e,f).
mid31(a, b, f):-inp31(a,b,c), funcinp31(a,b,c,d),funcinp281(a,b,d,e),funcmid31(a,b,e,f).
mid32(a, b, f):-inp32(a,b,c), funcinp32(a,b,c,d),funcinp282(a,b,d,e),funcmid32(a,b,e,f).
mid33(a, b, f):-inp33(a,b,c), funcinp33(a,b,c,d),funcinp283(a,b,d,e),funcmid33(a,b,e,f).
mid34(a, b, f):-inp34(a,b,c), funcinp34(a,b,c,d),funcinp284(a,b,d,e),funcmid34(a,b,e,f).
mid35(a, b, f):-inp35(a,b,c), funcinp35(a,b,c,d),funcinp285(a,b,d,e),funcmid35(a,b,e,f).
mid36(a, b, f):-inp36(a,b,c), funcinp36(a,b,c,d),funcinp286(a,b,d,e),funcmid36(a,b,e,f).
mid37(a, b, f):-inp37(a,b,c), funcinp37(a,b,c,d),funcinp287(a,b,d,e),funcmid37(a,b,e,f).
mid38(a, b, f):-inp38(a,b,c), funcinp38(a,b,c,d),funcinp288(a,b,d,e),funcmid38(a,b,e,f).
mid39(a, b, f):-inp39(a,b,c), funcinp39(a,b,c,d),funcinp289(a,b,d,e),funcmid39(a,b,e,f).
mid40(a, b, f):-inp40(a,b,c), funcinp40(a,b,c,d),funcinp290(a,b,d,e),funcmid40(a,b,e,f).
mid41(a, b, f):-inp41(a,b,c), funcinp41(a,b,c,d),funcinp291(a,b,d,e),funcmid41(a,b,e,f).
mid42(a, b, f):-inp42(a,b,c), funcinp42(a,b,c,d),funcinp292(a,b,d,e),funcmid42(a,b,e,f).
mid43(a, b, f):-inp43(a,b,c), funcinp43(a,b,c,d),funcinp293(a,b,d,e),funcmid43(a,b,e,f).
mid44(a, b, f):-inp44(a,b,c), funcinp44(a,b,c,d),funcinp294(a,b,d,e),funcmid44(a,b,e,f).
mid45(a, b, f):-inp45(a,b,c), funcinp45(a,b,c,d),funcinp295(a,b,d,e),funcmid45(a,b,e,f).
mid46(a, b, f):-inp46(a,b,c), funcinp46(a,b,c,d),funcinp296(a,b,d,e),funcmid46(a,b,e,f).
mid47(a, b, f):-inp47(a,b,c), funcinp47(a,b,c,d),funcinp297(a,b,d,e),funcmid47(a,b,e,f).
mid48(a, b, f):-inp48(a,b,c), funcinp48(a,b,c,d),funcinp298(a,b,d,e),funcmid48(a,b,e,f).
mid49(a, b, f):-inp49(a,b,c), funcinp49(a,b,c,d),funcinp299(a,b,d,e),funcmid49(a,b,e,f).
mid50(a, b, f):-inp50(a,b,c), funcinp50(a,b,c,d),funcinp300(a,b,d,e),funcmid50(a,b,e,f).
mid51(a, b, f):-inp51(a,b,c), funcinp51(a,b,c,d),funcinp301(a,b,d,e),funcmid51(a,b,e,f).
mid52(a, b, f):-inp52(a,b,c), funcinp52(a,b,c,d),funcinp302(a,b,d,e),funcmid52(a,b,e,f).
mid53(a, b, f):-inp53(a,b,c), funcinp53(a,b,c,d),funcinp303(a,b,d,e),funcmid53(a,b,e,f).
mid54(a, b, f):-inp54(a,b,c), funcinp54(a,b,c,d),funcinp304(a,b,d,e),funcmid54(a,b,e,f).
mid55(a, b, f):-inp55(a,b,c), funcinp55(a,b,c,d),funcinp305(a,b,d,e),funcmid55(a,b,e,f).
mid56(a, b, f):-inp56(a,b,c), funcinp56(a,b,c,d),funcinp306(a,b,d,e),funcmid56(a,b,e,f).
mid57(a, b, f):-inp57(a,b,c), funcinp57(a,b,c,d),funcinp307(a,b,d,e),funcmid57(a,b,e,f).
mid58(a, b, f):-inp58(a,b,c), funcinp58(a,b,c,d),funcinp308(a,b,d,e),funcmid58(a,b,e,f).
mid59(a, b, f):-inp59(a,b,c), funcinp59(a,b,c,d),funcinp309(a,b,d,e),funcmid59(a,b,e,f).
mid60(a, b, f):-inp60(a,b,c), funcinp60(a,b,c,d),funcinp310(a,b,d,e),funcmid60(a,b,e,f).
mid61(a, b, f):-inp61(a,b,c), funcinp61(a,b,c,d),funcinp311(a,b,d,e),funcmid61(a,b,e,f).
mid62(a, b, f):-inp62(a,b,c), funcinp62(a,b,c,d),funcinp312(a,b,d,e),funcmid62(a,b,e,f).
mid63(a, b, f):-inp63(a,b,c), funcinp63(a,b,c,d),funcinp313(a,b,d,e),funcmid63(a,b,e,f).
mid64(a, b, f):-inp64(a,b,c), funcinp64(a,b,c,d),funcinp314(a,b,d,e),funcmid64(a,b,e,f).
mid65(a, b, f):-inp65(a,b,c), funcinp65(a,b,c,d),funcinp315(a,b,d,e),funcmid65(a,b,e,f).
mid66(a, b, f):-inp66(a,b,c), funcinp66(a,b,c,d),funcinp316(a,b,d,e),funcmid66(a,b,e,f).
mid67(a, b, f):-inp67(a,b,c), funcinp67(a,b,c,d),funcinp317(a,b,d,e),funcmid67(a,b,e,f).
mid68(a, b, f):-inp68(a,b,c), funcinp68(a,b,c,d),funcinp318(a,b,d,e),funcmid68(a,b,e,f).
mid69(a, b, f):-inp69(a,b,c), funcinp69(a,b,c,d),funcinp319(a,b,d,e),funcmid69(a,b,e,f).
mid70(a, b, f):-inp70(a,b,c), funcinp70(a,b,c,d),funcinp320(a,b,d,e),funcmid70(a,b,e,f).
mid71(a, b, f):-inp71(a,b,c), funcinp71(a,b,c,d),funcinp321(a,b,d,e),funcmid71(a,b,e,f).
mid72(a, b, f):-inp72(a,b,c), funcinp72(a,b,c,d),funcinp322(a,b,d,e),funcmid72(a,b,e,f).
mid73(a, b, f):-inp73(a,b,c), funcinp73(a,b,c,d),funcinp323(a,b,d,e),funcmid73(a,b,e,f).
mid74(a, b, f):-inp74(a,b,c), funcinp74(a,b,c,d),funcinp324(a,b,d,e),funcmid74(a,b,e,f).
mid75(a, b, f):-inp75(a,b,c), funcinp75(a,b,c,d),funcinp325(a,b,d,e),funcmid75(a,b,e,f).
mid76(a, b, f):-inp76(a,b,c), funcinp76(a,b,c,d),funcinp326(a,b,d,e),funcmid76(a,b,e,f).
mid77(a, b, f):-inp77(a,b,c), funcinp77(a,b,c,d),funcinp327(a,b,d,e),funcmid77(a,b,e,f).
mid78(a, b, f):-inp78(a,b,c), funcinp78(a,b,c,d),funcinp328(a,b,d,e),funcmid78(a,b,e,f).
mid79(a, b, f):-inp79(a,b,c), funcinp79(a,b,c,d),funcinp329(a,b,d,e),funcmid79(a,b,e,f).
mid80(a, b, f):-inp80(a,b,c), funcinp80(a,b,c,d),funcinp330(a,b,d,e),funcmid80(a,b,e,f).
mid81(a, b, f):-inp81(a,b,c), funcinp81(a,b,c,d),funcinp331(a,b,d,e),funcmid81(a,b,e,f).
mid82(a, b, f):-inp82(a,b,c), funcinp82(a,b,c,d),funcinp332(a,b,d,e),funcmid82(a,b,e,f).
mid83(a, b, f):-inp83(a,b,c), funcinp83(a,b,c,d),funcinp333(a,b,d,e),funcmid83(a,b,e,f).
mid84(a, b, f):-inp84(a,b,c), funcinp84(a,b,c,d),funcinp334(a,b,d,e),funcmid84(a,b,e,f).
mid85(a, b, f):-inp85(a,b,c), funcinp85(a,b,c,d),funcinp335(a,b,d,e),funcmid85(a,b,e,f).
mid86(a, b, f):-inp86(a,b,c), funcinp86(a,b,c,d),funcinp336(a,b,d,e),funcmid86(a,b,e,f).
mid87(a, b, f):-inp87(a,b,c), funcinp87(a,b,c,d),funcinp337(a,b,d,e),funcmid87(a,b,e,f).
mid88(a, b, f):-inp88(a,b,c), funcinp88(a,b,c,d),funcinp338(a,b,d,e),funcmid88(a,b,e,f).
mid89(a, b, f):-inp89(a,b,c), funcinp89(a,b,c,d),funcinp339(a,b,d,e),funcmid89(a,b,e,f).
mid90(a, b, f):-inp90(a,b,c), funcinp90(a,b,c,d),funcinp340(a,b,d,e),funcmid90(a,b,e,f).
mid91(a, b, f):-inp91(a,b,c), funcinp91(a,b,c,d),funcinp341(a,b,d,e),funcmid91(a,b,e,f).
mid92(a, b, f):-inp92(a,b,c), funcinp92(a,b,c,d),funcinp342(a,b,d,e),funcmid92(a,b,e,f).
mid93(a, b, f):-inp93(a,b,c), funcinp93(a,b,c,d),funcinp343(a,b,d,e),funcmid93(a,b,e,f).
mid94(a, b, f):-inp94(a,b,c), funcinp94(a,b,c,d),funcinp344(a,b,d,e),funcmid94(a,b,e,f).
mid95(a, b, f):-inp95(a,b,c), funcinp95(a,b,c,d),funcinp345(a,b,d,e),funcmid95(a,b,e,f).
mid96(a, b, f):-inp96(a,b,c), funcinp96(a,b,c,d),funcinp346(a,b,d,e),funcmid96(a,b,e,f).
mid97(a, b, f):-inp97(a,b,c), funcinp97(a,b,c,d),funcinp347(a,b,d,e),funcmid97(a,b,e,f).
mid98(a, b, f):-inp98(a,b,c), funcinp98(a,b,c,d),funcinp348(a,b,d,e),funcmid98(a,b,e,f).
mid99(a, b, f):-inp99(a,b,c), funcinp99(a,b,c,d),funcinp349(a,b,d,e),funcmid99(a,b,e,f).
mid100(a, b, f):-inp100(a,b,c), funcinp100(a,b,c,d),funcinp350(a,b,d,e),funcmid100(a,b,e,f).
mid101(a, b, f):-inp101(a,b,c), funcinp101(a,b,c,d),funcinp351(a,b,d,e),funcmid101(a,b,e,f).
mid102(a, b, f):-inp102(a,b,c), funcinp102(a,b,c,d),funcinp352(a,b,d,e),funcmid102(a,b,e,f).
mid103(a, b, f):-inp103(a,b,c), funcinp103(a,b,c,d),funcinp353(a,b,d,e),funcmid103(a,b,e,f).
mid104(a, b, f):-inp104(a,b,c), funcinp104(a,b,c,d),funcinp354(a,b,d,e),funcmid104(a,b,e,f).
mid105(a, b, f):-inp105(a,b,c), funcinp105(a,b,c,d),funcinp355(a,b,d,e),funcmid105(a,b,e,f).
mid106(a, b, f):-inp106(a,b,c), funcinp106(a,b,c,d),funcinp356(a,b,d,e),funcmid106(a,b,e,f).
mid107(a, b, f):-inp107(a,b,c), funcinp107(a,b,c,d),funcinp357(a,b,d,e),funcmid107(a,b,e,f).
mid108(a, b, f):-inp108(a,b,c), funcinp108(a,b,c,d),funcinp358(a,b,d,e),funcmid108(a,b,e,f).
mid109(a, b, f):-inp109(a,b,c), funcinp109(a,b,c,d),funcinp359(a,b,d,e),funcmid109(a,b,e,f).
mid110(a, b, f):-inp110(a,b,c), funcinp110(a,b,c,d),funcinp360(a,b,d,e),funcmid110(a,b,e,f).
mid111(a, b, f):-inp111(a,b,c), funcinp111(a,b,c,d),funcinp361(a,b,d,e),funcmid111(a,b,e,f).
mid112(a, b, f):-inp112(a,b,c), funcinp112(a,b,c,d),funcinp362(a,b,d,e),funcmid112(a,b,e,f).
mid113(a, b, f):-inp113(a,b,c), funcinp113(a,b,c,d),funcinp363(a,b,d,e),funcmid113(a,b,e,f).
mid114(a, b, f):-inp114(a,b,c), funcinp114(a,b,c,d),funcinp364(a,b,d,e),funcmid114(a,b,e,f).
mid115(a, b, f):-inp115(a,b,c), funcinp115(a,b,c,d),funcinp365(a,b,d,e),funcmid115(a,b,e,f).
mid116(a, b, f):-inp116(a,b,c), funcinp116(a,b,c,d),funcinp366(a,b,d,e),funcmid116(a,b,e,f).
mid117(a, b, f):-inp117(a,b,c), funcinp117(a,b,c,d),funcinp367(a,b,d,e),funcmid117(a,b,e,f).
mid118(a, b, f):-inp118(a,b,c), funcinp118(a,b,c,d),funcinp368(a,b,d,e),funcmid118(a,b,e,f).
mid119(a, b, f):-inp119(a,b,c), funcinp119(a,b,c,d),funcinp369(a,b,d,e),funcmid119(a,b,e,f).
mid120(a, b, f):-inp120(a,b,c), funcinp120(a,b,c,d),funcinp370(a,b,d,e),funcmid120(a,b,e,f).
mid121(a, b, f):-inp121(a,b,c), funcinp121(a,b,c,d),funcinp371(a,b,d,e),funcmid121(a,b,e,f).
mid122(a, b, f):-inp122(a,b,c), funcinp122(a,b,c,d),funcinp372(a,b,d,e),funcmid122(a,b,e,f).
mid123(a, b, f):-inp123(a,b,c), funcinp123(a,b,c,d),funcinp373(a,b,d,e),funcmid123(a,b,e,f).
mid124(a, b, f):-inp124(a,b,c), funcinp124(a,b,c,d),funcinp374(a,b,d,e),funcmid124(a,b,e,f).
mid125(a, b, f):-inp125(a,b,c), funcinp125(a,b,c,d),funcinp375(a,b,d,e),funcmid125(a,b,e,f).
mid126(a, b, f):-inp126(a,b,c), funcinp126(a,b,c,d),funcinp376(a,b,d,e),funcmid126(a,b,e,f).
mid127(a, b, f):-inp127(a,b,c), funcinp127(a,b,c,d),funcinp377(a,b,d,e),funcmid127(a,b,e,f).
mid128(a, b, f):-inp128(a,b,c), funcinp128(a,b,c,d),funcinp378(a,b,d,e),funcmid128(a,b,e,f).
mid129(a, b, f):-inp129(a,b,c), funcinp129(a,b,c,d),funcinp379(a,b,d,e),funcmid129(a,b,e,f).
mid130(a, b, f):-inp130(a,b,c), funcinp130(a,b,c,d),funcinp380(a,b,d,e),funcmid130(a,b,e,f).
mid131(a, b, f):-inp131(a,b,c), funcinp131(a,b,c,d),funcinp381(a,b,d,e),funcmid131(a,b,e,f).
mid132(a, b, f):-inp132(a,b,c), funcinp132(a,b,c,d),funcinp382(a,b,d,e),funcmid132(a,b,e,f).
mid133(a, b, f):-inp133(a,b,c), funcinp133(a,b,c,d),funcinp383(a,b,d,e),funcmid133(a,b,e,f).
mid134(a, b, f):-inp134(a,b,c), funcinp134(a,b,c,d),funcinp384(a,b,d,e),funcmid134(a,b,e,f).
mid135(a, b, f):-inp135(a,b,c), funcinp135(a,b,c,d),funcinp385(a,b,d,e),funcmid135(a,b,e,f).
mid136(a, b, f):-inp136(a,b,c), funcinp136(a,b,c,d),funcinp386(a,b,d,e),funcmid136(a,b,e,f).
mid137(a, b, f):-inp137(a,b,c), funcinp137(a,b,c,d),funcinp387(a,b,d,e),funcmid137(a,b,e,f).
mid138(a, b, f):-inp138(a,b,c), funcinp138(a,b,c,d),funcinp388(a,b,d,e),funcmid138(a,b,e,f).
mid139(a, b, f):-inp139(a,b,c), funcinp139(a,b,c,d),funcinp389(a,b,d,e),funcmid139(a,b,e,f).
mid140(a, b, f):-inp140(a,b,c), funcinp140(a,b,c,d),funcinp390(a,b,d,e),funcmid140(a,b,e,f).
mid141(a, b, f):-inp141(a,b,c), funcinp141(a,b,c,d),funcinp391(a,b,d,e),funcmid141(a,b,e,f).
mid142(a, b, f):-inp142(a,b,c), funcinp142(a,b,c,d),funcinp392(a,b,d,e),funcmid142(a,b,e,f).
mid143(a, b, f):-inp143(a,b,c), funcinp143(a,b,c,d),funcinp393(a,b,d,e),funcmid143(a,b,e,f).
mid144(a, b, f):-inp144(a,b,c), funcinp144(a,b,c,d),funcinp394(a,b,d,e),funcmid144(a,b,e,f).
mid145(a, b, f):-inp145(a,b,c), funcinp145(a,b,c,d),funcinp395(a,b,d,e),funcmid145(a,b,e,f).
mid146(a, b, f):-inp146(a,b,c), funcinp146(a,b,c,d),funcinp396(a,b,d,e),funcmid146(a,b,e,f).
mid147(a, b, f):-inp147(a,b,c), funcinp147(a,b,c,d),funcinp397(a,b,d,e),funcmid147(a,b,e,f).
mid148(a, b, f):-inp148(a,b,c), funcinp148(a,b,c,d),funcinp398(a,b,d,e),funcmid148(a,b,e,f).
mid149(a, b, f):-inp149(a,b,c), funcinp149(a,b,c,d),funcinp399(a,b,d,e),funcmid149(a,b,e,f).
mid150(a, b, f):-inp150(a,b,c), funcinp150(a,b,c,d),funcinp400(a,b,d,e),funcmid150(a,b,e,f).
mid151(a, b, f):-inp151(a,b,c), funcinp151(a,b,c,d),funcinp401(a,b,d,e),funcmid151(a,b,e,f).
mid152(a, b, f):-inp152(a,b,c), funcinp152(a,b,c,d),funcinp402(a,b,d,e),funcmid152(a,b,e,f).
mid153(a, b, f):-inp153(a,b,c), funcinp153(a,b,c,d),funcinp403(a,b,d,e),funcmid153(a,b,e,f).
mid154(a, b, f):-inp154(a,b,c), funcinp154(a,b,c,d),funcinp404(a,b,d,e),funcmid154(a,b,e,f).
mid155(a, b, f):-inp155(a,b,c), funcinp155(a,b,c,d),funcinp405(a,b,d,e),funcmid155(a,b,e,f).
mid156(a, b, f):-inp156(a,b,c), funcinp156(a,b,c,d),funcinp406(a,b,d,e),funcmid156(a,b,e,f).
mid157(a, b, f):-inp157(a,b,c), funcinp157(a,b,c,d),funcinp407(a,b,d,e),funcmid157(a,b,e,f).
mid158(a, b, f):-inp158(a,b,c), funcinp158(a,b,c,d),funcinp408(a,b,d,e),funcmid158(a,b,e,f).
mid159(a, b, f):-inp159(a,b,c), funcinp159(a,b,c,d),funcinp409(a,b,d,e),funcmid159(a,b,e,f).
mid160(a, b, f):-inp160(a,b,c), funcinp160(a,b,c,d),funcinp410(a,b,d,e),funcmid160(a,b,e,f).
mid161(a, b, f):-inp161(a,b,c), funcinp161(a,b,c,d),funcinp411(a,b,d,e),funcmid161(a,b,e,f).
mid162(a, b, f):-inp162(a,b,c), funcinp162(a,b,c,d),funcinp412(a,b,d,e),funcmid162(a,b,e,f).
mid163(a, b, f):-inp163(a,b,c), funcinp163(a,b,c,d),funcinp413(a,b,d,e),funcmid163(a,b,e,f).
mid164(a, b, f):-inp164(a,b,c), funcinp164(a,b,c,d),funcinp414(a,b,d,e),funcmid164(a,b,e,f).
mid165(a, b, f):-inp165(a,b,c), funcinp165(a,b,c,d),funcinp415(a,b,d,e),funcmid165(a,b,e,f).
mid166(a, b, f):-inp166(a,b,c), funcinp166(a,b,c,d),funcinp416(a,b,d,e),funcmid166(a,b,e,f).
mid167(a, b, f):-inp167(a,b,c), funcinp167(a,b,c,d),funcinp417(a,b,d,e),funcmid167(a,b,e,f).
mid168(a, b, f):-inp168(a,b,c), funcinp168(a,b,c,d),funcinp418(a,b,d,e),funcmid168(a,b,e,f).
mid169(a, b, f):-inp169(a,b,c), funcinp169(a,b,c,d),funcinp419(a,b,d,e),funcmid169(a,b,e,f).
mid170(a, b, f):-inp170(a,b,c), funcinp170(a,b,c,d),funcinp420(a,b,d,e),funcmid170(a,b,e,f).
mid171(a, b, f):-inp171(a,b,c), funcinp171(a,b,c,d),funcinp421(a,b,d,e),funcmid171(a,b,e,f).
mid172(a, b, f):-inp172(a,b,c), funcinp172(a,b,c,d),funcinp422(a,b,d,e),funcmid172(a,b,e,f).
mid173(a, b, f):-inp173(a,b,c), funcinp173(a,b,c,d),funcinp423(a,b,d,e),funcmid173(a,b,e,f).
mid174(a, b, f):-inp174(a,b,c), funcinp174(a,b,c,d),funcinp424(a,b,d,e),funcmid174(a,b,e,f).
mid175(a, b, f):-inp175(a,b,c), funcinp175(a,b,c,d),funcinp425(a,b,d,e),funcmid175(a,b,e,f).
mid176(a, b, f):-inp176(a,b,c), funcinp176(a,b,c,d),funcinp426(a,b,d,e),funcmid176(a,b,e,f).
mid177(a, b, f):-inp177(a,b,c), funcinp177(a,b,c,d),funcinp427(a,b,d,e),funcmid177(a,b,e,f).
mid178(a, b, f):-inp178(a,b,c), funcinp178(a,b,c,d),funcinp428(a,b,d,e),funcmid178(a,b,e,f).
mid179(a, b, f):-inp179(a,b,c), funcinp179(a,b,c,d),funcinp429(a,b,d,e),funcmid179(a,b,e,f).
mid180(a, b, f):-inp180(a,b,c), funcinp180(a,b,c,d),funcinp430(a,b,d,e),funcmid180(a,b,e,f).
mid181(a, b, f):-inp181(a,b,c), funcinp181(a,b,c,d),funcinp431(a,b,d,e),funcmid181(a,b,e,f).
mid182(a, b, f):-inp182(a,b,c), funcinp182(a,b,c,d),funcinp432(a,b,d,e),funcmid182(a,b,e,f).
mid183(a, b, f):-inp183(a,b,c), funcinp183(a,b,c,d),funcinp433(a,b,d,e),funcmid183(a,b,e,f).
mid184(a, b, f):-inp184(a,b,c), funcinp184(a,b,c,d),funcinp434(a,b,d,e),funcmid184(a,b,e,f).
mid185(a, b, f):-inp185(a,b,c), funcinp185(a,b,c,d),funcinp435(a,b,d,e),funcmid185(a,b,e,f).
mid186(a, b, f):-inp186(a,b,c), funcinp186(a,b,c,d),funcinp436(a,b,d,e),funcmid186(a,b,e,f).
mid187(a, b, f):-inp187(a,b,c), funcinp187(a,b,c,d),funcinp437(a,b,d,e),funcmid187(a,b,e,f).
mid188(a, b, f):-inp188(a,b,c), funcinp188(a,b,c,d),funcinp438(a,b,d,e),funcmid188(a,b,e,f).
mid189(a, b, f):-inp189(a,b,c), funcinp189(a,b,c,d),funcinp439(a,b,d,e),funcmid189(a,b,e,f).
mid190(a, b, f):-inp190(a,b,c), funcinp190(a,b,c,d),funcinp440(a,b,d,e),funcmid190(a,b,e,f).
mid191(a, b, f):-inp191(a,b,c), funcinp191(a,b,c,d),funcinp441(a,b,d,e),funcmid191(a,b,e,f).
mid192(a, b, f):-inp192(a,b,c), funcinp192(a,b,c,d),funcinp442(a,b,d,e),funcmid192(a,b,e,f).
mid193(a, b, f):-inp193(a,b,c), funcinp193(a,b,c,d),funcinp443(a,b,d,e),funcmid193(a,b,e,f).
mid194(a, b, f):-inp194(a,b,c), funcinp194(a,b,c,d),funcinp444(a,b,d,e),funcmid194(a,b,e,f).
mid195(a, b, f):-inp195(a,b,c), funcinp195(a,b,c,d),funcinp445(a,b,d,e),funcmid195(a,b,e,f).
mid196(a, b, f):-inp196(a,b,c), funcinp196(a,b,c,d),funcinp446(a,b,d,e),funcmid196(a,b,e,f).
mid197(a, b, f):-inp197(a,b,c), funcinp197(a,b,c,d),funcinp447(a,b,d,e),funcmid197(a,b,e,f).
mid198(a, b, f):-inp198(a,b,c), funcinp198(a,b,c,d),funcinp448(a,b,d,e),funcmid198(a,b,e,f).
mid199(a, b, f):-inp199(a,b,c), funcinp199(a,b,c,d),funcinp449(a,b,d,e),funcmid199(a,b,e,f).
mid200(a, b, f):-inp200(a,b,c), funcinp200(a,b,c,d),funcinp450(a,b,d,e),funcmid200(a,b,e,f).
mid201(a, b, f):-inp201(a,b,c), funcinp201(a,b,c,d),funcinp451(a,b,d,e),funcmid201(a,b,e,f).
mid202(a, b, f):-inp202(a,b,c), funcinp202(a,b,c,d),funcinp452(a,b,d,e),funcmid202(a,b,e,f).
mid203(a, b, f):-inp203(a,b,c), funcinp203(a,b,c,d),funcinp453(a,b,d,e),funcmid203(a,b,e,f).
mid204(a, b, f):-inp204(a,b,c), funcinp204(a,b,c,d),funcinp454(a,b,d,e),funcmid204(a,b,e,f).
mid205(a, b, f):-inp205(a,b,c), funcinp205(a,b,c,d),funcinp455(a,b,d,e),funcmid205(a,b,e,f).
mid206(a, b, f):-inp206(a,b,c), funcinp206(a,b,c,d),funcinp456(a,b,d,e),funcmid206(a,b,e,f).
mid207(a, b, f):-inp207(a,b,c), funcinp207(a,b,c,d),funcinp457(a,b,d,e),funcmid207(a,b,e,f).
mid208(a, b, f):-inp208(a,b,c), funcinp208(a,b,c,d),funcinp458(a,b,d,e),funcmid208(a,b,e,f).
mid209(a, b, f):-inp209(a,b,c), funcinp209(a,b,c,d),funcinp459(a,b,d,e),funcmid209(a,b,e,f).
mid210(a, b, f):-inp210(a,b,c), funcinp210(a,b,c,d),funcinp460(a,b,d,e),funcmid210(a,b,e,f).
mid211(a, b, f):-inp211(a,b,c), funcinp211(a,b,c,d),funcinp461(a,b,d,e),funcmid211(a,b,e,f).
mid212(a, b, f):-inp212(a,b,c), funcinp212(a,b,c,d),funcinp462(a,b,d,e),funcmid212(a,b,e,f).
mid213(a, b, f):-inp213(a,b,c), funcinp213(a,b,c,d),funcinp463(a,b,d,e),funcmid213(a,b,e,f).
mid214(a, b, f):-inp214(a,b,c), funcinp214(a,b,c,d),funcinp464(a,b,d,e),funcmid214(a,b,e,f).
mid215(a, b, f):-inp215(a,b,c), funcinp215(a,b,c,d),funcinp465(a,b,d,e),funcmid215(a,b,e,f).
mid216(a, b, f):-inp216(a,b,c), funcinp216(a,b,c,d),funcinp466(a,b,d,e),funcmid216(a,b,e,f).
mid217(a, b, f):-inp217(a,b,c), funcinp217(a,b,c,d),funcinp467(a,b,d,e),funcmid217(a,b,e,f).
mid218(a, b, f):-inp218(a,b,c), funcinp218(a,b,c,d),funcinp468(a,b,d,e),funcmid218(a,b,e,f).
mid219(a, b, f):-inp219(a,b,c), funcinp219(a,b,c,d),funcinp469(a,b,d,e),funcmid219(a,b,e,f).
mid220(a, b, f):-inp220(a,b,c), funcinp220(a,b,c,d),funcinp470(a,b,d,e),funcmid220(a,b,e,f).
mid221(a, b, f):-inp221(a,b,c), funcinp221(a,b,c,d),funcinp471(a,b,d,e),funcmid221(a,b,e,f).
mid222(a, b, f):-inp222(a,b,c), funcinp222(a,b,c,d),funcinp472(a,b,d,e),funcmid222(a,b,e,f).
mid223(a, b, f):-inp223(a,b,c), funcinp223(a,b,c,d),funcinp473(a,b,d,e),funcmid223(a,b,e,f).
mid224(a, b, f):-inp224(a,b,c), funcinp224(a,b,c,d),funcinp474(a,b,d,e),funcmid224(a,b,e,f).
mid225(a, b, f):-inp225(a,b,c), funcinp225(a,b,c,d),funcinp475(a,b,d,e),funcmid225(a,b,e,f).
mid226(a, b, f):-inp226(a,b,c), funcinp226(a,b,c,d),funcinp476(a,b,d,e),funcmid226(a,b,e,f).
mid227(a, b, f):-inp227(a,b,c), funcinp227(a,b,c,d),funcinp477(a,b,d,e),funcmid227(a,b,e,f).
mid228(a, b, f):-inp228(a,b,c), funcinp228(a,b,c,d),funcinp478(a,b,d,e),funcmid228(a,b,e,f).
mid229(a, b, f):-inp229(a,b,c), funcinp229(a,b,c,d),funcinp479(a,b,d,e),funcmid229(a,b,e,f).
mid230(a, b, f):-inp230(a,b,c), funcinp230(a,b,c,d),funcinp480(a,b,d,e),funcmid230(a,b,e,f).
mid231(a, b, f):-inp231(a,b,c), funcinp231(a,b,c,d),funcinp481(a,b,d,e),funcmid231(a,b,e,f).
mid232(a, b, f):-inp232(a,b,c), funcinp232(a,b,c,d),funcinp482(a,b,d,e),funcmid232(a,b,e,f).
mid233(a, b, f):-inp233(a,b,c), funcinp233(a,b,c,d),funcinp483(a,b,d,e),funcmid233(a,b,e,f).
mid234(a, b, f):-inp234(a,b,c), funcinp234(a,b,c,d),funcinp484(a,b,d,e),funcmid234(a,b,e,f).
mid235(a, b, f):-inp235(a,b,c), funcinp235(a,b,c,d),funcinp485(a,b,d,e),funcmid235(a,b,e,f).
mid236(a, b, f):-inp236(a,b,c), funcinp236(a,b,c,d),funcinp486(a,b,d,e),funcmid236(a,b,e,f).
mid237(a, b, f):-inp237(a,b,c), funcinp237(a,b,c,d),funcinp487(a,b,d,e),funcmid237(a,b,e,f).
mid238(a, b, f):-inp238(a,b,c), funcinp238(a,b,c,d),funcinp488(a,b,d,e),funcmid238(a,b,e,f).
mid239(a, b, f):-inp239(a,b,c), funcinp239(a,b,c,d),funcinp489(a,b,d,e),funcmid239(a,b,e,f).
mid240(a, b, f):-inp240(a,b,c), funcinp240(a,b,c,d),funcinp490(a,b,d,e),funcmid240(a,b,e,f).
mid241(a, b, f):-inp241(a,b,c), funcinp241(a,b,c,d),funcinp491(a,b,d,e),funcmid241(a,b,e,f).
mid242(a, b, f):-inp242(a,b,c), funcinp242(a,b,c,d),funcinp492(a,b,d,e),funcmid242(a,b,e,f).
mid243(a, b, f):-inp243(a,b,c), funcinp243(a,b,c,d),funcinp493(a,b,d,e),funcmid243(a,b,e,f).
mid244(a, b, f):-inp244(a,b,c), funcinp244(a,b,c,d),funcinp494(a,b,d,e),funcmid244(a,b,e,f).
mid245(a, b, f):-inp245(a,b,c), funcinp245(a,b,c,d),funcinp495(a,b,d,e),funcmid245(a,b,e,f).
mid246(a, b, f):-inp246(a,b,c), funcinp246(a,b,c,d),funcinp496(a,b,d,e),funcmid246(a,b,e,f).
mid247(a, b, f):-inp247(a,b,c), funcinp247(a,b,c,d),funcinp497(a,b,d,e),funcmid247(a,b,e,f).
mid248(a, b, f):-inp248(a,b,c), funcinp248(a,b,c,d),funcinp498(a,b,d,e),funcmid248(a,b,e,f).
mid249(a, b, f):-inp249(a,b,c), funcinp249(a,b,c,d),funcinp499(a,b,d,e),funcmid249(a,b,e,f).
mid250(a, b, f):-inp250(a,b,c), funcinp250(a,b,c,d),funcinp500(a,b,d,e),funcmid250(a,b,e,f).



null(0):-inp1(a,b,c).
null(0):-inp2(a,b,c).
null(0):-inp3(a,b,c).
null(0):-inp4(a,b,c).
null(0):-inp5(a,b,c).
null(0):-inp6(a,b,c).
null(0):-inp7(a,b,c).
null(0):-inp8(a,b,c).
null(0):-inp9(a,b,c).
null(0):-inp10(a,b,c).
null(0):-inp11(a,b,c).
null(0):-inp12(a,b,c).
null(0):-inp13(a,b,c).
null(0):-inp14(a,b,c).
null(0):-inp15(a,b,c).
null(0):-inp16(a,b,c).
null(0):-inp17(a,b,c).
null(0):-inp18(a,b,c).
null(0):-inp19(a,b,c).
null(0):-inp20(a,b,c).
null(0):-inp21(a,b,c).
null(0):-inp22(a,b,c).
null(0):-inp23(a,b,c).
null(0):-inp24(a,b,c).
null(0):-inp25(a,b,c).
null(0):-inp26(a,b,c).
null(0):-inp27(a,b,c).
null(0):-inp28(a,b,c).
null(0):-inp29(a,b,c).
null(0):-inp30(a,b,c).
null(0):-inp31(a,b,c).
null(0):-inp32(a,b,c).
null(0):-inp33(a,b,c).
null(0):-inp34(a,b,c).
null(0):-inp35(a,b,c).
null(0):-inp36(a,b,c).
null(0):-inp37(a,b,c).
null(0):-inp38(a,b,c).
null(0):-inp39(a,b,c).
null(0):-inp40(a,b,c).
null(0):-inp41(a,b,c).
null(0):-inp42(a,b,c).
null(0):-inp43(a,b,c).
null(0):-inp44(a,b,c).
null(0):-inp45(a,b,c).
null(0):-inp46(a,b,c).
null(0):-inp47(a,b,c).
null(0):-inp48(a,b,c).
null(0):-inp49(a,b,c).
null(0):-inp50(a,b,c).
null(0):-inp51(a,b,c).
null(0):-inp52(a,b,c).
null(0):-inp53(a,b,c).
null(0):-inp54(a,b,c).
null(0):-inp55(a,b,c).
null(0):-inp56(a,b,c).
null(0):-inp57(a,b,c).
null(0):-inp58(a,b,c).
null(0):-inp59(a,b,c).
null(0):-inp60(a,b,c).
null(0):-inp61(a,b,c).
null(0):-inp62(a,b,c).
null(0):-inp63(a,b,c).
null(0):-inp64(a,b,c).
null(0):-inp65(a,b,c).
null(0):-inp66(a,b,c).
null(0):-inp67(a,b,c).
null(0):-inp68(a,b,c).
null(0):-inp69(a,b,c).
null(0):-inp70(a,b,c).
null(0):-inp71(a,b,c).
null(0):-inp72(a,b,c).
null(0):-inp73(a,b,c).
null(0):-inp74(a,b,c).
null(0):-inp75(a,b,c).
null(0):-inp76(a,b,c).
null(0):-inp77(a,b,c).
null(0):-inp78(a,b,c).
null(0):-inp79(a,b,c).
null(0):-inp80(a,b,c).
null(0):-inp81(a,b,c).
null(0):-inp82(a,b,c).
null(0):-inp83(a,b,c).
null(0):-inp84(a,b,c).
null(0):-inp85(a,b,c).
null(0):-inp86(a,b,c).
null(0):-inp87(a,b,c).
null(0):-inp88(a,b,c).
null(0):-inp89(a,b,c).
null(0):-inp90(a,b,c).
null(0):-inp91(a,b,c).
null(0):-inp92(a,b,c).
null(0):-inp93(a,b,c).
null(0):-inp94(a,b,c).
null(0):-inp95(a,b,c).
null(0):-inp96(a,b,c).
null(0):-inp97(a,b,c).
null(0):-inp98(a,b,c).
null(0):-inp99(a,b,c).
null(0):-inp100(a,b,c).
null(0):-inp101(a,b,c).
null(0):-inp102(a,b,c).
null(0):-inp103(a,b,c).
null(0):-inp104(a,b,c).
null(0):-inp105(a,b,c).
null(0):-inp106(a,b,c).
null(0):-inp107(a,b,c).
null(0):-inp108(a,b,c).
null(0):-inp109(a,b,c).
null(0):-inp110(a,b,c).
null(0):-inp111(a,b,c).
null(0):-inp112(a,b,c).
null(0):-inp113(a,b,c).
null(0):-inp114(a,b,c).
null(0):-inp115(a,b,c).
null(0):-inp116(a,b,c).
null(0):-inp117(a,b,c).
null(0):-inp118(a,b,c).
null(0):-inp119(a,b,c).
null(0):-inp120(a,b,c).
null(0):-inp121(a,b,c).
null(0):-inp122(a,b,c).
null(0):-inp123(a,b,c).
null(0):-inp124(a,b,c).
null(0):-inp125(a,b,c).
null(0):-inp126(a,b,c).
null(0):-inp127(a,b,c).
null(0):-inp128(a,b,c).
null(0):-inp129(a,b,c).
null(0):-inp130(a,b,c).
null(0):-inp131(a,b,c).
null(0):-inp132(a,b,c).
null(0):-inp133(a,b,c).
null(0):-inp134(a,b,c).
null(0):-inp135(a,b,c).
null(0):-inp136(a,b,c).
null(0):-inp137(a,b,c).
null(0):-inp138(a,b,c).
null(0):-inp139(a,b,c).
null(0):-inp140(a,b,c).
null(0):-inp141(a,b,c).
null(0):-inp142(a,b,c).
null(0):-inp143(a,b,c).
null(0):-inp144(a,b,c).
null(0):-inp145(a,b,c).
null(0):-inp146(a,b,c).
null(0):-inp147(a,b,c).
null(0):-inp148(a,b,c).
null(0):-inp149(a,b,c).
null(0):-inp150(a,b,c).
null(0):-inp151(a,b,c).
null(0):-inp152(a,b,c).
null(0):-inp153(a,b,c).
null(0):-inp154(a,b,c).
null(0):-inp155(a,b,c).
null(0):-inp156(a,b,c).
null(0):-inp157(a,b,c).
null(0):-inp158(a,b,c).
null(0):-inp159(a,b,c).
null(0):-inp160(a,b,c).
null(0):-inp161(a,b,c).
null(0):-inp162(a,b,c).
null(0):-inp163(a,b,c).
null(0):-inp164(a,b,c).
null(0):-inp165(a,b,c).
null(0):-inp166(a,b,c).
null(0):-inp167(a,b,c).
null(0):-inp168(a,b,c).
null(0):-inp169(a,b,c).
null(0):-inp170(a,b,c).
null(0):-inp171(a,b,c).
null(0):-inp172(a,b,c).
null(0):-inp173(a,b,c).
null(0):-inp174(a,b,c).
null(0):-inp175(a,b,c).
null(0):-inp176(a,b,c).
null(0):-inp177(a,b,c).
null(0):-inp178(a,b,c).
null(0):-inp179(a,b,c).
null(0):-inp180(a,b,c).
null(0):-inp181(a,b,c).
null(0):-inp182(a,b,c).
null(0):-inp183(a,b,c).
null(0):-inp184(a,b,c).
null(0):-inp185(a,b,c).
null(0):-inp186(a,b,c).
null(0):-inp187(a,b,c).
null(0):-inp188(a,b,c).
null(0):-inp189(a,b,c).
null(0):-inp190(a,b,c).
null(0):-inp191(a,b,c).
null(0):-inp192(a,b,c).
null(0):-inp193(a,b,c).
null(0):-inp194(a,b,c).
null(0):-inp195(a,b,c).
null(0):-inp196(a,b,c).
null(0):-inp197(a,b,c).
null(0):-inp198(a,b,c).
null(0):-inp199(a,b,c).
null(0):-inp200(a,b,c).
null(0):-inp201(a,b,c).
null(0):-inp202(a,b,c).
null(0):-inp203(a,b,c).
null(0):-inp204(a,b,c).
null(0):-inp205(a,b,c).
null(0):-inp206(a,b,c).
null(0):-inp207(a,b,c).
null(0):-inp208(a,b,c).
null(0):-inp209(a,b,c).
null(0):-inp210(a,b,c).
null(0):-inp211(a,b,c).
null(0):-inp212(a,b,c).
null(0):-inp213(a,b,c).
null(0):-inp214(a,b,c).
null(0):-inp215(a,b,c).
null(0):-inp216(a,b,c).
null(0):-inp217(a,b,c).
null(0):-inp218(a,b,c).
null(0):-inp219(a,b,c).
null(0):-inp220(a,b,c).
null(0):-inp221(a,b,c).
null(0):-inp222(a,b,c).
null(0):-inp223(a,b,c).
null(0):-inp224(a,b,c).
null(0):-inp225(a,b,c).
null(0):-inp226(a,b,c).
null(0):-inp227(a,b,c).
null(0):-inp228(a,b,c).
null(0):-inp229(a,b,c).
null(0):-inp230(a,b,c).
null(0):-inp231(a,b,c).
null(0):-inp232(a,b,c).
null(0):-inp233(a,b,c).
null(0):-inp234(a,b,c).
null(0):-inp235(a,b,c).
null(0):-inp236(a,b,c).
null(0):-inp237(a,b,c).
null(0):-inp238(a,b,c).
null(0):-inp239(a,b,c).
null(0):-inp240(a,b,c).
null(0):-inp241(a,b,c).
null(0):-inp242(a,b,c).
null(0):-inp243(a,b,c).
null(0):-inp244(a,b,c).
null(0):-inp245(a,b,c).
null(0):-inp246(a,b,c).
null(0):-inp247(a,b,c).
null(0):-inp248(a,b,c).
null(0):-inp249(a,b,c).
null(0):-inp250(a,b,c).
null(0):-inp251(a,b,c).
null(0):-inp252(a,b,c).
null(0):-inp253(a,b,c).
null(0):-inp254(a,b,c).
null(0):-inp255(a,b,c).
null(0):-inp256(a,b,c).
null(0):-inp257(a,b,c).
null(0):-inp258(a,b,c).
null(0):-inp259(a,b,c).
null(0):-inp260(a,b,c).
null(0):-inp261(a,b,c).
null(0):-inp262(a,b,c).
null(0):-inp263(a,b,c).
null(0):-inp264(a,b,c).
null(0):-inp265(a,b,c).
null(0):-inp266(a,b,c).
null(0):-inp267(a,b,c).
null(0):-inp268(a,b,c).
null(0):-inp269(a,b,c).
null(0):-inp270(a,b,c).
null(0):-inp271(a,b,c).
null(0):-inp272(a,b,c).
null(0):-inp273(a,b,c).
null(0):-inp274(a,b,c).
null(0):-inp275(a,b,c).
null(0):-inp276(a,b,c).
null(0):-inp277(a,b,c).
null(0):-inp278(a,b,c).
null(0):-inp279(a,b,c).
null(0):-inp280(a,b,c).
null(0):-inp281(a,b,c).
null(0):-inp282(a,b,c).
null(0):-inp283(a,b,c).
null(0):-inp284(a,b,c).
null(0):-inp285(a,b,c).
null(0):-inp286(a,b,c).
null(0):-inp287(a,b,c).
null(0):-inp288(a,b,c).
null(0):-inp289(a,b,c).
null(0):-inp290(a,b,c).
null(0):-inp291(a,b,c).
null(0):-inp292(a,b,c).
null(0):-inp293(a,b,c).
null(0):-inp294(a,b,c).
null(0):-inp295(a,b,c).
null(0):-inp296(a,b,c).
null(0):-inp297(a,b,c).
null(0):-inp298(a,b,c).
null(0):-inp299(a,b,c).
null(0):-inp300(a,b,c).
null(0):-inp301(a,b,c).
null(0):-inp302(a,b,c).
null(0):-inp303(a,b,c).
null(0):-inp304(a,b,c).
null(0):-inp305(a,b,c).
null(0):-inp306(a,b,c).
null(0):-inp307(a,b,c).
null(0):-inp308(a,b,c).
null(0):-inp309(a,b,c).
null(0):-inp310(a,b,c).
null(0):-inp311(a,b,c).
null(0):-inp312(a,b,c).
null(0):-inp313(a,b,c).
null(0):-inp314(a,b,c).
null(0):-inp315(a,b,c).
null(0):-inp316(a,b,c).
null(0):-inp317(a,b,c).
null(0):-inp318(a,b,c).
null(0):-inp319(a,b,c).
null(0):-inp320(a,b,c).
null(0):-inp321(a,b,c).
null(0):-inp322(a,b,c).
null(0):-inp323(a,b,c).
null(0):-inp324(a,b,c).
null(0):-inp325(a,b,c).
null(0):-inp326(a,b,c).
null(0):-inp327(a,b,c).
null(0):-inp328(a,b,c).
null(0):-inp329(a,b,c).
null(0):-inp330(a,b,c).
null(0):-inp331(a,b,c).
null(0):-inp332(a,b,c).
null(0):-inp333(a,b,c).
null(0):-inp334(a,b,c).
null(0):-inp335(a,b,c).
null(0):-inp336(a,b,c).
null(0):-inp337(a,b,c).
null(0):-inp338(a,b,c).
null(0):-inp339(a,b,c).
null(0):-inp340(a,b,c).
null(0):-inp341(a,b,c).
null(0):-inp342(a,b,c).
null(0):-inp343(a,b,c).
null(0):-inp344(a,b,c).
null(0):-inp345(a,b,c).
null(0):-inp346(a,b,c).
null(0):-inp347(a,b,c).
null(0):-inp348(a,b,c).
null(0):-inp349(a,b,c).
null(0):-inp350(a,b,c).
null(0):-inp351(a,b,c).
null(0):-inp352(a,b,c).
null(0):-inp353(a,b,c).
null(0):-inp354(a,b,c).
null(0):-inp355(a,b,c).
null(0):-inp356(a,b,c).
null(0):-inp357(a,b,c).
null(0):-inp358(a,b,c).
null(0):-inp359(a,b,c).
null(0):-inp360(a,b,c).
null(0):-inp361(a,b,c).
null(0):-inp362(a,b,c).
null(0):-inp363(a,b,c).
null(0):-inp364(a,b,c).
null(0):-inp365(a,b,c).
null(0):-inp366(a,b,c).
null(0):-inp367(a,b,c).
null(0):-inp368(a,b,c).
null(0):-inp369(a,b,c).
null(0):-inp370(a,b,c).
null(0):-inp371(a,b,c).
null(0):-inp372(a,b,c).
null(0):-inp373(a,b,c).
null(0):-inp374(a,b,c).
null(0):-inp375(a,b,c).
null(0):-inp376(a,b,c).
null(0):-inp377(a,b,c).
null(0):-inp378(a,b,c).
null(0):-inp379(a,b,c).
null(0):-inp380(a,b,c).
null(0):-inp381(a,b,c).
null(0):-inp382(a,b,c).
null(0):-inp383(a,b,c).
null(0):-inp384(a,b,c).
null(0):-inp385(a,b,c).
null(0):-inp386(a,b,c).
null(0):-inp387(a,b,c).
null(0):-inp388(a,b,c).
null(0):-inp389(a,b,c).
null(0):-inp390(a,b,c).
null(0):-inp391(a,b,c).
null(0):-inp392(a,b,c).
null(0):-inp393(a,b,c).
null(0):-inp394(a,b,c).
null(0):-inp395(a,b,c).
null(0):-inp396(a,b,c).
null(0):-inp397(a,b,c).
null(0):-inp398(a,b,c).
null(0):-inp399(a,b,c).
null(0):-inp400(a,b,c).
null(0):-inp401(a,b,c).
null(0):-inp402(a,b,c).
null(0):-inp403(a,b,c).
null(0):-inp404(a,b,c).
null(0):-inp405(a,b,c).
null(0):-inp406(a,b,c).
null(0):-inp407(a,b,c).
null(0):-inp408(a,b,c).
null(0):-inp409(a,b,c).
null(0):-inp410(a,b,c).
null(0):-inp411(a,b,c).
null(0):-inp412(a,b,c).
null(0):-inp413(a,b,c).
null(0):-inp414(a,b,c).
null(0):-inp415(a,b,c).
null(0):-inp416(a,b,c).
null(0):-inp417(a,b,c).
null(0):-inp418(a,b,c).
null(0):-inp419(a,b,c).
null(0):-inp420(a,b,c).
null(0):-inp421(a,b,c).
null(0):-inp422(a,b,c).
null(0):-inp423(a,b,c).
null(0):-inp424(a,b,c).
null(0):-inp425(a,b,c).
null(0):-inp426(a,b,c).
null(0):-inp427(a,b,c).
null(0):-inp428(a,b,c).
null(0):-inp429(a,b,c).
null(0):-inp430(a,b,c).
null(0):-inp431(a,b,c).
null(0):-inp432(a,b,c).
null(0):-inp433(a,b,c).
null(0):-inp434(a,b,c).
null(0):-inp435(a,b,c).
null(0):-inp436(a,b,c).
null(0):-inp437(a,b,c).
null(0):-inp438(a,b,c).
null(0):-inp439(a,b,c).
null(0):-inp440(a,b,c).
null(0):-inp441(a,b,c).
null(0):-inp442(a,b,c).
null(0):-inp443(a,b,c).
null(0):-inp444(a,b,c).
null(0):-inp445(a,b,c).
null(0):-inp446(a,b,c).
null(0):-inp447(a,b,c).
null(0):-inp448(a,b,c).
null(0):-inp449(a,b,c).
null(0):-inp450(a,b,c).
null(0):-inp451(a,b,c).
null(0):-inp452(a,b,c).
null(0):-inp453(a,b,c).
null(0):-inp454(a,b,c).
null(0):-inp455(a,b,c).
null(0):-inp456(a,b,c).
null(0):-inp457(a,b,c).
null(0):-inp458(a,b,c).
null(0):-inp459(a,b,c).
null(0):-inp460(a,b,c).
null(0):-inp461(a,b,c).
null(0):-inp462(a,b,c).
null(0):-inp463(a,b,c).
null(0):-inp464(a,b,c).
null(0):-inp465(a,b,c).
null(0):-inp466(a,b,c).
null(0):-inp467(a,b,c).
null(0):-inp468(a,b,c).
null(0):-inp469(a,b,c).
null(0):-inp470(a,b,c).
null(0):-inp471(a,b,c).
null(0):-inp472(a,b,c).
null(0):-inp473(a,b,c).
null(0):-inp474(a,b,c).
null(0):-inp475(a,b,c).
null(0):-inp476(a,b,c).
null(0):-inp477(a,b,c).
null(0):-inp478(a,b,c).
null(0):-inp479(a,b,c).
null(0):-inp480(a,b,c).
null(0):-inp481(a,b,c).
null(0):-inp482(a,b,c).
null(0):-inp483(a,b,c).
null(0):-inp484(a,b,c).
null(0):-inp485(a,b,c).
null(0):-inp486(a,b,c).
null(0):-inp487(a,b,c).
null(0):-inp488(a,b,c).
null(0):-inp489(a,b,c).
null(0):-inp490(a,b,c).
null(0):-inp491(a,b,c).
null(0):-inp492(a,b,c).
null(0):-inp493(a,b,c).
null(0):-inp494(a,b,c).
null(0):-inp495(a,b,c).
null(0):-inp496(a,b,c).
null(0):-inp497(a,b,c).
null(0):-inp498(a,b,c).
null(0):-inp499(a,b,c).
out1(a, b, c):-mid1(a, b, c).
out2(a, b, c):-mid2(a, b, c).
out3(a, b, c):-mid3(a, b, c).
out4(a, b, c):-mid4(a, b, c).
out5(a, b, c):-mid5(a, b, c).
out6(a, b, c):-mid6(a, b, c).
out7(a, b, c):-mid7(a, b, c).
out8(a, b, c):-mid8(a, b, c).
out9(a, b, c):-mid9(a, b, c).
out10(a, b, c):-mid10(a, b, c).
out11(a, b, c):-mid11(a, b, c).
out12(a, b, c):-mid12(a, b, c).
out13(a, b, c):-mid13(a, b, c).
out14(a, b, c):-mid14(a, b, c).
out15(a, b, c):-mid15(a, b, c).
out16(a, b, c):-mid16(a, b, c).
out17(a, b, c):-mid17(a, b, c).
out18(a, b, c):-mid18(a, b, c).
out19(a, b, c):-mid19(a, b, c).
out20(a, b, c):-mid20(a, b, c).
out21(a, b, c):-mid21(a, b, c).
out22(a, b, c):-mid22(a, b, c).
out23(a, b, c):-mid23(a, b, c).
out24(a, b, c):-mid24(a, b, c).
out25(a, b, c):-mid25(a, b, c).
out26(a, b, c):-mid26(a, b, c).
out27(a, b, c):-mid27(a, b, c).
out28(a, b, c):-mid28(a, b, c).
out29(a, b, c):-mid29(a, b, c).
out30(a, b, c):-mid30(a, b, c).
out31(a, b, c):-mid31(a, b, c).
out32(a, b, c):-mid32(a, b, c).
out33(a, b, c):-mid33(a, b, c).
out34(a, b, c):-mid34(a, b, c).
out35(a, b, c):-mid35(a, b, c).
out36(a, b, c):-mid36(a, b, c).
out37(a, b, c):-mid37(a, b, c).
out38(a, b, c):-mid38(a, b, c).
out39(a, b, c):-mid39(a, b, c).
out40(a, b, c):-mid40(a, b, c).
out41(a, b, c):-mid41(a, b, c).
out42(a, b, c):-mid42(a, b, c).
out43(a, b, c):-mid43(a, b, c).
out44(a, b, c):-mid44(a, b, c).
out45(a, b, c):-mid45(a, b, c).
out46(a, b, c):-mid46(a, b, c).
out47(a, b, c):-mid47(a, b, c).
out48(a, b, c):-mid48(a, b, c).
out49(a, b, c):-mid49(a, b, c).
out50(a, b, c):-mid50(a, b, c).
out51(a, b, c):-mid51(a, b, c).
out52(a, b, c):-mid52(a, b, c).
out53(a, b, c):-mid53(a, b, c).
out54(a, b, c):-mid54(a, b, c).
out55(a, b, c):-mid55(a, b, c).
out56(a, b, c):-mid56(a, b, c).
out57(a, b, c):-mid57(a, b, c).
out58(a, b, c):-mid58(a, b, c).
out59(a, b, c):-mid59(a, b, c).
out60(a, b, c):-mid60(a, b, c).
out61(a, b, c):-mid61(a, b, c).
out62(a, b, c):-mid62(a, b, c).
out63(a, b, c):-mid63(a, b, c).
out64(a, b, c):-mid64(a, b, c).
out65(a, b, c):-mid65(a, b, c).
out66(a, b, c):-mid66(a, b, c).
out67(a, b, c):-mid67(a, b, c).
out68(a, b, c):-mid68(a, b, c).
out69(a, b, c):-mid69(a, b, c).
out70(a, b, c):-mid70(a, b, c).
out71(a, b, c):-mid71(a, b, c).
out72(a, b, c):-mid72(a, b, c).
out73(a, b, c):-mid73(a, b, c).
out74(a, b, c):-mid74(a, b, c).
out75(a, b, c):-mid75(a, b, c).
out76(a, b, c):-mid76(a, b, c).
out77(a, b, c):-mid77(a, b, c).
out78(a, b, c):-mid78(a, b, c).
out79(a, b, c):-mid79(a, b, c).
out80(a, b, c):-mid80(a, b, c).
out81(a, b, c):-mid81(a, b, c).
out82(a, b, c):-mid82(a, b, c).
out83(a, b, c):-mid83(a, b, c).
out84(a, b, c):-mid84(a, b, c).
out85(a, b, c):-mid85(a, b, c).
out86(a, b, c):-mid86(a, b, c).
out87(a, b, c):-mid87(a, b, c).
out88(a, b, c):-mid88(a, b, c).
out89(a, b, c):-mid89(a, b, c).
out90(a, b, c):-mid90(a, b, c).
out91(a, b, c):-mid91(a, b, c).
out92(a, b, c):-mid92(a, b, c).
out93(a, b, c):-mid93(a, b, c).
out94(a, b, c):-mid94(a, b, c).
out95(a, b, c):-mid95(a, b, c).
out96(a, b, c):-mid96(a, b, c).
out97(a, b, c):-mid97(a, b, c).
out98(a, b, c):-mid98(a, b, c).
out99(a, b, c):-mid99(a, b, c).
out100(a, b, c):-mid100(a, b, c).
out101(a, b, c):-mid101(a, b, c).
out102(a, b, c):-mid102(a, b, c).
out103(a, b, c):-mid103(a, b, c).
out104(a, b, c):-mid104(a, b, c).
out105(a, b, c):-mid105(a, b, c).
out106(a, b, c):-mid106(a, b, c).
out107(a, b, c):-mid107(a, b, c).
out108(a, b, c):-mid108(a, b, c).
out109(a, b, c):-mid109(a, b, c).
out110(a, b, c):-mid110(a, b, c).
out111(a, b, c):-mid111(a, b, c).
out112(a, b, c):-mid112(a, b, c).
out113(a, b, c):-mid113(a, b, c).
out114(a, b, c):-mid114(a, b, c).
out115(a, b, c):-mid115(a, b, c).
out116(a, b, c):-mid116(a, b, c).
out117(a, b, c):-mid117(a, b, c).
out118(a, b, c):-mid118(a, b, c).
out119(a, b, c):-mid119(a, b, c).
out120(a, b, c):-mid120(a, b, c).
out121(a, b, c):-mid121(a, b, c).
out122(a, b, c):-mid122(a, b, c).
out123(a, b, c):-mid123(a, b, c).
out124(a, b, c):-mid124(a, b, c).
out125(a, b, c):-mid125(a, b, c).
out126(a, b, c):-mid126(a, b, c).
out127(a, b, c):-mid127(a, b, c).
out128(a, b, c):-mid128(a, b, c).
out129(a, b, c):-mid129(a, b, c).
out130(a, b, c):-mid130(a, b, c).
out131(a, b, c):-mid131(a, b, c).
out132(a, b, c):-mid132(a, b, c).
out133(a, b, c):-mid133(a, b, c).
out134(a, b, c):-mid134(a, b, c).
out135(a, b, c):-mid135(a, b, c).
out136(a, b, c):-mid136(a, b, c).
out137(a, b, c):-mid137(a, b, c).
out138(a, b, c):-mid138(a, b, c).
out139(a, b, c):-mid139(a, b, c).
out140(a, b, c):-mid140(a, b, c).
out141(a, b, c):-mid141(a, b, c).
out142(a, b, c):-mid142(a, b, c).
out143(a, b, c):-mid143(a, b, c).
out144(a, b, c):-mid144(a, b, c).
out145(a, b, c):-mid145(a, b, c).
out146(a, b, c):-mid146(a, b, c).
out147(a, b, c):-mid147(a, b, c).
out148(a, b, c):-mid148(a, b, c).
out149(a, b, c):-mid149(a, b, c).
out150(a, b, c):-mid150(a, b, c).
out151(a, b, c):-mid151(a, b, c).
out152(a, b, c):-mid152(a, b, c).
out153(a, b, c):-mid153(a, b, c).
out154(a, b, c):-mid154(a, b, c).
out155(a, b, c):-mid155(a, b, c).
out156(a, b, c):-mid156(a, b, c).
out157(a, b, c):-mid157(a, b, c).
out158(a, b, c):-mid158(a, b, c).
out159(a, b, c):-mid159(a, b, c).
out160(a, b, c):-mid160(a, b, c).
out161(a, b, c):-mid161(a, b, c).
out162(a, b, c):-mid162(a, b, c).
out163(a, b, c):-mid163(a, b, c).
out164(a, b, c):-mid164(a, b, c).
out165(a, b, c):-mid165(a, b, c).
out166(a, b, c):-mid166(a, b, c).
out167(a, b, c):-mid167(a, b, c).
out168(a, b, c):-mid168(a, b, c).
out169(a, b, c):-mid169(a, b, c).
out170(a, b, c):-mid170(a, b, c).
out171(a, b, c):-mid171(a, b, c).
out172(a, b, c):-mid172(a, b, c).
out173(a, b, c):-mid173(a, b, c).
out174(a, b, c):-mid174(a, b, c).
out175(a, b, c):-mid175(a, b, c).
out176(a, b, c):-mid176(a, b, c).
out177(a, b, c):-mid177(a, b, c).
out178(a, b, c):-mid178(a, b, c).
out179(a, b, c):-mid179(a, b, c).
out180(a, b, c):-mid180(a, b, c).
out181(a, b, c):-mid181(a, b, c).
out182(a, b, c):-mid182(a, b, c).
out183(a, b, c):-mid183(a, b, c).
out184(a, b, c):-mid184(a, b, c).
out185(a, b, c):-mid185(a, b, c).
out186(a, b, c):-mid186(a, b, c).
out187(a, b, c):-mid187(a, b, c).
out188(a, b, c):-mid188(a, b, c).
out189(a, b, c):-mid189(a, b, c).
out190(a, b, c):-mid190(a, b, c).
out191(a, b, c):-mid191(a, b, c).
out192(a, b, c):-mid192(a, b, c).
out193(a, b, c):-mid193(a, b, c).
out194(a, b, c):-mid194(a, b, c).
out195(a, b, c):-mid195(a, b, c).
out196(a, b, c):-mid196(a, b, c).
out197(a, b, c):-mid197(a, b, c).
out198(a, b, c):-mid198(a, b, c).
out199(a, b, c):-mid199(a, b, c).
out200(a, b, c):-mid200(a, b, c).
out201(a, b, c):-mid201(a, b, c).
out202(a, b, c):-mid202(a, b, c).
out203(a, b, c):-mid203(a, b, c).
out204(a, b, c):-mid204(a, b, c).
out205(a, b, c):-mid205(a, b, c).
out206(a, b, c):-mid206(a, b, c).
out207(a, b, c):-mid207(a, b, c).
out208(a, b, c):-mid208(a, b, c).
out209(a, b, c):-mid209(a, b, c).
out210(a, b, c):-mid210(a, b, c).
out211(a, b, c):-mid211(a, b, c).
out212(a, b, c):-mid212(a, b, c).
out213(a, b, c):-mid213(a, b, c).
out214(a, b, c):-mid214(a, b, c).
out215(a, b, c):-mid215(a, b, c).
out216(a, b, c):-mid216(a, b, c).
out217(a, b, c):-mid217(a, b, c).
out218(a, b, c):-mid218(a, b, c).
out219(a, b, c):-mid219(a, b, c).
out220(a, b, c):-mid220(a, b, c).
out221(a, b, c):-mid221(a, b, c).
out222(a, b, c):-mid222(a, b, c).
out223(a, b, c):-mid223(a, b, c).
out224(a, b, c):-mid224(a, b, c).
out225(a, b, c):-mid225(a, b, c).
out226(a, b, c):-mid226(a, b, c).
out227(a, b, c):-mid227(a, b, c).
out228(a, b, c):-mid228(a, b, c).
out229(a, b, c):-mid229(a, b, c).
out230(a, b, c):-mid230(a, b, c).
out231(a, b, c):-mid231(a, b, c).
out232(a, b, c):-mid232(a, b, c).
out233(a, b, c):-mid233(a, b, c).
out234(a, b, c):-mid234(a, b, c).
out235(a, b, c):-mid235(a, b, c).
out236(a, b, c):-mid236(a, b, c).
out237(a, b, c):-mid237(a, b, c).
out238(a, b, c):-mid238(a, b, c).
out239(a, b, c):-mid239(a, b, c).
%function funcinp1(a:int8, b:int8, c:int8->d:int8)
%function funcinp2(a:int8, b:int8, c:int8->d:int8)
%function funcinp3(a:int8, b:int8, c:int8->d:int8)
%function funcinp4(a:int8, b:int8, c:int8->d:int8)
%function funcinp5(a:int8, b:int8, c:int8->d:int8)
%function funcinp6(a:int8, b:int8, c:int8->d:int8)
%function funcinp7(a:int8, b:int8, c:int8->d:int8)
%function funcinp8(a:int8, b:int8, c:int8->d:int8)
%function funcinp9(a:int8, b:int8, c:int8->d:int8)
%function funcinp10(a:int8, b:int8, c:int8->d:int8)
%function funcinp11(a:int8, b:int8, c:int8->d:int8)
%function funcinp12(a:int8, b:int8, c:int8->d:int8)
%function funcinp13(a:int8, b:int8, c:int8->d:int8)
%function funcinp14(a:int8, b:int8, c:int8->d:int8)
%function funcinp15(a:int8, b:int8, c:int8->d:int8)
%function funcinp16(a:int8, b:int8, c:int8->d:int8)
%function funcinp17(a:int8, b:int8, c:int8->d:int8)
%function funcinp18(a:int8, b:int8, c:int8->d:int8)
%function funcinp19(a:int8, b:int8, c:int8->d:int8)
%function funcinp20(a:int8, b:int8, c:int8->d:int8)
%function funcinp21(a:int8, b:int8, c:int8->d:int8)
%function funcinp22(a:int8, b:int8, c:int8->d:int8)
%function funcinp23(a:int8, b:int8, c:int8->d:int8)
%function funcinp24(a:int8, b:int8, c:int8->d:int8)
%function funcinp25(a:int8, b:int8, c:int8->d:int8)
%function funcinp26(a:int8, b:int8, c:int8->d:int8)
%function funcinp27(a:int8, b:int8, c:int8->d:int8)
%function funcinp28(a:int8, b:int8, c:int8->d:int8)
%function funcinp29(a:int8, b:int8, c:int8->d:int8)
%function funcinp30(a:int8, b:int8, c:int8->d:int8)
%function funcinp31(a:int8, b:int8, c:int8->d:int8)
%function funcinp32(a:int8, b:int8, c:int8->d:int8)
%function funcinp33(a:int8, b:int8, c:int8->d:int8)
%function funcinp34(a:int8, b:int8, c:int8->d:int8)
%function funcinp35(a:int8, b:int8, c:int8->d:int8)
%function funcinp36(a:int8, b:int8, c:int8->d:int8)
%function funcinp37(a:int8, b:int8, c:int8->d:int8)
%function funcinp38(a:int8, b:int8, c:int8->d:int8)
%function funcinp39(a:int8, b:int8, c:int8->d:int8)
%function funcinp40(a:int8, b:int8, c:int8->d:int8)
%function funcinp41(a:int8, b:int8, c:int8->d:int8)
%function funcinp42(a:int8, b:int8, c:int8->d:int8)
%function funcinp43(a:int8, b:int8, c:int8->d:int8)
%function funcinp44(a:int8, b:int8, c:int8->d:int8)
%function funcinp45(a:int8, b:int8, c:int8->d:int8)
%function funcinp46(a:int8, b:int8, c:int8->d:int8)
%function funcinp47(a:int8, b:int8, c:int8->d:int8)
%function funcinp48(a:int8, b:int8, c:int8->d:int8)
%function funcinp49(a:int8, b:int8, c:int8->d:int8)
%function funcinp50(a:int8, b:int8, c:int8->d:int8)
%function funcinp51(a:int8, b:int8, c:int8->d:int8)
%function funcinp52(a:int8, b:int8, c:int8->d:int8)
%function funcinp53(a:int8, b:int8, c:int8->d:int8)
%function funcinp54(a:int8, b:int8, c:int8->d:int8)
%function funcinp55(a:int8, b:int8, c:int8->d:int8)
%function funcinp56(a:int8, b:int8, c:int8->d:int8)
%function funcinp57(a:int8, b:int8, c:int8->d:int8)
%function funcinp58(a:int8, b:int8, c:int8->d:int8)
%function funcinp59(a:int8, b:int8, c:int8->d:int8)
%function funcinp60(a:int8, b:int8, c:int8->d:int8)
%function funcinp61(a:int8, b:int8, c:int8->d:int8)
%function funcinp62(a:int8, b:int8, c:int8->d:int8)
%function funcinp63(a:int8, b:int8, c:int8->d:int8)
%function funcinp64(a:int8, b:int8, c:int8->d:int8)
%function funcinp65(a:int8, b:int8, c:int8->d:int8)
%function funcinp66(a:int8, b:int8, c:int8->d:int8)
%function funcinp67(a:int8, b:int8, c:int8->d:int8)
%function funcinp68(a:int8, b:int8, c:int8->d:int8)
%function funcinp69(a:int8, b:int8, c:int8->d:int8)
%function funcinp70(a:int8, b:int8, c:int8->d:int8)
%function funcinp71(a:int8, b:int8, c:int8->d:int8)
%function funcinp72(a:int8, b:int8, c:int8->d:int8)
%function funcinp73(a:int8, b:int8, c:int8->d:int8)
%function funcinp74(a:int8, b:int8, c:int8->d:int8)
%function funcinp75(a:int8, b:int8, c:int8->d:int8)
%function funcinp76(a:int8, b:int8, c:int8->d:int8)
%function funcinp77(a:int8, b:int8, c:int8->d:int8)
%function funcinp78(a:int8, b:int8, c:int8->d:int8)
%function funcinp79(a:int8, b:int8, c:int8->d:int8)
%function funcinp80(a:int8, b:int8, c:int8->d:int8)
%function funcinp81(a:int8, b:int8, c:int8->d:int8)
%function funcinp82(a:int8, b:int8, c:int8->d:int8)
%function funcinp83(a:int8, b:int8, c:int8->d:int8)
%function funcinp84(a:int8, b:int8, c:int8->d:int8)
%function funcinp85(a:int8, b:int8, c:int8->d:int8)
%function funcinp86(a:int8, b:int8, c:int8->d:int8)
%function funcinp87(a:int8, b:int8, c:int8->d:int8)
%function funcinp88(a:int8, b:int8, c:int8->d:int8)
%function funcinp89(a:int8, b:int8, c:int8->d:int8)
%function funcinp90(a:int8, b:int8, c:int8->d:int8)
%function funcinp91(a:int8, b:int8, c:int8->d:int8)
%function funcinp92(a:int8, b:int8, c:int8->d:int8)
%function funcinp93(a:int8, b:int8, c:int8->d:int8)
%function funcinp94(a:int8, b:int8, c:int8->d:int8)
%function funcinp95(a:int8, b:int8, c:int8->d:int8)
%function funcinp96(a:int8, b:int8, c:int8->d:int8)
%function funcinp97(a:int8, b:int8, c:int8->d:int8)
%function funcinp98(a:int8, b:int8, c:int8->d:int8)
%function funcinp99(a:int8, b:int8, c:int8->d:int8)
%function funcinp100(a:int8, b:int8, c:int8->d:int8)
%function funcinp101(a:int8, b:int8, c:int8->d:int8)
%function funcinp102(a:int8, b:int8, c:int8->d:int8)
%function funcinp103(a:int8, b:int8, c:int8->d:int8)
%function funcinp104(a:int8, b:int8, c:int8->d:int8)
%function funcinp105(a:int8, b:int8, c:int8->d:int8)
%function funcinp106(a:int8, b:int8, c:int8->d:int8)
%function funcinp107(a:int8, b:int8, c:int8->d:int8)
%function funcinp108(a:int8, b:int8, c:int8->d:int8)
%function funcinp109(a:int8, b:int8, c:int8->d:int8)
%function funcinp110(a:int8, b:int8, c:int8->d:int8)
%function funcinp111(a:int8, b:int8, c:int8->d:int8)
%function funcinp112(a:int8, b:int8, c:int8->d:int8)
%function funcinp113(a:int8, b:int8, c:int8->d:int8)
%function funcinp114(a:int8, b:int8, c:int8->d:int8)
%function funcinp115(a:int8, b:int8, c:int8->d:int8)
%function funcinp116(a:int8, b:int8, c:int8->d:int8)
%function funcinp117(a:int8, b:int8, c:int8->d:int8)
%function funcinp118(a:int8, b:int8, c:int8->d:int8)
%function funcinp119(a:int8, b:int8, c:int8->d:int8)
%function funcinp120(a:int8, b:int8, c:int8->d:int8)
%function funcinp121(a:int8, b:int8, c:int8->d:int8)
%function funcinp122(a:int8, b:int8, c:int8->d:int8)
%function funcinp123(a:int8, b:int8, c:int8->d:int8)
%function funcinp124(a:int8, b:int8, c:int8->d:int8)
%function funcinp125(a:int8, b:int8, c:int8->d:int8)
%function funcinp126(a:int8, b:int8, c:int8->d:int8)
%function funcinp127(a:int8, b:int8, c:int8->d:int8)
%function funcinp128(a:int8, b:int8, c:int8->d:int8)
%function funcinp129(a:int8, b:int8, c:int8->d:int8)
%function funcinp130(a:int8, b:int8, c:int8->d:int8)
%function funcinp131(a:int8, b:int8, c:int8->d:int8)
%function funcinp132(a:int8, b:int8, c:int8->d:int8)
%function funcinp133(a:int8, b:int8, c:int8->d:int8)
%function funcinp134(a:int8, b:int8, c:int8->d:int8)
%function funcinp135(a:int8, b:int8, c:int8->d:int8)
%function funcinp136(a:int8, b:int8, c:int8->d:int8)
%function funcinp137(a:int8, b:int8, c:int8->d:int8)
%function funcinp138(a:int8, b:int8, c:int8->d:int8)
%function funcinp139(a:int8, b:int8, c:int8->d:int8)
%function funcinp140(a:int8, b:int8, c:int8->d:int8)
%function funcinp141(a:int8, b:int8, c:int8->d:int8)
%function funcinp142(a:int8, b:int8, c:int8->d:int8)
%function funcinp143(a:int8, b:int8, c:int8->d:int8)
%function funcinp144(a:int8, b:int8, c:int8->d:int8)
%function funcinp145(a:int8, b:int8, c:int8->d:int8)
%function funcinp146(a:int8, b:int8, c:int8->d:int8)
%function funcinp147(a:int8, b:int8, c:int8->d:int8)
%function funcinp148(a:int8, b:int8, c:int8->d:int8)
%function funcinp149(a:int8, b:int8, c:int8->d:int8)
%function funcinp150(a:int8, b:int8, c:int8->d:int8)
%function funcinp151(a:int8, b:int8, c:int8->d:int8)
%function funcinp152(a:int8, b:int8, c:int8->d:int8)
%function funcinp153(a:int8, b:int8, c:int8->d:int8)
%function funcinp154(a:int8, b:int8, c:int8->d:int8)
%function funcinp155(a:int8, b:int8, c:int8->d:int8)
%function funcinp156(a:int8, b:int8, c:int8->d:int8)
%function funcinp157(a:int8, b:int8, c:int8->d:int8)
%function funcinp158(a:int8, b:int8, c:int8->d:int8)
%function funcinp159(a:int8, b:int8, c:int8->d:int8)
%function funcinp160(a:int8, b:int8, c:int8->d:int8)
%function funcinp161(a:int8, b:int8, c:int8->d:int8)
%function funcinp162(a:int8, b:int8, c:int8->d:int8)
%function funcinp163(a:int8, b:int8, c:int8->d:int8)
%function funcinp164(a:int8, b:int8, c:int8->d:int8)
%function funcinp165(a:int8, b:int8, c:int8->d:int8)
%function funcinp166(a:int8, b:int8, c:int8->d:int8)
%function funcinp167(a:int8, b:int8, c:int8->d:int8)
%function funcinp168(a:int8, b:int8, c:int8->d:int8)
%function funcinp169(a:int8, b:int8, c:int8->d:int8)
%function funcinp170(a:int8, b:int8, c:int8->d:int8)
%function funcinp171(a:int8, b:int8, c:int8->d:int8)
%function funcinp172(a:int8, b:int8, c:int8->d:int8)
%function funcinp173(a:int8, b:int8, c:int8->d:int8)
%function funcinp174(a:int8, b:int8, c:int8->d:int8)
%function funcinp175(a:int8, b:int8, c:int8->d:int8)
%function funcinp176(a:int8, b:int8, c:int8->d:int8)
%function funcinp177(a:int8, b:int8, c:int8->d:int8)
%function funcinp178(a:int8, b:int8, c:int8->d:int8)
%function funcinp179(a:int8, b:int8, c:int8->d:int8)
%function funcinp180(a:int8, b:int8, c:int8->d:int8)
%function funcinp181(a:int8, b:int8, c:int8->d:int8)
%function funcinp182(a:int8, b:int8, c:int8->d:int8)
%function funcinp183(a:int8, b:int8, c:int8->d:int8)
%function funcinp184(a:int8, b:int8, c:int8->d:int8)
%function funcinp185(a:int8, b:int8, c:int8->d:int8)
%function funcinp186(a:int8, b:int8, c:int8->d:int8)
%function funcinp187(a:int8, b:int8, c:int8->d:int8)
%function funcinp188(a:int8, b:int8, c:int8->d:int8)
%function funcinp189(a:int8, b:int8, c:int8->d:int8)
%function funcinp190(a:int8, b:int8, c:int8->d:int8)
%function funcinp191(a:int8, b:int8, c:int8->d:int8)
%function funcinp192(a:int8, b:int8, c:int8->d:int8)
%function funcinp193(a:int8, b:int8, c:int8->d:int8)
%function funcinp194(a:int8, b:int8, c:int8->d:int8)
%function funcinp195(a:int8, b:int8, c:int8->d:int8)
%function funcinp196(a:int8, b:int8, c:int8->d:int8)
%function funcinp197(a:int8, b:int8, c:int8->d:int8)
%function funcinp198(a:int8, b:int8, c:int8->d:int8)
%function funcinp199(a:int8, b:int8, c:int8->d:int8)
%function funcinp200(a:int8, b:int8, c:int8->d:int8)
%function funcinp201(a:int8, b:int8, c:int8->d:int8)
%function funcinp202(a:int8, b:int8, c:int8->d:int8)
%function funcinp203(a:int8, b:int8, c:int8->d:int8)
%function funcinp204(a:int8, b:int8, c:int8->d:int8)
%function funcinp205(a:int8, b:int8, c:int8->d:int8)
%function funcinp206(a:int8, b:int8, c:int8->d:int8)
%function funcinp207(a:int8, b:int8, c:int8->d:int8)
%function funcinp208(a:int8, b:int8, c:int8->d:int8)
%function funcinp209(a:int8, b:int8, c:int8->d:int8)
%function funcinp210(a:int8, b:int8, c:int8->d:int8)
%function funcinp211(a:int8, b:int8, c:int8->d:int8)
%function funcinp212(a:int8, b:int8, c:int8->d:int8)
%function funcinp213(a:int8, b:int8, c:int8->d:int8)
%function funcinp214(a:int8, b:int8, c:int8->d:int8)
%function funcinp215(a:int8, b:int8, c:int8->d:int8)
%function funcinp216(a:int8, b:int8, c:int8->d:int8)
%function funcinp217(a:int8, b:int8, c:int8->d:int8)
%function funcinp218(a:int8, b:int8, c:int8->d:int8)
%function funcinp219(a:int8, b:int8, c:int8->d:int8)
%function funcinp220(a:int8, b:int8, c:int8->d:int8)
%function funcinp221(a:int8, b:int8, c:int8->d:int8)
%function funcinp222(a:int8, b:int8, c:int8->d:int8)
%function funcinp223(a:int8, b:int8, c:int8->d:int8)
%function funcinp224(a:int8, b:int8, c:int8->d:int8)
%function funcinp225(a:int8, b:int8, c:int8->d:int8)
%function funcinp226(a:int8, b:int8, c:int8->d:int8)
%function funcinp227(a:int8, b:int8, c:int8->d:int8)
%function funcinp228(a:int8, b:int8, c:int8->d:int8)
%function funcinp229(a:int8, b:int8, c:int8->d:int8)
%function funcinp230(a:int8, b:int8, c:int8->d:int8)
%function funcinp231(a:int8, b:int8, c:int8->d:int8)
%function funcinp232(a:int8, b:int8, c:int8->d:int8)
%function funcinp233(a:int8, b:int8, c:int8->d:int8)
%function funcinp234(a:int8, b:int8, c:int8->d:int8)
%function funcinp235(a:int8, b:int8, c:int8->d:int8)
%function funcinp236(a:int8, b:int8, c:int8->d:int8)
%function funcinp237(a:int8, b:int8, c:int8->d:int8)
%function funcinp238(a:int8, b:int8, c:int8->d:int8)
%function funcinp239(a:int8, b:int8, c:int8->d:int8)
%function funcinp240(a:int8, b:int8, c:int8->d:int8)
%function funcinp241(a:int8, b:int8, c:int8->d:int8)
%function funcinp242(a:int8, b:int8, c:int8->d:int8)
%function funcinp243(a:int8, b:int8, c:int8->d:int8)
%function funcinp244(a:int8, b:int8, c:int8->d:int8)
%function funcinp245(a:int8, b:int8, c:int8->d:int8)
%function funcinp246(a:int8, b:int8, c:int8->d:int8)
%function funcinp247(a:int8, b:int8, c:int8->d:int8)
%function funcinp248(a:int8, b:int8, c:int8->d:int8)
%function funcinp249(a:int8, b:int8, c:int8->d:int8)
%function funcinp250(a:int8, b:int8, c:int8->d:int8)
%function funcinp251(a:int8, b:int8, c:int8->d:int8)
%function funcinp252(a:int8, b:int8, c:int8->d:int8)
%function funcinp253(a:int8, b:int8, c:int8->d:int8)
%function funcinp254(a:int8, b:int8, c:int8->d:int8)
%function funcinp255(a:int8, b:int8, c:int8->d:int8)
%function funcinp256(a:int8, b:int8, c:int8->d:int8)
%function funcinp257(a:int8, b:int8, c:int8->d:int8)
%function funcinp258(a:int8, b:int8, c:int8->d:int8)
%function funcinp259(a:int8, b:int8, c:int8->d:int8)
%function funcinp260(a:int8, b:int8, c:int8->d:int8)
%function funcinp261(a:int8, b:int8, c:int8->d:int8)
%function funcinp262(a:int8, b:int8, c:int8->d:int8)
%function funcinp263(a:int8, b:int8, c:int8->d:int8)
%function funcinp264(a:int8, b:int8, c:int8->d:int8)
%function funcinp265(a:int8, b:int8, c:int8->d:int8)
%function funcinp266(a:int8, b:int8, c:int8->d:int8)
%function funcinp267(a:int8, b:int8, c:int8->d:int8)
%function funcinp268(a:int8, b:int8, c:int8->d:int8)
%function funcinp269(a:int8, b:int8, c:int8->d:int8)
%function funcinp270(a:int8, b:int8, c:int8->d:int8)
%function funcinp271(a:int8, b:int8, c:int8->d:int8)
%function funcinp272(a:int8, b:int8, c:int8->d:int8)
%function funcinp273(a:int8, b:int8, c:int8->d:int8)
%function funcinp274(a:int8, b:int8, c:int8->d:int8)
%function funcinp275(a:int8, b:int8, c:int8->d:int8)
%function funcinp276(a:int8, b:int8, c:int8->d:int8)
%function funcinp277(a:int8, b:int8, c:int8->d:int8)
%function funcinp278(a:int8, b:int8, c:int8->d:int8)
%function funcinp279(a:int8, b:int8, c:int8->d:int8)
%function funcinp280(a:int8, b:int8, c:int8->d:int8)
%function funcinp281(a:int8, b:int8, c:int8->d:int8)
%function funcinp282(a:int8, b:int8, c:int8->d:int8)
%function funcinp283(a:int8, b:int8, c:int8->d:int8)
%function funcinp284(a:int8, b:int8, c:int8->d:int8)
%function funcinp285(a:int8, b:int8, c:int8->d:int8)
%function funcinp286(a:int8, b:int8, c:int8->d:int8)
%function funcinp287(a:int8, b:int8, c:int8->d:int8)
%function funcinp288(a:int8, b:int8, c:int8->d:int8)
%function funcinp289(a:int8, b:int8, c:int8->d:int8)
%function funcinp290(a:int8, b:int8, c:int8->d:int8)
%function funcinp291(a:int8, b:int8, c:int8->d:int8)
%function funcinp292(a:int8, b:int8, c:int8->d:int8)
%function funcinp293(a:int8, b:int8, c:int8->d:int8)
%function funcinp294(a:int8, b:int8, c:int8->d:int8)
%function funcinp295(a:int8, b:int8, c:int8->d:int8)
%function funcinp296(a:int8, b:int8, c:int8->d:int8)
%function funcinp297(a:int8, b:int8, c:int8->d:int8)
%function funcinp298(a:int8, b:int8, c:int8->d:int8)
%function funcinp299(a:int8, b:int8, c:int8->d:int8)
%function funcinp300(a:int8, b:int8, c:int8->d:int8)
%function funcinp301(a:int8, b:int8, c:int8->d:int8)
%function funcinp302(a:int8, b:int8, c:int8->d:int8)
%function funcinp303(a:int8, b:int8, c:int8->d:int8)
%function funcinp304(a:int8, b:int8, c:int8->d:int8)
%function funcinp305(a:int8, b:int8, c:int8->d:int8)
%function funcinp306(a:int8, b:int8, c:int8->d:int8)
%function funcinp307(a:int8, b:int8, c:int8->d:int8)
%function funcinp308(a:int8, b:int8, c:int8->d:int8)
%function funcinp309(a:int8, b:int8, c:int8->d:int8)
%function funcinp310(a:int8, b:int8, c:int8->d:int8)
%function funcinp311(a:int8, b:int8, c:int8->d:int8)
%function funcinp312(a:int8, b:int8, c:int8->d:int8)
%function funcinp313(a:int8, b:int8, c:int8->d:int8)
%function funcinp314(a:int8, b:int8, c:int8->d:int8)
%function funcinp315(a:int8, b:int8, c:int8->d:int8)
%function funcinp316(a:int8, b:int8, c:int8->d:int8)
%function funcinp317(a:int8, b:int8, c:int8->d:int8)
%function funcinp318(a:int8, b:int8, c:int8->d:int8)
%function funcinp319(a:int8, b:int8, c:int8->d:int8)
%function funcinp320(a:int8, b:int8, c:int8->d:int8)
%function funcinp321(a:int8, b:int8, c:int8->d:int8)
%function funcinp322(a:int8, b:int8, c:int8->d:int8)
%function funcinp323(a:int8, b:int8, c:int8->d:int8)
%function funcinp324(a:int8, b:int8, c:int8->d:int8)
%function funcinp325(a:int8, b:int8, c:int8->d:int8)
%function funcinp326(a:int8, b:int8, c:int8->d:int8)
%function funcinp327(a:int8, b:int8, c:int8->d:int8)
%function funcinp328(a:int8, b:int8, c:int8->d:int8)
%function funcinp329(a:int8, b:int8, c:int8->d:int8)
%function funcinp330(a:int8, b:int8, c:int8->d:int8)
%function funcinp331(a:int8, b:int8, c:int8->d:int8)
%function funcinp332(a:int8, b:int8, c:int8->d:int8)
%function funcinp333(a:int8, b:int8, c:int8->d:int8)
%function funcinp334(a:int8, b:int8, c:int8->d:int8)
%function funcinp335(a:int8, b:int8, c:int8->d:int8)
%function funcinp336(a:int8, b:int8, c:int8->d:int8)
%function funcinp337(a:int8, b:int8, c:int8->d:int8)
%function funcinp338(a:int8, b:int8, c:int8->d:int8)
%function funcinp339(a:int8, b:int8, c:int8->d:int8)
%function funcinp340(a:int8, b:int8, c:int8->d:int8)
%function funcinp341(a:int8, b:int8, c:int8->d:int8)
%function funcinp342(a:int8, b:int8, c:int8->d:int8)
%function funcinp343(a:int8, b:int8, c:int8->d:int8)
%function funcinp344(a:int8, b:int8, c:int8->d:int8)
%function funcinp345(a:int8, b:int8, c:int8->d:int8)
%function funcinp346(a:int8, b:int8, c:int8->d:int8)
%function funcinp347(a:int8, b:int8, c:int8->d:int8)
%function funcinp348(a:int8, b:int8, c:int8->d:int8)
%function funcinp349(a:int8, b:int8, c:int8->d:int8)
%function funcinp350(a:int8, b:int8, c:int8->d:int8)
%function funcinp351(a:int8, b:int8, c:int8->d:int8)
%function funcinp352(a:int8, b:int8, c:int8->d:int8)
%function funcinp353(a:int8, b:int8, c:int8->d:int8)
%function funcinp354(a:int8, b:int8, c:int8->d:int8)
%function funcinp355(a:int8, b:int8, c:int8->d:int8)
%function funcinp356(a:int8, b:int8, c:int8->d:int8)
%function funcinp357(a:int8, b:int8, c:int8->d:int8)
%function funcinp358(a:int8, b:int8, c:int8->d:int8)
%function funcinp359(a:int8, b:int8, c:int8->d:int8)
%function funcinp360(a:int8, b:int8, c:int8->d:int8)
%function funcinp361(a:int8, b:int8, c:int8->d:int8)
%function funcinp362(a:int8, b:int8, c:int8->d:int8)
%function funcinp363(a:int8, b:int8, c:int8->d:int8)
%function funcinp364(a:int8, b:int8, c:int8->d:int8)
%function funcinp365(a:int8, b:int8, c:int8->d:int8)
%function funcinp366(a:int8, b:int8, c:int8->d:int8)
%function funcinp367(a:int8, b:int8, c:int8->d:int8)
%function funcinp368(a:int8, b:int8, c:int8->d:int8)
%function funcinp369(a:int8, b:int8, c:int8->d:int8)
%function funcinp370(a:int8, b:int8, c:int8->d:int8)
%function funcinp371(a:int8, b:int8, c:int8->d:int8)
%function funcinp372(a:int8, b:int8, c:int8->d:int8)
%function funcinp373(a:int8, b:int8, c:int8->d:int8)
%function funcinp374(a:int8, b:int8, c:int8->d:int8)
%function funcinp375(a:int8, b:int8, c:int8->d:int8)
%function funcinp376(a:int8, b:int8, c:int8->d:int8)
%function funcinp377(a:int8, b:int8, c:int8->d:int8)
%function funcinp378(a:int8, b:int8, c:int8->d:int8)
%function funcinp379(a:int8, b:int8, c:int8->d:int8)
%function funcinp380(a:int8, b:int8, c:int8->d:int8)
%function funcinp381(a:int8, b:int8, c:int8->d:int8)
%function funcinp382(a:int8, b:int8, c:int8->d:int8)
%function funcinp383(a:int8, b:int8, c:int8->d:int8)
%function funcinp384(a:int8, b:int8, c:int8->d:int8)
%function funcinp385(a:int8, b:int8, c:int8->d:int8)
%function funcinp386(a:int8, b:int8, c:int8->d:int8)
%function funcinp387(a:int8, b:int8, c:int8->d:int8)
%function funcinp388(a:int8, b:int8, c:int8->d:int8)
%function funcinp389(a:int8, b:int8, c:int8->d:int8)
%function funcinp390(a:int8, b:int8, c:int8->d:int8)
%function funcinp391(a:int8, b:int8, c:int8->d:int8)
%function funcinp392(a:int8, b:int8, c:int8->d:int8)
%function funcinp393(a:int8, b:int8, c:int8->d:int8)
%function funcinp394(a:int8, b:int8, c:int8->d:int8)
%function funcinp395(a:int8, b:int8, c:int8->d:int8)
%function funcinp396(a:int8, b:int8, c:int8->d:int8)
%function funcinp397(a:int8, b:int8, c:int8->d:int8)
%function funcinp398(a:int8, b:int8, c:int8->d:int8)
%function funcinp399(a:int8, b:int8, c:int8->d:int8)
%function funcinp400(a:int8, b:int8, c:int8->d:int8)
%function funcinp401(a:int8, b:int8, c:int8->d:int8)
%function funcinp402(a:int8, b:int8, c:int8->d:int8)
%function funcinp403(a:int8, b:int8, c:int8->d:int8)
%function funcinp404(a:int8, b:int8, c:int8->d:int8)
%function funcinp405(a:int8, b:int8, c:int8->d:int8)
%function funcinp406(a:int8, b:int8, c:int8->d:int8)
%function funcinp407(a:int8, b:int8, c:int8->d:int8)
%function funcinp408(a:int8, b:int8, c:int8->d:int8)
%function funcinp409(a:int8, b:int8, c:int8->d:int8)
%function funcinp410(a:int8, b:int8, c:int8->d:int8)
%function funcinp411(a:int8, b:int8, c:int8->d:int8)
%function funcinp412(a:int8, b:int8, c:int8->d:int8)
%function funcinp413(a:int8, b:int8, c:int8->d:int8)
%function funcinp414(a:int8, b:int8, c:int8->d:int8)
%function funcinp415(a:int8, b:int8, c:int8->d:int8)
%function funcinp416(a:int8, b:int8, c:int8->d:int8)
%function funcinp417(a:int8, b:int8, c:int8->d:int8)
%function funcinp418(a:int8, b:int8, c:int8->d:int8)
%function funcinp419(a:int8, b:int8, c:int8->d:int8)
%function funcinp420(a:int8, b:int8, c:int8->d:int8)
%function funcinp421(a:int8, b:int8, c:int8->d:int8)
%function funcinp422(a:int8, b:int8, c:int8->d:int8)
%function funcinp423(a:int8, b:int8, c:int8->d:int8)
%function funcinp424(a:int8, b:int8, c:int8->d:int8)
%function funcinp425(a:int8, b:int8, c:int8->d:int8)
%function funcinp426(a:int8, b:int8, c:int8->d:int8)
%function funcinp427(a:int8, b:int8, c:int8->d:int8)
%function funcinp428(a:int8, b:int8, c:int8->d:int8)
%function funcinp429(a:int8, b:int8, c:int8->d:int8)
%function funcinp430(a:int8, b:int8, c:int8->d:int8)
%function funcinp431(a:int8, b:int8, c:int8->d:int8)
%function funcinp432(a:int8, b:int8, c:int8->d:int8)
%function funcinp433(a:int8, b:int8, c:int8->d:int8)
%function funcinp434(a:int8, b:int8, c:int8->d:int8)
%function funcinp435(a:int8, b:int8, c:int8->d:int8)
%function funcinp436(a:int8, b:int8, c:int8->d:int8)
%function funcinp437(a:int8, b:int8, c:int8->d:int8)
%function funcinp438(a:int8, b:int8, c:int8->d:int8)
%function funcinp439(a:int8, b:int8, c:int8->d:int8)
%function funcinp440(a:int8, b:int8, c:int8->d:int8)
%function funcinp441(a:int8, b:int8, c:int8->d:int8)
%function funcinp442(a:int8, b:int8, c:int8->d:int8)
%function funcinp443(a:int8, b:int8, c:int8->d:int8)
%function funcinp444(a:int8, b:int8, c:int8->d:int8)
%function funcinp445(a:int8, b:int8, c:int8->d:int8)
%function funcinp446(a:int8, b:int8, c:int8->d:int8)
%function funcinp447(a:int8, b:int8, c:int8->d:int8)
%function funcinp448(a:int8, b:int8, c:int8->d:int8)
%function funcinp449(a:int8, b:int8, c:int8->d:int8)
%function funcinp450(a:int8, b:int8, c:int8->d:int8)
%function funcinp451(a:int8, b:int8, c:int8->d:int8)
%function funcinp452(a:int8, b:int8, c:int8->d:int8)
%function funcinp453(a:int8, b:int8, c:int8->d:int8)
%function funcinp454(a:int8, b:int8, c:int8->d:int8)
%function funcinp455(a:int8, b:int8, c:int8->d:int8)
%function funcinp456(a:int8, b:int8, c:int8->d:int8)
%function funcinp457(a:int8, b:int8, c:int8->d:int8)
%function funcinp458(a:int8, b:int8, c:int8->d:int8)
%function funcinp459(a:int8, b:int8, c:int8->d:int8)
%function funcinp460(a:int8, b:int8, c:int8->d:int8)
%function funcinp461(a:int8, b:int8, c:int8->d:int8)
%function funcinp462(a:int8, b:int8, c:int8->d:int8)
%function funcinp463(a:int8, b:int8, c:int8->d:int8)
%function funcinp464(a:int8, b:int8, c:int8->d:int8)
%function funcinp465(a:int8, b:int8, c:int8->d:int8)
%function funcinp466(a:int8, b:int8, c:int8->d:int8)
%function funcinp467(a:int8, b:int8, c:int8->d:int8)
%function funcinp468(a:int8, b:int8, c:int8->d:int8)
%function funcinp469(a:int8, b:int8, c:int8->d:int8)
%function funcinp470(a:int8, b:int8, c:int8->d:int8)
%function funcinp471(a:int8, b:int8, c:int8->d:int8)
%function funcinp472(a:int8, b:int8, c:int8->d:int8)
%function funcinp473(a:int8, b:int8, c:int8->d:int8)
%function funcinp474(a:int8, b:int8, c:int8->d:int8)
%function funcinp475(a:int8, b:int8, c:int8->d:int8)
%function funcinp476(a:int8, b:int8, c:int8->d:int8)
%function funcinp477(a:int8, b:int8, c:int8->d:int8)
%function funcinp478(a:int8, b:int8, c:int8->d:int8)
%function funcinp479(a:int8, b:int8, c:int8->d:int8)
%function funcinp480(a:int8, b:int8, c:int8->d:int8)
%function funcinp481(a:int8, b:int8, c:int8->d:int8)
%function funcinp482(a:int8, b:int8, c:int8->d:int8)
%function funcinp483(a:int8, b:int8, c:int8->d:int8)
%function funcinp484(a:int8, b:int8, c:int8->d:int8)
%function funcinp485(a:int8, b:int8, c:int8->d:int8)
%function funcinp486(a:int8, b:int8, c:int8->d:int8)
%function funcinp487(a:int8, b:int8, c:int8->d:int8)
%function funcinp488(a:int8, b:int8, c:int8->d:int8)
%function funcinp489(a:int8, b:int8, c:int8->d:int8)
%function funcinp490(a:int8, b:int8, c:int8->d:int8)
%function funcinp491(a:int8, b:int8, c:int8->d:int8)
%function funcinp492(a:int8, b:int8, c:int8->d:int8)
%function funcinp493(a:int8, b:int8, c:int8->d:int8)
%function funcinp494(a:int8, b:int8, c:int8->d:int8)
%function funcinp495(a:int8, b:int8, c:int8->d:int8)
%function funcinp496(a:int8, b:int8, c:int8->d:int8)
%function funcinp497(a:int8, b:int8, c:int8->d:int8)
%function funcinp498(a:int8, b:int8, c:int8->d:int8)
%function funcinp499(a:int8, b:int8, c:int8->d:int8)
%function funcinp500(a:int8, b:int8, c:int8->d:int8)
null(0):-inp1(a,b,c),funcinp1(a,b,c,d).
null(0):-inp2(a,b,c),funcinp2(a,b,c,d).
null(0):-inp3(a,b,c),funcinp3(a,b,c,d).
null(0):-inp4(a,b,c),funcinp4(a,b,c,d).
null(0):-inp5(a,b,c),funcinp5(a,b,c,d).
null(0):-inp6(a,b,c),funcinp6(a,b,c,d).
null(0):-inp7(a,b,c),funcinp7(a,b,c,d).
null(0):-inp8(a,b,c),funcinp8(a,b,c,d).
null(0):-inp9(a,b,c),funcinp9(a,b,c,d).
null(0):-inp10(a,b,c),funcinp10(a,b,c,d).
null(0):-inp11(a,b,c),funcinp11(a,b,c,d).
null(0):-inp12(a,b,c),funcinp12(a,b,c,d).
null(0):-inp13(a,b,c),funcinp13(a,b,c,d).
null(0):-inp14(a,b,c),funcinp14(a,b,c,d).
null(0):-inp15(a,b,c),funcinp15(a,b,c,d).
null(0):-inp16(a,b,c),funcinp16(a,b,c,d).
null(0):-inp17(a,b,c),funcinp17(a,b,c,d).
null(0):-inp18(a,b,c),funcinp18(a,b,c,d).
null(0):-inp19(a,b,c),funcinp19(a,b,c,d).
null(0):-inp20(a,b,c),funcinp20(a,b,c,d).
null(0):-inp21(a,b,c),funcinp21(a,b,c,d).
null(0):-inp22(a,b,c),funcinp22(a,b,c,d).
null(0):-inp23(a,b,c),funcinp23(a,b,c,d).
null(0):-inp24(a,b,c),funcinp24(a,b,c,d).
null(0):-inp25(a,b,c),funcinp25(a,b,c,d).
null(0):-inp26(a,b,c),funcinp26(a,b,c,d).
null(0):-inp27(a,b,c),funcinp27(a,b,c,d).
null(0):-inp28(a,b,c),funcinp28(a,b,c,d).
null(0):-inp29(a,b,c),funcinp29(a,b,c,d).
null(0):-inp30(a,b,c),funcinp30(a,b,c,d).
null(0):-inp31(a,b,c),funcinp31(a,b,c,d).
null(0):-inp32(a,b,c),funcinp32(a,b,c,d).
null(0):-inp33(a,b,c),funcinp33(a,b,c,d).
null(0):-inp34(a,b,c),funcinp34(a,b,c,d).
null(0):-inp35(a,b,c),funcinp35(a,b,c,d).
null(0):-inp36(a,b,c),funcinp36(a,b,c,d).
null(0):-inp37(a,b,c),funcinp37(a,b,c,d).
null(0):-inp38(a,b,c),funcinp38(a,b,c,d).
null(0):-inp39(a,b,c),funcinp39(a,b,c,d).
null(0):-inp40(a,b,c),funcinp40(a,b,c,d).
null(0):-inp41(a,b,c),funcinp41(a,b,c,d).
null(0):-inp42(a,b,c),funcinp42(a,b,c,d).
null(0):-inp43(a,b,c),funcinp43(a,b,c,d).
null(0):-inp44(a,b,c),funcinp44(a,b,c,d).
null(0):-inp45(a,b,c),funcinp45(a,b,c,d).
null(0):-inp46(a,b,c),funcinp46(a,b,c,d).
null(0):-inp47(a,b,c),funcinp47(a,b,c,d).
null(0):-inp48(a,b,c),funcinp48(a,b,c,d).
null(0):-inp49(a,b,c),funcinp49(a,b,c,d).
null(0):-inp50(a,b,c),funcinp50(a,b,c,d).
null(0):-inp51(a,b,c),funcinp51(a,b,c,d).
null(0):-inp52(a,b,c),funcinp52(a,b,c,d).
null(0):-inp53(a,b,c),funcinp53(a,b,c,d).
null(0):-inp54(a,b,c),funcinp54(a,b,c,d).
null(0):-inp55(a,b,c),funcinp55(a,b,c,d).
null(0):-inp56(a,b,c),funcinp56(a,b,c,d).
null(0):-inp57(a,b,c),funcinp57(a,b,c,d).
null(0):-inp58(a,b,c),funcinp58(a,b,c,d).
null(0):-inp59(a,b,c),funcinp59(a,b,c,d).
null(0):-inp60(a,b,c),funcinp60(a,b,c,d).
null(0):-inp61(a,b,c),funcinp61(a,b,c,d).
null(0):-inp62(a,b,c),funcinp62(a,b,c,d).
null(0):-inp63(a,b,c),funcinp63(a,b,c,d).
null(0):-inp64(a,b,c),funcinp64(a,b,c,d).
null(0):-inp65(a,b,c),funcinp65(a,b,c,d).
null(0):-inp66(a,b,c),funcinp66(a,b,c,d).
null(0):-inp67(a,b,c),funcinp67(a,b,c,d).
null(0):-inp68(a,b,c),funcinp68(a,b,c,d).
null(0):-inp69(a,b,c),funcinp69(a,b,c,d).
null(0):-inp70(a,b,c),funcinp70(a,b,c,d).
null(0):-inp71(a,b,c),funcinp71(a,b,c,d).
null(0):-inp72(a,b,c),funcinp72(a,b,c,d).
null(0):-inp73(a,b,c),funcinp73(a,b,c,d).
null(0):-inp74(a,b,c),funcinp74(a,b,c,d).
null(0):-inp75(a,b,c),funcinp75(a,b,c,d).
null(0):-inp76(a,b,c),funcinp76(a,b,c,d).
null(0):-inp77(a,b,c),funcinp77(a,b,c,d).
null(0):-inp78(a,b,c),funcinp78(a,b,c,d).
null(0):-inp79(a,b,c),funcinp79(a,b,c,d).
null(0):-inp80(a,b,c),funcinp80(a,b,c,d).
null(0):-inp81(a,b,c),funcinp81(a,b,c,d).
null(0):-inp82(a,b,c),funcinp82(a,b,c,d).
null(0):-inp83(a,b,c),funcinp83(a,b,c,d).
null(0):-inp84(a,b,c),funcinp84(a,b,c,d).
null(0):-inp85(a,b,c),funcinp85(a,b,c,d).
null(0):-inp86(a,b,c),funcinp86(a,b,c,d).
null(0):-inp87(a,b,c),funcinp87(a,b,c,d).
null(0):-inp88(a,b,c),funcinp88(a,b,c,d).
null(0):-inp89(a,b,c),funcinp89(a,b,c,d).
null(0):-inp90(a,b,c),funcinp90(a,b,c,d).
null(0):-inp91(a,b,c),funcinp91(a,b,c,d).
null(0):-inp92(a,b,c),funcinp92(a,b,c,d).
null(0):-inp93(a,b,c),funcinp93(a,b,c,d).
null(0):-inp94(a,b,c),funcinp94(a,b,c,d).
null(0):-inp95(a,b,c),funcinp95(a,b,c,d).
null(0):-inp96(a,b,c),funcinp96(a,b,c,d).
null(0):-inp97(a,b,c),funcinp97(a,b,c,d).
null(0):-inp98(a,b,c),funcinp98(a,b,c,d).
null(0):-inp99(a,b,c),funcinp99(a,b,c,d).
null(0):-inp100(a,b,c),funcinp100(a,b,c,d).
null(0):-inp101(a,b,c),funcinp101(a,b,c,d).
null(0):-inp102(a,b,c),funcinp102(a,b,c,d).
null(0):-inp103(a,b,c),funcinp103(a,b,c,d).
null(0):-inp104(a,b,c),funcinp104(a,b,c,d).
null(0):-inp105(a,b,c),funcinp105(a,b,c,d).
null(0):-inp106(a,b,c),funcinp106(a,b,c,d).
null(0):-inp107(a,b,c),funcinp107(a,b,c,d).
null(0):-inp108(a,b,c),funcinp108(a,b,c,d).
null(0):-inp109(a,b,c),funcinp109(a,b,c,d).
null(0):-inp110(a,b,c),funcinp110(a,b,c,d).
null(0):-inp111(a,b,c),funcinp111(a,b,c,d).
null(0):-inp112(a,b,c),funcinp112(a,b,c,d).
null(0):-inp113(a,b,c),funcinp113(a,b,c,d).
null(0):-inp114(a,b,c),funcinp114(a,b,c,d).
null(0):-inp115(a,b,c),funcinp115(a,b,c,d).
null(0):-inp116(a,b,c),funcinp116(a,b,c,d).
null(0):-inp117(a,b,c),funcinp117(a,b,c,d).
null(0):-inp118(a,b,c),funcinp118(a,b,c,d).
null(0):-inp119(a,b,c),funcinp119(a,b,c,d).
null(0):-inp120(a,b,c),funcinp120(a,b,c,d).
null(0):-inp121(a,b,c),funcinp121(a,b,c,d).
null(0):-inp122(a,b,c),funcinp122(a,b,c,d).
null(0):-inp123(a,b,c),funcinp123(a,b,c,d).
null(0):-inp124(a,b,c),funcinp124(a,b,c,d).
null(0):-inp125(a,b,c),funcinp125(a,b,c,d).
null(0):-inp126(a,b,c),funcinp126(a,b,c,d).
null(0):-inp127(a,b,c),funcinp127(a,b,c,d).
null(0):-inp128(a,b,c),funcinp128(a,b,c,d).
null(0):-inp129(a,b,c),funcinp129(a,b,c,d).
null(0):-inp130(a,b,c),funcinp130(a,b,c,d).
null(0):-inp131(a,b,c),funcinp131(a,b,c,d).
null(0):-inp132(a,b,c),funcinp132(a,b,c,d).
null(0):-inp133(a,b,c),funcinp133(a,b,c,d).
null(0):-inp134(a,b,c),funcinp134(a,b,c,d).
null(0):-inp135(a,b,c),funcinp135(a,b,c,d).
null(0):-inp136(a,b,c),funcinp136(a,b,c,d).
null(0):-inp137(a,b,c),funcinp137(a,b,c,d).
null(0):-inp138(a,b,c),funcinp138(a,b,c,d).
null(0):-inp139(a,b,c),funcinp139(a,b,c,d).
null(0):-inp140(a,b,c),funcinp140(a,b,c,d).
null(0):-inp141(a,b,c),funcinp141(a,b,c,d).
null(0):-inp142(a,b,c),funcinp142(a,b,c,d).
null(0):-inp143(a,b,c),funcinp143(a,b,c,d).
null(0):-inp144(a,b,c),funcinp144(a,b,c,d).
null(0):-inp145(a,b,c),funcinp145(a,b,c,d).
null(0):-inp146(a,b,c),funcinp146(a,b,c,d).
null(0):-inp147(a,b,c),funcinp147(a,b,c,d).
null(0):-inp148(a,b,c),funcinp148(a,b,c,d).
null(0):-inp149(a,b,c),funcinp149(a,b,c,d).
null(0):-inp150(a,b,c),funcinp150(a,b,c,d).
null(0):-inp151(a,b,c),funcinp151(a,b,c,d).
null(0):-inp152(a,b,c),funcinp152(a,b,c,d).
null(0):-inp153(a,b,c),funcinp153(a,b,c,d).
null(0):-inp154(a,b,c),funcinp154(a,b,c,d).
null(0):-inp155(a,b,c),funcinp155(a,b,c,d).
null(0):-inp156(a,b,c),funcinp156(a,b,c,d).
null(0):-inp157(a,b,c),funcinp157(a,b,c,d).
null(0):-inp158(a,b,c),funcinp158(a,b,c,d).
null(0):-inp159(a,b,c),funcinp159(a,b,c,d).
null(0):-inp160(a,b,c),funcinp160(a,b,c,d).
null(0):-inp161(a,b,c),funcinp161(a,b,c,d).
null(0):-inp162(a,b,c),funcinp162(a,b,c,d).
null(0):-inp163(a,b,c),funcinp163(a,b,c,d).
null(0):-inp164(a,b,c),funcinp164(a,b,c,d).
null(0):-inp165(a,b,c),funcinp165(a,b,c,d).
null(0):-inp166(a,b,c),funcinp166(a,b,c,d).
null(0):-inp167(a,b,c),funcinp167(a,b,c,d).
null(0):-inp168(a,b,c),funcinp168(a,b,c,d).
null(0):-inp169(a,b,c),funcinp169(a,b,c,d).
null(0):-inp170(a,b,c),funcinp170(a,b,c,d).
null(0):-inp171(a,b,c),funcinp171(a,b,c,d).
null(0):-inp172(a,b,c),funcinp172(a,b,c,d).
null(0):-inp173(a,b,c),funcinp173(a,b,c,d).
null(0):-inp174(a,b,c),funcinp174(a,b,c,d).
null(0):-inp175(a,b,c),funcinp175(a,b,c,d).
null(0):-inp176(a,b,c),funcinp176(a,b,c,d).
null(0):-inp177(a,b,c),funcinp177(a,b,c,d).
null(0):-inp178(a,b,c),funcinp178(a,b,c,d).
null(0):-inp179(a,b,c),funcinp179(a,b,c,d).
null(0):-inp180(a,b,c),funcinp180(a,b,c,d).
null(0):-inp181(a,b,c),funcinp181(a,b,c,d).
null(0):-inp182(a,b,c),funcinp182(a,b,c,d).
null(0):-inp183(a,b,c),funcinp183(a,b,c,d).
null(0):-inp184(a,b,c),funcinp184(a,b,c,d).
null(0):-inp185(a,b,c),funcinp185(a,b,c,d).
null(0):-inp186(a,b,c),funcinp186(a,b,c,d).
null(0):-inp187(a,b,c),funcinp187(a,b,c,d).
null(0):-inp188(a,b,c),funcinp188(a,b,c,d).
null(0):-inp189(a,b,c),funcinp189(a,b,c,d).
null(0):-inp190(a,b,c),funcinp190(a,b,c,d).
null(0):-inp191(a,b,c),funcinp191(a,b,c,d).
null(0):-inp192(a,b,c),funcinp192(a,b,c,d).
null(0):-inp193(a,b,c),funcinp193(a,b,c,d).
null(0):-inp194(a,b,c),funcinp194(a,b,c,d).
null(0):-inp195(a,b,c),funcinp195(a,b,c,d).
null(0):-inp196(a,b,c),funcinp196(a,b,c,d).
null(0):-inp197(a,b,c),funcinp197(a,b,c,d).
null(0):-inp198(a,b,c),funcinp198(a,b,c,d).
null(0):-inp199(a,b,c),funcinp199(a,b,c,d).
null(0):-inp200(a,b,c),funcinp200(a,b,c,d).
null(0):-inp201(a,b,c),funcinp201(a,b,c,d).
null(0):-inp202(a,b,c),funcinp202(a,b,c,d).
null(0):-inp203(a,b,c),funcinp203(a,b,c,d).
null(0):-inp204(a,b,c),funcinp204(a,b,c,d).
null(0):-inp205(a,b,c),funcinp205(a,b,c,d).
null(0):-inp206(a,b,c),funcinp206(a,b,c,d).
null(0):-inp207(a,b,c),funcinp207(a,b,c,d).
null(0):-inp208(a,b,c),funcinp208(a,b,c,d).
null(0):-inp209(a,b,c),funcinp209(a,b,c,d).
null(0):-inp210(a,b,c),funcinp210(a,b,c,d).
null(0):-inp211(a,b,c),funcinp211(a,b,c,d).
null(0):-inp212(a,b,c),funcinp212(a,b,c,d).
null(0):-inp213(a,b,c),funcinp213(a,b,c,d).
null(0):-inp214(a,b,c),funcinp214(a,b,c,d).
null(0):-inp215(a,b,c),funcinp215(a,b,c,d).
null(0):-inp216(a,b,c),funcinp216(a,b,c,d).
null(0):-inp217(a,b,c),funcinp217(a,b,c,d).
null(0):-inp218(a,b,c),funcinp218(a,b,c,d).
null(0):-inp219(a,b,c),funcinp219(a,b,c,d).
null(0):-inp220(a,b,c),funcinp220(a,b,c,d).
null(0):-inp221(a,b,c),funcinp221(a,b,c,d).
null(0):-inp222(a,b,c),funcinp222(a,b,c,d).
null(0):-inp223(a,b,c),funcinp223(a,b,c,d).
null(0):-inp224(a,b,c),funcinp224(a,b,c,d).
null(0):-inp225(a,b,c),funcinp225(a,b,c,d).
null(0):-inp226(a,b,c),funcinp226(a,b,c,d).
null(0):-inp227(a,b,c),funcinp227(a,b,c,d).
null(0):-inp228(a,b,c),funcinp228(a,b,c,d).
null(0):-inp229(a,b,c),funcinp229(a,b,c,d).
null(0):-inp230(a,b,c),funcinp230(a,b,c,d).
null(0):-inp231(a,b,c),funcinp231(a,b,c,d).
null(0):-inp232(a,b,c),funcinp232(a,b,c,d).
null(0):-inp233(a,b,c),funcinp233(a,b,c,d).
null(0):-inp234(a,b,c),funcinp234(a,b,c,d).
null(0):-inp235(a,b,c),funcinp235(a,b,c,d).
null(0):-inp236(a,b,c),funcinp236(a,b,c,d).
null(0):-inp237(a,b,c),funcinp237(a,b,c,d).
null(0):-inp238(a,b,c),funcinp238(a,b,c,d).
null(0):-inp239(a,b,c),funcinp239(a,b,c,d).
null(0):-inp240(a,b,c),funcinp240(a,b,c,d).
null(0):-inp241(a,b,c),funcinp241(a,b,c,d).
null(0):-inp242(a,b,c),funcinp242(a,b,c,d).
null(0):-inp243(a,b,c),funcinp243(a,b,c,d).
null(0):-inp244(a,b,c),funcinp244(a,b,c,d).
null(0):-inp245(a,b,c),funcinp245(a,b,c,d).
null(0):-inp246(a,b,c),funcinp246(a,b,c,d).
null(0):-inp247(a,b,c),funcinp247(a,b,c,d).
null(0):-inp248(a,b,c),funcinp248(a,b,c,d).
null(0):-inp249(a,b,c),funcinp249(a,b,c,d).
null(0):-inp250(a,b,c),funcinp250(a,b,c,d).
null(0):-inp251(a,b,c),funcinp251(a,b,c,d).
null(0):-inp252(a,b,c),funcinp252(a,b,c,d).
null(0):-inp253(a,b,c),funcinp253(a,b,c,d).
null(0):-inp254(a,b,c),funcinp254(a,b,c,d).
null(0):-inp255(a,b,c),funcinp255(a,b,c,d).
null(0):-inp256(a,b,c),funcinp256(a,b,c,d).
null(0):-inp257(a,b,c),funcinp257(a,b,c,d).
null(0):-inp258(a,b,c),funcinp258(a,b,c,d).
null(0):-inp259(a,b,c),funcinp259(a,b,c,d).
null(0):-inp260(a,b,c),funcinp260(a,b,c,d).
null(0):-inp261(a,b,c),funcinp261(a,b,c,d).
null(0):-inp262(a,b,c),funcinp262(a,b,c,d).
null(0):-inp263(a,b,c),funcinp263(a,b,c,d).
null(0):-inp264(a,b,c),funcinp264(a,b,c,d).
null(0):-inp265(a,b,c),funcinp265(a,b,c,d).
null(0):-inp266(a,b,c),funcinp266(a,b,c,d).
null(0):-inp267(a,b,c),funcinp267(a,b,c,d).
null(0):-inp268(a,b,c),funcinp268(a,b,c,d).
null(0):-inp269(a,b,c),funcinp269(a,b,c,d).
null(0):-inp270(a,b,c),funcinp270(a,b,c,d).
null(0):-inp271(a,b,c),funcinp271(a,b,c,d).
null(0):-inp272(a,b,c),funcinp272(a,b,c,d).
null(0):-inp273(a,b,c),funcinp273(a,b,c,d).
null(0):-inp274(a,b,c),funcinp274(a,b,c,d).
null(0):-inp275(a,b,c),funcinp275(a,b,c,d).
null(0):-inp276(a,b,c),funcinp276(a,b,c,d).
null(0):-inp277(a,b,c),funcinp277(a,b,c,d).
null(0):-inp278(a,b,c),funcinp278(a,b,c,d).
null(0):-inp279(a,b,c),funcinp279(a,b,c,d).
null(0):-inp280(a,b,c),funcinp280(a,b,c,d).
null(0):-inp281(a,b,c),funcinp281(a,b,c,d).
null(0):-inp282(a,b,c),funcinp282(a,b,c,d).
null(0):-inp283(a,b,c),funcinp283(a,b,c,d).
null(0):-inp284(a,b,c),funcinp284(a,b,c,d).
null(0):-inp285(a,b,c),funcinp285(a,b,c,d).
null(0):-inp286(a,b,c),funcinp286(a,b,c,d).
null(0):-inp287(a,b,c),funcinp287(a,b,c,d).
null(0):-inp288(a,b,c),funcinp288(a,b,c,d).
null(0):-inp289(a,b,c),funcinp289(a,b,c,d).
null(0):-inp290(a,b,c),funcinp290(a,b,c,d).
null(0):-inp291(a,b,c),funcinp291(a,b,c,d).
null(0):-inp292(a,b,c),funcinp292(a,b,c,d).
null(0):-inp293(a,b,c),funcinp293(a,b,c,d).
null(0):-inp294(a,b,c),funcinp294(a,b,c,d).
null(0):-inp295(a,b,c),funcinp295(a,b,c,d).
null(0):-inp296(a,b,c),funcinp296(a,b,c,d).
null(0):-inp297(a,b,c),funcinp297(a,b,c,d).
null(0):-inp298(a,b,c),funcinp298(a,b,c,d).
null(0):-inp299(a,b,c),funcinp299(a,b,c,d).
null(0):-inp300(a,b,c),funcinp300(a,b,c,d).
null(0):-inp301(a,b,c),funcinp301(a,b,c,d).
null(0):-inp302(a,b,c),funcinp302(a,b,c,d).
null(0):-inp303(a,b,c),funcinp303(a,b,c,d).
null(0):-inp304(a,b,c),funcinp304(a,b,c,d).
null(0):-inp305(a,b,c),funcinp305(a,b,c,d).
null(0):-inp306(a,b,c),funcinp306(a,b,c,d).
null(0):-inp307(a,b,c),funcinp307(a,b,c,d).
null(0):-inp308(a,b,c),funcinp308(a,b,c,d).
null(0):-inp309(a,b,c),funcinp309(a,b,c,d).
null(0):-inp310(a,b,c),funcinp310(a,b,c,d).
null(0):-inp311(a,b,c),funcinp311(a,b,c,d).
null(0):-inp312(a,b,c),funcinp312(a,b,c,d).
null(0):-inp313(a,b,c),funcinp313(a,b,c,d).
null(0):-inp314(a,b,c),funcinp314(a,b,c,d).
null(0):-inp315(a,b,c),funcinp315(a,b,c,d).
null(0):-inp316(a,b,c),funcinp316(a,b,c,d).
null(0):-inp317(a,b,c),funcinp317(a,b,c,d).
null(0):-inp318(a,b,c),funcinp318(a,b,c,d).
null(0):-inp319(a,b,c),funcinp319(a,b,c,d).
null(0):-inp320(a,b,c),funcinp320(a,b,c,d).
null(0):-inp321(a,b,c),funcinp321(a,b,c,d).
null(0):-inp322(a,b,c),funcinp322(a,b,c,d).
null(0):-inp323(a,b,c),funcinp323(a,b,c,d).
null(0):-inp324(a,b,c),funcinp324(a,b,c,d).
null(0):-inp325(a,b,c),funcinp325(a,b,c,d).
null(0):-inp326(a,b,c),funcinp326(a,b,c,d).
null(0):-inp327(a,b,c),funcinp327(a,b,c,d).
null(0):-inp328(a,b,c),funcinp328(a,b,c,d).
null(0):-inp329(a,b,c),funcinp329(a,b,c,d).
null(0):-inp330(a,b,c),funcinp330(a,b,c,d).
null(0):-inp331(a,b,c),funcinp331(a,b,c,d).
null(0):-inp332(a,b,c),funcinp332(a,b,c,d).
null(0):-inp333(a,b,c),funcinp333(a,b,c,d).
null(0):-inp334(a,b,c),funcinp334(a,b,c,d).
null(0):-inp335(a,b,c),funcinp335(a,b,c,d).
null(0):-inp336(a,b,c),funcinp336(a,b,c,d).
null(0):-inp337(a,b,c),funcinp337(a,b,c,d).
null(0):-inp338(a,b,c),funcinp338(a,b,c,d).
null(0):-inp339(a,b,c),funcinp339(a,b,c,d).
null(0):-inp340(a,b,c),funcinp340(a,b,c,d).
null(0):-inp341(a,b,c),funcinp341(a,b,c,d).
null(0):-inp342(a,b,c),funcinp342(a,b,c,d).
null(0):-inp343(a,b,c),funcinp343(a,b,c,d).
null(0):-inp344(a,b,c),funcinp344(a,b,c,d).
null(0):-inp345(a,b,c),funcinp345(a,b,c,d).
null(0):-inp346(a,b,c),funcinp346(a,b,c,d).
null(0):-inp347(a,b,c),funcinp347(a,b,c,d).
null(0):-inp348(a,b,c),funcinp348(a,b,c,d).
null(0):-inp349(a,b,c),funcinp349(a,b,c,d).
null(0):-inp350(a,b,c),funcinp350(a,b,c,d).
null(0):-inp351(a,b,c),funcinp351(a,b,c,d).
null(0):-inp352(a,b,c),funcinp352(a,b,c,d).
null(0):-inp353(a,b,c),funcinp353(a,b,c,d).
null(0):-inp354(a,b,c),funcinp354(a,b,c,d).
null(0):-inp355(a,b,c),funcinp355(a,b,c,d).
null(0):-inp356(a,b,c),funcinp356(a,b,c,d).
null(0):-inp357(a,b,c),funcinp357(a,b,c,d).
null(0):-inp358(a,b,c),funcinp358(a,b,c,d).
null(0):-inp359(a,b,c),funcinp359(a,b,c,d).
null(0):-inp360(a,b,c),funcinp360(a,b,c,d).
null(0):-inp361(a,b,c),funcinp361(a,b,c,d).
null(0):-inp362(a,b,c),funcinp362(a,b,c,d).
null(0):-inp363(a,b,c),funcinp363(a,b,c,d).
null(0):-inp364(a,b,c),funcinp364(a,b,c,d).
null(0):-inp365(a,b,c),funcinp365(a,b,c,d).
null(0):-inp366(a,b,c),funcinp366(a,b,c,d).
null(0):-inp367(a,b,c),funcinp367(a,b,c,d).
null(0):-inp368(a,b,c),funcinp368(a,b,c,d).
null(0):-inp369(a,b,c),funcinp369(a,b,c,d).
null(0):-inp370(a,b,c),funcinp370(a,b,c,d).
null(0):-inp371(a,b,c),funcinp371(a,b,c,d).
null(0):-inp372(a,b,c),funcinp372(a,b,c,d).
null(0):-inp373(a,b,c),funcinp373(a,b,c,d).
null(0):-inp374(a,b,c),funcinp374(a,b,c,d).
null(0):-inp375(a,b,c),funcinp375(a,b,c,d).
null(0):-inp376(a,b,c),funcinp376(a,b,c,d).
null(0):-inp377(a,b,c),funcinp377(a,b,c,d).
null(0):-inp378(a,b,c),funcinp378(a,b,c,d).
null(0):-inp379(a,b,c),funcinp379(a,b,c,d).
null(0):-inp380(a,b,c),funcinp380(a,b,c,d).
null(0):-inp381(a,b,c),funcinp381(a,b,c,d).
null(0):-inp382(a,b,c),funcinp382(a,b,c,d).
null(0):-inp383(a,b,c),funcinp383(a,b,c,d).
null(0):-inp384(a,b,c),funcinp384(a,b,c,d).
null(0):-inp385(a,b,c),funcinp385(a,b,c,d).
null(0):-inp386(a,b,c),funcinp386(a,b,c,d).
null(0):-inp387(a,b,c),funcinp387(a,b,c,d).
null(0):-inp388(a,b,c),funcinp388(a,b,c,d).
null(0):-inp389(a,b,c),funcinp389(a,b,c,d).
null(0):-inp390(a,b,c),funcinp390(a,b,c,d).
null(0):-inp391(a,b,c),funcinp391(a,b,c,d).
null(0):-inp392(a,b,c),funcinp392(a,b,c,d).
null(0):-inp393(a,b,c),funcinp393(a,b,c,d).
null(0):-inp394(a,b,c),funcinp394(a,b,c,d).
null(0):-inp395(a,b,c),funcinp395(a,b,c,d).
null(0):-inp396(a,b,c),funcinp396(a,b,c,d).
null(0):-inp397(a,b,c),funcinp397(a,b,c,d).
null(0):-inp398(a,b,c),funcinp398(a,b,c,d).
null(0):-inp399(a,b,c),funcinp399(a,b,c,d).
null(0):-inp400(a,b,c),funcinp400(a,b,c,d).
null(0):-inp401(a,b,c),funcinp401(a,b,c,d).
null(0):-inp402(a,b,c),funcinp402(a,b,c,d).
null(0):-inp403(a,b,c),funcinp403(a,b,c,d).
null(0):-inp404(a,b,c),funcinp404(a,b,c,d).
null(0):-inp405(a,b,c),funcinp405(a,b,c,d).
null(0):-inp406(a,b,c),funcinp406(a,b,c,d).
null(0):-inp407(a,b,c),funcinp407(a,b,c,d).
null(0):-inp408(a,b,c),funcinp408(a,b,c,d).
null(0):-inp409(a,b,c),funcinp409(a,b,c,d).
null(0):-inp410(a,b,c),funcinp410(a,b,c,d).
null(0):-inp411(a,b,c),funcinp411(a,b,c,d).
null(0):-inp412(a,b,c),funcinp412(a,b,c,d).
null(0):-inp413(a,b,c),funcinp413(a,b,c,d).
null(0):-inp414(a,b,c),funcinp414(a,b,c,d).
null(0):-inp415(a,b,c),funcinp415(a,b,c,d).
null(0):-inp416(a,b,c),funcinp416(a,b,c,d).
null(0):-inp417(a,b,c),funcinp417(a,b,c,d).
null(0):-inp418(a,b,c),funcinp418(a,b,c,d).
null(0):-inp419(a,b,c),funcinp419(a,b,c,d).
null(0):-inp420(a,b,c),funcinp420(a,b,c,d).
null(0):-inp421(a,b,c),funcinp421(a,b,c,d).
null(0):-inp422(a,b,c),funcinp422(a,b,c,d).
null(0):-inp423(a,b,c),funcinp423(a,b,c,d).
null(0):-inp424(a,b,c),funcinp424(a,b,c,d).
null(0):-inp425(a,b,c),funcinp425(a,b,c,d).
null(0):-inp426(a,b,c),funcinp426(a,b,c,d).
null(0):-inp427(a,b,c),funcinp427(a,b,c,d).
null(0):-inp428(a,b,c),funcinp428(a,b,c,d).
null(0):-inp429(a,b,c),funcinp429(a,b,c,d).
null(0):-inp430(a,b,c),funcinp430(a,b,c,d).
null(0):-inp431(a,b,c),funcinp431(a,b,c,d).
null(0):-inp432(a,b,c),funcinp432(a,b,c,d).
null(0):-inp433(a,b,c),funcinp433(a,b,c,d).
null(0):-inp434(a,b,c),funcinp434(a,b,c,d).
null(0):-inp435(a,b,c),funcinp435(a,b,c,d).
null(0):-inp436(a,b,c),funcinp436(a,b,c,d).
null(0):-inp437(a,b,c),funcinp437(a,b,c,d).
null(0):-inp438(a,b,c),funcinp438(a,b,c,d).
null(0):-inp439(a,b,c),funcinp439(a,b,c,d).
null(0):-inp440(a,b,c),funcinp440(a,b,c,d).
null(0):-inp441(a,b,c),funcinp441(a,b,c,d).
null(0):-inp442(a,b,c),funcinp442(a,b,c,d).
null(0):-inp443(a,b,c),funcinp443(a,b,c,d).
null(0):-inp444(a,b,c),funcinp444(a,b,c,d).
null(0):-inp445(a,b,c),funcinp445(a,b,c,d).
null(0):-inp446(a,b,c),funcinp446(a,b,c,d).
null(0):-inp447(a,b,c),funcinp447(a,b,c,d).
null(0):-inp448(a,b,c),funcinp448(a,b,c,d).
null(0):-inp449(a,b,c),funcinp449(a,b,c,d).
null(0):-inp450(a,b,c),funcinp450(a,b,c,d).
null(0):-inp451(a,b,c),funcinp451(a,b,c,d).
null(0):-inp452(a,b,c),funcinp452(a,b,c,d).
null(0):-inp453(a,b,c),funcinp453(a,b,c,d).
null(0):-inp454(a,b,c),funcinp454(a,b,c,d).
null(0):-inp455(a,b,c),funcinp455(a,b,c,d).
null(0):-inp456(a,b,c),funcinp456(a,b,c,d).
null(0):-inp457(a,b,c),funcinp457(a,b,c,d).
null(0):-inp458(a,b,c),funcinp458(a,b,c,d).
null(0):-inp459(a,b,c),funcinp459(a,b,c,d).
null(0):-inp460(a,b,c),funcinp460(a,b,c,d).
null(0):-inp461(a,b,c),funcinp461(a,b,c,d).
null(0):-inp462(a,b,c),funcinp462(a,b,c,d).
null(0):-inp463(a,b,c),funcinp463(a,b,c,d).
null(0):-inp464(a,b,c),funcinp464(a,b,c,d).
null(0):-inp465(a,b,c),funcinp465(a,b,c,d).
null(0):-inp466(a,b,c),funcinp466(a,b,c,d).
null(0):-inp467(a,b,c),funcinp467(a,b,c,d).
null(0):-inp468(a,b,c),funcinp468(a,b,c,d).
null(0):-inp469(a,b,c),funcinp469(a,b,c,d).
null(0):-inp470(a,b,c),funcinp470(a,b,c,d).
null(0):-inp471(a,b,c),funcinp471(a,b,c,d).
null(0):-inp472(a,b,c),funcinp472(a,b,c,d).
null(0):-inp473(a,b,c),funcinp473(a,b,c,d).
null(0):-inp474(a,b,c),funcinp474(a,b,c,d).
null(0):-inp475(a,b,c),funcinp475(a,b,c,d).
null(0):-inp476(a,b,c),funcinp476(a,b,c,d).
null(0):-inp477(a,b,c),funcinp477(a,b,c,d).
null(0):-inp478(a,b,c),funcinp478(a,b,c,d).
null(0):-inp479(a,b,c),funcinp479(a,b,c,d).
null(0):-inp480(a,b,c),funcinp480(a,b,c,d).
null(0):-inp481(a,b,c),funcinp481(a,b,c,d).
null(0):-inp482(a,b,c),funcinp482(a,b,c,d).
null(0):-inp483(a,b,c),funcinp483(a,b,c,d).
null(0):-inp484(a,b,c),funcinp484(a,b,c,d).
null(0):-inp485(a,b,c),funcinp485(a,b,c,d).
null(0):-inp486(a,b,c),funcinp486(a,b,c,d).
null(0):-inp487(a,b,c),funcinp487(a,b,c,d).
null(0):-inp488(a,b,c),funcinp488(a,b,c,d).
null(0):-inp489(a,b,c),funcinp489(a,b,c,d).
null(0):-inp490(a,b,c),funcinp490(a,b,c,d).
null(0):-inp491(a,b,c),funcinp491(a,b,c,d).
null(0):-inp492(a,b,c),funcinp492(a,b,c,d).
null(0):-inp493(a,b,c),funcinp493(a,b,c,d).
null(0):-inp494(a,b,c),funcinp494(a,b,c,d).
null(0):-inp495(a,b,c),funcinp495(a,b,c,d).
null(0):-inp496(a,b,c),funcinp496(a,b,c,d).
null(0):-inp497(a,b,c),funcinp497(a,b,c,d).
null(0):-inp498(a,b,c),funcinp498(a,b,c,d).
null(0):-inp499(a,b,c),funcinp499(a,b,c,d).
null(0):-mid1(a,b,c).
null(0):-mid2(a,b,c).
null(0):-mid3(a,b,c).
null(0):-mid4(a,b,c).
null(0):-mid5(a,b,c).
null(0):-mid6(a,b,c).
null(0):-mid7(a,b,c).
null(0):-mid8(a,b,c).
null(0):-mid9(a,b,c).
null(0):-mid10(a,b,c).
null(0):-mid11(a,b,c).
null(0):-mid12(a,b,c).
null(0):-mid13(a,b,c).
null(0):-mid14(a,b,c).
null(0):-mid15(a,b,c).
null(0):-mid16(a,b,c).
null(0):-mid17(a,b,c).
null(0):-mid18(a,b,c).
null(0):-mid19(a,b,c).
null(0):-mid20(a,b,c).
null(0):-mid21(a,b,c).
null(0):-mid22(a,b,c).
null(0):-mid23(a,b,c).
null(0):-mid24(a,b,c).
null(0):-mid25(a,b,c).
null(0):-mid26(a,b,c).
null(0):-mid27(a,b,c).
null(0):-mid28(a,b,c).
null(0):-mid29(a,b,c).
null(0):-mid30(a,b,c).
null(0):-mid31(a,b,c).
null(0):-mid32(a,b,c).
null(0):-mid33(a,b,c).
null(0):-mid34(a,b,c).
null(0):-mid35(a,b,c).
null(0):-mid36(a,b,c).
null(0):-mid37(a,b,c).
null(0):-mid38(a,b,c).
null(0):-mid39(a,b,c).
null(0):-mid40(a,b,c).
null(0):-mid41(a,b,c).
null(0):-mid42(a,b,c).
null(0):-mid43(a,b,c).
null(0):-mid44(a,b,c).
null(0):-mid45(a,b,c).
null(0):-mid46(a,b,c).
null(0):-mid47(a,b,c).
null(0):-mid48(a,b,c).
null(0):-mid49(a,b,c).
null(0):-mid50(a,b,c).
null(0):-mid51(a,b,c).
null(0):-mid52(a,b,c).
null(0):-mid53(a,b,c).
null(0):-mid54(a,b,c).
null(0):-mid55(a,b,c).
null(0):-mid56(a,b,c).
null(0):-mid57(a,b,c).
null(0):-mid58(a,b,c).
null(0):-mid59(a,b,c).
null(0):-mid60(a,b,c).
null(0):-mid61(a,b,c).
null(0):-mid62(a,b,c).
null(0):-mid63(a,b,c).
null(0):-mid64(a,b,c).
null(0):-mid65(a,b,c).
null(0):-mid66(a,b,c).
null(0):-mid67(a,b,c).
null(0):-mid68(a,b,c).
null(0):-mid69(a,b,c).
null(0):-mid70(a,b,c).
null(0):-mid71(a,b,c).
null(0):-mid72(a,b,c).
null(0):-mid73(a,b,c).
null(0):-mid74(a,b,c).
null(0):-mid75(a,b,c).
null(0):-mid76(a,b,c).
null(0):-mid77(a,b,c).
null(0):-mid78(a,b,c).
null(0):-mid79(a,b,c).
null(0):-mid80(a,b,c).
null(0):-mid81(a,b,c).
null(0):-mid82(a,b,c).
null(0):-mid83(a,b,c).
null(0):-mid84(a,b,c).
null(0):-mid85(a,b,c).
null(0):-mid86(a,b,c).
null(0):-mid87(a,b,c).
null(0):-mid88(a,b,c).
null(0):-mid89(a,b,c).
null(0):-mid90(a,b,c).
null(0):-mid91(a,b,c).
null(0):-mid92(a,b,c).
null(0):-mid93(a,b,c).
null(0):-mid94(a,b,c).
null(0):-mid95(a,b,c).
null(0):-mid96(a,b,c).
null(0):-mid97(a,b,c).
null(0):-mid98(a,b,c).
null(0):-mid99(a,b,c).
null(0):-mid100(a,b,c).
null(0):-mid101(a,b,c).
null(0):-mid102(a,b,c).
null(0):-mid103(a,b,c).
null(0):-mid104(a,b,c).
null(0):-mid105(a,b,c).
null(0):-mid106(a,b,c).
null(0):-mid107(a,b,c).
null(0):-mid108(a,b,c).
null(0):-mid109(a,b,c).
null(0):-mid110(a,b,c).
null(0):-mid111(a,b,c).
null(0):-mid112(a,b,c).
null(0):-mid113(a,b,c).
null(0):-mid114(a,b,c).
null(0):-mid115(a,b,c).
null(0):-mid116(a,b,c).
null(0):-mid117(a,b,c).
null(0):-mid118(a,b,c).
null(0):-mid119(a,b,c).
null(0):-mid120(a,b,c).
null(0):-mid121(a,b,c).
null(0):-mid122(a,b,c).
null(0):-mid123(a,b,c).
null(0):-mid124(a,b,c).
null(0):-mid125(a,b,c).
null(0):-mid126(a,b,c).
null(0):-mid127(a,b,c).
null(0):-mid128(a,b,c).
null(0):-mid129(a,b,c).
null(0):-mid130(a,b,c).
null(0):-mid131(a,b,c).
null(0):-mid132(a,b,c).
null(0):-mid133(a,b,c).
null(0):-mid134(a,b,c).
null(0):-mid135(a,b,c).
null(0):-mid136(a,b,c).
null(0):-mid137(a,b,c).
null(0):-mid138(a,b,c).
null(0):-mid139(a,b,c).
null(0):-mid140(a,b,c).
null(0):-mid141(a,b,c).
null(0):-mid142(a,b,c).
null(0):-mid143(a,b,c).
null(0):-mid144(a,b,c).
null(0):-mid145(a,b,c).
null(0):-mid146(a,b,c).
null(0):-mid147(a,b,c).
null(0):-mid148(a,b,c).
null(0):-mid149(a,b,c).
null(0):-mid150(a,b,c).
null(0):-mid151(a,b,c).
null(0):-mid152(a,b,c).
null(0):-mid153(a,b,c).
null(0):-mid154(a,b,c).
null(0):-mid155(a,b,c).
null(0):-mid156(a,b,c).
null(0):-mid157(a,b,c).
null(0):-mid158(a,b,c).
null(0):-mid159(a,b,c).
null(0):-mid160(a,b,c).
null(0):-mid161(a,b,c).
null(0):-mid162(a,b,c).
null(0):-mid163(a,b,c).
null(0):-mid164(a,b,c).
null(0):-mid165(a,b,c).
null(0):-mid166(a,b,c).
null(0):-mid167(a,b,c).
null(0):-mid168(a,b,c).
null(0):-mid169(a,b,c).
null(0):-mid170(a,b,c).
null(0):-mid171(a,b,c).
null(0):-mid172(a,b,c).
null(0):-mid173(a,b,c).
null(0):-mid174(a,b,c).
null(0):-mid175(a,b,c).
null(0):-mid176(a,b,c).
null(0):-mid177(a,b,c).
null(0):-mid178(a,b,c).
null(0):-mid179(a,b,c).
null(0):-mid180(a,b,c).
null(0):-mid181(a,b,c).
null(0):-mid182(a,b,c).
null(0):-mid183(a,b,c).
null(0):-mid184(a,b,c).
null(0):-mid185(a,b,c).
null(0):-mid186(a,b,c).
null(0):-mid187(a,b,c).
null(0):-mid188(a,b,c).
null(0):-mid189(a,b,c).
null(0):-mid190(a,b,c).
null(0):-mid191(a,b,c).
null(0):-mid192(a,b,c).
null(0):-mid193(a,b,c).
null(0):-mid194(a,b,c).
null(0):-mid195(a,b,c).
null(0):-mid196(a,b,c).
null(0):-mid197(a,b,c).
null(0):-mid198(a,b,c).
null(0):-mid199(a,b,c).
null(0):-mid200(a,b,c).
null(0):-mid201(a,b,c).
null(0):-mid202(a,b,c).
null(0):-mid203(a,b,c).
null(0):-mid204(a,b,c).
null(0):-mid205(a,b,c).
null(0):-mid206(a,b,c).
null(0):-mid207(a,b,c).
null(0):-mid208(a,b,c).
null(0):-mid209(a,b,c).
null(0):-mid210(a,b,c).
null(0):-mid211(a,b,c).
null(0):-mid212(a,b,c).
null(0):-mid213(a,b,c).
null(0):-mid214(a,b,c).
null(0):-mid215(a,b,c).
null(0):-mid216(a,b,c).
null(0):-mid217(a,b,c).
null(0):-mid218(a,b,c).
null(0):-mid219(a,b,c).
null(0):-mid220(a,b,c).
null(0):-mid221(a,b,c).
null(0):-mid222(a,b,c).
null(0):-mid223(a,b,c).
null(0):-mid224(a,b,c).
null(0):-mid225(a,b,c).
null(0):-mid226(a,b,c).
null(0):-mid227(a,b,c).
null(0):-mid228(a,b,c).
null(0):-mid229(a,b,c).
null(0):-mid230(a,b,c).
null(0):-mid231(a,b,c).
null(0):-mid232(a,b,c).
null(0):-mid233(a,b,c).
null(0):-mid234(a,b,c).
null(0):-mid235(a,b,c).
null(0):-mid236(a,b,c).
null(0):-mid237(a,b,c).
null(0):-mid238(a,b,c).
null(0):-mid239(a,b,c).
null(0):-mid240(a,b,c).
null(0):-mid241(a,b,c).
null(0):-mid242(a,b,c).
null(0):-mid243(a,b,c).
null(0):-mid244(a,b,c).
null(0):-mid245(a,b,c).
null(0):-mid246(a,b,c).
null(0):-mid247(a,b,c).
null(0):-mid248(a,b,c).
null(0):-mid249(a,b,c).
null(0):-mid250(a,b,c).
%function funcmid1(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid2(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid3(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid4(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid5(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid6(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid7(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid8(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid9(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid10(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid11(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid12(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid13(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid14(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid15(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid16(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid17(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid18(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid19(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid20(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid21(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid22(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid23(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid24(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid25(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid26(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid27(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid28(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid29(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid30(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid31(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid32(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid33(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid34(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid35(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid36(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid37(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid38(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid39(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid40(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid41(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid42(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid43(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid44(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid45(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid46(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid47(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid48(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid49(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid50(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid51(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid52(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid53(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid54(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid55(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid56(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid57(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid58(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid59(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid60(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid61(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid62(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid63(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid64(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid65(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid66(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid67(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid68(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid69(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid70(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid71(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid72(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid73(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid74(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid75(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid76(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid77(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid78(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid79(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid80(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid81(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid82(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid83(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid84(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid85(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid86(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid87(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid88(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid89(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid90(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid91(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid92(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid93(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid94(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid95(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid96(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid97(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid98(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid99(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid100(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid101(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid102(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid103(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid104(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid105(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid106(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid107(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid108(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid109(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid110(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid111(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid112(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid113(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid114(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid115(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid116(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid117(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid118(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid119(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid120(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid121(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid122(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid123(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid124(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid125(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid126(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid127(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid128(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid129(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid130(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid131(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid132(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid133(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid134(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid135(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid136(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid137(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid138(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid139(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid140(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid141(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid142(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid143(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid144(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid145(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid146(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid147(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid148(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid149(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid150(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid151(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid152(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid153(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid154(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid155(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid156(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid157(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid158(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid159(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid160(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid161(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid162(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid163(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid164(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid165(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid166(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid167(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid168(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid169(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid170(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid171(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid172(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid173(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid174(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid175(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid176(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid177(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid178(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid179(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid180(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid181(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid182(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid183(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid184(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid185(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid186(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid187(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid188(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid189(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid190(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid191(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid192(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid193(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid194(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid195(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid196(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid197(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid198(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid199(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid200(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid201(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid202(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid203(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid204(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid205(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid206(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid207(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid208(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid209(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid210(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid211(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid212(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid213(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid214(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid215(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid216(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid217(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid218(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid219(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid220(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid221(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid222(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid223(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid224(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid225(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid226(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid227(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid228(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid229(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid230(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid231(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid232(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid233(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid234(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid235(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid236(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid237(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid238(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid239(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid240(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid241(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid242(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid243(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid244(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid245(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid246(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid247(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid248(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid249(a:int8, b:int8, c:int8 -> d:int8)
%function funcmid250(a:int8, b:int8, c:int8 -> d:int8)
null(0):-mid1(a,b,c),funcmid1(a,b,c,d).
null(0):-mid2(a,b,c),funcmid2(a,b,c,d).
null(0):-mid3(a,b,c),funcmid3(a,b,c,d).
null(0):-mid4(a,b,c),funcmid4(a,b,c,d).
null(0):-mid5(a,b,c),funcmid5(a,b,c,d).
null(0):-mid6(a,b,c),funcmid6(a,b,c,d).
null(0):-mid7(a,b,c),funcmid7(a,b,c,d).
null(0):-mid8(a,b,c),funcmid8(a,b,c,d).
null(0):-mid9(a,b,c),funcmid9(a,b,c,d).
null(0):-mid10(a,b,c),funcmid10(a,b,c,d).
null(0):-mid11(a,b,c),funcmid11(a,b,c,d).
null(0):-mid12(a,b,c),funcmid12(a,b,c,d).
null(0):-mid13(a,b,c),funcmid13(a,b,c,d).
null(0):-mid14(a,b,c),funcmid14(a,b,c,d).
null(0):-mid15(a,b,c),funcmid15(a,b,c,d).
null(0):-mid16(a,b,c),funcmid16(a,b,c,d).
null(0):-mid17(a,b,c),funcmid17(a,b,c,d).
null(0):-mid18(a,b,c),funcmid18(a,b,c,d).
null(0):-mid19(a,b,c),funcmid19(a,b,c,d).
null(0):-mid20(a,b,c),funcmid20(a,b,c,d).
null(0):-mid21(a,b,c),funcmid21(a,b,c,d).
null(0):-mid22(a,b,c),funcmid22(a,b,c,d).
null(0):-mid23(a,b,c),funcmid23(a,b,c,d).
null(0):-mid24(a,b,c),funcmid24(a,b,c,d).
null(0):-mid25(a,b,c),funcmid25(a,b,c,d).
null(0):-mid26(a,b,c),funcmid26(a,b,c,d).
null(0):-mid27(a,b,c),funcmid27(a,b,c,d).
null(0):-mid28(a,b,c),funcmid28(a,b,c,d).
null(0):-mid29(a,b,c),funcmid29(a,b,c,d).
null(0):-mid30(a,b,c),funcmid30(a,b,c,d).
null(0):-mid31(a,b,c),funcmid31(a,b,c,d).
null(0):-mid32(a,b,c),funcmid32(a,b,c,d).
null(0):-mid33(a,b,c),funcmid33(a,b,c,d).
null(0):-mid34(a,b,c),funcmid34(a,b,c,d).
null(0):-mid35(a,b,c),funcmid35(a,b,c,d).
null(0):-mid36(a,b,c),funcmid36(a,b,c,d).
null(0):-mid37(a,b,c),funcmid37(a,b,c,d).
null(0):-mid38(a,b,c),funcmid38(a,b,c,d).
null(0):-mid39(a,b,c),funcmid39(a,b,c,d).
null(0):-mid40(a,b,c),funcmid40(a,b,c,d).
null(0):-mid41(a,b,c),funcmid41(a,b,c,d).
null(0):-mid42(a,b,c),funcmid42(a,b,c,d).
null(0):-mid43(a,b,c),funcmid43(a,b,c,d).
null(0):-mid44(a,b,c),funcmid44(a,b,c,d).
null(0):-mid45(a,b,c),funcmid45(a,b,c,d).
null(0):-mid46(a,b,c),funcmid46(a,b,c,d).
null(0):-mid47(a,b,c),funcmid47(a,b,c,d).
null(0):-mid48(a,b,c),funcmid48(a,b,c,d).
null(0):-mid49(a,b,c),funcmid49(a,b,c,d).
null(0):-mid50(a,b,c),funcmid50(a,b,c,d).
null(0):-mid51(a,b,c),funcmid51(a,b,c,d).
null(0):-mid52(a,b,c),funcmid52(a,b,c,d).
null(0):-mid53(a,b,c),funcmid53(a,b,c,d).
null(0):-mid54(a,b,c),funcmid54(a,b,c,d).
null(0):-mid55(a,b,c),funcmid55(a,b,c,d).
null(0):-mid56(a,b,c),funcmid56(a,b,c,d).
null(0):-mid57(a,b,c),funcmid57(a,b,c,d).
null(0):-mid58(a,b,c),funcmid58(a,b,c,d).
null(0):-mid59(a,b,c),funcmid59(a,b,c,d).
null(0):-mid60(a,b,c),funcmid60(a,b,c,d).
null(0):-mid61(a,b,c),funcmid61(a,b,c,d).
null(0):-mid62(a,b,c),funcmid62(a,b,c,d).
null(0):-mid63(a,b,c),funcmid63(a,b,c,d).
null(0):-mid64(a,b,c),funcmid64(a,b,c,d).
null(0):-mid65(a,b,c),funcmid65(a,b,c,d).
null(0):-mid66(a,b,c),funcmid66(a,b,c,d).
null(0):-mid67(a,b,c),funcmid67(a,b,c,d).
null(0):-mid68(a,b,c),funcmid68(a,b,c,d).
null(0):-mid69(a,b,c),funcmid69(a,b,c,d).
null(0):-mid70(a,b,c),funcmid70(a,b,c,d).
null(0):-mid71(a,b,c),funcmid71(a,b,c,d).
null(0):-mid72(a,b,c),funcmid72(a,b,c,d).
null(0):-mid73(a,b,c),funcmid73(a,b,c,d).
null(0):-mid74(a,b,c),funcmid74(a,b,c,d).
null(0):-mid75(a,b,c),funcmid75(a,b,c,d).
null(0):-mid76(a,b,c),funcmid76(a,b,c,d).
null(0):-mid77(a,b,c),funcmid77(a,b,c,d).
null(0):-mid78(a,b,c),funcmid78(a,b,c,d).
null(0):-mid79(a,b,c),funcmid79(a,b,c,d).
null(0):-mid80(a,b,c),funcmid80(a,b,c,d).
null(0):-mid81(a,b,c),funcmid81(a,b,c,d).
null(0):-mid82(a,b,c),funcmid82(a,b,c,d).
null(0):-mid83(a,b,c),funcmid83(a,b,c,d).
null(0):-mid84(a,b,c),funcmid84(a,b,c,d).
null(0):-mid85(a,b,c),funcmid85(a,b,c,d).
null(0):-mid86(a,b,c),funcmid86(a,b,c,d).
null(0):-mid87(a,b,c),funcmid87(a,b,c,d).
null(0):-mid88(a,b,c),funcmid88(a,b,c,d).
null(0):-mid89(a,b,c),funcmid89(a,b,c,d).
null(0):-mid90(a,b,c),funcmid90(a,b,c,d).
null(0):-mid91(a,b,c),funcmid91(a,b,c,d).
null(0):-mid92(a,b,c),funcmid92(a,b,c,d).
null(0):-mid93(a,b,c),funcmid93(a,b,c,d).
null(0):-mid94(a,b,c),funcmid94(a,b,c,d).
null(0):-mid95(a,b,c),funcmid95(a,b,c,d).
null(0):-mid96(a,b,c),funcmid96(a,b,c,d).
null(0):-mid97(a,b,c),funcmid97(a,b,c,d).
null(0):-mid98(a,b,c),funcmid98(a,b,c,d).
null(0):-mid99(a,b,c),funcmid99(a,b,c,d).
null(0):-mid100(a,b,c),funcmid100(a,b,c,d).
null(0):-mid101(a,b,c),funcmid101(a,b,c,d).
null(0):-mid102(a,b,c),funcmid102(a,b,c,d).
null(0):-mid103(a,b,c),funcmid103(a,b,c,d).
null(0):-mid104(a,b,c),funcmid104(a,b,c,d).
null(0):-mid105(a,b,c),funcmid105(a,b,c,d).
null(0):-mid106(a,b,c),funcmid106(a,b,c,d).
null(0):-mid107(a,b,c),funcmid107(a,b,c,d).
null(0):-mid108(a,b,c),funcmid108(a,b,c,d).
null(0):-mid109(a,b,c),funcmid109(a,b,c,d).
null(0):-mid110(a,b,c),funcmid110(a,b,c,d).
null(0):-mid111(a,b,c),funcmid111(a,b,c,d).
null(0):-mid112(a,b,c),funcmid112(a,b,c,d).
null(0):-mid113(a,b,c),funcmid113(a,b,c,d).
null(0):-mid114(a,b,c),funcmid114(a,b,c,d).
null(0):-mid115(a,b,c),funcmid115(a,b,c,d).
null(0):-mid116(a,b,c),funcmid116(a,b,c,d).
null(0):-mid117(a,b,c),funcmid117(a,b,c,d).
null(0):-mid118(a,b,c),funcmid118(a,b,c,d).
null(0):-mid119(a,b,c),funcmid119(a,b,c,d).
null(0):-mid120(a,b,c),funcmid120(a,b,c,d).
null(0):-mid121(a,b,c),funcmid121(a,b,c,d).
null(0):-mid122(a,b,c),funcmid122(a,b,c,d).
null(0):-mid123(a,b,c),funcmid123(a,b,c,d).
null(0):-mid124(a,b,c),funcmid124(a,b,c,d).
null(0):-mid125(a,b,c),funcmid125(a,b,c,d).
null(0):-mid126(a,b,c),funcmid126(a,b,c,d).
null(0):-mid127(a,b,c),funcmid127(a,b,c,d).
null(0):-mid128(a,b,c),funcmid128(a,b,c,d).
null(0):-mid129(a,b,c),funcmid129(a,b,c,d).
null(0):-mid130(a,b,c),funcmid130(a,b,c,d).
null(0):-mid131(a,b,c),funcmid131(a,b,c,d).
null(0):-mid132(a,b,c),funcmid132(a,b,c,d).
null(0):-mid133(a,b,c),funcmid133(a,b,c,d).
null(0):-mid134(a,b,c),funcmid134(a,b,c,d).
null(0):-mid135(a,b,c),funcmid135(a,b,c,d).
null(0):-mid136(a,b,c),funcmid136(a,b,c,d).
null(0):-mid137(a,b,c),funcmid137(a,b,c,d).
null(0):-mid138(a,b,c),funcmid138(a,b,c,d).
null(0):-mid139(a,b,c),funcmid139(a,b,c,d).
null(0):-mid140(a,b,c),funcmid140(a,b,c,d).
null(0):-mid141(a,b,c),funcmid141(a,b,c,d).
null(0):-mid142(a,b,c),funcmid142(a,b,c,d).
null(0):-mid143(a,b,c),funcmid143(a,b,c,d).
null(0):-mid144(a,b,c),funcmid144(a,b,c,d).
null(0):-mid145(a,b,c),funcmid145(a,b,c,d).
null(0):-mid146(a,b,c),funcmid146(a,b,c,d).
null(0):-mid147(a,b,c),funcmid147(a,b,c,d).
null(0):-mid148(a,b,c),funcmid148(a,b,c,d).
null(0):-mid149(a,b,c),funcmid149(a,b,c,d).
null(0):-mid150(a,b,c),funcmid150(a,b,c,d).
null(0):-mid151(a,b,c),funcmid151(a,b,c,d).
null(0):-mid152(a,b,c),funcmid152(a,b,c,d).
null(0):-mid153(a,b,c),funcmid153(a,b,c,d).
null(0):-mid154(a,b,c),funcmid154(a,b,c,d).
null(0):-mid155(a,b,c),funcmid155(a,b,c,d).
null(0):-mid156(a,b,c),funcmid156(a,b,c,d).
null(0):-mid157(a,b,c),funcmid157(a,b,c,d).
null(0):-mid158(a,b,c),funcmid158(a,b,c,d).
null(0):-mid159(a,b,c),funcmid159(a,b,c,d).
null(0):-mid160(a,b,c),funcmid160(a,b,c,d).
null(0):-mid161(a,b,c),funcmid161(a,b,c,d).
null(0):-mid162(a,b,c),funcmid162(a,b,c,d).
null(0):-mid163(a,b,c),funcmid163(a,b,c,d).
null(0):-mid164(a,b,c),funcmid164(a,b,c,d).
null(0):-mid165(a,b,c),funcmid165(a,b,c,d).
null(0):-mid166(a,b,c),funcmid166(a,b,c,d).
null(0):-mid167(a,b,c),funcmid167(a,b,c,d).
null(0):-mid168(a,b,c),funcmid168(a,b,c,d).
null(0):-mid169(a,b,c),funcmid169(a,b,c,d).
null(0):-mid170(a,b,c),funcmid170(a,b,c,d).
null(0):-mid171(a,b,c),funcmid171(a,b,c,d).
null(0):-mid172(a,b,c),funcmid172(a,b,c,d).
null(0):-mid173(a,b,c),funcmid173(a,b,c,d).
null(0):-mid174(a,b,c),funcmid174(a,b,c,d).
null(0):-mid175(a,b,c),funcmid175(a,b,c,d).
null(0):-mid176(a,b,c),funcmid176(a,b,c,d).
null(0):-mid177(a,b,c),funcmid177(a,b,c,d).
null(0):-mid178(a,b,c),funcmid178(a,b,c,d).
null(0):-mid179(a,b,c),funcmid179(a,b,c,d).
null(0):-mid180(a,b,c),funcmid180(a,b,c,d).
null(0):-mid181(a,b,c),funcmid181(a,b,c,d).
null(0):-mid182(a,b,c),funcmid182(a,b,c,d).
null(0):-mid183(a,b,c),funcmid183(a,b,c,d).
null(0):-mid184(a,b,c),funcmid184(a,b,c,d).
null(0):-mid185(a,b,c),funcmid185(a,b,c,d).
null(0):-mid186(a,b,c),funcmid186(a,b,c,d).
null(0):-mid187(a,b,c),funcmid187(a,b,c,d).
null(0):-mid188(a,b,c),funcmid188(a,b,c,d).
null(0):-mid189(a,b,c),funcmid189(a,b,c,d).
null(0):-mid190(a,b,c),funcmid190(a,b,c,d).
null(0):-mid191(a,b,c),funcmid191(a,b,c,d).
null(0):-mid192(a,b,c),funcmid192(a,b,c,d).
null(0):-mid193(a,b,c),funcmid193(a,b,c,d).
null(0):-mid194(a,b,c),funcmid194(a,b,c,d).
null(0):-mid195(a,b,c),funcmid195(a,b,c,d).
null(0):-mid196(a,b,c),funcmid196(a,b,c,d).
null(0):-mid197(a,b,c),funcmid197(a,b,c,d).
null(0):-mid198(a,b,c),funcmid198(a,b,c,d).
null(0):-mid199(a,b,c),funcmid199(a,b,c,d).
null(0):-mid200(a,b,c),funcmid200(a,b,c,d).
null(0):-mid201(a,b,c),funcmid201(a,b,c,d).
null(0):-mid202(a,b,c),funcmid202(a,b,c,d).
null(0):-mid203(a,b,c),funcmid203(a,b,c,d).
null(0):-mid204(a,b,c),funcmid204(a,b,c,d).
null(0):-mid205(a,b,c),funcmid205(a,b,c,d).
null(0):-mid206(a,b,c),funcmid206(a,b,c,d).
null(0):-mid207(a,b,c),funcmid207(a,b,c,d).
null(0):-mid208(a,b,c),funcmid208(a,b,c,d).
null(0):-mid209(a,b,c),funcmid209(a,b,c,d).
null(0):-mid210(a,b,c),funcmid210(a,b,c,d).
null(0):-mid211(a,b,c),funcmid211(a,b,c,d).
null(0):-mid212(a,b,c),funcmid212(a,b,c,d).
null(0):-mid213(a,b,c),funcmid213(a,b,c,d).
null(0):-mid214(a,b,c),funcmid214(a,b,c,d).
null(0):-mid215(a,b,c),funcmid215(a,b,c,d).
null(0):-mid216(a,b,c),funcmid216(a,b,c,d).
null(0):-mid217(a,b,c),funcmid217(a,b,c,d).
null(0):-mid218(a,b,c),funcmid218(a,b,c,d).
null(0):-mid219(a,b,c),funcmid219(a,b,c,d).
null(0):-mid220(a,b,c),funcmid220(a,b,c,d).
null(0):-mid221(a,b,c),funcmid221(a,b,c,d).
null(0):-mid222(a,b,c),funcmid222(a,b,c,d).
null(0):-mid223(a,b,c),funcmid223(a,b,c,d).
null(0):-mid224(a,b,c),funcmid224(a,b,c,d).
null(0):-mid225(a,b,c),funcmid225(a,b,c,d).
null(0):-mid226(a,b,c),funcmid226(a,b,c,d).
null(0):-mid227(a,b,c),funcmid227(a,b,c,d).
null(0):-mid228(a,b,c),funcmid228(a,b,c,d).
null(0):-mid229(a,b,c),funcmid229(a,b,c,d).
null(0):-mid230(a,b,c),funcmid230(a,b,c,d).
null(0):-mid231(a,b,c),funcmid231(a,b,c,d).
null(0):-mid232(a,b,c),funcmid232(a,b,c,d).
null(0):-mid233(a,b,c),funcmid233(a,b,c,d).
null(0):-mid234(a,b,c),funcmid234(a,b,c,d).
null(0):-mid235(a,b,c),funcmid235(a,b,c,d).
null(0):-mid236(a,b,c),funcmid236(a,b,c,d).
null(0):-mid237(a,b,c),funcmid237(a,b,c,d).
null(0):-mid238(a,b,c),funcmid238(a,b,c,d).
null(0):-mid239(a,b,c),funcmid239(a,b,c,d).
null(0):-mid240(a,b,c),funcmid240(a,b,c,d).
null(0):-mid241(a,b,c),funcmid241(a,b,c,d).
null(0):-mid242(a,b,c),funcmid242(a,b,c,d).
null(0):-mid243(a,b,c),funcmid243(a,b,c,d).
null(0):-mid244(a,b,c),funcmid244(a,b,c,d).
null(0):-mid245(a,b,c),funcmid245(a,b,c,d).
null(0):-mid246(a,b,c),funcmid246(a,b,c,d).
null(0):-mid247(a,b,c),funcmid247(a,b,c,d).
null(0):-mid248(a,b,c),funcmid248(a,b,c,d).
null(0):-mid249(a,b,c),funcmid249(a,b,c,d).
null(0):-mid250(a,b,c),funcmid250(a,b,c,d).
null(0):-inp1(a,b,c),inp2(a,b,c),inp3(a,b,c).
null(0):-inp2(a,b,c),inp3(a,b,c),inp4(a,b,c).
null(0):-inp3(a,b,c),inp4(a,b,c),inp5(a,b,c).
null(0):-inp4(a,b,c),inp5(a,b,c),inp6(a,b,c).
null(0):-inp5(a,b,c),inp6(a,b,c),inp7(a,b,c).
null(0):-inp6(a,b,c),inp7(a,b,c),inp8(a,b,c).
null(0):-inp7(a,b,c),inp8(a,b,c),inp9(a,b,c).
null(0):-inp8(a,b,c),inp9(a,b,c),inp10(a,b,c).
null(0):-inp9(a,b,c),inp10(a,b,c),inp11(a,b,c).
null(0):-inp10(a,b,c),inp11(a,b,c),inp12(a,b,c).
null(0):-inp11(a,b,c),inp12(a,b,c),inp13(a,b,c).
null(0):-inp12(a,b,c),inp13(a,b,c),inp14(a,b,c).
null(0):-inp13(a,b,c),inp14(a,b,c),inp15(a,b,c).
null(0):-inp14(a,b,c),inp15(a,b,c),inp16(a,b,c).
null(0):-inp15(a,b,c),inp16(a,b,c),inp17(a,b,c).
null(0):-inp16(a,b,c),inp17(a,b,c),inp18(a,b,c).
null(0):-inp17(a,b,c),inp18(a,b,c),inp19(a,b,c).
null(0):-inp18(a,b,c),inp19(a,b,c),inp20(a,b,c).
null(0):-inp19(a,b,c),inp20(a,b,c),inp21(a,b,c).
null(0):-inp20(a,b,c),inp21(a,b,c),inp22(a,b,c).
null(0):-inp21(a,b,c),inp22(a,b,c),inp23(a,b,c).
null(0):-inp22(a,b,c),inp23(a,b,c),inp24(a,b,c).
null(0):-inp23(a,b,c),inp24(a,b,c),inp25(a,b,c).
null(0):-inp24(a,b,c),inp25(a,b,c),inp26(a,b,c).
null(0):-inp25(a,b,c),inp26(a,b,c),inp27(a,b,c).
null(0):-inp26(a,b,c),inp27(a,b,c),inp28(a,b,c).
null(0):-inp27(a,b,c),inp28(a,b,c),inp29(a,b,c).
null(0):-inp28(a,b,c),inp29(a,b,c),inp30(a,b,c).
null(0):-inp29(a,b,c),inp30(a,b,c),inp31(a,b,c).
null(0):-inp30(a,b,c),inp31(a,b,c),inp32(a,b,c).
null(0):-inp31(a,b,c),inp32(a,b,c),inp33(a,b,c).
null(0):-inp32(a,b,c),inp33(a,b,c),inp34(a,b,c).
null(0):-inp33(a,b,c),inp34(a,b,c),inp35(a,b,c).
null(0):-inp34(a,b,c),inp35(a,b,c),inp36(a,b,c).
null(0):-inp35(a,b,c),inp36(a,b,c),inp37(a,b,c).
null(0):-inp36(a,b,c),inp37(a,b,c),inp38(a,b,c).
null(0):-inp37(a,b,c),inp38(a,b,c),inp39(a,b,c).
null(0):-inp38(a,b,c),inp39(a,b,c),inp40(a,b,c).
null(0):-inp39(a,b,c),inp40(a,b,c),inp41(a,b,c).
null(0):-inp40(a,b,c),inp41(a,b,c),inp42(a,b,c).
null(0):-inp41(a,b,c),inp42(a,b,c),inp43(a,b,c).
null(0):-inp42(a,b,c),inp43(a,b,c),inp44(a,b,c).
null(0):-inp43(a,b,c),inp44(a,b,c),inp45(a,b,c).
null(0):-inp44(a,b,c),inp45(a,b,c),inp46(a,b,c).
null(0):-inp45(a,b,c),inp46(a,b,c),inp47(a,b,c).
null(0):-inp46(a,b,c),inp47(a,b,c),inp48(a,b,c).
null(0):-inp47(a,b,c),inp48(a,b,c),inp49(a,b,c).
null(0):-inp48(a,b,c),inp49(a,b,c),inp50(a,b,c).
null(0):-inp49(a,b,c),inp50(a,b,c),inp51(a,b,c).
null(0):-inp50(a,b,c),inp51(a,b,c),inp52(a,b,c).
null(0):-inp51(a,b,c),inp52(a,b,c),inp53(a,b,c).
null(0):-inp52(a,b,c),inp53(a,b,c),inp54(a,b,c).
null(0):-inp53(a,b,c),inp54(a,b,c),inp55(a,b,c).
null(0):-inp54(a,b,c),inp55(a,b,c),inp56(a,b,c).
null(0):-inp55(a,b,c),inp56(a,b,c),inp57(a,b,c).
null(0):-inp56(a,b,c),inp57(a,b,c),inp58(a,b,c).
null(0):-inp57(a,b,c),inp58(a,b,c),inp59(a,b,c).
null(0):-inp58(a,b,c),inp59(a,b,c),inp60(a,b,c).
null(0):-inp59(a,b,c),inp60(a,b,c),inp61(a,b,c).
null(0):-inp60(a,b,c),inp61(a,b,c),inp62(a,b,c).
null(0):-inp61(a,b,c),inp62(a,b,c),inp63(a,b,c).
null(0):-inp62(a,b,c),inp63(a,b,c),inp64(a,b,c).
null(0):-inp63(a,b,c),inp64(a,b,c),inp65(a,b,c).
null(0):-inp64(a,b,c),inp65(a,b,c),inp66(a,b,c).
null(0):-inp65(a,b,c),inp66(a,b,c),inp67(a,b,c).
null(0):-inp66(a,b,c),inp67(a,b,c),inp68(a,b,c).
null(0):-inp67(a,b,c),inp68(a,b,c),inp69(a,b,c).
null(0):-inp68(a,b,c),inp69(a,b,c),inp70(a,b,c).
null(0):-inp69(a,b,c),inp70(a,b,c),inp71(a,b,c).
null(0):-inp70(a,b,c),inp71(a,b,c),inp72(a,b,c).
null(0):-inp71(a,b,c),inp72(a,b,c),inp73(a,b,c).
null(0):-inp72(a,b,c),inp73(a,b,c),inp74(a,b,c).
null(0):-inp73(a,b,c),inp74(a,b,c),inp75(a,b,c).
null(0):-inp74(a,b,c),inp75(a,b,c),inp76(a,b,c).
null(0):-inp75(a,b,c),inp76(a,b,c),inp77(a,b,c).
null(0):-inp76(a,b,c),inp77(a,b,c),inp78(a,b,c).
null(0):-inp77(a,b,c),inp78(a,b,c),inp79(a,b,c).
null(0):-inp78(a,b,c),inp79(a,b,c),inp80(a,b,c).
null(0):-inp79(a,b,c),inp80(a,b,c),inp81(a,b,c).
null(0):-inp80(a,b,c),inp81(a,b,c),inp82(a,b,c).
null(0):-inp81(a,b,c),inp82(a,b,c),inp83(a,b,c).
null(0):-inp82(a,b,c),inp83(a,b,c),inp84(a,b,c).
null(0):-inp83(a,b,c),inp84(a,b,c),inp85(a,b,c).
null(0):-inp84(a,b,c),inp85(a,b,c),inp86(a,b,c).
null(0):-inp85(a,b,c),inp86(a,b,c),inp87(a,b,c).
null(0):-inp86(a,b,c),inp87(a,b,c),inp88(a,b,c).
null(0):-inp87(a,b,c),inp88(a,b,c),inp89(a,b,c).
null(0):-inp88(a,b,c),inp89(a,b,c),inp90(a,b,c).
null(0):-inp89(a,b,c),inp90(a,b,c),inp91(a,b,c).
null(0):-inp90(a,b,c),inp91(a,b,c),inp92(a,b,c).
null(0):-inp91(a,b,c),inp92(a,b,c),inp93(a,b,c).
null(0):-inp92(a,b,c),inp93(a,b,c),inp94(a,b,c).
null(0):-inp93(a,b,c),inp94(a,b,c),inp95(a,b,c).
null(0):-inp94(a,b,c),inp95(a,b,c),inp96(a,b,c).
null(0):-inp95(a,b,c),inp96(a,b,c),inp97(a,b,c).
null(0):-inp96(a,b,c),inp97(a,b,c),inp98(a,b,c).
null(0):-inp97(a,b,c),inp98(a,b,c),inp99(a,b,c).
null(0):-inp98(a,b,c),inp99(a,b,c),inp100(a,b,c).
null(0):-inp99(a,b,c),inp100(a,b,c),inp101(a,b,c).
null(0):-inp100(a,b,c),inp101(a,b,c),inp102(a,b,c).
null(0):-inp101(a,b,c),inp102(a,b,c),inp103(a,b,c).
null(0):-inp102(a,b,c),inp103(a,b,c),inp104(a,b,c).
null(0):-inp103(a,b,c),inp104(a,b,c),inp105(a,b,c).
null(0):-inp104(a,b,c),inp105(a,b,c),inp106(a,b,c).
null(0):-inp105(a,b,c),inp106(a,b,c),inp107(a,b,c).
null(0):-inp106(a,b,c),inp107(a,b,c),inp108(a,b,c).
null(0):-inp107(a,b,c),inp108(a,b,c),inp109(a,b,c).
null(0):-inp108(a,b,c),inp109(a,b,c),inp110(a,b,c).
null(0):-inp109(a,b,c),inp110(a,b,c),inp111(a,b,c).
null(0):-inp110(a,b,c),inp111(a,b,c),inp112(a,b,c).
null(0):-inp111(a,b,c),inp112(a,b,c),inp113(a,b,c).
null(0):-inp112(a,b,c),inp113(a,b,c),inp114(a,b,c).
null(0):-inp113(a,b,c),inp114(a,b,c),inp115(a,b,c).
null(0):-inp114(a,b,c),inp115(a,b,c),inp116(a,b,c).
null(0):-inp115(a,b,c),inp116(a,b,c),inp117(a,b,c).
null(0):-inp116(a,b,c),inp117(a,b,c),inp118(a,b,c).
null(0):-inp117(a,b,c),inp118(a,b,c),inp119(a,b,c).
null(0):-inp118(a,b,c),inp119(a,b,c),inp120(a,b,c).
null(0):-inp119(a,b,c),inp120(a,b,c),inp121(a,b,c).
null(0):-inp120(a,b,c),inp121(a,b,c),inp122(a,b,c).
null(0):-inp121(a,b,c),inp122(a,b,c),inp123(a,b,c).
null(0):-inp122(a,b,c),inp123(a,b,c),inp124(a,b,c).
null(0):-inp123(a,b,c),inp124(a,b,c),inp125(a,b,c).
null(0):-inp124(a,b,c),inp125(a,b,c),inp126(a,b,c).
null(0):-inp125(a,b,c),inp126(a,b,c),inp127(a,b,c).
null(0):-inp126(a,b,c),inp127(a,b,c),inp128(a,b,c).
null(0):-inp127(a,b,c),inp128(a,b,c),inp129(a,b,c).
null(0):-inp128(a,b,c),inp129(a,b,c),inp130(a,b,c).
null(0):-inp129(a,b,c),inp130(a,b,c),inp131(a,b,c).
null(0):-inp130(a,b,c),inp131(a,b,c),inp132(a,b,c).
null(0):-inp131(a,b,c),inp132(a,b,c),inp133(a,b,c).
null(0):-inp132(a,b,c),inp133(a,b,c),inp134(a,b,c).
null(0):-inp133(a,b,c),inp134(a,b,c),inp135(a,b,c).
null(0):-inp134(a,b,c),inp135(a,b,c),inp136(a,b,c).
null(0):-inp135(a,b,c),inp136(a,b,c),inp137(a,b,c).
null(0):-inp136(a,b,c),inp137(a,b,c),inp138(a,b,c).
null(0):-inp137(a,b,c),inp138(a,b,c),inp139(a,b,c).
null(0):-inp138(a,b,c),inp139(a,b,c),inp140(a,b,c).
null(0):-inp139(a,b,c),inp140(a,b,c),inp141(a,b,c).
null(0):-inp140(a,b,c),inp141(a,b,c),inp142(a,b,c).
null(0):-inp141(a,b,c),inp142(a,b,c),inp143(a,b,c).
null(0):-inp142(a,b,c),inp143(a,b,c),inp144(a,b,c).
null(0):-inp143(a,b,c),inp144(a,b,c),inp145(a,b,c).
null(0):-inp144(a,b,c),inp145(a,b,c),inp146(a,b,c).
null(0):-inp145(a,b,c),inp146(a,b,c),inp147(a,b,c).
null(0):-inp146(a,b,c),inp147(a,b,c),inp148(a,b,c).
null(0):-inp147(a,b,c),inp148(a,b,c),inp149(a,b,c).
null(0):-inp148(a,b,c),inp149(a,b,c),inp150(a,b,c).
null(0):-inp149(a,b,c),inp150(a,b,c),inp151(a,b,c).
null(0):-inp150(a,b,c),inp151(a,b,c),inp152(a,b,c).
null(0):-inp151(a,b,c),inp152(a,b,c),inp153(a,b,c).
null(0):-inp152(a,b,c),inp153(a,b,c),inp154(a,b,c).
null(0):-inp153(a,b,c),inp154(a,b,c),inp155(a,b,c).
null(0):-inp154(a,b,c),inp155(a,b,c),inp156(a,b,c).
null(0):-inp155(a,b,c),inp156(a,b,c),inp157(a,b,c).
null(0):-inp156(a,b,c),inp157(a,b,c),inp158(a,b,c).
null(0):-inp157(a,b,c),inp158(a,b,c),inp159(a,b,c).
null(0):-inp158(a,b,c),inp159(a,b,c),inp160(a,b,c).
null(0):-inp159(a,b,c),inp160(a,b,c),inp161(a,b,c).
null(0):-inp160(a,b,c),inp161(a,b,c),inp162(a,b,c).
null(0):-inp161(a,b,c),inp162(a,b,c),inp163(a,b,c).
null(0):-inp162(a,b,c),inp163(a,b,c),inp164(a,b,c).
null(0):-inp163(a,b,c),inp164(a,b,c),inp165(a,b,c).
null(0):-inp164(a,b,c),inp165(a,b,c),inp166(a,b,c).
null(0):-inp165(a,b,c),inp166(a,b,c),inp167(a,b,c).
null(0):-inp166(a,b,c),inp167(a,b,c),inp168(a,b,c).
null(0):-inp167(a,b,c),inp168(a,b,c),inp169(a,b,c).
null(0):-inp168(a,b,c),inp169(a,b,c),inp170(a,b,c).
null(0):-inp169(a,b,c),inp170(a,b,c),inp171(a,b,c).
null(0):-inp170(a,b,c),inp171(a,b,c),inp172(a,b,c).
null(0):-inp171(a,b,c),inp172(a,b,c),inp173(a,b,c).
null(0):-inp172(a,b,c),inp173(a,b,c),inp174(a,b,c).
null(0):-inp173(a,b,c),inp174(a,b,c),inp175(a,b,c).
null(0):-inp174(a,b,c),inp175(a,b,c),inp176(a,b,c).
null(0):-inp175(a,b,c),inp176(a,b,c),inp177(a,b,c).
null(0):-inp176(a,b,c),inp177(a,b,c),inp178(a,b,c).
null(0):-inp177(a,b,c),inp178(a,b,c),inp179(a,b,c).
null(0):-inp178(a,b,c),inp179(a,b,c),inp180(a,b,c).
null(0):-inp179(a,b,c),inp180(a,b,c),inp181(a,b,c).
null(0):-inp180(a,b,c),inp181(a,b,c),inp182(a,b,c).
null(0):-inp181(a,b,c),inp182(a,b,c),inp183(a,b,c).
null(0):-inp182(a,b,c),inp183(a,b,c),inp184(a,b,c).
null(0):-inp183(a,b,c),inp184(a,b,c),inp185(a,b,c).
null(0):-inp184(a,b,c),inp185(a,b,c),inp186(a,b,c).
null(0):-inp185(a,b,c),inp186(a,b,c),inp187(a,b,c).
null(0):-inp186(a,b,c),inp187(a,b,c),inp188(a,b,c).
null(0):-inp187(a,b,c),inp188(a,b,c),inp189(a,b,c).
null(0):-inp188(a,b,c),inp189(a,b,c),inp190(a,b,c).
null(0):-inp189(a,b,c),inp190(a,b,c),inp191(a,b,c).
null(0):-inp190(a,b,c),inp191(a,b,c),inp192(a,b,c).
null(0):-inp191(a,b,c),inp192(a,b,c),inp193(a,b,c).
null(0):-inp192(a,b,c),inp193(a,b,c),inp194(a,b,c).
null(0):-inp193(a,b,c),inp194(a,b,c),inp195(a,b,c).
null(0):-inp194(a,b,c),inp195(a,b,c),inp196(a,b,c).
null(0):-inp195(a,b,c),inp196(a,b,c),inp197(a,b,c).
null(0):-inp196(a,b,c),inp197(a,b,c),inp198(a,b,c).
null(0):-inp197(a,b,c),inp198(a,b,c),inp199(a,b,c).
null(0):-inp198(a,b,c),inp199(a,b,c),inp200(a,b,c).
null(0):-inp199(a,b,c),inp200(a,b,c),inp201(a,b,c).
null(0):-inp200(a,b,c),inp201(a,b,c),inp202(a,b,c).
null(0):-inp201(a,b,c),inp202(a,b,c),inp203(a,b,c).
null(0):-inp202(a,b,c),inp203(a,b,c),inp204(a,b,c).
null(0):-inp203(a,b,c),inp204(a,b,c),inp205(a,b,c).
null(0):-inp204(a,b,c),inp205(a,b,c),inp206(a,b,c).
null(0):-inp205(a,b,c),inp206(a,b,c),inp207(a,b,c).
null(0):-inp206(a,b,c),inp207(a,b,c),inp208(a,b,c).
null(0):-inp207(a,b,c),inp208(a,b,c),inp209(a,b,c).
null(0):-inp208(a,b,c),inp209(a,b,c),inp210(a,b,c).
null(0):-inp209(a,b,c),inp210(a,b,c),inp211(a,b,c).
null(0):-inp210(a,b,c),inp211(a,b,c),inp212(a,b,c).
null(0):-inp211(a,b,c),inp212(a,b,c),inp213(a,b,c).
null(0):-inp212(a,b,c),inp213(a,b,c),inp214(a,b,c).
null(0):-inp213(a,b,c),inp214(a,b,c),inp215(a,b,c).
null(0):-inp214(a,b,c),inp215(a,b,c),inp216(a,b,c).
null(0):-inp215(a,b,c),inp216(a,b,c),inp217(a,b,c).
null(0):-inp216(a,b,c),inp217(a,b,c),inp218(a,b,c).
null(0):-inp217(a,b,c),inp218(a,b,c),inp219(a,b,c).
null(0):-inp218(a,b,c),inp219(a,b,c),inp220(a,b,c).
null(0):-inp219(a,b,c),inp220(a,b,c),inp221(a,b,c).
null(0):-inp220(a,b,c),inp221(a,b,c),inp222(a,b,c).
null(0):-inp221(a,b,c),inp222(a,b,c),inp223(a,b,c).
null(0):-inp222(a,b,c),inp223(a,b,c),inp224(a,b,c).
null(0):-inp223(a,b,c),inp224(a,b,c),inp225(a,b,c).
null(0):-inp224(a,b,c),inp225(a,b,c),inp226(a,b,c).
null(0):-inp225(a,b,c),inp226(a,b,c),inp227(a,b,c).
null(0):-inp226(a,b,c),inp227(a,b,c),inp228(a,b,c).
null(0):-inp227(a,b,c),inp228(a,b,c),inp229(a,b,c).
null(0):-inp228(a,b,c),inp229(a,b,c),inp230(a,b,c).
null(0):-inp229(a,b,c),inp230(a,b,c),inp231(a,b,c).
null(0):-inp230(a,b,c),inp231(a,b,c),inp232(a,b,c).
null(0):-inp231(a,b,c),inp232(a,b,c),inp233(a,b,c).
null(0):-inp232(a,b,c),inp233(a,b,c),inp234(a,b,c).
null(0):-inp233(a,b,c),inp234(a,b,c),inp235(a,b,c).
null(0):-inp234(a,b,c),inp235(a,b,c),inp236(a,b,c).
null(0):-inp235(a,b,c),inp236(a,b,c),inp237(a,b,c).
null(0):-inp236(a,b,c),inp237(a,b,c),inp238(a,b,c).
null(0):-inp237(a,b,c),inp238(a,b,c),inp239(a,b,c).
null(0):-inp238(a,b,c),inp239(a,b,c),inp240(a,b,c).
null(0):-inp239(a,b,c),inp240(a,b,c),inp241(a,b,c).
null(0):-inp240(a,b,c),inp241(a,b,c),inp242(a,b,c).
null(0):-inp241(a,b,c),inp242(a,b,c),inp243(a,b,c).
null(0):-inp242(a,b,c),inp243(a,b,c),inp244(a,b,c).
null(0):-inp243(a,b,c),inp244(a,b,c),inp245(a,b,c).
null(0):-inp244(a,b,c),inp245(a,b,c),inp246(a,b,c).
null(0):-inp245(a,b,c),inp246(a,b,c),inp247(a,b,c).
null(0):-inp246(a,b,c),inp247(a,b,c),inp248(a,b,c).
null(0):-inp247(a,b,c),inp248(a,b,c),inp249(a,b,c).
null(0):-inp248(a,b,c),inp249(a,b,c),inp250(a,b,c).
null(0):-inp249(a,b,c),inp250(a,b,c),inp251(a,b,c).
null(0):-inp250(a,b,c),inp251(a,b,c),inp252(a,b,c).
null(0):-inp251(a,b,c),inp252(a,b,c),inp253(a,b,c).
null(0):-inp252(a,b,c),inp253(a,b,c),inp254(a,b,c).
null(0):-inp253(a,b,c),inp254(a,b,c),inp255(a,b,c).
null(0):-inp254(a,b,c),inp255(a,b,c),inp256(a,b,c).
null(0):-inp255(a,b,c),inp256(a,b,c),inp257(a,b,c).
null(0):-inp256(a,b,c),inp257(a,b,c),inp258(a,b,c).
null(0):-inp257(a,b,c),inp258(a,b,c),inp259(a,b,c).
null(0):-inp258(a,b,c),inp259(a,b,c),inp260(a,b,c).
null(0):-inp259(a,b,c),inp260(a,b,c),inp261(a,b,c).
null(0):-inp260(a,b,c),inp261(a,b,c),inp262(a,b,c).
null(0):-inp261(a,b,c),inp262(a,b,c),inp263(a,b,c).
null(0):-inp262(a,b,c),inp263(a,b,c),inp264(a,b,c).
null(0):-inp263(a,b,c),inp264(a,b,c),inp265(a,b,c).
null(0):-inp264(a,b,c),inp265(a,b,c),inp266(a,b,c).
null(0):-inp265(a,b,c),inp266(a,b,c),inp267(a,b,c).
null(0):-inp266(a,b,c),inp267(a,b,c),inp268(a,b,c).
null(0):-inp267(a,b,c),inp268(a,b,c),inp269(a,b,c).
null(0):-inp268(a,b,c),inp269(a,b,c),inp270(a,b,c).
null(0):-inp269(a,b,c),inp270(a,b,c),inp271(a,b,c).
null(0):-inp270(a,b,c),inp271(a,b,c),inp272(a,b,c).
null(0):-inp271(a,b,c),inp272(a,b,c),inp273(a,b,c).
null(0):-inp272(a,b,c),inp273(a,b,c),inp274(a,b,c).
null(0):-inp273(a,b,c),inp274(a,b,c),inp275(a,b,c).
null(0):-inp274(a,b,c),inp275(a,b,c),inp276(a,b,c).
null(0):-inp275(a,b,c),inp276(a,b,c),inp277(a,b,c).
null(0):-inp276(a,b,c),inp277(a,b,c),inp278(a,b,c).
null(0):-inp277(a,b,c),inp278(a,b,c),inp279(a,b,c).
null(0):-inp278(a,b,c),inp279(a,b,c),inp280(a,b,c).
null(0):-inp279(a,b,c),inp280(a,b,c),inp281(a,b,c).
null(0):-inp280(a,b,c),inp281(a,b,c),inp282(a,b,c).
null(0):-inp281(a,b,c),inp282(a,b,c),inp283(a,b,c).
null(0):-inp282(a,b,c),inp283(a,b,c),inp284(a,b,c).
null(0):-inp283(a,b,c),inp284(a,b,c),inp285(a,b,c).
null(0):-inp284(a,b,c),inp285(a,b,c),inp286(a,b,c).
null(0):-inp285(a,b,c),inp286(a,b,c),inp287(a,b,c).
null(0):-inp286(a,b,c),inp287(a,b,c),inp288(a,b,c).
null(0):-inp287(a,b,c),inp288(a,b,c),inp289(a,b,c).
null(0):-inp288(a,b,c),inp289(a,b,c),inp290(a,b,c).
null(0):-inp289(a,b,c),inp290(a,b,c),inp291(a,b,c).
null(0):-inp290(a,b,c),inp291(a,b,c),inp292(a,b,c).
null(0):-inp291(a,b,c),inp292(a,b,c),inp293(a,b,c).
null(0):-inp292(a,b,c),inp293(a,b,c),inp294(a,b,c).
null(0):-inp293(a,b,c),inp294(a,b,c),inp295(a,b,c).
null(0):-inp294(a,b,c),inp295(a,b,c),inp296(a,b,c).
null(0):-inp295(a,b,c),inp296(a,b,c),inp297(a,b,c).
null(0):-inp296(a,b,c),inp297(a,b,c),inp298(a,b,c).
null(0):-inp297(a,b,c),inp298(a,b,c),inp299(a,b,c).
null(0):-inp298(a,b,c),inp299(a,b,c),inp300(a,b,c).
null(0):-inp299(a,b,c),inp300(a,b,c),inp301(a,b,c).
null(0):-inp300(a,b,c),inp301(a,b,c),inp302(a,b,c).
null(0):-inp301(a,b,c),inp302(a,b,c),inp303(a,b,c).
null(0):-inp302(a,b,c),inp303(a,b,c),inp304(a,b,c).
null(0):-inp303(a,b,c),inp304(a,b,c),inp305(a,b,c).
null(0):-inp304(a,b,c),inp305(a,b,c),inp306(a,b,c).
null(0):-inp305(a,b,c),inp306(a,b,c),inp307(a,b,c).
null(0):-inp306(a,b,c),inp307(a,b,c),inp308(a,b,c).
null(0):-inp307(a,b,c),inp308(a,b,c),inp309(a,b,c).
null(0):-inp308(a,b,c),inp309(a,b,c),inp310(a,b,c).
null(0):-inp309(a,b,c),inp310(a,b,c),inp311(a,b,c).
null(0):-inp310(a,b,c),inp311(a,b,c),inp312(a,b,c).
null(0):-inp311(a,b,c),inp312(a,b,c),inp313(a,b,c).
null(0):-inp312(a,b,c),inp313(a,b,c),inp314(a,b,c).
null(0):-inp313(a,b,c),inp314(a,b,c),inp315(a,b,c).
null(0):-inp314(a,b,c),inp315(a,b,c),inp316(a,b,c).
null(0):-inp315(a,b,c),inp316(a,b,c),inp317(a,b,c).
null(0):-inp316(a,b,c),inp317(a,b,c),inp318(a,b,c).
null(0):-inp317(a,b,c),inp318(a,b,c),inp319(a,b,c).
null(0):-inp318(a,b,c),inp319(a,b,c),inp320(a,b,c).
null(0):-inp319(a,b,c),inp320(a,b,c),inp321(a,b,c).
null(0):-inp320(a,b,c),inp321(a,b,c),inp322(a,b,c).
null(0):-inp321(a,b,c),inp322(a,b,c),inp323(a,b,c).
null(0):-inp322(a,b,c),inp323(a,b,c),inp324(a,b,c).
null(0):-inp323(a,b,c),inp324(a,b,c),inp325(a,b,c).
null(0):-inp324(a,b,c),inp325(a,b,c),inp326(a,b,c).
null(0):-inp325(a,b,c),inp326(a,b,c),inp327(a,b,c).
null(0):-inp326(a,b,c),inp327(a,b,c),inp328(a,b,c).
null(0):-inp327(a,b,c),inp328(a,b,c),inp329(a,b,c).
null(0):-inp328(a,b,c),inp329(a,b,c),inp330(a,b,c).
null(0):-inp329(a,b,c),inp330(a,b,c),inp331(a,b,c).
null(0):-inp330(a,b,c),inp331(a,b,c),inp332(a,b,c).
null(0):-inp331(a,b,c),inp332(a,b,c),inp333(a,b,c).
null(0):-inp332(a,b,c),inp333(a,b,c),inp334(a,b,c).
null(0):-inp333(a,b,c),inp334(a,b,c),inp335(a,b,c).
null(0):-inp334(a,b,c),inp335(a,b,c),inp336(a,b,c).
null(0):-inp335(a,b,c),inp336(a,b,c),inp337(a,b,c).
null(0):-inp336(a,b,c),inp337(a,b,c),inp338(a,b,c).
null(0):-inp337(a,b,c),inp338(a,b,c),inp339(a,b,c).
null(0):-inp338(a,b,c),inp339(a,b,c),inp340(a,b,c).
null(0):-inp339(a,b,c),inp340(a,b,c),inp341(a,b,c).
null(0):-inp340(a,b,c),inp341(a,b,c),inp342(a,b,c).
null(0):-inp341(a,b,c),inp342(a,b,c),inp343(a,b,c).
null(0):-inp342(a,b,c),inp343(a,b,c),inp344(a,b,c).
null(0):-inp343(a,b,c),inp344(a,b,c),inp345(a,b,c).
null(0):-inp344(a,b,c),inp345(a,b,c),inp346(a,b,c).
null(0):-inp345(a,b,c),inp346(a,b,c),inp347(a,b,c).
null(0):-inp346(a,b,c),inp347(a,b,c),inp348(a,b,c).
null(0):-inp347(a,b,c),inp348(a,b,c),inp349(a,b,c).
null(0):-inp348(a,b,c),inp349(a,b,c),inp350(a,b,c).
null(0):-inp349(a,b,c),inp350(a,b,c),inp351(a,b,c).
null(0):-inp350(a,b,c),inp351(a,b,c),inp352(a,b,c).
null(0):-inp351(a,b,c),inp352(a,b,c),inp353(a,b,c).
null(0):-inp352(a,b,c),inp353(a,b,c),inp354(a,b,c).
null(0):-inp353(a,b,c),inp354(a,b,c),inp355(a,b,c).
null(0):-inp354(a,b,c),inp355(a,b,c),inp356(a,b,c).
null(0):-inp355(a,b,c),inp356(a,b,c),inp357(a,b,c).
null(0):-inp356(a,b,c),inp357(a,b,c),inp358(a,b,c).
null(0):-inp357(a,b,c),inp358(a,b,c),inp359(a,b,c).
null(0):-inp358(a,b,c),inp359(a,b,c),inp360(a,b,c).
null(0):-inp359(a,b,c),inp360(a,b,c),inp361(a,b,c).
null(0):-inp360(a,b,c),inp361(a,b,c),inp362(a,b,c).
null(0):-inp361(a,b,c),inp362(a,b,c),inp363(a,b,c).
null(0):-inp362(a,b,c),inp363(a,b,c),inp364(a,b,c).
null(0):-inp363(a,b,c),inp364(a,b,c),inp365(a,b,c).
null(0):-inp364(a,b,c),inp365(a,b,c),inp366(a,b,c).
null(0):-inp365(a,b,c),inp366(a,b,c),inp367(a,b,c).
null(0):-inp366(a,b,c),inp367(a,b,c),inp368(a,b,c).
null(0):-inp367(a,b,c),inp368(a,b,c),inp369(a,b,c).
null(0):-inp368(a,b,c),inp369(a,b,c),inp370(a,b,c).
null(0):-inp369(a,b,c),inp370(a,b,c),inp371(a,b,c).
null(0):-inp370(a,b,c),inp371(a,b,c),inp372(a,b,c).
null(0):-inp371(a,b,c),inp372(a,b,c),inp373(a,b,c).
null(0):-inp372(a,b,c),inp373(a,b,c),inp374(a,b,c).
null(0):-inp373(a,b,c),inp374(a,b,c),inp375(a,b,c).
null(0):-inp374(a,b,c),inp375(a,b,c),inp376(a,b,c).
null(0):-inp375(a,b,c),inp376(a,b,c),inp377(a,b,c).
null(0):-inp376(a,b,c),inp377(a,b,c),inp378(a,b,c).
null(0):-inp377(a,b,c),inp378(a,b,c),inp379(a,b,c).
null(0):-inp378(a,b,c),inp379(a,b,c),inp380(a,b,c).
null(0):-inp379(a,b,c),inp380(a,b,c),inp381(a,b,c).
null(0):-inp380(a,b,c),inp381(a,b,c),inp382(a,b,c).
null(0):-inp381(a,b,c),inp382(a,b,c),inp383(a,b,c).
null(0):-inp382(a,b,c),inp383(a,b,c),inp384(a,b,c).
null(0):-inp383(a,b,c),inp384(a,b,c),inp385(a,b,c).
null(0):-inp384(a,b,c),inp385(a,b,c),inp386(a,b,c).
null(0):-inp385(a,b,c),inp386(a,b,c),inp387(a,b,c).
null(0):-inp386(a,b,c),inp387(a,b,c),inp388(a,b,c).
null(0):-inp387(a,b,c),inp388(a,b,c),inp389(a,b,c).
null(0):-inp388(a,b,c),inp389(a,b,c),inp390(a,b,c).
null(0):-inp389(a,b,c),inp390(a,b,c),inp391(a,b,c).
null(0):-inp390(a,b,c),inp391(a,b,c),inp392(a,b,c).
null(0):-inp391(a,b,c),inp392(a,b,c),inp393(a,b,c).
null(0):-inp392(a,b,c),inp393(a,b,c),inp394(a,b,c).
null(0):-inp393(a,b,c),inp394(a,b,c),inp395(a,b,c).
null(0):-inp394(a,b,c),inp395(a,b,c),inp396(a,b,c).
null(0):-inp395(a,b,c),inp396(a,b,c),inp397(a,b,c).
null(0):-inp396(a,b,c),inp397(a,b,c),inp398(a,b,c).
null(0):-inp397(a,b,c),inp398(a,b,c),inp399(a,b,c).
null(0):-inp398(a,b,c),inp399(a,b,c),inp400(a,b,c).
null(0):-inp399(a,b,c),inp400(a,b,c),inp401(a,b,c).
null(0):-inp400(a,b,c),inp401(a,b,c),inp402(a,b,c).
null(0):-inp401(a,b,c),inp402(a,b,c),inp403(a,b,c).
null(0):-inp402(a,b,c),inp403(a,b,c),inp404(a,b,c).
null(0):-inp403(a,b,c),inp404(a,b,c),inp405(a,b,c).
null(0):-inp404(a,b,c),inp405(a,b,c),inp406(a,b,c).
null(0):-inp405(a,b,c),inp406(a,b,c),inp407(a,b,c).
null(0):-inp406(a,b,c),inp407(a,b,c),inp408(a,b,c).
null(0):-inp407(a,b,c),inp408(a,b,c),inp409(a,b,c).
null(0):-inp408(a,b,c),inp409(a,b,c),inp410(a,b,c).
null(0):-inp409(a,b,c),inp410(a,b,c),inp411(a,b,c).
null(0):-inp410(a,b,c),inp411(a,b,c),inp412(a,b,c).
null(0):-inp411(a,b,c),inp412(a,b,c),inp413(a,b,c).
null(0):-inp412(a,b,c),inp413(a,b,c),inp414(a,b,c).
null(0):-inp413(a,b,c),inp414(a,b,c),inp415(a,b,c).
null(0):-inp414(a,b,c),inp415(a,b,c),inp416(a,b,c).
null(0):-inp415(a,b,c),inp416(a,b,c),inp417(a,b,c).
null(0):-inp416(a,b,c),inp417(a,b,c),inp418(a,b,c).
null(0):-inp417(a,b,c),inp418(a,b,c),inp419(a,b,c).
null(0):-inp418(a,b,c),inp419(a,b,c),inp420(a,b,c).
null(0):-inp419(a,b,c),inp420(a,b,c),inp421(a,b,c).
null(0):-inp420(a,b,c),inp421(a,b,c),inp422(a,b,c).
null(0):-inp421(a,b,c),inp422(a,b,c),inp423(a,b,c).
null(0):-inp422(a,b,c),inp423(a,b,c),inp424(a,b,c).
null(0):-inp423(a,b,c),inp424(a,b,c),inp425(a,b,c).
null(0):-inp424(a,b,c),inp425(a,b,c),inp426(a,b,c).
null(0):-inp425(a,b,c),inp426(a,b,c),inp427(a,b,c).
null(0):-inp426(a,b,c),inp427(a,b,c),inp428(a,b,c).
null(0):-inp427(a,b,c),inp428(a,b,c),inp429(a,b,c).
null(0):-inp428(a,b,c),inp429(a,b,c),inp430(a,b,c).
null(0):-inp429(a,b,c),inp430(a,b,c),inp431(a,b,c).
null(0):-inp430(a,b,c),inp431(a,b,c),inp432(a,b,c).
null(0):-inp431(a,b,c),inp432(a,b,c),inp433(a,b,c).
null(0):-inp432(a,b,c),inp433(a,b,c),inp434(a,b,c).
null(0):-inp433(a,b,c),inp434(a,b,c),inp435(a,b,c).
null(0):-inp434(a,b,c),inp435(a,b,c),inp436(a,b,c).
null(0):-inp435(a,b,c),inp436(a,b,c),inp437(a,b,c).
null(0):-inp436(a,b,c),inp437(a,b,c),inp438(a,b,c).
null(0):-inp437(a,b,c),inp438(a,b,c),inp439(a,b,c).
null(0):-inp438(a,b,c),inp439(a,b,c),inp440(a,b,c).
null(0):-inp439(a,b,c),inp440(a,b,c),inp441(a,b,c).
null(0):-inp440(a,b,c),inp441(a,b,c),inp442(a,b,c).
null(0):-inp441(a,b,c),inp442(a,b,c),inp443(a,b,c).
null(0):-inp442(a,b,c),inp443(a,b,c),inp444(a,b,c).
null(0):-inp443(a,b,c),inp444(a,b,c),inp445(a,b,c).
null(0):-inp444(a,b,c),inp445(a,b,c),inp446(a,b,c).
null(0):-inp445(a,b,c),inp446(a,b,c),inp447(a,b,c).
null(0):-inp446(a,b,c),inp447(a,b,c),inp448(a,b,c).
null(0):-inp447(a,b,c),inp448(a,b,c),inp449(a,b,c).
null(0):-inp448(a,b,c),inp449(a,b,c),inp450(a,b,c).
null(0):-inp449(a,b,c),inp450(a,b,c),inp451(a,b,c).
null(0):-inp450(a,b,c),inp451(a,b,c),inp452(a,b,c).
null(0):-inp451(a,b,c),inp452(a,b,c),inp453(a,b,c).
null(0):-inp452(a,b,c),inp453(a,b,c),inp454(a,b,c).
null(0):-inp453(a,b,c),inp454(a,b,c),inp455(a,b,c).
null(0):-inp454(a,b,c),inp455(a,b,c),inp456(a,b,c).
null(0):-inp455(a,b,c),inp456(a,b,c),inp457(a,b,c).
null(0):-inp456(a,b,c),inp457(a,b,c),inp458(a,b,c).
null(0):-inp457(a,b,c),inp458(a,b,c),inp459(a,b,c).
null(0):-inp458(a,b,c),inp459(a,b,c),inp460(a,b,c).
null(0):-inp459(a,b,c),inp460(a,b,c),inp461(a,b,c).
null(0):-inp460(a,b,c),inp461(a,b,c),inp462(a,b,c).
null(0):-inp461(a,b,c),inp462(a,b,c),inp463(a,b,c).
null(0):-inp462(a,b,c),inp463(a,b,c),inp464(a,b,c).
null(0):-inp463(a,b,c),inp464(a,b,c),inp465(a,b,c).
null(0):-inp464(a,b,c),inp465(a,b,c),inp466(a,b,c).
null(0):-inp465(a,b,c),inp466(a,b,c),inp467(a,b,c).
null(0):-inp466(a,b,c),inp467(a,b,c),inp468(a,b,c).
null(0):-inp467(a,b,c),inp468(a,b,c),inp469(a,b,c).
null(0):-inp468(a,b,c),inp469(a,b,c),inp470(a,b,c).
null(0):-inp469(a,b,c),inp470(a,b,c),inp471(a,b,c).
null(0):-inp470(a,b,c),inp471(a,b,c),inp472(a,b,c).
null(0):-inp471(a,b,c),inp472(a,b,c),inp473(a,b,c).
null(0):-inp472(a,b,c),inp473(a,b,c),inp474(a,b,c).
null(0):-inp473(a,b,c),inp474(a,b,c),inp475(a,b,c).
null(0):-inp474(a,b,c),inp475(a,b,c),inp476(a,b,c).
null(0):-inp475(a,b,c),inp476(a,b,c),inp477(a,b,c).
null(0):-inp476(a,b,c),inp477(a,b,c),inp478(a,b,c).
null(0):-inp477(a,b,c),inp478(a,b,c),inp479(a,b,c).
null(0):-inp478(a,b,c),inp479(a,b,c),inp480(a,b,c).
null(0):-inp479(a,b,c),inp480(a,b,c),inp481(a,b,c).
null(0):-inp480(a,b,c),inp481(a,b,c),inp482(a,b,c).
null(0):-inp481(a,b,c),inp482(a,b,c),inp483(a,b,c).
null(0):-inp482(a,b,c),inp483(a,b,c),inp484(a,b,c).
null(0):-inp483(a,b,c),inp484(a,b,c),inp485(a,b,c).
null(0):-inp484(a,b,c),inp485(a,b,c),inp486(a,b,c).
null(0):-inp485(a,b,c),inp486(a,b,c),inp487(a,b,c).
null(0):-inp486(a,b,c),inp487(a,b,c),inp488(a,b,c).
null(0):-inp487(a,b,c),inp488(a,b,c),inp489(a,b,c).
null(0):-inp488(a,b,c),inp489(a,b,c),inp490(a,b,c).
null(0):-inp489(a,b,c),inp490(a,b,c),inp491(a,b,c).
null(0):-inp490(a,b,c),inp491(a,b,c),inp492(a,b,c).
null(0):-inp491(a,b,c),inp492(a,b,c),inp493(a,b,c).
null(0):-inp492(a,b,c),inp493(a,b,c),inp494(a,b,c).
null(0):-inp493(a,b,c),inp494(a,b,c),inp495(a,b,c).
null(0):-inp494(a,b,c),inp495(a,b,c),inp496(a,b,c).
null(0):-inp495(a,b,c),inp496(a,b,c),inp497(a,b,c).
null(0):-inp496(a,b,c),inp497(a,b,c),inp498(a,b,c).
null(0):-inp497(a,b,c),inp498(a,b,c),inp499(a,b,c).
null(0):-inp1(a,b,c),funcinp2(a,b,c,d).
null(0):-inp2(a,b,c),funcinp3(a,b,c,d).
null(0):-inp3(a,b,c),funcinp4(a,b,c,d).
null(0):-inp4(a,b,c),funcinp5(a,b,c,d).
null(0):-inp5(a,b,c),funcinp6(a,b,c,d).
null(0):-inp6(a,b,c),funcinp7(a,b,c,d).
null(0):-inp7(a,b,c),funcinp8(a,b,c,d).
null(0):-inp8(a,b,c),funcinp9(a,b,c,d).
null(0):-inp9(a,b,c),funcinp10(a,b,c,d).
null(0):-inp10(a,b,c),funcinp11(a,b,c,d).
null(0):-inp11(a,b,c),funcinp12(a,b,c,d).
null(0):-inp12(a,b,c),funcinp13(a,b,c,d).
null(0):-inp13(a,b,c),funcinp14(a,b,c,d).
null(0):-inp14(a,b,c),funcinp15(a,b,c,d).
null(0):-inp15(a,b,c),funcinp16(a,b,c,d).
null(0):-inp16(a,b,c),funcinp17(a,b,c,d).
null(0):-inp17(a,b,c),funcinp18(a,b,c,d).
null(0):-inp18(a,b,c),funcinp19(a,b,c,d).
null(0):-inp19(a,b,c),funcinp20(a,b,c,d).
null(0):-inp20(a,b,c),funcinp21(a,b,c,d).
null(0):-inp21(a,b,c),funcinp22(a,b,c,d).
null(0):-inp22(a,b,c),funcinp23(a,b,c,d).
null(0):-inp23(a,b,c),funcinp24(a,b,c,d).
null(0):-inp24(a,b,c),funcinp25(a,b,c,d).
null(0):-inp25(a,b,c),funcinp26(a,b,c,d).
null(0):-inp26(a,b,c),funcinp27(a,b,c,d).
null(0):-inp27(a,b,c),funcinp28(a,b,c,d).
null(0):-inp28(a,b,c),funcinp29(a,b,c,d).
null(0):-inp29(a,b,c),funcinp30(a,b,c,d).
null(0):-inp30(a,b,c),funcinp31(a,b,c,d).
null(0):-inp31(a,b,c),funcinp32(a,b,c,d).
null(0):-inp32(a,b,c),funcinp33(a,b,c,d).
null(0):-inp33(a,b,c),funcinp34(a,b,c,d).
null(0):-inp34(a,b,c),funcinp35(a,b,c,d).
null(0):-inp35(a,b,c),funcinp36(a,b,c,d).
null(0):-inp36(a,b,c),funcinp37(a,b,c,d).
null(0):-inp37(a,b,c),funcinp38(a,b,c,d).
null(0):-inp38(a,b,c),funcinp39(a,b,c,d).
null(0):-inp39(a,b,c),funcinp40(a,b,c,d).
null(0):-inp40(a,b,c),funcinp41(a,b,c,d).
null(0):-inp41(a,b,c),funcinp42(a,b,c,d).
null(0):-inp42(a,b,c),funcinp43(a,b,c,d).
null(0):-inp43(a,b,c),funcinp44(a,b,c,d).
null(0):-inp44(a,b,c),funcinp45(a,b,c,d).
null(0):-inp45(a,b,c),funcinp46(a,b,c,d).
null(0):-inp46(a,b,c),funcinp47(a,b,c,d).
null(0):-inp47(a,b,c),funcinp48(a,b,c,d).
null(0):-inp48(a,b,c),funcinp49(a,b,c,d).
null(0):-inp49(a,b,c),funcinp50(a,b,c,d).
null(0):-inp50(a,b,c),funcinp51(a,b,c,d).
null(0):-inp51(a,b,c),funcinp52(a,b,c,d).
null(0):-inp52(a,b,c),funcinp53(a,b,c,d).
null(0):-inp53(a,b,c),funcinp54(a,b,c,d).
null(0):-inp54(a,b,c),funcinp55(a,b,c,d).
null(0):-inp55(a,b,c),funcinp56(a,b,c,d).
null(0):-inp56(a,b,c),funcinp57(a,b,c,d).
null(0):-inp57(a,b,c),funcinp58(a,b,c,d).
null(0):-inp58(a,b,c),funcinp59(a,b,c,d).
null(0):-inp59(a,b,c),funcinp60(a,b,c,d).
null(0):-inp60(a,b,c),funcinp61(a,b,c,d).
null(0):-inp61(a,b,c),funcinp62(a,b,c,d).
null(0):-inp62(a,b,c),funcinp63(a,b,c,d).
null(0):-inp63(a,b,c),funcinp64(a,b,c,d).
null(0):-inp64(a,b,c),funcinp65(a,b,c,d).
null(0):-inp65(a,b,c),funcinp66(a,b,c,d).
null(0):-inp66(a,b,c),funcinp67(a,b,c,d).
null(0):-inp67(a,b,c),funcinp68(a,b,c,d).
null(0):-inp68(a,b,c),funcinp69(a,b,c,d).
null(0):-inp69(a,b,c),funcinp70(a,b,c,d).
null(0):-inp70(a,b,c),funcinp71(a,b,c,d).
null(0):-inp71(a,b,c),funcinp72(a,b,c,d).
null(0):-inp72(a,b,c),funcinp73(a,b,c,d).
null(0):-inp73(a,b,c),funcinp74(a,b,c,d).
null(0):-inp74(a,b,c),funcinp75(a,b,c,d).
null(0):-inp75(a,b,c),funcinp76(a,b,c,d).
null(0):-inp76(a,b,c),funcinp77(a,b,c,d).
null(0):-inp77(a,b,c),funcinp78(a,b,c,d).
null(0):-inp78(a,b,c),funcinp79(a,b,c,d).
null(0):-inp79(a,b,c),funcinp80(a,b,c,d).
null(0):-inp80(a,b,c),funcinp81(a,b,c,d).
null(0):-inp81(a,b,c),funcinp82(a,b,c,d).
null(0):-inp82(a,b,c),funcinp83(a,b,c,d).
null(0):-inp83(a,b,c),funcinp84(a,b,c,d).
null(0):-inp84(a,b,c),funcinp85(a,b,c,d).
null(0):-inp85(a,b,c),funcinp86(a,b,c,d).
null(0):-inp86(a,b,c),funcinp87(a,b,c,d).
null(0):-inp87(a,b,c),funcinp88(a,b,c,d).
null(0):-inp88(a,b,c),funcinp89(a,b,c,d).
null(0):-inp89(a,b,c),funcinp90(a,b,c,d).
null(0):-inp90(a,b,c),funcinp91(a,b,c,d).
null(0):-inp91(a,b,c),funcinp92(a,b,c,d).
null(0):-inp92(a,b,c),funcinp93(a,b,c,d).
null(0):-inp93(a,b,c),funcinp94(a,b,c,d).
null(0):-inp94(a,b,c),funcinp95(a,b,c,d).
null(0):-inp95(a,b,c),funcinp96(a,b,c,d).
null(0):-inp96(a,b,c),funcinp97(a,b,c,d).
null(0):-inp97(a,b,c),funcinp98(a,b,c,d).
null(0):-inp98(a,b,c),funcinp99(a,b,c,d).
null(0):-inp99(a,b,c),funcinp100(a,b,c,d).
null(0):-inp100(a,b,c),funcinp101(a,b,c,d).
null(0):-inp101(a,b,c),funcinp102(a,b,c,d).
null(0):-inp102(a,b,c),funcinp103(a,b,c,d).
null(0):-inp103(a,b,c),funcinp104(a,b,c,d).
null(0):-inp104(a,b,c),funcinp105(a,b,c,d).
null(0):-inp105(a,b,c),funcinp106(a,b,c,d).
null(0):-inp106(a,b,c),funcinp107(a,b,c,d).
null(0):-inp107(a,b,c),funcinp108(a,b,c,d).
null(0):-inp108(a,b,c),funcinp109(a,b,c,d).
null(0):-inp109(a,b,c),funcinp110(a,b,c,d).
null(0):-inp110(a,b,c),funcinp111(a,b,c,d).
null(0):-inp111(a,b,c),funcinp112(a,b,c,d).
null(0):-inp112(a,b,c),funcinp113(a,b,c,d).
null(0):-inp113(a,b,c),funcinp114(a,b,c,d).
null(0):-inp114(a,b,c),funcinp115(a,b,c,d).
null(0):-inp115(a,b,c),funcinp116(a,b,c,d).
null(0):-inp116(a,b,c),funcinp117(a,b,c,d).
null(0):-inp117(a,b,c),funcinp118(a,b,c,d).
null(0):-inp118(a,b,c),funcinp119(a,b,c,d).
null(0):-inp119(a,b,c),funcinp120(a,b,c,d).
null(0):-inp120(a,b,c),funcinp121(a,b,c,d).
null(0):-inp121(a,b,c),funcinp122(a,b,c,d).
null(0):-inp122(a,b,c),funcinp123(a,b,c,d).
null(0):-inp123(a,b,c),funcinp124(a,b,c,d).
null(0):-inp124(a,b,c),funcinp125(a,b,c,d).
null(0):-inp125(a,b,c),funcinp126(a,b,c,d).
null(0):-inp126(a,b,c),funcinp127(a,b,c,d).
null(0):-inp127(a,b,c),funcinp128(a,b,c,d).
null(0):-inp128(a,b,c),funcinp129(a,b,c,d).
null(0):-inp129(a,b,c),funcinp130(a,b,c,d).
null(0):-inp130(a,b,c),funcinp131(a,b,c,d).
null(0):-inp131(a,b,c),funcinp132(a,b,c,d).
null(0):-inp132(a,b,c),funcinp133(a,b,c,d).
null(0):-inp133(a,b,c),funcinp134(a,b,c,d).
null(0):-inp134(a,b,c),funcinp135(a,b,c,d).
null(0):-inp135(a,b,c),funcinp136(a,b,c,d).
null(0):-inp136(a,b,c),funcinp137(a,b,c,d).
null(0):-inp137(a,b,c),funcinp138(a,b,c,d).
null(0):-inp138(a,b,c),funcinp139(a,b,c,d).
null(0):-inp139(a,b,c),funcinp140(a,b,c,d).
null(0):-inp140(a,b,c),funcinp141(a,b,c,d).
null(0):-inp141(a,b,c),funcinp142(a,b,c,d).
null(0):-inp142(a,b,c),funcinp143(a,b,c,d).
null(0):-inp143(a,b,c),funcinp144(a,b,c,d).
null(0):-inp144(a,b,c),funcinp145(a,b,c,d).
null(0):-inp145(a,b,c),funcinp146(a,b,c,d).
null(0):-inp146(a,b,c),funcinp147(a,b,c,d).
null(0):-inp147(a,b,c),funcinp148(a,b,c,d).
null(0):-inp148(a,b,c),funcinp149(a,b,c,d).
null(0):-inp149(a,b,c),funcinp150(a,b,c,d).
null(0):-inp150(a,b,c),funcinp151(a,b,c,d).
null(0):-inp151(a,b,c),funcinp152(a,b,c,d).
null(0):-inp152(a,b,c),funcinp153(a,b,c,d).
null(0):-inp153(a,b,c),funcinp154(a,b,c,d).
null(0):-inp154(a,b,c),funcinp155(a,b,c,d).
null(0):-inp155(a,b,c),funcinp156(a,b,c,d).
null(0):-inp156(a,b,c),funcinp157(a,b,c,d).
null(0):-inp157(a,b,c),funcinp158(a,b,c,d).
null(0):-inp158(a,b,c),funcinp159(a,b,c,d).
null(0):-inp159(a,b,c),funcinp160(a,b,c,d).
null(0):-inp160(a,b,c),funcinp161(a,b,c,d).
null(0):-inp161(a,b,c),funcinp162(a,b,c,d).
null(0):-inp162(a,b,c),funcinp163(a,b,c,d).
null(0):-inp163(a,b,c),funcinp164(a,b,c,d).
null(0):-inp164(a,b,c),funcinp165(a,b,c,d).
null(0):-inp165(a,b,c),funcinp166(a,b,c,d).
null(0):-inp166(a,b,c),funcinp167(a,b,c,d).
null(0):-inp167(a,b,c),funcinp168(a,b,c,d).
null(0):-inp168(a,b,c),funcinp169(a,b,c,d).
null(0):-inp169(a,b,c),funcinp170(a,b,c,d).
null(0):-inp170(a,b,c),funcinp171(a,b,c,d).
null(0):-inp171(a,b,c),funcinp172(a,b,c,d).
null(0):-inp172(a,b,c),funcinp173(a,b,c,d).
null(0):-inp173(a,b,c),funcinp174(a,b,c,d).
null(0):-inp174(a,b,c),funcinp175(a,b,c,d).
null(0):-inp175(a,b,c),funcinp176(a,b,c,d).
null(0):-inp176(a,b,c),funcinp177(a,b,c,d).
null(0):-inp177(a,b,c),funcinp178(a,b,c,d).
null(0):-inp178(a,b,c),funcinp179(a,b,c,d).
null(0):-inp179(a,b,c),funcinp180(a,b,c,d).
null(0):-inp180(a,b,c),funcinp181(a,b,c,d).
null(0):-inp181(a,b,c),funcinp182(a,b,c,d).
null(0):-inp182(a,b,c),funcinp183(a,b,c,d).
null(0):-inp183(a,b,c),funcinp184(a,b,c,d).
null(0):-inp184(a,b,c),funcinp185(a,b,c,d).
null(0):-inp185(a,b,c),funcinp186(a,b,c,d).
null(0):-inp186(a,b,c),funcinp187(a,b,c,d).
null(0):-inp187(a,b,c),funcinp188(a,b,c,d).
null(0):-inp188(a,b,c),funcinp189(a,b,c,d).
null(0):-inp189(a,b,c),funcinp190(a,b,c,d).
null(0):-inp190(a,b,c),funcinp191(a,b,c,d).
null(0):-inp191(a,b,c),funcinp192(a,b,c,d).
null(0):-inp192(a,b,c),funcinp193(a,b,c,d).
null(0):-inp193(a,b,c),funcinp194(a,b,c,d).
null(0):-inp194(a,b,c),funcinp195(a,b,c,d).
null(0):-inp195(a,b,c),funcinp196(a,b,c,d).
null(0):-inp196(a,b,c),funcinp197(a,b,c,d).
null(0):-inp197(a,b,c),funcinp198(a,b,c,d).
null(0):-inp198(a,b,c),funcinp199(a,b,c,d).
null(0):-inp199(a,b,c),funcinp200(a,b,c,d).
null(0):-inp200(a,b,c),funcinp201(a,b,c,d).
null(0):-inp201(a,b,c),funcinp202(a,b,c,d).
null(0):-inp202(a,b,c),funcinp203(a,b,c,d).
null(0):-inp203(a,b,c),funcinp204(a,b,c,d).
null(0):-inp204(a,b,c),funcinp205(a,b,c,d).
null(0):-inp205(a,b,c),funcinp206(a,b,c,d).
null(0):-inp206(a,b,c),funcinp207(a,b,c,d).
null(0):-inp207(a,b,c),funcinp208(a,b,c,d).
null(0):-inp208(a,b,c),funcinp209(a,b,c,d).
null(0):-inp209(a,b,c),funcinp210(a,b,c,d).
null(0):-inp210(a,b,c),funcinp211(a,b,c,d).
null(0):-inp211(a,b,c),funcinp212(a,b,c,d).
null(0):-inp212(a,b,c),funcinp213(a,b,c,d).
null(0):-inp213(a,b,c),funcinp214(a,b,c,d).
null(0):-inp214(a,b,c),funcinp215(a,b,c,d).
null(0):-inp215(a,b,c),funcinp216(a,b,c,d).
null(0):-inp216(a,b,c),funcinp217(a,b,c,d).
null(0):-inp217(a,b,c),funcinp218(a,b,c,d).
null(0):-inp218(a,b,c),funcinp219(a,b,c,d).
null(0):-inp219(a,b,c),funcinp220(a,b,c,d).
null(0):-inp220(a,b,c),funcinp221(a,b,c,d).
null(0):-inp221(a,b,c),funcinp222(a,b,c,d).
null(0):-inp222(a,b,c),funcinp223(a,b,c,d).
null(0):-inp223(a,b,c),funcinp224(a,b,c,d).
null(0):-inp224(a,b,c),funcinp225(a,b,c,d).
null(0):-inp225(a,b,c),funcinp226(a,b,c,d).
null(0):-inp226(a,b,c),funcinp227(a,b,c,d).
null(0):-inp227(a,b,c),funcinp228(a,b,c,d).
null(0):-inp228(a,b,c),funcinp229(a,b,c,d).
null(0):-inp229(a,b,c),funcinp230(a,b,c,d).
null(0):-inp230(a,b,c),funcinp231(a,b,c,d).
null(0):-inp231(a,b,c),funcinp232(a,b,c,d).
null(0):-inp232(a,b,c),funcinp233(a,b,c,d).
null(0):-inp233(a,b,c),funcinp234(a,b,c,d).
null(0):-inp234(a,b,c),funcinp235(a,b,c,d).
null(0):-inp235(a,b,c),funcinp236(a,b,c,d).
null(0):-inp236(a,b,c),funcinp237(a,b,c,d).
null(0):-inp237(a,b,c),funcinp238(a,b,c,d).
null(0):-inp238(a,b,c),funcinp239(a,b,c,d).
null(0):-inp239(a,b,c),funcinp240(a,b,c,d).
null(0):-inp240(a,b,c),funcinp241(a,b,c,d).
null(0):-inp241(a,b,c),funcinp242(a,b,c,d).
null(0):-inp242(a,b,c),funcinp243(a,b,c,d).
null(0):-inp243(a,b,c),funcinp244(a,b,c,d).
null(0):-inp244(a,b,c),funcinp245(a,b,c,d).
null(0):-inp245(a,b,c),funcinp246(a,b,c,d).
null(0):-inp246(a,b,c),funcinp247(a,b,c,d).
null(0):-inp247(a,b,c),funcinp248(a,b,c,d).
null(0):-inp248(a,b,c),funcinp249(a,b,c,d).
null(0):-inp249(a,b,c),funcinp250(a,b,c,d).
null(0):-inp250(a,b,c),funcinp251(a,b,c,d).
null(0):-inp251(a,b,c),funcinp252(a,b,c,d).
null(0):-inp252(a,b,c),funcinp253(a,b,c,d).
null(0):-inp253(a,b,c),funcinp254(a,b,c,d).
null(0):-inp254(a,b,c),funcinp255(a,b,c,d).
null(0):-inp255(a,b,c),funcinp256(a,b,c,d).
null(0):-inp256(a,b,c),funcinp257(a,b,c,d).
null(0):-inp257(a,b,c),funcinp258(a,b,c,d).
null(0):-inp258(a,b,c),funcinp259(a,b,c,d).
null(0):-inp259(a,b,c),funcinp260(a,b,c,d).
null(0):-inp260(a,b,c),funcinp261(a,b,c,d).
null(0):-inp261(a,b,c),funcinp262(a,b,c,d).
null(0):-inp262(a,b,c),funcinp263(a,b,c,d).
null(0):-inp263(a,b,c),funcinp264(a,b,c,d).
null(0):-inp264(a,b,c),funcinp265(a,b,c,d).
null(0):-inp265(a,b,c),funcinp266(a,b,c,d).
null(0):-inp266(a,b,c),funcinp267(a,b,c,d).
null(0):-inp267(a,b,c),funcinp268(a,b,c,d).
null(0):-inp268(a,b,c),funcinp269(a,b,c,d).
null(0):-inp269(a,b,c),funcinp270(a,b,c,d).
null(0):-inp270(a,b,c),funcinp271(a,b,c,d).
null(0):-inp271(a,b,c),funcinp272(a,b,c,d).
null(0):-inp272(a,b,c),funcinp273(a,b,c,d).
null(0):-inp273(a,b,c),funcinp274(a,b,c,d).
null(0):-inp274(a,b,c),funcinp275(a,b,c,d).
null(0):-inp275(a,b,c),funcinp276(a,b,c,d).
null(0):-inp276(a,b,c),funcinp277(a,b,c,d).
null(0):-inp277(a,b,c),funcinp278(a,b,c,d).
null(0):-inp278(a,b,c),funcinp279(a,b,c,d).
null(0):-inp279(a,b,c),funcinp280(a,b,c,d).
null(0):-inp280(a,b,c),funcinp281(a,b,c,d).
null(0):-inp281(a,b,c),funcinp282(a,b,c,d).
null(0):-inp282(a,b,c),funcinp283(a,b,c,d).
null(0):-inp283(a,b,c),funcinp284(a,b,c,d).
null(0):-inp284(a,b,c),funcinp285(a,b,c,d).
null(0):-inp285(a,b,c),funcinp286(a,b,c,d).
null(0):-inp286(a,b,c),funcinp287(a,b,c,d).
null(0):-inp287(a,b,c),funcinp288(a,b,c,d).
null(0):-inp288(a,b,c),funcinp289(a,b,c,d).
null(0):-inp289(a,b,c),funcinp290(a,b,c,d).
null(0):-inp290(a,b,c),funcinp291(a,b,c,d).
null(0):-inp291(a,b,c),funcinp292(a,b,c,d).
null(0):-inp292(a,b,c),funcinp293(a,b,c,d).
null(0):-inp293(a,b,c),funcinp294(a,b,c,d).
null(0):-inp294(a,b,c),funcinp295(a,b,c,d).
null(0):-inp295(a,b,c),funcinp296(a,b,c,d).
null(0):-inp296(a,b,c),funcinp297(a,b,c,d).
null(0):-inp297(a,b,c),funcinp298(a,b,c,d).
null(0):-inp298(a,b,c),funcinp299(a,b,c,d).
null(0):-inp299(a,b,c),funcinp300(a,b,c,d).
null(0):-inp300(a,b,c),funcinp301(a,b,c,d).
null(0):-inp301(a,b,c),funcinp302(a,b,c,d).
null(0):-inp302(a,b,c),funcinp303(a,b,c,d).
null(0):-inp303(a,b,c),funcinp304(a,b,c,d).
null(0):-inp304(a,b,c),funcinp305(a,b,c,d).
null(0):-inp305(a,b,c),funcinp306(a,b,c,d).
null(0):-inp306(a,b,c),funcinp307(a,b,c,d).
null(0):-inp307(a,b,c),funcinp308(a,b,c,d).
null(0):-inp308(a,b,c),funcinp309(a,b,c,d).
null(0):-inp309(a,b,c),funcinp310(a,b,c,d).
null(0):-inp310(a,b,c),funcinp311(a,b,c,d).
null(0):-inp311(a,b,c),funcinp312(a,b,c,d).
null(0):-inp312(a,b,c),funcinp313(a,b,c,d).
null(0):-inp313(a,b,c),funcinp314(a,b,c,d).
null(0):-inp314(a,b,c),funcinp315(a,b,c,d).
null(0):-inp315(a,b,c),funcinp316(a,b,c,d).
null(0):-inp316(a,b,c),funcinp317(a,b,c,d).
null(0):-inp317(a,b,c),funcinp318(a,b,c,d).
null(0):-inp318(a,b,c),funcinp319(a,b,c,d).
null(0):-inp319(a,b,c),funcinp320(a,b,c,d).
null(0):-inp320(a,b,c),funcinp321(a,b,c,d).
null(0):-inp321(a,b,c),funcinp322(a,b,c,d).
null(0):-inp322(a,b,c),funcinp323(a,b,c,d).
null(0):-inp323(a,b,c),funcinp324(a,b,c,d).
null(0):-inp324(a,b,c),funcinp325(a,b,c,d).
null(0):-inp325(a,b,c),funcinp326(a,b,c,d).
null(0):-inp326(a,b,c),funcinp327(a,b,c,d).
null(0):-inp327(a,b,c),funcinp328(a,b,c,d).
null(0):-inp328(a,b,c),funcinp329(a,b,c,d).
null(0):-inp329(a,b,c),funcinp330(a,b,c,d).
null(0):-inp330(a,b,c),funcinp331(a,b,c,d).
null(0):-inp331(a,b,c),funcinp332(a,b,c,d).
null(0):-inp332(a,b,c),funcinp333(a,b,c,d).
null(0):-inp333(a,b,c),funcinp334(a,b,c,d).
null(0):-inp334(a,b,c),funcinp335(a,b,c,d).
null(0):-inp335(a,b,c),funcinp336(a,b,c,d).
null(0):-inp336(a,b,c),funcinp337(a,b,c,d).
null(0):-inp337(a,b,c),funcinp338(a,b,c,d).
null(0):-inp338(a,b,c),funcinp339(a,b,c,d).
null(0):-inp339(a,b,c),funcinp340(a,b,c,d).
null(0):-inp340(a,b,c),funcinp341(a,b,c,d).
null(0):-inp341(a,b,c),funcinp342(a,b,c,d).
null(0):-inp342(a,b,c),funcinp343(a,b,c,d).
null(0):-inp343(a,b,c),funcinp344(a,b,c,d).
null(0):-inp344(a,b,c),funcinp345(a,b,c,d).
null(0):-inp345(a,b,c),funcinp346(a,b,c,d).
null(0):-inp346(a,b,c),funcinp347(a,b,c,d).
null(0):-inp347(a,b,c),funcinp348(a,b,c,d).
null(0):-inp348(a,b,c),funcinp349(a,b,c,d).
null(0):-inp349(a,b,c),funcinp350(a,b,c,d).
null(0):-inp350(a,b,c),funcinp351(a,b,c,d).
null(0):-inp351(a,b,c),funcinp352(a,b,c,d).
null(0):-inp352(a,b,c),funcinp353(a,b,c,d).
null(0):-inp353(a,b,c),funcinp354(a,b,c,d).
null(0):-inp354(a,b,c),funcinp355(a,b,c,d).
null(0):-inp355(a,b,c),funcinp356(a,b,c,d).
null(0):-inp356(a,b,c),funcinp357(a,b,c,d).
null(0):-inp357(a,b,c),funcinp358(a,b,c,d).
null(0):-inp358(a,b,c),funcinp359(a,b,c,d).
null(0):-inp359(a,b,c),funcinp360(a,b,c,d).
null(0):-inp360(a,b,c),funcinp361(a,b,c,d).
null(0):-inp361(a,b,c),funcinp362(a,b,c,d).
null(0):-inp362(a,b,c),funcinp363(a,b,c,d).
null(0):-inp363(a,b,c),funcinp364(a,b,c,d).
null(0):-inp364(a,b,c),funcinp365(a,b,c,d).
null(0):-inp365(a,b,c),funcinp366(a,b,c,d).
null(0):-inp366(a,b,c),funcinp367(a,b,c,d).
null(0):-inp367(a,b,c),funcinp368(a,b,c,d).
null(0):-inp368(a,b,c),funcinp369(a,b,c,d).
null(0):-inp369(a,b,c),funcinp370(a,b,c,d).
null(0):-inp370(a,b,c),funcinp371(a,b,c,d).
null(0):-inp371(a,b,c),funcinp372(a,b,c,d).
null(0):-inp372(a,b,c),funcinp373(a,b,c,d).
null(0):-inp373(a,b,c),funcinp374(a,b,c,d).
null(0):-inp374(a,b,c),funcinp375(a,b,c,d).
null(0):-inp375(a,b,c),funcinp376(a,b,c,d).
null(0):-inp376(a,b,c),funcinp377(a,b,c,d).
null(0):-inp377(a,b,c),funcinp378(a,b,c,d).
null(0):-inp378(a,b,c),funcinp379(a,b,c,d).
null(0):-inp379(a,b,c),funcinp380(a,b,c,d).
null(0):-inp380(a,b,c),funcinp381(a,b,c,d).
null(0):-inp381(a,b,c),funcinp382(a,b,c,d).
null(0):-inp382(a,b,c),funcinp383(a,b,c,d).
null(0):-inp383(a,b,c),funcinp384(a,b,c,d).
null(0):-inp384(a,b,c),funcinp385(a,b,c,d).
null(0):-inp385(a,b,c),funcinp386(a,b,c,d).
null(0):-inp386(a,b,c),funcinp387(a,b,c,d).
null(0):-inp387(a,b,c),funcinp388(a,b,c,d).
null(0):-inp388(a,b,c),funcinp389(a,b,c,d).
null(0):-inp389(a,b,c),funcinp390(a,b,c,d).
null(0):-inp390(a,b,c),funcinp391(a,b,c,d).
null(0):-inp391(a,b,c),funcinp392(a,b,c,d).
null(0):-inp392(a,b,c),funcinp393(a,b,c,d).
null(0):-inp393(a,b,c),funcinp394(a,b,c,d).
null(0):-inp394(a,b,c),funcinp395(a,b,c,d).
null(0):-inp395(a,b,c),funcinp396(a,b,c,d).
null(0):-inp396(a,b,c),funcinp397(a,b,c,d).
null(0):-inp397(a,b,c),funcinp398(a,b,c,d).
null(0):-inp398(a,b,c),funcinp399(a,b,c,d).
null(0):-inp399(a,b,c),funcinp400(a,b,c,d).
null(0):-inp400(a,b,c),funcinp401(a,b,c,d).
null(0):-inp401(a,b,c),funcinp402(a,b,c,d).
null(0):-inp402(a,b,c),funcinp403(a,b,c,d).
null(0):-inp403(a,b,c),funcinp404(a,b,c,d).
null(0):-inp404(a,b,c),funcinp405(a,b,c,d).
null(0):-inp405(a,b,c),funcinp406(a,b,c,d).
null(0):-inp406(a,b,c),funcinp407(a,b,c,d).
null(0):-inp407(a,b,c),funcinp408(a,b,c,d).
null(0):-inp408(a,b,c),funcinp409(a,b,c,d).
null(0):-inp409(a,b,c),funcinp410(a,b,c,d).
null(0):-inp410(a,b,c),funcinp411(a,b,c,d).
null(0):-inp411(a,b,c),funcinp412(a,b,c,d).
null(0):-inp412(a,b,c),funcinp413(a,b,c,d).
null(0):-inp413(a,b,c),funcinp414(a,b,c,d).
null(0):-inp414(a,b,c),funcinp415(a,b,c,d).
null(0):-inp415(a,b,c),funcinp416(a,b,c,d).
null(0):-inp416(a,b,c),funcinp417(a,b,c,d).
null(0):-inp417(a,b,c),funcinp418(a,b,c,d).
null(0):-inp418(a,b,c),funcinp419(a,b,c,d).
null(0):-inp419(a,b,c),funcinp420(a,b,c,d).
null(0):-inp420(a,b,c),funcinp421(a,b,c,d).
null(0):-inp421(a,b,c),funcinp422(a,b,c,d).
null(0):-inp422(a,b,c),funcinp423(a,b,c,d).
null(0):-inp423(a,b,c),funcinp424(a,b,c,d).
null(0):-inp424(a,b,c),funcinp425(a,b,c,d).
null(0):-inp425(a,b,c),funcinp426(a,b,c,d).
null(0):-inp426(a,b,c),funcinp427(a,b,c,d).
null(0):-inp427(a,b,c),funcinp428(a,b,c,d).
null(0):-inp428(a,b,c),funcinp429(a,b,c,d).
null(0):-inp429(a,b,c),funcinp430(a,b,c,d).
null(0):-inp430(a,b,c),funcinp431(a,b,c,d).
null(0):-inp431(a,b,c),funcinp432(a,b,c,d).
null(0):-inp432(a,b,c),funcinp433(a,b,c,d).
null(0):-inp433(a,b,c),funcinp434(a,b,c,d).
null(0):-inp434(a,b,c),funcinp435(a,b,c,d).
null(0):-inp435(a,b,c),funcinp436(a,b,c,d).
null(0):-inp436(a,b,c),funcinp437(a,b,c,d).
null(0):-inp437(a,b,c),funcinp438(a,b,c,d).
null(0):-inp438(a,b,c),funcinp439(a,b,c,d).
null(0):-inp439(a,b,c),funcinp440(a,b,c,d).
null(0):-inp440(a,b,c),funcinp441(a,b,c,d).
null(0):-inp441(a,b,c),funcinp442(a,b,c,d).
null(0):-inp442(a,b,c),funcinp443(a,b,c,d).
null(0):-inp443(a,b,c),funcinp444(a,b,c,d).
null(0):-inp444(a,b,c),funcinp445(a,b,c,d).
null(0):-inp445(a,b,c),funcinp446(a,b,c,d).
null(0):-inp446(a,b,c),funcinp447(a,b,c,d).
null(0):-inp447(a,b,c),funcinp448(a,b,c,d).
null(0):-inp448(a,b,c),funcinp449(a,b,c,d).
null(0):-inp449(a,b,c),funcinp450(a,b,c,d).
null(0):-inp450(a,b,c),funcinp451(a,b,c,d).
null(0):-inp451(a,b,c),funcinp452(a,b,c,d).
null(0):-inp452(a,b,c),funcinp453(a,b,c,d).
null(0):-inp453(a,b,c),funcinp454(a,b,c,d).
null(0):-inp454(a,b,c),funcinp455(a,b,c,d).
null(0):-inp455(a,b,c),funcinp456(a,b,c,d).
null(0):-inp456(a,b,c),funcinp457(a,b,c,d).
null(0):-inp457(a,b,c),funcinp458(a,b,c,d).
null(0):-inp458(a,b,c),funcinp459(a,b,c,d).
null(0):-inp459(a,b,c),funcinp460(a,b,c,d).
null(0):-inp460(a,b,c),funcinp461(a,b,c,d).
null(0):-inp461(a,b,c),funcinp462(a,b,c,d).
null(0):-inp462(a,b,c),funcinp463(a,b,c,d).
null(0):-inp463(a,b,c),funcinp464(a,b,c,d).
null(0):-inp464(a,b,c),funcinp465(a,b,c,d).
null(0):-inp465(a,b,c),funcinp466(a,b,c,d).
null(0):-inp466(a,b,c),funcinp467(a,b,c,d).
null(0):-inp467(a,b,c),funcinp468(a,b,c,d).
null(0):-inp468(a,b,c),funcinp469(a,b,c,d).
null(0):-inp469(a,b,c),funcinp470(a,b,c,d).
null(0):-inp470(a,b,c),funcinp471(a,b,c,d).
null(0):-inp471(a,b,c),funcinp472(a,b,c,d).
null(0):-inp472(a,b,c),funcinp473(a,b,c,d).
null(0):-inp473(a,b,c),funcinp474(a,b,c,d).
null(0):-inp474(a,b,c),funcinp475(a,b,c,d).
null(0):-inp475(a,b,c),funcinp476(a,b,c,d).
null(0):-inp476(a,b,c),funcinp477(a,b,c,d).
null(0):-inp477(a,b,c),funcinp478(a,b,c,d).
null(0):-inp478(a,b,c),funcinp479(a,b,c,d).
null(0):-inp479(a,b,c),funcinp480(a,b,c,d).
null(0):-inp480(a,b,c),funcinp481(a,b,c,d).
null(0):-inp481(a,b,c),funcinp482(a,b,c,d).
null(0):-inp482(a,b,c),funcinp483(a,b,c,d).
null(0):-inp483(a,b,c),funcinp484(a,b,c,d).
null(0):-inp484(a,b,c),funcinp485(a,b,c,d).
null(0):-inp485(a,b,c),funcinp486(a,b,c,d).
null(0):-inp486(a,b,c),funcinp487(a,b,c,d).
null(0):-inp487(a,b,c),funcinp488(a,b,c,d).
null(0):-inp488(a,b,c),funcinp489(a,b,c,d).
null(0):-inp489(a,b,c),funcinp490(a,b,c,d).
null(0):-inp490(a,b,c),funcinp491(a,b,c,d).
null(0):-inp491(a,b,c),funcinp492(a,b,c,d).
null(0):-inp492(a,b,c),funcinp493(a,b,c,d).
null(0):-inp493(a,b,c),funcinp494(a,b,c,d).
null(0):-inp494(a,b,c),funcinp495(a,b,c,d).
null(0):-inp495(a,b,c),funcinp496(a,b,c,d).
null(0):-inp496(a,b,c),funcinp497(a,b,c,d).
null(0):-inp497(a,b,c),funcinp498(a,b,c,d).
null(0):-inp498(a,b,c),funcinp499(a,b,c,d).
null(0):-inp499(a,b,c),funcinp500(a,b,c,d).
null(0):-inp1(a,b,c),funcinp3(a,b,c,d).
null(0):-inp2(a,b,c),funcinp4(a,b,c,d).
null(0):-inp3(a,b,c),funcinp5(a,b,c,d).
null(0):-inp4(a,b,c),funcinp6(a,b,c,d).
null(0):-inp5(a,b,c),funcinp7(a,b,c,d).
null(0):-inp6(a,b,c),funcinp8(a,b,c,d).
null(0):-inp7(a,b,c),funcinp9(a,b,c,d).
null(0):-inp8(a,b,c),funcinp10(a,b,c,d).
null(0):-inp9(a,b,c),funcinp11(a,b,c,d).
null(0):-inp10(a,b,c),funcinp12(a,b,c,d).
null(0):-inp11(a,b,c),funcinp13(a,b,c,d).
null(0):-inp12(a,b,c),funcinp14(a,b,c,d).
null(0):-inp13(a,b,c),funcinp15(a,b,c,d).
null(0):-inp14(a,b,c),funcinp16(a,b,c,d).
null(0):-inp15(a,b,c),funcinp17(a,b,c,d).
null(0):-inp16(a,b,c),funcinp18(a,b,c,d).
null(0):-inp17(a,b,c),funcinp19(a,b,c,d).
null(0):-inp18(a,b,c),funcinp20(a,b,c,d).
null(0):-inp19(a,b,c),funcinp21(a,b,c,d).
null(0):-inp20(a,b,c),funcinp22(a,b,c,d).
null(0):-inp21(a,b,c),funcinp23(a,b,c,d).
null(0):-inp22(a,b,c),funcinp24(a,b,c,d).
null(0):-inp23(a,b,c),funcinp25(a,b,c,d).
null(0):-inp24(a,b,c),funcinp26(a,b,c,d).
null(0):-inp25(a,b,c),funcinp27(a,b,c,d).
null(0):-inp26(a,b,c),funcinp28(a,b,c,d).
null(0):-inp27(a,b,c),funcinp29(a,b,c,d).
null(0):-inp28(a,b,c),funcinp30(a,b,c,d).
null(0):-inp29(a,b,c),funcinp31(a,b,c,d).
null(0):-inp30(a,b,c),funcinp32(a,b,c,d).
null(0):-inp31(a,b,c),funcinp33(a,b,c,d).
null(0):-inp32(a,b,c),funcinp34(a,b,c,d).
null(0):-inp33(a,b,c),funcinp35(a,b,c,d).
null(0):-inp34(a,b,c),funcinp36(a,b,c,d).
null(0):-inp35(a,b,c),funcinp37(a,b,c,d).
null(0):-inp36(a,b,c),funcinp38(a,b,c,d).
null(0):-inp37(a,b,c),funcinp39(a,b,c,d).
null(0):-inp38(a,b,c),funcinp40(a,b,c,d).
null(0):-inp39(a,b,c),funcinp41(a,b,c,d).
null(0):-inp40(a,b,c),funcinp42(a,b,c,d).
null(0):-inp41(a,b,c),funcinp43(a,b,c,d).
null(0):-inp42(a,b,c),funcinp44(a,b,c,d).
null(0):-inp43(a,b,c),funcinp45(a,b,c,d).
null(0):-inp44(a,b,c),funcinp46(a,b,c,d).
null(0):-inp45(a,b,c),funcinp47(a,b,c,d).
null(0):-inp46(a,b,c),funcinp48(a,b,c,d).
null(0):-inp47(a,b,c),funcinp49(a,b,c,d).
null(0):-inp48(a,b,c),funcinp50(a,b,c,d).
null(0):-inp49(a,b,c),funcinp51(a,b,c,d).
null(0):-inp50(a,b,c),funcinp52(a,b,c,d).
null(0):-inp51(a,b,c),funcinp53(a,b,c,d).
null(0):-inp52(a,b,c),funcinp54(a,b,c,d).
null(0):-inp53(a,b,c),funcinp55(a,b,c,d).
null(0):-inp54(a,b,c),funcinp56(a,b,c,d).
null(0):-inp55(a,b,c),funcinp57(a,b,c,d).
null(0):-inp56(a,b,c),funcinp58(a,b,c,d).
null(0):-inp57(a,b,c),funcinp59(a,b,c,d).
null(0):-inp58(a,b,c),funcinp60(a,b,c,d).
null(0):-inp59(a,b,c),funcinp61(a,b,c,d).
null(0):-inp60(a,b,c),funcinp62(a,b,c,d).
null(0):-inp61(a,b,c),funcinp63(a,b,c,d).
null(0):-inp62(a,b,c),funcinp64(a,b,c,d).
null(0):-inp63(a,b,c),funcinp65(a,b,c,d).
null(0):-inp64(a,b,c),funcinp66(a,b,c,d).
null(0):-inp65(a,b,c),funcinp67(a,b,c,d).
null(0):-inp66(a,b,c),funcinp68(a,b,c,d).
null(0):-inp67(a,b,c),funcinp69(a,b,c,d).
null(0):-inp68(a,b,c),funcinp70(a,b,c,d).
null(0):-inp69(a,b,c),funcinp71(a,b,c,d).
null(0):-inp70(a,b,c),funcinp72(a,b,c,d).
null(0):-inp71(a,b,c),funcinp73(a,b,c,d).
null(0):-inp72(a,b,c),funcinp74(a,b,c,d).
null(0):-inp73(a,b,c),funcinp75(a,b,c,d).
null(0):-inp74(a,b,c),funcinp76(a,b,c,d).
null(0):-inp75(a,b,c),funcinp77(a,b,c,d).
null(0):-inp76(a,b,c),funcinp78(a,b,c,d).
null(0):-inp77(a,b,c),funcinp79(a,b,c,d).
null(0):-inp78(a,b,c),funcinp80(a,b,c,d).
null(0):-inp79(a,b,c),funcinp81(a,b,c,d).
null(0):-inp80(a,b,c),funcinp82(a,b,c,d).
null(0):-inp81(a,b,c),funcinp83(a,b,c,d).
null(0):-inp82(a,b,c),funcinp84(a,b,c,d).
null(0):-inp83(a,b,c),funcinp85(a,b,c,d).
null(0):-inp84(a,b,c),funcinp86(a,b,c,d).
null(0):-inp85(a,b,c),funcinp87(a,b,c,d).
null(0):-inp86(a,b,c),funcinp88(a,b,c,d).
null(0):-inp87(a,b,c),funcinp89(a,b,c,d).
null(0):-inp88(a,b,c),funcinp90(a,b,c,d).
null(0):-inp89(a,b,c),funcinp91(a,b,c,d).
null(0):-inp90(a,b,c),funcinp92(a,b,c,d).
null(0):-inp91(a,b,c),funcinp93(a,b,c,d).
null(0):-inp92(a,b,c),funcinp94(a,b,c,d).
null(0):-inp93(a,b,c),funcinp95(a,b,c,d).
null(0):-inp94(a,b,c),funcinp96(a,b,c,d).
null(0):-inp95(a,b,c),funcinp97(a,b,c,d).
null(0):-inp96(a,b,c),funcinp98(a,b,c,d).
null(0):-inp97(a,b,c),funcinp99(a,b,c,d).
null(0):-inp98(a,b,c),funcinp100(a,b,c,d).
null(0):-inp99(a,b,c),funcinp101(a,b,c,d).
null(0):-inp100(a,b,c),funcinp102(a,b,c,d).
null(0):-inp101(a,b,c),funcinp103(a,b,c,d).
null(0):-inp102(a,b,c),funcinp104(a,b,c,d).
null(0):-inp103(a,b,c),funcinp105(a,b,c,d).
null(0):-inp104(a,b,c),funcinp106(a,b,c,d).
null(0):-inp105(a,b,c),funcinp107(a,b,c,d).
null(0):-inp106(a,b,c),funcinp108(a,b,c,d).
null(0):-inp107(a,b,c),funcinp109(a,b,c,d).
null(0):-inp108(a,b,c),funcinp110(a,b,c,d).
null(0):-inp109(a,b,c),funcinp111(a,b,c,d).
null(0):-inp110(a,b,c),funcinp112(a,b,c,d).
null(0):-inp111(a,b,c),funcinp113(a,b,c,d).
null(0):-inp112(a,b,c),funcinp114(a,b,c,d).
null(0):-inp113(a,b,c),funcinp115(a,b,c,d).
null(0):-inp114(a,b,c),funcinp116(a,b,c,d).
null(0):-inp115(a,b,c),funcinp117(a,b,c,d).
null(0):-inp116(a,b,c),funcinp118(a,b,c,d).
null(0):-inp117(a,b,c),funcinp119(a,b,c,d).
null(0):-inp118(a,b,c),funcinp120(a,b,c,d).
null(0):-inp119(a,b,c),funcinp121(a,b,c,d).
null(0):-inp120(a,b,c),funcinp122(a,b,c,d).
null(0):-inp121(a,b,c),funcinp123(a,b,c,d).
null(0):-inp122(a,b,c),funcinp124(a,b,c,d).
null(0):-inp123(a,b,c),funcinp125(a,b,c,d).
null(0):-inp124(a,b,c),funcinp126(a,b,c,d).
null(0):-inp125(a,b,c),funcinp127(a,b,c,d).
null(0):-inp126(a,b,c),funcinp128(a,b,c,d).
null(0):-inp127(a,b,c),funcinp129(a,b,c,d).
null(0):-inp128(a,b,c),funcinp130(a,b,c,d).
null(0):-inp129(a,b,c),funcinp131(a,b,c,d).
null(0):-inp130(a,b,c),funcinp132(a,b,c,d).
null(0):-inp131(a,b,c),funcinp133(a,b,c,d).
null(0):-inp132(a,b,c),funcinp134(a,b,c,d).
null(0):-inp133(a,b,c),funcinp135(a,b,c,d).
null(0):-inp134(a,b,c),funcinp136(a,b,c,d).
null(0):-inp135(a,b,c),funcinp137(a,b,c,d).
null(0):-inp136(a,b,c),funcinp138(a,b,c,d).
null(0):-inp137(a,b,c),funcinp139(a,b,c,d).
null(0):-inp138(a,b,c),funcinp140(a,b,c,d).
null(0):-inp139(a,b,c),funcinp141(a,b,c,d).
null(0):-inp140(a,b,c),funcinp142(a,b,c,d).
null(0):-inp141(a,b,c),funcinp143(a,b,c,d).
null(0):-inp142(a,b,c),funcinp144(a,b,c,d).
null(0):-inp143(a,b,c),funcinp145(a,b,c,d).
null(0):-inp144(a,b,c),funcinp146(a,b,c,d).
null(0):-inp145(a,b,c),funcinp147(a,b,c,d).
null(0):-inp146(a,b,c),funcinp148(a,b,c,d).
null(0):-inp147(a,b,c),funcinp149(a,b,c,d).
null(0):-inp148(a,b,c),funcinp150(a,b,c,d).
null(0):-inp149(a,b,c),funcinp151(a,b,c,d).
null(0):-inp150(a,b,c),funcinp152(a,b,c,d).
null(0):-inp151(a,b,c),funcinp153(a,b,c,d).
null(0):-inp152(a,b,c),funcinp154(a,b,c,d).
null(0):-inp153(a,b,c),funcinp155(a,b,c,d).
null(0):-inp154(a,b,c),funcinp156(a,b,c,d).
null(0):-inp155(a,b,c),funcinp157(a,b,c,d).
null(0):-inp156(a,b,c),funcinp158(a,b,c,d).
null(0):-inp157(a,b,c),funcinp159(a,b,c,d).
null(0):-inp158(a,b,c),funcinp160(a,b,c,d).
null(0):-inp159(a,b,c),funcinp161(a,b,c,d).
null(0):-inp160(a,b,c),funcinp162(a,b,c,d).
null(0):-inp161(a,b,c),funcinp163(a,b,c,d).
null(0):-inp162(a,b,c),funcinp164(a,b,c,d).
null(0):-inp163(a,b,c),funcinp165(a,b,c,d).
null(0):-inp164(a,b,c),funcinp166(a,b,c,d).
null(0):-inp165(a,b,c),funcinp167(a,b,c,d).
null(0):-inp166(a,b,c),funcinp168(a,b,c,d).
null(0):-inp167(a,b,c),funcinp169(a,b,c,d).
null(0):-inp168(a,b,c),funcinp170(a,b,c,d).
null(0):-inp169(a,b,c),funcinp171(a,b,c,d).
null(0):-inp170(a,b,c),funcinp172(a,b,c,d).
null(0):-inp171(a,b,c),funcinp173(a,b,c,d).
null(0):-inp172(a,b,c),funcinp174(a,b,c,d).
null(0):-inp173(a,b,c),funcinp175(a,b,c,d).
null(0):-inp174(a,b,c),funcinp176(a,b,c,d).
null(0):-inp175(a,b,c),funcinp177(a,b,c,d).
null(0):-inp176(a,b,c),funcinp178(a,b,c,d).
null(0):-inp177(a,b,c),funcinp179(a,b,c,d).
null(0):-inp178(a,b,c),funcinp180(a,b,c,d).
null(0):-inp179(a,b,c),funcinp181(a,b,c,d).
null(0):-inp180(a,b,c),funcinp182(a,b,c,d).
null(0):-inp181(a,b,c),funcinp183(a,b,c,d).
null(0):-inp182(a,b,c),funcinp184(a,b,c,d).
null(0):-inp183(a,b,c),funcinp185(a,b,c,d).
null(0):-inp184(a,b,c),funcinp186(a,b,c,d).
null(0):-inp185(a,b,c),funcinp187(a,b,c,d).
null(0):-inp186(a,b,c),funcinp188(a,b,c,d).
null(0):-inp187(a,b,c),funcinp189(a,b,c,d).
null(0):-inp188(a,b,c),funcinp190(a,b,c,d).
null(0):-inp189(a,b,c),funcinp191(a,b,c,d).
null(0):-inp190(a,b,c),funcinp192(a,b,c,d).
null(0):-inp191(a,b,c),funcinp193(a,b,c,d).
null(0):-inp192(a,b,c),funcinp194(a,b,c,d).
null(0):-inp193(a,b,c),funcinp195(a,b,c,d).
null(0):-inp194(a,b,c),funcinp196(a,b,c,d).
null(0):-inp195(a,b,c),funcinp197(a,b,c,d).
null(0):-inp196(a,b,c),funcinp198(a,b,c,d).
null(0):-inp197(a,b,c),funcinp199(a,b,c,d).
null(0):-inp198(a,b,c),funcinp200(a,b,c,d).
null(0):-inp199(a,b,c),funcinp201(a,b,c,d).
null(0):-inp200(a,b,c),funcinp202(a,b,c,d).
null(0):-inp201(a,b,c),funcinp203(a,b,c,d).
null(0):-inp202(a,b,c),funcinp204(a,b,c,d).
null(0):-inp203(a,b,c),funcinp205(a,b,c,d).
null(0):-inp204(a,b,c),funcinp206(a,b,c,d).
null(0):-inp205(a,b,c),funcinp207(a,b,c,d).
null(0):-inp206(a,b,c),funcinp208(a,b,c,d).
null(0):-inp207(a,b,c),funcinp209(a,b,c,d).
null(0):-inp208(a,b,c),funcinp210(a,b,c,d).
null(0):-inp209(a,b,c),funcinp211(a,b,c,d).
null(0):-inp210(a,b,c),funcinp212(a,b,c,d).
null(0):-inp211(a,b,c),funcinp213(a,b,c,d).
null(0):-inp212(a,b,c),funcinp214(a,b,c,d).
null(0):-inp213(a,b,c),funcinp215(a,b,c,d).
null(0):-inp214(a,b,c),funcinp216(a,b,c,d).
null(0):-inp215(a,b,c),funcinp217(a,b,c,d).
null(0):-inp216(a,b,c),funcinp218(a,b,c,d).
null(0):-inp217(a,b,c),funcinp219(a,b,c,d).
null(0):-inp218(a,b,c),funcinp220(a,b,c,d).
null(0):-inp219(a,b,c),funcinp221(a,b,c,d).
null(0):-inp220(a,b,c),funcinp222(a,b,c,d).
null(0):-inp221(a,b,c),funcinp223(a,b,c,d).
null(0):-inp222(a,b,c),funcinp224(a,b,c,d).
null(0):-inp223(a,b,c),funcinp225(a,b,c,d).
null(0):-inp224(a,b,c),funcinp226(a,b,c,d).
null(0):-inp225(a,b,c),funcinp227(a,b,c,d).
null(0):-inp226(a,b,c),funcinp228(a,b,c,d).
null(0):-inp227(a,b,c),funcinp229(a,b,c,d).
null(0):-inp228(a,b,c),funcinp230(a,b,c,d).
null(0):-inp229(a,b,c),funcinp231(a,b,c,d).
null(0):-inp230(a,b,c),funcinp232(a,b,c,d).
null(0):-inp231(a,b,c),funcinp233(a,b,c,d).
null(0):-inp232(a,b,c),funcinp234(a,b,c,d).
null(0):-inp233(a,b,c),funcinp235(a,b,c,d).
null(0):-inp234(a,b,c),funcinp236(a,b,c,d).
null(0):-inp235(a,b,c),funcinp237(a,b,c,d).
null(0):-inp236(a,b,c),funcinp238(a,b,c,d).
null(0):-inp237(a,b,c),funcinp239(a,b,c,d).
null(0):-inp238(a,b,c),funcinp240(a,b,c,d).
null(0):-inp239(a,b,c),funcinp241(a,b,c,d).
null(0):-inp240(a,b,c),funcinp242(a,b,c,d).
null(0):-inp241(a,b,c),funcinp243(a,b,c,d).
null(0):-inp242(a,b,c),funcinp244(a,b,c,d).
null(0):-inp243(a,b,c),funcinp245(a,b,c,d).
null(0):-inp244(a,b,c),funcinp246(a,b,c,d).
null(0):-inp245(a,b,c),funcinp247(a,b,c,d).
null(0):-inp246(a,b,c),funcinp248(a,b,c,d).
null(0):-inp247(a,b,c),funcinp249(a,b,c,d).
null(0):-inp248(a,b,c),funcinp250(a,b,c,d).
null(0):-inp249(a,b,c),funcinp251(a,b,c,d).
null(0):-inp250(a,b,c),funcinp252(a,b,c,d).
null(0):-inp251(a,b,c),funcinp253(a,b,c,d).
null(0):-inp252(a,b,c),funcinp254(a,b,c,d).
null(0):-inp253(a,b,c),funcinp255(a,b,c,d).
null(0):-inp254(a,b,c),funcinp256(a,b,c,d).
null(0):-inp255(a,b,c),funcinp257(a,b,c,d).
null(0):-inp256(a,b,c),funcinp258(a,b,c,d).
null(0):-inp257(a,b,c),funcinp259(a,b,c,d).
null(0):-inp258(a,b,c),funcinp260(a,b,c,d).
null(0):-inp259(a,b,c),funcinp261(a,b,c,d).
null(0):-inp260(a,b,c),funcinp262(a,b,c,d).
null(0):-inp261(a,b,c),funcinp263(a,b,c,d).
null(0):-inp262(a,b,c),funcinp264(a,b,c,d).
null(0):-inp263(a,b,c),funcinp265(a,b,c,d).
null(0):-inp264(a,b,c),funcinp266(a,b,c,d).
null(0):-inp265(a,b,c),funcinp267(a,b,c,d).
null(0):-inp266(a,b,c),funcinp268(a,b,c,d).
null(0):-inp267(a,b,c),funcinp269(a,b,c,d).
null(0):-inp268(a,b,c),funcinp270(a,b,c,d).
null(0):-inp269(a,b,c),funcinp271(a,b,c,d).
null(0):-inp270(a,b,c),funcinp272(a,b,c,d).
null(0):-inp271(a,b,c),funcinp273(a,b,c,d).
null(0):-inp272(a,b,c),funcinp274(a,b,c,d).
null(0):-inp273(a,b,c),funcinp275(a,b,c,d).
null(0):-inp274(a,b,c),funcinp276(a,b,c,d).
null(0):-inp275(a,b,c),funcinp277(a,b,c,d).
null(0):-inp276(a,b,c),funcinp278(a,b,c,d).
null(0):-inp277(a,b,c),funcinp279(a,b,c,d).
null(0):-inp278(a,b,c),funcinp280(a,b,c,d).
null(0):-inp279(a,b,c),funcinp281(a,b,c,d).
null(0):-inp280(a,b,c),funcinp282(a,b,c,d).
null(0):-inp281(a,b,c),funcinp283(a,b,c,d).
null(0):-inp282(a,b,c),funcinp284(a,b,c,d).
null(0):-inp283(a,b,c),funcinp285(a,b,c,d).
null(0):-inp284(a,b,c),funcinp286(a,b,c,d).
null(0):-inp285(a,b,c),funcinp287(a,b,c,d).
null(0):-inp286(a,b,c),funcinp288(a,b,c,d).
null(0):-inp287(a,b,c),funcinp289(a,b,c,d).
null(0):-inp288(a,b,c),funcinp290(a,b,c,d).
null(0):-inp289(a,b,c),funcinp291(a,b,c,d).
null(0):-inp290(a,b,c),funcinp292(a,b,c,d).
null(0):-inp291(a,b,c),funcinp293(a,b,c,d).
null(0):-inp292(a,b,c),funcinp294(a,b,c,d).
null(0):-inp293(a,b,c),funcinp295(a,b,c,d).
null(0):-inp294(a,b,c),funcinp296(a,b,c,d).
null(0):-inp295(a,b,c),funcinp297(a,b,c,d).
null(0):-inp296(a,b,c),funcinp298(a,b,c,d).
null(0):-inp297(a,b,c),funcinp299(a,b,c,d).
null(0):-inp298(a,b,c),funcinp300(a,b,c,d).
null(0):-inp299(a,b,c),funcinp301(a,b,c,d).
null(0):-inp300(a,b,c),funcinp302(a,b,c,d).
null(0):-inp301(a,b,c),funcinp303(a,b,c,d).
null(0):-inp302(a,b,c),funcinp304(a,b,c,d).
null(0):-inp303(a,b,c),funcinp305(a,b,c,d).
null(0):-inp304(a,b,c),funcinp306(a,b,c,d).
null(0):-inp305(a,b,c),funcinp307(a,b,c,d).
null(0):-inp306(a,b,c),funcinp308(a,b,c,d).
null(0):-inp307(a,b,c),funcinp309(a,b,c,d).
null(0):-inp308(a,b,c),funcinp310(a,b,c,d).
null(0):-inp309(a,b,c),funcinp311(a,b,c,d).
null(0):-inp310(a,b,c),funcinp312(a,b,c,d).
null(0):-inp311(a,b,c),funcinp313(a,b,c,d).
null(0):-inp312(a,b,c),funcinp314(a,b,c,d).
null(0):-inp313(a,b,c),funcinp315(a,b,c,d).
null(0):-inp314(a,b,c),funcinp316(a,b,c,d).
null(0):-inp315(a,b,c),funcinp317(a,b,c,d).
null(0):-inp316(a,b,c),funcinp318(a,b,c,d).
null(0):-inp317(a,b,c),funcinp319(a,b,c,d).
null(0):-inp318(a,b,c),funcinp320(a,b,c,d).
null(0):-inp319(a,b,c),funcinp321(a,b,c,d).
null(0):-inp320(a,b,c),funcinp322(a,b,c,d).
null(0):-inp321(a,b,c),funcinp323(a,b,c,d).
null(0):-inp322(a,b,c),funcinp324(a,b,c,d).
null(0):-inp323(a,b,c),funcinp325(a,b,c,d).
null(0):-inp324(a,b,c),funcinp326(a,b,c,d).
null(0):-inp325(a,b,c),funcinp327(a,b,c,d).
null(0):-inp326(a,b,c),funcinp328(a,b,c,d).
null(0):-inp327(a,b,c),funcinp329(a,b,c,d).
null(0):-inp328(a,b,c),funcinp330(a,b,c,d).
null(0):-inp329(a,b,c),funcinp331(a,b,c,d).
null(0):-inp330(a,b,c),funcinp332(a,b,c,d).
null(0):-inp331(a,b,c),funcinp333(a,b,c,d).
null(0):-inp332(a,b,c),funcinp334(a,b,c,d).
null(0):-inp333(a,b,c),funcinp335(a,b,c,d).
null(0):-inp334(a,b,c),funcinp336(a,b,c,d).
null(0):-inp335(a,b,c),funcinp337(a,b,c,d).
null(0):-inp336(a,b,c),funcinp338(a,b,c,d).
null(0):-inp337(a,b,c),funcinp339(a,b,c,d).
null(0):-inp338(a,b,c),funcinp340(a,b,c,d).
null(0):-inp339(a,b,c),funcinp341(a,b,c,d).
null(0):-inp340(a,b,c),funcinp342(a,b,c,d).
null(0):-inp341(a,b,c),funcinp343(a,b,c,d).
null(0):-inp342(a,b,c),funcinp344(a,b,c,d).
null(0):-inp343(a,b,c),funcinp345(a,b,c,d).
null(0):-inp344(a,b,c),funcinp346(a,b,c,d).
null(0):-inp345(a,b,c),funcinp347(a,b,c,d).
null(0):-inp346(a,b,c),funcinp348(a,b,c,d).
null(0):-inp347(a,b,c),funcinp349(a,b,c,d).
null(0):-inp348(a,b,c),funcinp350(a,b,c,d).
null(0):-inp349(a,b,c),funcinp351(a,b,c,d).
null(0):-inp350(a,b,c),funcinp352(a,b,c,d).
null(0):-inp351(a,b,c),funcinp353(a,b,c,d).
null(0):-inp352(a,b,c),funcinp354(a,b,c,d).
null(0):-inp353(a,b,c),funcinp355(a,b,c,d).
null(0):-inp354(a,b,c),funcinp356(a,b,c,d).
null(0):-inp355(a,b,c),funcinp357(a,b,c,d).
null(0):-inp356(a,b,c),funcinp358(a,b,c,d).
null(0):-inp357(a,b,c),funcinp359(a,b,c,d).
null(0):-inp358(a,b,c),funcinp360(a,b,c,d).
null(0):-inp359(a,b,c),funcinp361(a,b,c,d).
null(0):-inp360(a,b,c),funcinp362(a,b,c,d).
null(0):-inp361(a,b,c),funcinp363(a,b,c,d).
null(0):-inp362(a,b,c),funcinp364(a,b,c,d).
null(0):-inp363(a,b,c),funcinp365(a,b,c,d).
null(0):-inp364(a,b,c),funcinp366(a,b,c,d).
null(0):-inp365(a,b,c),funcinp367(a,b,c,d).
null(0):-inp366(a,b,c),funcinp368(a,b,c,d).
null(0):-inp367(a,b,c),funcinp369(a,b,c,d).
null(0):-inp368(a,b,c),funcinp370(a,b,c,d).
null(0):-inp369(a,b,c),funcinp371(a,b,c,d).
null(0):-inp370(a,b,c),funcinp372(a,b,c,d).
null(0):-inp371(a,b,c),funcinp373(a,b,c,d).
null(0):-inp372(a,b,c),funcinp374(a,b,c,d).
null(0):-inp373(a,b,c),funcinp375(a,b,c,d).
null(0):-inp374(a,b,c),funcinp376(a,b,c,d).
null(0):-inp375(a,b,c),funcinp377(a,b,c,d).
null(0):-inp376(a,b,c),funcinp378(a,b,c,d).
null(0):-inp377(a,b,c),funcinp379(a,b,c,d).
null(0):-inp378(a,b,c),funcinp380(a,b,c,d).
null(0):-inp379(a,b,c),funcinp381(a,b,c,d).
null(0):-inp380(a,b,c),funcinp382(a,b,c,d).
null(0):-inp381(a,b,c),funcinp383(a,b,c,d).
null(0):-inp382(a,b,c),funcinp384(a,b,c,d).
null(0):-inp383(a,b,c),funcinp385(a,b,c,d).
null(0):-inp384(a,b,c),funcinp386(a,b,c,d).
null(0):-inp385(a,b,c),funcinp387(a,b,c,d).
null(0):-inp386(a,b,c),funcinp388(a,b,c,d).
null(0):-inp387(a,b,c),funcinp389(a,b,c,d).
null(0):-inp388(a,b,c),funcinp390(a,b,c,d).
null(0):-inp389(a,b,c),funcinp391(a,b,c,d).
null(0):-inp390(a,b,c),funcinp392(a,b,c,d).
null(0):-inp391(a,b,c),funcinp393(a,b,c,d).
null(0):-inp392(a,b,c),funcinp394(a,b,c,d).
null(0):-inp393(a,b,c),funcinp395(a,b,c,d).
null(0):-inp394(a,b,c),funcinp396(a,b,c,d).
null(0):-inp395(a,b,c),funcinp397(a,b,c,d).
null(0):-inp396(a,b,c),funcinp398(a,b,c,d).
null(0):-inp397(a,b,c),funcinp399(a,b,c,d).
null(0):-inp398(a,b,c),funcinp400(a,b,c,d).
null(0):-inp399(a,b,c),funcinp401(a,b,c,d).
null(0):-inp400(a,b,c),funcinp402(a,b,c,d).
null(0):-inp401(a,b,c),funcinp403(a,b,c,d).
null(0):-inp402(a,b,c),funcinp404(a,b,c,d).
null(0):-inp403(a,b,c),funcinp405(a,b,c,d).
null(0):-inp404(a,b,c),funcinp406(a,b,c,d).
null(0):-inp405(a,b,c),funcinp407(a,b,c,d).
null(0):-inp406(a,b,c),funcinp408(a,b,c,d).
null(0):-inp407(a,b,c),funcinp409(a,b,c,d).
null(0):-inp408(a,b,c),funcinp410(a,b,c,d).
null(0):-inp409(a,b,c),funcinp411(a,b,c,d).
null(0):-inp410(a,b,c),funcinp412(a,b,c,d).
null(0):-inp411(a,b,c),funcinp413(a,b,c,d).
null(0):-inp412(a,b,c),funcinp414(a,b,c,d).
null(0):-inp413(a,b,c),funcinp415(a,b,c,d).
null(0):-inp414(a,b,c),funcinp416(a,b,c,d).
null(0):-inp415(a,b,c),funcinp417(a,b,c,d).
null(0):-inp416(a,b,c),funcinp418(a,b,c,d).
null(0):-inp417(a,b,c),funcinp419(a,b,c,d).
null(0):-inp418(a,b,c),funcinp420(a,b,c,d).
null(0):-inp419(a,b,c),funcinp421(a,b,c,d).
null(0):-inp420(a,b,c),funcinp422(a,b,c,d).
null(0):-inp421(a,b,c),funcinp423(a,b,c,d).
null(0):-inp422(a,b,c),funcinp424(a,b,c,d).
null(0):-inp423(a,b,c),funcinp425(a,b,c,d).
null(0):-inp424(a,b,c),funcinp426(a,b,c,d).
null(0):-inp425(a,b,c),funcinp427(a,b,c,d).
null(0):-inp426(a,b,c),funcinp428(a,b,c,d).
null(0):-inp427(a,b,c),funcinp429(a,b,c,d).
null(0):-inp428(a,b,c),funcinp430(a,b,c,d).
null(0):-inp429(a,b,c),funcinp431(a,b,c,d).
null(0):-inp430(a,b,c),funcinp432(a,b,c,d).
null(0):-inp431(a,b,c),funcinp433(a,b,c,d).
null(0):-inp432(a,b,c),funcinp434(a,b,c,d).
null(0):-inp433(a,b,c),funcinp435(a,b,c,d).
null(0):-inp434(a,b,c),funcinp436(a,b,c,d).
null(0):-inp435(a,b,c),funcinp437(a,b,c,d).
null(0):-inp436(a,b,c),funcinp438(a,b,c,d).
null(0):-inp437(a,b,c),funcinp439(a,b,c,d).
null(0):-inp438(a,b,c),funcinp440(a,b,c,d).
null(0):-inp439(a,b,c),funcinp441(a,b,c,d).
null(0):-inp440(a,b,c),funcinp442(a,b,c,d).
null(0):-inp441(a,b,c),funcinp443(a,b,c,d).
null(0):-inp442(a,b,c),funcinp444(a,b,c,d).
null(0):-inp443(a,b,c),funcinp445(a,b,c,d).
null(0):-inp444(a,b,c),funcinp446(a,b,c,d).
null(0):-inp445(a,b,c),funcinp447(a,b,c,d).
null(0):-inp446(a,b,c),funcinp448(a,b,c,d).
null(0):-inp447(a,b,c),funcinp449(a,b,c,d).
null(0):-inp448(a,b,c),funcinp450(a,b,c,d).
null(0):-inp449(a,b,c),funcinp451(a,b,c,d).
null(0):-inp450(a,b,c),funcinp452(a,b,c,d).
null(0):-inp451(a,b,c),funcinp453(a,b,c,d).
null(0):-inp452(a,b,c),funcinp454(a,b,c,d).
null(0):-inp453(a,b,c),funcinp455(a,b,c,d).
null(0):-inp454(a,b,c),funcinp456(a,b,c,d).
null(0):-inp455(a,b,c),funcinp457(a,b,c,d).
null(0):-inp456(a,b,c),funcinp458(a,b,c,d).
null(0):-inp457(a,b,c),funcinp459(a,b,c,d).
null(0):-inp458(a,b,c),funcinp460(a,b,c,d).
null(0):-inp459(a,b,c),funcinp461(a,b,c,d).
null(0):-inp460(a,b,c),funcinp462(a,b,c,d).
null(0):-inp461(a,b,c),funcinp463(a,b,c,d).
null(0):-inp462(a,b,c),funcinp464(a,b,c,d).
null(0):-inp463(a,b,c),funcinp465(a,b,c,d).
null(0):-inp464(a,b,c),funcinp466(a,b,c,d).
null(0):-inp465(a,b,c),funcinp467(a,b,c,d).
null(0):-inp466(a,b,c),funcinp468(a,b,c,d).
null(0):-inp467(a,b,c),funcinp469(a,b,c,d).
null(0):-inp468(a,b,c),funcinp470(a,b,c,d).
null(0):-inp469(a,b,c),funcinp471(a,b,c,d).
null(0):-inp470(a,b,c),funcinp472(a,b,c,d).
null(0):-inp471(a,b,c),funcinp473(a,b,c,d).
null(0):-inp472(a,b,c),funcinp474(a,b,c,d).
null(0):-inp473(a,b,c),funcinp475(a,b,c,d).
null(0):-inp474(a,b,c),funcinp476(a,b,c,d).
null(0):-inp475(a,b,c),funcinp477(a,b,c,d).
null(0):-inp476(a,b,c),funcinp478(a,b,c,d).
null(0):-inp477(a,b,c),funcinp479(a,b,c,d).
null(0):-inp478(a,b,c),funcinp480(a,b,c,d).
null(0):-inp479(a,b,c),funcinp481(a,b,c,d).
null(0):-inp480(a,b,c),funcinp482(a,b,c,d).
null(0):-inp481(a,b,c),funcinp483(a,b,c,d).
null(0):-inp482(a,b,c),funcinp484(a,b,c,d).
null(0):-inp483(a,b,c),funcinp485(a,b,c,d).
null(0):-inp484(a,b,c),funcinp486(a,b,c,d).
null(0):-inp485(a,b,c),funcinp487(a,b,c,d).
null(0):-inp486(a,b,c),funcinp488(a,b,c,d).
null(0):-inp487(a,b,c),funcinp489(a,b,c,d).
null(0):-inp488(a,b,c),funcinp490(a,b,c,d).
null(0):-inp489(a,b,c),funcinp491(a,b,c,d).
null(0):-inp490(a,b,c),funcinp492(a,b,c,d).
null(0):-inp491(a,b,c),funcinp493(a,b,c,d).
null(0):-inp492(a,b,c),funcinp494(a,b,c,d).
null(0):-inp493(a,b,c),funcinp495(a,b,c,d).
null(0):-inp494(a,b,c),funcinp496(a,b,c,d).
null(0):-inp495(a,b,c),funcinp497(a,b,c,d).
null(0):-inp496(a,b,c),funcinp498(a,b,c,d).
null(0):-inp497(a,b,c),funcinp499(a,b,c,d).
null(0):-inp498(a,b,c),funcinp500(a,b,c,d).
null(0):-inp499(a,b,c),funcinp1(a,b,c,d).
null(0):-inp1(a,b,c),funcinp4(a,b,c,d).
null(0):-inp2(a,b,c),funcinp5(a,b,c,d).
null(0):-inp3(a,b,c),funcinp6(a,b,c,d).
null(0):-inp4(a,b,c),funcinp7(a,b,c,d).
null(0):-inp5(a,b,c),funcinp8(a,b,c,d).
null(0):-inp6(a,b,c),funcinp9(a,b,c,d).
null(0):-inp7(a,b,c),funcinp10(a,b,c,d).
null(0):-inp8(a,b,c),funcinp11(a,b,c,d).
null(0):-inp9(a,b,c),funcinp12(a,b,c,d).
null(0):-inp10(a,b,c),funcinp13(a,b,c,d).
null(0):-inp11(a,b,c),funcinp14(a,b,c,d).
null(0):-inp12(a,b,c),funcinp15(a,b,c,d).
null(0):-inp13(a,b,c),funcinp16(a,b,c,d).
null(0):-inp14(a,b,c),funcinp17(a,b,c,d).
null(0):-inp15(a,b,c),funcinp18(a,b,c,d).
null(0):-inp16(a,b,c),funcinp19(a,b,c,d).
null(0):-inp17(a,b,c),funcinp20(a,b,c,d).
null(0):-inp18(a,b,c),funcinp21(a,b,c,d).
null(0):-inp19(a,b,c),funcinp22(a,b,c,d).
null(0):-inp20(a,b,c),funcinp23(a,b,c,d).
null(0):-inp21(a,b,c),funcinp24(a,b,c,d).
null(0):-inp22(a,b,c),funcinp25(a,b,c,d).
null(0):-inp23(a,b,c),funcinp26(a,b,c,d).
null(0):-inp24(a,b,c),funcinp27(a,b,c,d).
null(0):-inp25(a,b,c),funcinp28(a,b,c,d).
null(0):-inp26(a,b,c),funcinp29(a,b,c,d).
null(0):-inp27(a,b,c),funcinp30(a,b,c,d).
null(0):-inp28(a,b,c),funcinp31(a,b,c,d).
null(0):-inp29(a,b,c),funcinp32(a,b,c,d).
null(0):-inp30(a,b,c),funcinp33(a,b,c,d).
null(0):-inp31(a,b,c),funcinp34(a,b,c,d).
null(0):-inp32(a,b,c),funcinp35(a,b,c,d).
null(0):-inp33(a,b,c),funcinp36(a,b,c,d).
null(0):-inp34(a,b,c),funcinp37(a,b,c,d).
null(0):-inp35(a,b,c),funcinp38(a,b,c,d).
null(0):-inp36(a,b,c),funcinp39(a,b,c,d).
null(0):-inp37(a,b,c),funcinp40(a,b,c,d).
null(0):-inp38(a,b,c),funcinp41(a,b,c,d).
null(0):-inp39(a,b,c),funcinp42(a,b,c,d).
null(0):-inp40(a,b,c),funcinp43(a,b,c,d).
null(0):-inp41(a,b,c),funcinp44(a,b,c,d).
null(0):-inp42(a,b,c),funcinp45(a,b,c,d).
null(0):-inp43(a,b,c),funcinp46(a,b,c,d).
null(0):-inp44(a,b,c),funcinp47(a,b,c,d).
null(0):-inp45(a,b,c),funcinp48(a,b,c,d).
null(0):-inp46(a,b,c),funcinp49(a,b,c,d).
null(0):-inp47(a,b,c),funcinp50(a,b,c,d).
null(0):-inp48(a,b,c),funcinp51(a,b,c,d).
null(0):-inp49(a,b,c),funcinp52(a,b,c,d).
null(0):-inp50(a,b,c),funcinp53(a,b,c,d).
null(0):-inp51(a,b,c),funcinp54(a,b,c,d).
null(0):-inp52(a,b,c),funcinp55(a,b,c,d).
null(0):-inp53(a,b,c),funcinp56(a,b,c,d).
null(0):-inp54(a,b,c),funcinp57(a,b,c,d).
null(0):-inp55(a,b,c),funcinp58(a,b,c,d).
null(0):-inp56(a,b,c),funcinp59(a,b,c,d).
null(0):-inp57(a,b,c),funcinp60(a,b,c,d).
null(0):-inp58(a,b,c),funcinp61(a,b,c,d).
null(0):-inp59(a,b,c),funcinp62(a,b,c,d).
null(0):-inp60(a,b,c),funcinp63(a,b,c,d).
null(0):-inp61(a,b,c),funcinp64(a,b,c,d).
null(0):-inp62(a,b,c),funcinp65(a,b,c,d).
null(0):-inp63(a,b,c),funcinp66(a,b,c,d).
null(0):-inp64(a,b,c),funcinp67(a,b,c,d).
null(0):-inp65(a,b,c),funcinp68(a,b,c,d).
null(0):-inp66(a,b,c),funcinp69(a,b,c,d).
null(0):-inp67(a,b,c),funcinp70(a,b,c,d).
null(0):-inp68(a,b,c),funcinp71(a,b,c,d).
null(0):-inp69(a,b,c),funcinp72(a,b,c,d).
null(0):-inp70(a,b,c),funcinp73(a,b,c,d).
null(0):-inp71(a,b,c),funcinp74(a,b,c,d).
null(0):-inp72(a,b,c),funcinp75(a,b,c,d).
null(0):-inp73(a,b,c),funcinp76(a,b,c,d).
null(0):-inp74(a,b,c),funcinp77(a,b,c,d).
null(0):-inp75(a,b,c),funcinp78(a,b,c,d).
null(0):-inp76(a,b,c),funcinp79(a,b,c,d).
null(0):-inp77(a,b,c),funcinp80(a,b,c,d).
null(0):-inp78(a,b,c),funcinp81(a,b,c,d).
null(0):-inp79(a,b,c),funcinp82(a,b,c,d).
null(0):-inp80(a,b,c),funcinp83(a,b,c,d).
null(0):-inp81(a,b,c),funcinp84(a,b,c,d).
null(0):-inp82(a,b,c),funcinp85(a,b,c,d).
null(0):-inp83(a,b,c),funcinp86(a,b,c,d).
null(0):-inp84(a,b,c),funcinp87(a,b,c,d).
null(0):-inp85(a,b,c),funcinp88(a,b,c,d).
null(0):-inp86(a,b,c),funcinp89(a,b,c,d).
null(0):-inp87(a,b,c),funcinp90(a,b,c,d).
null(0):-inp88(a,b,c),funcinp91(a,b,c,d).
null(0):-inp89(a,b,c),funcinp92(a,b,c,d).
null(0):-inp90(a,b,c),funcinp93(a,b,c,d).
null(0):-inp91(a,b,c),funcinp94(a,b,c,d).
null(0):-inp92(a,b,c),funcinp95(a,b,c,d).
null(0):-inp93(a,b,c),funcinp96(a,b,c,d).
null(0):-inp94(a,b,c),funcinp97(a,b,c,d).
null(0):-inp95(a,b,c),funcinp98(a,b,c,d).
null(0):-inp96(a,b,c),funcinp99(a,b,c,d).
null(0):-inp97(a,b,c),funcinp100(a,b,c,d).
null(0):-inp98(a,b,c),funcinp101(a,b,c,d).
null(0):-inp99(a,b,c),funcinp102(a,b,c,d).
null(0):-inp100(a,b,c),funcinp103(a,b,c,d).
null(0):-inp101(a,b,c),funcinp104(a,b,c,d).
null(0):-inp102(a,b,c),funcinp105(a,b,c,d).
null(0):-inp103(a,b,c),funcinp106(a,b,c,d).
null(0):-inp104(a,b,c),funcinp107(a,b,c,d).
null(0):-inp105(a,b,c),funcinp108(a,b,c,d).
null(0):-inp106(a,b,c),funcinp109(a,b,c,d).
null(0):-inp107(a,b,c),funcinp110(a,b,c,d).
null(0):-inp108(a,b,c),funcinp111(a,b,c,d).
null(0):-inp109(a,b,c),funcinp112(a,b,c,d).
null(0):-inp110(a,b,c),funcinp113(a,b,c,d).
null(0):-inp111(a,b,c),funcinp114(a,b,c,d).
null(0):-inp112(a,b,c),funcinp115(a,b,c,d).
null(0):-inp113(a,b,c),funcinp116(a,b,c,d).
null(0):-inp114(a,b,c),funcinp117(a,b,c,d).
null(0):-inp115(a,b,c),funcinp118(a,b,c,d).
null(0):-inp116(a,b,c),funcinp119(a,b,c,d).
null(0):-inp117(a,b,c),funcinp120(a,b,c,d).
null(0):-inp118(a,b,c),funcinp121(a,b,c,d).
null(0):-inp119(a,b,c),funcinp122(a,b,c,d).
null(0):-inp120(a,b,c),funcinp123(a,b,c,d).
null(0):-inp121(a,b,c),funcinp124(a,b,c,d).
null(0):-inp122(a,b,c),funcinp125(a,b,c,d).
null(0):-inp123(a,b,c),funcinp126(a,b,c,d).
null(0):-inp124(a,b,c),funcinp127(a,b,c,d).
null(0):-inp125(a,b,c),funcinp128(a,b,c,d).
null(0):-inp126(a,b,c),funcinp129(a,b,c,d).
null(0):-inp127(a,b,c),funcinp130(a,b,c,d).
null(0):-inp128(a,b,c),funcinp131(a,b,c,d).
null(0):-inp129(a,b,c),funcinp132(a,b,c,d).
null(0):-inp130(a,b,c),funcinp133(a,b,c,d).
null(0):-inp131(a,b,c),funcinp134(a,b,c,d).
null(0):-inp132(a,b,c),funcinp135(a,b,c,d).
null(0):-inp133(a,b,c),funcinp136(a,b,c,d).
null(0):-inp134(a,b,c),funcinp137(a,b,c,d).
null(0):-inp135(a,b,c),funcinp138(a,b,c,d).
null(0):-inp136(a,b,c),funcinp139(a,b,c,d).
null(0):-inp137(a,b,c),funcinp140(a,b,c,d).
null(0):-inp138(a,b,c),funcinp141(a,b,c,d).
null(0):-inp139(a,b,c),funcinp142(a,b,c,d).
null(0):-inp140(a,b,c),funcinp143(a,b,c,d).
null(0):-inp141(a,b,c),funcinp144(a,b,c,d).
null(0):-inp142(a,b,c),funcinp145(a,b,c,d).
null(0):-inp143(a,b,c),funcinp146(a,b,c,d).
null(0):-inp144(a,b,c),funcinp147(a,b,c,d).
null(0):-inp145(a,b,c),funcinp148(a,b,c,d).
null(0):-inp146(a,b,c),funcinp149(a,b,c,d).
null(0):-inp147(a,b,c),funcinp150(a,b,c,d).
null(0):-inp148(a,b,c),funcinp151(a,b,c,d).
null(0):-inp149(a,b,c),funcinp152(a,b,c,d).
null(0):-inp150(a,b,c),funcinp153(a,b,c,d).
null(0):-inp151(a,b,c),funcinp154(a,b,c,d).
null(0):-inp152(a,b,c),funcinp155(a,b,c,d).
null(0):-inp153(a,b,c),funcinp156(a,b,c,d).
null(0):-inp154(a,b,c),funcinp157(a,b,c,d).
null(0):-inp155(a,b,c),funcinp158(a,b,c,d).
null(0):-inp156(a,b,c),funcinp159(a,b,c,d).
null(0):-inp157(a,b,c),funcinp160(a,b,c,d).
null(0):-inp158(a,b,c),funcinp161(a,b,c,d).
null(0):-inp159(a,b,c),funcinp162(a,b,c,d).
null(0):-inp160(a,b,c),funcinp163(a,b,c,d).
null(0):-inp161(a,b,c),funcinp164(a,b,c,d).
null(0):-inp162(a,b,c),funcinp165(a,b,c,d).
null(0):-inp163(a,b,c),funcinp166(a,b,c,d).
null(0):-inp164(a,b,c),funcinp167(a,b,c,d).
null(0):-inp165(a,b,c),funcinp168(a,b,c,d).
null(0):-inp166(a,b,c),funcinp169(a,b,c,d).
null(0):-inp167(a,b,c),funcinp170(a,b,c,d).
null(0):-inp168(a,b,c),funcinp171(a,b,c,d).
null(0):-inp169(a,b,c),funcinp172(a,b,c,d).
null(0):-inp170(a,b,c),funcinp173(a,b,c,d).
null(0):-inp171(a,b,c),funcinp174(a,b,c,d).
null(0):-inp172(a,b,c),funcinp175(a,b,c,d).
null(0):-inp173(a,b,c),funcinp176(a,b,c,d).
null(0):-inp174(a,b,c),funcinp177(a,b,c,d).
null(0):-inp175(a,b,c),funcinp178(a,b,c,d).
null(0):-inp176(a,b,c),funcinp179(a,b,c,d).
null(0):-inp177(a,b,c),funcinp180(a,b,c,d).
null(0):-inp178(a,b,c),funcinp181(a,b,c,d).
null(0):-inp179(a,b,c),funcinp182(a,b,c,d).
null(0):-inp180(a,b,c),funcinp183(a,b,c,d).
null(0):-inp181(a,b,c),funcinp184(a,b,c,d).
null(0):-inp182(a,b,c),funcinp185(a,b,c,d).
null(0):-inp183(a,b,c),funcinp186(a,b,c,d).
null(0):-inp184(a,b,c),funcinp187(a,b,c,d).
null(0):-inp185(a,b,c),funcinp188(a,b,c,d).
null(0):-inp186(a,b,c),funcinp189(a,b,c,d).
null(0):-inp187(a,b,c),funcinp190(a,b,c,d).
null(0):-inp188(a,b,c),funcinp191(a,b,c,d).
null(0):-inp189(a,b,c),funcinp192(a,b,c,d).
null(0):-inp190(a,b,c),funcinp193(a,b,c,d).
null(0):-inp191(a,b,c),funcinp194(a,b,c,d).
null(0):-inp192(a,b,c),funcinp195(a,b,c,d).
null(0):-inp193(a,b,c),funcinp196(a,b,c,d).
null(0):-inp194(a,b,c),funcinp197(a,b,c,d).
null(0):-inp195(a,b,c),funcinp198(a,b,c,d).
null(0):-inp196(a,b,c),funcinp199(a,b,c,d).
null(0):-inp197(a,b,c),funcinp200(a,b,c,d).
null(0):-inp198(a,b,c),funcinp201(a,b,c,d).
null(0):-inp199(a,b,c),funcinp202(a,b,c,d).
null(0):-inp200(a,b,c),funcinp203(a,b,c,d).
null(0):-inp201(a,b,c),funcinp204(a,b,c,d).
null(0):-inp202(a,b,c),funcinp205(a,b,c,d).
null(0):-inp203(a,b,c),funcinp206(a,b,c,d).
null(0):-inp204(a,b,c),funcinp207(a,b,c,d).
null(0):-inp205(a,b,c),funcinp208(a,b,c,d).
null(0):-inp206(a,b,c),funcinp209(a,b,c,d).
null(0):-inp207(a,b,c),funcinp210(a,b,c,d).
null(0):-inp208(a,b,c),funcinp211(a,b,c,d).
null(0):-inp209(a,b,c),funcinp212(a,b,c,d).
null(0):-inp210(a,b,c),funcinp213(a,b,c,d).
null(0):-inp211(a,b,c),funcinp214(a,b,c,d).
null(0):-inp212(a,b,c),funcinp215(a,b,c,d).
null(0):-inp213(a,b,c),funcinp216(a,b,c,d).
null(0):-inp214(a,b,c),funcinp217(a,b,c,d).
null(0):-inp215(a,b,c),funcinp218(a,b,c,d).
null(0):-inp216(a,b,c),funcinp219(a,b,c,d).
null(0):-inp217(a,b,c),funcinp220(a,b,c,d).
null(0):-inp218(a,b,c),funcinp221(a,b,c,d).
null(0):-inp219(a,b,c),funcinp222(a,b,c,d).
null(0):-inp220(a,b,c),funcinp223(a,b,c,d).
null(0):-inp221(a,b,c),funcinp224(a,b,c,d).
null(0):-inp222(a,b,c),funcinp225(a,b,c,d).
null(0):-inp223(a,b,c),funcinp226(a,b,c,d).
null(0):-inp224(a,b,c),funcinp227(a,b,c,d).
null(0):-inp225(a,b,c),funcinp228(a,b,c,d).
null(0):-inp226(a,b,c),funcinp229(a,b,c,d).
null(0):-inp227(a,b,c),funcinp230(a,b,c,d).
null(0):-inp228(a,b,c),funcinp231(a,b,c,d).
null(0):-inp229(a,b,c),funcinp232(a,b,c,d).
null(0):-inp230(a,b,c),funcinp233(a,b,c,d).
null(0):-inp231(a,b,c),funcinp234(a,b,c,d).
null(0):-inp232(a,b,c),funcinp235(a,b,c,d).
null(0):-inp233(a,b,c),funcinp236(a,b,c,d).
null(0):-inp234(a,b,c),funcinp237(a,b,c,d).
null(0):-inp235(a,b,c),funcinp238(a,b,c,d).
null(0):-inp236(a,b,c),funcinp239(a,b,c,d).
null(0):-inp237(a,b,c),funcinp240(a,b,c,d).
null(0):-inp238(a,b,c),funcinp241(a,b,c,d).
null(0):-inp239(a,b,c),funcinp242(a,b,c,d).
null(0):-inp240(a,b,c),funcinp243(a,b,c,d).
null(0):-inp241(a,b,c),funcinp244(a,b,c,d).
null(0):-inp242(a,b,c),funcinp245(a,b,c,d).
null(0):-inp243(a,b,c),funcinp246(a,b,c,d).
null(0):-inp244(a,b,c),funcinp247(a,b,c,d).
null(0):-inp245(a,b,c),funcinp248(a,b,c,d).
null(0):-inp246(a,b,c),funcinp249(a,b,c,d).
null(0):-inp247(a,b,c),funcinp250(a,b,c,d).
null(0):-inp248(a,b,c),funcinp251(a,b,c,d).
null(0):-inp249(a,b,c),funcinp252(a,b,c,d).
null(0):-inp250(a,b,c),funcinp253(a,b,c,d).
null(0):-inp251(a,b,c),funcinp254(a,b,c,d).
null(0):-inp252(a,b,c),funcinp255(a,b,c,d).
null(0):-inp253(a,b,c),funcinp256(a,b,c,d).
null(0):-inp254(a,b,c),funcinp257(a,b,c,d).
null(0):-inp255(a,b,c),funcinp258(a,b,c,d).
null(0):-inp256(a,b,c),funcinp259(a,b,c,d).
null(0):-inp257(a,b,c),funcinp260(a,b,c,d).
null(0):-inp258(a,b,c),funcinp261(a,b,c,d).
null(0):-inp259(a,b,c),funcinp262(a,b,c,d).
null(0):-inp260(a,b,c),funcinp263(a,b,c,d).
null(0):-inp261(a,b,c),funcinp264(a,b,c,d).
null(0):-inp262(a,b,c),funcinp265(a,b,c,d).
null(0):-inp263(a,b,c),funcinp266(a,b,c,d).
null(0):-inp264(a,b,c),funcinp267(a,b,c,d).
null(0):-inp265(a,b,c),funcinp268(a,b,c,d).
null(0):-inp266(a,b,c),funcinp269(a,b,c,d).
null(0):-inp267(a,b,c),funcinp270(a,b,c,d).
null(0):-inp268(a,b,c),funcinp271(a,b,c,d).
null(0):-inp269(a,b,c),funcinp272(a,b,c,d).
null(0):-inp270(a,b,c),funcinp273(a,b,c,d).
null(0):-inp271(a,b,c),funcinp274(a,b,c,d).
null(0):-inp272(a,b,c),funcinp275(a,b,c,d).
null(0):-inp273(a,b,c),funcinp276(a,b,c,d).
null(0):-inp274(a,b,c),funcinp277(a,b,c,d).
null(0):-inp275(a,b,c),funcinp278(a,b,c,d).
null(0):-inp276(a,b,c),funcinp279(a,b,c,d).
null(0):-inp277(a,b,c),funcinp280(a,b,c,d).
null(0):-inp278(a,b,c),funcinp281(a,b,c,d).
null(0):-inp279(a,b,c),funcinp282(a,b,c,d).
null(0):-inp280(a,b,c),funcinp283(a,b,c,d).
null(0):-inp281(a,b,c),funcinp284(a,b,c,d).
null(0):-inp282(a,b,c),funcinp285(a,b,c,d).
null(0):-inp283(a,b,c),funcinp286(a,b,c,d).
null(0):-inp284(a,b,c),funcinp287(a,b,c,d).
null(0):-inp285(a,b,c),funcinp288(a,b,c,d).
null(0):-inp286(a,b,c),funcinp289(a,b,c,d).
null(0):-inp287(a,b,c),funcinp290(a,b,c,d).
null(0):-inp288(a,b,c),funcinp291(a,b,c,d).
null(0):-inp289(a,b,c),funcinp292(a,b,c,d).
null(0):-inp290(a,b,c),funcinp293(a,b,c,d).
null(0):-inp291(a,b,c),funcinp294(a,b,c,d).
null(0):-inp292(a,b,c),funcinp295(a,b,c,d).
null(0):-inp293(a,b,c),funcinp296(a,b,c,d).
null(0):-inp294(a,b,c),funcinp297(a,b,c,d).
null(0):-inp295(a,b,c),funcinp298(a,b,c,d).
null(0):-inp296(a,b,c),funcinp299(a,b,c,d).
null(0):-inp297(a,b,c),funcinp300(a,b,c,d).
null(0):-inp298(a,b,c),funcinp301(a,b,c,d).
null(0):-inp299(a,b,c),funcinp302(a,b,c,d).
null(0):-inp300(a,b,c),funcinp303(a,b,c,d).
null(0):-inp301(a,b,c),funcinp304(a,b,c,d).
null(0):-inp302(a,b,c),funcinp305(a,b,c,d).
null(0):-inp303(a,b,c),funcinp306(a,b,c,d).
null(0):-inp304(a,b,c),funcinp307(a,b,c,d).
null(0):-inp305(a,b,c),funcinp308(a,b,c,d).
null(0):-inp306(a,b,c),funcinp309(a,b,c,d).
null(0):-inp307(a,b,c),funcinp310(a,b,c,d).
null(0):-inp308(a,b,c),funcinp311(a,b,c,d).
null(0):-inp309(a,b,c),funcinp312(a,b,c,d).
null(0):-inp310(a,b,c),funcinp313(a,b,c,d).
null(0):-inp311(a,b,c),funcinp314(a,b,c,d).
null(0):-inp312(a,b,c),funcinp315(a,b,c,d).
null(0):-inp313(a,b,c),funcinp316(a,b,c,d).
null(0):-inp314(a,b,c),funcinp317(a,b,c,d).
null(0):-inp315(a,b,c),funcinp318(a,b,c,d).
null(0):-inp316(a,b,c),funcinp319(a,b,c,d).
null(0):-inp317(a,b,c),funcinp320(a,b,c,d).
null(0):-inp318(a,b,c),funcinp321(a,b,c,d).
null(0):-inp319(a,b,c),funcinp322(a,b,c,d).
null(0):-inp320(a,b,c),funcinp323(a,b,c,d).
null(0):-inp321(a,b,c),funcinp324(a,b,c,d).
null(0):-inp322(a,b,c),funcinp325(a,b,c,d).
null(0):-inp323(a,b,c),funcinp326(a,b,c,d).
null(0):-inp324(a,b,c),funcinp327(a,b,c,d).
null(0):-inp325(a,b,c),funcinp328(a,b,c,d).
null(0):-inp326(a,b,c),funcinp329(a,b,c,d).
null(0):-inp327(a,b,c),funcinp330(a,b,c,d).
null(0):-inp328(a,b,c),funcinp331(a,b,c,d).
null(0):-inp329(a,b,c),funcinp332(a,b,c,d).
null(0):-inp330(a,b,c),funcinp333(a,b,c,d).
null(0):-inp331(a,b,c),funcinp334(a,b,c,d).
null(0):-inp332(a,b,c),funcinp335(a,b,c,d).
null(0):-inp333(a,b,c),funcinp336(a,b,c,d).
null(0):-inp334(a,b,c),funcinp337(a,b,c,d).
null(0):-inp335(a,b,c),funcinp338(a,b,c,d).
null(0):-inp336(a,b,c),funcinp339(a,b,c,d).
null(0):-inp337(a,b,c),funcinp340(a,b,c,d).
null(0):-inp338(a,b,c),funcinp341(a,b,c,d).
null(0):-inp339(a,b,c),funcinp342(a,b,c,d).
null(0):-inp340(a,b,c),funcinp343(a,b,c,d).
null(0):-inp341(a,b,c),funcinp344(a,b,c,d).
null(0):-inp342(a,b,c),funcinp345(a,b,c,d).
null(0):-inp343(a,b,c),funcinp346(a,b,c,d).
null(0):-inp344(a,b,c),funcinp347(a,b,c,d).
null(0):-inp345(a,b,c),funcinp348(a,b,c,d).
null(0):-inp346(a,b,c),funcinp349(a,b,c,d).
null(0):-inp347(a,b,c),funcinp350(a,b,c,d).
null(0):-inp348(a,b,c),funcinp351(a,b,c,d).
null(0):-inp349(a,b,c),funcinp352(a,b,c,d).
null(0):-inp350(a,b,c),funcinp353(a,b,c,d).
null(0):-inp351(a,b,c),funcinp354(a,b,c,d).
null(0):-inp352(a,b,c),funcinp355(a,b,c,d).
null(0):-inp353(a,b,c),funcinp356(a,b,c,d).
null(0):-inp354(a,b,c),funcinp357(a,b,c,d).
null(0):-inp355(a,b,c),funcinp358(a,b,c,d).
null(0):-inp356(a,b,c),funcinp359(a,b,c,d).
null(0):-inp357(a,b,c),funcinp360(a,b,c,d).
null(0):-inp358(a,b,c),funcinp361(a,b,c,d).
null(0):-inp359(a,b,c),funcinp362(a,b,c,d).
null(0):-inp360(a,b,c),funcinp363(a,b,c,d).
null(0):-inp361(a,b,c),funcinp364(a,b,c,d).
null(0):-inp362(a,b,c),funcinp365(a,b,c,d).
null(0):-inp363(a,b,c),funcinp366(a,b,c,d).
null(0):-inp364(a,b,c),funcinp367(a,b,c,d).
null(0):-inp365(a,b,c),funcinp368(a,b,c,d).
null(0):-inp366(a,b,c),funcinp369(a,b,c,d).
null(0):-inp367(a,b,c),funcinp370(a,b,c,d).
null(0):-inp368(a,b,c),funcinp371(a,b,c,d).
null(0):-inp369(a,b,c),funcinp372(a,b,c,d).
null(0):-inp370(a,b,c),funcinp373(a,b,c,d).
null(0):-inp371(a,b,c),funcinp374(a,b,c,d).
null(0):-inp372(a,b,c),funcinp375(a,b,c,d).
null(0):-inp373(a,b,c),funcinp376(a,b,c,d).
null(0):-inp374(a,b,c),funcinp377(a,b,c,d).
null(0):-inp375(a,b,c),funcinp378(a,b,c,d).
null(0):-inp376(a,b,c),funcinp379(a,b,c,d).
null(0):-inp377(a,b,c),funcinp380(a,b,c,d).
null(0):-inp378(a,b,c),funcinp381(a,b,c,d).
null(0):-inp379(a,b,c),funcinp382(a,b,c,d).
null(0):-inp380(a,b,c),funcinp383(a,b,c,d).
null(0):-inp381(a,b,c),funcinp384(a,b,c,d).
null(0):-inp382(a,b,c),funcinp385(a,b,c,d).
null(0):-inp383(a,b,c),funcinp386(a,b,c,d).
null(0):-inp384(a,b,c),funcinp387(a,b,c,d).
null(0):-inp385(a,b,c),funcinp388(a,b,c,d).
null(0):-inp386(a,b,c),funcinp389(a,b,c,d).
null(0):-inp387(a,b,c),funcinp390(a,b,c,d).
null(0):-inp388(a,b,c),funcinp391(a,b,c,d).
null(0):-inp389(a,b,c),funcinp392(a,b,c,d).
null(0):-inp390(a,b,c),funcinp393(a,b,c,d).
null(0):-inp391(a,b,c),funcinp394(a,b,c,d).
null(0):-inp392(a,b,c),funcinp395(a,b,c,d).
null(0):-inp393(a,b,c),funcinp396(a,b,c,d).
null(0):-inp394(a,b,c),funcinp397(a,b,c,d).
null(0):-inp395(a,b,c),funcinp398(a,b,c,d).
null(0):-inp396(a,b,c),funcinp399(a,b,c,d).
null(0):-inp397(a,b,c),funcinp400(a,b,c,d).
null(0):-inp398(a,b,c),funcinp401(a,b,c,d).
null(0):-inp399(a,b,c),funcinp402(a,b,c,d).
null(0):-inp400(a,b,c),funcinp403(a,b,c,d).
null(0):-inp401(a,b,c),funcinp404(a,b,c,d).
null(0):-inp402(a,b,c),funcinp405(a,b,c,d).
null(0):-inp403(a,b,c),funcinp406(a,b,c,d).
null(0):-inp404(a,b,c),funcinp407(a,b,c,d).
null(0):-inp405(a,b,c),funcinp408(a,b,c,d).
null(0):-inp406(a,b,c),funcinp409(a,b,c,d).
null(0):-inp407(a,b,c),funcinp410(a,b,c,d).
null(0):-inp408(a,b,c),funcinp411(a,b,c,d).
null(0):-inp409(a,b,c),funcinp412(a,b,c,d).
null(0):-inp410(a,b,c),funcinp413(a,b,c,d).
null(0):-inp411(a,b,c),funcinp414(a,b,c,d).
null(0):-inp412(a,b,c),funcinp415(a,b,c,d).
null(0):-inp413(a,b,c),funcinp416(a,b,c,d).
null(0):-inp414(a,b,c),funcinp417(a,b,c,d).
null(0):-inp415(a,b,c),funcinp418(a,b,c,d).
null(0):-inp416(a,b,c),funcinp419(a,b,c,d).
null(0):-inp417(a,b,c),funcinp420(a,b,c,d).
null(0):-inp418(a,b,c),funcinp421(a,b,c,d).
null(0):-inp419(a,b,c),funcinp422(a,b,c,d).
null(0):-inp420(a,b,c),funcinp423(a,b,c,d).
null(0):-inp421(a,b,c),funcinp424(a,b,c,d).
null(0):-inp422(a,b,c),funcinp425(a,b,c,d).
null(0):-inp423(a,b,c),funcinp426(a,b,c,d).
null(0):-inp424(a,b,c),funcinp427(a,b,c,d).
null(0):-inp425(a,b,c),funcinp428(a,b,c,d).
null(0):-inp426(a,b,c),funcinp429(a,b,c,d).
null(0):-inp427(a,b,c),funcinp430(a,b,c,d).
null(0):-inp428(a,b,c),funcinp431(a,b,c,d).
null(0):-inp429(a,b,c),funcinp432(a,b,c,d).
null(0):-inp430(a,b,c),funcinp433(a,b,c,d).
null(0):-inp431(a,b,c),funcinp434(a,b,c,d).
null(0):-inp432(a,b,c),funcinp435(a,b,c,d).
null(0):-inp433(a,b,c),funcinp436(a,b,c,d).
null(0):-inp434(a,b,c),funcinp437(a,b,c,d).
null(0):-inp435(a,b,c),funcinp438(a,b,c,d).
null(0):-inp436(a,b,c),funcinp439(a,b,c,d).
null(0):-inp437(a,b,c),funcinp440(a,b,c,d).
null(0):-inp438(a,b,c),funcinp441(a,b,c,d).
null(0):-inp439(a,b,c),funcinp442(a,b,c,d).
null(0):-inp440(a,b,c),funcinp443(a,b,c,d).
null(0):-inp441(a,b,c),funcinp444(a,b,c,d).
null(0):-inp442(a,b,c),funcinp445(a,b,c,d).
null(0):-inp443(a,b,c),funcinp446(a,b,c,d).
null(0):-inp444(a,b,c),funcinp447(a,b,c,d).
null(0):-inp445(a,b,c),funcinp448(a,b,c,d).
null(0):-inp446(a,b,c),funcinp449(a,b,c,d).
null(0):-inp447(a,b,c),funcinp450(a,b,c,d).
null(0):-inp448(a,b,c),funcinp451(a,b,c,d).
null(0):-inp449(a,b,c),funcinp452(a,b,c,d).
null(0):-inp450(a,b,c),funcinp453(a,b,c,d).
null(0):-inp451(a,b,c),funcinp454(a,b,c,d).
null(0):-inp452(a,b,c),funcinp455(a,b,c,d).
null(0):-inp453(a,b,c),funcinp456(a,b,c,d).
null(0):-inp454(a,b,c),funcinp457(a,b,c,d).
null(0):-inp455(a,b,c),funcinp458(a,b,c,d).
null(0):-inp456(a,b,c),funcinp459(a,b,c,d).
null(0):-inp457(a,b,c),funcinp460(a,b,c,d).
null(0):-inp458(a,b,c),funcinp461(a,b,c,d).
null(0):-inp459(a,b,c),funcinp462(a,b,c,d).
null(0):-inp460(a,b,c),funcinp463(a,b,c,d).
null(0):-inp461(a,b,c),funcinp464(a,b,c,d).
null(0):-inp462(a,b,c),funcinp465(a,b,c,d).
null(0):-inp463(a,b,c),funcinp466(a,b,c,d).
null(0):-inp464(a,b,c),funcinp467(a,b,c,d).
null(0):-inp465(a,b,c),funcinp468(a,b,c,d).
null(0):-inp466(a,b,c),funcinp469(a,b,c,d).
null(0):-inp467(a,b,c),funcinp470(a,b,c,d).
null(0):-inp468(a,b,c),funcinp471(a,b,c,d).
null(0):-inp469(a,b,c),funcinp472(a,b,c,d).
null(0):-inp470(a,b,c),funcinp473(a,b,c,d).
null(0):-inp471(a,b,c),funcinp474(a,b,c,d).
null(0):-inp472(a,b,c),funcinp475(a,b,c,d).
null(0):-inp473(a,b,c),funcinp476(a,b,c,d).
null(0):-inp474(a,b,c),funcinp477(a,b,c,d).
null(0):-inp475(a,b,c),funcinp478(a,b,c,d).
null(0):-inp476(a,b,c),funcinp479(a,b,c,d).
null(0):-inp477(a,b,c),funcinp480(a,b,c,d).
null(0):-inp478(a,b,c),funcinp481(a,b,c,d).
null(0):-inp479(a,b,c),funcinp482(a,b,c,d).
null(0):-inp480(a,b,c),funcinp483(a,b,c,d).
null(0):-inp481(a,b,c),funcinp484(a,b,c,d).
null(0):-inp482(a,b,c),funcinp485(a,b,c,d).
null(0):-inp483(a,b,c),funcinp486(a,b,c,d).
null(0):-inp484(a,b,c),funcinp487(a,b,c,d).
null(0):-inp485(a,b,c),funcinp488(a,b,c,d).
null(0):-inp486(a,b,c),funcinp489(a,b,c,d).
null(0):-inp487(a,b,c),funcinp490(a,b,c,d).
null(0):-inp488(a,b,c),funcinp491(a,b,c,d).
null(0):-inp489(a,b,c),funcinp492(a,b,c,d).
null(0):-inp490(a,b,c),funcinp493(a,b,c,d).
null(0):-inp491(a,b,c),funcinp494(a,b,c,d).
null(0):-inp492(a,b,c),funcinp495(a,b,c,d).
null(0):-inp493(a,b,c),funcinp496(a,b,c,d).
null(0):-inp494(a,b,c),funcinp497(a,b,c,d).
null(0):-inp495(a,b,c),funcinp498(a,b,c,d).
null(0):-inp496(a,b,c),funcinp499(a,b,c,d).
null(0):-inp497(a,b,c),funcinp500(a,b,c,d).
null(0):-inp498(a,b,c),funcinp1(a,b,c,d).
null(0):-inp499(a,b,c),funcinp2(a,b,c,d).
null(0):-inp1(a,b,c),funcinp5(a,b,c,d).
null(0):-inp2(a,b,c),funcinp6(a,b,c,d).
null(0):-inp3(a,b,c),funcinp7(a,b,c,d).
null(0):-inp4(a,b,c),funcinp8(a,b,c,d).
null(0):-inp5(a,b,c),funcinp9(a,b,c,d).
null(0):-inp6(a,b,c),funcinp10(a,b,c,d).
null(0):-inp7(a,b,c),funcinp11(a,b,c,d).
null(0):-inp8(a,b,c),funcinp12(a,b,c,d).
null(0):-inp9(a,b,c),funcinp13(a,b,c,d).
null(0):-inp10(a,b,c),funcinp14(a,b,c,d).
null(0):-inp11(a,b,c),funcinp15(a,b,c,d).
null(0):-inp12(a,b,c),funcinp16(a,b,c,d).
null(0):-inp13(a,b,c),funcinp17(a,b,c,d).
null(0):-inp14(a,b,c),funcinp18(a,b,c,d).
null(0):-inp15(a,b,c),funcinp19(a,b,c,d).
null(0):-inp16(a,b,c),funcinp20(a,b,c,d).
null(0):-inp17(a,b,c),funcinp21(a,b,c,d).
null(0):-inp18(a,b,c),funcinp22(a,b,c,d).
null(0):-inp19(a,b,c),funcinp23(a,b,c,d).
null(0):-inp20(a,b,c),funcinp24(a,b,c,d).
null(0):-inp21(a,b,c),funcinp25(a,b,c,d).
null(0):-inp22(a,b,c),funcinp26(a,b,c,d).
null(0):-inp23(a,b,c),funcinp27(a,b,c,d).
null(0):-inp24(a,b,c),funcinp28(a,b,c,d).
null(0):-inp25(a,b,c),funcinp29(a,b,c,d).
null(0):-inp26(a,b,c),funcinp30(a,b,c,d).
null(0):-inp27(a,b,c),funcinp31(a,b,c,d).
null(0):-inp28(a,b,c),funcinp32(a,b,c,d).
null(0):-inp29(a,b,c),funcinp33(a,b,c,d).
null(0):-inp30(a,b,c),funcinp34(a,b,c,d).
null(0):-inp31(a,b,c),funcinp35(a,b,c,d).
null(0):-inp32(a,b,c),funcinp36(a,b,c,d).
null(0):-inp33(a,b,c),funcinp37(a,b,c,d).
null(0):-inp34(a,b,c),funcinp38(a,b,c,d).
null(0):-inp35(a,b,c),funcinp39(a,b,c,d).
null(0):-inp36(a,b,c),funcinp40(a,b,c,d).
null(0):-inp37(a,b,c),funcinp41(a,b,c,d).
null(0):-inp38(a,b,c),funcinp42(a,b,c,d).
null(0):-inp39(a,b,c),funcinp43(a,b,c,d).
null(0):-inp40(a,b,c),funcinp44(a,b,c,d).
null(0):-inp41(a,b,c),funcinp45(a,b,c,d).
null(0):-inp42(a,b,c),funcinp46(a,b,c,d).
null(0):-inp43(a,b,c),funcinp47(a,b,c,d).
null(0):-inp44(a,b,c),funcinp48(a,b,c,d).
null(0):-inp45(a,b,c),funcinp49(a,b,c,d).
null(0):-inp46(a,b,c),funcinp50(a,b,c,d).
null(0):-inp47(a,b,c),funcinp51(a,b,c,d).
null(0):-inp48(a,b,c),funcinp52(a,b,c,d).
null(0):-inp49(a,b,c),funcinp53(a,b,c,d).
null(0):-inp50(a,b,c),funcinp54(a,b,c,d).
null(0):-inp51(a,b,c),funcinp55(a,b,c,d).
null(0):-inp52(a,b,c),funcinp56(a,b,c,d).
null(0):-inp53(a,b,c),funcinp57(a,b,c,d).
null(0):-inp54(a,b,c),funcinp58(a,b,c,d).
null(0):-inp55(a,b,c),funcinp59(a,b,c,d).
null(0):-inp56(a,b,c),funcinp60(a,b,c,d).
null(0):-inp57(a,b,c),funcinp61(a,b,c,d).
null(0):-inp58(a,b,c),funcinp62(a,b,c,d).
null(0):-inp59(a,b,c),funcinp63(a,b,c,d).
null(0):-inp60(a,b,c),funcinp64(a,b,c,d).
null(0):-inp61(a,b,c),funcinp65(a,b,c,d).
null(0):-inp62(a,b,c),funcinp66(a,b,c,d).
null(0):-inp63(a,b,c),funcinp67(a,b,c,d).
null(0):-inp64(a,b,c),funcinp68(a,b,c,d).
null(0):-inp65(a,b,c),funcinp69(a,b,c,d).
null(0):-inp66(a,b,c),funcinp70(a,b,c,d).
null(0):-inp67(a,b,c),funcinp71(a,b,c,d).
null(0):-inp68(a,b,c),funcinp72(a,b,c,d).
null(0):-inp69(a,b,c),funcinp73(a,b,c,d).
null(0):-inp70(a,b,c),funcinp74(a,b,c,d).
null(0):-inp71(a,b,c),funcinp75(a,b,c,d).
null(0):-inp72(a,b,c),funcinp76(a,b,c,d).
null(0):-inp73(a,b,c),funcinp77(a,b,c,d).
null(0):-inp74(a,b,c),funcinp78(a,b,c,d).
null(0):-inp75(a,b,c),funcinp79(a,b,c,d).
null(0):-inp76(a,b,c),funcinp80(a,b,c,d).
null(0):-inp77(a,b,c),funcinp81(a,b,c,d).
null(0):-inp78(a,b,c),funcinp82(a,b,c,d).
null(0):-inp79(a,b,c),funcinp83(a,b,c,d).
null(0):-inp80(a,b,c),funcinp84(a,b,c,d).
null(0):-inp81(a,b,c),funcinp85(a,b,c,d).
null(0):-inp82(a,b,c),funcinp86(a,b,c,d).
null(0):-inp83(a,b,c),funcinp87(a,b,c,d).
null(0):-inp84(a,b,c),funcinp88(a,b,c,d).
null(0):-inp85(a,b,c),funcinp89(a,b,c,d).
null(0):-inp86(a,b,c),funcinp90(a,b,c,d).
null(0):-inp87(a,b,c),funcinp91(a,b,c,d).
null(0):-inp88(a,b,c),funcinp92(a,b,c,d).
null(0):-inp89(a,b,c),funcinp93(a,b,c,d).
null(0):-inp90(a,b,c),funcinp94(a,b,c,d).
null(0):-inp91(a,b,c),funcinp95(a,b,c,d).
null(0):-inp92(a,b,c),funcinp96(a,b,c,d).
null(0):-inp93(a,b,c),funcinp97(a,b,c,d).
null(0):-inp94(a,b,c),funcinp98(a,b,c,d).
null(0):-inp95(a,b,c),funcinp99(a,b,c,d).
null(0):-inp96(a,b,c),funcinp100(a,b,c,d).
null(0):-inp97(a,b,c),funcinp101(a,b,c,d).
null(0):-inp98(a,b,c),funcinp102(a,b,c,d).
null(0):-inp99(a,b,c),funcinp103(a,b,c,d).
null(0):-inp100(a,b,c),funcinp104(a,b,c,d).
null(0):-inp101(a,b,c),funcinp105(a,b,c,d).
null(0):-inp102(a,b,c),funcinp106(a,b,c,d).
null(0):-inp103(a,b,c),funcinp107(a,b,c,d).
null(0):-inp104(a,b,c),funcinp108(a,b,c,d).
null(0):-inp105(a,b,c),funcinp109(a,b,c,d).
null(0):-inp106(a,b,c),funcinp110(a,b,c,d).
null(0):-inp107(a,b,c),funcinp111(a,b,c,d).
null(0):-inp108(a,b,c),funcinp112(a,b,c,d).
null(0):-inp109(a,b,c),funcinp113(a,b,c,d).
null(0):-inp110(a,b,c),funcinp114(a,b,c,d).
null(0):-inp111(a,b,c),funcinp115(a,b,c,d).
null(0):-inp112(a,b,c),funcinp116(a,b,c,d).
null(0):-inp113(a,b,c),funcinp117(a,b,c,d).
null(0):-inp114(a,b,c),funcinp118(a,b,c,d).
null(0):-inp115(a,b,c),funcinp119(a,b,c,d).
null(0):-inp116(a,b,c),funcinp120(a,b,c,d).
null(0):-inp117(a,b,c),funcinp121(a,b,c,d).
null(0):-inp118(a,b,c),funcinp122(a,b,c,d).
null(0):-inp119(a,b,c),funcinp123(a,b,c,d).
null(0):-inp120(a,b,c),funcinp124(a,b,c,d).
null(0):-inp121(a,b,c),funcinp125(a,b,c,d).
null(0):-inp122(a,b,c),funcinp126(a,b,c,d).
null(0):-inp123(a,b,c),funcinp127(a,b,c,d).
null(0):-inp124(a,b,c),funcinp128(a,b,c,d).
null(0):-inp125(a,b,c),funcinp129(a,b,c,d).
null(0):-inp126(a,b,c),funcinp130(a,b,c,d).
null(0):-inp127(a,b,c),funcinp131(a,b,c,d).
null(0):-inp128(a,b,c),funcinp132(a,b,c,d).
null(0):-inp129(a,b,c),funcinp133(a,b,c,d).
null(0):-inp130(a,b,c),funcinp134(a,b,c,d).
null(0):-inp131(a,b,c),funcinp135(a,b,c,d).
null(0):-inp132(a,b,c),funcinp136(a,b,c,d).
null(0):-inp133(a,b,c),funcinp137(a,b,c,d).
null(0):-inp134(a,b,c),funcinp138(a,b,c,d).
null(0):-inp135(a,b,c),funcinp139(a,b,c,d).
null(0):-inp136(a,b,c),funcinp140(a,b,c,d).
null(0):-inp137(a,b,c),funcinp141(a,b,c,d).
null(0):-inp138(a,b,c),funcinp142(a,b,c,d).
null(0):-inp139(a,b,c),funcinp143(a,b,c,d).
null(0):-inp140(a,b,c),funcinp144(a,b,c,d).
null(0):-inp141(a,b,c),funcinp145(a,b,c,d).
null(0):-inp142(a,b,c),funcinp146(a,b,c,d).
null(0):-inp143(a,b,c),funcinp147(a,b,c,d).
null(0):-inp144(a,b,c),funcinp148(a,b,c,d).
null(0):-inp145(a,b,c),funcinp149(a,b,c,d).
null(0):-inp146(a,b,c),funcinp150(a,b,c,d).
null(0):-inp147(a,b,c),funcinp151(a,b,c,d).
null(0):-inp148(a,b,c),funcinp152(a,b,c,d).
null(0):-inp149(a,b,c),funcinp153(a,b,c,d).
null(0):-inp150(a,b,c),funcinp154(a,b,c,d).
null(0):-inp151(a,b,c),funcinp155(a,b,c,d).
null(0):-inp152(a,b,c),funcinp156(a,b,c,d).
null(0):-inp153(a,b,c),funcinp157(a,b,c,d).
null(0):-inp154(a,b,c),funcinp158(a,b,c,d).
null(0):-inp155(a,b,c),funcinp159(a,b,c,d).
null(0):-inp156(a,b,c),funcinp160(a,b,c,d).
null(0):-inp157(a,b,c),funcinp161(a,b,c,d).
null(0):-inp158(a,b,c),funcinp162(a,b,c,d).
null(0):-inp159(a,b,c),funcinp163(a,b,c,d).
null(0):-inp160(a,b,c),funcinp164(a,b,c,d).
null(0):-inp161(a,b,c),funcinp165(a,b,c,d).
null(0):-inp162(a,b,c),funcinp166(a,b,c,d).
null(0):-inp163(a,b,c),funcinp167(a,b,c,d).
null(0):-inp164(a,b,c),funcinp168(a,b,c,d).
null(0):-inp165(a,b,c),funcinp169(a,b,c,d).
null(0):-inp166(a,b,c),funcinp170(a,b,c,d).
null(0):-inp167(a,b,c),funcinp171(a,b,c,d).
null(0):-inp168(a,b,c),funcinp172(a,b,c,d).
null(0):-inp169(a,b,c),funcinp173(a,b,c,d).
null(0):-inp170(a,b,c),funcinp174(a,b,c,d).
null(0):-inp171(a,b,c),funcinp175(a,b,c,d).
null(0):-inp172(a,b,c),funcinp176(a,b,c,d).
null(0):-inp173(a,b,c),funcinp177(a,b,c,d).
null(0):-inp174(a,b,c),funcinp178(a,b,c,d).
null(0):-inp175(a,b,c),funcinp179(a,b,c,d).
null(0):-inp176(a,b,c),funcinp180(a,b,c,d).
null(0):-inp177(a,b,c),funcinp181(a,b,c,d).
null(0):-inp178(a,b,c),funcinp182(a,b,c,d).
null(0):-inp179(a,b,c),funcinp183(a,b,c,d).
null(0):-inp180(a,b,c),funcinp184(a,b,c,d).
null(0):-inp181(a,b,c),funcinp185(a,b,c,d).
null(0):-inp182(a,b,c),funcinp186(a,b,c,d).
null(0):-inp183(a,b,c),funcinp187(a,b,c,d).
null(0):-inp184(a,b,c),funcinp188(a,b,c,d).
null(0):-inp185(a,b,c),funcinp189(a,b,c,d).
null(0):-inp186(a,b,c),funcinp190(a,b,c,d).
null(0):-inp187(a,b,c),funcinp191(a,b,c,d).
null(0):-inp188(a,b,c),funcinp192(a,b,c,d).
null(0):-inp189(a,b,c),funcinp193(a,b,c,d).
null(0):-inp190(a,b,c),funcinp194(a,b,c,d).
null(0):-inp191(a,b,c),funcinp195(a,b,c,d).
null(0):-inp192(a,b,c),funcinp196(a,b,c,d).
null(0):-inp193(a,b,c),funcinp197(a,b,c,d).
null(0):-inp194(a,b,c),funcinp198(a,b,c,d).
null(0):-inp195(a,b,c),funcinp199(a,b,c,d).
null(0):-inp196(a,b,c),funcinp200(a,b,c,d).
null(0):-inp197(a,b,c),funcinp201(a,b,c,d).
null(0):-inp198(a,b,c),funcinp202(a,b,c,d).
null(0):-inp199(a,b,c),funcinp203(a,b,c,d).
null(0):-inp200(a,b,c),funcinp204(a,b,c,d).
null(0):-inp201(a,b,c),funcinp205(a,b,c,d).
null(0):-inp202(a,b,c),funcinp206(a,b,c,d).
null(0):-inp203(a,b,c),funcinp207(a,b,c,d).
null(0):-inp204(a,b,c),funcinp208(a,b,c,d).
null(0):-inp205(a,b,c),funcinp209(a,b,c,d).
null(0):-inp206(a,b,c),funcinp210(a,b,c,d).
null(0):-inp207(a,b,c),funcinp211(a,b,c,d).
null(0):-inp208(a,b,c),funcinp212(a,b,c,d).
null(0):-inp209(a,b,c),funcinp213(a,b,c,d).
null(0):-inp210(a,b,c),funcinp214(a,b,c,d).
null(0):-inp211(a,b,c),funcinp215(a,b,c,d).
null(0):-inp212(a,b,c),funcinp216(a,b,c,d).
null(0):-inp213(a,b,c),funcinp217(a,b,c,d).
null(0):-inp214(a,b,c),funcinp218(a,b,c,d).
null(0):-inp215(a,b,c),funcinp219(a,b,c,d).
null(0):-inp216(a,b,c),funcinp220(a,b,c,d).
null(0):-inp217(a,b,c),funcinp221(a,b,c,d).
null(0):-inp218(a,b,c),funcinp222(a,b,c,d).
null(0):-inp219(a,b,c),funcinp223(a,b,c,d).
null(0):-inp220(a,b,c),funcinp224(a,b,c,d).
null(0):-inp221(a,b,c),funcinp225(a,b,c,d).
null(0):-inp222(a,b,c),funcinp226(a,b,c,d).
null(0):-inp223(a,b,c),funcinp227(a,b,c,d).
null(0):-inp224(a,b,c),funcinp228(a,b,c,d).
null(0):-inp225(a,b,c),funcinp229(a,b,c,d).
null(0):-inp226(a,b,c),funcinp230(a,b,c,d).
null(0):-inp227(a,b,c),funcinp231(a,b,c,d).
null(0):-inp228(a,b,c),funcinp232(a,b,c,d).
null(0):-inp229(a,b,c),funcinp233(a,b,c,d).
null(0):-inp230(a,b,c),funcinp234(a,b,c,d).
null(0):-inp231(a,b,c),funcinp235(a,b,c,d).
null(0):-inp232(a,b,c),funcinp236(a,b,c,d).
null(0):-inp233(a,b,c),funcinp237(a,b,c,d).
null(0):-inp234(a,b,c),funcinp238(a,b,c,d).
null(0):-inp235(a,b,c),funcinp239(a,b,c,d).
null(0):-inp236(a,b,c),funcinp240(a,b,c,d).
null(0):-inp237(a,b,c),funcinp241(a,b,c,d).
null(0):-inp238(a,b,c),funcinp242(a,b,c,d).
null(0):-inp239(a,b,c),funcinp243(a,b,c,d).
null(0):-inp240(a,b,c),funcinp244(a,b,c,d).
null(0):-inp241(a,b,c),funcinp245(a,b,c,d).
null(0):-inp242(a,b,c),funcinp246(a,b,c,d).
null(0):-inp243(a,b,c),funcinp247(a,b,c,d).
null(0):-inp244(a,b,c),funcinp248(a,b,c,d).
null(0):-inp245(a,b,c),funcinp249(a,b,c,d).
null(0):-inp246(a,b,c),funcinp250(a,b,c,d).
null(0):-inp247(a,b,c),funcinp251(a,b,c,d).
null(0):-inp248(a,b,c),funcinp252(a,b,c,d).
null(0):-inp249(a,b,c),funcinp253(a,b,c,d).
null(0):-inp250(a,b,c),funcinp254(a,b,c,d).
null(0):-inp251(a,b,c),funcinp255(a,b,c,d).
null(0):-inp252(a,b,c),funcinp256(a,b,c,d).
null(0):-inp253(a,b,c),funcinp257(a,b,c,d).
null(0):-inp254(a,b,c),funcinp258(a,b,c,d).
null(0):-inp255(a,b,c),funcinp259(a,b,c,d).
null(0):-inp256(a,b,c),funcinp260(a,b,c,d).
null(0):-inp257(a,b,c),funcinp261(a,b,c,d).
null(0):-inp258(a,b,c),funcinp262(a,b,c,d).
null(0):-inp259(a,b,c),funcinp263(a,b,c,d).
null(0):-inp260(a,b,c),funcinp264(a,b,c,d).
null(0):-inp261(a,b,c),funcinp265(a,b,c,d).
null(0):-inp262(a,b,c),funcinp266(a,b,c,d).
null(0):-inp263(a,b,c),funcinp267(a,b,c,d).
null(0):-inp264(a,b,c),funcinp268(a,b,c,d).
null(0):-inp265(a,b,c),funcinp269(a,b,c,d).
null(0):-inp266(a,b,c),funcinp270(a,b,c,d).
null(0):-inp267(a,b,c),funcinp271(a,b,c,d).
null(0):-inp268(a,b,c),funcinp272(a,b,c,d).
null(0):-inp269(a,b,c),funcinp273(a,b,c,d).
null(0):-inp270(a,b,c),funcinp274(a,b,c,d).
null(0):-inp271(a,b,c),funcinp275(a,b,c,d).
null(0):-inp272(a,b,c),funcinp276(a,b,c,d).
null(0):-inp273(a,b,c),funcinp277(a,b,c,d).
null(0):-inp274(a,b,c),funcinp278(a,b,c,d).
null(0):-inp275(a,b,c),funcinp279(a,b,c,d).
null(0):-inp276(a,b,c),funcinp280(a,b,c,d).
null(0):-inp277(a,b,c),funcinp281(a,b,c,d).
null(0):-inp278(a,b,c),funcinp282(a,b,c,d).
null(0):-inp279(a,b,c),funcinp283(a,b,c,d).
null(0):-inp280(a,b,c),funcinp284(a,b,c,d).
null(0):-inp281(a,b,c),funcinp285(a,b,c,d).
null(0):-inp282(a,b,c),funcinp286(a,b,c,d).
null(0):-inp283(a,b,c),funcinp287(a,b,c,d).
null(0):-inp284(a,b,c),funcinp288(a,b,c,d).
null(0):-inp285(a,b,c),funcinp289(a,b,c,d).
null(0):-inp286(a,b,c),funcinp290(a,b,c,d).
null(0):-inp287(a,b,c),funcinp291(a,b,c,d).
null(0):-inp288(a,b,c),funcinp292(a,b,c,d).
null(0):-inp289(a,b,c),funcinp293(a,b,c,d).
null(0):-inp290(a,b,c),funcinp294(a,b,c,d).
null(0):-inp291(a,b,c),funcinp295(a,b,c,d).
null(0):-inp292(a,b,c),funcinp296(a,b,c,d).
null(0):-inp293(a,b,c),funcinp297(a,b,c,d).
null(0):-inp294(a,b,c),funcinp298(a,b,c,d).
null(0):-inp295(a,b,c),funcinp299(a,b,c,d).
null(0):-inp296(a,b,c),funcinp300(a,b,c,d).
null(0):-inp297(a,b,c),funcinp301(a,b,c,d).
null(0):-inp298(a,b,c),funcinp302(a,b,c,d).
null(0):-inp299(a,b,c),funcinp303(a,b,c,d).
null(0):-inp300(a,b,c),funcinp304(a,b,c,d).
null(0):-inp301(a,b,c),funcinp305(a,b,c,d).
null(0):-inp302(a,b,c),funcinp306(a,b,c,d).
null(0):-inp303(a,b,c),funcinp307(a,b,c,d).
null(0):-inp304(a,b,c),funcinp308(a,b,c,d).
null(0):-inp305(a,b,c),funcinp309(a,b,c,d).
null(0):-inp306(a,b,c),funcinp310(a,b,c,d).
null(0):-inp307(a,b,c),funcinp311(a,b,c,d).
null(0):-inp308(a,b,c),funcinp312(a,b,c,d).
null(0):-inp309(a,b,c),funcinp313(a,b,c,d).
null(0):-inp310(a,b,c),funcinp314(a,b,c,d).
null(0):-inp311(a,b,c),funcinp315(a,b,c,d).
null(0):-inp312(a,b,c),funcinp316(a,b,c,d).
null(0):-inp313(a,b,c),funcinp317(a,b,c,d).
null(0):-inp314(a,b,c),funcinp318(a,b,c,d).
null(0):-inp315(a,b,c),funcinp319(a,b,c,d).
null(0):-inp316(a,b,c),funcinp320(a,b,c,d).
null(0):-inp317(a,b,c),funcinp321(a,b,c,d).
null(0):-inp318(a,b,c),funcinp322(a,b,c,d).
null(0):-inp319(a,b,c),funcinp323(a,b,c,d).
null(0):-inp320(a,b,c),funcinp324(a,b,c,d).
null(0):-inp321(a,b,c),funcinp325(a,b,c,d).
null(0):-inp322(a,b,c),funcinp326(a,b,c,d).
null(0):-inp323(a,b,c),funcinp327(a,b,c,d).
null(0):-inp324(a,b,c),funcinp328(a,b,c,d).
null(0):-inp325(a,b,c),funcinp329(a,b,c,d).
null(0):-inp326(a,b,c),funcinp330(a,b,c,d).
null(0):-inp327(a,b,c),funcinp331(a,b,c,d).
null(0):-inp328(a,b,c),funcinp332(a,b,c,d).
null(0):-inp329(a,b,c),funcinp333(a,b,c,d).
null(0):-inp330(a,b,c),funcinp334(a,b,c,d).
null(0):-inp331(a,b,c),funcinp335(a,b,c,d).
null(0):-inp332(a,b,c),funcinp336(a,b,c,d).
null(0):-inp333(a,b,c),funcinp337(a,b,c,d).
null(0):-inp334(a,b,c),funcinp338(a,b,c,d).
null(0):-inp335(a,b,c),funcinp339(a,b,c,d).
null(0):-inp336(a,b,c),funcinp340(a,b,c,d).
null(0):-inp337(a,b,c),funcinp341(a,b,c,d).
null(0):-inp338(a,b,c),funcinp342(a,b,c,d).
null(0):-inp339(a,b,c),funcinp343(a,b,c,d).
null(0):-inp340(a,b,c),funcinp344(a,b,c,d).
null(0):-inp341(a,b,c),funcinp345(a,b,c,d).
null(0):-inp342(a,b,c),funcinp346(a,b,c,d).
null(0):-inp343(a,b,c),funcinp347(a,b,c,d).
null(0):-inp344(a,b,c),funcinp348(a,b,c,d).
null(0):-inp345(a,b,c),funcinp349(a,b,c,d).
null(0):-inp346(a,b,c),funcinp350(a,b,c,d).
null(0):-inp347(a,b,c),funcinp351(a,b,c,d).
null(0):-inp348(a,b,c),funcinp352(a,b,c,d).
null(0):-inp349(a,b,c),funcinp353(a,b,c,d).
null(0):-inp350(a,b,c),funcinp354(a,b,c,d).
null(0):-inp351(a,b,c),funcinp355(a,b,c,d).
null(0):-inp352(a,b,c),funcinp356(a,b,c,d).
null(0):-inp353(a,b,c),funcinp357(a,b,c,d).
null(0):-inp354(a,b,c),funcinp358(a,b,c,d).
null(0):-inp355(a,b,c),funcinp359(a,b,c,d).
null(0):-inp356(a,b,c),funcinp360(a,b,c,d).
null(0):-inp357(a,b,c),funcinp361(a,b,c,d).
null(0):-inp358(a,b,c),funcinp362(a,b,c,d).
null(0):-inp359(a,b,c),funcinp363(a,b,c,d).
null(0):-inp360(a,b,c),funcinp364(a,b,c,d).
null(0):-inp361(a,b,c),funcinp365(a,b,c,d).
null(0):-inp362(a,b,c),funcinp366(a,b,c,d).
null(0):-inp363(a,b,c),funcinp367(a,b,c,d).
null(0):-inp364(a,b,c),funcinp368(a,b,c,d).
null(0):-inp365(a,b,c),funcinp369(a,b,c,d).
null(0):-inp366(a,b,c),funcinp370(a,b,c,d).
null(0):-inp367(a,b,c),funcinp371(a,b,c,d).
null(0):-inp368(a,b,c),funcinp372(a,b,c,d).
null(0):-inp369(a,b,c),funcinp373(a,b,c,d).
null(0):-inp370(a,b,c),funcinp374(a,b,c,d).
null(0):-inp371(a,b,c),funcinp375(a,b,c,d).
null(0):-inp372(a,b,c),funcinp376(a,b,c,d).
null(0):-inp373(a,b,c),funcinp377(a,b,c,d).
null(0):-inp374(a,b,c),funcinp378(a,b,c,d).
null(0):-inp375(a,b,c),funcinp379(a,b,c,d).
null(0):-inp376(a,b,c),funcinp380(a,b,c,d).
null(0):-inp377(a,b,c),funcinp381(a,b,c,d).
null(0):-inp378(a,b,c),funcinp382(a,b,c,d).
null(0):-inp379(a,b,c),funcinp383(a,b,c,d).
null(0):-inp380(a,b,c),funcinp384(a,b,c,d).
null(0):-inp381(a,b,c),funcinp385(a,b,c,d).
null(0):-inp382(a,b,c),funcinp386(a,b,c,d).
null(0):-inp383(a,b,c),funcinp387(a,b,c,d).
null(0):-inp384(a,b,c),funcinp388(a,b,c,d).
null(0):-inp385(a,b,c),funcinp389(a,b,c,d).
null(0):-inp386(a,b,c),funcinp390(a,b,c,d).
null(0):-inp387(a,b,c),funcinp391(a,b,c,d).
null(0):-inp388(a,b,c),funcinp392(a,b,c,d).
null(0):-inp389(a,b,c),funcinp393(a,b,c,d).
null(0):-inp390(a,b,c),funcinp394(a,b,c,d).
null(0):-inp391(a,b,c),funcinp395(a,b,c,d).
null(0):-inp392(a,b,c),funcinp396(a,b,c,d).
null(0):-inp393(a,b,c),funcinp397(a,b,c,d).
null(0):-inp394(a,b,c),funcinp398(a,b,c,d).
null(0):-inp395(a,b,c),funcinp399(a,b,c,d).
null(0):-inp396(a,b,c),funcinp400(a,b,c,d).
null(0):-inp397(a,b,c),funcinp401(a,b,c,d).
null(0):-inp398(a,b,c),funcinp402(a,b,c,d).
null(0):-inp399(a,b,c),funcinp403(a,b,c,d).
null(0):-inp400(a,b,c),funcinp404(a,b,c,d).
null(0):-inp401(a,b,c),funcinp405(a,b,c,d).
null(0):-inp402(a,b,c),funcinp406(a,b,c,d).
null(0):-inp403(a,b,c),funcinp407(a,b,c,d).
null(0):-inp404(a,b,c),funcinp408(a,b,c,d).
null(0):-inp405(a,b,c),funcinp409(a,b,c,d).
null(0):-inp406(a,b,c),funcinp410(a,b,c,d).
null(0):-inp407(a,b,c),funcinp411(a,b,c,d).
null(0):-inp408(a,b,c),funcinp412(a,b,c,d).
null(0):-inp409(a,b,c),funcinp413(a,b,c,d).
null(0):-inp410(a,b,c),funcinp414(a,b,c,d).
null(0):-inp411(a,b,c),funcinp415(a,b,c,d).
null(0):-inp412(a,b,c),funcinp416(a,b,c,d).
null(0):-inp413(a,b,c),funcinp417(a,b,c,d).
null(0):-inp414(a,b,c),funcinp418(a,b,c,d).
null(0):-inp415(a,b,c),funcinp419(a,b,c,d).
null(0):-inp416(a,b,c),funcinp420(a,b,c,d).
null(0):-inp417(a,b,c),funcinp421(a,b,c,d).
null(0):-inp418(a,b,c),funcinp422(a,b,c,d).
null(0):-inp419(a,b,c),funcinp423(a,b,c,d).
null(0):-inp420(a,b,c),funcinp424(a,b,c,d).
null(0):-inp421(a,b,c),funcinp425(a,b,c,d).
null(0):-inp422(a,b,c),funcinp426(a,b,c,d).
null(0):-inp423(a,b,c),funcinp427(a,b,c,d).
null(0):-inp424(a,b,c),funcinp428(a,b,c,d).
null(0):-inp425(a,b,c),funcinp429(a,b,c,d).
null(0):-inp426(a,b,c),funcinp430(a,b,c,d).
null(0):-inp427(a,b,c),funcinp431(a,b,c,d).
null(0):-inp428(a,b,c),funcinp432(a,b,c,d).
null(0):-inp429(a,b,c),funcinp433(a,b,c,d).
null(0):-inp430(a,b,c),funcinp434(a,b,c,d).
null(0):-inp431(a,b,c),funcinp435(a,b,c,d).
null(0):-inp432(a,b,c),funcinp436(a,b,c,d).
null(0):-inp433(a,b,c),funcinp437(a,b,c,d).
null(0):-inp434(a,b,c),funcinp438(a,b,c,d).
null(0):-inp435(a,b,c),funcinp439(a,b,c,d).
null(0):-inp436(a,b,c),funcinp440(a,b,c,d).
null(0):-inp437(a,b,c),funcinp441(a,b,c,d).
null(0):-inp438(a,b,c),funcinp442(a,b,c,d).
null(0):-inp439(a,b,c),funcinp443(a,b,c,d).
null(0):-inp440(a,b,c),funcinp444(a,b,c,d).
null(0):-inp441(a,b,c),funcinp445(a,b,c,d).
null(0):-inp442(a,b,c),funcinp446(a,b,c,d).
null(0):-inp443(a,b,c),funcinp447(a,b,c,d).
null(0):-inp444(a,b,c),funcinp448(a,b,c,d).
null(0):-inp445(a,b,c),funcinp449(a,b,c,d).
null(0):-inp446(a,b,c),funcinp450(a,b,c,d).
null(0):-inp447(a,b,c),funcinp451(a,b,c,d).
null(0):-inp448(a,b,c),funcinp452(a,b,c,d).
null(0):-inp449(a,b,c),funcinp453(a,b,c,d).
null(0):-inp450(a,b,c),funcinp454(a,b,c,d).
null(0):-inp451(a,b,c),funcinp455(a,b,c,d).
null(0):-inp452(a,b,c),funcinp456(a,b,c,d).
null(0):-inp453(a,b,c),funcinp457(a,b,c,d).
null(0):-inp454(a,b,c),funcinp458(a,b,c,d).
null(0):-inp455(a,b,c),funcinp459(a,b,c,d).
null(0):-inp456(a,b,c),funcinp460(a,b,c,d).
null(0):-inp457(a,b,c),funcinp461(a,b,c,d).
null(0):-inp458(a,b,c),funcinp462(a,b,c,d).
null(0):-inp459(a,b,c),funcinp463(a,b,c,d).
null(0):-inp460(a,b,c),funcinp464(a,b,c,d).
null(0):-inp461(a,b,c),funcinp465(a,b,c,d).
null(0):-inp462(a,b,c),funcinp466(a,b,c,d).
null(0):-inp463(a,b,c),funcinp467(a,b,c,d).
null(0):-inp464(a,b,c),funcinp468(a,b,c,d).
null(0):-inp465(a,b,c),funcinp469(a,b,c,d).
null(0):-inp466(a,b,c),funcinp470(a,b,c,d).
null(0):-inp467(a,b,c),funcinp471(a,b,c,d).
null(0):-inp468(a,b,c),funcinp472(a,b,c,d).
null(0):-inp469(a,b,c),funcinp473(a,b,c,d).
null(0):-inp470(a,b,c),funcinp474(a,b,c,d).
null(0):-inp471(a,b,c),funcinp475(a,b,c,d).
null(0):-inp472(a,b,c),funcinp476(a,b,c,d).
null(0):-inp473(a,b,c),funcinp477(a,b,c,d).
null(0):-inp474(a,b,c),funcinp478(a,b,c,d).
null(0):-inp475(a,b,c),funcinp479(a,b,c,d).
null(0):-inp476(a,b,c),funcinp480(a,b,c,d).
null(0):-inp477(a,b,c),funcinp481(a,b,c,d).
null(0):-inp478(a,b,c),funcinp482(a,b,c,d).
null(0):-inp479(a,b,c),funcinp483(a,b,c,d).
null(0):-inp480(a,b,c),funcinp484(a,b,c,d).
null(0):-inp481(a,b,c),funcinp485(a,b,c,d).
null(0):-inp482(a,b,c),funcinp486(a,b,c,d).
null(0):-inp483(a,b,c),funcinp487(a,b,c,d).
null(0):-inp484(a,b,c),funcinp488(a,b,c,d).
null(0):-inp485(a,b,c),funcinp489(a,b,c,d).
null(0):-inp486(a,b,c),funcinp490(a,b,c,d).
null(0):-inp487(a,b,c),funcinp491(a,b,c,d).
null(0):-inp488(a,b,c),funcinp492(a,b,c,d).
null(0):-inp489(a,b,c),funcinp493(a,b,c,d).
null(0):-inp490(a,b,c),funcinp494(a,b,c,d).
null(0):-inp491(a,b,c),funcinp495(a,b,c,d).
null(0):-inp492(a,b,c),funcinp496(a,b,c,d).
null(0):-inp493(a,b,c),funcinp497(a,b,c,d).
null(0):-inp494(a,b,c),funcinp498(a,b,c,d).
null(0):-inp495(a,b,c),funcinp499(a,b,c,d).
null(0):-inp496(a,b,c),funcinp500(a,b,c,d).
null(0):-inp497(a,b,c),funcinp1(a,b,c,d).
null(0):-inp498(a,b,c),funcinp2(a,b,c,d).
null(0):-inp499(a,b,c),funcinp3(a,b,c,d).
null(0):-inp1(a,b,c),funcinp6(a,b,c,d).
null(0):-inp2(a,b,c),funcinp7(a,b,c,d).
null(0):-inp3(a,b,c),funcinp8(a,b,c,d).
null(0):-inp4(a,b,c),funcinp9(a,b,c,d).
null(0):-inp5(a,b,c),funcinp10(a,b,c,d).
null(0):-inp6(a,b,c),funcinp11(a,b,c,d).
null(0):-inp7(a,b,c),funcinp12(a,b,c,d).
null(0):-inp8(a,b,c),funcinp13(a,b,c,d).
null(0):-inp9(a,b,c),funcinp14(a,b,c,d).
null(0):-inp10(a,b,c),funcinp15(a,b,c,d).
null(0):-inp11(a,b,c),funcinp16(a,b,c,d).
null(0):-inp12(a,b,c),funcinp17(a,b,c,d).
null(0):-inp13(a,b,c),funcinp18(a,b,c,d).
null(0):-inp14(a,b,c),funcinp19(a,b,c,d).
null(0):-inp15(a,b,c),funcinp20(a,b,c,d).
null(0):-inp16(a,b,c),funcinp21(a,b,c,d).
null(0):-inp17(a,b,c),funcinp22(a,b,c,d).
null(0):-inp18(a,b,c),funcinp23(a,b,c,d).
null(0):-inp19(a,b,c),funcinp24(a,b,c,d).
null(0):-inp20(a,b,c),funcinp25(a,b,c,d).
null(0):-inp21(a,b,c),funcinp26(a,b,c,d).
null(0):-inp22(a,b,c),funcinp27(a,b,c,d).
null(0):-inp23(a,b,c),funcinp28(a,b,c,d).
null(0):-inp24(a,b,c),funcinp29(a,b,c,d).
null(0):-inp25(a,b,c),funcinp30(a,b,c,d).
null(0):-inp26(a,b,c),funcinp31(a,b,c,d).
null(0):-inp27(a,b,c),funcinp32(a,b,c,d).
null(0):-inp28(a,b,c),funcinp33(a,b,c,d).
null(0):-inp29(a,b,c),funcinp34(a,b,c,d).
null(0):-inp30(a,b,c),funcinp35(a,b,c,d).
null(0):-inp31(a,b,c),funcinp36(a,b,c,d).
null(0):-inp32(a,b,c),funcinp37(a,b,c,d).
null(0):-inp33(a,b,c),funcinp38(a,b,c,d).
null(0):-inp34(a,b,c),funcinp39(a,b,c,d).
null(0):-inp35(a,b,c),funcinp40(a,b,c,d).
null(0):-inp36(a,b,c),funcinp41(a,b,c,d).
null(0):-inp37(a,b,c),funcinp42(a,b,c,d).
null(0):-inp38(a,b,c),funcinp43(a,b,c,d).
null(0):-inp39(a,b,c),funcinp44(a,b,c,d).
null(0):-inp40(a,b,c),funcinp45(a,b,c,d).
null(0):-inp41(a,b,c),funcinp46(a,b,c,d).
null(0):-inp42(a,b,c),funcinp47(a,b,c,d).
null(0):-inp43(a,b,c),funcinp48(a,b,c,d).
null(0):-inp44(a,b,c),funcinp49(a,b,c,d).
null(0):-inp45(a,b,c),funcinp50(a,b,c,d).
null(0):-inp46(a,b,c),funcinp51(a,b,c,d).
null(0):-inp47(a,b,c),funcinp52(a,b,c,d).
null(0):-inp48(a,b,c),funcinp53(a,b,c,d).
null(0):-inp49(a,b,c),funcinp54(a,b,c,d).
null(0):-inp50(a,b,c),funcinp55(a,b,c,d).
null(0):-inp51(a,b,c),funcinp56(a,b,c,d).
null(0):-inp52(a,b,c),funcinp57(a,b,c,d).
null(0):-inp53(a,b,c),funcinp58(a,b,c,d).
null(0):-inp54(a,b,c),funcinp59(a,b,c,d).
null(0):-inp55(a,b,c),funcinp60(a,b,c,d).
null(0):-inp56(a,b,c),funcinp61(a,b,c,d).
null(0):-inp57(a,b,c),funcinp62(a,b,c,d).
null(0):-inp58(a,b,c),funcinp63(a,b,c,d).
null(0):-inp59(a,b,c),funcinp64(a,b,c,d).
null(0):-inp60(a,b,c),funcinp65(a,b,c,d).
null(0):-inp61(a,b,c),funcinp66(a,b,c,d).
null(0):-inp62(a,b,c),funcinp67(a,b,c,d).
null(0):-inp63(a,b,c),funcinp68(a,b,c,d).
null(0):-inp64(a,b,c),funcinp69(a,b,c,d).
null(0):-inp65(a,b,c),funcinp70(a,b,c,d).
null(0):-inp66(a,b,c),funcinp71(a,b,c,d).
null(0):-inp67(a,b,c),funcinp72(a,b,c,d).
null(0):-inp68(a,b,c),funcinp73(a,b,c,d).
null(0):-inp69(a,b,c),funcinp74(a,b,c,d).
null(0):-inp70(a,b,c),funcinp75(a,b,c,d).
null(0):-inp71(a,b,c),funcinp76(a,b,c,d).
null(0):-inp72(a,b,c),funcinp77(a,b,c,d).
null(0):-inp73(a,b,c),funcinp78(a,b,c,d).
null(0):-inp74(a,b,c),funcinp79(a,b,c,d).
null(0):-inp75(a,b,c),funcinp80(a,b,c,d).
null(0):-inp76(a,b,c),funcinp81(a,b,c,d).
null(0):-inp77(a,b,c),funcinp82(a,b,c,d).
null(0):-inp78(a,b,c),funcinp83(a,b,c,d).
null(0):-inp79(a,b,c),funcinp84(a,b,c,d).
null(0):-inp80(a,b,c),funcinp85(a,b,c,d).
null(0):-inp81(a,b,c),funcinp86(a,b,c,d).
null(0):-inp82(a,b,c),funcinp87(a,b,c,d).
null(0):-inp83(a,b,c),funcinp88(a,b,c,d).
null(0):-inp84(a,b,c),funcinp89(a,b,c,d).
null(0):-inp85(a,b,c),funcinp90(a,b,c,d).
null(0):-inp86(a,b,c),funcinp91(a,b,c,d).
null(0):-inp87(a,b,c),funcinp92(a,b,c,d).
null(0):-inp88(a,b,c),funcinp93(a,b,c,d).
null(0):-inp89(a,b,c),funcinp94(a,b,c,d).
null(0):-inp90(a,b,c),funcinp95(a,b,c,d).
null(0):-inp91(a,b,c),funcinp96(a,b,c,d).
null(0):-inp92(a,b,c),funcinp97(a,b,c,d).
null(0):-inp93(a,b,c),funcinp98(a,b,c,d).
null(0):-inp94(a,b,c),funcinp99(a,b,c,d).
null(0):-inp95(a,b,c),funcinp100(a,b,c,d).
null(0):-inp96(a,b,c),funcinp101(a,b,c,d).
null(0):-inp97(a,b,c),funcinp102(a,b,c,d).
null(0):-inp98(a,b,c),funcinp103(a,b,c,d).
null(0):-inp99(a,b,c),funcinp104(a,b,c,d).
null(0):-inp100(a,b,c),funcinp105(a,b,c,d).
null(0):-inp101(a,b,c),funcinp106(a,b,c,d).
null(0):-inp102(a,b,c),funcinp107(a,b,c,d).
null(0):-inp103(a,b,c),funcinp108(a,b,c,d).
null(0):-inp104(a,b,c),funcinp109(a,b,c,d).
null(0):-inp105(a,b,c),funcinp110(a,b,c,d).
null(0):-inp106(a,b,c),funcinp111(a,b,c,d).
null(0):-inp107(a,b,c),funcinp112(a,b,c,d).
null(0):-inp108(a,b,c),funcinp113(a,b,c,d).
null(0):-inp109(a,b,c),funcinp114(a,b,c,d).
null(0):-inp110(a,b,c),funcinp115(a,b,c,d).
null(0):-inp111(a,b,c),funcinp116(a,b,c,d).
null(0):-inp112(a,b,c),funcinp117(a,b,c,d).
null(0):-inp113(a,b,c),funcinp118(a,b,c,d).
null(0):-inp114(a,b,c),funcinp119(a,b,c,d).
null(0):-inp115(a,b,c),funcinp120(a,b,c,d).
null(0):-inp116(a,b,c),funcinp121(a,b,c,d).
null(0):-inp117(a,b,c),funcinp122(a,b,c,d).
null(0):-inp118(a,b,c),funcinp123(a,b,c,d).
null(0):-inp119(a,b,c),funcinp124(a,b,c,d).
null(0):-inp120(a,b,c),funcinp125(a,b,c,d).
null(0):-inp121(a,b,c),funcinp126(a,b,c,d).
null(0):-inp122(a,b,c),funcinp127(a,b,c,d).
null(0):-inp123(a,b,c),funcinp128(a,b,c,d).
null(0):-inp124(a,b,c),funcinp129(a,b,c,d).
null(0):-inp125(a,b,c),funcinp130(a,b,c,d).
null(0):-inp126(a,b,c),funcinp131(a,b,c,d).
null(0):-inp127(a,b,c),funcinp132(a,b,c,d).
null(0):-inp128(a,b,c),funcinp133(a,b,c,d).
null(0):-inp129(a,b,c),funcinp134(a,b,c,d).
null(0):-inp130(a,b,c),funcinp135(a,b,c,d).
null(0):-inp131(a,b,c),funcinp136(a,b,c,d).
null(0):-inp132(a,b,c),funcinp137(a,b,c,d).
null(0):-inp133(a,b,c),funcinp138(a,b,c,d).
null(0):-inp134(a,b,c),funcinp139(a,b,c,d).
null(0):-inp135(a,b,c),funcinp140(a,b,c,d).
null(0):-inp136(a,b,c),funcinp141(a,b,c,d).
null(0):-inp137(a,b,c),funcinp142(a,b,c,d).
null(0):-inp138(a,b,c),funcinp143(a,b,c,d).
null(0):-inp139(a,b,c),funcinp144(a,b,c,d).
null(0):-inp140(a,b,c),funcinp145(a,b,c,d).
null(0):-inp141(a,b,c),funcinp146(a,b,c,d).
null(0):-inp142(a,b,c),funcinp147(a,b,c,d).
null(0):-inp143(a,b,c),funcinp148(a,b,c,d).
null(0):-inp144(a,b,c),funcinp149(a,b,c,d).
null(0):-inp145(a,b,c),funcinp150(a,b,c,d).
null(0):-inp146(a,b,c),funcinp151(a,b,c,d).
null(0):-inp147(a,b,c),funcinp152(a,b,c,d).
null(0):-inp148(a,b,c),funcinp153(a,b,c,d).
null(0):-inp149(a,b,c),funcinp154(a,b,c,d).
null(0):-inp150(a,b,c),funcinp155(a,b,c,d).
null(0):-inp151(a,b,c),funcinp156(a,b,c,d).
null(0):-inp152(a,b,c),funcinp157(a,b,c,d).
null(0):-inp153(a,b,c),funcinp158(a,b,c,d).
null(0):-inp154(a,b,c),funcinp159(a,b,c,d).
null(0):-inp155(a,b,c),funcinp160(a,b,c,d).
null(0):-inp156(a,b,c),funcinp161(a,b,c,d).
null(0):-inp157(a,b,c),funcinp162(a,b,c,d).
null(0):-inp158(a,b,c),funcinp163(a,b,c,d).
null(0):-inp159(a,b,c),funcinp164(a,b,c,d).
null(0):-inp160(a,b,c),funcinp165(a,b,c,d).
null(0):-inp161(a,b,c),funcinp166(a,b,c,d).
null(0):-inp162(a,b,c),funcinp167(a,b,c,d).
null(0):-inp163(a,b,c),funcinp168(a,b,c,d).
null(0):-inp164(a,b,c),funcinp169(a,b,c,d).
null(0):-inp165(a,b,c),funcinp170(a,b,c,d).
null(0):-inp166(a,b,c),funcinp171(a,b,c,d).
null(0):-inp167(a,b,c),funcinp172(a,b,c,d).
null(0):-inp168(a,b,c),funcinp173(a,b,c,d).
null(0):-inp169(a,b,c),funcinp174(a,b,c,d).
null(0):-inp170(a,b,c),funcinp175(a,b,c,d).
null(0):-inp171(a,b,c),funcinp176(a,b,c,d).
null(0):-inp172(a,b,c),funcinp177(a,b,c,d).
null(0):-inp173(a,b,c),funcinp178(a,b,c,d).
null(0):-inp174(a,b,c),funcinp179(a,b,c,d).
null(0):-inp175(a,b,c),funcinp180(a,b,c,d).
null(0):-inp176(a,b,c),funcinp181(a,b,c,d).
null(0):-inp177(a,b,c),funcinp182(a,b,c,d).
null(0):-inp178(a,b,c),funcinp183(a,b,c,d).
null(0):-inp179(a,b,c),funcinp184(a,b,c,d).
null(0):-inp180(a,b,c),funcinp185(a,b,c,d).
null(0):-inp181(a,b,c),funcinp186(a,b,c,d).
null(0):-inp182(a,b,c),funcinp187(a,b,c,d).
null(0):-inp183(a,b,c),funcinp188(a,b,c,d).
null(0):-inp184(a,b,c),funcinp189(a,b,c,d).
null(0):-inp185(a,b,c),funcinp190(a,b,c,d).
null(0):-inp186(a,b,c),funcinp191(a,b,c,d).
null(0):-inp187(a,b,c),funcinp192(a,b,c,d).
null(0):-inp188(a,b,c),funcinp193(a,b,c,d).
null(0):-inp189(a,b,c),funcinp194(a,b,c,d).
null(0):-inp190(a,b,c),funcinp195(a,b,c,d).
null(0):-inp191(a,b,c),funcinp196(a,b,c,d).
null(0):-inp192(a,b,c),funcinp197(a,b,c,d).
null(0):-inp193(a,b,c),funcinp198(a,b,c,d).
null(0):-inp194(a,b,c),funcinp199(a,b,c,d).
null(0):-inp195(a,b,c),funcinp200(a,b,c,d).
null(0):-inp196(a,b,c),funcinp201(a,b,c,d).
null(0):-inp197(a,b,c),funcinp202(a,b,c,d).
null(0):-inp198(a,b,c),funcinp203(a,b,c,d).
null(0):-inp199(a,b,c),funcinp204(a,b,c,d).
null(0):-inp200(a,b,c),funcinp205(a,b,c,d).
null(0):-inp201(a,b,c),funcinp206(a,b,c,d).
null(0):-inp202(a,b,c),funcinp207(a,b,c,d).
null(0):-inp203(a,b,c),funcinp208(a,b,c,d).
null(0):-inp204(a,b,c),funcinp209(a,b,c,d).
null(0):-inp205(a,b,c),funcinp210(a,b,c,d).
null(0):-inp206(a,b,c),funcinp211(a,b,c,d).
null(0):-inp207(a,b,c),funcinp212(a,b,c,d).
null(0):-inp208(a,b,c),funcinp213(a,b,c,d).
null(0):-inp209(a,b,c),funcinp214(a,b,c,d).
null(0):-inp210(a,b,c),funcinp215(a,b,c,d).
null(0):-inp211(a,b,c),funcinp216(a,b,c,d).
null(0):-inp212(a,b,c),funcinp217(a,b,c,d).
null(0):-inp213(a,b,c),funcinp218(a,b,c,d).
null(0):-inp214(a,b,c),funcinp219(a,b,c,d).
null(0):-inp215(a,b,c),funcinp220(a,b,c,d).
null(0):-inp216(a,b,c),funcinp221(a,b,c,d).
null(0):-inp217(a,b,c),funcinp222(a,b,c,d).
null(0):-inp218(a,b,c),funcinp223(a,b,c,d).
null(0):-inp219(a,b,c),funcinp224(a,b,c,d).
null(0):-inp220(a,b,c),funcinp225(a,b,c,d).
null(0):-inp221(a,b,c),funcinp226(a,b,c,d).
null(0):-inp222(a,b,c),funcinp227(a,b,c,d).
null(0):-inp223(a,b,c),funcinp228(a,b,c,d).
null(0):-inp224(a,b,c),funcinp229(a,b,c,d).
null(0):-inp225(a,b,c),funcinp230(a,b,c,d).
null(0):-inp226(a,b,c),funcinp231(a,b,c,d).
null(0):-inp227(a,b,c),funcinp232(a,b,c,d).
null(0):-inp228(a,b,c),funcinp233(a,b,c,d).
null(0):-inp229(a,b,c),funcinp234(a,b,c,d).
null(0):-inp230(a,b,c),funcinp235(a,b,c,d).
null(0):-inp231(a,b,c),funcinp236(a,b,c,d).
null(0):-inp232(a,b,c),funcinp237(a,b,c,d).
null(0):-inp233(a,b,c),funcinp238(a,b,c,d).
null(0):-inp234(a,b,c),funcinp239(a,b,c,d).
null(0):-inp235(a,b,c),funcinp240(a,b,c,d).
null(0):-inp236(a,b,c),funcinp241(a,b,c,d).
null(0):-inp237(a,b,c),funcinp242(a,b,c,d).
null(0):-inp238(a,b,c),funcinp243(a,b,c,d).
null(0):-inp239(a,b,c),funcinp244(a,b,c,d).
null(0):-inp240(a,b,c),funcinp245(a,b,c,d).
null(0):-inp241(a,b,c),funcinp246(a,b,c,d).
null(0):-inp242(a,b,c),funcinp247(a,b,c,d).
null(0):-inp243(a,b,c),funcinp248(a,b,c,d).
null(0):-inp244(a,b,c),funcinp249(a,b,c,d).
null(0):-inp245(a,b,c),funcinp250(a,b,c,d).
null(0):-inp246(a,b,c),funcinp251(a,b,c,d).
null(0):-inp247(a,b,c),funcinp252(a,b,c,d).
null(0):-inp248(a,b,c),funcinp253(a,b,c,d).
null(0):-inp249(a,b,c),funcinp254(a,b,c,d).
null(0):-inp250(a,b,c),funcinp255(a,b,c,d).
null(0):-inp251(a,b,c),funcinp256(a,b,c,d).
null(0):-inp252(a,b,c),funcinp257(a,b,c,d).
null(0):-inp253(a,b,c),funcinp258(a,b,c,d).
null(0):-inp254(a,b,c),funcinp259(a,b,c,d).
null(0):-inp255(a,b,c),funcinp260(a,b,c,d).
null(0):-inp256(a,b,c),funcinp261(a,b,c,d).
null(0):-inp257(a,b,c),funcinp262(a,b,c,d).
null(0):-inp258(a,b,c),funcinp263(a,b,c,d).
null(0):-inp259(a,b,c),funcinp264(a,b,c,d).
null(0):-inp260(a,b,c),funcinp265(a,b,c,d).
null(0):-inp261(a,b,c),funcinp266(a,b,c,d).
null(0):-inp262(a,b,c),funcinp267(a,b,c,d).
null(0):-inp263(a,b,c),funcinp268(a,b,c,d).
null(0):-inp264(a,b,c),funcinp269(a,b,c,d).
null(0):-inp265(a,b,c),funcinp270(a,b,c,d).
null(0):-inp266(a,b,c),funcinp271(a,b,c,d).
null(0):-inp267(a,b,c),funcinp272(a,b,c,d).
null(0):-inp268(a,b,c),funcinp273(a,b,c,d).
null(0):-inp269(a,b,c),funcinp274(a,b,c,d).
null(0):-inp270(a,b,c),funcinp275(a,b,c,d).
null(0):-inp271(a,b,c),funcinp276(a,b,c,d).
null(0):-inp272(a,b,c),funcinp277(a,b,c,d).
null(0):-inp273(a,b,c),funcinp278(a,b,c,d).
null(0):-inp274(a,b,c),funcinp279(a,b,c,d).
null(0):-inp275(a,b,c),funcinp280(a,b,c,d).
null(0):-inp276(a,b,c),funcinp281(a,b,c,d).
null(0):-inp277(a,b,c),funcinp282(a,b,c,d).
null(0):-inp278(a,b,c),funcinp283(a,b,c,d).
null(0):-inp279(a,b,c),funcinp284(a,b,c,d).
null(0):-inp280(a,b,c),funcinp285(a,b,c,d).
null(0):-inp281(a,b,c),funcinp286(a,b,c,d).
null(0):-inp282(a,b,c),funcinp287(a,b,c,d).
null(0):-inp283(a,b,c),funcinp288(a,b,c,d).
null(0):-inp284(a,b,c),funcinp289(a,b,c,d).
null(0):-inp285(a,b,c),funcinp290(a,b,c,d).
null(0):-inp286(a,b,c),funcinp291(a,b,c,d).
null(0):-inp287(a,b,c),funcinp292(a,b,c,d).
null(0):-inp288(a,b,c),funcinp293(a,b,c,d).
null(0):-inp289(a,b,c),funcinp294(a,b,c,d).
null(0):-inp290(a,b,c),funcinp295(a,b,c,d).
null(0):-inp291(a,b,c),funcinp296(a,b,c,d).
null(0):-inp292(a,b,c),funcinp297(a,b,c,d).
null(0):-inp293(a,b,c),funcinp298(a,b,c,d).
null(0):-inp294(a,b,c),funcinp299(a,b,c,d).
null(0):-inp295(a,b,c),funcinp300(a,b,c,d).
null(0):-inp296(a,b,c),funcinp301(a,b,c,d).
null(0):-inp297(a,b,c),funcinp302(a,b,c,d).
null(0):-inp298(a,b,c),funcinp303(a,b,c,d).
null(0):-inp299(a,b,c),funcinp304(a,b,c,d).
null(0):-inp300(a,b,c),funcinp305(a,b,c,d).
null(0):-inp301(a,b,c),funcinp306(a,b,c,d).
null(0):-inp302(a,b,c),funcinp307(a,b,c,d).
null(0):-inp303(a,b,c),funcinp308(a,b,c,d).
null(0):-inp304(a,b,c),funcinp309(a,b,c,d).
null(0):-inp305(a,b,c),funcinp310(a,b,c,d).
null(0):-inp306(a,b,c),funcinp311(a,b,c,d).
null(0):-inp307(a,b,c),funcinp312(a,b,c,d).
null(0):-inp308(a,b,c),funcinp313(a,b,c,d).
null(0):-inp309(a,b,c),funcinp314(a,b,c,d).
null(0):-inp310(a,b,c),funcinp315(a,b,c,d).
null(0):-inp311(a,b,c),funcinp316(a,b,c,d).
null(0):-inp312(a,b,c),funcinp317(a,b,c,d).
null(0):-inp313(a,b,c),funcinp318(a,b,c,d).
null(0):-inp314(a,b,c),funcinp319(a,b,c,d).
null(0):-inp315(a,b,c),funcinp320(a,b,c,d).
null(0):-inp316(a,b,c),funcinp321(a,b,c,d).
null(0):-inp317(a,b,c),funcinp322(a,b,c,d).
null(0):-inp318(a,b,c),funcinp323(a,b,c,d).
null(0):-inp319(a,b,c),funcinp324(a,b,c,d).
null(0):-inp320(a,b,c),funcinp325(a,b,c,d).
null(0):-inp321(a,b,c),funcinp326(a,b,c,d).
null(0):-inp322(a,b,c),funcinp327(a,b,c,d).
null(0):-inp323(a,b,c),funcinp328(a,b,c,d).
null(0):-inp324(a,b,c),funcinp329(a,b,c,d).
null(0):-inp325(a,b,c),funcinp330(a,b,c,d).
null(0):-inp326(a,b,c),funcinp331(a,b,c,d).
null(0):-inp327(a,b,c),funcinp332(a,b,c,d).
null(0):-inp328(a,b,c),funcinp333(a,b,c,d).
null(0):-inp329(a,b,c),funcinp334(a,b,c,d).
null(0):-inp330(a,b,c),funcinp335(a,b,c,d).
null(0):-inp331(a,b,c),funcinp336(a,b,c,d).
null(0):-inp332(a,b,c),funcinp337(a,b,c,d).
null(0):-inp333(a,b,c),funcinp338(a,b,c,d).
null(0):-inp334(a,b,c),funcinp339(a,b,c,d).
null(0):-inp335(a,b,c),funcinp340(a,b,c,d).
null(0):-inp336(a,b,c),funcinp341(a,b,c,d).
null(0):-inp337(a,b,c),funcinp342(a,b,c,d).
null(0):-inp338(a,b,c),funcinp343(a,b,c,d).
null(0):-inp339(a,b,c),funcinp344(a,b,c,d).
null(0):-inp340(a,b,c),funcinp345(a,b,c,d).
null(0):-inp341(a,b,c),funcinp346(a,b,c,d).
null(0):-inp342(a,b,c),funcinp347(a,b,c,d).
null(0):-inp343(a,b,c),funcinp348(a,b,c,d).
null(0):-inp344(a,b,c),funcinp349(a,b,c,d).
null(0):-inp345(a,b,c),funcinp350(a,b,c,d).
null(0):-inp346(a,b,c),funcinp351(a,b,c,d).
null(0):-inp347(a,b,c),funcinp352(a,b,c,d).
null(0):-inp348(a,b,c),funcinp353(a,b,c,d).
null(0):-inp349(a,b,c),funcinp354(a,b,c,d).
null(0):-inp350(a,b,c),funcinp355(a,b,c,d).
null(0):-inp351(a,b,c),funcinp356(a,b,c,d).
null(0):-inp352(a,b,c),funcinp357(a,b,c,d).
null(0):-inp353(a,b,c),funcinp358(a,b,c,d).
null(0):-inp354(a,b,c),funcinp359(a,b,c,d).
null(0):-inp355(a,b,c),funcinp360(a,b,c,d).
null(0):-inp356(a,b,c),funcinp361(a,b,c,d).
null(0):-inp357(a,b,c),funcinp362(a,b,c,d).
null(0):-inp358(a,b,c),funcinp363(a,b,c,d).
null(0):-inp359(a,b,c),funcinp364(a,b,c,d).
null(0):-inp360(a,b,c),funcinp365(a,b,c,d).
null(0):-inp361(a,b,c),funcinp366(a,b,c,d).
null(0):-inp362(a,b,c),funcinp367(a,b,c,d).
null(0):-inp363(a,b,c),funcinp368(a,b,c,d).
null(0):-inp364(a,b,c),funcinp369(a,b,c,d).
null(0):-inp365(a,b,c),funcinp370(a,b,c,d).
null(0):-inp366(a,b,c),funcinp371(a,b,c,d).
null(0):-inp367(a,b,c),funcinp372(a,b,c,d).
null(0):-inp368(a,b,c),funcinp373(a,b,c,d).
null(0):-inp369(a,b,c),funcinp374(a,b,c,d).
null(0):-inp370(a,b,c),funcinp375(a,b,c,d).
null(0):-inp371(a,b,c),funcinp376(a,b,c,d).
null(0):-inp372(a,b,c),funcinp377(a,b,c,d).
null(0):-inp373(a,b,c),funcinp378(a,b,c,d).
null(0):-inp374(a,b,c),funcinp379(a,b,c,d).
null(0):-inp375(a,b,c),funcinp380(a,b,c,d).
null(0):-inp376(a,b,c),funcinp381(a,b,c,d).
null(0):-inp377(a,b,c),funcinp382(a,b,c,d).
null(0):-inp378(a,b,c),funcinp383(a,b,c,d).
null(0):-inp379(a,b,c),funcinp384(a,b,c,d).
null(0):-inp380(a,b,c),funcinp385(a,b,c,d).
null(0):-inp381(a,b,c),funcinp386(a,b,c,d).
null(0):-inp382(a,b,c),funcinp387(a,b,c,d).
null(0):-inp383(a,b,c),funcinp388(a,b,c,d).
null(0):-inp384(a,b,c),funcinp389(a,b,c,d).
null(0):-inp385(a,b,c),funcinp390(a,b,c,d).
null(0):-inp386(a,b,c),funcinp391(a,b,c,d).
null(0):-inp387(a,b,c),funcinp392(a,b,c,d).
null(0):-inp388(a,b,c),funcinp393(a,b,c,d).
null(0):-inp389(a,b,c),funcinp394(a,b,c,d).
null(0):-inp390(a,b,c),funcinp395(a,b,c,d).
null(0):-inp391(a,b,c),funcinp396(a,b,c,d).
null(0):-inp392(a,b,c),funcinp397(a,b,c,d).
null(0):-inp393(a,b,c),funcinp398(a,b,c,d).
null(0):-inp394(a,b,c),funcinp399(a,b,c,d).
null(0):-inp395(a,b,c),funcinp400(a,b,c,d).
null(0):-inp396(a,b,c),funcinp401(a,b,c,d).
null(0):-inp397(a,b,c),funcinp402(a,b,c,d).
null(0):-inp398(a,b,c),funcinp403(a,b,c,d).
null(0):-inp399(a,b,c),funcinp404(a,b,c,d).
null(0):-inp400(a,b,c),funcinp405(a,b,c,d).
null(0):-inp401(a,b,c),funcinp406(a,b,c,d).
null(0):-inp402(a,b,c),funcinp407(a,b,c,d).
null(0):-inp403(a,b,c),funcinp408(a,b,c,d).
null(0):-inp404(a,b,c),funcinp409(a,b,c,d).
null(0):-inp405(a,b,c),funcinp410(a,b,c,d).
null(0):-inp406(a,b,c),funcinp411(a,b,c,d).
null(0):-inp407(a,b,c),funcinp412(a,b,c,d).
null(0):-inp408(a,b,c),funcinp413(a,b,c,d).
null(0):-inp409(a,b,c),funcinp414(a,b,c,d).
null(0):-inp410(a,b,c),funcinp415(a,b,c,d).
null(0):-inp411(a,b,c),funcinp416(a,b,c,d).
null(0):-inp412(a,b,c),funcinp417(a,b,c,d).
null(0):-inp413(a,b,c),funcinp418(a,b,c,d).
null(0):-inp414(a,b,c),funcinp419(a,b,c,d).
null(0):-inp415(a,b,c),funcinp420(a,b,c,d).
null(0):-inp416(a,b,c),funcinp421(a,b,c,d).
null(0):-inp417(a,b,c),funcinp422(a,b,c,d).
null(0):-inp418(a,b,c),funcinp423(a,b,c,d).
null(0):-inp419(a,b,c),funcinp424(a,b,c,d).
null(0):-inp420(a,b,c),funcinp425(a,b,c,d).
null(0):-inp421(a,b,c),funcinp426(a,b,c,d).
null(0):-inp422(a,b,c),funcinp427(a,b,c,d).
null(0):-inp423(a,b,c),funcinp428(a,b,c,d).
null(0):-inp424(a,b,c),funcinp429(a,b,c,d).
null(0):-inp425(a,b,c),funcinp430(a,b,c,d).
null(0):-inp426(a,b,c),funcinp431(a,b,c,d).
null(0):-inp427(a,b,c),funcinp432(a,b,c,d).
null(0):-inp428(a,b,c),funcinp433(a,b,c,d).
null(0):-inp429(a,b,c),funcinp434(a,b,c,d).
null(0):-inp430(a,b,c),funcinp435(a,b,c,d).
null(0):-inp431(a,b,c),funcinp436(a,b,c,d).
null(0):-inp432(a,b,c),funcinp437(a,b,c,d).
null(0):-inp433(a,b,c),funcinp438(a,b,c,d).
null(0):-inp434(a,b,c),funcinp439(a,b,c,d).
null(0):-inp435(a,b,c),funcinp440(a,b,c,d).
null(0):-inp436(a,b,c),funcinp441(a,b,c,d).
null(0):-inp437(a,b,c),funcinp442(a,b,c,d).
null(0):-inp438(a,b,c),funcinp443(a,b,c,d).
null(0):-inp439(a,b,c),funcinp444(a,b,c,d).
null(0):-inp440(a,b,c),funcinp445(a,b,c,d).
null(0):-inp441(a,b,c),funcinp446(a,b,c,d).
null(0):-inp442(a,b,c),funcinp447(a,b,c,d).
null(0):-inp443(a,b,c),funcinp448(a,b,c,d).
null(0):-inp444(a,b,c),funcinp449(a,b,c,d).
null(0):-inp445(a,b,c),funcinp450(a,b,c,d).
null(0):-inp446(a,b,c),funcinp451(a,b,c,d).
null(0):-inp447(a,b,c),funcinp452(a,b,c,d).
null(0):-inp448(a,b,c),funcinp453(a,b,c,d).
null(0):-inp449(a,b,c),funcinp454(a,b,c,d).
null(0):-inp450(a,b,c),funcinp455(a,b,c,d).
null(0):-inp451(a,b,c),funcinp456(a,b,c,d).
null(0):-inp452(a,b,c),funcinp457(a,b,c,d).
null(0):-inp453(a,b,c),funcinp458(a,b,c,d).
null(0):-inp454(a,b,c),funcinp459(a,b,c,d).
null(0):-inp455(a,b,c),funcinp460(a,b,c,d).
null(0):-inp456(a,b,c),funcinp461(a,b,c,d).
null(0):-inp457(a,b,c),funcinp462(a,b,c,d).
null(0):-inp458(a,b,c),funcinp463(a,b,c,d).
null(0):-inp459(a,b,c),funcinp464(a,b,c,d).
null(0):-inp460(a,b,c),funcinp465(a,b,c,d).
null(0):-inp461(a,b,c),funcinp466(a,b,c,d).
null(0):-inp462(a,b,c),funcinp467(a,b,c,d).
null(0):-inp463(a,b,c),funcinp468(a,b,c,d).
null(0):-inp464(a,b,c),funcinp469(a,b,c,d).
null(0):-inp465(a,b,c),funcinp470(a,b,c,d).
null(0):-inp466(a,b,c),funcinp471(a,b,c,d).
null(0):-inp467(a,b,c),funcinp472(a,b,c,d).
null(0):-inp468(a,b,c),funcinp473(a,b,c,d).
null(0):-inp469(a,b,c),funcinp474(a,b,c,d).
null(0):-inp470(a,b,c),funcinp475(a,b,c,d).
null(0):-inp471(a,b,c),funcinp476(a,b,c,d).
null(0):-inp472(a,b,c),funcinp477(a,b,c,d).
null(0):-inp473(a,b,c),funcinp478(a,b,c,d).
null(0):-inp474(a,b,c),funcinp479(a,b,c,d).
null(0):-inp475(a,b,c),funcinp480(a,b,c,d).
null(0):-inp476(a,b,c),funcinp481(a,b,c,d).
null(0):-inp477(a,b,c),funcinp482(a,b,c,d).
null(0):-inp478(a,b,c),funcinp483(a,b,c,d).
null(0):-inp479(a,b,c),funcinp484(a,b,c,d).
null(0):-inp480(a,b,c),funcinp485(a,b,c,d).
null(0):-inp481(a,b,c),funcinp486(a,b,c,d).
null(0):-inp482(a,b,c),funcinp487(a,b,c,d).
null(0):-inp483(a,b,c),funcinp488(a,b,c,d).
null(0):-inp484(a,b,c),funcinp489(a,b,c,d).
null(0):-inp485(a,b,c),funcinp490(a,b,c,d).
null(0):-inp486(a,b,c),funcinp491(a,b,c,d).
null(0):-inp487(a,b,c),funcinp492(a,b,c,d).
null(0):-inp488(a,b,c),funcinp493(a,b,c,d).
null(0):-inp489(a,b,c),funcinp494(a,b,c,d).
null(0):-inp490(a,b,c),funcinp495(a,b,c,d).
null(0):-inp491(a,b,c),funcinp496(a,b,c,d).
null(0):-inp492(a,b,c),funcinp497(a,b,c,d).
null(0):-inp493(a,b,c),funcinp498(a,b,c,d).
null(0):-inp494(a,b,c),funcinp499(a,b,c,d).
null(0):-inp495(a,b,c),funcinp500(a,b,c,d).
null(0):-inp496(a,b,c),funcinp1(a,b,c,d).
null(0):-inp497(a,b,c),funcinp2(a,b,c,d).
null(0):-inp498(a,b,c),funcinp3(a,b,c,d).
null(0):-inp499(a,b,c),funcinp4(a,b,c,d).

null(0):-inp1(a,b,c),funcinp7(a,b,c,d).
null(0):-inp2(a,b,c),funcinp8(a,b,c,d).
null(0):-inp3(a,b,c),funcinp9(a,b,c,d).
null(0):-inp4(a,b,c),funcinp10(a,b,c,d).
null(0):-inp5(a,b,c),funcinp11(a,b,c,d).
null(0):-inp6(a,b,c),funcinp12(a,b,c,d).
null(0):-inp7(a,b,c),funcinp13(a,b,c,d).
null(0):-inp8(a,b,c),funcinp14(a,b,c,d).
null(0):-inp9(a,b,c),funcinp15(a,b,c,d).
null(0):-inp10(a,b,c),funcinp16(a,b,c,d).
null(0):-inp11(a,b,c),funcinp17(a,b,c,d).
null(0):-inp12(a,b,c),funcinp18(a,b,c,d).
null(0):-inp13(a,b,c),funcinp19(a,b,c,d).



%table inp501(a:int8, b:int8, c:int8) {index(0(a)), timeout(field(c), state_function)}
%table mid501(a:int8, b:int8, c:int8) {index(0(a))}
%table out501(a:int8, b:int8, c:int8) {index(0(a)), tbm}
%table inp504(a:int8, b:int8, c:int8) {index(0(a, b, c))}
%table inp505(a:int8, b:int8, c:int8) {index(0(a, b, c))}
%table inp507(a:int8, b:int8, c:int8) {index(0(a, b)), update_partial, update_by_rank}

%table mid504(a:int8, b:int8, c:int8)
%table mid505(a:int8, b:int8, c:int8) {index(0(a, b, c))}
%table mid506(a:int8, b:int8, c:int8) {index(0(a, b)), state}


%table out505(a:int8, b:int8, c:int8) {index(0(a, b, c))}
%table out507(a:int8, b:int8, c:int8) {index(0(a, b))}
%table msg(a:int8, b:int8, c:int8) {index(0(a, b, c)), msg_notify, batch_msg_size(2), ordered}

%function init()
%function uninit()

%function pre_invoke()
%function post_invoke()
%function funcinp501(a:int8, b:int8, c:int8 -> d:int8)
%function funcstat(a: int8 -> b:int8) { state_transfer }

%aggregate agginp504(a:int8 -> b:int8){
    many_to_one
}

%aggregate agginp505(a:int8 -> b:int8){
    many_to_many,
    ordered
}

mid501(a, b, d) :- inp501(a, b, c), funcinp501(a, b, c, d).
out501(a, b, c) :- mid501(a, b, c).

mid504(a, b, d) :- inp504(a, b, c) GROUP-BY(a, b) agginp504(c, d).
msg(a, b, c) :- mid504(a, b, c).

mid505(a, b, d) :- inp505(a, b, c) GROUP-BY(a, b) agginp505(c, d).
out505(a, b, c) :- mid505(a, b, c).

mid506(a, b, d) :- inp507(a, b, c), funcstat(c, d).
out507(a, b, c) :- mid506(a, b, c).
