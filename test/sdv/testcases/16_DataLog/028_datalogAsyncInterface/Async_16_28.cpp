/* ****************************************************************************
 Description  : datalog异步接口1222
 Node      :

 Author       : 杨柠蔚 ywx1037054
 Modification :
 Date         : 2023/2/3
 node : 
**************************************************************************** */

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "AsyncInterface.h"

class DatalogAsyncInterface : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        char result[64] = { 0 };
        ret = GetPrintBycmd("cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, g_libDir, sizeof(g_libDir));
        EXPECT_EQ(0, ret);
        ret = GetPrintBycmd("cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, result, 64);
        EXPECT_EQ(0, ret);
        SystemSnprintf("mkdir -p %s", g_libDir);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {
        SystemSnprintf("rm -rf %s", g_libDir);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogAsyncInterface::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret = 0;
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh");
        system("modifyCfg.sh recover");
    } else {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh");
        system("modifyCfg.sh recover");
    }
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void DatalogAsyncInterface::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg ();
    testEnvClean();
}

class DatalogAsyncInterfacePrivs : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        char result[64] = { 0 };
        ret = GetPrintBycmd("cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, g_libDir, sizeof(g_libDir));
        EXPECT_EQ(0, ret);
        ret = GetPrintBycmd("cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, result, 64);
        EXPECT_EQ(0, ret);
        SystemSnprintf("mkdir -p %s", g_libDir);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {
        SystemSnprintf("rm -rf %s", g_libDir);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogAsyncInterfacePrivs::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    int ret = 0;
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void DatalogAsyncInterfacePrivs::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg ();
    testEnvClean();
    system("modifyCfg.sh recover");
}
// 001. 加载常规表写入数据,新增接口写入数据a
TEST_F(DatalogAsyncInterface, DataLog_028_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 002.加载transient(tuple)表，新增接口写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_mid.d";
    char udfFileName[] = "base_prefile/transient_tuple_mid_udf.c";
    char libName[] = "base_prefile/transient_tuple_mid.so";
    char nsName[] = "transient_tuple_mid";
    char labelName_in[] = "ns3.C";
    char labelName_mid[] = "ns3.B";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t groupId = 1;
    int32_t sum = 0;
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);
    ret = TestUninstallDatalog("transient_tuple_mid");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 003.加载transient(field)表，新增接口写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in[] = "ns5.C";
    char labelName_mid[] = "ns5.B";
    char labelName_out[] = "ns5.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = 0;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);
    ret = TestUninstallDatalog("transient_field_mid");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 004.加载固定资源池表，新增接口写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int writeCount = 10;
    SingleInt4St *Obj_in = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, SingleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, SingleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = TestUninstallDatalog("resource");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 005.加载pubsub表，新增接口写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"schemaFile/pubResource_ns1.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 1;
    userData->readIdFunc = DoubleInt4_getId;
    userData->checkIdFunc= DoubleInt4_checkId;
    userData->startid = 0;
    userData->endid = 11;
    userData->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;
    int32_t count = userData->count;
    ret = writeRecordIdDtlAsync(conn, stmt, labelName_in, startid, endid, count, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordIdDtlAsync(conn, stmt, labelName_in, startid, endid, count, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, (endid - startid), RECV_TIMEOUT/10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int readCount = endid - startid;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = -2;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = endid - startid;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = -2;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData->data);
    free(userData);

    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 006.加载function表，新增接口写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = TestUninstallDatalog("function");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007. 加载agg表，新增接口写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 2000));
    sleep(2);
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 2000));
    sleep(2);
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("aggregate_toMany");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void AppendTime(int64_t *currentTime, uint32_t appendTime)
{
    if (currentTime == NULL) {
        return;
    }
    // 获取时间
    struct timeval tmValue;
    gettimeofday(&tmValue, NULL);
    // 获取当前时间
    *currentTime = (int64_t)((uint32_t)((tmValue.tv_sec + appendTime) * (uint32_t)1000) +
        (uint32_t)(tmValue.tv_usec / (uint32_t)1000));
}

// 009. 加载过期表，新增接口写入数据[DTS]
TEST_F(DatalogAsyncInterface, DataLog_028_001_009)
{
    int64_t currentTime;
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/timeout_table_001.d";
    char libName[] = "base_prefile/timeout_table_001.so";
    char nsName[] = "timeout_table_001";
    char labelName_in[] = "A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写表
    char tableA[LABEL_NAME] = "A";
    char tableB[LABEL_NAME] = "B";
    int recordNum = 4;
    // 预置过期字段时间值
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 1, 1, 1, 2}, {1, 1, 3, -2, -1}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTableDtl(conn, stmt, tableA, SINGLE_INSERT_DTL, data, recordNum, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // 等待过期字段删除
    sleep(2);
    // 读数据
    int64_t readData[2][5] = {{2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTableDtl(conn, stmt, tableA, READ_DTL, readData, 2, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second read end.");
    
    ret = TestUninstallDatalog("timeout_table_001");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 010. 加载可更新表，新增接口写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_010)
{
    int64_t currentTime;
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/timeout_updatetable_001.d";
    char libName[] = "base_prefile/timeout_updatetable_001.so";
    char nsName[] = "timeout_updatetable_001";
    char labelName_in[] = "A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写表
    char tableA[LABEL_NAME] = "A";
    char tableB[LABEL_NAME] = "B";
    int recordNum = 4;
    // 预置过期字段时间值
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, 1}};
    // 写数据
    ret = testTableDtl(conn, stmt, tableA, SINGLE_INSERT_DTL, data, recordNum, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // 等待过期字段删除
    sleep(2);
    // 读数据
    int64_t readData[2][5] = {{2, 1, 1, 1, 1}, {2, 2, 2, 1, 1}};
    ret = testTableDtl(conn, stmt, tableA, READ_DTL, readData, 2, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second read end.");
    ret = TestUninstallDatalog("timeout_updatetable_001");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 011. 加载常规表, 新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 012. 加载transient(tuple)表，新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_tuple_mid.d";
    char udfFileName[] = "base_prefile/transient_tuple_mid_udf.c";
    char libName[] = "base_prefile/transient_tuple_mid.so";
    char nsName[] = "transient_tuple_mid";
    char labelName_in[] = "ns3.C";
    char labelName_mid[] = "ns3.B";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t groupId = 1;
    int32_t sum = 0;
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);
    ret = TestUninstallDatalog("transient_tuple_mid");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 013. 加载transient(field)表，新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in[] = "ns5.C";
    char labelName_mid[] = "ns5.B";
    char labelName_out[] = "ns5.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = 0;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);
    ret = TestUninstallDatalog("transient_field_mid");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 014. 加载固定资源池表，新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int writeCount = 11;
    SingleInt4St *Obj_in = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, SingleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, SingleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = TestUninstallDatalog("resource");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 015. 加载pubsub表，新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"schemaFile/pubResource_ns1.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 1;
    userData->readIdFunc = DoubleInt4_getId;
    userData->checkIdFunc= DoubleInt4_checkId;
    userData->startid = 0;
    userData->endid = 11;
    userData->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;
    int32_t count = userData->count;
    ret = writeRecordIdDtlAsync(conn, stmt, labelName_in, startid, endid, count, SingleInt4_setId, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordIdDtlAsync(conn, stmt, labelName_in, startid, endid, count, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, (endid - startid), RECV_TIMEOUT/10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int readCount = endid - startid;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = -2;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = endid - startid;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = -2;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData->data);
    free(userData);

    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 016. 加载function表，新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = TestUninstallDatalog("function");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 017. 加载agg表，新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 2000));
    sleep(2);
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 2000));
    sleep(2);
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecordDtlAsync(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("aggregate_toMany");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 019. 加载过期表，新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_019)
{
    int64_t currentTime;
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/timeout_table_001.d";
    char libName[] = "base_prefile/timeout_table_001.so";
    char nsName[] = "timeout_table_001";
    char labelName_in[] = "A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写表
    char tableA[LABEL_NAME] = "A";
    char tableB[LABEL_NAME] = "B";
    int recordNum = 4;
    // 预置过期字段时间值
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 1, 1, 1, 2}, {1, 1, 3, -2, -1}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTableDtl(conn, stmt, tableA, BATCH_INSERT_DTL, data, recordNum, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // 等待过期字段删除
    sleep(2);
    // 读数据
    int64_t readData[2][5] = {{2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTableDtl(conn, stmt, tableA, READ_DTL, readData, 2, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second read end.");
    
    ret = TestUninstallDatalog("timeout_table_001");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 020. 加载可更新表，新增接口批量写入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_020)
{
    int64_t currentTime;
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/timeout_updatetable_001.d";
    char libName[] = "base_prefile/timeout_updatetable_001.so";
    char nsName[] = "timeout_updatetable_001";
    char labelName_in[] = "A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写表
    char tableA[LABEL_NAME] = "A";
    char tableB[LABEL_NAME] = "B";
    int recordNum = 4;
    // 预置过期字段时间值
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {1, 1, 3, -2, 1}, {2, 1, 1, 1, 1}, {2, 2, 2, 1, 1}};
    // 写数据
    ret = testTableDtl(conn, stmt, tableA, BATCH_INSERT_DTL, data, recordNum, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    // 等待过期字段删除
    sleep(2);
    // 读数据
    int64_t readData[2][5] = {{2, 1, 1, 1, 1}, {2, 2, 2, 1, 1}};
    ret = testTableDtl(conn, stmt, tableA, READ_DTL, readData, 2, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second read end.");
    ret = TestUninstallDatalog("timeout_updatetable_001");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 021. 使用GmcExecute，GmcBatchExecute写入数据预期正常没触发流控
// INPUT_QUEUE_PUSH_SUCCEED_COUNT对比
TEST_F(DatalogAsyncInterface, DataLog_028_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_QUEUE ", (char *)"INPUT_QUEUE_PUSH_SUCCEED_COUNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 022. 使用GmcExecuteDtlAsync，GmcBatchExecuteDtlAsync写入数据预期正常触发流控
TEST_F(DatalogAsyncInterface, DataLog_028_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_QUEUE ", (char *)"INPUT_QUEUE_NUM: 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 023. 进行datalog交互事务，使用新接口报错
// 交互式事务不走流控
TEST_F(DatalogAsyncInterface, DataLog_028_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    GmcTransStart(conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    // 交互式事务不支持流控
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(Obj_out);
    GmcTransCommit(conn);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 024. 非datalog表无法使用新接口，会返回错误码[DTS]
TEST_F(DatalogAsyncInterface, DataLog_028_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *test_schema = NULL;
    readJanssonFile("schemaFile/normal.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, g_glabelconfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(test_schema);

    ret = testGmcPrepareStmtByLabelName(stmt, "Person_T", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t ID_value = 10;
    // 注意改成使用新接口
    for (int i = 0; i < 1; i++) {
        ret = GmcSetVertexProperty(stmt, "ID", GMC_DATATYPE_UINT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 回答：新接口仅支持Datalog表使用，正常
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
    ret = GmcDropVertexLabel(stmt, "Person_T");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 025. 普通表GmcExecuteDtlAsync，GmcBatchExecuteDtlAsync使用异步连接stmt,操作报错[DTS]
TEST_F(DatalogAsyncInterface, DataLog_028_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    GmcConnT *asyncConn;
    GmcStmtT *asyncStmt;
    AsyncUserDataT userData = {0};
    ret = testGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = testGmcPrepareStmtByLabelName(asyncStmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(asyncConn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 1; i++) {
        ret = GmcSetVertexProperty(asyncStmt, "a", GMC_DATATYPE_INT32, &Obj_in->a, sizeof(Obj_in->a));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(asyncStmt, "b", GMC_DATATYPE_INT32, &Obj_in->b, sizeof(Obj_in->b));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(asyncStmt, "dtlReservedCount", GMC_DATATYPE_INT32, &Obj_in->dtlReservedCount,
                                   sizeof(Obj_in->dtlReservedCount));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, asyncStmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 回答：这里的conn使用的是同步连接，可以正确执行，正常
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
        GmcBatchDestroy(batch);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 026. 普通表GmcExecuteDtlAsync，GmcBatchExecuteDtlAsync使用订阅连接stmt,操作报错
TEST_F(DatalogAsyncInterface, DataLog_028_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    GmcConnT *asyncConn;
    GmcStmtT *asyncStmt;
    ret = testGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(subConn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &Obj_in->a, sizeof(Obj_in->a));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &Obj_in->b, sizeof(Obj_in->b));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &Obj_in->dtlReservedCount,
                                   sizeof(Obj_in->dtlReservedCount));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = testGmcGetLastError(NULL);// 为什么没有日志
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchDestroy(batch);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 027. 普通表不给接口设置权限，使用GmcExecuteDtlAsync，GmcBatchExecuteDtlAsync插入数据预期报错
TEST_F(DatalogAsyncInterfacePrivs, DataLog_028_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    const char *allow_list_file = "./allow_list/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    const char *sys_policy_file = "./gmpolicy_file/VertexLabelAllPolicy.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecordDtlAsync(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    ret = executeCommand(g_command, "remove db user. success: 2");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
}
// 028. GmcExecuteDtlAsync执行DELETE操作，预期报错
// 【batch9007，excute通过】
// 不能允许使用更新，不允许删除,[插入/删除]
TEST_F(DatalogAsyncInterface, DataLog_028_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0_value = 0;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 029. GmcBatchExecuteDtlAsync执行delete操作，预期报错
TEST_F(DatalogAsyncInterface, DataLog_028_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0_value = 0;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 030. GmcExecuteDtlAsync执行update操作，预期报错[DTSprepare处报错]
TEST_F(DatalogAsyncInterface, DataLog_028_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0_value = 0;
    int32_t ID_value = 10;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ID_value = ID_value + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 回答：老问题，是DataService标记位的初始化问题，和新特性无关
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 031. GmcBatchExecuteDtlAsync执行update操作，预期报错
TEST_F(DatalogAsyncInterface, DataLog_028_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0_value = 0;
    int32_t ID_value = 10;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ID_value = ID_value + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 032. GmcExecuteDtlAsync执行replace操作，预期报错[DTS，GmcExecute没报错]
TEST_F(DatalogAsyncInterface, DataLog_028_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    int32_t f0_value = 0;
    int32_t ID_value = 10;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ID_value = ID_value + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         // 回答：老问题，是DataService标记位的初始化问题，和新特性无关
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 033. GmcBatchExecuteDtlAsync执行replace操作，预期报错
TEST_F(DatalogAsyncInterface, DataLog_028_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0_value = 0;
    int32_t ID_value = 10;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ID_value = ID_value + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 034. GmcExecuteDtlAsync执行merge操作，预期报错【DTS】
TEST_F(DatalogAsyncInterface, DataLog_028_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    int32_t f0_value = 0;
    int32_t ID_value = 10;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ID_value = ID_value + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         // 回答：老问题，是DataService标记位的初始化问题，和新特性无关
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 035. GmcBatchExecuteDtlAsync执行merge操作，预期报错
TEST_F(DatalogAsyncInterface, DataLog_028_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0_value = 0;
    int32_t ID_value = 10;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ID_value = ID_value + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 036. GmcExecuteDtlAsync执行scan操作，预期报错【DTS】
TEST_F(DatalogAsyncInterface, DataLog_028_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0_value = 0;
    int32_t ID_value = 10;
    bool isNull;
    bool isFinish;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ID_value = ID_value + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
         // 回答：老问题，是DataService标记位的初始化问题，和新特性无关
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 037. 结构化使用GmcBatchExecuteDtlAsync插入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_037)
{
    int64_t currentTime;
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/timeout_table_001.d";
    char libName[] = "base_prefile/timeout_table_001.so";
    char nsName[] = "timeout_table_001";
    char labelName_in[] = "A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写表
    char tableA[LABEL_NAME] = "A";
    char tableB[LABEL_NAME] = "B";
    int recordNum = 4;
    // 预置过期字段时间值
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 1, 1, 1, 2}, {1, 1, 3, -2, -1}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTableDtl(conn, stmt, tableA, BATCH_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    // 等待过期字段删除
    sleep(2);
    // 读数据
    int64_t readData[2][5] = {{2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTableDtl(conn, stmt, tableA, STRUCT_READ_DTL, readData, 2, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second read end.");
    
    ret = TestUninstallDatalog("timeout_table_001");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 038. 结构化使用GmcExecuteDtlAsync插入数据
TEST_F(DatalogAsyncInterface, DataLog_028_001_038)
{
    int64_t currentTime;
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/timeout_table_001.d";
    char libName[] = "base_prefile/timeout_table_001.so";
    char nsName[] = "timeout_table_001";
    char labelName_in[] = "A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写表
    char tableA[LABEL_NAME] = "A";
    char tableB[LABEL_NAME] = "B";
    int recordNum = 4;
    // 预置过期字段时间值
    GetTimeMs(&currentTime);
    int64_t data[recordNum][5] = {{1, 2, 2, -1, 1}, {2, 1, 1, 1, 2}, {1, 1, 3, -2, -1}, {2, 2, 2, 1, -2}};
    // 写数据
    ret = testTableDtl(conn, stmt, tableA, SINGLE_STRUCT_INSERT, data, recordNum, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    // 等待过期字段删除
    sleep(2);
    // 读数据
    int64_t readData[2][5] = {{2, 1, 1, 1, 2}, {2, 2, 2, 1, -2}};
    ret = testTableDtl(conn, stmt, tableA, STRUCT_READ_DTL, readData, 2, currentTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second read end.");
    
    ret = TestUninstallDatalog("timeout_table_001");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 039. 普通表GmcBatchExecuteDtlAsync参数1传NULL
TEST_F(DatalogAsyncInterface, DataLog_028_001_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    GmcConnT *asyncConn;
    GmcStmtT *asyncStmt;
    ret = testGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &Obj_in->a, sizeof(Obj_in->a));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &Obj_in->b, sizeof(Obj_in->b));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &Obj_in->dtlReservedCount,
                                   sizeof(Obj_in->dtlReservedCount));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(NULL, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);// 为什么没有日志
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchDestroy(batch);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 040. 普通表GmcBatchExecuteDtlAsync参数2传NULL
TEST_F(DatalogAsyncInterface, DataLog_028_001_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    GmcConnT *asyncConn;
    GmcStmtT *asyncStmt;
    ret = testGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &Obj_in->a, sizeof(Obj_in->a));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &Obj_in->b, sizeof(Obj_in->b));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &Obj_in->dtlReservedCount,
                                   sizeof(Obj_in->dtlReservedCount));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        ret = testGmcGetLastError(NULL);// 为什么没有日志
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchDestroy(batch);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 041. 普通表GmcExecuteDtlAsync参数1传NULL
TEST_F(DatalogAsyncInterface, DataLog_028_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecordDtlAsync(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordDtlAsync(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0_value = 0;
    int32_t ID_value = 10;
    for (int i = 0; i < 5; i++) {
        f0_value = i;
        ID_value = ID_value + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &ID_value, sizeof(ID_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    }
    free(Obj_in);
    free(Obj_out);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
