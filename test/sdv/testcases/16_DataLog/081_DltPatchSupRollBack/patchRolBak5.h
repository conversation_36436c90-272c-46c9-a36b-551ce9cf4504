/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: patchRolBak5.h
 * Description: patchRolBak5文件
 * Author:
 * Create: 2024-04-19
 */

#ifndef PATCHROLBAK5_H
#define PATCHROLBAK5_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define FILE_PATH 512
#define MAX_CMD_SIZE 1024

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_command[MAX_CMD_SIZE];
char g_hFile[FILE_PATH] = "../../../../../pub/include/";
char const *g_viewName = "V\\$PTL_DATALOG_PATCH_INFO";
char const *g_viewName2 = "V\\$CATA_UDF_INFO";
char g_ns[] = "public";

#pragma pack(1)
typedef struct {
    int32_t tabelMemtexValue;
    int32_t udfMemtexValue;
    int32_t planListMemtexValue;
} DatalogCtxValue;
#pragma pack(0)

using namespace std;

int get_value(const char *command, string key_word)
{
    const char *keyWordCh = key_word.c_str();
    string value_str;
    int value;
    char cmdOutPut[1024] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        cout << endl << "WRONG!" << endl;
        return -1;
    }
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        value_str.assign(cmdOutPut);
        string::size_type idx = value_str.find(key_word);
        if (idx != string::npos) {
            value_str = value_str.substr(value_str.find(key_word) + key_word.length());
            value = stoi(value_str);
            break;
        }
    }
    pclose(pf);

    return value;
}

const char *g_schemaJson = R"([{
    "type":"record",
    "name":"%s",
    "fields":[
        {"name":"F7", "type":"uint32",  "nullable":false},
	    {"name":"F0", "type":"char",    "nullable":false},
	    {"name":"F1", "type":"uchar",   "nullable":true},
        {"name":"F2", "type":"int8",    "nullable":true},
        {"name":"F3", "type":"uint8",   "nullable":true},
        {"name":"F4", "type":"int16",   "nullable":true},
        {"name":"F5", "type":"uint16",  "nullable":true},
        {"name":"F6", "type":"int32",   "nullable":true},
        {"name":"F8", "type":"boolean", "nullable":true},
        {"name":"F9", "type":"int64",   "nullable":true},
        {"name":"F10", "type":"uint64", "nullable":true},
        {"name":"F11", "type":"float",  "nullable":true},
        {"name":"F12", "type":"double", "nullable":true},
        {"name":"F13", "type":"time",   "nullable":true},
        {"name":"F14", "type":"string", "nullable":true, "size":100},
        {"name":"F15", "type":"bytes",  "nullable":true, "size":10 },
        {"name":"F16", "type":"fixed",  "nullable":true, "size":5 },
        {"name":"F17", "type":"uint32", "nullable":true}
    ],
    "keys":[
       {
            "node":"%s",
            "name":"T20_PK",
            "fields":["F7"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
])";

// 仅统计当前现存normal表不包含yang
int testGetNormalTableNum(uint32_t *tableNum)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    int ret = 0;
    uint32_t isDeleted = 0;
    if (tableNum == NULL) {
        printf("[testGetNormalTableNum]:null pointer error\n");
        return FAILED;
    } else {
        *tableNum = 0;
    }
    bool innerError = false;
    char command[1024];
    char const *view_name = "V\\$CATA_VERTEX_LABEL_INFO";
    ret = snprintf(command, 1024, "%s/gmsysview -q %s ", g_toolPath, view_name);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer[150] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        perror("popen fail");
        return FAILED;
    }
    // 判断视图信息
    char *tmp = fgets(buffer, 150, pf);
    // 无打印
    if (tmp == NULL) {
        printf("[CATA_VERTEX_LABEL_INFO]:no print\n");
        if (pclose(pf) == -1) {
            perror("pclose fail");
        }
        return FAILED;
    }
    // 无表
    if (strstr(buffer, "fetched all records, finish") != NULL) {
        printf("[CATA_VERTEX_LABEL_INFO]:no table left in server\n");
        if (pclose(pf) == -1) {
            perror("pclose fail");
            return FAILED;
        }
        return 0;
    }
    // 非预期报错，打印输出
    if (strstr(buffer, "index = 0") == NULL) {
        innerError = true;
        printf("[CATA_VERTEX_LABEL_INFO]inner error:\n%s\n", buffer);
    }
    // 继续打印视图错误信息或者累计表数目
    while (fgets(buffer, 150, pf) != NULL) {
        if (innerError) {  // 继续打印视图错误信息
            printf("%s\n", buffer);
        } else {
            if (strstr(buffer, "VERTEX_TYPE_NORMAL") != NULL) {
                (*tableNum)++;
            }
            if (strstr(buffer, "VERTEX_TYPE_DATALOG") != NULL) {
                (*tableNum)++;
            }
            if (strstr(buffer, "IS_DELETED: 1") != NULL) {
                isDeleted++;
                // 查询具体是那张表被标记删除
                system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f IS_DELETED=1");
            }
        }
    }
    if (pclose(pf) == -1) {
        perror("pclose fail");
        return FAILED;
    } else if (innerError) {
        return FAILED;
    }

    // 查询TBM_TABLE_NUM
    char command2[1024];
    char const *view_name2 = "V\\$CATA_GENERAL_INFO";
    ret = snprintf(command2, 1024, "%s/gmsysview -q %s |grep TBM_TABLE_NUM |awk '{print $2}'", g_toolPath, view_name2);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer2[150] = {0};
    FILE *pf2 = popen(command2, "r");
    if (pf2 == NULL) {
        perror("popen fail");
        return FAILED;
    }

    int size = 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        (void)pclose(pf2);
        printf("malloc failed, %s.", strerror(errno));
        return FAILED;
    }
    memset(tmpResult, 0, size);

    while (fgets(buffer2, sizeof(buffer2), pf2) != NULL) {
        strcat(tmpResult, buffer2);
    }

    ret = pclose(pf2);
    if (ret == -1) {
        printf("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return FAILED;
    }

    int tbmTableNum = 0;
    tbmTableNum = atoi(tmpResult);
    free(tmpResult);

    // 查询msg_notify_TABLE_NUM
    char command3[1024];
    ret = snprintf(
        command3, 1024, "%s/gmsysview -q %s |grep MSG_NOTIFY_TABLE_NUM |awk '{print $2}'", g_toolPath, view_name2);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer3[150] = {0};
    FILE *pf3 = popen(command3, "r");
    if (pf3 == NULL) {
        perror("popen fail");
        return FAILED;
    }
    tmpResult = NULL;
    tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        (void)pclose(pf3);
        printf("malloc failed, %s.", strerror(errno));
        return FAILED;
    }
    memset(tmpResult, 0, size);

    while (fgets(buffer3, sizeof(buffer3), pf3) != NULL) {
        strcat(tmpResult, buffer3);
    }

    ret = pclose(pf3);
    if (ret == -1) {
        printf("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return FAILED;
    }

    int msgTableNum = 0;
    msgTableNum = atoi(tmpResult);
    free(tmpResult);

    printf("当前datalog可观测的表+normal表数量为%d\n", *tableNum);
    printf("当前tbm的表数量为%d\n", tbmTableNum);
    printf("当前msgnotify的表数量为%d\n", msgTableNum);
    printf("当前标记删除的表数量为%d\n", isDeleted);

    // 对删除标识为1的数据进行再次查询保证这里仅normal和datalog表的标记删除
    // 查询表记删除表中是否含yang表
    char command4[1024];
    ret = snprintf(
        command4, 1024, "%s/gmsysview -q %s -f IS_DELETED=1|grep VERTEX_TYPE_YANG |wc -l", g_toolPath, view_name);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    FILE *pf4 = popen(command4, "r");
    if (pf4 == NULL) {
        perror("popen fail");
        return FAILED;
    }

    size = 100;
    tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        (void)pclose(pf4);
        printf("malloc failed, %s.", strerror(errno));
        return FAILED;
    }
    memset(tmpResult, 0, size);

    while (fgets(buffer2, sizeof(buffer2), pf4) != NULL) {
        strcat(tmpResult, buffer2);
    }

    ret = pclose(pf4);
    if (ret == -1) {
        printf("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return FAILED;
    }

    int yangTableNum = 0;
    yangTableNum = atoi(tmpResult);
    free(tmpResult);

    printf("[CATA_VERTEX_LABEL_INFO]existTable yangTableNum isdeleteNum: %d\n", yangTableNum);
    *tableNum = *tableNum + tbmTableNum - isDeleted + msgTableNum;
    printf("当前现存表数量为%d\n", *tableNum);
    return 0;
#endif
}

int32_t CreateMuiltTables(GmcStmtT *stmt, int32_t tableNums)
{
    int32_t ret = -1;
    char *schema = NULL;
    char configJson[128] = "{\"max_record_count\" : 10000}";
    char labelName[20] = "T20_all_type";
    uint32_t tableNum = 0;
    ret = testGetNormalTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    tableNums = tableNums - tableNum;
    char schemaJson[2048] = "";
    AW_FUN_Log(LOG_STEP, "create tableNum is %d", tableNums);
    for (int i = 0; i < tableNums; i++) {
        (void)sprintf(labelName, "T20_all_type%d", i);
        (void)sprintf(schemaJson, g_schemaJson, labelName, labelName);
        ret = GmcCreateVertexLabel(stmt, schemaJson, configJson);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema);
    }
    return tableNums;
}

void DropMuiltTables(GmcStmtT *stmt, int32_t tableNums)
{
    int32_t ret = -1;
    char labelName[20] = "T20_all_type";
    AW_FUN_Log(LOG_STEP, "Drop tableNum is %d", tableNums);
    for (int i = 0; i < tableNums; i++) {
        (void)sprintf(labelName, "T20_all_type%d", i);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*
获取datalog 相关ctx value
表:COM_SHMEM_CTX catalog share memory context
udf:COM_DYN_CTX catalog dynamic memory context
plan list:COM_DYN_CTX datalog plan list memCtx
*/
void GetDatalogMemCtxValue(int32_t *tabelMemtexValue, int32_t *udfMemtexValue, int32_t *planListMemtexValue)
{
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command,
        MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME='catalog share memory context'|grep TOTAL_ALLOC_SIZE:|sed "
        "'s/[^0-9]//g'");
    TestGetResultCommand(command, tabelMemtexValue, NULL, 0);
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME='catalog share memory context'");
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command,
        MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='catalog dynamic memory context'|grep TOTAL_ALLOC_SIZE:|sed "
        "'s/[^0-9]//g'");
    TestGetResultCommand(command, udfMemtexValue, NULL, 0);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command,
        MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog plan list memCtx'|grep TOTAL_ALLOC_SIZE:|sed 's/[^0-9]//g'");
    TestGetResultCommand(command, planListMemtexValue, NULL, 0);
    return;
}

int CompareMemCtxValue(DatalogCtxValue *memctxValue1, DatalogCtxValue *memctxValue2)
{
    char command[MAX_CMD_SIZE] = {0};
    if (memctxValue1->tabelMemtexValue != memctxValue2->tabelMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "【ERROR】tableMemctx is InCrease!!!");
        return 1;
    }
    if (memctxValue1->udfMemtexValue != memctxValue2->udfMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "【ERROR】udfMemtex is InCrease!!!");
        return 1;
    }
    if (memctxValue1->planListMemtexValue != memctxValue2->planListMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "【ERROR】planListMemtex is InCrease!!!");
        return 1;
    }
    return GMERR_OK;
}

int singleRecordInsert(GmcStmtT *stmt, char *labelName, int insertValue, int32_t dtlReservedCountValue = 1)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value = insertValue;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCountValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int singleRecordInsert212(GmcStmtT *stmt, char *labelName, int insertValue, int32_t dtlReservedCountValue = 1)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value = insertValue;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (value == 2 || value == 3) {
        value = 1;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCountValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int singleRecordInsert210(GmcStmtT *stmt, char *labelName, int insertValue, int32_t dtlReservedCountValue = 1)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value = insertValue;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    if (value % 2 == 0) {
        value = 1;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCountValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int batchSingleWrite(GmcStmtT *stmt, GmcConnT *conn, char *labelName, int32_t startNum, int32_t endNum)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t maxBatchOpNum = 4;
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, maxBatchOpNum);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t dtlReservedCountValue = 1;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t value = i;
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret =
            GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCountValue, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

#endif
