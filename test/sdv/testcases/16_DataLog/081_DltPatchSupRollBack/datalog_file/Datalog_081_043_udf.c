/*  版权所有 (c) 华为技术有限公司 2022-2023 */
#include <string.h>
#include "stdio.h"
#include "unistd.h"
#include "gm_udf.h"

#pragma pack(1)

typedef struct A {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    uint8_t c[256];
    int64_t d;
    uint32_t bLen;
    char *b;
} A;

// agg比较字段
typedef struct AggCmp {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
} AggCmp;

// 输入字段--聚合字段
typedef struct AggInp {
    int32_t dtlReservedCount;
    int64_t a;
} AggInp;
// 输出字段---count
typedef struct AggOut {
    int64_t a;
} AggOut;

#pragma pack(0)
const char *g_logOrgBName = "/root/_datalog_/RunLogOrgB.txt";
const char *g_logDeltaBName = "/root/_datalog_/RunLogDeltaB.txt";
const char *g_errorLogName = "/root/_datalog_/readkv.txt";

// agg比较函数
int32_t dtl_agg_compare_ns1_agg(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    AggCmp *inp1 = (AggCmp *)tuple1;
    AggCmp *inp2 = (AggCmp *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        if (inp1->b < inp2->b) {
            return -1;
        } else if (inp1->b > inp2->b) {
            return 1;
        }
        return 0;
    }
}

// agg 分组后求行数
int32_t dtl_agg_func_ns1_agg(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    AggInp *inpStruct;
    AggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(AggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t cnt = 0;
    int32_t ret = 0;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        cnt++;
    }
    outStruct->a = cnt;
    if (cnt > 1) {
        return 76;
    }
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
