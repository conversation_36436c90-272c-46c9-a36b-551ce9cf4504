/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-09-13
 */

#ifndef __SUPPORTUDF_H__
#define __SUPPORTUDF_H__

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <pthread.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 512
#define FILE_PATH 512
#define PRINT_INFO 0

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

char g_hFile[FILE_PATH] = "../../../../../pub/include/";

// 执行命令
char g_command[MAX_CMD_SIZE] = {0};
// 原始文件存放目录
char g_orgFile[FILE_PATH] = {0};
char g_inputFile[FILE_PATH] = {0};
char g_udfFile[FILE_PATH] = {0};
// 编译生成文件存放目录
char g_destFile[FILE_PATH] = "planStrFile";
char g_outputFile[FILE_PATH] = {0};
char g_libName[FILE_PATH] = {0};
// 卸载so名称
char g_nsName[FILE_PATH] = {0};

// 存放错误信息
char g_errorMsg[MAX_CMD_SIZE] = {0};

// 存放日志白名单错误码
char g_errorCode01[MAX_CMD_SIZE] = {0};
char g_errorCode02[MAX_CMD_SIZE] = {0};
char g_errorCode03[MAX_CMD_SIZE] = {0};
char g_errorCode04[MAX_CMD_SIZE] = {0};
char g_errorCode05[MAX_CMD_SIZE] = {0};
char g_errorCode06[MAX_CMD_SIZE] = {0};

const char *g_schemaJson1 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

const char *g_schemaJson2 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

const char *g_schemaJson3 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b", "c"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

const char *g_schemaJson4 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "fixed", "size": 8},
            {"name" : "f", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "f"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

const char *g_schemaJson5 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a1", "type" : "int32"},
            {"name" : "a2", "type" : "int32"},
            {"name" : "a3", "type" : "int32"},
            {"name" : "a4", "type" : "int32"},
            {"name" : "a5", "type" : "int32"},
            {"name" : "a6", "type" : "int32"},
            {"name" : "a7", "type" : "int32"},
            {"name" : "a8", "type" : "int32"},
            {"name" : "a9", "type" : "int32"},
            {"name" : "a10", "type" : "int32"},
            {"name" : "a11", "type" : "int32"},
            {"name" : "a12", "type" : "int32"},
            {"name" : "a13", "type" : "int32"},
            {"name" : "a14", "type" : "int32"},
            {"name" : "a15", "type" : "int32"},
            {"name" : "a16", "type" : "int32"},
            {"name" : "a17", "type" : "int32"},
            {"name" : "a18", "type" : "int32"},
            {"name" : "a19", "type" : "int32"},
            {"name" : "a20", "type" : "int32"},
            {"name" : "a21", "type" : "int32"},
            {"name" : "a22", "type" : "int32"},
            {"name" : "a23", "type" : "int32"},
            {"name" : "a24", "type" : "int32"},
            {"name" : "a25", "type" : "int32"},
            {"name" : "a26", "type" : "int32"},
            {"name" : "a27", "type" : "int32"},
            {"name" : "a28", "type" : "int32"},
            {"name" : "a29", "type" : "int32"},
            {"name" : "a30", "type" : "int32"},
            {"name" : "a31", "type" : "int32"}
        ]
    } ])";

const char *g_schemaJson6 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "int32"},
            {"name" : "d", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b", "c", "d"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

const char *g_schemaJson7 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "int32"},
            {"name" : "d", "type" : "int32"},
            {"name" : "e", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b", "c", "d", "e"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

const char *g_schemaJson8 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "fixed", "size": 8},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "fixed", "size": 8}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b", "c"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

void SystemSnprintf(const char *format, ...)
{
    char command[1024];
    va_list p;
    va_start(p, format);
    (void)vsnprintf(command, sizeof(command), format, p);
    va_end(p);

    system(command);
}

// A(a, b)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} TEST_INPUT;
#pragma pack()

// A(a)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
} TEST_INPUT04;
#pragma pack()

// A(a, b, c)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TEST_INPUT02;
#pragma pack()

// A(a:byte8, f:int4)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    uint8_t a[8];
    int32_t b;
} TEST_INPUT03;
#pragma pack()

// A 32个int
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a1;
    int32_t a2;
    int32_t a3;
    int32_t a4;
    int32_t a5;
    int32_t a6;
    int32_t a7;
    int32_t a8;
    int32_t a9;
    int32_t a10;
    int32_t a11;
    int32_t a12;
    int32_t a13;
    int32_t a14;
    int32_t a15;
    int32_t a16;
    int32_t a17;
    int32_t a18;
    int32_t a19;
    int32_t a20;
    int32_t a21;
    int32_t a22;
    int32_t a23;
    int32_t a24;
    int32_t a25;
    int32_t a26;
    int32_t a27;
    int32_t a28;
    int32_t a29;
    int32_t a30;
    int32_t a31;
    int32_t a32;
} TEST_INPUT05;
#pragma pack()

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TEST_FUNC;
#pragma pack()

// B(a, b, c)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TEST_OUTPUT;
#pragma pack()

// B(a, b)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} TEST_OUTPUT02;
#pragma pack()

// B(a, b, c, d)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
} TEST_OUTPUT03;
#pragma pack()

// B(a:byte8, b:int4, c:byte8)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    uint8_t a[8];
    int32_t b;
    uint8_t c[8];
} TEST_OUTPUT04;
#pragma pack()

// B(a)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
} TEST_OUTPUT05;
#pragma pack()

// B(a, b, c, d, e)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
    int32_t e;
} TEST_OUTPUT06;
#pragma pack()

void TEST_INPUTValue(TEST_INPUT *d, int value[])
{
    d->a = value[0];
    d->b = value[1];
    d->dtlReservedCount = value[2];
}

void TEST_INPUTValue02(TEST_INPUT02 *d, int value[])
{
    d->a = value[0];
    d->b = value[1];
    d->c = value[2];
    d->dtlReservedCount = value[3];
}

void TEST_INPUTValue03(TEST_INPUT03 *d, int value[])
{
    uint8_t test[8] = {0}; // .d文件中需要自己补0
    test[0] = value[0];
    memcpy(d->a, test, sizeof(test));
    d->b = value[1];
    d->dtlReservedCount = value[2];
}

void TEST_INPUTValue04(TEST_INPUT04 *d, int value[])
{
    d->a = value[0];
    d->dtlReservedCount = value[1];
}

void TEST_INPUTValue05(TEST_INPUT05 *d, int value[])
{
    d->a1 = value[0];
    d->a2 = value[0];
    d->a3 = value[0];
    d->a4 = value[0];
    d->a5 = value[0];
    d->a6 = value[0];
    d->a7 = value[0];
    d->a8 = value[0];
    d->a9 = value[0];
    d->a10 = value[0];
    d->a11 = value[1];
    d->a12 = value[1];
    d->a13 = value[1];
    d->a14 = value[1];
    d->a15 = value[1];
    d->a16 = value[1];
    d->a17 = value[1];
    d->a18 = value[1];
    d->a19 = value[1];
    d->a20 = value[1];
    d->a21 = value[1];
    d->a22 = value[1];
    d->a23 = value[1];
    d->a24 = value[1];
    d->a25 = value[1];
    d->a26 = value[1];
    d->a27 = value[1];
    d->a28 = value[1];
    d->a29 = value[1];
    d->a30 = value[1];
    d->a31 = value[1];
    d->a32 = value[2];
    d->dtlReservedCount = value[3];
}

// 结构化批量写 A表 A(a, b).
int batchA(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][3], int dataNum)
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, labelName, labelName);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TEST_INPUT objIn = (TEST_INPUT){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        TEST_INPUTValue(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    // 迭代九禁用流控，适配udf函数里对表进行操作导致递归的场景
    if (ret != GMERR_OK) {
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}
// 结构化批量写 A表 A(a).
int batchA01(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][2], int dataNum)
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson2, labelName, labelName);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TEST_INPUT04 objIn = (TEST_INPUT04){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        TEST_INPUTValue04(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A表 A(a, b, c).
int batchA02(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][4], int dataNum)
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson3, labelName, labelName);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TEST_INPUT02 objIn = (TEST_INPUT02){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        TEST_INPUTValue02(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}
// 结构化批量写 C表 (a:str, f:int4)
int batchA03(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][3], int dataNum)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        // a:str
        uint8_t value1[10] = {0};
        (void)snprintf((char *)value1, 10, "s%08d", count[i][0]);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
        EXPECT_EQ(GMERR_OK, ret);
        // f:int4
        int32_t value2 = count[i][1];
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_INT32, &value2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // dtlReservedCount
        int32_t value3 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A表 A(a:byte8, f:int4)
int batchA04(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][3], int dataNum)
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson4, labelName, labelName);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TEST_INPUT03 objIn = (TEST_INPUT03){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        TEST_INPUTValue03(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 C表 C(f:str, c:byte8)
int batchA05(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][3], int dataNum)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        // f:str
        uint8_t value1[10] = {0};
        (void)snprintf((char *)value1, 10, "s%08d", count[i][0]);
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
        EXPECT_EQ(GMERR_OK, ret);
        // c:byte8
        uint8_t value2[8] = {0};
        value2[0] = count[i][1];
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_FIXED, value2, 8);
        EXPECT_EQ(GMERR_OK, ret);
        // dtlReservedCount
        int32_t value4 = count[i][2];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value4, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

void testSetPropertyInt(GmcStmtT *stmt, int32_t v)
{
    int ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a10", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a31", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void testSetPropertyStr(GmcStmtT *stmt, int32_t v)
{
    uint8_t value1[10] = {0};
    (void)snprintf((char *)value1, 10, "s%08d", v);
    int ret = GmcSetVertexProperty(stmt, "a11", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a12", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a13", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a14", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a15", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a16", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a17", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a18", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a19", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a20", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
}
void testSetPropertyByte(GmcStmtT *stmt, int32_t v)
{
    uint8_t value2[8] = {0};
    value2[0] = v;
    int ret = GmcSetVertexProperty(stmt, "a21", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a22", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a23", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a24", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a25", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a26", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a27", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a28", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a29", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a30", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
}
void testSetPropertyCount(GmcStmtT *stmt, int32_t v)
{
    int ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &v, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
// 批量写 A表
// A(
//     a1:int4, a2:int4, a3:int4, a4:int4, a5:int4, a6:int4, a7:int4, a8:int4, a9:int4, a10:int4,
//     a11:str, a12:str, a13:str, a14:str, a15:str, a16:str, a17:str, a18:str, a19:str, a20:str,
//     a21:byte8, a22:byte8, a23:byte8, a24:byte8, a25:byte8, a26:byte8, a27:byte8, a28:byte8, a29:byte8, a30:byte8,
//     a31:int4)
int batchA06(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][2], int dataNum)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        testSetPropertyInt(stmt, count[i][0]);
        testSetPropertyStr(stmt, count[i][0]);
        testSetPropertyByte(stmt, count[i][0]);
        testSetPropertyCount(stmt, count[i][1]);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

void testSetProperty07Int(GmcStmtT *stmt, int32_t v1, int32_t v2)
{
    int ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &v1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_INT32, &v2, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void testSetProperty07Str(GmcStmtT *stmt, int32_t v1, int32_t v2)
{
    uint8_t value1[10] = {0};
    (void)snprintf((char *)value1, 10, "s%08d", v1);
    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_STRING, value1, strlen((char *)value1));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value2[10] = {0};
    (void)snprintf((char *)value2, 10, "s%08d", v2);
    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_STRING, value2, strlen((char *)value2));
    EXPECT_EQ(GMERR_OK, ret);
}
void testSetProperty07Byte(GmcStmtT *stmt, int32_t v1, int32_t v2)
{
    uint8_t value1[8] = {0};
    value1[0] = v1;
    int ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_FIXED, value1, 8);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value2[8] = {0};
    value2[0] = v2;
    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, value2, 8);
    EXPECT_EQ(GMERR_OK, ret);
}
// 批量写 C表
// C(a:str, b:byte8, c:int4, d:str, e:byte8, f:int4)
int batchA07(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        testSetProperty07Str(stmt, count[i][0], count[i][3]);
        testSetProperty07Byte(stmt, count[i][1], count[i][4]);
        testSetProperty07Int(stmt, count[i][2], count[i][5]);
        testSetPropertyCount(stmt, count[i][6]);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写A表
// A(a1:int4, a2:int4, a3:int4, a4:int4, a5:int4, a6:int4, a7:int4, a8:int4, a9:int4, a10:int4,
// a11:int4, a12:int4, a13:int4, a14:int4, a15:int4, a16:int4, a17:int4, a18:int4, a19:int4, a20:int4,
// a21:int4, a22:int4, a23:int4, a24:int4, a25:int4, a26:int4, a27:int4, a28:int4,
// a29:int4, a30:int4, a31:int4, a32:int4)
int batchA08(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][4], int dataNum)
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson5, labelName, labelName);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TEST_INPUT05 objIn = (TEST_INPUT05){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        TEST_INPUTValue05(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 通过Datalog流控队列批量执行batch句柄中的DML操作
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 删除指定记录(a, b, c)
void DeleteElement(int32_t arr[][4], int index, int len)
{
    for (int i = 0; i < len - 1; i++) {
        if (i < index) {
            for (int j = 0; j < 4; j++) {
                arr[i][j] = arr[i][j];
            }
        } else {
            for (int j = 0; j < 4; j++) {
                arr[i][j] = arr[i + 1][j];
            }
        }
    }
}


// 删除指定记录(a, b)
void DeleteElement1(int32_t arr[][3], int index, int len)
{
    for (int i = 0; i < len - 1; i++) {
        if (i < index) {
            for (int j = 0; j < 3; j++) {
                arr[i][j] = arr[i][j];
            }
        } else {
            for (int j = 0; j < 3; j++) {
                arr[i][j] = arr[i + 1][j];
            }
        }
    }
}

// 删除指定记录(a, b, c, d)
void DeleteElement2(int32_t arr[][5], int index, int len)
{
    for (int i = 0; i < len - 1; i++) {
        if (i < index) {
            for (int j = 0; j < 5; j++) {
                arr[i][j] = arr[i][j];
            }
        } else {
            for (int j = 0; j < 5; j++) {
                arr[i][j] = arr[i + 1][j];
            }
        }
    }
}

// 结构化读 B(a, b)
int readOutB02(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][3])
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)outLabel, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, outLabel, outLabel);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // out表 B
    TEST_OUTPUT02 objOut = (TEST_OUTPUT02){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int dataNum = record;
    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d", objOut.a, objOut.b, objOut.dtlReservedCount);
        }
        // 更改校验方式，输出顺序可能不同
        // 检验值
        int loop = 0;
        for (int i = 0;i < dataNum; i++) {
            if (result[i][0] == objOut.a && result[i][1] == objOut.b && result[i][2] == objOut.dtlReservedCount) {
                // 已经匹配上，那么result数组删除该条记录
                DeleteElement1(result, i, record);
                loop += 1;
                sum++;
                break;
            }
        }
        if (loop == 1) {
            dataNum -= 1;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    if (sum != cnt) {
        // 先释放再return
        deSeriFreeDynMem(&deseriCtx, true);
        return FAILED;
    } else {
        AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

// 结构化读 B(a, b, c)
int readOutB01(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][4])
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)outLabel, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson3, outLabel, outLabel);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // out表 C
    TEST_OUTPUT objOut = (TEST_OUTPUT){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int dataNum = record;
    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d", objOut.a,
                objOut.b, objOut.c, objOut.dtlReservedCount);
        }
        // 更改校验方式，输出顺序可能不同
        // 检验值
        int loop = 0;
        for (int i = 0;i < dataNum; i++) {
            if (result[i][0] == objOut.a && result[i][1] == objOut.b && result[i][2] == objOut.c &&
                result[i][3] == objOut.dtlReservedCount) {
                    // 已经匹配上，那么result数组删除该条记录
                    DeleteElement(result, i, record);
                    loop += 1;
                    sum++;
                    break;
                }
        }
        if (loop == 1) {
            dataNum -= 1;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    if (sum != cnt) {
        // 先释放再return
        deSeriFreeDynMem(&deseriCtx, true);
        return FAILED;
    } else {
        AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

// 结构化读 B(a, b, c, d) .
int readOutB03(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][5])
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)outLabel, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson6, outLabel, outLabel);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // out表 B
    TEST_OUTPUT03 objOut = (TEST_OUTPUT03){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int dataNum = record;
    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d", objOut.a,
                objOut.b, objOut.c, objOut.d, objOut.dtlReservedCount);
        }
        // 更改校验方式，输出顺序可能不同
        // 检验值
        int loop = 0;
        for (int i = 0;i < dataNum; i++) {
            if (result[i][0] == objOut.a && result[i][1] == objOut.b && result[i][2] == objOut.c &&
                result[i][3] == objOut.d && result[i][4] == objOut.dtlReservedCount) {
                    // 已经匹配上，那么result数组删除该条记录
                    DeleteElement2(result, i, record);
                    loop += 1;
                    sum++;
                    break;
                }
        }
        if (loop == 1) {
            dataNum -= 1;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    if (sum != cnt) {
        // 先释放再return
        deSeriFreeDynMem(&deseriCtx, true);
        return FAILED;
    } else {
        AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

/*--------------------------------------------表字段(a, b, c)-----------------------------------*/
int FindElement(int32_t arr[][4], int32_t valueA, int32_t valueB, int32_t valueC, int32_t valueCount, int len)
{
    for (int i = 0; i < len; i++) {
        if (valueA == arr[i][0] && valueB == arr[i][1] && valueC == arr[i][2] && valueCount == arr[i][3]) {
            return 1;
        }
    }
    return -1;
}

// 两个字段判断
int FindElement1(int32_t arr[][2], int32_t valueA, int32_t valueB, int32_t valueC, int32_t valueCount, int len)
{
    for (int i = 0; i < len; i++) {
        if (valueA == arr[i][0] && valueB == arr[i][0] && valueC == arr[i][0] && valueCount == arr[i][1]) {
            return 1;
        }
    }
    return -1;
}

// 判断ValueC为0的情况
int FindElement2(int32_t arr[][2], int32_t valueA, int32_t valueB, int32_t valueC, int32_t valueCount, int len)
{
    for (int i = 0; i < len; i++) {
        if (valueA == arr[i][0] && valueB == arr[i][0] && valueC == 0 && valueCount == arr[i][1]) {
            return 1;
        }
    }
    return -1;
}

// 校验B(a:str, b:byte8, c:int4, d:str, e:byte8, f:int4)
int FindElement3(int32_t arr[][7], int32_t valueA, int32_t valueB, int32_t valueC, int32_t valueD, int32_t valueE,
    int32_t valueF, int32_t valueCount, int len)
{
    for (int i = 0; i < len; i++) {
        if (valueA == arr[i][0] && valueB == arr[i][1] && valueC == arr[i][2] && valueD == arr[i][3] &&
            valueE == arr[i][4] && valueF == arr[i][5] && valueCount == arr[i][6]) {
            return 1;
        }
    }
    return -1;
}

// 读B(a:str, b:int4, c:str)
int readOutB04(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][4])
{
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool isNull;
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: ---------%d-------\n", cnt);
        }
        // a
        int stringLen = 10;
        char *buf1 = (char *)malloc(stringLen + 1);
        if (buf1 == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        memset(buf1, stringLen + 1, 0);
        unsigned int strSize1;
        ret = GmcGetVertexPropertySizeByName(stmt, "a", &strSize1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "a", buf1, strSize1, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // b
        int32_t countB;
        ret = GmcGetVertexPropertyByName(stmt, "b", &countB, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        char *buf2 = (char *)malloc(stringLen + 1);
        if (buf2 == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        memset(buf2, stringLen + 1, 0);
        unsigned int strSize2;
        ret = GmcGetVertexPropertySizeByName(stmt, "c", &strSize2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "c", buf2, strSize2, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // dtlReservedCount
        int32_t countD;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &countD, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %s %d %s %d", buf1, countB, buf2, countD);
        }

        // 校验值
        ret = FindElement(result, (int32_t)(buf1[strSize1 - 2] - '0'), countB, (int32_t)(buf2[strSize2 - 2] - '0'),
            countD, record);
        if (ret == 1) {
            sum++;
        }
        cnt++;
        free(buf1);
        free(buf2);
    }
    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    return ret;
}

// 读B(a:byte8, b:int4, c:byte8)
int readOutB05(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][4])
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)outLabel, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson8, outLabel, outLabel);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // out表 C
    TEST_OUTPUT04 objOut = (TEST_OUTPUT04){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);

    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    
    int cnt = 0;
    int sum = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d", objOut.a[0],
                objOut.b, objOut.c[0], objOut.dtlReservedCount);
        }
        // 校验值
        ret = FindElement(result, (int32_t)(objOut.a[0]), (int32_t)(objOut.b), (int32_t)(objOut.c[0]),
            objOut.dtlReservedCount, record);
        if (ret == 1) {
            sum++;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    deSeriFreeDynMem(&deseriCtx, true);
}

// 读A(a:str, f:int4)
void readOutB06(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][3])
{
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool isNull;
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: ---------%d-------\n", cnt);
        }
        // a
        int stringLen = 10;
        char *buf1 = (char *)malloc(stringLen + 1);
        if (buf1 == NULL) {
            return;
        }
        memset(buf1, stringLen + 1, 0);
        unsigned int strSize1;
        ret = GmcGetVertexPropertySizeByName(stmt, "a", &strSize1);
        ret = GmcGetVertexPropertyByName(stmt, "a", buf1, strSize1, &isNull);
        if (ret == GMERR_OK && PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %s ", buf1);
            EXPECT_EQ(result[cnt][0], buf1[strSize1 - 2] - '0');
        }

        // f
        int32_t countB;
        ret = GmcGetVertexPropertyByName(stmt, "f", &countB, sizeof(int32_t), &isNull);
        if (ret == GMERR_OK && PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d", countB);
            EXPECT_EQ(result[cnt][1], countB);
        }
        
        // dtlReservedCount
        int32_t countD;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &countD, sizeof(int32_t), &isNull);
        if (ret == GMERR_OK && PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d", countD);
            EXPECT_EQ(result[cnt][2], countD);
        }

        cnt++;
        free(buf1);
    }
    EXPECT_EQ(record, cnt);
}

// 读B(a:str, b:byte8, c:int4, d:str, e:byte8, f:int4)
int readOutB07(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][7])
{
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool isNull;
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: ---------%d-------\n", cnt);
        }

        // a
        int stringLen = 10;
        char *buf1 = (char *)malloc(stringLen + 1);
        if (buf1 == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        memset(buf1, stringLen + 1, 0);
        unsigned int strSize1;
        ret = GmcGetVertexPropertySizeByName(stmt, "a", &strSize1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "a", buf1, strSize1, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        uint8_t valueB[8] = {0};
        ret = GmcGetVertexPropertyByName(stmt, "b", &valueB, 8, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        int32_t countC;
        ret = GmcGetVertexPropertyByName(stmt, "c", &countC, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d
        char *buf2 = (char *)malloc(stringLen + 1);
        if (buf2 == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        memset(buf2, stringLen + 1, 0);
        unsigned int strSize2;
        ret = GmcGetVertexPropertySizeByName(stmt, "d", &strSize2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "d", buf2, strSize2, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // e
        uint8_t valueE[8] = {0};
        ret = GmcGetVertexPropertyByName(stmt, "e", &valueE, 8, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f
        int32_t countF;
        ret = GmcGetVertexPropertyByName(stmt, "f", &countF, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // dtlReservedCount
        int32_t countD;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &countD, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // 校验
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %s %d %d %s %d %d %d", buf1, valueB[0],
                countC, buf2, valueE[0], countF, countD);
        }

        // 校验值
        ret = FindElement3(result, (int32_t)(buf1[strSize1 - 2] - '0'), (int32_t)(valueB[0]), countC,
            (int32_t)(buf2[strSize2 - 2] - '0'), (int32_t)(valueE[0]), countF, countD, record);
        if (ret == 1) {
            sum++;
        }
        cnt++;
        free(buf1);
        free(buf2);
    }
    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    return ret;
}

void testGetPropertyInt(GmcStmtT *stmt, int32_t v, bool isNull)
{
    int32_t read;
    // 校验部分
    int ret = GmcGetVertexPropertyByName(stmt, "c", &read, sizeof(int32_t), &isNull);
    EXPECT_EQ(v, read);
    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d", read);
    }
}

void testGetPropertyStr(GmcStmtT *stmt, int32_t v, bool isNull)
{
    // 校验部分
    int stringLen = 10;
    char *buf = (char *)malloc(stringLen + 1);
    if (buf == NULL) {
        return;
    }
    memset(buf, stringLen + 1, 0);
    unsigned int strSize;
    int ret = GmcGetVertexPropertySizeByName(stmt, "a", &strSize);
    ret = GmcGetVertexPropertyByName(stmt, "a", buf, strSize, &isNull);
    EXPECT_EQ(v, buf[strSize - 2] - '0');
    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %s ", buf);
    }
    free(buf);
}
void testGetPropertyByte(GmcStmtT *stmt, int32_t v, bool isNull)
{
    uint8_t valueB[8] = {0};
    int ret = GmcGetVertexPropertyByName(stmt, "b", &valueB, 8, &isNull);
    EXPECT_EQ(v, valueB[0]);
    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d", valueB[0]);
    }
}
void testGetPropertyCount(GmcStmtT *stmt, int32_t v, bool isNull)
{
    int32_t read;
    int ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &read, sizeof(int32_t), &isNull);
    EXPECT_EQ(v, read);
    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d", read);
    }
}

// 读B(a:str, b:byte8, c:int4)
int readOutB08(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][2])
{
    int ret = 0;
    uint32_t sizeB = 8;
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool isNull;
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        // a
        int stringLen = 10;
        char *buf1 = (char *)malloc(stringLen + 1);
        if (buf1 == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        memset(buf1, stringLen + 1, 0);
        unsigned int strSize1;
        ret = GmcGetVertexPropertySizeByName(stmt, "a", &strSize1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "a", buf1, strSize1, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        uint8_t countB[sizeB] = {0};
        ret = GmcGetVertexPropertyByName(stmt, "b", &countB, sizeB, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // c
        int32_t countC;
        ret = GmcGetVertexPropertyByName(stmt, "c", &countC, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // dtlReservedCount
        int32_t countD;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &countD, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %s %d %d %d", buf1, countB[0], countC, countD);
        }

        // 校验值
        ret = FindElement1(result, (int32_t)(buf1[strSize1 - 2] - '0'), (int32_t)(countB[0]), countC, countD, record);
        if (ret == 1) {
            sum++;
        }
        cnt++;
        free(buf1);
    }
    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    return ret;
}

void testGetPropertyInt02(GmcStmtT *stmt, int32_t v, bool isNull)
{
    int32_t read;
    // 校验部分
    int ret = GmcGetVertexPropertyByName(stmt, "a2", &read, sizeof(int32_t), &isNull);
    EXPECT_EQ(v, read);
}

void testGetPropertyStr02(GmcStmtT *stmt, int32_t v, bool isNull)
{
    // 校验部分
    int stringLen = 10;
    char *buf = (char *)malloc(stringLen + 1);
    if (buf == NULL) {
        return;
    }
    memset(buf, stringLen + 1, 0);
    unsigned int strSize;
    int ret = GmcGetVertexPropertySizeByName(stmt, "a11", &strSize);
    ret = GmcGetVertexPropertyByName(stmt, "a11", buf, strSize, &isNull);
    EXPECT_EQ(v, buf[strSize - 2] - '0');
    free(buf);
}
void testGetPropertyByte02(GmcStmtT *stmt, int32_t v, bool isNull)
{
    uint8_t valueB[8] = {0};
    int ret = GmcGetVertexPropertyByName(stmt, "a21", &valueB, 8, &isNull);
    for (int i = 0; i < 8; i++) {
        EXPECT_EQ(i, valueB[i]);
    }
}

// 读B
// B(a:int, a2:int4, a3:int4, a4:int4, a5:int4, a6:int4, a7:int4, a8:int4, a9:int4, a10:int4,
// a11:str, a12:str, a13:str, a14:str, a15:str, a16:str, a17:str, a18:str, a19:str, a20:str,
// a21:byte8, a22:byte8, a23:byte8, a24:byte8, a25:byte8, a26:byte8, a27:byte8, a28:byte8, a29:byte8, a30:byte8,
// a31:int4)
int readOutB09(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][2])
{
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    uint32_t sizeA21 = 8;
    bool isNull;
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    int sum = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        // 校验部分
        // a2
        int32_t readA2;
        ret = GmcGetVertexPropertyByName(stmt, "a2", &readA2, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a11
        int stringLen = 10;
        char *buf1 = (char *)malloc(stringLen + 1);
        if (buf1 == NULL) {
            return GMERR_OUT_OF_MEMORY;
        }
        memset(buf1, stringLen + 1, 0);
        unsigned int strSize1;
        ret = GmcGetVertexPropertySizeByName(stmt, "a11", &strSize1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "a11", buf1, strSize1, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a21   值为0~7
        uint8_t readA21[sizeA21] = {0};
        ret = GmcGetVertexPropertyByName(stmt, "a21", &readA21, sizeA21, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // dtlReservedCount
        int32_t readCount;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &readCount, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %s %d %d", readA2, buf1, readA21[0], readCount);
        }

        // 校验部分值
        ret = FindElement2(result, readA2, (int32_t)(buf1[strSize1 - 2] - '0'), (int32_t)(readA21[0]),
            readCount, record);
        if (ret == 1) {
            sum++;
        }
        cnt++;
        free(buf1);
    }
    AW_MACRO_EXPECT_EQ_INT(record, cnt);
    AW_MACRO_EXPECT_EQ_INT(sum, cnt);
    return ret;
}

// 结构化读 B(a)
void readOutB10(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][2])
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)outLabel, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson2, outLabel, outLabel);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // out表 B
    TEST_OUTPUT05 objOut = (TEST_OUTPUT05){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d", objOut.a, objOut.dtlReservedCount);
        }
        // out
        EXPECT_EQ(result[cnt][0], objOut.a);
        EXPECT_EQ(result[cnt][1], objOut.dtlReservedCount);
        cnt++;
    }
    EXPECT_EQ(cnt, record);
    deSeriFreeDynMem(&deseriCtx, true);
}

void readOutB11(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][5])
{
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool isNull;
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int32_t read01;
        // 校验部分
        ret = GmcGetVertexPropertyByName(stmt, "a1", &read01, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][0], read01);
        int32_t read02;
        ret = GmcGetVertexPropertyByName(stmt, "a2", &read02, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][1], read02);
        int32_t read03;
        ret = GmcGetVertexPropertyByName(stmt, "a3", &read03, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][2], read03);
        int32_t read04;
        ret = GmcGetVertexPropertyByName(stmt, "a4", &read04, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][3], read04);
        int32_t read05;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &read05, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][4], read05);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d", read01, read02, read03, read04, read05);
        }

        cnt++;
    }
    EXPECT_EQ(record, cnt);
}

void readOutB12(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][4])
{
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool isNull;
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int32_t read01;
        // 校验部分
        ret = GmcGetVertexPropertyByName(stmt, "a1", &read01, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][0], read01);
        int32_t read02;
        ret = GmcGetVertexPropertyByName(stmt, "a11", &read02, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][1], read02);
        int32_t read03;
        ret = GmcGetVertexPropertyByName(stmt, "a31", &read03, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][2], read03);
        int32_t read05;
        ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &read05, sizeof(int32_t), &isNull);
        EXPECT_EQ(result[cnt][3], read05);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d", read01, read02, read03, read05);
        }

        cnt++;
    }
    EXPECT_EQ(record, cnt);
}

// 结构化读 B(a, b, c, d, e) .
void readOutB13(GmcStmtT *stmt, char *outLabel, int record, int32_t result[][6])
{
    int ret = 0;
    TestLabelInfoT labelInfo = {(char *)outLabel, 0, g_testNameSpace};
    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson7, outLabel, outLabel);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // out表 B
    TEST_OUTPUT06 objOut = (TEST_OUTPUT06){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);
    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, outLabel, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        if (PRINT_INFO) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d %d", objOut.a,
                objOut.b, objOut.c, objOut.d, objOut.e, objOut.dtlReservedCount);
        }
        // out
        EXPECT_EQ(result[cnt][0], objOut.a);
        EXPECT_EQ(result[cnt][1], objOut.b);
        EXPECT_EQ(result[cnt][2], objOut.c);
        EXPECT_EQ(result[cnt][3], objOut.d);
        EXPECT_EQ(result[cnt][4], objOut.e);
        EXPECT_EQ(result[cnt][5], objOut.dtlReservedCount);
        cnt++;
    }
    EXPECT_EQ(cnt, record);
    deSeriFreeDynMem(&deseriCtx, true);
}

// 回调中，获取属性值并设置属性值
int GetProValueAndSetProValue(GmcStmtT *subStmt)
{
    // get值
    int ret = 0;
    bool isNull;
    int stringLen = 10;
    char *buf1 = (char *)malloc(stringLen + 1);
    if (buf1 == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    memset(buf1, stringLen + 1, 0);
    unsigned int strSize1;
    ret = GmcGetVertexPropertySizeByName(subStmt, "f", &strSize1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(subStmt, "f", buf1, strSize1, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c
    uint8_t valueB[8] = {0};
    ret = GmcGetVertexPropertyByName(subStmt, "c", &valueB, 8, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a---资源值
    int32_t a = 0;

    // dtlReservedCount
    int32_t countD;
    ret = GmcGetVertexPropertyByName(subStmt, "dtlReservedCount", &countD, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d", int32_t(buf1[8] - '0'), int32_t(valueB[0]), a, countD);

    // set值
    ret = GmcSetVertexProperty(subStmt, "f", GMC_DATATYPE_STRING, buf1, strlen(buf1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(subStmt, "c", GMC_DATATYPE_FIXED, valueB, sizeof(valueB));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(subStmt, "a", GMC_DATATYPE_INT32, &a, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(subStmt, "dtlReservedCount", GMC_DATATYPE_INT32, &countD, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(buf1);
    return ret;
}

int snCallbackCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataT *data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;

    data->callbackTimes++;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);
    // pubsub型资源表
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    RETURN_IFERR(ret);
    
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }
        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 获取pubsub型资源表值并设置值
                    GetProValueAndSetProValue(subStmt);
                    // pubsub型资源表
                    ret = GmcSubAddRespDML(response, subStmt);
                    RETURN_IFERR(ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    ret = GmcSendResp(subStmt, response);
    RETURN_IFERR(ret);
    ret = GmcDestroyResp(subStmt, response);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

void snCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = snCallbackCheck(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetFileName(const char *name)
{
    (void)sprintf(g_inputFile, "./%s/%s.d", g_orgFile, g_nsName);
    (void)sprintf(g_udfFile, "./%s/%s_udf.c", g_orgFile, g_nsName);
    (void)sprintf(g_outputFile, "./%s/%s.c", g_destFile, g_nsName);
    (void)sprintf(g_libName, "./%s/%s.so", g_destFile, g_nsName);
}

void TestCompile(const char *name)
{
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n",
        g_hFile, g_outputFile, g_udfFile, g_libName);
    system(g_command);
}
#endif
