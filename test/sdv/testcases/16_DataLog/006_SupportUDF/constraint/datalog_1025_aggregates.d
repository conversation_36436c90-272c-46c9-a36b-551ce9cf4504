%table A(a:int4, b:int4)
%table B(a:int4, min:int4, max:int4)


%aggregate agg001(a: int4->min: int4, max: int4) {}
%aggregate agg002(a: int4->min: int4, max: int4) {}
%aggregate agg003(a: int4->min: int4, max: int4) {}
%aggregate agg004(a: int4->min: int4, max: int4) {}
%aggregate agg005(a: int4->min: int4, max: int4) {}
%aggregate agg006(a: int4->min: int4, max: int4) {}
%aggregate agg007(a: int4->min: int4, max: int4) {}
%aggregate agg008(a: int4->min: int4, max: int4) {}
%aggregate agg009(a: int4->min: int4, max: int4) {}
%aggregate agg010(a: int4->min: int4, max: int4) {}
%aggregate agg011(a: int4->min: int4, max: int4) {}
%aggregate agg012(a: int4->min: int4, max: int4) {}
%aggregate agg013(a: int4->min: int4, max: int4) {}
%aggregate agg014(a: int4->min: int4, max: int4) {}
%aggregate agg015(a: int4->min: int4, max: int4) {}
%aggregate agg016(a: int4->min: int4, max: int4) {}
%aggregate agg017(a: int4->min: int4, max: int4) {}
%aggregate agg018(a: int4->min: int4, max: int4) {}
%aggregate agg019(a: int4->min: int4, max: int4) {}
%aggregate agg020(a: int4->min: int4, max: int4) {}
%aggregate agg021(a: int4->min: int4, max: int4) {}
%aggregate agg022(a: int4->min: int4, max: int4) {}
%aggregate agg023(a: int4->min: int4, max: int4) {}
%aggregate agg024(a: int4->min: int4, max: int4) {}
%aggregate agg025(a: int4->min: int4, max: int4) {}
%aggregate agg026(a: int4->min: int4, max: int4) {}
%aggregate agg027(a: int4->min: int4, max: int4) {}
%aggregate agg028(a: int4->min: int4, max: int4) {}
%aggregate agg029(a: int4->min: int4, max: int4) {}
%aggregate agg030(a: int4->min: int4, max: int4) {}
%aggregate agg031(a: int4->min: int4, max: int4) {}
%aggregate agg032(a: int4->min: int4, max: int4) {}
%aggregate agg033(a: int4->min: int4, max: int4) {}
%aggregate agg034(a: int4->min: int4, max: int4) {}
%aggregate agg035(a: int4->min: int4, max: int4) {}
%aggregate agg036(a: int4->min: int4, max: int4) {}
%aggregate agg037(a: int4->min: int4, max: int4) {}
%aggregate agg038(a: int4->min: int4, max: int4) {}
%aggregate agg039(a: int4->min: int4, max: int4) {}
%aggregate agg040(a: int4->min: int4, max: int4) {}
%aggregate agg041(a: int4->min: int4, max: int4) {}
%aggregate agg042(a: int4->min: int4, max: int4) {}
%aggregate agg043(a: int4->min: int4, max: int4) {}
%aggregate agg044(a: int4->min: int4, max: int4) {}
%aggregate agg045(a: int4->min: int4, max: int4) {}
%aggregate agg046(a: int4->min: int4, max: int4) {}
%aggregate agg047(a: int4->min: int4, max: int4) {}
%aggregate agg048(a: int4->min: int4, max: int4) {}
%aggregate agg049(a: int4->min: int4, max: int4) {}
%aggregate agg050(a: int4->min: int4, max: int4) {}
%aggregate agg051(a: int4->min: int4, max: int4) {}
%aggregate agg052(a: int4->min: int4, max: int4) {}
%aggregate agg053(a: int4->min: int4, max: int4) {}
%aggregate agg054(a: int4->min: int4, max: int4) {}
%aggregate agg055(a: int4->min: int4, max: int4) {}
%aggregate agg056(a: int4->min: int4, max: int4) {}
%aggregate agg057(a: int4->min: int4, max: int4) {}
%aggregate agg058(a: int4->min: int4, max: int4) {}
%aggregate agg059(a: int4->min: int4, max: int4) {}
%aggregate agg060(a: int4->min: int4, max: int4) {}
%aggregate agg061(a: int4->min: int4, max: int4) {}
%aggregate agg062(a: int4->min: int4, max: int4) {}
%aggregate agg063(a: int4->min: int4, max: int4) {}
%aggregate agg064(a: int4->min: int4, max: int4) {}
%aggregate agg065(a: int4->min: int4, max: int4) {}
%aggregate agg066(a: int4->min: int4, max: int4) {}
%aggregate agg067(a: int4->min: int4, max: int4) {}
%aggregate agg068(a: int4->min: int4, max: int4) {}
%aggregate agg069(a: int4->min: int4, max: int4) {}
%aggregate agg070(a: int4->min: int4, max: int4) {}
%aggregate agg071(a: int4->min: int4, max: int4) {}
%aggregate agg072(a: int4->min: int4, max: int4) {}
%aggregate agg073(a: int4->min: int4, max: int4) {}
%aggregate agg074(a: int4->min: int4, max: int4) {}
%aggregate agg075(a: int4->min: int4, max: int4) {}
%aggregate agg076(a: int4->min: int4, max: int4) {}
%aggregate agg077(a: int4->min: int4, max: int4) {}
%aggregate agg078(a: int4->min: int4, max: int4) {}
%aggregate agg079(a: int4->min: int4, max: int4) {}
%aggregate agg080(a: int4->min: int4, max: int4) {}
%aggregate agg081(a: int4->min: int4, max: int4) {}
%aggregate agg082(a: int4->min: int4, max: int4) {}
%aggregate agg083(a: int4->min: int4, max: int4) {}
%aggregate agg084(a: int4->min: int4, max: int4) {}
%aggregate agg085(a: int4->min: int4, max: int4) {}
%aggregate agg086(a: int4->min: int4, max: int4) {}
%aggregate agg087(a: int4->min: int4, max: int4) {}
%aggregate agg088(a: int4->min: int4, max: int4) {}
%aggregate agg089(a: int4->min: int4, max: int4) {}
%aggregate agg090(a: int4->min: int4, max: int4) {}
%aggregate agg091(a: int4->min: int4, max: int4) {}
%aggregate agg092(a: int4->min: int4, max: int4) {}
%aggregate agg093(a: int4->min: int4, max: int4) {}
%aggregate agg094(a: int4->min: int4, max: int4) {}
%aggregate agg095(a: int4->min: int4, max: int4) {}
%aggregate agg096(a: int4->min: int4, max: int4) {}
%aggregate agg097(a: int4->min: int4, max: int4) {}
%aggregate agg098(a: int4->min: int4, max: int4) {}
%aggregate agg099(a: int4->min: int4, max: int4) {}
%aggregate agg100(a: int4->min: int4, max: int4) {}
%aggregate agg101(a: int4->min: int4, max: int4) {}
%aggregate agg102(a: int4->min: int4, max: int4) {}
%aggregate agg103(a: int4->min: int4, max: int4) {}
%aggregate agg104(a: int4->min: int4, max: int4) {}
%aggregate agg105(a: int4->min: int4, max: int4) {}
%aggregate agg106(a: int4->min: int4, max: int4) {}
%aggregate agg107(a: int4->min: int4, max: int4) {}
%aggregate agg108(a: int4->min: int4, max: int4) {}
%aggregate agg109(a: int4->min: int4, max: int4) {}
%aggregate agg110(a: int4->min: int4, max: int4) {}
%aggregate agg111(a: int4->min: int4, max: int4) {}
%aggregate agg112(a: int4->min: int4, max: int4) {}
%aggregate agg113(a: int4->min: int4, max: int4) {}
%aggregate agg114(a: int4->min: int4, max: int4) {}
%aggregate agg115(a: int4->min: int4, max: int4) {}
%aggregate agg116(a: int4->min: int4, max: int4) {}
%aggregate agg117(a: int4->min: int4, max: int4) {}
%aggregate agg118(a: int4->min: int4, max: int4) {}
%aggregate agg119(a: int4->min: int4, max: int4) {}
%aggregate agg120(a: int4->min: int4, max: int4) {}
%aggregate agg121(a: int4->min: int4, max: int4) {}
%aggregate agg122(a: int4->min: int4, max: int4) {}
%aggregate agg123(a: int4->min: int4, max: int4) {}
%aggregate agg124(a: int4->min: int4, max: int4) {}
%aggregate agg125(a: int4->min: int4, max: int4) {}
%aggregate agg126(a: int4->min: int4, max: int4) {}
%aggregate agg127(a: int4->min: int4, max: int4) {}
%aggregate agg128(a: int4->min: int4, max: int4) {}
%aggregate agg129(a: int4->min: int4, max: int4) {}
%aggregate agg130(a: int4->min: int4, max: int4) {}
%aggregate agg131(a: int4->min: int4, max: int4) {}
%aggregate agg132(a: int4->min: int4, max: int4) {}
%aggregate agg133(a: int4->min: int4, max: int4) {}
%aggregate agg134(a: int4->min: int4, max: int4) {}
%aggregate agg135(a: int4->min: int4, max: int4) {}
%aggregate agg136(a: int4->min: int4, max: int4) {}
%aggregate agg137(a: int4->min: int4, max: int4) {}
%aggregate agg138(a: int4->min: int4, max: int4) {}
%aggregate agg139(a: int4->min: int4, max: int4) {}
%aggregate agg140(a: int4->min: int4, max: int4) {}
%aggregate agg141(a: int4->min: int4, max: int4) {}
%aggregate agg142(a: int4->min: int4, max: int4) {}
%aggregate agg143(a: int4->min: int4, max: int4) {}
%aggregate agg144(a: int4->min: int4, max: int4) {}
%aggregate agg145(a: int4->min: int4, max: int4) {}
%aggregate agg146(a: int4->min: int4, max: int4) {}
%aggregate agg147(a: int4->min: int4, max: int4) {}
%aggregate agg148(a: int4->min: int4, max: int4) {}
%aggregate agg149(a: int4->min: int4, max: int4) {}
%aggregate agg150(a: int4->min: int4, max: int4) {}
%aggregate agg151(a: int4->min: int4, max: int4) {}
%aggregate agg152(a: int4->min: int4, max: int4) {}
%aggregate agg153(a: int4->min: int4, max: int4) {}
%aggregate agg154(a: int4->min: int4, max: int4) {}
%aggregate agg155(a: int4->min: int4, max: int4) {}
%aggregate agg156(a: int4->min: int4, max: int4) {}
%aggregate agg157(a: int4->min: int4, max: int4) {}
%aggregate agg158(a: int4->min: int4, max: int4) {}
%aggregate agg159(a: int4->min: int4, max: int4) {}
%aggregate agg160(a: int4->min: int4, max: int4) {}
%aggregate agg161(a: int4->min: int4, max: int4) {}
%aggregate agg162(a: int4->min: int4, max: int4) {}
%aggregate agg163(a: int4->min: int4, max: int4) {}
%aggregate agg164(a: int4->min: int4, max: int4) {}
%aggregate agg165(a: int4->min: int4, max: int4) {}
%aggregate agg166(a: int4->min: int4, max: int4) {}
%aggregate agg167(a: int4->min: int4, max: int4) {}
%aggregate agg168(a: int4->min: int4, max: int4) {}
%aggregate agg169(a: int4->min: int4, max: int4) {}
%aggregate agg170(a: int4->min: int4, max: int4) {}
%aggregate agg171(a: int4->min: int4, max: int4) {}
%aggregate agg172(a: int4->min: int4, max: int4) {}
%aggregate agg173(a: int4->min: int4, max: int4) {}
%aggregate agg174(a: int4->min: int4, max: int4) {}
%aggregate agg175(a: int4->min: int4, max: int4) {}
%aggregate agg176(a: int4->min: int4, max: int4) {}
%aggregate agg177(a: int4->min: int4, max: int4) {}
%aggregate agg178(a: int4->min: int4, max: int4) {}
%aggregate agg179(a: int4->min: int4, max: int4) {}
%aggregate agg180(a: int4->min: int4, max: int4) {}
%aggregate agg181(a: int4->min: int4, max: int4) {}
%aggregate agg182(a: int4->min: int4, max: int4) {}
%aggregate agg183(a: int4->min: int4, max: int4) {}
%aggregate agg184(a: int4->min: int4, max: int4) {}
%aggregate agg185(a: int4->min: int4, max: int4) {}
%aggregate agg186(a: int4->min: int4, max: int4) {}
%aggregate agg187(a: int4->min: int4, max: int4) {}
%aggregate agg188(a: int4->min: int4, max: int4) {}
%aggregate agg189(a: int4->min: int4, max: int4) {}
%aggregate agg190(a: int4->min: int4, max: int4) {}
%aggregate agg191(a: int4->min: int4, max: int4) {}
%aggregate agg192(a: int4->min: int4, max: int4) {}
%aggregate agg193(a: int4->min: int4, max: int4) {}
%aggregate agg194(a: int4->min: int4, max: int4) {}
%aggregate agg195(a: int4->min: int4, max: int4) {}
%aggregate agg196(a: int4->min: int4, max: int4) {}
%aggregate agg197(a: int4->min: int4, max: int4) {}
%aggregate agg198(a: int4->min: int4, max: int4) {}
%aggregate agg199(a: int4->min: int4, max: int4) {}
%aggregate agg200(a: int4->min: int4, max: int4) {}
%aggregate agg201(a: int4->min: int4, max: int4) {}
%aggregate agg202(a: int4->min: int4, max: int4) {}
%aggregate agg203(a: int4->min: int4, max: int4) {}
%aggregate agg204(a: int4->min: int4, max: int4) {}
%aggregate agg205(a: int4->min: int4, max: int4) {}
%aggregate agg206(a: int4->min: int4, max: int4) {}
%aggregate agg207(a: int4->min: int4, max: int4) {}
%aggregate agg208(a: int4->min: int4, max: int4) {}
%aggregate agg209(a: int4->min: int4, max: int4) {}
%aggregate agg210(a: int4->min: int4, max: int4) {}
%aggregate agg211(a: int4->min: int4, max: int4) {}
%aggregate agg212(a: int4->min: int4, max: int4) {}
%aggregate agg213(a: int4->min: int4, max: int4) {}
%aggregate agg214(a: int4->min: int4, max: int4) {}
%aggregate agg215(a: int4->min: int4, max: int4) {}
%aggregate agg216(a: int4->min: int4, max: int4) {}
%aggregate agg217(a: int4->min: int4, max: int4) {}
%aggregate agg218(a: int4->min: int4, max: int4) {}
%aggregate agg219(a: int4->min: int4, max: int4) {}
%aggregate agg220(a: int4->min: int4, max: int4) {}
%aggregate agg221(a: int4->min: int4, max: int4) {}
%aggregate agg222(a: int4->min: int4, max: int4) {}
%aggregate agg223(a: int4->min: int4, max: int4) {}
%aggregate agg224(a: int4->min: int4, max: int4) {}
%aggregate agg225(a: int4->min: int4, max: int4) {}
%aggregate agg226(a: int4->min: int4, max: int4) {}
%aggregate agg227(a: int4->min: int4, max: int4) {}
%aggregate agg228(a: int4->min: int4, max: int4) {}
%aggregate agg229(a: int4->min: int4, max: int4) {}
%aggregate agg230(a: int4->min: int4, max: int4) {}
%aggregate agg231(a: int4->min: int4, max: int4) {}
%aggregate agg232(a: int4->min: int4, max: int4) {}
%aggregate agg233(a: int4->min: int4, max: int4) {}
%aggregate agg234(a: int4->min: int4, max: int4) {}
%aggregate agg235(a: int4->min: int4, max: int4) {}
%aggregate agg236(a: int4->min: int4, max: int4) {}
%aggregate agg237(a: int4->min: int4, max: int4) {}
%aggregate agg238(a: int4->min: int4, max: int4) {}
%aggregate agg239(a: int4->min: int4, max: int4) {}
%aggregate agg240(a: int4->min: int4, max: int4) {}
%aggregate agg241(a: int4->min: int4, max: int4) {}
%aggregate agg242(a: int4->min: int4, max: int4) {}
%aggregate agg243(a: int4->min: int4, max: int4) {}
%aggregate agg244(a: int4->min: int4, max: int4) {}
%aggregate agg245(a: int4->min: int4, max: int4) {}
%aggregate agg246(a: int4->min: int4, max: int4) {}
%aggregate agg247(a: int4->min: int4, max: int4) {}
%aggregate agg248(a: int4->min: int4, max: int4) {}
%aggregate agg249(a: int4->min: int4, max: int4) {}
%aggregate agg250(a: int4->min: int4, max: int4) {}
%aggregate agg251(a: int4->min: int4, max: int4) {}
%aggregate agg252(a: int4->min: int4, max: int4) {}
%aggregate agg253(a: int4->min: int4, max: int4) {}
%aggregate agg254(a: int4->min: int4, max: int4) {}
%aggregate agg255(a: int4->min: int4, max: int4) {}
%aggregate agg256(a: int4->min: int4, max: int4) {}
%aggregate agg257(a: int4->min: int4, max: int4) {}
%aggregate agg258(a: int4->min: int4, max: int4) {}
%aggregate agg259(a: int4->min: int4, max: int4) {}
%aggregate agg260(a: int4->min: int4, max: int4) {}
%aggregate agg261(a: int4->min: int4, max: int4) {}
%aggregate agg262(a: int4->min: int4, max: int4) {}
%aggregate agg263(a: int4->min: int4, max: int4) {}
%aggregate agg264(a: int4->min: int4, max: int4) {}
%aggregate agg265(a: int4->min: int4, max: int4) {}
%aggregate agg266(a: int4->min: int4, max: int4) {}
%aggregate agg267(a: int4->min: int4, max: int4) {}
%aggregate agg268(a: int4->min: int4, max: int4) {}
%aggregate agg269(a: int4->min: int4, max: int4) {}
%aggregate agg270(a: int4->min: int4, max: int4) {}
%aggregate agg271(a: int4->min: int4, max: int4) {}
%aggregate agg272(a: int4->min: int4, max: int4) {}
%aggregate agg273(a: int4->min: int4, max: int4) {}
%aggregate agg274(a: int4->min: int4, max: int4) {}
%aggregate agg275(a: int4->min: int4, max: int4) {}
%aggregate agg276(a: int4->min: int4, max: int4) {}
%aggregate agg277(a: int4->min: int4, max: int4) {}
%aggregate agg278(a: int4->min: int4, max: int4) {}
%aggregate agg279(a: int4->min: int4, max: int4) {}
%aggregate agg280(a: int4->min: int4, max: int4) {}
%aggregate agg281(a: int4->min: int4, max: int4) {}
%aggregate agg282(a: int4->min: int4, max: int4) {}
%aggregate agg283(a: int4->min: int4, max: int4) {}
%aggregate agg284(a: int4->min: int4, max: int4) {}
%aggregate agg285(a: int4->min: int4, max: int4) {}
%aggregate agg286(a: int4->min: int4, max: int4) {}
%aggregate agg287(a: int4->min: int4, max: int4) {}
%aggregate agg288(a: int4->min: int4, max: int4) {}
%aggregate agg289(a: int4->min: int4, max: int4) {}
%aggregate agg290(a: int4->min: int4, max: int4) {}
%aggregate agg291(a: int4->min: int4, max: int4) {}
%aggregate agg292(a: int4->min: int4, max: int4) {}
%aggregate agg293(a: int4->min: int4, max: int4) {}
%aggregate agg294(a: int4->min: int4, max: int4) {}
%aggregate agg295(a: int4->min: int4, max: int4) {}
%aggregate agg296(a: int4->min: int4, max: int4) {}
%aggregate agg297(a: int4->min: int4, max: int4) {}
%aggregate agg298(a: int4->min: int4, max: int4) {}
%aggregate agg299(a: int4->min: int4, max: int4) {}
%aggregate agg300(a: int4->min: int4, max: int4) {}
%aggregate agg301(a: int4->min: int4, max: int4) {}
%aggregate agg302(a: int4->min: int4, max: int4) {}
%aggregate agg303(a: int4->min: int4, max: int4) {}
%aggregate agg304(a: int4->min: int4, max: int4) {}
%aggregate agg305(a: int4->min: int4, max: int4) {}
%aggregate agg306(a: int4->min: int4, max: int4) {}
%aggregate agg307(a: int4->min: int4, max: int4) {}
%aggregate agg308(a: int4->min: int4, max: int4) {}
%aggregate agg309(a: int4->min: int4, max: int4) {}
%aggregate agg310(a: int4->min: int4, max: int4) {}
%aggregate agg311(a: int4->min: int4, max: int4) {}
%aggregate agg312(a: int4->min: int4, max: int4) {}
%aggregate agg313(a: int4->min: int4, max: int4) {}
%aggregate agg314(a: int4->min: int4, max: int4) {}
%aggregate agg315(a: int4->min: int4, max: int4) {}
%aggregate agg316(a: int4->min: int4, max: int4) {}
%aggregate agg317(a: int4->min: int4, max: int4) {}
%aggregate agg318(a: int4->min: int4, max: int4) {}
%aggregate agg319(a: int4->min: int4, max: int4) {}
%aggregate agg320(a: int4->min: int4, max: int4) {}
%aggregate agg321(a: int4->min: int4, max: int4) {}
%aggregate agg322(a: int4->min: int4, max: int4) {}
%aggregate agg323(a: int4->min: int4, max: int4) {}
%aggregate agg324(a: int4->min: int4, max: int4) {}
%aggregate agg325(a: int4->min: int4, max: int4) {}
%aggregate agg326(a: int4->min: int4, max: int4) {}
%aggregate agg327(a: int4->min: int4, max: int4) {}
%aggregate agg328(a: int4->min: int4, max: int4) {}
%aggregate agg329(a: int4->min: int4, max: int4) {}
%aggregate agg330(a: int4->min: int4, max: int4) {}
%aggregate agg331(a: int4->min: int4, max: int4) {}
%aggregate agg332(a: int4->min: int4, max: int4) {}
%aggregate agg333(a: int4->min: int4, max: int4) {}
%aggregate agg334(a: int4->min: int4, max: int4) {}
%aggregate agg335(a: int4->min: int4, max: int4) {}
%aggregate agg336(a: int4->min: int4, max: int4) {}
%aggregate agg337(a: int4->min: int4, max: int4) {}
%aggregate agg338(a: int4->min: int4, max: int4) {}
%aggregate agg339(a: int4->min: int4, max: int4) {}
%aggregate agg340(a: int4->min: int4, max: int4) {}
%aggregate agg341(a: int4->min: int4, max: int4) {}
%aggregate agg342(a: int4->min: int4, max: int4) {}
%aggregate agg343(a: int4->min: int4, max: int4) {}
%aggregate agg344(a: int4->min: int4, max: int4) {}
%aggregate agg345(a: int4->min: int4, max: int4) {}
%aggregate agg346(a: int4->min: int4, max: int4) {}
%aggregate agg347(a: int4->min: int4, max: int4) {}
%aggregate agg348(a: int4->min: int4, max: int4) {}
%aggregate agg349(a: int4->min: int4, max: int4) {}
%aggregate agg350(a: int4->min: int4, max: int4) {}
%aggregate agg351(a: int4->min: int4, max: int4) {}
%aggregate agg352(a: int4->min: int4, max: int4) {}
%aggregate agg353(a: int4->min: int4, max: int4) {}
%aggregate agg354(a: int4->min: int4, max: int4) {}
%aggregate agg355(a: int4->min: int4, max: int4) {}
%aggregate agg356(a: int4->min: int4, max: int4) {}
%aggregate agg357(a: int4->min: int4, max: int4) {}
%aggregate agg358(a: int4->min: int4, max: int4) {}
%aggregate agg359(a: int4->min: int4, max: int4) {}
%aggregate agg360(a: int4->min: int4, max: int4) {}
%aggregate agg361(a: int4->min: int4, max: int4) {}
%aggregate agg362(a: int4->min: int4, max: int4) {}
%aggregate agg363(a: int4->min: int4, max: int4) {}
%aggregate agg364(a: int4->min: int4, max: int4) {}
%aggregate agg365(a: int4->min: int4, max: int4) {}
%aggregate agg366(a: int4->min: int4, max: int4) {}
%aggregate agg367(a: int4->min: int4, max: int4) {}
%aggregate agg368(a: int4->min: int4, max: int4) {}
%aggregate agg369(a: int4->min: int4, max: int4) {}
%aggregate agg370(a: int4->min: int4, max: int4) {}
%aggregate agg371(a: int4->min: int4, max: int4) {}
%aggregate agg372(a: int4->min: int4, max: int4) {}
%aggregate agg373(a: int4->min: int4, max: int4) {}
%aggregate agg374(a: int4->min: int4, max: int4) {}
%aggregate agg375(a: int4->min: int4, max: int4) {}
%aggregate agg376(a: int4->min: int4, max: int4) {}
%aggregate agg377(a: int4->min: int4, max: int4) {}
%aggregate agg378(a: int4->min: int4, max: int4) {}
%aggregate agg379(a: int4->min: int4, max: int4) {}
%aggregate agg380(a: int4->min: int4, max: int4) {}
%aggregate agg381(a: int4->min: int4, max: int4) {}
%aggregate agg382(a: int4->min: int4, max: int4) {}
%aggregate agg383(a: int4->min: int4, max: int4) {}
%aggregate agg384(a: int4->min: int4, max: int4) {}
%aggregate agg385(a: int4->min: int4, max: int4) {}
%aggregate agg386(a: int4->min: int4, max: int4) {}
%aggregate agg387(a: int4->min: int4, max: int4) {}
%aggregate agg388(a: int4->min: int4, max: int4) {}
%aggregate agg389(a: int4->min: int4, max: int4) {}
%aggregate agg390(a: int4->min: int4, max: int4) {}
%aggregate agg391(a: int4->min: int4, max: int4) {}
%aggregate agg392(a: int4->min: int4, max: int4) {}
%aggregate agg393(a: int4->min: int4, max: int4) {}
%aggregate agg394(a: int4->min: int4, max: int4) {}
%aggregate agg395(a: int4->min: int4, max: int4) {}
%aggregate agg396(a: int4->min: int4, max: int4) {}
%aggregate agg397(a: int4->min: int4, max: int4) {}
%aggregate agg398(a: int4->min: int4, max: int4) {}
%aggregate agg399(a: int4->min: int4, max: int4) {}
%aggregate agg400(a: int4->min: int4, max: int4) {}
%aggregate agg401(a: int4->min: int4, max: int4) {}
%aggregate agg402(a: int4->min: int4, max: int4) {}
%aggregate agg403(a: int4->min: int4, max: int4) {}
%aggregate agg404(a: int4->min: int4, max: int4) {}
%aggregate agg405(a: int4->min: int4, max: int4) {}
%aggregate agg406(a: int4->min: int4, max: int4) {}
%aggregate agg407(a: int4->min: int4, max: int4) {}
%aggregate agg408(a: int4->min: int4, max: int4) {}
%aggregate agg409(a: int4->min: int4, max: int4) {}
%aggregate agg410(a: int4->min: int4, max: int4) {}
%aggregate agg411(a: int4->min: int4, max: int4) {}
%aggregate agg412(a: int4->min: int4, max: int4) {}
%aggregate agg413(a: int4->min: int4, max: int4) {}
%aggregate agg414(a: int4->min: int4, max: int4) {}
%aggregate agg415(a: int4->min: int4, max: int4) {}
%aggregate agg416(a: int4->min: int4, max: int4) {}
%aggregate agg417(a: int4->min: int4, max: int4) {}
%aggregate agg418(a: int4->min: int4, max: int4) {}
%aggregate agg419(a: int4->min: int4, max: int4) {}
%aggregate agg420(a: int4->min: int4, max: int4) {}
%aggregate agg421(a: int4->min: int4, max: int4) {}
%aggregate agg422(a: int4->min: int4, max: int4) {}
%aggregate agg423(a: int4->min: int4, max: int4) {}
%aggregate agg424(a: int4->min: int4, max: int4) {}
%aggregate agg425(a: int4->min: int4, max: int4) {}
%aggregate agg426(a: int4->min: int4, max: int4) {}
%aggregate agg427(a: int4->min: int4, max: int4) {}
%aggregate agg428(a: int4->min: int4, max: int4) {}
%aggregate agg429(a: int4->min: int4, max: int4) {}
%aggregate agg430(a: int4->min: int4, max: int4) {}
%aggregate agg431(a: int4->min: int4, max: int4) {}
%aggregate agg432(a: int4->min: int4, max: int4) {}
%aggregate agg433(a: int4->min: int4, max: int4) {}
%aggregate agg434(a: int4->min: int4, max: int4) {}
%aggregate agg435(a: int4->min: int4, max: int4) {}
%aggregate agg436(a: int4->min: int4, max: int4) {}
%aggregate agg437(a: int4->min: int4, max: int4) {}
%aggregate agg438(a: int4->min: int4, max: int4) {}
%aggregate agg439(a: int4->min: int4, max: int4) {}
%aggregate agg440(a: int4->min: int4, max: int4) {}
%aggregate agg441(a: int4->min: int4, max: int4) {}
%aggregate agg442(a: int4->min: int4, max: int4) {}
%aggregate agg443(a: int4->min: int4, max: int4) {}
%aggregate agg444(a: int4->min: int4, max: int4) {}
%aggregate agg445(a: int4->min: int4, max: int4) {}
%aggregate agg446(a: int4->min: int4, max: int4) {}
%aggregate agg447(a: int4->min: int4, max: int4) {}
%aggregate agg448(a: int4->min: int4, max: int4) {}
%aggregate agg449(a: int4->min: int4, max: int4) {}
%aggregate agg450(a: int4->min: int4, max: int4) {}
%aggregate agg451(a: int4->min: int4, max: int4) {}
%aggregate agg452(a: int4->min: int4, max: int4) {}
%aggregate agg453(a: int4->min: int4, max: int4) {}
%aggregate agg454(a: int4->min: int4, max: int4) {}
%aggregate agg455(a: int4->min: int4, max: int4) {}
%aggregate agg456(a: int4->min: int4, max: int4) {}
%aggregate agg457(a: int4->min: int4, max: int4) {}
%aggregate agg458(a: int4->min: int4, max: int4) {}
%aggregate agg459(a: int4->min: int4, max: int4) {}
%aggregate agg460(a: int4->min: int4, max: int4) {}
%aggregate agg461(a: int4->min: int4, max: int4) {}
%aggregate agg462(a: int4->min: int4, max: int4) {}
%aggregate agg463(a: int4->min: int4, max: int4) {}
%aggregate agg464(a: int4->min: int4, max: int4) {}
%aggregate agg465(a: int4->min: int4, max: int4) {}
%aggregate agg466(a: int4->min: int4, max: int4) {}
%aggregate agg467(a: int4->min: int4, max: int4) {}
%aggregate agg468(a: int4->min: int4, max: int4) {}
%aggregate agg469(a: int4->min: int4, max: int4) {}
%aggregate agg470(a: int4->min: int4, max: int4) {}
%aggregate agg471(a: int4->min: int4, max: int4) {}
%aggregate agg472(a: int4->min: int4, max: int4) {}
%aggregate agg473(a: int4->min: int4, max: int4) {}
%aggregate agg474(a: int4->min: int4, max: int4) {}
%aggregate agg475(a: int4->min: int4, max: int4) {}
%aggregate agg476(a: int4->min: int4, max: int4) {}
%aggregate agg477(a: int4->min: int4, max: int4) {}
%aggregate agg478(a: int4->min: int4, max: int4) {}
%aggregate agg479(a: int4->min: int4, max: int4) {}
%aggregate agg480(a: int4->min: int4, max: int4) {}
%aggregate agg481(a: int4->min: int4, max: int4) {}
%aggregate agg482(a: int4->min: int4, max: int4) {}
%aggregate agg483(a: int4->min: int4, max: int4) {}
%aggregate agg484(a: int4->min: int4, max: int4) {}
%aggregate agg485(a: int4->min: int4, max: int4) {}
%aggregate agg486(a: int4->min: int4, max: int4) {}
%aggregate agg487(a: int4->min: int4, max: int4) {}
%aggregate agg488(a: int4->min: int4, max: int4) {}
%aggregate agg489(a: int4->min: int4, max: int4) {}
%aggregate agg490(a: int4->min: int4, max: int4) {}
%aggregate agg491(a: int4->min: int4, max: int4) {}
%aggregate agg492(a: int4->min: int4, max: int4) {}
%aggregate agg493(a: int4->min: int4, max: int4) {}
%aggregate agg494(a: int4->min: int4, max: int4) {}
%aggregate agg495(a: int4->min: int4, max: int4) {}
%aggregate agg496(a: int4->min: int4, max: int4) {}
%aggregate agg497(a: int4->min: int4, max: int4) {}
%aggregate agg498(a: int4->min: int4, max: int4) {}
%aggregate agg499(a: int4->min: int4, max: int4) {}
%aggregate agg500(a: int4->min: int4, max: int4) {}
%aggregate agg501(a: int4->min: int4, max: int4) {}
%aggregate agg502(a: int4->min: int4, max: int4) {}
%aggregate agg503(a: int4->min: int4, max: int4) {}
%aggregate agg504(a: int4->min: int4, max: int4) {}
%aggregate agg505(a: int4->min: int4, max: int4) {}
%aggregate agg506(a: int4->min: int4, max: int4) {}
%aggregate agg507(a: int4->min: int4, max: int4) {}
%aggregate agg508(a: int4->min: int4, max: int4) {}
%aggregate agg509(a: int4->min: int4, max: int4) {}
%aggregate agg510(a: int4->min: int4, max: int4) {}
%aggregate agg511(a: int4->min: int4, max: int4) {}
%aggregate agg512(a: int4->min: int4, max: int4) {}
%aggregate agg513(a: int4->min: int4, max: int4) {}
%aggregate agg514(a: int4->min: int4, max: int4) {}
%aggregate agg515(a: int4->min: int4, max: int4) {}
%aggregate agg516(a: int4->min: int4, max: int4) {}
%aggregate agg517(a: int4->min: int4, max: int4) {}
%aggregate agg518(a: int4->min: int4, max: int4) {}
%aggregate agg519(a: int4->min: int4, max: int4) {}
%aggregate agg520(a: int4->min: int4, max: int4) {}
%aggregate agg521(a: int4->min: int4, max: int4) {}
%aggregate agg522(a: int4->min: int4, max: int4) {}
%aggregate agg523(a: int4->min: int4, max: int4) {}
%aggregate agg524(a: int4->min: int4, max: int4) {}
%aggregate agg525(a: int4->min: int4, max: int4) {}
%aggregate agg526(a: int4->min: int4, max: int4) {}
%aggregate agg527(a: int4->min: int4, max: int4) {}
%aggregate agg528(a: int4->min: int4, max: int4) {}
%aggregate agg529(a: int4->min: int4, max: int4) {}
%aggregate agg530(a: int4->min: int4, max: int4) {}
%aggregate agg531(a: int4->min: int4, max: int4) {}
%aggregate agg532(a: int4->min: int4, max: int4) {}
%aggregate agg533(a: int4->min: int4, max: int4) {}
%aggregate agg534(a: int4->min: int4, max: int4) {}
%aggregate agg535(a: int4->min: int4, max: int4) {}
%aggregate agg536(a: int4->min: int4, max: int4) {}
%aggregate agg537(a: int4->min: int4, max: int4) {}
%aggregate agg538(a: int4->min: int4, max: int4) {}
%aggregate agg539(a: int4->min: int4, max: int4) {}
%aggregate agg540(a: int4->min: int4, max: int4) {}
%aggregate agg541(a: int4->min: int4, max: int4) {}
%aggregate agg542(a: int4->min: int4, max: int4) {}
%aggregate agg543(a: int4->min: int4, max: int4) {}
%aggregate agg544(a: int4->min: int4, max: int4) {}
%aggregate agg545(a: int4->min: int4, max: int4) {}
%aggregate agg546(a: int4->min: int4, max: int4) {}
%aggregate agg547(a: int4->min: int4, max: int4) {}
%aggregate agg548(a: int4->min: int4, max: int4) {}
%aggregate agg549(a: int4->min: int4, max: int4) {}
%aggregate agg550(a: int4->min: int4, max: int4) {}
%aggregate agg551(a: int4->min: int4, max: int4) {}
%aggregate agg552(a: int4->min: int4, max: int4) {}
%aggregate agg553(a: int4->min: int4, max: int4) {}
%aggregate agg554(a: int4->min: int4, max: int4) {}
%aggregate agg555(a: int4->min: int4, max: int4) {}
%aggregate agg556(a: int4->min: int4, max: int4) {}
%aggregate agg557(a: int4->min: int4, max: int4) {}
%aggregate agg558(a: int4->min: int4, max: int4) {}
%aggregate agg559(a: int4->min: int4, max: int4) {}
%aggregate agg560(a: int4->min: int4, max: int4) {}
%aggregate agg561(a: int4->min: int4, max: int4) {}
%aggregate agg562(a: int4->min: int4, max: int4) {}
%aggregate agg563(a: int4->min: int4, max: int4) {}
%aggregate agg564(a: int4->min: int4, max: int4) {}
%aggregate agg565(a: int4->min: int4, max: int4) {}
%aggregate agg566(a: int4->min: int4, max: int4) {}
%aggregate agg567(a: int4->min: int4, max: int4) {}
%aggregate agg568(a: int4->min: int4, max: int4) {}
%aggregate agg569(a: int4->min: int4, max: int4) {}
%aggregate agg570(a: int4->min: int4, max: int4) {}
%aggregate agg571(a: int4->min: int4, max: int4) {}
%aggregate agg572(a: int4->min: int4, max: int4) {}
%aggregate agg573(a: int4->min: int4, max: int4) {}
%aggregate agg574(a: int4->min: int4, max: int4) {}
%aggregate agg575(a: int4->min: int4, max: int4) {}
%aggregate agg576(a: int4->min: int4, max: int4) {}
%aggregate agg577(a: int4->min: int4, max: int4) {}
%aggregate agg578(a: int4->min: int4, max: int4) {}
%aggregate agg579(a: int4->min: int4, max: int4) {}
%aggregate agg580(a: int4->min: int4, max: int4) {}
%aggregate agg581(a: int4->min: int4, max: int4) {}
%aggregate agg582(a: int4->min: int4, max: int4) {}
%aggregate agg583(a: int4->min: int4, max: int4) {}
%aggregate agg584(a: int4->min: int4, max: int4) {}
%aggregate agg585(a: int4->min: int4, max: int4) {}
%aggregate agg586(a: int4->min: int4, max: int4) {}
%aggregate agg587(a: int4->min: int4, max: int4) {}
%aggregate agg588(a: int4->min: int4, max: int4) {}
%aggregate agg589(a: int4->min: int4, max: int4) {}
%aggregate agg590(a: int4->min: int4, max: int4) {}
%aggregate agg591(a: int4->min: int4, max: int4) {}
%aggregate agg592(a: int4->min: int4, max: int4) {}
%aggregate agg593(a: int4->min: int4, max: int4) {}
%aggregate agg594(a: int4->min: int4, max: int4) {}
%aggregate agg595(a: int4->min: int4, max: int4) {}
%aggregate agg596(a: int4->min: int4, max: int4) {}
%aggregate agg597(a: int4->min: int4, max: int4) {}
%aggregate agg598(a: int4->min: int4, max: int4) {}
%aggregate agg599(a: int4->min: int4, max: int4) {}
%aggregate agg600(a: int4->min: int4, max: int4) {}
%aggregate agg601(a: int4->min: int4, max: int4) {}
%aggregate agg602(a: int4->min: int4, max: int4) {}
%aggregate agg603(a: int4->min: int4, max: int4) {}
%aggregate agg604(a: int4->min: int4, max: int4) {}
%aggregate agg605(a: int4->min: int4, max: int4) {}
%aggregate agg606(a: int4->min: int4, max: int4) {}
%aggregate agg607(a: int4->min: int4, max: int4) {}
%aggregate agg608(a: int4->min: int4, max: int4) {}
%aggregate agg609(a: int4->min: int4, max: int4) {}
%aggregate agg610(a: int4->min: int4, max: int4) {}
%aggregate agg611(a: int4->min: int4, max: int4) {}
%aggregate agg612(a: int4->min: int4, max: int4) {}
%aggregate agg613(a: int4->min: int4, max: int4) {}
%aggregate agg614(a: int4->min: int4, max: int4) {}
%aggregate agg615(a: int4->min: int4, max: int4) {}
%aggregate agg616(a: int4->min: int4, max: int4) {}
%aggregate agg617(a: int4->min: int4, max: int4) {}
%aggregate agg618(a: int4->min: int4, max: int4) {}
%aggregate agg619(a: int4->min: int4, max: int4) {}
%aggregate agg620(a: int4->min: int4, max: int4) {}
%aggregate agg621(a: int4->min: int4, max: int4) {}
%aggregate agg622(a: int4->min: int4, max: int4) {}
%aggregate agg623(a: int4->min: int4, max: int4) {}
%aggregate agg624(a: int4->min: int4, max: int4) {}
%aggregate agg625(a: int4->min: int4, max: int4) {}
%aggregate agg626(a: int4->min: int4, max: int4) {}
%aggregate agg627(a: int4->min: int4, max: int4) {}
%aggregate agg628(a: int4->min: int4, max: int4) {}
%aggregate agg629(a: int4->min: int4, max: int4) {}
%aggregate agg630(a: int4->min: int4, max: int4) {}
%aggregate agg631(a: int4->min: int4, max: int4) {}
%aggregate agg632(a: int4->min: int4, max: int4) {}
%aggregate agg633(a: int4->min: int4, max: int4) {}
%aggregate agg634(a: int4->min: int4, max: int4) {}
%aggregate agg635(a: int4->min: int4, max: int4) {}
%aggregate agg636(a: int4->min: int4, max: int4) {}
%aggregate agg637(a: int4->min: int4, max: int4) {}
%aggregate agg638(a: int4->min: int4, max: int4) {}
%aggregate agg639(a: int4->min: int4, max: int4) {}
%aggregate agg640(a: int4->min: int4, max: int4) {}
%aggregate agg641(a: int4->min: int4, max: int4) {}
%aggregate agg642(a: int4->min: int4, max: int4) {}
%aggregate agg643(a: int4->min: int4, max: int4) {}
%aggregate agg644(a: int4->min: int4, max: int4) {}
%aggregate agg645(a: int4->min: int4, max: int4) {}
%aggregate agg646(a: int4->min: int4, max: int4) {}
%aggregate agg647(a: int4->min: int4, max: int4) {}
%aggregate agg648(a: int4->min: int4, max: int4) {}
%aggregate agg649(a: int4->min: int4, max: int4) {}
%aggregate agg650(a: int4->min: int4, max: int4) {}
%aggregate agg651(a: int4->min: int4, max: int4) {}
%aggregate agg652(a: int4->min: int4, max: int4) {}
%aggregate agg653(a: int4->min: int4, max: int4) {}
%aggregate agg654(a: int4->min: int4, max: int4) {}
%aggregate agg655(a: int4->min: int4, max: int4) {}
%aggregate agg656(a: int4->min: int4, max: int4) {}
%aggregate agg657(a: int4->min: int4, max: int4) {}
%aggregate agg658(a: int4->min: int4, max: int4) {}
%aggregate agg659(a: int4->min: int4, max: int4) {}
%aggregate agg660(a: int4->min: int4, max: int4) {}
%aggregate agg661(a: int4->min: int4, max: int4) {}
%aggregate agg662(a: int4->min: int4, max: int4) {}
%aggregate agg663(a: int4->min: int4, max: int4) {}
%aggregate agg664(a: int4->min: int4, max: int4) {}
%aggregate agg665(a: int4->min: int4, max: int4) {}
%aggregate agg666(a: int4->min: int4, max: int4) {}
%aggregate agg667(a: int4->min: int4, max: int4) {}
%aggregate agg668(a: int4->min: int4, max: int4) {}
%aggregate agg669(a: int4->min: int4, max: int4) {}
%aggregate agg670(a: int4->min: int4, max: int4) {}
%aggregate agg671(a: int4->min: int4, max: int4) {}
%aggregate agg672(a: int4->min: int4, max: int4) {}
%aggregate agg673(a: int4->min: int4, max: int4) {}
%aggregate agg674(a: int4->min: int4, max: int4) {}
%aggregate agg675(a: int4->min: int4, max: int4) {}
%aggregate agg676(a: int4->min: int4, max: int4) {}
%aggregate agg677(a: int4->min: int4, max: int4) {}
%aggregate agg678(a: int4->min: int4, max: int4) {}
%aggregate agg679(a: int4->min: int4, max: int4) {}
%aggregate agg680(a: int4->min: int4, max: int4) {}
%aggregate agg681(a: int4->min: int4, max: int4) {}
%aggregate agg682(a: int4->min: int4, max: int4) {}
%aggregate agg683(a: int4->min: int4, max: int4) {}
%aggregate agg684(a: int4->min: int4, max: int4) {}
%aggregate agg685(a: int4->min: int4, max: int4) {}
%aggregate agg686(a: int4->min: int4, max: int4) {}
%aggregate agg687(a: int4->min: int4, max: int4) {}
%aggregate agg688(a: int4->min: int4, max: int4) {}
%aggregate agg689(a: int4->min: int4, max: int4) {}
%aggregate agg690(a: int4->min: int4, max: int4) {}
%aggregate agg691(a: int4->min: int4, max: int4) {}
%aggregate agg692(a: int4->min: int4, max: int4) {}
%aggregate agg693(a: int4->min: int4, max: int4) {}
%aggregate agg694(a: int4->min: int4, max: int4) {}
%aggregate agg695(a: int4->min: int4, max: int4) {}
%aggregate agg696(a: int4->min: int4, max: int4) {}
%aggregate agg697(a: int4->min: int4, max: int4) {}
%aggregate agg698(a: int4->min: int4, max: int4) {}
%aggregate agg699(a: int4->min: int4, max: int4) {}
%aggregate agg700(a: int4->min: int4, max: int4) {}
%aggregate agg701(a: int4->min: int4, max: int4) {}
%aggregate agg702(a: int4->min: int4, max: int4) {}
%aggregate agg703(a: int4->min: int4, max: int4) {}
%aggregate agg704(a: int4->min: int4, max: int4) {}
%aggregate agg705(a: int4->min: int4, max: int4) {}
%aggregate agg706(a: int4->min: int4, max: int4) {}
%aggregate agg707(a: int4->min: int4, max: int4) {}
%aggregate agg708(a: int4->min: int4, max: int4) {}
%aggregate agg709(a: int4->min: int4, max: int4) {}
%aggregate agg710(a: int4->min: int4, max: int4) {}
%aggregate agg711(a: int4->min: int4, max: int4) {}
%aggregate agg712(a: int4->min: int4, max: int4) {}
%aggregate agg713(a: int4->min: int4, max: int4) {}
%aggregate agg714(a: int4->min: int4, max: int4) {}
%aggregate agg715(a: int4->min: int4, max: int4) {}
%aggregate agg716(a: int4->min: int4, max: int4) {}
%aggregate agg717(a: int4->min: int4, max: int4) {}
%aggregate agg718(a: int4->min: int4, max: int4) {}
%aggregate agg719(a: int4->min: int4, max: int4) {}
%aggregate agg720(a: int4->min: int4, max: int4) {}
%aggregate agg721(a: int4->min: int4, max: int4) {}
%aggregate agg722(a: int4->min: int4, max: int4) {}
%aggregate agg723(a: int4->min: int4, max: int4) {}
%aggregate agg724(a: int4->min: int4, max: int4) {}
%aggregate agg725(a: int4->min: int4, max: int4) {}
%aggregate agg726(a: int4->min: int4, max: int4) {}
%aggregate agg727(a: int4->min: int4, max: int4) {}
%aggregate agg728(a: int4->min: int4, max: int4) {}
%aggregate agg729(a: int4->min: int4, max: int4) {}
%aggregate agg730(a: int4->min: int4, max: int4) {}
%aggregate agg731(a: int4->min: int4, max: int4) {}
%aggregate agg732(a: int4->min: int4, max: int4) {}
%aggregate agg733(a: int4->min: int4, max: int4) {}
%aggregate agg734(a: int4->min: int4, max: int4) {}
%aggregate agg735(a: int4->min: int4, max: int4) {}
%aggregate agg736(a: int4->min: int4, max: int4) {}
%aggregate agg737(a: int4->min: int4, max: int4) {}
%aggregate agg738(a: int4->min: int4, max: int4) {}
%aggregate agg739(a: int4->min: int4, max: int4) {}
%aggregate agg740(a: int4->min: int4, max: int4) {}
%aggregate agg741(a: int4->min: int4, max: int4) {}
%aggregate agg742(a: int4->min: int4, max: int4) {}
%aggregate agg743(a: int4->min: int4, max: int4) {}
%aggregate agg744(a: int4->min: int4, max: int4) {}
%aggregate agg745(a: int4->min: int4, max: int4) {}
%aggregate agg746(a: int4->min: int4, max: int4) {}
%aggregate agg747(a: int4->min: int4, max: int4) {}
%aggregate agg748(a: int4->min: int4, max: int4) {}
%aggregate agg749(a: int4->min: int4, max: int4) {}
%aggregate agg750(a: int4->min: int4, max: int4) {}
%aggregate agg751(a: int4->min: int4, max: int4) {}
%aggregate agg752(a: int4->min: int4, max: int4) {}
%aggregate agg753(a: int4->min: int4, max: int4) {}
%aggregate agg754(a: int4->min: int4, max: int4) {}
%aggregate agg755(a: int4->min: int4, max: int4) {}
%aggregate agg756(a: int4->min: int4, max: int4) {}
%aggregate agg757(a: int4->min: int4, max: int4) {}
%aggregate agg758(a: int4->min: int4, max: int4) {}
%aggregate agg759(a: int4->min: int4, max: int4) {}
%aggregate agg760(a: int4->min: int4, max: int4) {}
%aggregate agg761(a: int4->min: int4, max: int4) {}
%aggregate agg762(a: int4->min: int4, max: int4) {}
%aggregate agg763(a: int4->min: int4, max: int4) {}
%aggregate agg764(a: int4->min: int4, max: int4) {}
%aggregate agg765(a: int4->min: int4, max: int4) {}
%aggregate agg766(a: int4->min: int4, max: int4) {}
%aggregate agg767(a: int4->min: int4, max: int4) {}
%aggregate agg768(a: int4->min: int4, max: int4) {}
%aggregate agg769(a: int4->min: int4, max: int4) {}
%aggregate agg770(a: int4->min: int4, max: int4) {}
%aggregate agg771(a: int4->min: int4, max: int4) {}
%aggregate agg772(a: int4->min: int4, max: int4) {}
%aggregate agg773(a: int4->min: int4, max: int4) {}
%aggregate agg774(a: int4->min: int4, max: int4) {}
%aggregate agg775(a: int4->min: int4, max: int4) {}
%aggregate agg776(a: int4->min: int4, max: int4) {}
%aggregate agg777(a: int4->min: int4, max: int4) {}
%aggregate agg778(a: int4->min: int4, max: int4) {}
%aggregate agg779(a: int4->min: int4, max: int4) {}
%aggregate agg780(a: int4->min: int4, max: int4) {}
%aggregate agg781(a: int4->min: int4, max: int4) {}
%aggregate agg782(a: int4->min: int4, max: int4) {}
%aggregate agg783(a: int4->min: int4, max: int4) {}
%aggregate agg784(a: int4->min: int4, max: int4) {}
%aggregate agg785(a: int4->min: int4, max: int4) {}
%aggregate agg786(a: int4->min: int4, max: int4) {}
%aggregate agg787(a: int4->min: int4, max: int4) {}
%aggregate agg788(a: int4->min: int4, max: int4) {}
%aggregate agg789(a: int4->min: int4, max: int4) {}
%aggregate agg790(a: int4->min: int4, max: int4) {}
%aggregate agg791(a: int4->min: int4, max: int4) {}
%aggregate agg792(a: int4->min: int4, max: int4) {}
%aggregate agg793(a: int4->min: int4, max: int4) {}
%aggregate agg794(a: int4->min: int4, max: int4) {}
%aggregate agg795(a: int4->min: int4, max: int4) {}
%aggregate agg796(a: int4->min: int4, max: int4) {}
%aggregate agg797(a: int4->min: int4, max: int4) {}
%aggregate agg798(a: int4->min: int4, max: int4) {}
%aggregate agg799(a: int4->min: int4, max: int4) {}
%aggregate agg800(a: int4->min: int4, max: int4) {}
%aggregate agg801(a: int4->min: int4, max: int4) {}
%aggregate agg802(a: int4->min: int4, max: int4) {}
%aggregate agg803(a: int4->min: int4, max: int4) {}
%aggregate agg804(a: int4->min: int4, max: int4) {}
%aggregate agg805(a: int4->min: int4, max: int4) {}
%aggregate agg806(a: int4->min: int4, max: int4) {}
%aggregate agg807(a: int4->min: int4, max: int4) {}
%aggregate agg808(a: int4->min: int4, max: int4) {}
%aggregate agg809(a: int4->min: int4, max: int4) {}
%aggregate agg810(a: int4->min: int4, max: int4) {}
%aggregate agg811(a: int4->min: int4, max: int4) {}
%aggregate agg812(a: int4->min: int4, max: int4) {}
%aggregate agg813(a: int4->min: int4, max: int4) {}
%aggregate agg814(a: int4->min: int4, max: int4) {}
%aggregate agg815(a: int4->min: int4, max: int4) {}
%aggregate agg816(a: int4->min: int4, max: int4) {}
%aggregate agg817(a: int4->min: int4, max: int4) {}
%aggregate agg818(a: int4->min: int4, max: int4) {}
%aggregate agg819(a: int4->min: int4, max: int4) {}
%aggregate agg820(a: int4->min: int4, max: int4) {}
%aggregate agg821(a: int4->min: int4, max: int4) {}
%aggregate agg822(a: int4->min: int4, max: int4) {}
%aggregate agg823(a: int4->min: int4, max: int4) {}
%aggregate agg824(a: int4->min: int4, max: int4) {}
%aggregate agg825(a: int4->min: int4, max: int4) {}
%aggregate agg826(a: int4->min: int4, max: int4) {}
%aggregate agg827(a: int4->min: int4, max: int4) {}
%aggregate agg828(a: int4->min: int4, max: int4) {}
%aggregate agg829(a: int4->min: int4, max: int4) {}
%aggregate agg830(a: int4->min: int4, max: int4) {}
%aggregate agg831(a: int4->min: int4, max: int4) {}
%aggregate agg832(a: int4->min: int4, max: int4) {}
%aggregate agg833(a: int4->min: int4, max: int4) {}
%aggregate agg834(a: int4->min: int4, max: int4) {}
%aggregate agg835(a: int4->min: int4, max: int4) {}
%aggregate agg836(a: int4->min: int4, max: int4) {}
%aggregate agg837(a: int4->min: int4, max: int4) {}
%aggregate agg838(a: int4->min: int4, max: int4) {}
%aggregate agg839(a: int4->min: int4, max: int4) {}
%aggregate agg840(a: int4->min: int4, max: int4) {}
%aggregate agg841(a: int4->min: int4, max: int4) {}
%aggregate agg842(a: int4->min: int4, max: int4) {}
%aggregate agg843(a: int4->min: int4, max: int4) {}
%aggregate agg844(a: int4->min: int4, max: int4) {}
%aggregate agg845(a: int4->min: int4, max: int4) {}
%aggregate agg846(a: int4->min: int4, max: int4) {}
%aggregate agg847(a: int4->min: int4, max: int4) {}
%aggregate agg848(a: int4->min: int4, max: int4) {}
%aggregate agg849(a: int4->min: int4, max: int4) {}
%aggregate agg850(a: int4->min: int4, max: int4) {}
%aggregate agg851(a: int4->min: int4, max: int4) {}
%aggregate agg852(a: int4->min: int4, max: int4) {}
%aggregate agg853(a: int4->min: int4, max: int4) {}
%aggregate agg854(a: int4->min: int4, max: int4) {}
%aggregate agg855(a: int4->min: int4, max: int4) {}
%aggregate agg856(a: int4->min: int4, max: int4) {}
%aggregate agg857(a: int4->min: int4, max: int4) {}
%aggregate agg858(a: int4->min: int4, max: int4) {}
%aggregate agg859(a: int4->min: int4, max: int4) {}
%aggregate agg860(a: int4->min: int4, max: int4) {}
%aggregate agg861(a: int4->min: int4, max: int4) {}
%aggregate agg862(a: int4->min: int4, max: int4) {}
%aggregate agg863(a: int4->min: int4, max: int4) {}
%aggregate agg864(a: int4->min: int4, max: int4) {}
%aggregate agg865(a: int4->min: int4, max: int4) {}
%aggregate agg866(a: int4->min: int4, max: int4) {}
%aggregate agg867(a: int4->min: int4, max: int4) {}
%aggregate agg868(a: int4->min: int4, max: int4) {}
%aggregate agg869(a: int4->min: int4, max: int4) {}
%aggregate agg870(a: int4->min: int4, max: int4) {}
%aggregate agg871(a: int4->min: int4, max: int4) {}
%aggregate agg872(a: int4->min: int4, max: int4) {}
%aggregate agg873(a: int4->min: int4, max: int4) {}
%aggregate agg874(a: int4->min: int4, max: int4) {}
%aggregate agg875(a: int4->min: int4, max: int4) {}
%aggregate agg876(a: int4->min: int4, max: int4) {}
%aggregate agg877(a: int4->min: int4, max: int4) {}
%aggregate agg878(a: int4->min: int4, max: int4) {}
%aggregate agg879(a: int4->min: int4, max: int4) {}
%aggregate agg880(a: int4->min: int4, max: int4) {}
%aggregate agg881(a: int4->min: int4, max: int4) {}
%aggregate agg882(a: int4->min: int4, max: int4) {}
%aggregate agg883(a: int4->min: int4, max: int4) {}
%aggregate agg884(a: int4->min: int4, max: int4) {}
%aggregate agg885(a: int4->min: int4, max: int4) {}
%aggregate agg886(a: int4->min: int4, max: int4) {}
%aggregate agg887(a: int4->min: int4, max: int4) {}
%aggregate agg888(a: int4->min: int4, max: int4) {}
%aggregate agg889(a: int4->min: int4, max: int4) {}
%aggregate agg890(a: int4->min: int4, max: int4) {}
%aggregate agg891(a: int4->min: int4, max: int4) {}
%aggregate agg892(a: int4->min: int4, max: int4) {}
%aggregate agg893(a: int4->min: int4, max: int4) {}
%aggregate agg894(a: int4->min: int4, max: int4) {}
%aggregate agg895(a: int4->min: int4, max: int4) {}
%aggregate agg896(a: int4->min: int4, max: int4) {}
%aggregate agg897(a: int4->min: int4, max: int4) {}
%aggregate agg898(a: int4->min: int4, max: int4) {}
%aggregate agg899(a: int4->min: int4, max: int4) {}
%aggregate agg900(a: int4->min: int4, max: int4) {}
%aggregate agg901(a: int4->min: int4, max: int4) {}
%aggregate agg902(a: int4->min: int4, max: int4) {}
%aggregate agg903(a: int4->min: int4, max: int4) {}
%aggregate agg904(a: int4->min: int4, max: int4) {}
%aggregate agg905(a: int4->min: int4, max: int4) {}
%aggregate agg906(a: int4->min: int4, max: int4) {}
%aggregate agg907(a: int4->min: int4, max: int4) {}
%aggregate agg908(a: int4->min: int4, max: int4) {}
%aggregate agg909(a: int4->min: int4, max: int4) {}
%aggregate agg910(a: int4->min: int4, max: int4) {}
%aggregate agg911(a: int4->min: int4, max: int4) {}
%aggregate agg912(a: int4->min: int4, max: int4) {}
%aggregate agg913(a: int4->min: int4, max: int4) {}
%aggregate agg914(a: int4->min: int4, max: int4) {}
%aggregate agg915(a: int4->min: int4, max: int4) {}
%aggregate agg916(a: int4->min: int4, max: int4) {}
%aggregate agg917(a: int4->min: int4, max: int4) {}
%aggregate agg918(a: int4->min: int4, max: int4) {}
%aggregate agg919(a: int4->min: int4, max: int4) {}
%aggregate agg920(a: int4->min: int4, max: int4) {}
%aggregate agg921(a: int4->min: int4, max: int4) {}
%aggregate agg922(a: int4->min: int4, max: int4) {}
%aggregate agg923(a: int4->min: int4, max: int4) {}
%aggregate agg924(a: int4->min: int4, max: int4) {}
%aggregate agg925(a: int4->min: int4, max: int4) {}
%aggregate agg926(a: int4->min: int4, max: int4) {}
%aggregate agg927(a: int4->min: int4, max: int4) {}
%aggregate agg928(a: int4->min: int4, max: int4) {}
%aggregate agg929(a: int4->min: int4, max: int4) {}
%aggregate agg930(a: int4->min: int4, max: int4) {}
%aggregate agg931(a: int4->min: int4, max: int4) {}
%aggregate agg932(a: int4->min: int4, max: int4) {}
%aggregate agg933(a: int4->min: int4, max: int4) {}
%aggregate agg934(a: int4->min: int4, max: int4) {}
%aggregate agg935(a: int4->min: int4, max: int4) {}
%aggregate agg936(a: int4->min: int4, max: int4) {}
%aggregate agg937(a: int4->min: int4, max: int4) {}
%aggregate agg938(a: int4->min: int4, max: int4) {}
%aggregate agg939(a: int4->min: int4, max: int4) {}
%aggregate agg940(a: int4->min: int4, max: int4) {}
%aggregate agg941(a: int4->min: int4, max: int4) {}
%aggregate agg942(a: int4->min: int4, max: int4) {}
%aggregate agg943(a: int4->min: int4, max: int4) {}
%aggregate agg944(a: int4->min: int4, max: int4) {}
%aggregate agg945(a: int4->min: int4, max: int4) {}
%aggregate agg946(a: int4->min: int4, max: int4) {}
%aggregate agg947(a: int4->min: int4, max: int4) {}
%aggregate agg948(a: int4->min: int4, max: int4) {}
%aggregate agg949(a: int4->min: int4, max: int4) {}
%aggregate agg950(a: int4->min: int4, max: int4) {}
%aggregate agg951(a: int4->min: int4, max: int4) {}
%aggregate agg952(a: int4->min: int4, max: int4) {}
%aggregate agg953(a: int4->min: int4, max: int4) {}
%aggregate agg954(a: int4->min: int4, max: int4) {}
%aggregate agg955(a: int4->min: int4, max: int4) {}
%aggregate agg956(a: int4->min: int4, max: int4) {}
%aggregate agg957(a: int4->min: int4, max: int4) {}
%aggregate agg958(a: int4->min: int4, max: int4) {}
%aggregate agg959(a: int4->min: int4, max: int4) {}
%aggregate agg960(a: int4->min: int4, max: int4) {}
%aggregate agg961(a: int4->min: int4, max: int4) {}
%aggregate agg962(a: int4->min: int4, max: int4) {}
%aggregate agg963(a: int4->min: int4, max: int4) {}
%aggregate agg964(a: int4->min: int4, max: int4) {}
%aggregate agg965(a: int4->min: int4, max: int4) {}
%aggregate agg966(a: int4->min: int4, max: int4) {}
%aggregate agg967(a: int4->min: int4, max: int4) {}
%aggregate agg968(a: int4->min: int4, max: int4) {}
%aggregate agg969(a: int4->min: int4, max: int4) {}
%aggregate agg970(a: int4->min: int4, max: int4) {}
%aggregate agg971(a: int4->min: int4, max: int4) {}
%aggregate agg972(a: int4->min: int4, max: int4) {}
%aggregate agg973(a: int4->min: int4, max: int4) {}
%aggregate agg974(a: int4->min: int4, max: int4) {}
%aggregate agg975(a: int4->min: int4, max: int4) {}
%aggregate agg976(a: int4->min: int4, max: int4) {}
%aggregate agg977(a: int4->min: int4, max: int4) {}
%aggregate agg978(a: int4->min: int4, max: int4) {}
%aggregate agg979(a: int4->min: int4, max: int4) {}
%aggregate agg980(a: int4->min: int4, max: int4) {}
%aggregate agg981(a: int4->min: int4, max: int4) {}
%aggregate agg982(a: int4->min: int4, max: int4) {}
%aggregate agg983(a: int4->min: int4, max: int4) {}
%aggregate agg984(a: int4->min: int4, max: int4) {}
%aggregate agg985(a: int4->min: int4, max: int4) {}
%aggregate agg986(a: int4->min: int4, max: int4) {}
%aggregate agg987(a: int4->min: int4, max: int4) {}
%aggregate agg988(a: int4->min: int4, max: int4) {}
%aggregate agg989(a: int4->min: int4, max: int4) {}
%aggregate agg990(a: int4->min: int4, max: int4) {}
%aggregate agg991(a: int4->min: int4, max: int4) {}
%aggregate agg992(a: int4->min: int4, max: int4) {}
%aggregate agg993(a: int4->min: int4, max: int4) {}
%aggregate agg994(a: int4->min: int4, max: int4) {}
%aggregate agg995(a: int4->min: int4, max: int4) {}
%aggregate agg996(a: int4->min: int4, max: int4) {}
%aggregate agg997(a: int4->min: int4, max: int4) {}
%aggregate agg998(a: int4->min: int4, max: int4) {}
%aggregate agg999(a: int4->min: int4, max: int4) {}
%aggregate agg1000(a: int4->min: int4, max: int4) {}
%aggregate agg1001(a: int4->min: int4, max: int4) {}
%aggregate agg1002(a: int4->min: int4, max: int4) {}
%aggregate agg1003(a: int4->min: int4, max: int4) {}
%aggregate agg1004(a: int4->min: int4, max: int4) {}
%aggregate agg1005(a: int4->min: int4, max: int4) {}
%aggregate agg1006(a: int4->min: int4, max: int4) {}
%aggregate agg1007(a: int4->min: int4, max: int4) {}
%aggregate agg1008(a: int4->min: int4, max: int4) {}
%aggregate agg1009(a: int4->min: int4, max: int4) {}
%aggregate agg1010(a: int4->min: int4, max: int4) {}
%aggregate agg1011(a: int4->min: int4, max: int4) {}
%aggregate agg1012(a: int4->min: int4, max: int4) {}
%aggregate agg1013(a: int4->min: int4, max: int4) {}
%aggregate agg1014(a: int4->min: int4, max: int4) {}
%aggregate agg1015(a: int4->min: int4, max: int4) {}
%aggregate agg1016(a: int4->min: int4, max: int4) {}
%aggregate agg1017(a: int4->min: int4, max: int4) {}
%aggregate agg1018(a: int4->min: int4, max: int4) {}
%aggregate agg1019(a: int4->min: int4, max: int4) {}
%aggregate agg1020(a: int4->min: int4, max: int4) {}
%aggregate agg1021(a: int4->min: int4, max: int4) {}
%aggregate agg1022(a: int4->min: int4, max: int4) {}
%aggregate agg1023(a: int4->min: int4, max: int4) {}
%aggregate agg1024(a: int4->min: int4, max: int4) {}
%aggregate agg1025(a: int4->min: int4, max: int4) {}



B(a, min, max) :- A(a, b) GROUP-BY(a) agg001(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg002(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg003(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg004(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg005(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg006(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg007(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg008(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg009(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg010(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg011(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg012(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg013(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg014(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg015(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg016(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg017(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg018(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg019(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg020(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg021(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg022(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg023(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg024(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg025(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg026(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg027(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg028(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg029(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg030(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg031(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg032(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg033(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg034(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg035(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg036(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg037(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg038(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg039(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg040(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg041(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg042(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg043(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg044(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg045(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg046(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg047(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg048(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg049(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg050(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg051(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg052(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg053(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg054(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg055(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg056(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg057(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg058(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg059(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg060(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg061(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg062(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg063(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg064(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg065(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg066(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg067(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg068(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg069(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg070(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg071(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg072(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg073(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg074(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg075(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg076(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg077(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg078(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg079(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg080(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg081(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg082(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg083(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg084(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg085(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg086(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg087(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg088(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg089(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg090(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg091(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg092(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg093(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg094(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg095(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg096(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg097(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg098(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg099(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg100(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg101(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg102(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg103(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg104(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg105(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg106(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg107(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg108(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg109(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg110(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg111(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg112(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg113(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg114(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg115(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg116(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg117(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg118(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg119(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg120(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg121(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg122(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg123(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg124(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg125(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg126(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg127(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg128(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg129(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg130(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg131(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg132(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg133(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg134(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg135(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg136(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg137(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg138(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg139(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg140(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg141(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg142(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg143(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg144(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg145(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg146(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg147(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg148(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg149(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg150(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg151(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg152(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg153(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg154(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg155(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg156(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg157(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg158(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg159(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg160(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg161(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg162(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg163(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg164(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg165(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg166(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg167(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg168(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg169(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg170(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg171(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg172(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg173(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg174(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg175(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg176(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg177(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg178(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg179(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg180(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg181(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg182(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg183(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg184(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg185(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg186(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg187(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg188(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg189(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg190(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg191(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg192(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg193(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg194(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg195(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg196(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg197(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg198(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg199(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg200(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg201(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg202(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg203(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg204(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg205(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg206(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg207(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg208(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg209(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg210(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg211(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg212(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg213(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg214(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg215(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg216(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg217(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg218(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg219(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg220(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg221(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg222(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg223(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg224(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg225(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg226(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg227(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg228(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg229(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg230(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg231(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg232(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg233(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg234(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg235(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg236(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg237(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg238(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg239(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg240(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg241(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg242(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg243(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg244(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg245(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg246(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg247(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg248(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg249(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg250(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg251(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg252(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg253(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg254(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg255(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg256(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg257(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg258(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg259(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg260(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg261(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg262(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg263(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg264(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg265(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg266(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg267(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg268(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg269(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg270(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg271(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg272(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg273(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg274(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg275(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg276(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg277(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg278(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg279(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg280(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg281(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg282(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg283(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg284(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg285(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg286(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg287(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg288(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg289(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg290(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg291(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg292(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg293(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg294(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg295(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg296(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg297(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg298(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg299(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg300(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg301(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg302(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg303(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg304(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg305(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg306(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg307(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg308(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg309(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg310(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg311(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg312(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg313(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg314(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg315(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg316(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg317(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg318(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg319(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg320(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg321(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg322(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg323(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg324(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg325(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg326(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg327(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg328(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg329(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg330(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg331(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg332(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg333(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg334(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg335(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg336(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg337(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg338(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg339(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg340(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg341(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg342(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg343(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg344(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg345(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg346(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg347(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg348(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg349(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg350(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg351(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg352(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg353(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg354(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg355(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg356(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg357(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg358(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg359(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg360(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg361(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg362(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg363(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg364(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg365(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg366(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg367(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg368(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg369(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg370(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg371(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg372(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg373(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg374(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg375(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg376(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg377(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg378(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg379(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg380(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg381(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg382(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg383(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg384(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg385(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg386(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg387(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg388(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg389(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg390(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg391(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg392(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg393(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg394(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg395(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg396(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg397(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg398(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg399(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg400(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg401(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg402(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg403(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg404(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg405(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg406(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg407(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg408(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg409(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg410(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg411(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg412(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg413(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg414(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg415(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg416(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg417(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg418(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg419(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg420(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg421(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg422(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg423(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg424(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg425(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg426(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg427(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg428(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg429(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg430(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg431(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg432(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg433(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg434(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg435(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg436(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg437(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg438(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg439(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg440(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg441(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg442(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg443(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg444(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg445(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg446(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg447(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg448(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg449(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg450(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg451(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg452(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg453(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg454(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg455(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg456(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg457(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg458(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg459(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg460(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg461(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg462(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg463(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg464(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg465(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg466(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg467(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg468(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg469(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg470(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg471(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg472(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg473(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg474(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg475(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg476(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg477(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg478(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg479(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg480(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg481(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg482(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg483(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg484(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg485(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg486(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg487(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg488(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg489(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg490(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg491(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg492(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg493(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg494(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg495(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg496(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg497(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg498(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg499(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg500(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg501(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg502(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg503(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg504(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg505(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg506(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg507(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg508(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg509(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg510(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg511(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg512(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg513(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg514(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg515(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg516(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg517(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg518(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg519(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg520(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg521(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg522(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg523(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg524(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg525(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg526(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg527(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg528(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg529(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg530(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg531(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg532(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg533(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg534(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg535(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg536(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg537(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg538(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg539(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg540(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg541(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg542(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg543(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg544(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg545(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg546(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg547(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg548(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg549(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg550(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg551(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg552(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg553(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg554(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg555(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg556(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg557(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg558(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg559(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg560(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg561(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg562(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg563(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg564(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg565(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg566(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg567(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg568(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg569(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg570(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg571(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg572(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg573(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg574(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg575(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg576(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg577(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg578(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg579(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg580(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg581(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg582(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg583(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg584(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg585(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg586(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg587(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg588(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg589(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg590(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg591(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg592(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg593(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg594(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg595(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg596(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg597(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg598(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg599(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg600(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg601(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg602(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg603(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg604(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg605(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg606(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg607(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg608(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg609(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg610(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg611(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg612(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg613(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg614(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg615(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg616(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg617(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg618(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg619(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg620(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg621(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg622(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg623(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg624(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg625(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg626(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg627(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg628(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg629(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg630(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg631(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg632(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg633(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg634(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg635(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg636(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg637(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg638(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg639(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg640(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg641(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg642(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg643(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg644(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg645(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg646(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg647(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg648(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg649(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg650(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg651(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg652(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg653(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg654(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg655(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg656(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg657(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg658(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg659(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg660(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg661(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg662(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg663(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg664(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg665(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg666(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg667(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg668(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg669(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg670(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg671(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg672(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg673(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg674(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg675(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg676(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg677(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg678(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg679(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg680(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg681(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg682(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg683(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg684(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg685(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg686(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg687(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg688(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg689(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg690(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg691(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg692(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg693(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg694(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg695(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg696(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg697(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg698(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg699(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg700(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg701(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg702(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg703(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg704(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg705(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg706(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg707(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg708(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg709(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg710(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg711(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg712(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg713(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg714(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg715(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg716(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg717(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg718(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg719(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg720(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg721(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg722(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg723(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg724(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg725(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg726(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg727(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg728(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg729(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg730(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg731(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg732(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg733(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg734(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg735(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg736(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg737(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg738(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg739(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg740(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg741(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg742(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg743(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg744(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg745(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg746(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg747(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg748(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg749(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg750(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg751(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg752(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg753(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg754(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg755(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg756(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg757(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg758(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg759(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg760(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg761(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg762(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg763(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg764(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg765(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg766(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg767(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg768(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg769(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg770(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg771(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg772(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg773(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg774(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg775(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg776(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg777(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg778(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg779(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg780(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg781(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg782(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg783(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg784(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg785(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg786(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg787(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg788(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg789(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg790(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg791(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg792(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg793(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg794(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg795(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg796(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg797(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg798(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg799(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg800(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg801(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg802(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg803(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg804(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg805(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg806(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg807(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg808(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg809(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg810(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg811(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg812(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg813(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg814(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg815(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg816(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg817(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg818(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg819(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg820(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg821(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg822(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg823(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg824(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg825(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg826(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg827(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg828(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg829(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg830(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg831(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg832(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg833(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg834(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg835(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg836(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg837(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg838(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg839(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg840(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg841(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg842(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg843(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg844(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg845(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg846(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg847(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg848(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg849(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg850(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg851(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg852(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg853(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg854(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg855(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg856(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg857(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg858(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg859(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg860(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg861(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg862(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg863(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg864(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg865(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg866(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg867(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg868(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg869(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg870(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg871(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg872(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg873(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg874(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg875(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg876(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg877(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg878(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg879(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg880(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg881(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg882(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg883(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg884(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg885(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg886(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg887(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg888(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg889(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg890(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg891(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg892(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg893(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg894(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg895(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg896(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg897(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg898(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg899(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg900(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg901(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg902(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg903(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg904(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg905(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg906(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg907(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg908(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg909(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg910(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg911(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg912(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg913(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg914(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg915(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg916(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg917(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg918(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg919(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg920(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg921(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg922(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg923(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg924(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg925(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg926(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg927(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg928(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg929(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg930(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg931(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg932(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg933(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg934(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg935(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg936(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg937(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg938(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg939(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg940(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg941(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg942(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg943(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg944(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg945(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg946(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg947(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg948(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg949(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg950(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg951(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg952(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg953(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg954(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg955(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg956(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg957(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg958(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg959(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg960(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg961(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg962(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg963(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg964(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg965(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg966(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg967(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg968(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg969(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg970(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg971(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg972(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg973(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg974(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg975(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg976(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg977(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg978(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg979(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg980(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg981(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg982(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg983(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg984(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg985(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg986(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg987(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg988(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg989(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg990(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg991(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg992(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg993(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg994(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg995(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg996(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg997(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg998(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg999(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1000(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1001(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1002(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1003(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1004(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1005(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1006(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1007(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1008(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1009(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1010(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1011(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1012(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1013(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1014(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1015(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1016(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1017(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1018(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1019(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1020(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1021(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1022(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1023(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1024(b, min, max).
null(0) :- B(a, min, max) .
B(a, min, max) :- A(a, b) GROUP-BY(a) agg1025(b, min, max).
null(0) :- B(a, min, max) .

