%table A22(a:int4, b:int4)
%table B22(a:int4, min:int4, max:int4)


%aggregate A22agg001(a: int4->min: int4, max: int4) {}
%aggregate A22agg002(a: int4->min: int4, max: int4) {}
%aggregate A22agg003(a: int4->min: int4, max: int4) {}
%aggregate A22agg004(a: int4->min: int4, max: int4) {}
%aggregate A22agg005(a: int4->min: int4, max: int4) {}
%aggregate A22agg006(a: int4->min: int4, max: int4) {}
%aggregate A22agg007(a: int4->min: int4, max: int4) {}
%aggregate A22agg008(a: int4->min: int4, max: int4) {}
%aggregate A22agg009(a: int4->min: int4, max: int4) {}
%aggregate A22agg010(a: int4->min: int4, max: int4) {}
%aggregate A22agg011(a: int4->min: int4, max: int4) {}
%aggregate A22agg012(a: int4->min: int4, max: int4) {}
%aggregate A22agg013(a: int4->min: int4, max: int4) {}
%aggregate A22agg014(a: int4->min: int4, max: int4) {}
%aggregate A22agg015(a: int4->min: int4, max: int4) {}
%aggregate A22agg016(a: int4->min: int4, max: int4) {}




B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg001(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg002(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg003(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg004(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg005(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg006(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg007(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg008(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg009(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg010(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg011(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg012(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg013(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg014(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg015(b, min, max).
null(0) :- B22(a, min, max) .
B22(a, min, max) :- A22(a, b) GROUP-BY(a) A22agg016(b, min, max).
null(0) :- B22(a, min, max) .

