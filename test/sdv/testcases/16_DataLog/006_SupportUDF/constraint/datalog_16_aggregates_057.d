%table A57(a:int4, b:int4)
%table B57(a:int4, min:int4, max:int4)


%aggregate A57agg001(a: int4->min: int4, max: int4) {}
%aggregate A57agg002(a: int4->min: int4, max: int4) {}
%aggregate A57agg003(a: int4->min: int4, max: int4) {}
%aggregate A57agg004(a: int4->min: int4, max: int4) {}
%aggregate A57agg005(a: int4->min: int4, max: int4) {}
%aggregate A57agg006(a: int4->min: int4, max: int4) {}
%aggregate A57agg007(a: int4->min: int4, max: int4) {}
%aggregate A57agg008(a: int4->min: int4, max: int4) {}
%aggregate A57agg009(a: int4->min: int4, max: int4) {}
%aggregate A57agg010(a: int4->min: int4, max: int4) {}
%aggregate A57agg011(a: int4->min: int4, max: int4) {}
%aggregate A57agg012(a: int4->min: int4, max: int4) {}
%aggregate A57agg013(a: int4->min: int4, max: int4) {}
%aggregate A57agg014(a: int4->min: int4, max: int4) {}
%aggregate A57agg015(a: int4->min: int4, max: int4) {}
%aggregate A57agg016(a: int4->min: int4, max: int4) {}




B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg001(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg002(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg003(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg004(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg005(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg006(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg007(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg008(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg009(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg010(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg011(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg012(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg013(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg014(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg015(b, min, max).
null(0) :- B57(a, min, max) .
B57(a, min, max) :- A57(a, b) GROUP-BY(a) A57agg016(b, min, max).
null(0) :- B57(a, min, max) .

