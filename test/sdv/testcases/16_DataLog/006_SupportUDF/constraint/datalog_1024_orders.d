%table A(a:int4, b:int4)
%table C(a:int4, min:int4, max:int4)

%aggregate agg001(a: int4->min: int4, max: int4) {ordered}
%aggregate agg002(a: int4->min: int4, max: int4) {ordered}
%aggregate agg003(a: int4->min: int4, max: int4) {ordered}
%aggregate agg004(a: int4->min: int4, max: int4) {ordered}
%aggregate agg005(a: int4->min: int4, max: int4) {ordered}
%aggregate agg006(a: int4->min: int4, max: int4) {ordered}
%aggregate agg007(a: int4->min: int4, max: int4) {ordered}
%aggregate agg008(a: int4->min: int4, max: int4) {ordered}
%aggregate agg009(a: int4->min: int4, max: int4) {ordered}
%aggregate agg010(a: int4->min: int4, max: int4) {ordered}
%aggregate agg011(a: int4->min: int4, max: int4) {ordered}
%aggregate agg012(a: int4->min: int4, max: int4) {ordered}
%aggregate agg013(a: int4->min: int4, max: int4) {ordered}
%aggregate agg014(a: int4->min: int4, max: int4) {ordered}
%aggregate agg015(a: int4->min: int4, max: int4) {ordered}
%aggregate agg016(a: int4->min: int4, max: int4) {ordered}
%aggregate agg017(a: int4->min: int4, max: int4) {ordered}
%aggregate agg018(a: int4->min: int4, max: int4) {ordered}
%aggregate agg019(a: int4->min: int4, max: int4) {ordered}
%aggregate agg020(a: int4->min: int4, max: int4) {ordered}
%aggregate agg021(a: int4->min: int4, max: int4) {ordered}
%aggregate agg022(a: int4->min: int4, max: int4) {ordered}
%aggregate agg023(a: int4->min: int4, max: int4) {ordered}
%aggregate agg024(a: int4->min: int4, max: int4) {ordered}
%aggregate agg025(a: int4->min: int4, max: int4) {ordered}
%aggregate agg026(a: int4->min: int4, max: int4) {ordered}
%aggregate agg027(a: int4->min: int4, max: int4) {ordered}
%aggregate agg028(a: int4->min: int4, max: int4) {ordered}
%aggregate agg029(a: int4->min: int4, max: int4) {ordered}
%aggregate agg030(a: int4->min: int4, max: int4) {ordered}
%aggregate agg031(a: int4->min: int4, max: int4) {ordered}
%aggregate agg032(a: int4->min: int4, max: int4) {ordered}
%aggregate agg033(a: int4->min: int4, max: int4) {ordered}
%aggregate agg034(a: int4->min: int4, max: int4) {ordered}
%aggregate agg035(a: int4->min: int4, max: int4) {ordered}
%aggregate agg036(a: int4->min: int4, max: int4) {ordered}
%aggregate agg037(a: int4->min: int4, max: int4) {ordered}
%aggregate agg038(a: int4->min: int4, max: int4) {ordered}
%aggregate agg039(a: int4->min: int4, max: int4) {ordered}
%aggregate agg040(a: int4->min: int4, max: int4) {ordered}
%aggregate agg041(a: int4->min: int4, max: int4) {ordered}
%aggregate agg042(a: int4->min: int4, max: int4) {ordered}
%aggregate agg043(a: int4->min: int4, max: int4) {ordered}
%aggregate agg044(a: int4->min: int4, max: int4) {ordered}
%aggregate agg045(a: int4->min: int4, max: int4) {ordered}
%aggregate agg046(a: int4->min: int4, max: int4) {ordered}
%aggregate agg047(a: int4->min: int4, max: int4) {ordered}
%aggregate agg048(a: int4->min: int4, max: int4) {ordered}
%aggregate agg049(a: int4->min: int4, max: int4) {ordered}
%aggregate agg050(a: int4->min: int4, max: int4) {ordered}
%aggregate agg051(a: int4->min: int4, max: int4) {ordered}
%aggregate agg052(a: int4->min: int4, max: int4) {ordered}
%aggregate agg053(a: int4->min: int4, max: int4) {ordered}
%aggregate agg054(a: int4->min: int4, max: int4) {ordered}
%aggregate agg055(a: int4->min: int4, max: int4) {ordered}
%aggregate agg056(a: int4->min: int4, max: int4) {ordered}
%aggregate agg057(a: int4->min: int4, max: int4) {ordered}
%aggregate agg058(a: int4->min: int4, max: int4) {ordered}
%aggregate agg059(a: int4->min: int4, max: int4) {ordered}
%aggregate agg060(a: int4->min: int4, max: int4) {ordered}
%aggregate agg061(a: int4->min: int4, max: int4) {ordered}
%aggregate agg062(a: int4->min: int4, max: int4) {ordered}
%aggregate agg063(a: int4->min: int4, max: int4) {ordered}
%aggregate agg064(a: int4->min: int4, max: int4) {ordered}
%aggregate agg065(a: int4->min: int4, max: int4) {ordered}
%aggregate agg066(a: int4->min: int4, max: int4) {ordered}
%aggregate agg067(a: int4->min: int4, max: int4) {ordered}
%aggregate agg068(a: int4->min: int4, max: int4) {ordered}
%aggregate agg069(a: int4->min: int4, max: int4) {ordered}
%aggregate agg070(a: int4->min: int4, max: int4) {ordered}
%aggregate agg071(a: int4->min: int4, max: int4) {ordered}
%aggregate agg072(a: int4->min: int4, max: int4) {ordered}
%aggregate agg073(a: int4->min: int4, max: int4) {ordered}
%aggregate agg074(a: int4->min: int4, max: int4) {ordered}
%aggregate agg075(a: int4->min: int4, max: int4) {ordered}
%aggregate agg076(a: int4->min: int4, max: int4) {ordered}
%aggregate agg077(a: int4->min: int4, max: int4) {ordered}
%aggregate agg078(a: int4->min: int4, max: int4) {ordered}
%aggregate agg079(a: int4->min: int4, max: int4) {ordered}
%aggregate agg080(a: int4->min: int4, max: int4) {ordered}
%aggregate agg081(a: int4->min: int4, max: int4) {ordered}
%aggregate agg082(a: int4->min: int4, max: int4) {ordered}
%aggregate agg083(a: int4->min: int4, max: int4) {ordered}
%aggregate agg084(a: int4->min: int4, max: int4) {ordered}
%aggregate agg085(a: int4->min: int4, max: int4) {ordered}
%aggregate agg086(a: int4->min: int4, max: int4) {ordered}
%aggregate agg087(a: int4->min: int4, max: int4) {ordered}
%aggregate agg088(a: int4->min: int4, max: int4) {ordered}
%aggregate agg089(a: int4->min: int4, max: int4) {ordered}
%aggregate agg090(a: int4->min: int4, max: int4) {ordered}
%aggregate agg091(a: int4->min: int4, max: int4) {ordered}
%aggregate agg092(a: int4->min: int4, max: int4) {ordered}
%aggregate agg093(a: int4->min: int4, max: int4) {ordered}
%aggregate agg094(a: int4->min: int4, max: int4) {ordered}
%aggregate agg095(a: int4->min: int4, max: int4) {ordered}
%aggregate agg096(a: int4->min: int4, max: int4) {ordered}
%aggregate agg097(a: int4->min: int4, max: int4) {ordered}
%aggregate agg098(a: int4->min: int4, max: int4) {ordered}
%aggregate agg099(a: int4->min: int4, max: int4) {ordered}
%aggregate agg100(a: int4->min: int4, max: int4) {ordered}
%aggregate agg101(a: int4->min: int4, max: int4) {ordered}
%aggregate agg102(a: int4->min: int4, max: int4) {ordered}
%aggregate agg103(a: int4->min: int4, max: int4) {ordered}
%aggregate agg104(a: int4->min: int4, max: int4) {ordered}
%aggregate agg105(a: int4->min: int4, max: int4) {ordered}
%aggregate agg106(a: int4->min: int4, max: int4) {ordered}
%aggregate agg107(a: int4->min: int4, max: int4) {ordered}
%aggregate agg108(a: int4->min: int4, max: int4) {ordered}
%aggregate agg109(a: int4->min: int4, max: int4) {ordered}
%aggregate agg110(a: int4->min: int4, max: int4) {ordered}
%aggregate agg111(a: int4->min: int4, max: int4) {ordered}
%aggregate agg112(a: int4->min: int4, max: int4) {ordered}
%aggregate agg113(a: int4->min: int4, max: int4) {ordered}
%aggregate agg114(a: int4->min: int4, max: int4) {ordered}
%aggregate agg115(a: int4->min: int4, max: int4) {ordered}
%aggregate agg116(a: int4->min: int4, max: int4) {ordered}
%aggregate agg117(a: int4->min: int4, max: int4) {ordered}
%aggregate agg118(a: int4->min: int4, max: int4) {ordered}
%aggregate agg119(a: int4->min: int4, max: int4) {ordered}
%aggregate agg120(a: int4->min: int4, max: int4) {ordered}
%aggregate agg121(a: int4->min: int4, max: int4) {ordered}
%aggregate agg122(a: int4->min: int4, max: int4) {ordered}
%aggregate agg123(a: int4->min: int4, max: int4) {ordered}
%aggregate agg124(a: int4->min: int4, max: int4) {ordered}
%aggregate agg125(a: int4->min: int4, max: int4) {ordered}
%aggregate agg126(a: int4->min: int4, max: int4) {ordered}
%aggregate agg127(a: int4->min: int4, max: int4) {ordered}
%aggregate agg128(a: int4->min: int4, max: int4) {ordered}
%aggregate agg129(a: int4->min: int4, max: int4) {ordered}
%aggregate agg130(a: int4->min: int4, max: int4) {ordered}
%aggregate agg131(a: int4->min: int4, max: int4) {ordered}
%aggregate agg132(a: int4->min: int4, max: int4) {ordered}
%aggregate agg133(a: int4->min: int4, max: int4) {ordered}
%aggregate agg134(a: int4->min: int4, max: int4) {ordered}
%aggregate agg135(a: int4->min: int4, max: int4) {ordered}
%aggregate agg136(a: int4->min: int4, max: int4) {ordered}
%aggregate agg137(a: int4->min: int4, max: int4) {ordered}
%aggregate agg138(a: int4->min: int4, max: int4) {ordered}
%aggregate agg139(a: int4->min: int4, max: int4) {ordered}
%aggregate agg140(a: int4->min: int4, max: int4) {ordered}
%aggregate agg141(a: int4->min: int4, max: int4) {ordered}
%aggregate agg142(a: int4->min: int4, max: int4) {ordered}
%aggregate agg143(a: int4->min: int4, max: int4) {ordered}
%aggregate agg144(a: int4->min: int4, max: int4) {ordered}
%aggregate agg145(a: int4->min: int4, max: int4) {ordered}
%aggregate agg146(a: int4->min: int4, max: int4) {ordered}
%aggregate agg147(a: int4->min: int4, max: int4) {ordered}
%aggregate agg148(a: int4->min: int4, max: int4) {ordered}
%aggregate agg149(a: int4->min: int4, max: int4) {ordered}
%aggregate agg150(a: int4->min: int4, max: int4) {ordered}
%aggregate agg151(a: int4->min: int4, max: int4) {ordered}
%aggregate agg152(a: int4->min: int4, max: int4) {ordered}
%aggregate agg153(a: int4->min: int4, max: int4) {ordered}
%aggregate agg154(a: int4->min: int4, max: int4) {ordered}
%aggregate agg155(a: int4->min: int4, max: int4) {ordered}
%aggregate agg156(a: int4->min: int4, max: int4) {ordered}
%aggregate agg157(a: int4->min: int4, max: int4) {ordered}
%aggregate agg158(a: int4->min: int4, max: int4) {ordered}
%aggregate agg159(a: int4->min: int4, max: int4) {ordered}
%aggregate agg160(a: int4->min: int4, max: int4) {ordered}
%aggregate agg161(a: int4->min: int4, max: int4) {ordered}
%aggregate agg162(a: int4->min: int4, max: int4) {ordered}
%aggregate agg163(a: int4->min: int4, max: int4) {ordered}
%aggregate agg164(a: int4->min: int4, max: int4) {ordered}
%aggregate agg165(a: int4->min: int4, max: int4) {ordered}
%aggregate agg166(a: int4->min: int4, max: int4) {ordered}
%aggregate agg167(a: int4->min: int4, max: int4) {ordered}
%aggregate agg168(a: int4->min: int4, max: int4) {ordered}
%aggregate agg169(a: int4->min: int4, max: int4) {ordered}
%aggregate agg170(a: int4->min: int4, max: int4) {ordered}
%aggregate agg171(a: int4->min: int4, max: int4) {ordered}
%aggregate agg172(a: int4->min: int4, max: int4) {ordered}
%aggregate agg173(a: int4->min: int4, max: int4) {ordered}
%aggregate agg174(a: int4->min: int4, max: int4) {ordered}
%aggregate agg175(a: int4->min: int4, max: int4) {ordered}
%aggregate agg176(a: int4->min: int4, max: int4) {ordered}
%aggregate agg177(a: int4->min: int4, max: int4) {ordered}
%aggregate agg178(a: int4->min: int4, max: int4) {ordered}
%aggregate agg179(a: int4->min: int4, max: int4) {ordered}
%aggregate agg180(a: int4->min: int4, max: int4) {ordered}
%aggregate agg181(a: int4->min: int4, max: int4) {ordered}
%aggregate agg182(a: int4->min: int4, max: int4) {ordered}
%aggregate agg183(a: int4->min: int4, max: int4) {ordered}
%aggregate agg184(a: int4->min: int4, max: int4) {ordered}
%aggregate agg185(a: int4->min: int4, max: int4) {ordered}
%aggregate agg186(a: int4->min: int4, max: int4) {ordered}
%aggregate agg187(a: int4->min: int4, max: int4) {ordered}
%aggregate agg188(a: int4->min: int4, max: int4) {ordered}
%aggregate agg189(a: int4->min: int4, max: int4) {ordered}
%aggregate agg190(a: int4->min: int4, max: int4) {ordered}
%aggregate agg191(a: int4->min: int4, max: int4) {ordered}
%aggregate agg192(a: int4->min: int4, max: int4) {ordered}
%aggregate agg193(a: int4->min: int4, max: int4) {ordered}
%aggregate agg194(a: int4->min: int4, max: int4) {ordered}
%aggregate agg195(a: int4->min: int4, max: int4) {ordered}
%aggregate agg196(a: int4->min: int4, max: int4) {ordered}
%aggregate agg197(a: int4->min: int4, max: int4) {ordered}
%aggregate agg198(a: int4->min: int4, max: int4) {ordered}
%aggregate agg199(a: int4->min: int4, max: int4) {ordered}
%aggregate agg200(a: int4->min: int4, max: int4) {ordered}
%aggregate agg201(a: int4->min: int4, max: int4) {ordered}
%aggregate agg202(a: int4->min: int4, max: int4) {ordered}
%aggregate agg203(a: int4->min: int4, max: int4) {ordered}
%aggregate agg204(a: int4->min: int4, max: int4) {ordered}
%aggregate agg205(a: int4->min: int4, max: int4) {ordered}
%aggregate agg206(a: int4->min: int4, max: int4) {ordered}
%aggregate agg207(a: int4->min: int4, max: int4) {ordered}
%aggregate agg208(a: int4->min: int4, max: int4) {ordered}
%aggregate agg209(a: int4->min: int4, max: int4) {ordered}
%aggregate agg210(a: int4->min: int4, max: int4) {ordered}
%aggregate agg211(a: int4->min: int4, max: int4) {ordered}
%aggregate agg212(a: int4->min: int4, max: int4) {ordered}
%aggregate agg213(a: int4->min: int4, max: int4) {ordered}
%aggregate agg214(a: int4->min: int4, max: int4) {ordered}
%aggregate agg215(a: int4->min: int4, max: int4) {ordered}
%aggregate agg216(a: int4->min: int4, max: int4) {ordered}
%aggregate agg217(a: int4->min: int4, max: int4) {ordered}
%aggregate agg218(a: int4->min: int4, max: int4) {ordered}
%aggregate agg219(a: int4->min: int4, max: int4) {ordered}
%aggregate agg220(a: int4->min: int4, max: int4) {ordered}
%aggregate agg221(a: int4->min: int4, max: int4) {ordered}
%aggregate agg222(a: int4->min: int4, max: int4) {ordered}
%aggregate agg223(a: int4->min: int4, max: int4) {ordered}
%aggregate agg224(a: int4->min: int4, max: int4) {ordered}
%aggregate agg225(a: int4->min: int4, max: int4) {ordered}
%aggregate agg226(a: int4->min: int4, max: int4) {ordered}
%aggregate agg227(a: int4->min: int4, max: int4) {ordered}
%aggregate agg228(a: int4->min: int4, max: int4) {ordered}
%aggregate agg229(a: int4->min: int4, max: int4) {ordered}
%aggregate agg230(a: int4->min: int4, max: int4) {ordered}
%aggregate agg231(a: int4->min: int4, max: int4) {ordered}
%aggregate agg232(a: int4->min: int4, max: int4) {ordered}
%aggregate agg233(a: int4->min: int4, max: int4) {ordered}
%aggregate agg234(a: int4->min: int4, max: int4) {ordered}
%aggregate agg235(a: int4->min: int4, max: int4) {ordered}
%aggregate agg236(a: int4->min: int4, max: int4) {ordered}
%aggregate agg237(a: int4->min: int4, max: int4) {ordered}
%aggregate agg238(a: int4->min: int4, max: int4) {ordered}
%aggregate agg239(a: int4->min: int4, max: int4) {ordered}
%aggregate agg240(a: int4->min: int4, max: int4) {ordered}
%aggregate agg241(a: int4->min: int4, max: int4) {ordered}
%aggregate agg242(a: int4->min: int4, max: int4) {ordered}
%aggregate agg243(a: int4->min: int4, max: int4) {ordered}
%aggregate agg244(a: int4->min: int4, max: int4) {ordered}
%aggregate agg245(a: int4->min: int4, max: int4) {ordered}
%aggregate agg246(a: int4->min: int4, max: int4) {ordered}
%aggregate agg247(a: int4->min: int4, max: int4) {ordered}
%aggregate agg248(a: int4->min: int4, max: int4) {ordered}
%aggregate agg249(a: int4->min: int4, max: int4) {ordered}
%aggregate agg250(a: int4->min: int4, max: int4) {ordered}
%aggregate agg251(a: int4->min: int4, max: int4) {ordered}
%aggregate agg252(a: int4->min: int4, max: int4) {ordered}
%aggregate agg253(a: int4->min: int4, max: int4) {ordered}
%aggregate agg254(a: int4->min: int4, max: int4) {ordered}
%aggregate agg255(a: int4->min: int4, max: int4) {ordered}
%aggregate agg256(a: int4->min: int4, max: int4) {ordered}
%aggregate agg257(a: int4->min: int4, max: int4) {ordered}
%aggregate agg258(a: int4->min: int4, max: int4) {ordered}
%aggregate agg259(a: int4->min: int4, max: int4) {ordered}
%aggregate agg260(a: int4->min: int4, max: int4) {ordered}
%aggregate agg261(a: int4->min: int4, max: int4) {ordered}
%aggregate agg262(a: int4->min: int4, max: int4) {ordered}
%aggregate agg263(a: int4->min: int4, max: int4) {ordered}
%aggregate agg264(a: int4->min: int4, max: int4) {ordered}
%aggregate agg265(a: int4->min: int4, max: int4) {ordered}
%aggregate agg266(a: int4->min: int4, max: int4) {ordered}
%aggregate agg267(a: int4->min: int4, max: int4) {ordered}
%aggregate agg268(a: int4->min: int4, max: int4) {ordered}
%aggregate agg269(a: int4->min: int4, max: int4) {ordered}
%aggregate agg270(a: int4->min: int4, max: int4) {ordered}
%aggregate agg271(a: int4->min: int4, max: int4) {ordered}
%aggregate agg272(a: int4->min: int4, max: int4) {ordered}
%aggregate agg273(a: int4->min: int4, max: int4) {ordered}
%aggregate agg274(a: int4->min: int4, max: int4) {ordered}
%aggregate agg275(a: int4->min: int4, max: int4) {ordered}
%aggregate agg276(a: int4->min: int4, max: int4) {ordered}
%aggregate agg277(a: int4->min: int4, max: int4) {ordered}
%aggregate agg278(a: int4->min: int4, max: int4) {ordered}
%aggregate agg279(a: int4->min: int4, max: int4) {ordered}
%aggregate agg280(a: int4->min: int4, max: int4) {ordered}
%aggregate agg281(a: int4->min: int4, max: int4) {ordered}
%aggregate agg282(a: int4->min: int4, max: int4) {ordered}
%aggregate agg283(a: int4->min: int4, max: int4) {ordered}
%aggregate agg284(a: int4->min: int4, max: int4) {ordered}
%aggregate agg285(a: int4->min: int4, max: int4) {ordered}
%aggregate agg286(a: int4->min: int4, max: int4) {ordered}
%aggregate agg287(a: int4->min: int4, max: int4) {ordered}
%aggregate agg288(a: int4->min: int4, max: int4) {ordered}
%aggregate agg289(a: int4->min: int4, max: int4) {ordered}
%aggregate agg290(a: int4->min: int4, max: int4) {ordered}
%aggregate agg291(a: int4->min: int4, max: int4) {ordered}
%aggregate agg292(a: int4->min: int4, max: int4) {ordered}
%aggregate agg293(a: int4->min: int4, max: int4) {ordered}
%aggregate agg294(a: int4->min: int4, max: int4) {ordered}
%aggregate agg295(a: int4->min: int4, max: int4) {ordered}
%aggregate agg296(a: int4->min: int4, max: int4) {ordered}
%aggregate agg297(a: int4->min: int4, max: int4) {ordered}
%aggregate agg298(a: int4->min: int4, max: int4) {ordered}
%aggregate agg299(a: int4->min: int4, max: int4) {ordered}
%aggregate agg300(a: int4->min: int4, max: int4) {ordered}
%aggregate agg301(a: int4->min: int4, max: int4) {ordered}
%aggregate agg302(a: int4->min: int4, max: int4) {ordered}
%aggregate agg303(a: int4->min: int4, max: int4) {ordered}
%aggregate agg304(a: int4->min: int4, max: int4) {ordered}
%aggregate agg305(a: int4->min: int4, max: int4) {ordered}
%aggregate agg306(a: int4->min: int4, max: int4) {ordered}
%aggregate agg307(a: int4->min: int4, max: int4) {ordered}
%aggregate agg308(a: int4->min: int4, max: int4) {ordered}
%aggregate agg309(a: int4->min: int4, max: int4) {ordered}
%aggregate agg310(a: int4->min: int4, max: int4) {ordered}
%aggregate agg311(a: int4->min: int4, max: int4) {ordered}
%aggregate agg312(a: int4->min: int4, max: int4) {ordered}
%aggregate agg313(a: int4->min: int4, max: int4) {ordered}
%aggregate agg314(a: int4->min: int4, max: int4) {ordered}
%aggregate agg315(a: int4->min: int4, max: int4) {ordered}
%aggregate agg316(a: int4->min: int4, max: int4) {ordered}
%aggregate agg317(a: int4->min: int4, max: int4) {ordered}
%aggregate agg318(a: int4->min: int4, max: int4) {ordered}
%aggregate agg319(a: int4->min: int4, max: int4) {ordered}
%aggregate agg320(a: int4->min: int4, max: int4) {ordered}
%aggregate agg321(a: int4->min: int4, max: int4) {ordered}
%aggregate agg322(a: int4->min: int4, max: int4) {ordered}
%aggregate agg323(a: int4->min: int4, max: int4) {ordered}
%aggregate agg324(a: int4->min: int4, max: int4) {ordered}
%aggregate agg325(a: int4->min: int4, max: int4) {ordered}
%aggregate agg326(a: int4->min: int4, max: int4) {ordered}
%aggregate agg327(a: int4->min: int4, max: int4) {ordered}
%aggregate agg328(a: int4->min: int4, max: int4) {ordered}
%aggregate agg329(a: int4->min: int4, max: int4) {ordered}
%aggregate agg330(a: int4->min: int4, max: int4) {ordered}
%aggregate agg331(a: int4->min: int4, max: int4) {ordered}
%aggregate agg332(a: int4->min: int4, max: int4) {ordered}
%aggregate agg333(a: int4->min: int4, max: int4) {ordered}
%aggregate agg334(a: int4->min: int4, max: int4) {ordered}
%aggregate agg335(a: int4->min: int4, max: int4) {ordered}
%aggregate agg336(a: int4->min: int4, max: int4) {ordered}
%aggregate agg337(a: int4->min: int4, max: int4) {ordered}
%aggregate agg338(a: int4->min: int4, max: int4) {ordered}
%aggregate agg339(a: int4->min: int4, max: int4) {ordered}
%aggregate agg340(a: int4->min: int4, max: int4) {ordered}
%aggregate agg341(a: int4->min: int4, max: int4) {ordered}
%aggregate agg342(a: int4->min: int4, max: int4) {ordered}
%aggregate agg343(a: int4->min: int4, max: int4) {ordered}
%aggregate agg344(a: int4->min: int4, max: int4) {ordered}
%aggregate agg345(a: int4->min: int4, max: int4) {ordered}
%aggregate agg346(a: int4->min: int4, max: int4) {ordered}
%aggregate agg347(a: int4->min: int4, max: int4) {ordered}
%aggregate agg348(a: int4->min: int4, max: int4) {ordered}
%aggregate agg349(a: int4->min: int4, max: int4) {ordered}
%aggregate agg350(a: int4->min: int4, max: int4) {ordered}
%aggregate agg351(a: int4->min: int4, max: int4) {ordered}
%aggregate agg352(a: int4->min: int4, max: int4) {ordered}
%aggregate agg353(a: int4->min: int4, max: int4) {ordered}
%aggregate agg354(a: int4->min: int4, max: int4) {ordered}
%aggregate agg355(a: int4->min: int4, max: int4) {ordered}
%aggregate agg356(a: int4->min: int4, max: int4) {ordered}
%aggregate agg357(a: int4->min: int4, max: int4) {ordered}
%aggregate agg358(a: int4->min: int4, max: int4) {ordered}
%aggregate agg359(a: int4->min: int4, max: int4) {ordered}
%aggregate agg360(a: int4->min: int4, max: int4) {ordered}
%aggregate agg361(a: int4->min: int4, max: int4) {ordered}
%aggregate agg362(a: int4->min: int4, max: int4) {ordered}
%aggregate agg363(a: int4->min: int4, max: int4) {ordered}
%aggregate agg364(a: int4->min: int4, max: int4) {ordered}
%aggregate agg365(a: int4->min: int4, max: int4) {ordered}
%aggregate agg366(a: int4->min: int4, max: int4) {ordered}
%aggregate agg367(a: int4->min: int4, max: int4) {ordered}
%aggregate agg368(a: int4->min: int4, max: int4) {ordered}
%aggregate agg369(a: int4->min: int4, max: int4) {ordered}
%aggregate agg370(a: int4->min: int4, max: int4) {ordered}
%aggregate agg371(a: int4->min: int4, max: int4) {ordered}
%aggregate agg372(a: int4->min: int4, max: int4) {ordered}
%aggregate agg373(a: int4->min: int4, max: int4) {ordered}
%aggregate agg374(a: int4->min: int4, max: int4) {ordered}
%aggregate agg375(a: int4->min: int4, max: int4) {ordered}
%aggregate agg376(a: int4->min: int4, max: int4) {ordered}
%aggregate agg377(a: int4->min: int4, max: int4) {ordered}
%aggregate agg378(a: int4->min: int4, max: int4) {ordered}
%aggregate agg379(a: int4->min: int4, max: int4) {ordered}
%aggregate agg380(a: int4->min: int4, max: int4) {ordered}
%aggregate agg381(a: int4->min: int4, max: int4) {ordered}
%aggregate agg382(a: int4->min: int4, max: int4) {ordered}
%aggregate agg383(a: int4->min: int4, max: int4) {ordered}
%aggregate agg384(a: int4->min: int4, max: int4) {ordered}
%aggregate agg385(a: int4->min: int4, max: int4) {ordered}
%aggregate agg386(a: int4->min: int4, max: int4) {ordered}
%aggregate agg387(a: int4->min: int4, max: int4) {ordered}
%aggregate agg388(a: int4->min: int4, max: int4) {ordered}
%aggregate agg389(a: int4->min: int4, max: int4) {ordered}
%aggregate agg390(a: int4->min: int4, max: int4) {ordered}
%aggregate agg391(a: int4->min: int4, max: int4) {ordered}
%aggregate agg392(a: int4->min: int4, max: int4) {ordered}
%aggregate agg393(a: int4->min: int4, max: int4) {ordered}
%aggregate agg394(a: int4->min: int4, max: int4) {ordered}
%aggregate agg395(a: int4->min: int4, max: int4) {ordered}
%aggregate agg396(a: int4->min: int4, max: int4) {ordered}
%aggregate agg397(a: int4->min: int4, max: int4) {ordered}
%aggregate agg398(a: int4->min: int4, max: int4) {ordered}
%aggregate agg399(a: int4->min: int4, max: int4) {ordered}
%aggregate agg400(a: int4->min: int4, max: int4) {ordered}
%aggregate agg401(a: int4->min: int4, max: int4) {ordered}
%aggregate agg402(a: int4->min: int4, max: int4) {ordered}
%aggregate agg403(a: int4->min: int4, max: int4) {ordered}
%aggregate agg404(a: int4->min: int4, max: int4) {ordered}
%aggregate agg405(a: int4->min: int4, max: int4) {ordered}
%aggregate agg406(a: int4->min: int4, max: int4) {ordered}
%aggregate agg407(a: int4->min: int4, max: int4) {ordered}
%aggregate agg408(a: int4->min: int4, max: int4) {ordered}
%aggregate agg409(a: int4->min: int4, max: int4) {ordered}
%aggregate agg410(a: int4->min: int4, max: int4) {ordered}
%aggregate agg411(a: int4->min: int4, max: int4) {ordered}
%aggregate agg412(a: int4->min: int4, max: int4) {ordered}
%aggregate agg413(a: int4->min: int4, max: int4) {ordered}
%aggregate agg414(a: int4->min: int4, max: int4) {ordered}
%aggregate agg415(a: int4->min: int4, max: int4) {ordered}
%aggregate agg416(a: int4->min: int4, max: int4) {ordered}
%aggregate agg417(a: int4->min: int4, max: int4) {ordered}
%aggregate agg418(a: int4->min: int4, max: int4) {ordered}
%aggregate agg419(a: int4->min: int4, max: int4) {ordered}
%aggregate agg420(a: int4->min: int4, max: int4) {ordered}
%aggregate agg421(a: int4->min: int4, max: int4) {ordered}
%aggregate agg422(a: int4->min: int4, max: int4) {ordered}
%aggregate agg423(a: int4->min: int4, max: int4) {ordered}
%aggregate agg424(a: int4->min: int4, max: int4) {ordered}
%aggregate agg425(a: int4->min: int4, max: int4) {ordered}
%aggregate agg426(a: int4->min: int4, max: int4) {ordered}
%aggregate agg427(a: int4->min: int4, max: int4) {ordered}
%aggregate agg428(a: int4->min: int4, max: int4) {ordered}
%aggregate agg429(a: int4->min: int4, max: int4) {ordered}
%aggregate agg430(a: int4->min: int4, max: int4) {ordered}
%aggregate agg431(a: int4->min: int4, max: int4) {ordered}
%aggregate agg432(a: int4->min: int4, max: int4) {ordered}
%aggregate agg433(a: int4->min: int4, max: int4) {ordered}
%aggregate agg434(a: int4->min: int4, max: int4) {ordered}
%aggregate agg435(a: int4->min: int4, max: int4) {ordered}
%aggregate agg436(a: int4->min: int4, max: int4) {ordered}
%aggregate agg437(a: int4->min: int4, max: int4) {ordered}
%aggregate agg438(a: int4->min: int4, max: int4) {ordered}
%aggregate agg439(a: int4->min: int4, max: int4) {ordered}
%aggregate agg440(a: int4->min: int4, max: int4) {ordered}
%aggregate agg441(a: int4->min: int4, max: int4) {ordered}
%aggregate agg442(a: int4->min: int4, max: int4) {ordered}
%aggregate agg443(a: int4->min: int4, max: int4) {ordered}
%aggregate agg444(a: int4->min: int4, max: int4) {ordered}
%aggregate agg445(a: int4->min: int4, max: int4) {ordered}
%aggregate agg446(a: int4->min: int4, max: int4) {ordered}
%aggregate agg447(a: int4->min: int4, max: int4) {ordered}
%aggregate agg448(a: int4->min: int4, max: int4) {ordered}
%aggregate agg449(a: int4->min: int4, max: int4) {ordered}
%aggregate agg450(a: int4->min: int4, max: int4) {ordered}
%aggregate agg451(a: int4->min: int4, max: int4) {ordered}
%aggregate agg452(a: int4->min: int4, max: int4) {ordered}
%aggregate agg453(a: int4->min: int4, max: int4) {ordered}
%aggregate agg454(a: int4->min: int4, max: int4) {ordered}
%aggregate agg455(a: int4->min: int4, max: int4) {ordered}
%aggregate agg456(a: int4->min: int4, max: int4) {ordered}
%aggregate agg457(a: int4->min: int4, max: int4) {ordered}
%aggregate agg458(a: int4->min: int4, max: int4) {ordered}
%aggregate agg459(a: int4->min: int4, max: int4) {ordered}
%aggregate agg460(a: int4->min: int4, max: int4) {ordered}
%aggregate agg461(a: int4->min: int4, max: int4) {ordered}
%aggregate agg462(a: int4->min: int4, max: int4) {ordered}
%aggregate agg463(a: int4->min: int4, max: int4) {ordered}
%aggregate agg464(a: int4->min: int4, max: int4) {ordered}
%aggregate agg465(a: int4->min: int4, max: int4) {ordered}
%aggregate agg466(a: int4->min: int4, max: int4) {ordered}
%aggregate agg467(a: int4->min: int4, max: int4) {ordered}
%aggregate agg468(a: int4->min: int4, max: int4) {ordered}
%aggregate agg469(a: int4->min: int4, max: int4) {ordered}
%aggregate agg470(a: int4->min: int4, max: int4) {ordered}
%aggregate agg471(a: int4->min: int4, max: int4) {ordered}
%aggregate agg472(a: int4->min: int4, max: int4) {ordered}
%aggregate agg473(a: int4->min: int4, max: int4) {ordered}
%aggregate agg474(a: int4->min: int4, max: int4) {ordered}
%aggregate agg475(a: int4->min: int4, max: int4) {ordered}
%aggregate agg476(a: int4->min: int4, max: int4) {ordered}
%aggregate agg477(a: int4->min: int4, max: int4) {ordered}
%aggregate agg478(a: int4->min: int4, max: int4) {ordered}
%aggregate agg479(a: int4->min: int4, max: int4) {ordered}
%aggregate agg480(a: int4->min: int4, max: int4) {ordered}
%aggregate agg481(a: int4->min: int4, max: int4) {ordered}
%aggregate agg482(a: int4->min: int4, max: int4) {ordered}
%aggregate agg483(a: int4->min: int4, max: int4) {ordered}
%aggregate agg484(a: int4->min: int4, max: int4) {ordered}
%aggregate agg485(a: int4->min: int4, max: int4) {ordered}
%aggregate agg486(a: int4->min: int4, max: int4) {ordered}
%aggregate agg487(a: int4->min: int4, max: int4) {ordered}
%aggregate agg488(a: int4->min: int4, max: int4) {ordered}
%aggregate agg489(a: int4->min: int4, max: int4) {ordered}
%aggregate agg490(a: int4->min: int4, max: int4) {ordered}
%aggregate agg491(a: int4->min: int4, max: int4) {ordered}
%aggregate agg492(a: int4->min: int4, max: int4) {ordered}
%aggregate agg493(a: int4->min: int4, max: int4) {ordered}
%aggregate agg494(a: int4->min: int4, max: int4) {ordered}
%aggregate agg495(a: int4->min: int4, max: int4) {ordered}
%aggregate agg496(a: int4->min: int4, max: int4) {ordered}
%aggregate agg497(a: int4->min: int4, max: int4) {ordered}
%aggregate agg498(a: int4->min: int4, max: int4) {ordered}
%aggregate agg499(a: int4->min: int4, max: int4) {ordered}
%aggregate agg500(a: int4->min: int4, max: int4) {ordered}
%aggregate agg501(a: int4->min: int4, max: int4) {ordered}
%aggregate agg502(a: int4->min: int4, max: int4) {ordered}
%aggregate agg503(a: int4->min: int4, max: int4) {ordered}
%aggregate agg504(a: int4->min: int4, max: int4) {ordered}
%aggregate agg505(a: int4->min: int4, max: int4) {ordered}
%aggregate agg506(a: int4->min: int4, max: int4) {ordered}
%aggregate agg507(a: int4->min: int4, max: int4) {ordered}
%aggregate agg508(a: int4->min: int4, max: int4) {ordered}
%aggregate agg509(a: int4->min: int4, max: int4) {ordered}
%aggregate agg510(a: int4->min: int4, max: int4) {ordered}
%aggregate agg511(a: int4->min: int4, max: int4) {ordered}
%aggregate agg512(a: int4->min: int4, max: int4) {ordered}

C(a, min, max) :- A(a, b) GROUP-BY(a) agg001(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg002(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg003(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg004(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg005(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg006(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg007(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg008(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg009(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg010(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg011(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg012(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg013(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg014(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg015(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg016(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg017(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg018(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg019(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg020(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg021(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg022(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg023(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg024(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg025(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg026(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg027(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg028(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg029(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg030(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg031(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg032(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg033(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg034(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg035(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg036(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg037(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg038(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg039(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg040(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg041(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg042(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg043(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg044(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg045(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg046(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg047(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg048(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg049(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg050(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg051(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg052(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg053(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg054(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg055(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg056(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg057(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg058(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg059(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg060(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg061(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg062(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg063(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg064(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg065(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg066(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg067(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg068(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg069(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg070(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg071(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg072(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg073(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg074(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg075(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg076(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg077(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg078(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg079(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg080(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg081(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg082(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg083(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg084(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg085(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg086(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg087(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg088(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg089(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg090(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg091(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg092(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg093(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg094(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg095(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg096(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg097(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg098(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg099(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg100(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg101(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg102(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg103(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg104(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg105(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg106(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg107(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg108(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg109(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg110(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg111(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg112(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg113(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg114(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg115(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg116(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg117(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg118(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg119(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg120(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg121(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg122(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg123(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg124(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg125(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg126(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg127(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg128(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg129(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg130(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg131(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg132(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg133(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg134(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg135(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg136(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg137(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg138(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg139(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg140(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg141(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg142(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg143(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg144(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg145(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg146(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg147(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg148(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg149(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg150(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg151(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg152(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg153(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg154(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg155(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg156(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg157(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg158(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg159(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg160(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg161(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg162(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg163(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg164(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg165(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg166(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg167(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg168(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg169(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg170(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg171(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg172(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg173(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg174(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg175(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg176(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg177(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg178(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg179(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg180(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg181(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg182(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg183(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg184(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg185(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg186(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg187(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg188(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg189(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg190(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg191(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg192(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg193(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg194(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg195(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg196(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg197(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg198(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg199(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg200(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg201(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg202(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg203(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg204(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg205(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg206(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg207(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg208(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg209(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg210(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg211(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg212(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg213(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg214(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg215(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg216(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg217(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg218(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg219(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg220(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg221(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg222(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg223(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg224(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg225(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg226(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg227(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg228(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg229(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg230(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg231(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg232(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg233(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg234(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg235(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg236(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg237(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg238(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg239(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg240(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg241(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg242(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg243(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg244(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg245(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg246(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg247(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg248(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg249(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg250(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg251(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg252(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg253(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg254(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg255(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg256(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg257(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg258(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg259(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg260(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg261(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg262(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg263(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg264(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg265(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg266(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg267(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg268(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg269(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg270(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg271(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg272(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg273(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg274(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg275(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg276(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg277(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg278(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg279(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg280(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg281(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg282(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg283(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg284(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg285(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg286(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg287(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg288(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg289(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg290(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg291(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg292(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg293(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg294(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg295(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg296(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg297(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg298(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg299(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg300(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg301(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg302(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg303(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg304(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg305(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg306(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg307(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg308(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg309(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg310(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg311(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg312(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg313(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg314(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg315(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg316(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg317(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg318(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg319(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg320(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg321(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg322(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg323(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg324(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg325(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg326(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg327(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg328(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg329(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg330(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg331(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg332(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg333(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg334(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg335(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg336(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg337(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg338(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg339(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg340(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg341(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg342(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg343(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg344(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg345(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg346(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg347(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg348(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg349(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg350(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg351(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg352(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg353(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg354(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg355(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg356(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg357(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg358(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg359(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg360(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg361(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg362(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg363(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg364(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg365(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg366(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg367(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg368(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg369(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg370(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg371(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg372(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg373(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg374(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg375(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg376(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg377(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg378(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg379(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg380(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg381(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg382(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg383(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg384(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg385(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg386(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg387(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg388(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg389(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg390(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg391(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg392(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg393(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg394(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg395(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg396(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg397(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg398(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg399(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg400(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg401(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg402(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg403(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg404(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg405(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg406(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg407(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg408(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg409(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg410(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg411(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg412(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg413(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg414(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg415(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg416(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg417(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg418(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg419(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg420(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg421(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg422(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg423(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg424(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg425(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg426(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg427(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg428(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg429(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg430(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg431(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg432(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg433(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg434(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg435(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg436(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg437(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg438(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg439(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg440(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg441(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg442(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg443(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg444(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg445(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg446(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg447(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg448(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg449(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg450(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg451(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg452(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg453(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg454(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg455(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg456(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg457(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg458(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg459(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg460(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg461(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg462(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg463(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg464(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg465(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg466(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg467(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg468(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg469(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg470(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg471(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg472(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg473(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg474(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg475(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg476(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg477(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg478(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg479(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg480(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg481(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg482(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg483(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg484(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg485(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg486(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg487(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg488(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg489(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg490(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg491(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg492(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg493(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg494(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg495(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg496(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg497(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg498(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg499(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg500(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg501(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg502(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg503(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg504(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg505(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg506(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg507(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg508(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg509(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg510(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg511(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg512(b, min, max).



