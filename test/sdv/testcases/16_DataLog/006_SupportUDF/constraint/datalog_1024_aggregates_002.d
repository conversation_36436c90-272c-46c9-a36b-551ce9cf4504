%table A1(a:int4, b:int4)
%table B1(a:int4, min:int4, max:int4)


%aggregate aggA001(a: int4->min: int4, max: int4) {}
%aggregate aggA002(a: int4->min: int4, max: int4) {}
%aggregate aggA003(a: int4->min: int4, max: int4) {}
%aggregate aggA004(a: int4->min: int4, max: int4) {}
%aggregate aggA005(a: int4->min: int4, max: int4) {}
%aggregate aggA006(a: int4->min: int4, max: int4) {}
%aggregate aggA007(a: int4->min: int4, max: int4) {}
%aggregate aggA008(a: int4->min: int4, max: int4) {}
%aggregate aggA009(a: int4->min: int4, max: int4) {}
%aggregate aggA010(a: int4->min: int4, max: int4) {}
%aggregate aggA011(a: int4->min: int4, max: int4) {}
%aggregate aggA012(a: int4->min: int4, max: int4) {}
%aggregate aggA013(a: int4->min: int4, max: int4) {}
%aggregate aggA014(a: int4->min: int4, max: int4) {}
%aggregate aggA015(a: int4->min: int4, max: int4) {}
%aggregate aggA016(a: int4->min: int4, max: int4) {}
%aggregate aggA017(a: int4->min: int4, max: int4) {}
%aggregate aggA018(a: int4->min: int4, max: int4) {}
%aggregate aggA019(a: int4->min: int4, max: int4) {}
%aggregate aggA020(a: int4->min: int4, max: int4) {}
%aggregate aggA021(a: int4->min: int4, max: int4) {}
%aggregate aggA022(a: int4->min: int4, max: int4) {}
%aggregate aggA023(a: int4->min: int4, max: int4) {}
%aggregate aggA024(a: int4->min: int4, max: int4) {}
%aggregate aggA025(a: int4->min: int4, max: int4) {}
%aggregate aggA026(a: int4->min: int4, max: int4) {}
%aggregate aggA027(a: int4->min: int4, max: int4) {}
%aggregate aggA028(a: int4->min: int4, max: int4) {}
%aggregate aggA029(a: int4->min: int4, max: int4) {}
%aggregate aggA030(a: int4->min: int4, max: int4) {}
%aggregate aggA031(a: int4->min: int4, max: int4) {}
%aggregate aggA032(a: int4->min: int4, max: int4) {}
%aggregate aggA033(a: int4->min: int4, max: int4) {}
%aggregate aggA034(a: int4->min: int4, max: int4) {}
%aggregate aggA035(a: int4->min: int4, max: int4) {}
%aggregate aggA036(a: int4->min: int4, max: int4) {}
%aggregate aggA037(a: int4->min: int4, max: int4) {}
%aggregate aggA038(a: int4->min: int4, max: int4) {}
%aggregate aggA039(a: int4->min: int4, max: int4) {}
%aggregate aggA040(a: int4->min: int4, max: int4) {}
%aggregate aggA041(a: int4->min: int4, max: int4) {}
%aggregate aggA042(a: int4->min: int4, max: int4) {}
%aggregate aggA043(a: int4->min: int4, max: int4) {}
%aggregate aggA044(a: int4->min: int4, max: int4) {}
%aggregate aggA045(a: int4->min: int4, max: int4) {}
%aggregate aggA046(a: int4->min: int4, max: int4) {}
%aggregate aggA047(a: int4->min: int4, max: int4) {}
%aggregate aggA048(a: int4->min: int4, max: int4) {}
%aggregate aggA049(a: int4->min: int4, max: int4) {}
%aggregate aggA050(a: int4->min: int4, max: int4) {}
%aggregate aggA051(a: int4->min: int4, max: int4) {}
%aggregate aggA052(a: int4->min: int4, max: int4) {}
%aggregate aggA053(a: int4->min: int4, max: int4) {}
%aggregate aggA054(a: int4->min: int4, max: int4) {}
%aggregate aggA055(a: int4->min: int4, max: int4) {}
%aggregate aggA056(a: int4->min: int4, max: int4) {}
%aggregate aggA057(a: int4->min: int4, max: int4) {}
%aggregate aggA058(a: int4->min: int4, max: int4) {}
%aggregate aggA059(a: int4->min: int4, max: int4) {}
%aggregate aggA060(a: int4->min: int4, max: int4) {}
%aggregate aggA061(a: int4->min: int4, max: int4) {}
%aggregate aggA062(a: int4->min: int4, max: int4) {}
%aggregate aggA063(a: int4->min: int4, max: int4) {}
%aggregate aggA064(a: int4->min: int4, max: int4) {}
%aggregate aggA065(a: int4->min: int4, max: int4) {}
%aggregate aggA066(a: int4->min: int4, max: int4) {}
%aggregate aggA067(a: int4->min: int4, max: int4) {}
%aggregate aggA068(a: int4->min: int4, max: int4) {}
%aggregate aggA069(a: int4->min: int4, max: int4) {}
%aggregate aggA070(a: int4->min: int4, max: int4) {}
%aggregate aggA071(a: int4->min: int4, max: int4) {}
%aggregate aggA072(a: int4->min: int4, max: int4) {}
%aggregate aggA073(a: int4->min: int4, max: int4) {}
%aggregate aggA074(a: int4->min: int4, max: int4) {}
%aggregate aggA075(a: int4->min: int4, max: int4) {}
%aggregate aggA076(a: int4->min: int4, max: int4) {}
%aggregate aggA077(a: int4->min: int4, max: int4) {}
%aggregate aggA078(a: int4->min: int4, max: int4) {}
%aggregate aggA079(a: int4->min: int4, max: int4) {}
%aggregate aggA080(a: int4->min: int4, max: int4) {}
%aggregate aggA081(a: int4->min: int4, max: int4) {}
%aggregate aggA082(a: int4->min: int4, max: int4) {}
%aggregate aggA083(a: int4->min: int4, max: int4) {}
%aggregate aggA084(a: int4->min: int4, max: int4) {}
%aggregate aggA085(a: int4->min: int4, max: int4) {}
%aggregate aggA086(a: int4->min: int4, max: int4) {}
%aggregate aggA087(a: int4->min: int4, max: int4) {}
%aggregate aggA088(a: int4->min: int4, max: int4) {}
%aggregate aggA089(a: int4->min: int4, max: int4) {}
%aggregate aggA090(a: int4->min: int4, max: int4) {}
%aggregate aggA091(a: int4->min: int4, max: int4) {}
%aggregate aggA092(a: int4->min: int4, max: int4) {}
%aggregate aggA093(a: int4->min: int4, max: int4) {}
%aggregate aggA094(a: int4->min: int4, max: int4) {}
%aggregate aggA095(a: int4->min: int4, max: int4) {}
%aggregate aggA096(a: int4->min: int4, max: int4) {}
%aggregate aggA097(a: int4->min: int4, max: int4) {}
%aggregate aggA098(a: int4->min: int4, max: int4) {}
%aggregate aggA099(a: int4->min: int4, max: int4) {}
%aggregate aggA100(a: int4->min: int4, max: int4) {}
%aggregate aggA101(a: int4->min: int4, max: int4) {}
%aggregate aggA102(a: int4->min: int4, max: int4) {}
%aggregate aggA103(a: int4->min: int4, max: int4) {}
%aggregate aggA104(a: int4->min: int4, max: int4) {}
%aggregate aggA105(a: int4->min: int4, max: int4) {}
%aggregate aggA106(a: int4->min: int4, max: int4) {}
%aggregate aggA107(a: int4->min: int4, max: int4) {}
%aggregate aggA108(a: int4->min: int4, max: int4) {}
%aggregate aggA109(a: int4->min: int4, max: int4) {}
%aggregate aggA110(a: int4->min: int4, max: int4) {}
%aggregate aggA111(a: int4->min: int4, max: int4) {}
%aggregate aggA112(a: int4->min: int4, max: int4) {}
%aggregate aggA113(a: int4->min: int4, max: int4) {}
%aggregate aggA114(a: int4->min: int4, max: int4) {}
%aggregate aggA115(a: int4->min: int4, max: int4) {}
%aggregate aggA116(a: int4->min: int4, max: int4) {}
%aggregate aggA117(a: int4->min: int4, max: int4) {}
%aggregate aggA118(a: int4->min: int4, max: int4) {}
%aggregate aggA119(a: int4->min: int4, max: int4) {}
%aggregate aggA120(a: int4->min: int4, max: int4) {}
%aggregate aggA121(a: int4->min: int4, max: int4) {}
%aggregate aggA122(a: int4->min: int4, max: int4) {}
%aggregate aggA123(a: int4->min: int4, max: int4) {}
%aggregate aggA124(a: int4->min: int4, max: int4) {}
%aggregate aggA125(a: int4->min: int4, max: int4) {}
%aggregate aggA126(a: int4->min: int4, max: int4) {}
%aggregate aggA127(a: int4->min: int4, max: int4) {}
%aggregate aggA128(a: int4->min: int4, max: int4) {}
%aggregate aggA129(a: int4->min: int4, max: int4) {}
%aggregate aggA130(a: int4->min: int4, max: int4) {}
%aggregate aggA131(a: int4->min: int4, max: int4) {}
%aggregate aggA132(a: int4->min: int4, max: int4) {}
%aggregate aggA133(a: int4->min: int4, max: int4) {}
%aggregate aggA134(a: int4->min: int4, max: int4) {}
%aggregate aggA135(a: int4->min: int4, max: int4) {}
%aggregate aggA136(a: int4->min: int4, max: int4) {}
%aggregate aggA137(a: int4->min: int4, max: int4) {}
%aggregate aggA138(a: int4->min: int4, max: int4) {}
%aggregate aggA139(a: int4->min: int4, max: int4) {}
%aggregate aggA140(a: int4->min: int4, max: int4) {}
%aggregate aggA141(a: int4->min: int4, max: int4) {}
%aggregate aggA142(a: int4->min: int4, max: int4) {}
%aggregate aggA143(a: int4->min: int4, max: int4) {}
%aggregate aggA144(a: int4->min: int4, max: int4) {}
%aggregate aggA145(a: int4->min: int4, max: int4) {}
%aggregate aggA146(a: int4->min: int4, max: int4) {}
%aggregate aggA147(a: int4->min: int4, max: int4) {}
%aggregate aggA148(a: int4->min: int4, max: int4) {}
%aggregate aggA149(a: int4->min: int4, max: int4) {}
%aggregate aggA150(a: int4->min: int4, max: int4) {}
%aggregate aggA151(a: int4->min: int4, max: int4) {}
%aggregate aggA152(a: int4->min: int4, max: int4) {}
%aggregate aggA153(a: int4->min: int4, max: int4) {}
%aggregate aggA154(a: int4->min: int4, max: int4) {}
%aggregate aggA155(a: int4->min: int4, max: int4) {}
%aggregate aggA156(a: int4->min: int4, max: int4) {}
%aggregate aggA157(a: int4->min: int4, max: int4) {}
%aggregate aggA158(a: int4->min: int4, max: int4) {}
%aggregate aggA159(a: int4->min: int4, max: int4) {}
%aggregate aggA160(a: int4->min: int4, max: int4) {}
%aggregate aggA161(a: int4->min: int4, max: int4) {}
%aggregate aggA162(a: int4->min: int4, max: int4) {}
%aggregate aggA163(a: int4->min: int4, max: int4) {}
%aggregate aggA164(a: int4->min: int4, max: int4) {}
%aggregate aggA165(a: int4->min: int4, max: int4) {}
%aggregate aggA166(a: int4->min: int4, max: int4) {}
%aggregate aggA167(a: int4->min: int4, max: int4) {}
%aggregate aggA168(a: int4->min: int4, max: int4) {}
%aggregate aggA169(a: int4->min: int4, max: int4) {}
%aggregate aggA170(a: int4->min: int4, max: int4) {}
%aggregate aggA171(a: int4->min: int4, max: int4) {}
%aggregate aggA172(a: int4->min: int4, max: int4) {}
%aggregate aggA173(a: int4->min: int4, max: int4) {}
%aggregate aggA174(a: int4->min: int4, max: int4) {}
%aggregate aggA175(a: int4->min: int4, max: int4) {}
%aggregate aggA176(a: int4->min: int4, max: int4) {}
%aggregate aggA177(a: int4->min: int4, max: int4) {}
%aggregate aggA178(a: int4->min: int4, max: int4) {}
%aggregate aggA179(a: int4->min: int4, max: int4) {}
%aggregate aggA180(a: int4->min: int4, max: int4) {}
%aggregate aggA181(a: int4->min: int4, max: int4) {}
%aggregate aggA182(a: int4->min: int4, max: int4) {}
%aggregate aggA183(a: int4->min: int4, max: int4) {}
%aggregate aggA184(a: int4->min: int4, max: int4) {}
%aggregate aggA185(a: int4->min: int4, max: int4) {}
%aggregate aggA186(a: int4->min: int4, max: int4) {}
%aggregate aggA187(a: int4->min: int4, max: int4) {}
%aggregate aggA188(a: int4->min: int4, max: int4) {}
%aggregate aggA189(a: int4->min: int4, max: int4) {}
%aggregate aggA190(a: int4->min: int4, max: int4) {}
%aggregate aggA191(a: int4->min: int4, max: int4) {}
%aggregate aggA192(a: int4->min: int4, max: int4) {}
%aggregate aggA193(a: int4->min: int4, max: int4) {}
%aggregate aggA194(a: int4->min: int4, max: int4) {}
%aggregate aggA195(a: int4->min: int4, max: int4) {}
%aggregate aggA196(a: int4->min: int4, max: int4) {}
%aggregate aggA197(a: int4->min: int4, max: int4) {}
%aggregate aggA198(a: int4->min: int4, max: int4) {}
%aggregate aggA199(a: int4->min: int4, max: int4) {}
%aggregate aggA200(a: int4->min: int4, max: int4) {}
%aggregate aggA201(a: int4->min: int4, max: int4) {}
%aggregate aggA202(a: int4->min: int4, max: int4) {}
%aggregate aggA203(a: int4->min: int4, max: int4) {}
%aggregate aggA204(a: int4->min: int4, max: int4) {}
%aggregate aggA205(a: int4->min: int4, max: int4) {}
%aggregate aggA206(a: int4->min: int4, max: int4) {}
%aggregate aggA207(a: int4->min: int4, max: int4) {}
%aggregate aggA208(a: int4->min: int4, max: int4) {}
%aggregate aggA209(a: int4->min: int4, max: int4) {}
%aggregate aggA210(a: int4->min: int4, max: int4) {}
%aggregate aggA211(a: int4->min: int4, max: int4) {}
%aggregate aggA212(a: int4->min: int4, max: int4) {}
%aggregate aggA213(a: int4->min: int4, max: int4) {}
%aggregate aggA214(a: int4->min: int4, max: int4) {}
%aggregate aggA215(a: int4->min: int4, max: int4) {}
%aggregate aggA216(a: int4->min: int4, max: int4) {}
%aggregate aggA217(a: int4->min: int4, max: int4) {}
%aggregate aggA218(a: int4->min: int4, max: int4) {}
%aggregate aggA219(a: int4->min: int4, max: int4) {}
%aggregate aggA220(a: int4->min: int4, max: int4) {}
%aggregate aggA221(a: int4->min: int4, max: int4) {}
%aggregate aggA222(a: int4->min: int4, max: int4) {}
%aggregate aggA223(a: int4->min: int4, max: int4) {}
%aggregate aggA224(a: int4->min: int4, max: int4) {}
%aggregate aggA225(a: int4->min: int4, max: int4) {}
%aggregate aggA226(a: int4->min: int4, max: int4) {}
%aggregate aggA227(a: int4->min: int4, max: int4) {}
%aggregate aggA228(a: int4->min: int4, max: int4) {}
%aggregate aggA229(a: int4->min: int4, max: int4) {}
%aggregate aggA230(a: int4->min: int4, max: int4) {}
%aggregate aggA231(a: int4->min: int4, max: int4) {}
%aggregate aggA232(a: int4->min: int4, max: int4) {}
%aggregate aggA233(a: int4->min: int4, max: int4) {}
%aggregate aggA234(a: int4->min: int4, max: int4) {}
%aggregate aggA235(a: int4->min: int4, max: int4) {}
%aggregate aggA236(a: int4->min: int4, max: int4) {}
%aggregate aggA237(a: int4->min: int4, max: int4) {}
%aggregate aggA238(a: int4->min: int4, max: int4) {}
%aggregate aggA239(a: int4->min: int4, max: int4) {}
%aggregate aggA240(a: int4->min: int4, max: int4) {}
%aggregate aggA241(a: int4->min: int4, max: int4) {}
%aggregate aggA242(a: int4->min: int4, max: int4) {}
%aggregate aggA243(a: int4->min: int4, max: int4) {}
%aggregate aggA244(a: int4->min: int4, max: int4) {}
%aggregate aggA245(a: int4->min: int4, max: int4) {}
%aggregate aggA246(a: int4->min: int4, max: int4) {}
%aggregate aggA247(a: int4->min: int4, max: int4) {}
%aggregate aggA248(a: int4->min: int4, max: int4) {}
%aggregate aggA249(a: int4->min: int4, max: int4) {}
%aggregate aggA250(a: int4->min: int4, max: int4) {}
%aggregate aggA251(a: int4->min: int4, max: int4) {}
%aggregate aggA252(a: int4->min: int4, max: int4) {}
%aggregate aggA253(a: int4->min: int4, max: int4) {}
%aggregate aggA254(a: int4->min: int4, max: int4) {}
%aggregate aggA255(a: int4->min: int4, max: int4) {}
%aggregate aggA256(a: int4->min: int4, max: int4) {}
%aggregate aggA257(a: int4->min: int4, max: int4) {}
%aggregate aggA258(a: int4->min: int4, max: int4) {}
%aggregate aggA259(a: int4->min: int4, max: int4) {}
%aggregate aggA260(a: int4->min: int4, max: int4) {}
%aggregate aggA261(a: int4->min: int4, max: int4) {}
%aggregate aggA262(a: int4->min: int4, max: int4) {}
%aggregate aggA263(a: int4->min: int4, max: int4) {}
%aggregate aggA264(a: int4->min: int4, max: int4) {}
%aggregate aggA265(a: int4->min: int4, max: int4) {}
%aggregate aggA266(a: int4->min: int4, max: int4) {}
%aggregate aggA267(a: int4->min: int4, max: int4) {}
%aggregate aggA268(a: int4->min: int4, max: int4) {}
%aggregate aggA269(a: int4->min: int4, max: int4) {}
%aggregate aggA270(a: int4->min: int4, max: int4) {}
%aggregate aggA271(a: int4->min: int4, max: int4) {}
%aggregate aggA272(a: int4->min: int4, max: int4) {}
%aggregate aggA273(a: int4->min: int4, max: int4) {}
%aggregate aggA274(a: int4->min: int4, max: int4) {}
%aggregate aggA275(a: int4->min: int4, max: int4) {}
%aggregate aggA276(a: int4->min: int4, max: int4) {}
%aggregate aggA277(a: int4->min: int4, max: int4) {}
%aggregate aggA278(a: int4->min: int4, max: int4) {}
%aggregate aggA279(a: int4->min: int4, max: int4) {}
%aggregate aggA280(a: int4->min: int4, max: int4) {}
%aggregate aggA281(a: int4->min: int4, max: int4) {}
%aggregate aggA282(a: int4->min: int4, max: int4) {}
%aggregate aggA283(a: int4->min: int4, max: int4) {}
%aggregate aggA284(a: int4->min: int4, max: int4) {}
%aggregate aggA285(a: int4->min: int4, max: int4) {}
%aggregate aggA286(a: int4->min: int4, max: int4) {}
%aggregate aggA287(a: int4->min: int4, max: int4) {}
%aggregate aggA288(a: int4->min: int4, max: int4) {}
%aggregate aggA289(a: int4->min: int4, max: int4) {}
%aggregate aggA290(a: int4->min: int4, max: int4) {}
%aggregate aggA291(a: int4->min: int4, max: int4) {}
%aggregate aggA292(a: int4->min: int4, max: int4) {}
%aggregate aggA293(a: int4->min: int4, max: int4) {}
%aggregate aggA294(a: int4->min: int4, max: int4) {}
%aggregate aggA295(a: int4->min: int4, max: int4) {}
%aggregate aggA296(a: int4->min: int4, max: int4) {}
%aggregate aggA297(a: int4->min: int4, max: int4) {}
%aggregate aggA298(a: int4->min: int4, max: int4) {}
%aggregate aggA299(a: int4->min: int4, max: int4) {}
%aggregate aggA300(a: int4->min: int4, max: int4) {}
%aggregate aggA301(a: int4->min: int4, max: int4) {}
%aggregate aggA302(a: int4->min: int4, max: int4) {}
%aggregate aggA303(a: int4->min: int4, max: int4) {}
%aggregate aggA304(a: int4->min: int4, max: int4) {}
%aggregate aggA305(a: int4->min: int4, max: int4) {}
%aggregate aggA306(a: int4->min: int4, max: int4) {}
%aggregate aggA307(a: int4->min: int4, max: int4) {}
%aggregate aggA308(a: int4->min: int4, max: int4) {}
%aggregate aggA309(a: int4->min: int4, max: int4) {}
%aggregate aggA310(a: int4->min: int4, max: int4) {}
%aggregate aggA311(a: int4->min: int4, max: int4) {}
%aggregate aggA312(a: int4->min: int4, max: int4) {}
%aggregate aggA313(a: int4->min: int4, max: int4) {}
%aggregate aggA314(a: int4->min: int4, max: int4) {}
%aggregate aggA315(a: int4->min: int4, max: int4) {}
%aggregate aggA316(a: int4->min: int4, max: int4) {}
%aggregate aggA317(a: int4->min: int4, max: int4) {}
%aggregate aggA318(a: int4->min: int4, max: int4) {}
%aggregate aggA319(a: int4->min: int4, max: int4) {}
%aggregate aggA320(a: int4->min: int4, max: int4) {}
%aggregate aggA321(a: int4->min: int4, max: int4) {}
%aggregate aggA322(a: int4->min: int4, max: int4) {}
%aggregate aggA323(a: int4->min: int4, max: int4) {}
%aggregate aggA324(a: int4->min: int4, max: int4) {}
%aggregate aggA325(a: int4->min: int4, max: int4) {}
%aggregate aggA326(a: int4->min: int4, max: int4) {}
%aggregate aggA327(a: int4->min: int4, max: int4) {}
%aggregate aggA328(a: int4->min: int4, max: int4) {}
%aggregate aggA329(a: int4->min: int4, max: int4) {}
%aggregate aggA330(a: int4->min: int4, max: int4) {}
%aggregate aggA331(a: int4->min: int4, max: int4) {}
%aggregate aggA332(a: int4->min: int4, max: int4) {}
%aggregate aggA333(a: int4->min: int4, max: int4) {}
%aggregate aggA334(a: int4->min: int4, max: int4) {}
%aggregate aggA335(a: int4->min: int4, max: int4) {}
%aggregate aggA336(a: int4->min: int4, max: int4) {}
%aggregate aggA337(a: int4->min: int4, max: int4) {}
%aggregate aggA338(a: int4->min: int4, max: int4) {}
%aggregate aggA339(a: int4->min: int4, max: int4) {}
%aggregate aggA340(a: int4->min: int4, max: int4) {}
%aggregate aggA341(a: int4->min: int4, max: int4) {}
%aggregate aggA342(a: int4->min: int4, max: int4) {}
%aggregate aggA343(a: int4->min: int4, max: int4) {}
%aggregate aggA344(a: int4->min: int4, max: int4) {}
%aggregate aggA345(a: int4->min: int4, max: int4) {}
%aggregate aggA346(a: int4->min: int4, max: int4) {}
%aggregate aggA347(a: int4->min: int4, max: int4) {}
%aggregate aggA348(a: int4->min: int4, max: int4) {}
%aggregate aggA349(a: int4->min: int4, max: int4) {}
%aggregate aggA350(a: int4->min: int4, max: int4) {}
%aggregate aggA351(a: int4->min: int4, max: int4) {}
%aggregate aggA352(a: int4->min: int4, max: int4) {}
%aggregate aggA353(a: int4->min: int4, max: int4) {}
%aggregate aggA354(a: int4->min: int4, max: int4) {}
%aggregate aggA355(a: int4->min: int4, max: int4) {}
%aggregate aggA356(a: int4->min: int4, max: int4) {}
%aggregate aggA357(a: int4->min: int4, max: int4) {}
%aggregate aggA358(a: int4->min: int4, max: int4) {}
%aggregate aggA359(a: int4->min: int4, max: int4) {}
%aggregate aggA360(a: int4->min: int4, max: int4) {}
%aggregate aggA361(a: int4->min: int4, max: int4) {}
%aggregate aggA362(a: int4->min: int4, max: int4) {}
%aggregate aggA363(a: int4->min: int4, max: int4) {}
%aggregate aggA364(a: int4->min: int4, max: int4) {}
%aggregate aggA365(a: int4->min: int4, max: int4) {}
%aggregate aggA366(a: int4->min: int4, max: int4) {}
%aggregate aggA367(a: int4->min: int4, max: int4) {}
%aggregate aggA368(a: int4->min: int4, max: int4) {}
%aggregate aggA369(a: int4->min: int4, max: int4) {}
%aggregate aggA370(a: int4->min: int4, max: int4) {}
%aggregate aggA371(a: int4->min: int4, max: int4) {}
%aggregate aggA372(a: int4->min: int4, max: int4) {}
%aggregate aggA373(a: int4->min: int4, max: int4) {}
%aggregate aggA374(a: int4->min: int4, max: int4) {}
%aggregate aggA375(a: int4->min: int4, max: int4) {}
%aggregate aggA376(a: int4->min: int4, max: int4) {}
%aggregate aggA377(a: int4->min: int4, max: int4) {}
%aggregate aggA378(a: int4->min: int4, max: int4) {}
%aggregate aggA379(a: int4->min: int4, max: int4) {}
%aggregate aggA380(a: int4->min: int4, max: int4) {}
%aggregate aggA381(a: int4->min: int4, max: int4) {}
%aggregate aggA382(a: int4->min: int4, max: int4) {}
%aggregate aggA383(a: int4->min: int4, max: int4) {}
%aggregate aggA384(a: int4->min: int4, max: int4) {}
%aggregate aggA385(a: int4->min: int4, max: int4) {}
%aggregate aggA386(a: int4->min: int4, max: int4) {}
%aggregate aggA387(a: int4->min: int4, max: int4) {}
%aggregate aggA388(a: int4->min: int4, max: int4) {}
%aggregate aggA389(a: int4->min: int4, max: int4) {}
%aggregate aggA390(a: int4->min: int4, max: int4) {}
%aggregate aggA391(a: int4->min: int4, max: int4) {}
%aggregate aggA392(a: int4->min: int4, max: int4) {}
%aggregate aggA393(a: int4->min: int4, max: int4) {}
%aggregate aggA394(a: int4->min: int4, max: int4) {}
%aggregate aggA395(a: int4->min: int4, max: int4) {}
%aggregate aggA396(a: int4->min: int4, max: int4) {}
%aggregate aggA397(a: int4->min: int4, max: int4) {}
%aggregate aggA398(a: int4->min: int4, max: int4) {}
%aggregate aggA399(a: int4->min: int4, max: int4) {}
%aggregate aggA400(a: int4->min: int4, max: int4) {}
%aggregate aggA401(a: int4->min: int4, max: int4) {}
%aggregate aggA402(a: int4->min: int4, max: int4) {}
%aggregate aggA403(a: int4->min: int4, max: int4) {}
%aggregate aggA404(a: int4->min: int4, max: int4) {}
%aggregate aggA405(a: int4->min: int4, max: int4) {}
%aggregate aggA406(a: int4->min: int4, max: int4) {}
%aggregate aggA407(a: int4->min: int4, max: int4) {}
%aggregate aggA408(a: int4->min: int4, max: int4) {}
%aggregate aggA409(a: int4->min: int4, max: int4) {}
%aggregate aggA410(a: int4->min: int4, max: int4) {}
%aggregate aggA411(a: int4->min: int4, max: int4) {}
%aggregate aggA412(a: int4->min: int4, max: int4) {}
%aggregate aggA413(a: int4->min: int4, max: int4) {}
%aggregate aggA414(a: int4->min: int4, max: int4) {}
%aggregate aggA415(a: int4->min: int4, max: int4) {}
%aggregate aggA416(a: int4->min: int4, max: int4) {}
%aggregate aggA417(a: int4->min: int4, max: int4) {}
%aggregate aggA418(a: int4->min: int4, max: int4) {}
%aggregate aggA419(a: int4->min: int4, max: int4) {}
%aggregate aggA420(a: int4->min: int4, max: int4) {}
%aggregate aggA421(a: int4->min: int4, max: int4) {}
%aggregate aggA422(a: int4->min: int4, max: int4) {}
%aggregate aggA423(a: int4->min: int4, max: int4) {}
%aggregate aggA424(a: int4->min: int4, max: int4) {}
%aggregate aggA425(a: int4->min: int4, max: int4) {}
%aggregate aggA426(a: int4->min: int4, max: int4) {}
%aggregate aggA427(a: int4->min: int4, max: int4) {}
%aggregate aggA428(a: int4->min: int4, max: int4) {}
%aggregate aggA429(a: int4->min: int4, max: int4) {}
%aggregate aggA430(a: int4->min: int4, max: int4) {}
%aggregate aggA431(a: int4->min: int4, max: int4) {}
%aggregate aggA432(a: int4->min: int4, max: int4) {}
%aggregate aggA433(a: int4->min: int4, max: int4) {}
%aggregate aggA434(a: int4->min: int4, max: int4) {}
%aggregate aggA435(a: int4->min: int4, max: int4) {}
%aggregate aggA436(a: int4->min: int4, max: int4) {}
%aggregate aggA437(a: int4->min: int4, max: int4) {}
%aggregate aggA438(a: int4->min: int4, max: int4) {}
%aggregate aggA439(a: int4->min: int4, max: int4) {}
%aggregate aggA440(a: int4->min: int4, max: int4) {}
%aggregate aggA441(a: int4->min: int4, max: int4) {}
%aggregate aggA442(a: int4->min: int4, max: int4) {}
%aggregate aggA443(a: int4->min: int4, max: int4) {}
%aggregate aggA444(a: int4->min: int4, max: int4) {}
%aggregate aggA445(a: int4->min: int4, max: int4) {}
%aggregate aggA446(a: int4->min: int4, max: int4) {}
%aggregate aggA447(a: int4->min: int4, max: int4) {}
%aggregate aggA448(a: int4->min: int4, max: int4) {}
%aggregate aggA449(a: int4->min: int4, max: int4) {}
%aggregate aggA450(a: int4->min: int4, max: int4) {}
%aggregate aggA451(a: int4->min: int4, max: int4) {}
%aggregate aggA452(a: int4->min: int4, max: int4) {}
%aggregate aggA453(a: int4->min: int4, max: int4) {}
%aggregate aggA454(a: int4->min: int4, max: int4) {}
%aggregate aggA455(a: int4->min: int4, max: int4) {}
%aggregate aggA456(a: int4->min: int4, max: int4) {}
%aggregate aggA457(a: int4->min: int4, max: int4) {}
%aggregate aggA458(a: int4->min: int4, max: int4) {}
%aggregate aggA459(a: int4->min: int4, max: int4) {}
%aggregate aggA460(a: int4->min: int4, max: int4) {}
%aggregate aggA461(a: int4->min: int4, max: int4) {}
%aggregate aggA462(a: int4->min: int4, max: int4) {}
%aggregate aggA463(a: int4->min: int4, max: int4) {}
%aggregate aggA464(a: int4->min: int4, max: int4) {}
%aggregate aggA465(a: int4->min: int4, max: int4) {}
%aggregate aggA466(a: int4->min: int4, max: int4) {}
%aggregate aggA467(a: int4->min: int4, max: int4) {}
%aggregate aggA468(a: int4->min: int4, max: int4) {}
%aggregate aggA469(a: int4->min: int4, max: int4) {}
%aggregate aggA470(a: int4->min: int4, max: int4) {}
%aggregate aggA471(a: int4->min: int4, max: int4) {}
%aggregate aggA472(a: int4->min: int4, max: int4) {}
%aggregate aggA473(a: int4->min: int4, max: int4) {}
%aggregate aggA474(a: int4->min: int4, max: int4) {}
%aggregate aggA475(a: int4->min: int4, max: int4) {}
%aggregate aggA476(a: int4->min: int4, max: int4) {}
%aggregate aggA477(a: int4->min: int4, max: int4) {}
%aggregate aggA478(a: int4->min: int4, max: int4) {}
%aggregate aggA479(a: int4->min: int4, max: int4) {}
%aggregate aggA480(a: int4->min: int4, max: int4) {}
%aggregate aggA481(a: int4->min: int4, max: int4) {}
%aggregate aggA482(a: int4->min: int4, max: int4) {}
%aggregate aggA483(a: int4->min: int4, max: int4) {}
%aggregate aggA484(a: int4->min: int4, max: int4) {}
%aggregate aggA485(a: int4->min: int4, max: int4) {}
%aggregate aggA486(a: int4->min: int4, max: int4) {}
%aggregate aggA487(a: int4->min: int4, max: int4) {}
%aggregate aggA488(a: int4->min: int4, max: int4) {}
%aggregate aggA489(a: int4->min: int4, max: int4) {}
%aggregate aggA490(a: int4->min: int4, max: int4) {}
%aggregate aggA491(a: int4->min: int4, max: int4) {}
%aggregate aggA492(a: int4->min: int4, max: int4) {}
%aggregate aggA493(a: int4->min: int4, max: int4) {}
%aggregate aggA494(a: int4->min: int4, max: int4) {}
%aggregate aggA495(a: int4->min: int4, max: int4) {}
%aggregate aggA496(a: int4->min: int4, max: int4) {}
%aggregate aggA497(a: int4->min: int4, max: int4) {}
%aggregate aggA498(a: int4->min: int4, max: int4) {}
%aggregate aggA499(a: int4->min: int4, max: int4) {}
%aggregate aggA500(a: int4->min: int4, max: int4) {}
%aggregate aggA501(a: int4->min: int4, max: int4) {}
%aggregate aggA502(a: int4->min: int4, max: int4) {}
%aggregate aggA503(a: int4->min: int4, max: int4) {}
%aggregate aggA504(a: int4->min: int4, max: int4) {}
%aggregate aggA505(a: int4->min: int4, max: int4) {}
%aggregate aggA506(a: int4->min: int4, max: int4) {}
%aggregate aggA507(a: int4->min: int4, max: int4) {}
%aggregate aggA508(a: int4->min: int4, max: int4) {}
%aggregate aggA509(a: int4->min: int4, max: int4) {}
%aggregate aggA510(a: int4->min: int4, max: int4) {}
%aggregate aggA511(a: int4->min: int4, max: int4) {}
%aggregate aggA512(a: int4->min: int4, max: int4) {}
%aggregate aggA513(a: int4->min: int4, max: int4) {}
%aggregate aggA514(a: int4->min: int4, max: int4) {}
%aggregate aggA515(a: int4->min: int4, max: int4) {}
%aggregate aggA516(a: int4->min: int4, max: int4) {}
%aggregate aggA517(a: int4->min: int4, max: int4) {}
%aggregate aggA518(a: int4->min: int4, max: int4) {}
%aggregate aggA519(a: int4->min: int4, max: int4) {}
%aggregate aggA520(a: int4->min: int4, max: int4) {}
%aggregate aggA521(a: int4->min: int4, max: int4) {}
%aggregate aggA522(a: int4->min: int4, max: int4) {}
%aggregate aggA523(a: int4->min: int4, max: int4) {}
%aggregate aggA524(a: int4->min: int4, max: int4) {}
%aggregate aggA525(a: int4->min: int4, max: int4) {}
%aggregate aggA526(a: int4->min: int4, max: int4) {}
%aggregate aggA527(a: int4->min: int4, max: int4) {}
%aggregate aggA528(a: int4->min: int4, max: int4) {}
%aggregate aggA529(a: int4->min: int4, max: int4) {}
%aggregate aggA530(a: int4->min: int4, max: int4) {}
%aggregate aggA531(a: int4->min: int4, max: int4) {}
%aggregate aggA532(a: int4->min: int4, max: int4) {}
%aggregate aggA533(a: int4->min: int4, max: int4) {}
%aggregate aggA534(a: int4->min: int4, max: int4) {}
%aggregate aggA535(a: int4->min: int4, max: int4) {}
%aggregate aggA536(a: int4->min: int4, max: int4) {}
%aggregate aggA537(a: int4->min: int4, max: int4) {}
%aggregate aggA538(a: int4->min: int4, max: int4) {}
%aggregate aggA539(a: int4->min: int4, max: int4) {}
%aggregate aggA540(a: int4->min: int4, max: int4) {}
%aggregate aggA541(a: int4->min: int4, max: int4) {}
%aggregate aggA542(a: int4->min: int4, max: int4) {}
%aggregate aggA543(a: int4->min: int4, max: int4) {}
%aggregate aggA544(a: int4->min: int4, max: int4) {}
%aggregate aggA545(a: int4->min: int4, max: int4) {}
%aggregate aggA546(a: int4->min: int4, max: int4) {}
%aggregate aggA547(a: int4->min: int4, max: int4) {}
%aggregate aggA548(a: int4->min: int4, max: int4) {}
%aggregate aggA549(a: int4->min: int4, max: int4) {}
%aggregate aggA550(a: int4->min: int4, max: int4) {}
%aggregate aggA551(a: int4->min: int4, max: int4) {}
%aggregate aggA552(a: int4->min: int4, max: int4) {}
%aggregate aggA553(a: int4->min: int4, max: int4) {}
%aggregate aggA554(a: int4->min: int4, max: int4) {}
%aggregate aggA555(a: int4->min: int4, max: int4) {}
%aggregate aggA556(a: int4->min: int4, max: int4) {}
%aggregate aggA557(a: int4->min: int4, max: int4) {}
%aggregate aggA558(a: int4->min: int4, max: int4) {}
%aggregate aggA559(a: int4->min: int4, max: int4) {}
%aggregate aggA560(a: int4->min: int4, max: int4) {}
%aggregate aggA561(a: int4->min: int4, max: int4) {}
%aggregate aggA562(a: int4->min: int4, max: int4) {}
%aggregate aggA563(a: int4->min: int4, max: int4) {}
%aggregate aggA564(a: int4->min: int4, max: int4) {}
%aggregate aggA565(a: int4->min: int4, max: int4) {}
%aggregate aggA566(a: int4->min: int4, max: int4) {}
%aggregate aggA567(a: int4->min: int4, max: int4) {}
%aggregate aggA568(a: int4->min: int4, max: int4) {}
%aggregate aggA569(a: int4->min: int4, max: int4) {}
%aggregate aggA570(a: int4->min: int4, max: int4) {}
%aggregate aggA571(a: int4->min: int4, max: int4) {}
%aggregate aggA572(a: int4->min: int4, max: int4) {}
%aggregate aggA573(a: int4->min: int4, max: int4) {}
%aggregate aggA574(a: int4->min: int4, max: int4) {}
%aggregate aggA575(a: int4->min: int4, max: int4) {}
%aggregate aggA576(a: int4->min: int4, max: int4) {}
%aggregate aggA577(a: int4->min: int4, max: int4) {}
%aggregate aggA578(a: int4->min: int4, max: int4) {}
%aggregate aggA579(a: int4->min: int4, max: int4) {}
%aggregate aggA580(a: int4->min: int4, max: int4) {}
%aggregate aggA581(a: int4->min: int4, max: int4) {}
%aggregate aggA582(a: int4->min: int4, max: int4) {}
%aggregate aggA583(a: int4->min: int4, max: int4) {}
%aggregate aggA584(a: int4->min: int4, max: int4) {}
%aggregate aggA585(a: int4->min: int4, max: int4) {}
%aggregate aggA586(a: int4->min: int4, max: int4) {}
%aggregate aggA587(a: int4->min: int4, max: int4) {}
%aggregate aggA588(a: int4->min: int4, max: int4) {}
%aggregate aggA589(a: int4->min: int4, max: int4) {}
%aggregate aggA590(a: int4->min: int4, max: int4) {}
%aggregate aggA591(a: int4->min: int4, max: int4) {}
%aggregate aggA592(a: int4->min: int4, max: int4) {}
%aggregate aggA593(a: int4->min: int4, max: int4) {}
%aggregate aggA594(a: int4->min: int4, max: int4) {}
%aggregate aggA595(a: int4->min: int4, max: int4) {}
%aggregate aggA596(a: int4->min: int4, max: int4) {}
%aggregate aggA597(a: int4->min: int4, max: int4) {}
%aggregate aggA598(a: int4->min: int4, max: int4) {}
%aggregate aggA599(a: int4->min: int4, max: int4) {}
%aggregate aggA600(a: int4->min: int4, max: int4) {}
%aggregate aggA601(a: int4->min: int4, max: int4) {}
%aggregate aggA602(a: int4->min: int4, max: int4) {}
%aggregate aggA603(a: int4->min: int4, max: int4) {}
%aggregate aggA604(a: int4->min: int4, max: int4) {}
%aggregate aggA605(a: int4->min: int4, max: int4) {}
%aggregate aggA606(a: int4->min: int4, max: int4) {}
%aggregate aggA607(a: int4->min: int4, max: int4) {}
%aggregate aggA608(a: int4->min: int4, max: int4) {}
%aggregate aggA609(a: int4->min: int4, max: int4) {}
%aggregate aggA610(a: int4->min: int4, max: int4) {}
%aggregate aggA611(a: int4->min: int4, max: int4) {}
%aggregate aggA612(a: int4->min: int4, max: int4) {}
%aggregate aggA613(a: int4->min: int4, max: int4) {}
%aggregate aggA614(a: int4->min: int4, max: int4) {}
%aggregate aggA615(a: int4->min: int4, max: int4) {}
%aggregate aggA616(a: int4->min: int4, max: int4) {}
%aggregate aggA617(a: int4->min: int4, max: int4) {}
%aggregate aggA618(a: int4->min: int4, max: int4) {}
%aggregate aggA619(a: int4->min: int4, max: int4) {}
%aggregate aggA620(a: int4->min: int4, max: int4) {}
%aggregate aggA621(a: int4->min: int4, max: int4) {}
%aggregate aggA622(a: int4->min: int4, max: int4) {}
%aggregate aggA623(a: int4->min: int4, max: int4) {}
%aggregate aggA624(a: int4->min: int4, max: int4) {}
%aggregate aggA625(a: int4->min: int4, max: int4) {}
%aggregate aggA626(a: int4->min: int4, max: int4) {}
%aggregate aggA627(a: int4->min: int4, max: int4) {}
%aggregate aggA628(a: int4->min: int4, max: int4) {}
%aggregate aggA629(a: int4->min: int4, max: int4) {}
%aggregate aggA630(a: int4->min: int4, max: int4) {}
%aggregate aggA631(a: int4->min: int4, max: int4) {}
%aggregate aggA632(a: int4->min: int4, max: int4) {}
%aggregate aggA633(a: int4->min: int4, max: int4) {}
%aggregate aggA634(a: int4->min: int4, max: int4) {}
%aggregate aggA635(a: int4->min: int4, max: int4) {}
%aggregate aggA636(a: int4->min: int4, max: int4) {}
%aggregate aggA637(a: int4->min: int4, max: int4) {}
%aggregate aggA638(a: int4->min: int4, max: int4) {}
%aggregate aggA639(a: int4->min: int4, max: int4) {}
%aggregate aggA640(a: int4->min: int4, max: int4) {}
%aggregate aggA641(a: int4->min: int4, max: int4) {}
%aggregate aggA642(a: int4->min: int4, max: int4) {}
%aggregate aggA643(a: int4->min: int4, max: int4) {}
%aggregate aggA644(a: int4->min: int4, max: int4) {}
%aggregate aggA645(a: int4->min: int4, max: int4) {}
%aggregate aggA646(a: int4->min: int4, max: int4) {}
%aggregate aggA647(a: int4->min: int4, max: int4) {}
%aggregate aggA648(a: int4->min: int4, max: int4) {}
%aggregate aggA649(a: int4->min: int4, max: int4) {}
%aggregate aggA650(a: int4->min: int4, max: int4) {}
%aggregate aggA651(a: int4->min: int4, max: int4) {}
%aggregate aggA652(a: int4->min: int4, max: int4) {}
%aggregate aggA653(a: int4->min: int4, max: int4) {}
%aggregate aggA654(a: int4->min: int4, max: int4) {}
%aggregate aggA655(a: int4->min: int4, max: int4) {}
%aggregate aggA656(a: int4->min: int4, max: int4) {}
%aggregate aggA657(a: int4->min: int4, max: int4) {}
%aggregate aggA658(a: int4->min: int4, max: int4) {}
%aggregate aggA659(a: int4->min: int4, max: int4) {}
%aggregate aggA660(a: int4->min: int4, max: int4) {}
%aggregate aggA661(a: int4->min: int4, max: int4) {}
%aggregate aggA662(a: int4->min: int4, max: int4) {}
%aggregate aggA663(a: int4->min: int4, max: int4) {}
%aggregate aggA664(a: int4->min: int4, max: int4) {}
%aggregate aggA665(a: int4->min: int4, max: int4) {}
%aggregate aggA666(a: int4->min: int4, max: int4) {}
%aggregate aggA667(a: int4->min: int4, max: int4) {}
%aggregate aggA668(a: int4->min: int4, max: int4) {}
%aggregate aggA669(a: int4->min: int4, max: int4) {}
%aggregate aggA670(a: int4->min: int4, max: int4) {}
%aggregate aggA671(a: int4->min: int4, max: int4) {}
%aggregate aggA672(a: int4->min: int4, max: int4) {}
%aggregate aggA673(a: int4->min: int4, max: int4) {}
%aggregate aggA674(a: int4->min: int4, max: int4) {}
%aggregate aggA675(a: int4->min: int4, max: int4) {}
%aggregate aggA676(a: int4->min: int4, max: int4) {}
%aggregate aggA677(a: int4->min: int4, max: int4) {}
%aggregate aggA678(a: int4->min: int4, max: int4) {}
%aggregate aggA679(a: int4->min: int4, max: int4) {}
%aggregate aggA680(a: int4->min: int4, max: int4) {}
%aggregate aggA681(a: int4->min: int4, max: int4) {}
%aggregate aggA682(a: int4->min: int4, max: int4) {}
%aggregate aggA683(a: int4->min: int4, max: int4) {}
%aggregate aggA684(a: int4->min: int4, max: int4) {}
%aggregate aggA685(a: int4->min: int4, max: int4) {}
%aggregate aggA686(a: int4->min: int4, max: int4) {}
%aggregate aggA687(a: int4->min: int4, max: int4) {}
%aggregate aggA688(a: int4->min: int4, max: int4) {}
%aggregate aggA689(a: int4->min: int4, max: int4) {}
%aggregate aggA690(a: int4->min: int4, max: int4) {}
%aggregate aggA691(a: int4->min: int4, max: int4) {}
%aggregate aggA692(a: int4->min: int4, max: int4) {}
%aggregate aggA693(a: int4->min: int4, max: int4) {}
%aggregate aggA694(a: int4->min: int4, max: int4) {}
%aggregate aggA695(a: int4->min: int4, max: int4) {}
%aggregate aggA696(a: int4->min: int4, max: int4) {}
%aggregate aggA697(a: int4->min: int4, max: int4) {}
%aggregate aggA698(a: int4->min: int4, max: int4) {}
%aggregate aggA699(a: int4->min: int4, max: int4) {}
%aggregate aggA700(a: int4->min: int4, max: int4) {}
%aggregate aggA701(a: int4->min: int4, max: int4) {}
%aggregate aggA702(a: int4->min: int4, max: int4) {}
%aggregate aggA703(a: int4->min: int4, max: int4) {}
%aggregate aggA704(a: int4->min: int4, max: int4) {}
%aggregate aggA705(a: int4->min: int4, max: int4) {}
%aggregate aggA706(a: int4->min: int4, max: int4) {}
%aggregate aggA707(a: int4->min: int4, max: int4) {}
%aggregate aggA708(a: int4->min: int4, max: int4) {}
%aggregate aggA709(a: int4->min: int4, max: int4) {}
%aggregate aggA710(a: int4->min: int4, max: int4) {}
%aggregate aggA711(a: int4->min: int4, max: int4) {}
%aggregate aggA712(a: int4->min: int4, max: int4) {}
%aggregate aggA713(a: int4->min: int4, max: int4) {}
%aggregate aggA714(a: int4->min: int4, max: int4) {}
%aggregate aggA715(a: int4->min: int4, max: int4) {}
%aggregate aggA716(a: int4->min: int4, max: int4) {}
%aggregate aggA717(a: int4->min: int4, max: int4) {}
%aggregate aggA718(a: int4->min: int4, max: int4) {}
%aggregate aggA719(a: int4->min: int4, max: int4) {}
%aggregate aggA720(a: int4->min: int4, max: int4) {}
%aggregate aggA721(a: int4->min: int4, max: int4) {}
%aggregate aggA722(a: int4->min: int4, max: int4) {}
%aggregate aggA723(a: int4->min: int4, max: int4) {}
%aggregate aggA724(a: int4->min: int4, max: int4) {}
%aggregate aggA725(a: int4->min: int4, max: int4) {}
%aggregate aggA726(a: int4->min: int4, max: int4) {}
%aggregate aggA727(a: int4->min: int4, max: int4) {}
%aggregate aggA728(a: int4->min: int4, max: int4) {}
%aggregate aggA729(a: int4->min: int4, max: int4) {}
%aggregate aggA730(a: int4->min: int4, max: int4) {}
%aggregate aggA731(a: int4->min: int4, max: int4) {}
%aggregate aggA732(a: int4->min: int4, max: int4) {}
%aggregate aggA733(a: int4->min: int4, max: int4) {}
%aggregate aggA734(a: int4->min: int4, max: int4) {}
%aggregate aggA735(a: int4->min: int4, max: int4) {}
%aggregate aggA736(a: int4->min: int4, max: int4) {}
%aggregate aggA737(a: int4->min: int4, max: int4) {}
%aggregate aggA738(a: int4->min: int4, max: int4) {}
%aggregate aggA739(a: int4->min: int4, max: int4) {}
%aggregate aggA740(a: int4->min: int4, max: int4) {}
%aggregate aggA741(a: int4->min: int4, max: int4) {}
%aggregate aggA742(a: int4->min: int4, max: int4) {}
%aggregate aggA743(a: int4->min: int4, max: int4) {}
%aggregate aggA744(a: int4->min: int4, max: int4) {}
%aggregate aggA745(a: int4->min: int4, max: int4) {}
%aggregate aggA746(a: int4->min: int4, max: int4) {}
%aggregate aggA747(a: int4->min: int4, max: int4) {}
%aggregate aggA748(a: int4->min: int4, max: int4) {}
%aggregate aggA749(a: int4->min: int4, max: int4) {}
%aggregate aggA750(a: int4->min: int4, max: int4) {}
%aggregate aggA751(a: int4->min: int4, max: int4) {}
%aggregate aggA752(a: int4->min: int4, max: int4) {}
%aggregate aggA753(a: int4->min: int4, max: int4) {}
%aggregate aggA754(a: int4->min: int4, max: int4) {}
%aggregate aggA755(a: int4->min: int4, max: int4) {}
%aggregate aggA756(a: int4->min: int4, max: int4) {}
%aggregate aggA757(a: int4->min: int4, max: int4) {}
%aggregate aggA758(a: int4->min: int4, max: int4) {}
%aggregate aggA759(a: int4->min: int4, max: int4) {}
%aggregate aggA760(a: int4->min: int4, max: int4) {}
%aggregate aggA761(a: int4->min: int4, max: int4) {}
%aggregate aggA762(a: int4->min: int4, max: int4) {}
%aggregate aggA763(a: int4->min: int4, max: int4) {}
%aggregate aggA764(a: int4->min: int4, max: int4) {}
%aggregate aggA765(a: int4->min: int4, max: int4) {}
%aggregate aggA766(a: int4->min: int4, max: int4) {}
%aggregate aggA767(a: int4->min: int4, max: int4) {}
%aggregate aggA768(a: int4->min: int4, max: int4) {}
%aggregate aggA769(a: int4->min: int4, max: int4) {}
%aggregate aggA770(a: int4->min: int4, max: int4) {}
%aggregate aggA771(a: int4->min: int4, max: int4) {}
%aggregate aggA772(a: int4->min: int4, max: int4) {}
%aggregate aggA773(a: int4->min: int4, max: int4) {}
%aggregate aggA774(a: int4->min: int4, max: int4) {}
%aggregate aggA775(a: int4->min: int4, max: int4) {}
%aggregate aggA776(a: int4->min: int4, max: int4) {}
%aggregate aggA777(a: int4->min: int4, max: int4) {}
%aggregate aggA778(a: int4->min: int4, max: int4) {}
%aggregate aggA779(a: int4->min: int4, max: int4) {}
%aggregate aggA780(a: int4->min: int4, max: int4) {}
%aggregate aggA781(a: int4->min: int4, max: int4) {}
%aggregate aggA782(a: int4->min: int4, max: int4) {}
%aggregate aggA783(a: int4->min: int4, max: int4) {}
%aggregate aggA784(a: int4->min: int4, max: int4) {}
%aggregate aggA785(a: int4->min: int4, max: int4) {}
%aggregate aggA786(a: int4->min: int4, max: int4) {}
%aggregate aggA787(a: int4->min: int4, max: int4) {}
%aggregate aggA788(a: int4->min: int4, max: int4) {}
%aggregate aggA789(a: int4->min: int4, max: int4) {}
%aggregate aggA790(a: int4->min: int4, max: int4) {}
%aggregate aggA791(a: int4->min: int4, max: int4) {}
%aggregate aggA792(a: int4->min: int4, max: int4) {}
%aggregate aggA793(a: int4->min: int4, max: int4) {}
%aggregate aggA794(a: int4->min: int4, max: int4) {}
%aggregate aggA795(a: int4->min: int4, max: int4) {}
%aggregate aggA796(a: int4->min: int4, max: int4) {}
%aggregate aggA797(a: int4->min: int4, max: int4) {}
%aggregate aggA798(a: int4->min: int4, max: int4) {}
%aggregate aggA799(a: int4->min: int4, max: int4) {}
%aggregate aggA800(a: int4->min: int4, max: int4) {}
%aggregate aggA801(a: int4->min: int4, max: int4) {}
%aggregate aggA802(a: int4->min: int4, max: int4) {}
%aggregate aggA803(a: int4->min: int4, max: int4) {}
%aggregate aggA804(a: int4->min: int4, max: int4) {}
%aggregate aggA805(a: int4->min: int4, max: int4) {}
%aggregate aggA806(a: int4->min: int4, max: int4) {}
%aggregate aggA807(a: int4->min: int4, max: int4) {}
%aggregate aggA808(a: int4->min: int4, max: int4) {}
%aggregate aggA809(a: int4->min: int4, max: int4) {}
%aggregate aggA810(a: int4->min: int4, max: int4) {}
%aggregate aggA811(a: int4->min: int4, max: int4) {}
%aggregate aggA812(a: int4->min: int4, max: int4) {}
%aggregate aggA813(a: int4->min: int4, max: int4) {}
%aggregate aggA814(a: int4->min: int4, max: int4) {}
%aggregate aggA815(a: int4->min: int4, max: int4) {}
%aggregate aggA816(a: int4->min: int4, max: int4) {}
%aggregate aggA817(a: int4->min: int4, max: int4) {}
%aggregate aggA818(a: int4->min: int4, max: int4) {}
%aggregate aggA819(a: int4->min: int4, max: int4) {}
%aggregate aggA820(a: int4->min: int4, max: int4) {}
%aggregate aggA821(a: int4->min: int4, max: int4) {}
%aggregate aggA822(a: int4->min: int4, max: int4) {}
%aggregate aggA823(a: int4->min: int4, max: int4) {}
%aggregate aggA824(a: int4->min: int4, max: int4) {}
%aggregate aggA825(a: int4->min: int4, max: int4) {}
%aggregate aggA826(a: int4->min: int4, max: int4) {}
%aggregate aggA827(a: int4->min: int4, max: int4) {}
%aggregate aggA828(a: int4->min: int4, max: int4) {}
%aggregate aggA829(a: int4->min: int4, max: int4) {}
%aggregate aggA830(a: int4->min: int4, max: int4) {}
%aggregate aggA831(a: int4->min: int4, max: int4) {}
%aggregate aggA832(a: int4->min: int4, max: int4) {}
%aggregate aggA833(a: int4->min: int4, max: int4) {}
%aggregate aggA834(a: int4->min: int4, max: int4) {}
%aggregate aggA835(a: int4->min: int4, max: int4) {}
%aggregate aggA836(a: int4->min: int4, max: int4) {}
%aggregate aggA837(a: int4->min: int4, max: int4) {}
%aggregate aggA838(a: int4->min: int4, max: int4) {}
%aggregate aggA839(a: int4->min: int4, max: int4) {}
%aggregate aggA840(a: int4->min: int4, max: int4) {}
%aggregate aggA841(a: int4->min: int4, max: int4) {}
%aggregate aggA842(a: int4->min: int4, max: int4) {}
%aggregate aggA843(a: int4->min: int4, max: int4) {}
%aggregate aggA844(a: int4->min: int4, max: int4) {}
%aggregate aggA845(a: int4->min: int4, max: int4) {}
%aggregate aggA846(a: int4->min: int4, max: int4) {}
%aggregate aggA847(a: int4->min: int4, max: int4) {}
%aggregate aggA848(a: int4->min: int4, max: int4) {}
%aggregate aggA849(a: int4->min: int4, max: int4) {}
%aggregate aggA850(a: int4->min: int4, max: int4) {}
%aggregate aggA851(a: int4->min: int4, max: int4) {}
%aggregate aggA852(a: int4->min: int4, max: int4) {}
%aggregate aggA853(a: int4->min: int4, max: int4) {}
%aggregate aggA854(a: int4->min: int4, max: int4) {}
%aggregate aggA855(a: int4->min: int4, max: int4) {}
%aggregate aggA856(a: int4->min: int4, max: int4) {}
%aggregate aggA857(a: int4->min: int4, max: int4) {}
%aggregate aggA858(a: int4->min: int4, max: int4) {}
%aggregate aggA859(a: int4->min: int4, max: int4) {}
%aggregate aggA860(a: int4->min: int4, max: int4) {}
%aggregate aggA861(a: int4->min: int4, max: int4) {}
%aggregate aggA862(a: int4->min: int4, max: int4) {}
%aggregate aggA863(a: int4->min: int4, max: int4) {}
%aggregate aggA864(a: int4->min: int4, max: int4) {}
%aggregate aggA865(a: int4->min: int4, max: int4) {}
%aggregate aggA866(a: int4->min: int4, max: int4) {}
%aggregate aggA867(a: int4->min: int4, max: int4) {}
%aggregate aggA868(a: int4->min: int4, max: int4) {}
%aggregate aggA869(a: int4->min: int4, max: int4) {}
%aggregate aggA870(a: int4->min: int4, max: int4) {}
%aggregate aggA871(a: int4->min: int4, max: int4) {}
%aggregate aggA872(a: int4->min: int4, max: int4) {}
%aggregate aggA873(a: int4->min: int4, max: int4) {}
%aggregate aggA874(a: int4->min: int4, max: int4) {}
%aggregate aggA875(a: int4->min: int4, max: int4) {}
%aggregate aggA876(a: int4->min: int4, max: int4) {}
%aggregate aggA877(a: int4->min: int4, max: int4) {}
%aggregate aggA878(a: int4->min: int4, max: int4) {}
%aggregate aggA879(a: int4->min: int4, max: int4) {}
%aggregate aggA880(a: int4->min: int4, max: int4) {}
%aggregate aggA881(a: int4->min: int4, max: int4) {}
%aggregate aggA882(a: int4->min: int4, max: int4) {}
%aggregate aggA883(a: int4->min: int4, max: int4) {}
%aggregate aggA884(a: int4->min: int4, max: int4) {}
%aggregate aggA885(a: int4->min: int4, max: int4) {}
%aggregate aggA886(a: int4->min: int4, max: int4) {}
%aggregate aggA887(a: int4->min: int4, max: int4) {}
%aggregate aggA888(a: int4->min: int4, max: int4) {}
%aggregate aggA889(a: int4->min: int4, max: int4) {}
%aggregate aggA890(a: int4->min: int4, max: int4) {}
%aggregate aggA891(a: int4->min: int4, max: int4) {}
%aggregate aggA892(a: int4->min: int4, max: int4) {}
%aggregate aggA893(a: int4->min: int4, max: int4) {}
%aggregate aggA894(a: int4->min: int4, max: int4) {}
%aggregate aggA895(a: int4->min: int4, max: int4) {}
%aggregate aggA896(a: int4->min: int4, max: int4) {}
%aggregate aggA897(a: int4->min: int4, max: int4) {}
%aggregate aggA898(a: int4->min: int4, max: int4) {}
%aggregate aggA899(a: int4->min: int4, max: int4) {}
%aggregate aggA900(a: int4->min: int4, max: int4) {}
%aggregate aggA901(a: int4->min: int4, max: int4) {}
%aggregate aggA902(a: int4->min: int4, max: int4) {}
%aggregate aggA903(a: int4->min: int4, max: int4) {}
%aggregate aggA904(a: int4->min: int4, max: int4) {}
%aggregate aggA905(a: int4->min: int4, max: int4) {}
%aggregate aggA906(a: int4->min: int4, max: int4) {}
%aggregate aggA907(a: int4->min: int4, max: int4) {}
%aggregate aggA908(a: int4->min: int4, max: int4) {}
%aggregate aggA909(a: int4->min: int4, max: int4) {}
%aggregate aggA910(a: int4->min: int4, max: int4) {}
%aggregate aggA911(a: int4->min: int4, max: int4) {}
%aggregate aggA912(a: int4->min: int4, max: int4) {}
%aggregate aggA913(a: int4->min: int4, max: int4) {}
%aggregate aggA914(a: int4->min: int4, max: int4) {}
%aggregate aggA915(a: int4->min: int4, max: int4) {}
%aggregate aggA916(a: int4->min: int4, max: int4) {}
%aggregate aggA917(a: int4->min: int4, max: int4) {}
%aggregate aggA918(a: int4->min: int4, max: int4) {}
%aggregate aggA919(a: int4->min: int4, max: int4) {}
%aggregate aggA920(a: int4->min: int4, max: int4) {}
%aggregate aggA921(a: int4->min: int4, max: int4) {}
%aggregate aggA922(a: int4->min: int4, max: int4) {}
%aggregate aggA923(a: int4->min: int4, max: int4) {}
%aggregate aggA924(a: int4->min: int4, max: int4) {}
%aggregate aggA925(a: int4->min: int4, max: int4) {}
%aggregate aggA926(a: int4->min: int4, max: int4) {}
%aggregate aggA927(a: int4->min: int4, max: int4) {}
%aggregate aggA928(a: int4->min: int4, max: int4) {}
%aggregate aggA929(a: int4->min: int4, max: int4) {}
%aggregate aggA930(a: int4->min: int4, max: int4) {}
%aggregate aggA931(a: int4->min: int4, max: int4) {}
%aggregate aggA932(a: int4->min: int4, max: int4) {}
%aggregate aggA933(a: int4->min: int4, max: int4) {}
%aggregate aggA934(a: int4->min: int4, max: int4) {}
%aggregate aggA935(a: int4->min: int4, max: int4) {}
%aggregate aggA936(a: int4->min: int4, max: int4) {}
%aggregate aggA937(a: int4->min: int4, max: int4) {}
%aggregate aggA938(a: int4->min: int4, max: int4) {}
%aggregate aggA939(a: int4->min: int4, max: int4) {}
%aggregate aggA940(a: int4->min: int4, max: int4) {}
%aggregate aggA941(a: int4->min: int4, max: int4) {}
%aggregate aggA942(a: int4->min: int4, max: int4) {}
%aggregate aggA943(a: int4->min: int4, max: int4) {}
%aggregate aggA944(a: int4->min: int4, max: int4) {}
%aggregate aggA945(a: int4->min: int4, max: int4) {}
%aggregate aggA946(a: int4->min: int4, max: int4) {}
%aggregate aggA947(a: int4->min: int4, max: int4) {}
%aggregate aggA948(a: int4->min: int4, max: int4) {}
%aggregate aggA949(a: int4->min: int4, max: int4) {}
%aggregate aggA950(a: int4->min: int4, max: int4) {}
%aggregate aggA951(a: int4->min: int4, max: int4) {}
%aggregate aggA952(a: int4->min: int4, max: int4) {}
%aggregate aggA953(a: int4->min: int4, max: int4) {}
%aggregate aggA954(a: int4->min: int4, max: int4) {}
%aggregate aggA955(a: int4->min: int4, max: int4) {}
%aggregate aggA956(a: int4->min: int4, max: int4) {}
%aggregate aggA957(a: int4->min: int4, max: int4) {}
%aggregate aggA958(a: int4->min: int4, max: int4) {}
%aggregate aggA959(a: int4->min: int4, max: int4) {}
%aggregate aggA960(a: int4->min: int4, max: int4) {}
%aggregate aggA961(a: int4->min: int4, max: int4) {}
%aggregate aggA962(a: int4->min: int4, max: int4) {}
%aggregate aggA963(a: int4->min: int4, max: int4) {}
%aggregate aggA964(a: int4->min: int4, max: int4) {}
%aggregate aggA965(a: int4->min: int4, max: int4) {}
%aggregate aggA966(a: int4->min: int4, max: int4) {}
%aggregate aggA967(a: int4->min: int4, max: int4) {}
%aggregate aggA968(a: int4->min: int4, max: int4) {}
%aggregate aggA969(a: int4->min: int4, max: int4) {}
%aggregate aggA970(a: int4->min: int4, max: int4) {}
%aggregate aggA971(a: int4->min: int4, max: int4) {}
%aggregate aggA972(a: int4->min: int4, max: int4) {}
%aggregate aggA973(a: int4->min: int4, max: int4) {}
%aggregate aggA974(a: int4->min: int4, max: int4) {}
%aggregate aggA975(a: int4->min: int4, max: int4) {}
%aggregate aggA976(a: int4->min: int4, max: int4) {}
%aggregate aggA977(a: int4->min: int4, max: int4) {}
%aggregate aggA978(a: int4->min: int4, max: int4) {}
%aggregate aggA979(a: int4->min: int4, max: int4) {}
%aggregate aggA980(a: int4->min: int4, max: int4) {}
%aggregate aggA981(a: int4->min: int4, max: int4) {}
%aggregate aggA982(a: int4->min: int4, max: int4) {}
%aggregate aggA983(a: int4->min: int4, max: int4) {}
%aggregate aggA984(a: int4->min: int4, max: int4) {}
%aggregate aggA985(a: int4->min: int4, max: int4) {}
%aggregate aggA986(a: int4->min: int4, max: int4) {}
%aggregate aggA987(a: int4->min: int4, max: int4) {}
%aggregate aggA988(a: int4->min: int4, max: int4) {}
%aggregate aggA989(a: int4->min: int4, max: int4) {}
%aggregate aggA990(a: int4->min: int4, max: int4) {}
%aggregate aggA991(a: int4->min: int4, max: int4) {}
%aggregate aggA992(a: int4->min: int4, max: int4) {}
%aggregate aggA993(a: int4->min: int4, max: int4) {}
%aggregate aggA994(a: int4->min: int4, max: int4) {}
%aggregate aggA995(a: int4->min: int4, max: int4) {}
%aggregate aggA996(a: int4->min: int4, max: int4) {}
%aggregate aggA997(a: int4->min: int4, max: int4) {}
%aggregate aggA998(a: int4->min: int4, max: int4) {}
%aggregate aggA999(a: int4->min: int4, max: int4) {}
%aggregate aggA1000(a: int4->min: int4, max: int4) {}
%aggregate aggA1001(a: int4->min: int4, max: int4) {}
%aggregate aggA1002(a: int4->min: int4, max: int4) {}
%aggregate aggA1003(a: int4->min: int4, max: int4) {}
%aggregate aggA1004(a: int4->min: int4, max: int4) {}
%aggregate aggA1005(a: int4->min: int4, max: int4) {}
%aggregate aggA1006(a: int4->min: int4, max: int4) {}
%aggregate aggA1007(a: int4->min: int4, max: int4) {}
%aggregate aggA1008(a: int4->min: int4, max: int4) {}
%aggregate aggA1009(a: int4->min: int4, max: int4) {}
%aggregate aggA1010(a: int4->min: int4, max: int4) {}
%aggregate aggA1011(a: int4->min: int4, max: int4) {}
%aggregate aggA1012(a: int4->min: int4, max: int4) {}
%aggregate aggA1013(a: int4->min: int4, max: int4) {}
%aggregate aggA1014(a: int4->min: int4, max: int4) {}
%aggregate aggA1015(a: int4->min: int4, max: int4) {}
%aggregate aggA1016(a: int4->min: int4, max: int4) {}
%aggregate aggA1017(a: int4->min: int4, max: int4) {}
%aggregate aggA1018(a: int4->min: int4, max: int4) {}
%aggregate aggA1019(a: int4->min: int4, max: int4) {}
%aggregate aggA1020(a: int4->min: int4, max: int4) {}
%aggregate aggA1021(a: int4->min: int4, max: int4) {}
%aggregate aggA1022(a: int4->min: int4, max: int4) {}
%aggregate aggA1023(a: int4->min: int4, max: int4) {}
%aggregate aggA1024(a: int4->min: int4, max: int4) {}



B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA001(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA002(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA003(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA004(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA005(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA006(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA007(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA008(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA009(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA010(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA011(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA012(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA013(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA014(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA015(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA016(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA017(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA018(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA019(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA020(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA021(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA022(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA023(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA024(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA025(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA026(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA027(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA028(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA029(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA030(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA031(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA032(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA033(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA034(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA035(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA036(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA037(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA038(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA039(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA040(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA041(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA042(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA043(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA044(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA045(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA046(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA047(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA048(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA049(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA050(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA051(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA052(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA053(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA054(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA055(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA056(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA057(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA058(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA059(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA060(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA061(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA062(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA063(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA064(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA065(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA066(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA067(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA068(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA069(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA070(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA071(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA072(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA073(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA074(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA075(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA076(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA077(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA078(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA079(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA080(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA081(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA082(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA083(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA084(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA085(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA086(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA087(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA088(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA089(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA090(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA091(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA092(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA093(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA094(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA095(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA096(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA097(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA098(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA099(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA100(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA101(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA102(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA103(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA104(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA105(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA106(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA107(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA108(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA109(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA110(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA111(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA112(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA113(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA114(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA115(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA116(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA117(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA118(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA119(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA120(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA121(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA122(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA123(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA124(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA125(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA126(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA127(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA128(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA129(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA130(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA131(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA132(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA133(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA134(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA135(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA136(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA137(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA138(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA139(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA140(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA141(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA142(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA143(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA144(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA145(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA146(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA147(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA148(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA149(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA150(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA151(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA152(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA153(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA154(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA155(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA156(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA157(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA158(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA159(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA160(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA161(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA162(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA163(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA164(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA165(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA166(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA167(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA168(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA169(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA170(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA171(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA172(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA173(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA174(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA175(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA176(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA177(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA178(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA179(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA180(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA181(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA182(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA183(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA184(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA185(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA186(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA187(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA188(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA189(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA190(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA191(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA192(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA193(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA194(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA195(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA196(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA197(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA198(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA199(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA200(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA201(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA202(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA203(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA204(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA205(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA206(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA207(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA208(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA209(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA210(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA211(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA212(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA213(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA214(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA215(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA216(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA217(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA218(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA219(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA220(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA221(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA222(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA223(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA224(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA225(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA226(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA227(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA228(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA229(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA230(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA231(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA232(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA233(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA234(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA235(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA236(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA237(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA238(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA239(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA240(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA241(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA242(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA243(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA244(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA245(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA246(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA247(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA248(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA249(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA250(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA251(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA252(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA253(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA254(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA255(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA256(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA257(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA258(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA259(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA260(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA261(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA262(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA263(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA264(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA265(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA266(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA267(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA268(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA269(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA270(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA271(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA272(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA273(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA274(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA275(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA276(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA277(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA278(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA279(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA280(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA281(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA282(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA283(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA284(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA285(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA286(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA287(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA288(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA289(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA290(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA291(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA292(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA293(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA294(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA295(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA296(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA297(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA298(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA299(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA300(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA301(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA302(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA303(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA304(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA305(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA306(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA307(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA308(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA309(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA310(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA311(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA312(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA313(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA314(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA315(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA316(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA317(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA318(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA319(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA320(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA321(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA322(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA323(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA324(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA325(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA326(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA327(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA328(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA329(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA330(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA331(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA332(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA333(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA334(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA335(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA336(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA337(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA338(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA339(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA340(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA341(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA342(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA343(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA344(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA345(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA346(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA347(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA348(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA349(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA350(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA351(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA352(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA353(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA354(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA355(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA356(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA357(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA358(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA359(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA360(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA361(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA362(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA363(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA364(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA365(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA366(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA367(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA368(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA369(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA370(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA371(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA372(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA373(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA374(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA375(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA376(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA377(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA378(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA379(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA380(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA381(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA382(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA383(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA384(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA385(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA386(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA387(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA388(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA389(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA390(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA391(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA392(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA393(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA394(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA395(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA396(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA397(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA398(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA399(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA400(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA401(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA402(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA403(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA404(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA405(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA406(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA407(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA408(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA409(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA410(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA411(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA412(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA413(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA414(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA415(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA416(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA417(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA418(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA419(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA420(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA421(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA422(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA423(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA424(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA425(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA426(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA427(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA428(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA429(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA430(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA431(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA432(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA433(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA434(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA435(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA436(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA437(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA438(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA439(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA440(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA441(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA442(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA443(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA444(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA445(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA446(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA447(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA448(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA449(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA450(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA451(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA452(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA453(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA454(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA455(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA456(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA457(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA458(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA459(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA460(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA461(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA462(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA463(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA464(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA465(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA466(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA467(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA468(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA469(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA470(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA471(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA472(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA473(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA474(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA475(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA476(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA477(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA478(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA479(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA480(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA481(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA482(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA483(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA484(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA485(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA486(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA487(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA488(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA489(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA490(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA491(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA492(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA493(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA494(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA495(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA496(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA497(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA498(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA499(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA500(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA501(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA502(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA503(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA504(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA505(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA506(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA507(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA508(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA509(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA510(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA511(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA512(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA513(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA514(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA515(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA516(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA517(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA518(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA519(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA520(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA521(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA522(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA523(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA524(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA525(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA526(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA527(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA528(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA529(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA530(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA531(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA532(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA533(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA534(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA535(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA536(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA537(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA538(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA539(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA540(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA541(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA542(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA543(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA544(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA545(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA546(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA547(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA548(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA549(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA550(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA551(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA552(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA553(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA554(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA555(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA556(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA557(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA558(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA559(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA560(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA561(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA562(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA563(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA564(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA565(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA566(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA567(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA568(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA569(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA570(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA571(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA572(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA573(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA574(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA575(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA576(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA577(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA578(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA579(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA580(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA581(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA582(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA583(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA584(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA585(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA586(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA587(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA588(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA589(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA590(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA591(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA592(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA593(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA594(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA595(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA596(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA597(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA598(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA599(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA600(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA601(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA602(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA603(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA604(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA605(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA606(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA607(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA608(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA609(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA610(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA611(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA612(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA613(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA614(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA615(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA616(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA617(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA618(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA619(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA620(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA621(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA622(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA623(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA624(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA625(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA626(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA627(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA628(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA629(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA630(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA631(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA632(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA633(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA634(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA635(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA636(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA637(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA638(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA639(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA640(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA641(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA642(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA643(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA644(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA645(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA646(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA647(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA648(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA649(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA650(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA651(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA652(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA653(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA654(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA655(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA656(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA657(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA658(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA659(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA660(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA661(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA662(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA663(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA664(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA665(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA666(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA667(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA668(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA669(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA670(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA671(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA672(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA673(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA674(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA675(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA676(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA677(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA678(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA679(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA680(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA681(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA682(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA683(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA684(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA685(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA686(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA687(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA688(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA689(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA690(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA691(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA692(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA693(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA694(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA695(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA696(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA697(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA698(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA699(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA700(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA701(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA702(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA703(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA704(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA705(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA706(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA707(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA708(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA709(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA710(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA711(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA712(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA713(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA714(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA715(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA716(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA717(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA718(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA719(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA720(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA721(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA722(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA723(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA724(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA725(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA726(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA727(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA728(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA729(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA730(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA731(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA732(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA733(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA734(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA735(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA736(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA737(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA738(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA739(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA740(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA741(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA742(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA743(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA744(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA745(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA746(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA747(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA748(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA749(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA750(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA751(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA752(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA753(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA754(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA755(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA756(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA757(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA758(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA759(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA760(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA761(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA762(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA763(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA764(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA765(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA766(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA767(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA768(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA769(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA770(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA771(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA772(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA773(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA774(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA775(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA776(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA777(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA778(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA779(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA780(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA781(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA782(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA783(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA784(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA785(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA786(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA787(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA788(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA789(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA790(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA791(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA792(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA793(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA794(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA795(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA796(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA797(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA798(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA799(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA800(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA801(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA802(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA803(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA804(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA805(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA806(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA807(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA808(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA809(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA810(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA811(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA812(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA813(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA814(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA815(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA816(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA817(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA818(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA819(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA820(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA821(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA822(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA823(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA824(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA825(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA826(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA827(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA828(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA829(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA830(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA831(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA832(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA833(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA834(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA835(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA836(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA837(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA838(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA839(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA840(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA841(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA842(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA843(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA844(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA845(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA846(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA847(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA848(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA849(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA850(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA851(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA852(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA853(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA854(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA855(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA856(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA857(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA858(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA859(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA860(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA861(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA862(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA863(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA864(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA865(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA866(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA867(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA868(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA869(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA870(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA871(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA872(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA873(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA874(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA875(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA876(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA877(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA878(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA879(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA880(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA881(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA882(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA883(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA884(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA885(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA886(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA887(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA888(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA889(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA890(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA891(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA892(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA893(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA894(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA895(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA896(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA897(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA898(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA899(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA900(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA901(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA902(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA903(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA904(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA905(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA906(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA907(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA908(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA909(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA910(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA911(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA912(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA913(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA914(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA915(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA916(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA917(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA918(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA919(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA920(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA921(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA922(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA923(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA924(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA925(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA926(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA927(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA928(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA929(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA930(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA931(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA932(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA933(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA934(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA935(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA936(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA937(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA938(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA939(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA940(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA941(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA942(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA943(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA944(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA945(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA946(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA947(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA948(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA949(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA950(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA951(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA952(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA953(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA954(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA955(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA956(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA957(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA958(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA959(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA960(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA961(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA962(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA963(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA964(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA965(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA966(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA967(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA968(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA969(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA970(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA971(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA972(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA973(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA974(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA975(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA976(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA977(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA978(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA979(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA980(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA981(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA982(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA983(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA984(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA985(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA986(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA987(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA988(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA989(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA990(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA991(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA992(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA993(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA994(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA995(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA996(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA997(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA998(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA999(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1000(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1001(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1002(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1003(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1004(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1005(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1006(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1007(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1008(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1009(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1010(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1011(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1012(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1013(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1014(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1015(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1016(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1017(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1018(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1019(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1020(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1021(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1022(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1023(b, min, max).
null(0) :- B1(a, min, max) .
B1(a, min, max) :- A1(a, b) GROUP-BY(a) aggA1024(b, min, max).
null(0) :- B1(a, min, max) .

