/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-09-13
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
} B;

typedef struct C {
    int32_t dtlReservedCount;
    int32_t a;
} C;

typedef struct D {
    int32_t a;
    int32_t b;
} D;

#pragma pack(0)

int32_t dtl_ext_func_A64func001(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A64agg001(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    C *inp1 = (C *)tuple1;
    C *inp2 = (C *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A64agg001(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    C *inpStruct;
    D *outStruct;
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_ext_func_A64func002(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A64agg002(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    C *inp1 = (C *)tuple1;
    C *inp2 = (C *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A64agg002(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    C *inpStruct;
    D *outStruct;
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_ext_func_A64func003(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A64agg003(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    C *inp1 = (C *)tuple1;
    C *inp2 = (C *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A64agg003(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    C *inpStruct;
    D *outStruct;
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_ext_func_A64func004(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A64agg004(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    C *inp1 = (C *)tuple1;
    C *inp2 = (C *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A64agg004(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    C *inpStruct;
    D *outStruct;
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_ext_func_A64func005(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A64agg005(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    C *inp1 = (C *)tuple1;
    C *inp2 = (C *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A64agg005(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    C *inpStruct;
    D *outStruct;
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_ext_func_A64func006(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A64agg006(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    C *inp1 = (C *)tuple1;
    C *inp2 = (C *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A64agg006(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    C *inpStruct;
    D *outStruct;
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_ext_func_A64func007(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A64agg007(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    C *inp1 = (C *)tuple1;
    C *inp2 = (C *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A64agg007(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    C *inpStruct;
    D *outStruct;
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_ext_func_A64func008(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A64agg008(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    C *inp1 = (C *)tuple1;
    C *inp2 = (C *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A64agg008(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    C *inpStruct;
    D *outStruct;
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
