/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-09-13
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
} B;

#pragma pack(0)

int32_t dtl_ext_func_funcA001(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA002(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA003(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA004(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA005(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA006(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA007(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA008(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA009(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA010(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA011(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA012(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA013(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA014(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA015(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA016(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA017(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA018(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA019(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA020(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA021(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA022(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA023(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA024(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA025(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA026(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA027(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA028(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA029(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA030(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA031(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA032(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA033(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA034(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA035(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA036(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA037(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA038(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA039(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA040(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA041(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA042(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA043(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA044(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA045(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA046(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA047(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA048(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA049(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA050(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA051(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA052(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA053(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA054(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA055(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA056(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA057(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA058(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA059(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA060(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA061(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA062(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA063(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA064(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA065(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA066(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA067(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA068(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA069(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA070(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA071(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA072(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA073(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA074(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA075(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA076(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA077(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA078(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA079(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA080(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA081(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA082(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA083(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA084(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA085(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA086(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA087(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA088(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA089(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA090(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA091(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA092(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA093(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA094(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA095(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA096(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA097(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA098(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA099(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA100(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA101(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA102(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA103(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA104(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA105(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA106(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA107(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA108(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA109(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA110(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA111(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA112(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA113(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA114(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA115(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA116(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA117(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA118(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA119(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA120(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA121(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA122(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA123(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA124(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA125(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA126(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA127(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA128(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA129(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA130(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA131(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA132(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA133(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA134(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA135(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA136(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA137(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA138(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA139(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA140(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA141(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA142(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA143(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA144(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA145(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA146(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA147(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA148(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA149(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA150(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA151(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA152(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA153(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA154(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA155(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA156(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA157(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA158(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA159(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA160(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA161(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA162(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA163(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA164(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA165(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA166(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA167(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA168(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA169(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA170(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA171(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA172(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA173(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA174(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA175(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA176(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA177(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA178(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA179(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA180(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA181(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA182(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA183(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA184(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA185(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA186(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA187(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA188(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA189(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA190(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA191(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA192(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA193(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA194(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA195(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA196(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA197(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA198(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA199(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA200(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA201(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA202(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA203(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA204(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA205(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA206(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA207(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA208(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA209(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA210(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA211(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA212(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA213(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA214(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA215(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA216(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA217(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA218(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA219(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA220(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA221(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA222(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA223(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA224(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA225(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA226(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA227(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA228(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA229(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA230(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA231(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA232(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA233(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA234(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA235(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA236(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA237(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA238(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA239(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA240(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA241(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA242(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA243(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA244(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA245(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA246(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA247(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA248(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA249(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA250(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA251(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA252(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA253(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA254(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA255(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA256(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA257(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA258(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA259(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA260(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA261(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA262(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA263(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA264(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA265(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA266(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA267(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA268(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA269(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA270(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA271(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA272(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA273(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA274(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA275(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA276(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA277(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA278(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA279(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA280(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA281(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA282(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA283(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA284(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA285(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA286(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA287(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA288(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA289(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA290(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA291(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA292(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA293(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA294(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA295(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA296(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA297(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA298(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA299(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA300(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA301(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA302(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA303(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA304(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA305(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA306(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA307(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA308(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA309(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA310(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA311(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA312(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA313(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA314(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA315(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA316(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA317(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA318(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA319(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA320(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA321(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA322(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA323(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA324(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA325(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA326(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA327(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA328(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA329(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA330(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA331(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA332(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA333(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA334(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA335(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA336(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA337(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA338(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA339(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA340(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA341(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA342(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA343(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA344(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA345(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA346(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA347(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA348(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA349(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA350(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA351(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA352(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA353(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA354(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA355(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA356(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA357(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA358(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA359(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA360(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA361(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA362(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA363(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA364(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA365(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA366(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA367(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA368(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA369(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA370(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA371(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA372(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA373(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA374(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA375(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA376(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA377(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA378(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA379(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA380(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA381(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA382(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA383(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA384(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA385(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA386(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA387(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA388(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA389(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA390(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA391(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA392(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA393(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA394(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA395(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA396(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA397(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA398(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA399(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA400(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA401(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA402(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA403(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA404(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA405(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA406(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA407(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA408(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA409(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA410(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA411(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA412(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA413(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA414(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA415(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA416(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA417(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA418(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA419(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA420(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA421(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA422(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA423(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA424(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA425(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA426(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA427(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA428(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA429(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA430(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA431(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA432(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA433(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA434(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA435(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA436(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA437(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA438(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA439(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA440(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA441(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA442(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA443(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA444(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA445(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA446(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA447(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA448(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA449(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA450(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA451(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA452(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA453(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA454(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA455(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA456(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA457(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA458(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA459(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA460(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA461(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA462(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA463(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA464(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA465(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA466(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA467(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA468(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA469(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA470(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA471(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA472(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA473(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA474(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA475(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA476(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA477(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA478(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA479(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA480(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA481(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA482(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA483(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA484(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA485(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA486(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA487(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA488(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA489(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA490(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA491(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA492(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA493(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA494(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA495(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA496(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA497(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA498(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA499(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA500(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA501(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA502(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA503(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA504(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA505(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA506(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA507(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA508(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA509(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA510(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA511(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA512(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA513(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA514(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA515(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA516(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA517(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA518(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA519(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA520(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA521(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA522(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA523(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA524(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA525(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA526(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA527(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA528(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA529(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA530(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA531(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA532(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA533(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA534(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA535(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA536(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA537(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA538(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA539(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA540(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA541(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA542(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA543(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA544(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA545(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA546(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA547(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA548(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA549(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA550(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA551(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA552(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA553(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA554(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA555(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA556(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA557(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA558(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA559(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA560(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA561(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA562(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA563(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA564(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA565(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA566(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA567(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA568(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA569(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA570(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA571(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA572(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA573(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA574(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA575(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA576(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA577(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA578(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA579(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA580(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA581(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA582(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA583(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA584(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA585(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA586(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA587(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA588(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA589(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA590(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA591(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA592(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA593(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA594(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA595(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA596(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA597(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA598(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA599(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA600(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA601(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA602(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA603(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA604(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA605(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA606(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA607(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA608(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA609(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA610(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA611(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA612(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA613(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA614(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA615(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA616(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA617(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA618(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA619(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA620(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA621(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA622(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA623(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA624(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA625(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA626(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA627(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA628(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA629(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA630(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA631(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA632(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA633(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA634(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA635(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA636(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA637(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA638(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA639(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA640(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA641(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA642(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA643(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA644(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA645(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA646(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA647(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA648(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA649(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA650(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA651(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA652(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA653(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA654(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA655(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA656(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA657(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA658(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA659(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA660(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA661(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA662(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA663(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA664(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA665(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA666(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA667(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA668(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA669(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA670(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA671(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA672(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA673(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA674(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA675(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA676(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA677(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA678(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA679(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA680(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA681(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA682(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA683(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA684(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA685(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA686(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA687(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA688(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA689(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA690(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA691(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA692(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA693(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA694(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA695(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA696(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA697(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA698(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA699(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA700(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA701(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA702(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA703(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA704(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA705(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA706(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA707(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA708(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA709(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA710(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA711(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA712(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA713(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA714(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA715(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA716(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA717(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA718(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA719(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA720(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA721(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA722(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA723(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA724(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA725(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA726(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA727(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA728(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA729(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA730(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA731(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA732(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA733(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA734(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA735(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA736(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA737(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA738(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA739(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA740(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA741(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA742(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA743(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA744(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA745(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA746(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA747(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA748(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA749(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA750(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA751(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA752(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA753(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA754(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA755(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA756(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA757(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA758(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA759(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA760(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA761(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA762(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA763(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA764(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA765(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA766(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA767(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA768(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA769(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA770(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA771(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA772(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA773(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA774(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA775(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA776(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA777(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA778(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA779(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA780(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA781(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA782(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA783(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA784(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA785(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA786(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA787(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA788(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA789(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA790(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA791(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA792(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA793(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA794(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA795(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA796(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA797(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA798(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA799(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA800(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA801(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA802(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA803(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA804(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA805(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA806(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA807(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA808(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA809(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA810(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA811(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA812(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA813(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA814(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA815(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA816(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA817(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA818(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA819(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA820(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA821(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA822(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA823(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA824(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA825(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA826(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA827(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA828(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA829(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA830(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA831(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA832(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA833(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA834(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA835(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA836(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA837(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA838(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA839(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA840(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA841(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA842(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA843(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA844(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA845(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA846(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA847(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA848(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA849(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA850(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA851(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA852(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA853(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA854(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA855(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA856(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA857(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA858(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA859(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA860(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA861(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA862(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA863(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA864(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA865(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA866(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA867(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA868(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA869(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA870(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA871(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA872(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA873(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA874(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA875(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA876(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA877(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA878(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA879(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA880(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA881(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA882(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA883(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA884(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA885(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA886(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA887(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA888(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA889(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA890(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA891(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA892(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA893(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA894(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA895(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA896(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA897(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA898(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA899(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA900(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA901(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA902(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA903(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA904(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA905(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA906(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA907(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA908(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA909(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA910(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA911(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA912(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA913(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA914(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA915(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA916(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA917(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA918(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA919(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA920(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA921(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA922(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA923(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA924(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA925(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA926(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA927(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA928(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA929(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA930(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA931(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA932(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA933(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA934(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA935(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA936(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA937(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA938(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA939(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA940(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA941(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA942(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA943(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA944(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA945(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA946(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA947(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA948(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA949(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA950(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA951(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA952(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA953(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA954(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA955(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA956(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA957(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA958(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA959(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA960(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA961(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA962(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA963(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA964(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA965(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA966(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA967(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA968(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA969(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA970(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA971(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA972(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA973(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA974(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA975(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA976(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA977(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA978(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA979(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA980(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA981(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA982(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA983(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA984(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA985(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA986(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA987(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA988(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA989(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA990(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA991(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA992(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA993(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA994(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA995(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA996(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA997(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA998(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA999(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1000(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1001(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1002(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1003(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1004(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1005(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1006(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1007(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1008(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1009(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1010(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1011(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1012(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1013(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1014(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1015(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1016(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1017(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1018(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1019(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1020(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1021(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1022(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1023(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_funcA1024(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
