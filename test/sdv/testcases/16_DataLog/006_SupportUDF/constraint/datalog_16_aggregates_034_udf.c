/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-09-13
 */
#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t a;
} A;

typedef struct B {
    int32_t a;
    int32_t b;
} B;

#pragma pack(0)

int32_t dtl_agg_func_A34agg001(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg002(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg003(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg004(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg005(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg006(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg007(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg008(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg009(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg010(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg011(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg012(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg013(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg014(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg015(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_func_A34agg016(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        if (inpStruct->a > max) {
            max = inpStruct->a;
        }
        if (inpStruct->a < max) {
            min = inpStruct->a;
        }
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
