%table A(a:int4, b:int4)
%table C(a:int4, min:int4, max:int4)

%aggregate agg001(a: int4->min: int4, max: int4) {ordered}
%aggregate agg002(a: int4->min: int4, max: int4) {ordered}
%aggregate agg003(a: int4->min: int4, max: int4) {ordered}
%aggregate agg004(a: int4->min: int4, max: int4) {ordered}
%aggregate agg005(a: int4->min: int4, max: int4) {ordered}
%aggregate agg006(a: int4->min: int4, max: int4) {ordered}
%aggregate agg007(a: int4->min: int4, max: int4) {ordered}
%aggregate agg008(a: int4->min: int4, max: int4) {ordered}
%aggregate agg009(a: int4->min: int4, max: int4) {ordered}
%aggregate agg010(a: int4->min: int4, max: int4) {ordered}
%aggregate agg011(a: int4->min: int4, max: int4) {ordered}
%aggregate agg012(a: int4->min: int4, max: int4) {ordered}
%aggregate agg013(a: int4->min: int4, max: int4) {ordered}
%aggregate agg014(a: int4->min: int4, max: int4) {ordered}
%aggregate agg015(a: int4->min: int4, max: int4) {ordered}
%aggregate agg016(a: int4->min: int4, max: int4) {ordered}
%aggregate agg017(a: int4->min: int4, max: int4) {ordered}
%aggregate agg018(a: int4->min: int4, max: int4) {ordered}
%aggregate agg019(a: int4->min: int4, max: int4) {ordered}
%aggregate agg020(a: int4->min: int4, max: int4) {ordered}
%aggregate agg021(a: int4->min: int4, max: int4) {ordered}
%aggregate agg022(a: int4->min: int4, max: int4) {ordered}
%aggregate agg023(a: int4->min: int4, max: int4) {ordered}
%aggregate agg024(a: int4->min: int4, max: int4) {ordered}
%aggregate agg025(a: int4->min: int4, max: int4) {ordered}
%aggregate agg026(a: int4->min: int4, max: int4) {ordered}
%aggregate agg027(a: int4->min: int4, max: int4) {ordered}
%aggregate agg028(a: int4->min: int4, max: int4) {ordered}
%aggregate agg029(a: int4->min: int4, max: int4) {ordered}
%aggregate agg030(a: int4->min: int4, max: int4) {ordered}
%aggregate agg031(a: int4->min: int4, max: int4) {ordered}
%aggregate agg032(a: int4->min: int4, max: int4) {ordered}
%aggregate agg033(a: int4->min: int4, max: int4) {ordered}
%aggregate agg034(a: int4->min: int4, max: int4) {ordered}
%aggregate agg035(a: int4->min: int4, max: int4) {ordered}
%aggregate agg036(a: int4->min: int4, max: int4) {ordered}
%aggregate agg037(a: int4->min: int4, max: int4) {ordered}
%aggregate agg038(a: int4->min: int4, max: int4) {ordered}
%aggregate agg039(a: int4->min: int4, max: int4) {ordered}
%aggregate agg040(a: int4->min: int4, max: int4) {ordered}
%aggregate agg041(a: int4->min: int4, max: int4) {ordered}
%aggregate agg042(a: int4->min: int4, max: int4) {ordered}
%aggregate agg043(a: int4->min: int4, max: int4) {ordered}
%aggregate agg044(a: int4->min: int4, max: int4) {ordered}
%aggregate agg045(a: int4->min: int4, max: int4) {ordered}
%aggregate agg046(a: int4->min: int4, max: int4) {ordered}
%aggregate agg047(a: int4->min: int4, max: int4) {ordered}
%aggregate agg048(a: int4->min: int4, max: int4) {ordered}
%aggregate agg049(a: int4->min: int4, max: int4) {ordered}
%aggregate agg050(a: int4->min: int4, max: int4) {ordered}
%aggregate agg051(a: int4->min: int4, max: int4) {ordered}
%aggregate agg052(a: int4->min: int4, max: int4) {ordered}
%aggregate agg053(a: int4->min: int4, max: int4) {ordered}
%aggregate agg054(a: int4->min: int4, max: int4) {ordered}
%aggregate agg055(a: int4->min: int4, max: int4) {ordered}
%aggregate agg056(a: int4->min: int4, max: int4) {ordered}
%aggregate agg057(a: int4->min: int4, max: int4) {ordered}
%aggregate agg058(a: int4->min: int4, max: int4) {ordered}
%aggregate agg059(a: int4->min: int4, max: int4) {ordered}
%aggregate agg060(a: int4->min: int4, max: int4) {ordered}
%aggregate agg061(a: int4->min: int4, max: int4) {ordered}
%aggregate agg062(a: int4->min: int4, max: int4) {ordered}
%aggregate agg063(a: int4->min: int4, max: int4) {ordered}
%aggregate agg064(a: int4->min: int4, max: int4) {ordered}
%aggregate agg065(a: int4->min: int4, max: int4) {ordered}
%aggregate agg066(a: int4->min: int4, max: int4) {ordered}
%aggregate agg067(a: int4->min: int4, max: int4) {ordered}
%aggregate agg068(a: int4->min: int4, max: int4) {ordered}
%aggregate agg069(a: int4->min: int4, max: int4) {ordered}
%aggregate agg070(a: int4->min: int4, max: int4) {ordered}
%aggregate agg071(a: int4->min: int4, max: int4) {ordered}
%aggregate agg072(a: int4->min: int4, max: int4) {ordered}
%aggregate agg073(a: int4->min: int4, max: int4) {ordered}
%aggregate agg074(a: int4->min: int4, max: int4) {ordered}
%aggregate agg075(a: int4->min: int4, max: int4) {ordered}
%aggregate agg076(a: int4->min: int4, max: int4) {ordered}
%aggregate agg077(a: int4->min: int4, max: int4) {ordered}
%aggregate agg078(a: int4->min: int4, max: int4) {ordered}
%aggregate agg079(a: int4->min: int4, max: int4) {ordered}
%aggregate agg080(a: int4->min: int4, max: int4) {ordered}
%aggregate agg081(a: int4->min: int4, max: int4) {ordered}
%aggregate agg082(a: int4->min: int4, max: int4) {ordered}
%aggregate agg083(a: int4->min: int4, max: int4) {ordered}
%aggregate agg084(a: int4->min: int4, max: int4) {ordered}
%aggregate agg085(a: int4->min: int4, max: int4) {ordered}
%aggregate agg086(a: int4->min: int4, max: int4) {ordered}
%aggregate agg087(a: int4->min: int4, max: int4) {ordered}
%aggregate agg088(a: int4->min: int4, max: int4) {ordered}
%aggregate agg089(a: int4->min: int4, max: int4) {ordered}
%aggregate agg090(a: int4->min: int4, max: int4) {ordered}
%aggregate agg091(a: int4->min: int4, max: int4) {ordered}
%aggregate agg092(a: int4->min: int4, max: int4) {ordered}
%aggregate agg093(a: int4->min: int4, max: int4) {ordered}
%aggregate agg094(a: int4->min: int4, max: int4) {ordered}
%aggregate agg095(a: int4->min: int4, max: int4) {ordered}
%aggregate agg096(a: int4->min: int4, max: int4) {ordered}
%aggregate agg097(a: int4->min: int4, max: int4) {ordered}
%aggregate agg098(a: int4->min: int4, max: int4) {ordered}
%aggregate agg099(a: int4->min: int4, max: int4) {ordered}
%aggregate agg100(a: int4->min: int4, max: int4) {ordered}
%aggregate agg101(a: int4->min: int4, max: int4) {ordered}
%aggregate agg102(a: int4->min: int4, max: int4) {ordered}
%aggregate agg103(a: int4->min: int4, max: int4) {ordered}
%aggregate agg104(a: int4->min: int4, max: int4) {ordered}
%aggregate agg105(a: int4->min: int4, max: int4) {ordered}
%aggregate agg106(a: int4->min: int4, max: int4) {ordered}
%aggregate agg107(a: int4->min: int4, max: int4) {ordered}
%aggregate agg108(a: int4->min: int4, max: int4) {ordered}
%aggregate agg109(a: int4->min: int4, max: int4) {ordered}
%aggregate agg110(a: int4->min: int4, max: int4) {ordered}
%aggregate agg111(a: int4->min: int4, max: int4) {ordered}
%aggregate agg112(a: int4->min: int4, max: int4) {ordered}
%aggregate agg113(a: int4->min: int4, max: int4) {ordered}
%aggregate agg114(a: int4->min: int4, max: int4) {ordered}
%aggregate agg115(a: int4->min: int4, max: int4) {ordered}
%aggregate agg116(a: int4->min: int4, max: int4) {ordered}
%aggregate agg117(a: int4->min: int4, max: int4) {ordered}
%aggregate agg118(a: int4->min: int4, max: int4) {ordered}
%aggregate agg119(a: int4->min: int4, max: int4) {ordered}
%aggregate agg120(a: int4->min: int4, max: int4) {ordered}
%aggregate agg121(a: int4->min: int4, max: int4) {ordered}
%aggregate agg122(a: int4->min: int4, max: int4) {ordered}
%aggregate agg123(a: int4->min: int4, max: int4) {ordered}
%aggregate agg124(a: int4->min: int4, max: int4) {ordered}
%aggregate agg125(a: int4->min: int4, max: int4) {ordered}
%aggregate agg126(a: int4->min: int4, max: int4) {ordered}
%aggregate agg127(a: int4->min: int4, max: int4) {ordered}
%aggregate agg128(a: int4->min: int4, max: int4) {ordered}
%aggregate agg129(a: int4->min: int4, max: int4) {ordered}
%aggregate agg130(a: int4->min: int4, max: int4) {ordered}
%aggregate agg131(a: int4->min: int4, max: int4) {ordered}
%aggregate agg132(a: int4->min: int4, max: int4) {ordered}
%aggregate agg133(a: int4->min: int4, max: int4) {ordered}
%aggregate agg134(a: int4->min: int4, max: int4) {ordered}
%aggregate agg135(a: int4->min: int4, max: int4) {ordered}
%aggregate agg136(a: int4->min: int4, max: int4) {ordered}
%aggregate agg137(a: int4->min: int4, max: int4) {ordered}
%aggregate agg138(a: int4->min: int4, max: int4) {ordered}
%aggregate agg139(a: int4->min: int4, max: int4) {ordered}
%aggregate agg140(a: int4->min: int4, max: int4) {ordered}
%aggregate agg141(a: int4->min: int4, max: int4) {ordered}
%aggregate agg142(a: int4->min: int4, max: int4) {ordered}
%aggregate agg143(a: int4->min: int4, max: int4) {ordered}
%aggregate agg144(a: int4->min: int4, max: int4) {ordered}
%aggregate agg145(a: int4->min: int4, max: int4) {ordered}
%aggregate agg146(a: int4->min: int4, max: int4) {ordered}
%aggregate agg147(a: int4->min: int4, max: int4) {ordered}
%aggregate agg148(a: int4->min: int4, max: int4) {ordered}
%aggregate agg149(a: int4->min: int4, max: int4) {ordered}
%aggregate agg150(a: int4->min: int4, max: int4) {ordered}
%aggregate agg151(a: int4->min: int4, max: int4) {ordered}
%aggregate agg152(a: int4->min: int4, max: int4) {ordered}
%aggregate agg153(a: int4->min: int4, max: int4) {ordered}
%aggregate agg154(a: int4->min: int4, max: int4) {ordered}
%aggregate agg155(a: int4->min: int4, max: int4) {ordered}
%aggregate agg156(a: int4->min: int4, max: int4) {ordered}
%aggregate agg157(a: int4->min: int4, max: int4) {ordered}
%aggregate agg158(a: int4->min: int4, max: int4) {ordered}
%aggregate agg159(a: int4->min: int4, max: int4) {ordered}
%aggregate agg160(a: int4->min: int4, max: int4) {ordered}
%aggregate agg161(a: int4->min: int4, max: int4) {ordered}
%aggregate agg162(a: int4->min: int4, max: int4) {ordered}
%aggregate agg163(a: int4->min: int4, max: int4) {ordered}
%aggregate agg164(a: int4->min: int4, max: int4) {ordered}
%aggregate agg165(a: int4->min: int4, max: int4) {ordered}
%aggregate agg166(a: int4->min: int4, max: int4) {ordered}
%aggregate agg167(a: int4->min: int4, max: int4) {ordered}
%aggregate agg168(a: int4->min: int4, max: int4) {ordered}
%aggregate agg169(a: int4->min: int4, max: int4) {ordered}
%aggregate agg170(a: int4->min: int4, max: int4) {ordered}
%aggregate agg171(a: int4->min: int4, max: int4) {ordered}
%aggregate agg172(a: int4->min: int4, max: int4) {ordered}
%aggregate agg173(a: int4->min: int4, max: int4) {ordered}
%aggregate agg174(a: int4->min: int4, max: int4) {ordered}
%aggregate agg175(a: int4->min: int4, max: int4) {ordered}
%aggregate agg176(a: int4->min: int4, max: int4) {ordered}
%aggregate agg177(a: int4->min: int4, max: int4) {ordered}
%aggregate agg178(a: int4->min: int4, max: int4) {ordered}
%aggregate agg179(a: int4->min: int4, max: int4) {ordered}
%aggregate agg180(a: int4->min: int4, max: int4) {ordered}
%aggregate agg181(a: int4->min: int4, max: int4) {ordered}
%aggregate agg182(a: int4->min: int4, max: int4) {ordered}
%aggregate agg183(a: int4->min: int4, max: int4) {ordered}
%aggregate agg184(a: int4->min: int4, max: int4) {ordered}
%aggregate agg185(a: int4->min: int4, max: int4) {ordered}
%aggregate agg186(a: int4->min: int4, max: int4) {ordered}
%aggregate agg187(a: int4->min: int4, max: int4) {ordered}
%aggregate agg188(a: int4->min: int4, max: int4) {ordered}
%aggregate agg189(a: int4->min: int4, max: int4) {ordered}
%aggregate agg190(a: int4->min: int4, max: int4) {ordered}
%aggregate agg191(a: int4->min: int4, max: int4) {ordered}
%aggregate agg192(a: int4->min: int4, max: int4) {ordered}
%aggregate agg193(a: int4->min: int4, max: int4) {ordered}
%aggregate agg194(a: int4->min: int4, max: int4) {ordered}
%aggregate agg195(a: int4->min: int4, max: int4) {ordered}
%aggregate agg196(a: int4->min: int4, max: int4) {ordered}
%aggregate agg197(a: int4->min: int4, max: int4) {ordered}
%aggregate agg198(a: int4->min: int4, max: int4) {ordered}
%aggregate agg199(a: int4->min: int4, max: int4) {ordered}
%aggregate agg200(a: int4->min: int4, max: int4) {ordered}
%aggregate agg201(a: int4->min: int4, max: int4) {ordered}
%aggregate agg202(a: int4->min: int4, max: int4) {ordered}
%aggregate agg203(a: int4->min: int4, max: int4) {ordered}
%aggregate agg204(a: int4->min: int4, max: int4) {ordered}
%aggregate agg205(a: int4->min: int4, max: int4) {ordered}
%aggregate agg206(a: int4->min: int4, max: int4) {ordered}
%aggregate agg207(a: int4->min: int4, max: int4) {ordered}
%aggregate agg208(a: int4->min: int4, max: int4) {ordered}
%aggregate agg209(a: int4->min: int4, max: int4) {ordered}
%aggregate agg210(a: int4->min: int4, max: int4) {ordered}
%aggregate agg211(a: int4->min: int4, max: int4) {ordered}
%aggregate agg212(a: int4->min: int4, max: int4) {ordered}
%aggregate agg213(a: int4->min: int4, max: int4) {ordered}
%aggregate agg214(a: int4->min: int4, max: int4) {ordered}
%aggregate agg215(a: int4->min: int4, max: int4) {ordered}
%aggregate agg216(a: int4->min: int4, max: int4) {ordered}
%aggregate agg217(a: int4->min: int4, max: int4) {ordered}
%aggregate agg218(a: int4->min: int4, max: int4) {ordered}
%aggregate agg219(a: int4->min: int4, max: int4) {ordered}
%aggregate agg220(a: int4->min: int4, max: int4) {ordered}
%aggregate agg221(a: int4->min: int4, max: int4) {ordered}
%aggregate agg222(a: int4->min: int4, max: int4) {ordered}
%aggregate agg223(a: int4->min: int4, max: int4) {ordered}
%aggregate agg224(a: int4->min: int4, max: int4) {ordered}
%aggregate agg225(a: int4->min: int4, max: int4) {ordered}
%aggregate agg226(a: int4->min: int4, max: int4) {ordered}
%aggregate agg227(a: int4->min: int4, max: int4) {ordered}
%aggregate agg228(a: int4->min: int4, max: int4) {ordered}
%aggregate agg229(a: int4->min: int4, max: int4) {ordered}
%aggregate agg230(a: int4->min: int4, max: int4) {ordered}
%aggregate agg231(a: int4->min: int4, max: int4) {ordered}
%aggregate agg232(a: int4->min: int4, max: int4) {ordered}
%aggregate agg233(a: int4->min: int4, max: int4) {ordered}
%aggregate agg234(a: int4->min: int4, max: int4) {ordered}
%aggregate agg235(a: int4->min: int4, max: int4) {ordered}
%aggregate agg236(a: int4->min: int4, max: int4) {ordered}
%aggregate agg237(a: int4->min: int4, max: int4) {ordered}
%aggregate agg238(a: int4->min: int4, max: int4) {ordered}
%aggregate agg239(a: int4->min: int4, max: int4) {ordered}
%aggregate agg240(a: int4->min: int4, max: int4) {ordered}
%aggregate agg241(a: int4->min: int4, max: int4) {ordered}
%aggregate agg242(a: int4->min: int4, max: int4) {ordered}
%aggregate agg243(a: int4->min: int4, max: int4) {ordered}
%aggregate agg244(a: int4->min: int4, max: int4) {ordered}
%aggregate agg245(a: int4->min: int4, max: int4) {ordered}
%aggregate agg246(a: int4->min: int4, max: int4) {ordered}
%aggregate agg247(a: int4->min: int4, max: int4) {ordered}
%aggregate agg248(a: int4->min: int4, max: int4) {ordered}
%aggregate agg249(a: int4->min: int4, max: int4) {ordered}
%aggregate agg250(a: int4->min: int4, max: int4) {ordered}
%aggregate agg251(a: int4->min: int4, max: int4) {ordered}
%aggregate agg252(a: int4->min: int4, max: int4) {ordered}
%aggregate agg253(a: int4->min: int4, max: int4) {ordered}
%aggregate agg254(a: int4->min: int4, max: int4) {ordered}
%aggregate agg255(a: int4->min: int4, max: int4) {ordered}
%aggregate agg256(a: int4->min: int4, max: int4) {ordered}
%aggregate agg257(a: int4->min: int4, max: int4) {ordered}
%aggregate agg258(a: int4->min: int4, max: int4) {ordered}
%aggregate agg259(a: int4->min: int4, max: int4) {ordered}
%aggregate agg260(a: int4->min: int4, max: int4) {ordered}
%aggregate agg261(a: int4->min: int4, max: int4) {ordered}
%aggregate agg262(a: int4->min: int4, max: int4) {ordered}
%aggregate agg263(a: int4->min: int4, max: int4) {ordered}
%aggregate agg264(a: int4->min: int4, max: int4) {ordered}
%aggregate agg265(a: int4->min: int4, max: int4) {ordered}
%aggregate agg266(a: int4->min: int4, max: int4) {ordered}
%aggregate agg267(a: int4->min: int4, max: int4) {ordered}
%aggregate agg268(a: int4->min: int4, max: int4) {ordered}
%aggregate agg269(a: int4->min: int4, max: int4) {ordered}
%aggregate agg270(a: int4->min: int4, max: int4) {ordered}
%aggregate agg271(a: int4->min: int4, max: int4) {ordered}
%aggregate agg272(a: int4->min: int4, max: int4) {ordered}
%aggregate agg273(a: int4->min: int4, max: int4) {ordered}
%aggregate agg274(a: int4->min: int4, max: int4) {ordered}
%aggregate agg275(a: int4->min: int4, max: int4) {ordered}
%aggregate agg276(a: int4->min: int4, max: int4) {ordered}
%aggregate agg277(a: int4->min: int4, max: int4) {ordered}
%aggregate agg278(a: int4->min: int4, max: int4) {ordered}
%aggregate agg279(a: int4->min: int4, max: int4) {ordered}
%aggregate agg280(a: int4->min: int4, max: int4) {ordered}
%aggregate agg281(a: int4->min: int4, max: int4) {ordered}
%aggregate agg282(a: int4->min: int4, max: int4) {ordered}
%aggregate agg283(a: int4->min: int4, max: int4) {ordered}
%aggregate agg284(a: int4->min: int4, max: int4) {ordered}
%aggregate agg285(a: int4->min: int4, max: int4) {ordered}
%aggregate agg286(a: int4->min: int4, max: int4) {ordered}
%aggregate agg287(a: int4->min: int4, max: int4) {ordered}
%aggregate agg288(a: int4->min: int4, max: int4) {ordered}
%aggregate agg289(a: int4->min: int4, max: int4) {ordered}
%aggregate agg290(a: int4->min: int4, max: int4) {ordered}
%aggregate agg291(a: int4->min: int4, max: int4) {ordered}
%aggregate agg292(a: int4->min: int4, max: int4) {ordered}
%aggregate agg293(a: int4->min: int4, max: int4) {ordered}
%aggregate agg294(a: int4->min: int4, max: int4) {ordered}
%aggregate agg295(a: int4->min: int4, max: int4) {ordered}
%aggregate agg296(a: int4->min: int4, max: int4) {ordered}
%aggregate agg297(a: int4->min: int4, max: int4) {ordered}
%aggregate agg298(a: int4->min: int4, max: int4) {ordered}
%aggregate agg299(a: int4->min: int4, max: int4) {ordered}
%aggregate agg300(a: int4->min: int4, max: int4) {ordered}
%aggregate agg301(a: int4->min: int4, max: int4) {ordered}
%aggregate agg302(a: int4->min: int4, max: int4) {ordered}
%aggregate agg303(a: int4->min: int4, max: int4) {ordered}
%aggregate agg304(a: int4->min: int4, max: int4) {ordered}
%aggregate agg305(a: int4->min: int4, max: int4) {ordered}
%aggregate agg306(a: int4->min: int4, max: int4) {ordered}
%aggregate agg307(a: int4->min: int4, max: int4) {ordered}
%aggregate agg308(a: int4->min: int4, max: int4) {ordered}
%aggregate agg309(a: int4->min: int4, max: int4) {ordered}
%aggregate agg310(a: int4->min: int4, max: int4) {ordered}
%aggregate agg311(a: int4->min: int4, max: int4) {ordered}
%aggregate agg312(a: int4->min: int4, max: int4) {ordered}
%aggregate agg313(a: int4->min: int4, max: int4) {ordered}
%aggregate agg314(a: int4->min: int4, max: int4) {ordered}
%aggregate agg315(a: int4->min: int4, max: int4) {ordered}
%aggregate agg316(a: int4->min: int4, max: int4) {ordered}
%aggregate agg317(a: int4->min: int4, max: int4) {ordered}
%aggregate agg318(a: int4->min: int4, max: int4) {ordered}
%aggregate agg319(a: int4->min: int4, max: int4) {ordered}
%aggregate agg320(a: int4->min: int4, max: int4) {ordered}
%aggregate agg321(a: int4->min: int4, max: int4) {ordered}
%aggregate agg322(a: int4->min: int4, max: int4) {ordered}
%aggregate agg323(a: int4->min: int4, max: int4) {ordered}
%aggregate agg324(a: int4->min: int4, max: int4) {ordered}
%aggregate agg325(a: int4->min: int4, max: int4) {ordered}
%aggregate agg326(a: int4->min: int4, max: int4) {ordered}
%aggregate agg327(a: int4->min: int4, max: int4) {ordered}
%aggregate agg328(a: int4->min: int4, max: int4) {ordered}
%aggregate agg329(a: int4->min: int4, max: int4) {ordered}
%aggregate agg330(a: int4->min: int4, max: int4) {ordered}
%aggregate agg331(a: int4->min: int4, max: int4) {ordered}
%aggregate agg332(a: int4->min: int4, max: int4) {ordered}
%aggregate agg333(a: int4->min: int4, max: int4) {ordered}
%aggregate agg334(a: int4->min: int4, max: int4) {ordered}
%aggregate agg335(a: int4->min: int4, max: int4) {ordered}
%aggregate agg336(a: int4->min: int4, max: int4) {ordered}
%aggregate agg337(a: int4->min: int4, max: int4) {ordered}
%aggregate agg338(a: int4->min: int4, max: int4) {ordered}
%aggregate agg339(a: int4->min: int4, max: int4) {ordered}
%aggregate agg340(a: int4->min: int4, max: int4) {ordered}
%aggregate agg341(a: int4->min: int4, max: int4) {ordered}
%aggregate agg342(a: int4->min: int4, max: int4) {ordered}
%aggregate agg343(a: int4->min: int4, max: int4) {ordered}
%aggregate agg344(a: int4->min: int4, max: int4) {ordered}
%aggregate agg345(a: int4->min: int4, max: int4) {ordered}
%aggregate agg346(a: int4->min: int4, max: int4) {ordered}
%aggregate agg347(a: int4->min: int4, max: int4) {ordered}
%aggregate agg348(a: int4->min: int4, max: int4) {ordered}
%aggregate agg349(a: int4->min: int4, max: int4) {ordered}
%aggregate agg350(a: int4->min: int4, max: int4) {ordered}
%aggregate agg351(a: int4->min: int4, max: int4) {ordered}
%aggregate agg352(a: int4->min: int4, max: int4) {ordered}
%aggregate agg353(a: int4->min: int4, max: int4) {ordered}
%aggregate agg354(a: int4->min: int4, max: int4) {ordered}
%aggregate agg355(a: int4->min: int4, max: int4) {ordered}
%aggregate agg356(a: int4->min: int4, max: int4) {ordered}
%aggregate agg357(a: int4->min: int4, max: int4) {ordered}
%aggregate agg358(a: int4->min: int4, max: int4) {ordered}
%aggregate agg359(a: int4->min: int4, max: int4) {ordered}
%aggregate agg360(a: int4->min: int4, max: int4) {ordered}
%aggregate agg361(a: int4->min: int4, max: int4) {ordered}
%aggregate agg362(a: int4->min: int4, max: int4) {ordered}
%aggregate agg363(a: int4->min: int4, max: int4) {ordered}
%aggregate agg364(a: int4->min: int4, max: int4) {ordered}
%aggregate agg365(a: int4->min: int4, max: int4) {ordered}
%aggregate agg366(a: int4->min: int4, max: int4) {ordered}
%aggregate agg367(a: int4->min: int4, max: int4) {ordered}
%aggregate agg368(a: int4->min: int4, max: int4) {ordered}
%aggregate agg369(a: int4->min: int4, max: int4) {ordered}
%aggregate agg370(a: int4->min: int4, max: int4) {ordered}
%aggregate agg371(a: int4->min: int4, max: int4) {ordered}
%aggregate agg372(a: int4->min: int4, max: int4) {ordered}
%aggregate agg373(a: int4->min: int4, max: int4) {ordered}
%aggregate agg374(a: int4->min: int4, max: int4) {ordered}
%aggregate agg375(a: int4->min: int4, max: int4) {ordered}
%aggregate agg376(a: int4->min: int4, max: int4) {ordered}
%aggregate agg377(a: int4->min: int4, max: int4) {ordered}
%aggregate agg378(a: int4->min: int4, max: int4) {ordered}
%aggregate agg379(a: int4->min: int4, max: int4) {ordered}
%aggregate agg380(a: int4->min: int4, max: int4) {ordered}
%aggregate agg381(a: int4->min: int4, max: int4) {ordered}
%aggregate agg382(a: int4->min: int4, max: int4) {ordered}
%aggregate agg383(a: int4->min: int4, max: int4) {ordered}
%aggregate agg384(a: int4->min: int4, max: int4) {ordered}
%aggregate agg385(a: int4->min: int4, max: int4) {ordered}
%aggregate agg386(a: int4->min: int4, max: int4) {ordered}
%aggregate agg387(a: int4->min: int4, max: int4) {ordered}
%aggregate agg388(a: int4->min: int4, max: int4) {ordered}
%aggregate agg389(a: int4->min: int4, max: int4) {ordered}
%aggregate agg390(a: int4->min: int4, max: int4) {ordered}
%aggregate agg391(a: int4->min: int4, max: int4) {ordered}
%aggregate agg392(a: int4->min: int4, max: int4) {ordered}
%aggregate agg393(a: int4->min: int4, max: int4) {ordered}
%aggregate agg394(a: int4->min: int4, max: int4) {ordered}
%aggregate agg395(a: int4->min: int4, max: int4) {ordered}
%aggregate agg396(a: int4->min: int4, max: int4) {ordered}
%aggregate agg397(a: int4->min: int4, max: int4) {ordered}
%aggregate agg398(a: int4->min: int4, max: int4) {ordered}
%aggregate agg399(a: int4->min: int4, max: int4) {ordered}
%aggregate agg400(a: int4->min: int4, max: int4) {ordered}
%aggregate agg401(a: int4->min: int4, max: int4) {ordered}
%aggregate agg402(a: int4->min: int4, max: int4) {ordered}
%aggregate agg403(a: int4->min: int4, max: int4) {ordered}
%aggregate agg404(a: int4->min: int4, max: int4) {ordered}
%aggregate agg405(a: int4->min: int4, max: int4) {ordered}
%aggregate agg406(a: int4->min: int4, max: int4) {ordered}
%aggregate agg407(a: int4->min: int4, max: int4) {ordered}
%aggregate agg408(a: int4->min: int4, max: int4) {ordered}
%aggregate agg409(a: int4->min: int4, max: int4) {ordered}
%aggregate agg410(a: int4->min: int4, max: int4) {ordered}
%aggregate agg411(a: int4->min: int4, max: int4) {ordered}
%aggregate agg412(a: int4->min: int4, max: int4) {ordered}
%aggregate agg413(a: int4->min: int4, max: int4) {ordered}
%aggregate agg414(a: int4->min: int4, max: int4) {ordered}
%aggregate agg415(a: int4->min: int4, max: int4) {ordered}
%aggregate agg416(a: int4->min: int4, max: int4) {ordered}
%aggregate agg417(a: int4->min: int4, max: int4) {ordered}
%aggregate agg418(a: int4->min: int4, max: int4) {ordered}
%aggregate agg419(a: int4->min: int4, max: int4) {ordered}
%aggregate agg420(a: int4->min: int4, max: int4) {ordered}
%aggregate agg421(a: int4->min: int4, max: int4) {ordered}
%aggregate agg422(a: int4->min: int4, max: int4) {ordered}
%aggregate agg423(a: int4->min: int4, max: int4) {ordered}
%aggregate agg424(a: int4->min: int4, max: int4) {ordered}
%aggregate agg425(a: int4->min: int4, max: int4) {ordered}
%aggregate agg426(a: int4->min: int4, max: int4) {ordered}
%aggregate agg427(a: int4->min: int4, max: int4) {ordered}
%aggregate agg428(a: int4->min: int4, max: int4) {ordered}
%aggregate agg429(a: int4->min: int4, max: int4) {ordered}
%aggregate agg430(a: int4->min: int4, max: int4) {ordered}
%aggregate agg431(a: int4->min: int4, max: int4) {ordered}
%aggregate agg432(a: int4->min: int4, max: int4) {ordered}
%aggregate agg433(a: int4->min: int4, max: int4) {ordered}
%aggregate agg434(a: int4->min: int4, max: int4) {ordered}
%aggregate agg435(a: int4->min: int4, max: int4) {ordered}
%aggregate agg436(a: int4->min: int4, max: int4) {ordered}
%aggregate agg437(a: int4->min: int4, max: int4) {ordered}
%aggregate agg438(a: int4->min: int4, max: int4) {ordered}
%aggregate agg439(a: int4->min: int4, max: int4) {ordered}
%aggregate agg440(a: int4->min: int4, max: int4) {ordered}
%aggregate agg441(a: int4->min: int4, max: int4) {ordered}
%aggregate agg442(a: int4->min: int4, max: int4) {ordered}
%aggregate agg443(a: int4->min: int4, max: int4) {ordered}
%aggregate agg444(a: int4->min: int4, max: int4) {ordered}
%aggregate agg445(a: int4->min: int4, max: int4) {ordered}
%aggregate agg446(a: int4->min: int4, max: int4) {ordered}
%aggregate agg447(a: int4->min: int4, max: int4) {ordered}
%aggregate agg448(a: int4->min: int4, max: int4) {ordered}
%aggregate agg449(a: int4->min: int4, max: int4) {ordered}
%aggregate agg450(a: int4->min: int4, max: int4) {ordered}
%aggregate agg451(a: int4->min: int4, max: int4) {ordered}
%aggregate agg452(a: int4->min: int4, max: int4) {ordered}
%aggregate agg453(a: int4->min: int4, max: int4) {ordered}
%aggregate agg454(a: int4->min: int4, max: int4) {ordered}
%aggregate agg455(a: int4->min: int4, max: int4) {ordered}
%aggregate agg456(a: int4->min: int4, max: int4) {ordered}
%aggregate agg457(a: int4->min: int4, max: int4) {ordered}
%aggregate agg458(a: int4->min: int4, max: int4) {ordered}
%aggregate agg459(a: int4->min: int4, max: int4) {ordered}
%aggregate agg460(a: int4->min: int4, max: int4) {ordered}
%aggregate agg461(a: int4->min: int4, max: int4) {ordered}
%aggregate agg462(a: int4->min: int4, max: int4) {ordered}
%aggregate agg463(a: int4->min: int4, max: int4) {ordered}
%aggregate agg464(a: int4->min: int4, max: int4) {ordered}
%aggregate agg465(a: int4->min: int4, max: int4) {ordered}
%aggregate agg466(a: int4->min: int4, max: int4) {ordered}
%aggregate agg467(a: int4->min: int4, max: int4) {ordered}
%aggregate agg468(a: int4->min: int4, max: int4) {ordered}
%aggregate agg469(a: int4->min: int4, max: int4) {ordered}
%aggregate agg470(a: int4->min: int4, max: int4) {ordered}
%aggregate agg471(a: int4->min: int4, max: int4) {ordered}
%aggregate agg472(a: int4->min: int4, max: int4) {ordered}
%aggregate agg473(a: int4->min: int4, max: int4) {ordered}
%aggregate agg474(a: int4->min: int4, max: int4) {ordered}
%aggregate agg475(a: int4->min: int4, max: int4) {ordered}
%aggregate agg476(a: int4->min: int4, max: int4) {ordered}
%aggregate agg477(a: int4->min: int4, max: int4) {ordered}
%aggregate agg478(a: int4->min: int4, max: int4) {ordered}
%aggregate agg479(a: int4->min: int4, max: int4) {ordered}
%aggregate agg480(a: int4->min: int4, max: int4) {ordered}
%aggregate agg481(a: int4->min: int4, max: int4) {ordered}
%aggregate agg482(a: int4->min: int4, max: int4) {ordered}
%aggregate agg483(a: int4->min: int4, max: int4) {ordered}
%aggregate agg484(a: int4->min: int4, max: int4) {ordered}
%aggregate agg485(a: int4->min: int4, max: int4) {ordered}
%aggregate agg486(a: int4->min: int4, max: int4) {ordered}
%aggregate agg487(a: int4->min: int4, max: int4) {ordered}
%aggregate agg488(a: int4->min: int4, max: int4) {ordered}
%aggregate agg489(a: int4->min: int4, max: int4) {ordered}
%aggregate agg490(a: int4->min: int4, max: int4) {ordered}
%aggregate agg491(a: int4->min: int4, max: int4) {ordered}
%aggregate agg492(a: int4->min: int4, max: int4) {ordered}
%aggregate agg493(a: int4->min: int4, max: int4) {ordered}
%aggregate agg494(a: int4->min: int4, max: int4) {ordered}
%aggregate agg495(a: int4->min: int4, max: int4) {ordered}
%aggregate agg496(a: int4->min: int4, max: int4) {ordered}
%aggregate agg497(a: int4->min: int4, max: int4) {ordered}
%aggregate agg498(a: int4->min: int4, max: int4) {ordered}
%aggregate agg499(a: int4->min: int4, max: int4) {ordered}
%aggregate agg500(a: int4->min: int4, max: int4) {ordered}
%aggregate agg501(a: int4->min: int4, max: int4) {ordered}
%aggregate agg502(a: int4->min: int4, max: int4) {ordered}
%aggregate agg503(a: int4->min: int4, max: int4) {ordered}
%aggregate agg504(a: int4->min: int4, max: int4) {ordered}
%aggregate agg505(a: int4->min: int4, max: int4) {ordered}
%aggregate agg506(a: int4->min: int4, max: int4) {ordered}
%aggregate agg507(a: int4->min: int4, max: int4) {ordered}
%aggregate agg508(a: int4->min: int4, max: int4) {ordered}
%aggregate agg509(a: int4->min: int4, max: int4) {ordered}
%aggregate agg510(a: int4->min: int4, max: int4) {ordered}
%aggregate agg511(a: int4->min: int4, max: int4) {ordered}
%aggregate agg512(a: int4->min: int4, max: int4) {ordered}
%aggregate agg513(a: int4->min: int4, max: int4) {ordered}
%aggregate agg514(a: int4->min: int4, max: int4) {ordered}
%aggregate agg515(a: int4->min: int4, max: int4) {ordered}
%aggregate agg516(a: int4->min: int4, max: int4) {ordered}
%aggregate agg517(a: int4->min: int4, max: int4) {ordered}
%aggregate agg518(a: int4->min: int4, max: int4) {ordered}
%aggregate agg519(a: int4->min: int4, max: int4) {ordered}
%aggregate agg520(a: int4->min: int4, max: int4) {ordered}
%aggregate agg521(a: int4->min: int4, max: int4) {ordered}
%aggregate agg522(a: int4->min: int4, max: int4) {ordered}
%aggregate agg523(a: int4->min: int4, max: int4) {ordered}
%aggregate agg524(a: int4->min: int4, max: int4) {ordered}
%aggregate agg525(a: int4->min: int4, max: int4) {ordered}
%aggregate agg526(a: int4->min: int4, max: int4) {ordered}
%aggregate agg527(a: int4->min: int4, max: int4) {ordered}
%aggregate agg528(a: int4->min: int4, max: int4) {ordered}
%aggregate agg529(a: int4->min: int4, max: int4) {ordered}
%aggregate agg530(a: int4->min: int4, max: int4) {ordered}
%aggregate agg531(a: int4->min: int4, max: int4) {ordered}
%aggregate agg532(a: int4->min: int4, max: int4) {ordered}
%aggregate agg533(a: int4->min: int4, max: int4) {ordered}
%aggregate agg534(a: int4->min: int4, max: int4) {ordered}
%aggregate agg535(a: int4->min: int4, max: int4) {ordered}
%aggregate agg536(a: int4->min: int4, max: int4) {ordered}
%aggregate agg537(a: int4->min: int4, max: int4) {ordered}
%aggregate agg538(a: int4->min: int4, max: int4) {ordered}
%aggregate agg539(a: int4->min: int4, max: int4) {ordered}
%aggregate agg540(a: int4->min: int4, max: int4) {ordered}
%aggregate agg541(a: int4->min: int4, max: int4) {ordered}
%aggregate agg542(a: int4->min: int4, max: int4) {ordered}
%aggregate agg543(a: int4->min: int4, max: int4) {ordered}
%aggregate agg544(a: int4->min: int4, max: int4) {ordered}
%aggregate agg545(a: int4->min: int4, max: int4) {ordered}
%aggregate agg546(a: int4->min: int4, max: int4) {ordered}
%aggregate agg547(a: int4->min: int4, max: int4) {ordered}
%aggregate agg548(a: int4->min: int4, max: int4) {ordered}
%aggregate agg549(a: int4->min: int4, max: int4) {ordered}
%aggregate agg550(a: int4->min: int4, max: int4) {ordered}
%aggregate agg551(a: int4->min: int4, max: int4) {ordered}
%aggregate agg552(a: int4->min: int4, max: int4) {ordered}
%aggregate agg553(a: int4->min: int4, max: int4) {ordered}
%aggregate agg554(a: int4->min: int4, max: int4) {ordered}
%aggregate agg555(a: int4->min: int4, max: int4) {ordered}
%aggregate agg556(a: int4->min: int4, max: int4) {ordered}
%aggregate agg557(a: int4->min: int4, max: int4) {ordered}
%aggregate agg558(a: int4->min: int4, max: int4) {ordered}
%aggregate agg559(a: int4->min: int4, max: int4) {ordered}
%aggregate agg560(a: int4->min: int4, max: int4) {ordered}
%aggregate agg561(a: int4->min: int4, max: int4) {ordered}
%aggregate agg562(a: int4->min: int4, max: int4) {ordered}
%aggregate agg563(a: int4->min: int4, max: int4) {ordered}
%aggregate agg564(a: int4->min: int4, max: int4) {ordered}
%aggregate agg565(a: int4->min: int4, max: int4) {ordered}
%aggregate agg566(a: int4->min: int4, max: int4) {ordered}
%aggregate agg567(a: int4->min: int4, max: int4) {ordered}
%aggregate agg568(a: int4->min: int4, max: int4) {ordered}
%aggregate agg569(a: int4->min: int4, max: int4) {ordered}
%aggregate agg570(a: int4->min: int4, max: int4) {ordered}
%aggregate agg571(a: int4->min: int4, max: int4) {ordered}
%aggregate agg572(a: int4->min: int4, max: int4) {ordered}
%aggregate agg573(a: int4->min: int4, max: int4) {ordered}
%aggregate agg574(a: int4->min: int4, max: int4) {ordered}
%aggregate agg575(a: int4->min: int4, max: int4) {ordered}
%aggregate agg576(a: int4->min: int4, max: int4) {ordered}
%aggregate agg577(a: int4->min: int4, max: int4) {ordered}
%aggregate agg578(a: int4->min: int4, max: int4) {ordered}
%aggregate agg579(a: int4->min: int4, max: int4) {ordered}
%aggregate agg580(a: int4->min: int4, max: int4) {ordered}
%aggregate agg581(a: int4->min: int4, max: int4) {ordered}
%aggregate agg582(a: int4->min: int4, max: int4) {ordered}
%aggregate agg583(a: int4->min: int4, max: int4) {ordered}
%aggregate agg584(a: int4->min: int4, max: int4) {ordered}
%aggregate agg585(a: int4->min: int4, max: int4) {ordered}
%aggregate agg586(a: int4->min: int4, max: int4) {ordered}
%aggregate agg587(a: int4->min: int4, max: int4) {ordered}
%aggregate agg588(a: int4->min: int4, max: int4) {ordered}
%aggregate agg589(a: int4->min: int4, max: int4) {ordered}
%aggregate agg590(a: int4->min: int4, max: int4) {ordered}
%aggregate agg591(a: int4->min: int4, max: int4) {ordered}
%aggregate agg592(a: int4->min: int4, max: int4) {ordered}
%aggregate agg593(a: int4->min: int4, max: int4) {ordered}
%aggregate agg594(a: int4->min: int4, max: int4) {ordered}
%aggregate agg595(a: int4->min: int4, max: int4) {ordered}
%aggregate agg596(a: int4->min: int4, max: int4) {ordered}
%aggregate agg597(a: int4->min: int4, max: int4) {ordered}
%aggregate agg598(a: int4->min: int4, max: int4) {ordered}
%aggregate agg599(a: int4->min: int4, max: int4) {ordered}
%aggregate agg600(a: int4->min: int4, max: int4) {ordered}
%aggregate agg601(a: int4->min: int4, max: int4) {ordered}
%aggregate agg602(a: int4->min: int4, max: int4) {ordered}
%aggregate agg603(a: int4->min: int4, max: int4) {ordered}
%aggregate agg604(a: int4->min: int4, max: int4) {ordered}
%aggregate agg605(a: int4->min: int4, max: int4) {ordered}
%aggregate agg606(a: int4->min: int4, max: int4) {ordered}
%aggregate agg607(a: int4->min: int4, max: int4) {ordered}
%aggregate agg608(a: int4->min: int4, max: int4) {ordered}
%aggregate agg609(a: int4->min: int4, max: int4) {ordered}
%aggregate agg610(a: int4->min: int4, max: int4) {ordered}
%aggregate agg611(a: int4->min: int4, max: int4) {ordered}
%aggregate agg612(a: int4->min: int4, max: int4) {ordered}
%aggregate agg613(a: int4->min: int4, max: int4) {ordered}
%aggregate agg614(a: int4->min: int4, max: int4) {ordered}
%aggregate agg615(a: int4->min: int4, max: int4) {ordered}
%aggregate agg616(a: int4->min: int4, max: int4) {ordered}
%aggregate agg617(a: int4->min: int4, max: int4) {ordered}
%aggregate agg618(a: int4->min: int4, max: int4) {ordered}
%aggregate agg619(a: int4->min: int4, max: int4) {ordered}
%aggregate agg620(a: int4->min: int4, max: int4) {ordered}
%aggregate agg621(a: int4->min: int4, max: int4) {ordered}
%aggregate agg622(a: int4->min: int4, max: int4) {ordered}
%aggregate agg623(a: int4->min: int4, max: int4) {ordered}
%aggregate agg624(a: int4->min: int4, max: int4) {ordered}
%aggregate agg625(a: int4->min: int4, max: int4) {ordered}
%aggregate agg626(a: int4->min: int4, max: int4) {ordered}
%aggregate agg627(a: int4->min: int4, max: int4) {ordered}
%aggregate agg628(a: int4->min: int4, max: int4) {ordered}
%aggregate agg629(a: int4->min: int4, max: int4) {ordered}
%aggregate agg630(a: int4->min: int4, max: int4) {ordered}
%aggregate agg631(a: int4->min: int4, max: int4) {ordered}
%aggregate agg632(a: int4->min: int4, max: int4) {ordered}
%aggregate agg633(a: int4->min: int4, max: int4) {ordered}
%aggregate agg634(a: int4->min: int4, max: int4) {ordered}
%aggregate agg635(a: int4->min: int4, max: int4) {ordered}
%aggregate agg636(a: int4->min: int4, max: int4) {ordered}
%aggregate agg637(a: int4->min: int4, max: int4) {ordered}
%aggregate agg638(a: int4->min: int4, max: int4) {ordered}
%aggregate agg639(a: int4->min: int4, max: int4) {ordered}
%aggregate agg640(a: int4->min: int4, max: int4) {ordered}
%aggregate agg641(a: int4->min: int4, max: int4) {ordered}
%aggregate agg642(a: int4->min: int4, max: int4) {ordered}
%aggregate agg643(a: int4->min: int4, max: int4) {ordered}
%aggregate agg644(a: int4->min: int4, max: int4) {ordered}
%aggregate agg645(a: int4->min: int4, max: int4) {ordered}
%aggregate agg646(a: int4->min: int4, max: int4) {ordered}
%aggregate agg647(a: int4->min: int4, max: int4) {ordered}
%aggregate agg648(a: int4->min: int4, max: int4) {ordered}
%aggregate agg649(a: int4->min: int4, max: int4) {ordered}
%aggregate agg650(a: int4->min: int4, max: int4) {ordered}
%aggregate agg651(a: int4->min: int4, max: int4) {ordered}
%aggregate agg652(a: int4->min: int4, max: int4) {ordered}
%aggregate agg653(a: int4->min: int4, max: int4) {ordered}
%aggregate agg654(a: int4->min: int4, max: int4) {ordered}
%aggregate agg655(a: int4->min: int4, max: int4) {ordered}
%aggregate agg656(a: int4->min: int4, max: int4) {ordered}
%aggregate agg657(a: int4->min: int4, max: int4) {ordered}
%aggregate agg658(a: int4->min: int4, max: int4) {ordered}
%aggregate agg659(a: int4->min: int4, max: int4) {ordered}
%aggregate agg660(a: int4->min: int4, max: int4) {ordered}
%aggregate agg661(a: int4->min: int4, max: int4) {ordered}
%aggregate agg662(a: int4->min: int4, max: int4) {ordered}
%aggregate agg663(a: int4->min: int4, max: int4) {ordered}
%aggregate agg664(a: int4->min: int4, max: int4) {ordered}
%aggregate agg665(a: int4->min: int4, max: int4) {ordered}
%aggregate agg666(a: int4->min: int4, max: int4) {ordered}
%aggregate agg667(a: int4->min: int4, max: int4) {ordered}
%aggregate agg668(a: int4->min: int4, max: int4) {ordered}
%aggregate agg669(a: int4->min: int4, max: int4) {ordered}
%aggregate agg670(a: int4->min: int4, max: int4) {ordered}
%aggregate agg671(a: int4->min: int4, max: int4) {ordered}
%aggregate agg672(a: int4->min: int4, max: int4) {ordered}
%aggregate agg673(a: int4->min: int4, max: int4) {ordered}
%aggregate agg674(a: int4->min: int4, max: int4) {ordered}
%aggregate agg675(a: int4->min: int4, max: int4) {ordered}
%aggregate agg676(a: int4->min: int4, max: int4) {ordered}
%aggregate agg677(a: int4->min: int4, max: int4) {ordered}
%aggregate agg678(a: int4->min: int4, max: int4) {ordered}
%aggregate agg679(a: int4->min: int4, max: int4) {ordered}
%aggregate agg680(a: int4->min: int4, max: int4) {ordered}
%aggregate agg681(a: int4->min: int4, max: int4) {ordered}
%aggregate agg682(a: int4->min: int4, max: int4) {ordered}
%aggregate agg683(a: int4->min: int4, max: int4) {ordered}
%aggregate agg684(a: int4->min: int4, max: int4) {ordered}
%aggregate agg685(a: int4->min: int4, max: int4) {ordered}
%aggregate agg686(a: int4->min: int4, max: int4) {ordered}
%aggregate agg687(a: int4->min: int4, max: int4) {ordered}
%aggregate agg688(a: int4->min: int4, max: int4) {ordered}
%aggregate agg689(a: int4->min: int4, max: int4) {ordered}
%aggregate agg690(a: int4->min: int4, max: int4) {ordered}
%aggregate agg691(a: int4->min: int4, max: int4) {ordered}
%aggregate agg692(a: int4->min: int4, max: int4) {ordered}
%aggregate agg693(a: int4->min: int4, max: int4) {ordered}
%aggregate agg694(a: int4->min: int4, max: int4) {ordered}
%aggregate agg695(a: int4->min: int4, max: int4) {ordered}
%aggregate agg696(a: int4->min: int4, max: int4) {ordered}
%aggregate agg697(a: int4->min: int4, max: int4) {ordered}
%aggregate agg698(a: int4->min: int4, max: int4) {ordered}
%aggregate agg699(a: int4->min: int4, max: int4) {ordered}
%aggregate agg700(a: int4->min: int4, max: int4) {ordered}
%aggregate agg701(a: int4->min: int4, max: int4) {ordered}
%aggregate agg702(a: int4->min: int4, max: int4) {ordered}
%aggregate agg703(a: int4->min: int4, max: int4) {ordered}
%aggregate agg704(a: int4->min: int4, max: int4) {ordered}
%aggregate agg705(a: int4->min: int4, max: int4) {ordered}
%aggregate agg706(a: int4->min: int4, max: int4) {ordered}
%aggregate agg707(a: int4->min: int4, max: int4) {ordered}
%aggregate agg708(a: int4->min: int4, max: int4) {ordered}
%aggregate agg709(a: int4->min: int4, max: int4) {ordered}
%aggregate agg710(a: int4->min: int4, max: int4) {ordered}
%aggregate agg711(a: int4->min: int4, max: int4) {ordered}
%aggregate agg712(a: int4->min: int4, max: int4) {ordered}
%aggregate agg713(a: int4->min: int4, max: int4) {ordered}
%aggregate agg714(a: int4->min: int4, max: int4) {ordered}
%aggregate agg715(a: int4->min: int4, max: int4) {ordered}
%aggregate agg716(a: int4->min: int4, max: int4) {ordered}
%aggregate agg717(a: int4->min: int4, max: int4) {ordered}
%aggregate agg718(a: int4->min: int4, max: int4) {ordered}
%aggregate agg719(a: int4->min: int4, max: int4) {ordered}
%aggregate agg720(a: int4->min: int4, max: int4) {ordered}
%aggregate agg721(a: int4->min: int4, max: int4) {ordered}
%aggregate agg722(a: int4->min: int4, max: int4) {ordered}
%aggregate agg723(a: int4->min: int4, max: int4) {ordered}
%aggregate agg724(a: int4->min: int4, max: int4) {ordered}
%aggregate agg725(a: int4->min: int4, max: int4) {ordered}
%aggregate agg726(a: int4->min: int4, max: int4) {ordered}
%aggregate agg727(a: int4->min: int4, max: int4) {ordered}
%aggregate agg728(a: int4->min: int4, max: int4) {ordered}
%aggregate agg729(a: int4->min: int4, max: int4) {ordered}
%aggregate agg730(a: int4->min: int4, max: int4) {ordered}
%aggregate agg731(a: int4->min: int4, max: int4) {ordered}
%aggregate agg732(a: int4->min: int4, max: int4) {ordered}
%aggregate agg733(a: int4->min: int4, max: int4) {ordered}
%aggregate agg734(a: int4->min: int4, max: int4) {ordered}
%aggregate agg735(a: int4->min: int4, max: int4) {ordered}
%aggregate agg736(a: int4->min: int4, max: int4) {ordered}
%aggregate agg737(a: int4->min: int4, max: int4) {ordered}
%aggregate agg738(a: int4->min: int4, max: int4) {ordered}
%aggregate agg739(a: int4->min: int4, max: int4) {ordered}
%aggregate agg740(a: int4->min: int4, max: int4) {ordered}
%aggregate agg741(a: int4->min: int4, max: int4) {ordered}
%aggregate agg742(a: int4->min: int4, max: int4) {ordered}
%aggregate agg743(a: int4->min: int4, max: int4) {ordered}
%aggregate agg744(a: int4->min: int4, max: int4) {ordered}
%aggregate agg745(a: int4->min: int4, max: int4) {ordered}
%aggregate agg746(a: int4->min: int4, max: int4) {ordered}
%aggregate agg747(a: int4->min: int4, max: int4) {ordered}
%aggregate agg748(a: int4->min: int4, max: int4) {ordered}
%aggregate agg749(a: int4->min: int4, max: int4) {ordered}
%aggregate agg750(a: int4->min: int4, max: int4) {ordered}
%aggregate agg751(a: int4->min: int4, max: int4) {ordered}
%aggregate agg752(a: int4->min: int4, max: int4) {ordered}
%aggregate agg753(a: int4->min: int4, max: int4) {ordered}
%aggregate agg754(a: int4->min: int4, max: int4) {ordered}
%aggregate agg755(a: int4->min: int4, max: int4) {ordered}
%aggregate agg756(a: int4->min: int4, max: int4) {ordered}
%aggregate agg757(a: int4->min: int4, max: int4) {ordered}
%aggregate agg758(a: int4->min: int4, max: int4) {ordered}
%aggregate agg759(a: int4->min: int4, max: int4) {ordered}
%aggregate agg760(a: int4->min: int4, max: int4) {ordered}
%aggregate agg761(a: int4->min: int4, max: int4) {ordered}
%aggregate agg762(a: int4->min: int4, max: int4) {ordered}
%aggregate agg763(a: int4->min: int4, max: int4) {ordered}
%aggregate agg764(a: int4->min: int4, max: int4) {ordered}
%aggregate agg765(a: int4->min: int4, max: int4) {ordered}
%aggregate agg766(a: int4->min: int4, max: int4) {ordered}
%aggregate agg767(a: int4->min: int4, max: int4) {ordered}
%aggregate agg768(a: int4->min: int4, max: int4) {ordered}
%aggregate agg769(a: int4->min: int4, max: int4) {ordered}
%aggregate agg770(a: int4->min: int4, max: int4) {ordered}
%aggregate agg771(a: int4->min: int4, max: int4) {ordered}
%aggregate agg772(a: int4->min: int4, max: int4) {ordered}
%aggregate agg773(a: int4->min: int4, max: int4) {ordered}
%aggregate agg774(a: int4->min: int4, max: int4) {ordered}
%aggregate agg775(a: int4->min: int4, max: int4) {ordered}
%aggregate agg776(a: int4->min: int4, max: int4) {ordered}
%aggregate agg777(a: int4->min: int4, max: int4) {ordered}
%aggregate agg778(a: int4->min: int4, max: int4) {ordered}
%aggregate agg779(a: int4->min: int4, max: int4) {ordered}
%aggregate agg780(a: int4->min: int4, max: int4) {ordered}
%aggregate agg781(a: int4->min: int4, max: int4) {ordered}
%aggregate agg782(a: int4->min: int4, max: int4) {ordered}
%aggregate agg783(a: int4->min: int4, max: int4) {ordered}
%aggregate agg784(a: int4->min: int4, max: int4) {ordered}
%aggregate agg785(a: int4->min: int4, max: int4) {ordered}
%aggregate agg786(a: int4->min: int4, max: int4) {ordered}
%aggregate agg787(a: int4->min: int4, max: int4) {ordered}
%aggregate agg788(a: int4->min: int4, max: int4) {ordered}
%aggregate agg789(a: int4->min: int4, max: int4) {ordered}
%aggregate agg790(a: int4->min: int4, max: int4) {ordered}
%aggregate agg791(a: int4->min: int4, max: int4) {ordered}
%aggregate agg792(a: int4->min: int4, max: int4) {ordered}
%aggregate agg793(a: int4->min: int4, max: int4) {ordered}
%aggregate agg794(a: int4->min: int4, max: int4) {ordered}
%aggregate agg795(a: int4->min: int4, max: int4) {ordered}
%aggregate agg796(a: int4->min: int4, max: int4) {ordered}
%aggregate agg797(a: int4->min: int4, max: int4) {ordered}
%aggregate agg798(a: int4->min: int4, max: int4) {ordered}
%aggregate agg799(a: int4->min: int4, max: int4) {ordered}
%aggregate agg800(a: int4->min: int4, max: int4) {ordered}
%aggregate agg801(a: int4->min: int4, max: int4) {ordered}
%aggregate agg802(a: int4->min: int4, max: int4) {ordered}
%aggregate agg803(a: int4->min: int4, max: int4) {ordered}
%aggregate agg804(a: int4->min: int4, max: int4) {ordered}
%aggregate agg805(a: int4->min: int4, max: int4) {ordered}
%aggregate agg806(a: int4->min: int4, max: int4) {ordered}
%aggregate agg807(a: int4->min: int4, max: int4) {ordered}
%aggregate agg808(a: int4->min: int4, max: int4) {ordered}
%aggregate agg809(a: int4->min: int4, max: int4) {ordered}
%aggregate agg810(a: int4->min: int4, max: int4) {ordered}
%aggregate agg811(a: int4->min: int4, max: int4) {ordered}
%aggregate agg812(a: int4->min: int4, max: int4) {ordered}
%aggregate agg813(a: int4->min: int4, max: int4) {ordered}
%aggregate agg814(a: int4->min: int4, max: int4) {ordered}
%aggregate agg815(a: int4->min: int4, max: int4) {ordered}
%aggregate agg816(a: int4->min: int4, max: int4) {ordered}
%aggregate agg817(a: int4->min: int4, max: int4) {ordered}
%aggregate agg818(a: int4->min: int4, max: int4) {ordered}
%aggregate agg819(a: int4->min: int4, max: int4) {ordered}
%aggregate agg820(a: int4->min: int4, max: int4) {ordered}
%aggregate agg821(a: int4->min: int4, max: int4) {ordered}
%aggregate agg822(a: int4->min: int4, max: int4) {ordered}
%aggregate agg823(a: int4->min: int4, max: int4) {ordered}
%aggregate agg824(a: int4->min: int4, max: int4) {ordered}
%aggregate agg825(a: int4->min: int4, max: int4) {ordered}
%aggregate agg826(a: int4->min: int4, max: int4) {ordered}
%aggregate agg827(a: int4->min: int4, max: int4) {ordered}
%aggregate agg828(a: int4->min: int4, max: int4) {ordered}
%aggregate agg829(a: int4->min: int4, max: int4) {ordered}
%aggregate agg830(a: int4->min: int4, max: int4) {ordered}
%aggregate agg831(a: int4->min: int4, max: int4) {ordered}
%aggregate agg832(a: int4->min: int4, max: int4) {ordered}
%aggregate agg833(a: int4->min: int4, max: int4) {ordered}
%aggregate agg834(a: int4->min: int4, max: int4) {ordered}
%aggregate agg835(a: int4->min: int4, max: int4) {ordered}
%aggregate agg836(a: int4->min: int4, max: int4) {ordered}
%aggregate agg837(a: int4->min: int4, max: int4) {ordered}
%aggregate agg838(a: int4->min: int4, max: int4) {ordered}
%aggregate agg839(a: int4->min: int4, max: int4) {ordered}
%aggregate agg840(a: int4->min: int4, max: int4) {ordered}
%aggregate agg841(a: int4->min: int4, max: int4) {ordered}
%aggregate agg842(a: int4->min: int4, max: int4) {ordered}
%aggregate agg843(a: int4->min: int4, max: int4) {ordered}
%aggregate agg844(a: int4->min: int4, max: int4) {ordered}
%aggregate agg845(a: int4->min: int4, max: int4) {ordered}
%aggregate agg846(a: int4->min: int4, max: int4) {ordered}
%aggregate agg847(a: int4->min: int4, max: int4) {ordered}
%aggregate agg848(a: int4->min: int4, max: int4) {ordered}
%aggregate agg849(a: int4->min: int4, max: int4) {ordered}
%aggregate agg850(a: int4->min: int4, max: int4) {ordered}
%aggregate agg851(a: int4->min: int4, max: int4) {ordered}
%aggregate agg852(a: int4->min: int4, max: int4) {ordered}
%aggregate agg853(a: int4->min: int4, max: int4) {ordered}
%aggregate agg854(a: int4->min: int4, max: int4) {ordered}
%aggregate agg855(a: int4->min: int4, max: int4) {ordered}
%aggregate agg856(a: int4->min: int4, max: int4) {ordered}
%aggregate agg857(a: int4->min: int4, max: int4) {ordered}
%aggregate agg858(a: int4->min: int4, max: int4) {ordered}
%aggregate agg859(a: int4->min: int4, max: int4) {ordered}
%aggregate agg860(a: int4->min: int4, max: int4) {ordered}
%aggregate agg861(a: int4->min: int4, max: int4) {ordered}
%aggregate agg862(a: int4->min: int4, max: int4) {ordered}
%aggregate agg863(a: int4->min: int4, max: int4) {ordered}
%aggregate agg864(a: int4->min: int4, max: int4) {ordered}
%aggregate agg865(a: int4->min: int4, max: int4) {ordered}
%aggregate agg866(a: int4->min: int4, max: int4) {ordered}
%aggregate agg867(a: int4->min: int4, max: int4) {ordered}
%aggregate agg868(a: int4->min: int4, max: int4) {ordered}
%aggregate agg869(a: int4->min: int4, max: int4) {ordered}
%aggregate agg870(a: int4->min: int4, max: int4) {ordered}
%aggregate agg871(a: int4->min: int4, max: int4) {ordered}
%aggregate agg872(a: int4->min: int4, max: int4) {ordered}
%aggregate agg873(a: int4->min: int4, max: int4) {ordered}
%aggregate agg874(a: int4->min: int4, max: int4) {ordered}
%aggregate agg875(a: int4->min: int4, max: int4) {ordered}
%aggregate agg876(a: int4->min: int4, max: int4) {ordered}
%aggregate agg877(a: int4->min: int4, max: int4) {ordered}
%aggregate agg878(a: int4->min: int4, max: int4) {ordered}
%aggregate agg879(a: int4->min: int4, max: int4) {ordered}
%aggregate agg880(a: int4->min: int4, max: int4) {ordered}
%aggregate agg881(a: int4->min: int4, max: int4) {ordered}
%aggregate agg882(a: int4->min: int4, max: int4) {ordered}
%aggregate agg883(a: int4->min: int4, max: int4) {ordered}
%aggregate agg884(a: int4->min: int4, max: int4) {ordered}
%aggregate agg885(a: int4->min: int4, max: int4) {ordered}
%aggregate agg886(a: int4->min: int4, max: int4) {ordered}
%aggregate agg887(a: int4->min: int4, max: int4) {ordered}
%aggregate agg888(a: int4->min: int4, max: int4) {ordered}
%aggregate agg889(a: int4->min: int4, max: int4) {ordered}
%aggregate agg890(a: int4->min: int4, max: int4) {ordered}
%aggregate agg891(a: int4->min: int4, max: int4) {ordered}
%aggregate agg892(a: int4->min: int4, max: int4) {ordered}
%aggregate agg893(a: int4->min: int4, max: int4) {ordered}
%aggregate agg894(a: int4->min: int4, max: int4) {ordered}
%aggregate agg895(a: int4->min: int4, max: int4) {ordered}
%aggregate agg896(a: int4->min: int4, max: int4) {ordered}
%aggregate agg897(a: int4->min: int4, max: int4) {ordered}
%aggregate agg898(a: int4->min: int4, max: int4) {ordered}
%aggregate agg899(a: int4->min: int4, max: int4) {ordered}
%aggregate agg900(a: int4->min: int4, max: int4) {ordered}
%aggregate agg901(a: int4->min: int4, max: int4) {ordered}
%aggregate agg902(a: int4->min: int4, max: int4) {ordered}
%aggregate agg903(a: int4->min: int4, max: int4) {ordered}
%aggregate agg904(a: int4->min: int4, max: int4) {ordered}
%aggregate agg905(a: int4->min: int4, max: int4) {ordered}
%aggregate agg906(a: int4->min: int4, max: int4) {ordered}
%aggregate agg907(a: int4->min: int4, max: int4) {ordered}
%aggregate agg908(a: int4->min: int4, max: int4) {ordered}
%aggregate agg909(a: int4->min: int4, max: int4) {ordered}
%aggregate agg910(a: int4->min: int4, max: int4) {ordered}
%aggregate agg911(a: int4->min: int4, max: int4) {ordered}
%aggregate agg912(a: int4->min: int4, max: int4) {ordered}
%aggregate agg913(a: int4->min: int4, max: int4) {ordered}
%aggregate agg914(a: int4->min: int4, max: int4) {ordered}
%aggregate agg915(a: int4->min: int4, max: int4) {ordered}
%aggregate agg916(a: int4->min: int4, max: int4) {ordered}
%aggregate agg917(a: int4->min: int4, max: int4) {ordered}
%aggregate agg918(a: int4->min: int4, max: int4) {ordered}
%aggregate agg919(a: int4->min: int4, max: int4) {ordered}
%aggregate agg920(a: int4->min: int4, max: int4) {ordered}
%aggregate agg921(a: int4->min: int4, max: int4) {ordered}
%aggregate agg922(a: int4->min: int4, max: int4) {ordered}
%aggregate agg923(a: int4->min: int4, max: int4) {ordered}
%aggregate agg924(a: int4->min: int4, max: int4) {ordered}
%aggregate agg925(a: int4->min: int4, max: int4) {ordered}
%aggregate agg926(a: int4->min: int4, max: int4) {ordered}
%aggregate agg927(a: int4->min: int4, max: int4) {ordered}
%aggregate agg928(a: int4->min: int4, max: int4) {ordered}
%aggregate agg929(a: int4->min: int4, max: int4) {ordered}
%aggregate agg930(a: int4->min: int4, max: int4) {ordered}
%aggregate agg931(a: int4->min: int4, max: int4) {ordered}
%aggregate agg932(a: int4->min: int4, max: int4) {ordered}
%aggregate agg933(a: int4->min: int4, max: int4) {ordered}
%aggregate agg934(a: int4->min: int4, max: int4) {ordered}
%aggregate agg935(a: int4->min: int4, max: int4) {ordered}
%aggregate agg936(a: int4->min: int4, max: int4) {ordered}
%aggregate agg937(a: int4->min: int4, max: int4) {ordered}
%aggregate agg938(a: int4->min: int4, max: int4) {ordered}
%aggregate agg939(a: int4->min: int4, max: int4) {ordered}
%aggregate agg940(a: int4->min: int4, max: int4) {ordered}
%aggregate agg941(a: int4->min: int4, max: int4) {ordered}
%aggregate agg942(a: int4->min: int4, max: int4) {ordered}
%aggregate agg943(a: int4->min: int4, max: int4) {ordered}
%aggregate agg944(a: int4->min: int4, max: int4) {ordered}
%aggregate agg945(a: int4->min: int4, max: int4) {ordered}
%aggregate agg946(a: int4->min: int4, max: int4) {ordered}
%aggregate agg947(a: int4->min: int4, max: int4) {ordered}
%aggregate agg948(a: int4->min: int4, max: int4) {ordered}
%aggregate agg949(a: int4->min: int4, max: int4) {ordered}
%aggregate agg950(a: int4->min: int4, max: int4) {ordered}
%aggregate agg951(a: int4->min: int4, max: int4) {ordered}
%aggregate agg952(a: int4->min: int4, max: int4) {ordered}
%aggregate agg953(a: int4->min: int4, max: int4) {ordered}
%aggregate agg954(a: int4->min: int4, max: int4) {ordered}
%aggregate agg955(a: int4->min: int4, max: int4) {ordered}
%aggregate agg956(a: int4->min: int4, max: int4) {ordered}
%aggregate agg957(a: int4->min: int4, max: int4) {ordered}
%aggregate agg958(a: int4->min: int4, max: int4) {ordered}
%aggregate agg959(a: int4->min: int4, max: int4) {ordered}
%aggregate agg960(a: int4->min: int4, max: int4) {ordered}
%aggregate agg961(a: int4->min: int4, max: int4) {ordered}
%aggregate agg962(a: int4->min: int4, max: int4) {ordered}
%aggregate agg963(a: int4->min: int4, max: int4) {ordered}
%aggregate agg964(a: int4->min: int4, max: int4) {ordered}
%aggregate agg965(a: int4->min: int4, max: int4) {ordered}
%aggregate agg966(a: int4->min: int4, max: int4) {ordered}
%aggregate agg967(a: int4->min: int4, max: int4) {ordered}
%aggregate agg968(a: int4->min: int4, max: int4) {ordered}
%aggregate agg969(a: int4->min: int4, max: int4) {ordered}
%aggregate agg970(a: int4->min: int4, max: int4) {ordered}
%aggregate agg971(a: int4->min: int4, max: int4) {ordered}
%aggregate agg972(a: int4->min: int4, max: int4) {ordered}
%aggregate agg973(a: int4->min: int4, max: int4) {ordered}
%aggregate agg974(a: int4->min: int4, max: int4) {ordered}
%aggregate agg975(a: int4->min: int4, max: int4) {ordered}
%aggregate agg976(a: int4->min: int4, max: int4) {ordered}
%aggregate agg977(a: int4->min: int4, max: int4) {ordered}
%aggregate agg978(a: int4->min: int4, max: int4) {ordered}
%aggregate agg979(a: int4->min: int4, max: int4) {ordered}
%aggregate agg980(a: int4->min: int4, max: int4) {ordered}
%aggregate agg981(a: int4->min: int4, max: int4) {ordered}
%aggregate agg982(a: int4->min: int4, max: int4) {ordered}
%aggregate agg983(a: int4->min: int4, max: int4) {ordered}
%aggregate agg984(a: int4->min: int4, max: int4) {ordered}
%aggregate agg985(a: int4->min: int4, max: int4) {ordered}
%aggregate agg986(a: int4->min: int4, max: int4) {ordered}
%aggregate agg987(a: int4->min: int4, max: int4) {ordered}
%aggregate agg988(a: int4->min: int4, max: int4) {ordered}
%aggregate agg989(a: int4->min: int4, max: int4) {ordered}
%aggregate agg990(a: int4->min: int4, max: int4) {ordered}
%aggregate agg991(a: int4->min: int4, max: int4) {ordered}
%aggregate agg992(a: int4->min: int4, max: int4) {ordered}
%aggregate agg993(a: int4->min: int4, max: int4) {ordered}
%aggregate agg994(a: int4->min: int4, max: int4) {ordered}
%aggregate agg995(a: int4->min: int4, max: int4) {ordered}
%aggregate agg996(a: int4->min: int4, max: int4) {ordered}
%aggregate agg997(a: int4->min: int4, max: int4) {ordered}
%aggregate agg998(a: int4->min: int4, max: int4) {ordered}
%aggregate agg999(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1000(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1001(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1002(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1003(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1004(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1005(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1006(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1007(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1008(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1009(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1010(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1011(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1012(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1013(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1014(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1015(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1016(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1017(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1018(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1019(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1020(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1021(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1022(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1023(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1024(a: int4->min: int4, max: int4) {ordered}
%aggregate agg1025(a: int4->min: int4, max: int4) {ordered}

C(a, min, max) :- A(a, b) GROUP-BY(a) agg001(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg002(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg003(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg004(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg005(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg006(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg007(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg008(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg009(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg010(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg011(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg012(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg013(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg014(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg015(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg016(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg017(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg018(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg019(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg020(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg021(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg022(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg023(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg024(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg025(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg026(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg027(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg028(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg029(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg030(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg031(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg032(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg033(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg034(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg035(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg036(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg037(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg038(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg039(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg040(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg041(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg042(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg043(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg044(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg045(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg046(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg047(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg048(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg049(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg050(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg051(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg052(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg053(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg054(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg055(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg056(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg057(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg058(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg059(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg060(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg061(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg062(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg063(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg064(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg065(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg066(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg067(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg068(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg069(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg070(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg071(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg072(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg073(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg074(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg075(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg076(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg077(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg078(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg079(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg080(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg081(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg082(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg083(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg084(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg085(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg086(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg087(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg088(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg089(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg090(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg091(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg092(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg093(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg094(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg095(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg096(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg097(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg098(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg099(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg100(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg101(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg102(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg103(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg104(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg105(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg106(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg107(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg108(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg109(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg110(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg111(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg112(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg113(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg114(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg115(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg116(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg117(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg118(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg119(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg120(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg121(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg122(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg123(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg124(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg125(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg126(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg127(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg128(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg129(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg130(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg131(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg132(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg133(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg134(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg135(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg136(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg137(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg138(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg139(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg140(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg141(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg142(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg143(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg144(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg145(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg146(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg147(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg148(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg149(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg150(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg151(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg152(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg153(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg154(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg155(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg156(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg157(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg158(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg159(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg160(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg161(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg162(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg163(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg164(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg165(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg166(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg167(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg168(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg169(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg170(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg171(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg172(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg173(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg174(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg175(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg176(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg177(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg178(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg179(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg180(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg181(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg182(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg183(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg184(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg185(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg186(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg187(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg188(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg189(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg190(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg191(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg192(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg193(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg194(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg195(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg196(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg197(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg198(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg199(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg200(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg201(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg202(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg203(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg204(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg205(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg206(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg207(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg208(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg209(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg210(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg211(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg212(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg213(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg214(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg215(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg216(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg217(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg218(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg219(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg220(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg221(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg222(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg223(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg224(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg225(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg226(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg227(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg228(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg229(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg230(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg231(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg232(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg233(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg234(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg235(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg236(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg237(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg238(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg239(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg240(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg241(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg242(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg243(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg244(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg245(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg246(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg247(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg248(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg249(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg250(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg251(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg252(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg253(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg254(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg255(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg256(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg257(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg258(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg259(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg260(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg261(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg262(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg263(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg264(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg265(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg266(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg267(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg268(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg269(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg270(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg271(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg272(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg273(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg274(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg275(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg276(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg277(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg278(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg279(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg280(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg281(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg282(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg283(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg284(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg285(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg286(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg287(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg288(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg289(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg290(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg291(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg292(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg293(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg294(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg295(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg296(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg297(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg298(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg299(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg300(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg301(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg302(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg303(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg304(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg305(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg306(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg307(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg308(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg309(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg310(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg311(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg312(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg313(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg314(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg315(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg316(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg317(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg318(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg319(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg320(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg321(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg322(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg323(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg324(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg325(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg326(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg327(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg328(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg329(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg330(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg331(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg332(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg333(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg334(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg335(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg336(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg337(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg338(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg339(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg340(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg341(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg342(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg343(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg344(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg345(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg346(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg347(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg348(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg349(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg350(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg351(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg352(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg353(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg354(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg355(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg356(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg357(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg358(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg359(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg360(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg361(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg362(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg363(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg364(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg365(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg366(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg367(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg368(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg369(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg370(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg371(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg372(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg373(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg374(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg375(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg376(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg377(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg378(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg379(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg380(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg381(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg382(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg383(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg384(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg385(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg386(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg387(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg388(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg389(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg390(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg391(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg392(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg393(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg394(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg395(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg396(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg397(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg398(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg399(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg400(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg401(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg402(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg403(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg404(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg405(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg406(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg407(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg408(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg409(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg410(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg411(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg412(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg413(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg414(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg415(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg416(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg417(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg418(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg419(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg420(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg421(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg422(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg423(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg424(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg425(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg426(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg427(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg428(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg429(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg430(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg431(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg432(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg433(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg434(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg435(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg436(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg437(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg438(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg439(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg440(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg441(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg442(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg443(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg444(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg445(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg446(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg447(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg448(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg449(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg450(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg451(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg452(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg453(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg454(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg455(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg456(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg457(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg458(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg459(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg460(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg461(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg462(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg463(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg464(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg465(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg466(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg467(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg468(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg469(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg470(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg471(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg472(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg473(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg474(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg475(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg476(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg477(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg478(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg479(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg480(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg481(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg482(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg483(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg484(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg485(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg486(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg487(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg488(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg489(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg490(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg491(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg492(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg493(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg494(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg495(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg496(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg497(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg498(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg499(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg500(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg501(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg502(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg503(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg504(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg505(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg506(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg507(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg508(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg509(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg510(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg511(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg512(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg513(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg514(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg515(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg516(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg517(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg518(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg519(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg520(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg521(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg522(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg523(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg524(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg525(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg526(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg527(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg528(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg529(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg530(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg531(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg532(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg533(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg534(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg535(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg536(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg537(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg538(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg539(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg540(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg541(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg542(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg543(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg544(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg545(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg546(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg547(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg548(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg549(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg550(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg551(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg552(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg553(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg554(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg555(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg556(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg557(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg558(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg559(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg560(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg561(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg562(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg563(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg564(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg565(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg566(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg567(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg568(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg569(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg570(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg571(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg572(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg573(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg574(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg575(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg576(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg577(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg578(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg579(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg580(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg581(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg582(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg583(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg584(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg585(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg586(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg587(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg588(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg589(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg590(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg591(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg592(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg593(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg594(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg595(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg596(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg597(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg598(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg599(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg600(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg601(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg602(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg603(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg604(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg605(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg606(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg607(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg608(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg609(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg610(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg611(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg612(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg613(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg614(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg615(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg616(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg617(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg618(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg619(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg620(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg621(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg622(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg623(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg624(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg625(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg626(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg627(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg628(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg629(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg630(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg631(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg632(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg633(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg634(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg635(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg636(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg637(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg638(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg639(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg640(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg641(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg642(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg643(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg644(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg645(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg646(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg647(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg648(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg649(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg650(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg651(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg652(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg653(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg654(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg655(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg656(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg657(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg658(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg659(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg660(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg661(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg662(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg663(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg664(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg665(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg666(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg667(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg668(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg669(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg670(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg671(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg672(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg673(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg674(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg675(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg676(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg677(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg678(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg679(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg680(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg681(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg682(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg683(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg684(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg685(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg686(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg687(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg688(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg689(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg690(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg691(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg692(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg693(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg694(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg695(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg696(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg697(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg698(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg699(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg700(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg701(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg702(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg703(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg704(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg705(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg706(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg707(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg708(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg709(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg710(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg711(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg712(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg713(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg714(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg715(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg716(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg717(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg718(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg719(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg720(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg721(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg722(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg723(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg724(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg725(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg726(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg727(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg728(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg729(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg730(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg731(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg732(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg733(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg734(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg735(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg736(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg737(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg738(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg739(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg740(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg741(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg742(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg743(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg744(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg745(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg746(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg747(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg748(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg749(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg750(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg751(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg752(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg753(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg754(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg755(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg756(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg757(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg758(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg759(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg760(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg761(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg762(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg763(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg764(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg765(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg766(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg767(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg768(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg769(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg770(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg771(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg772(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg773(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg774(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg775(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg776(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg777(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg778(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg779(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg780(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg781(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg782(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg783(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg784(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg785(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg786(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg787(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg788(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg789(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg790(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg791(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg792(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg793(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg794(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg795(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg796(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg797(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg798(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg799(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg800(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg801(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg802(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg803(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg804(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg805(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg806(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg807(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg808(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg809(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg810(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg811(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg812(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg813(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg814(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg815(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg816(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg817(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg818(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg819(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg820(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg821(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg822(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg823(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg824(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg825(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg826(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg827(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg828(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg829(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg830(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg831(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg832(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg833(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg834(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg835(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg836(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg837(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg838(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg839(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg840(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg841(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg842(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg843(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg844(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg845(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg846(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg847(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg848(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg849(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg850(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg851(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg852(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg853(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg854(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg855(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg856(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg857(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg858(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg859(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg860(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg861(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg862(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg863(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg864(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg865(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg866(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg867(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg868(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg869(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg870(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg871(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg872(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg873(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg874(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg875(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg876(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg877(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg878(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg879(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg880(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg881(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg882(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg883(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg884(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg885(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg886(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg887(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg888(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg889(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg890(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg891(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg892(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg893(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg894(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg895(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg896(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg897(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg898(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg899(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg900(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg901(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg902(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg903(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg904(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg905(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg906(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg907(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg908(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg909(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg910(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg911(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg912(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg913(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg914(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg915(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg916(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg917(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg918(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg919(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg920(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg921(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg922(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg923(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg924(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg925(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg926(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg927(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg928(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg929(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg930(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg931(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg932(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg933(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg934(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg935(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg936(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg937(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg938(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg939(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg940(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg941(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg942(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg943(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg944(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg945(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg946(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg947(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg948(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg949(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg950(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg951(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg952(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg953(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg954(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg955(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg956(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg957(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg958(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg959(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg960(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg961(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg962(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg963(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg964(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg965(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg966(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg967(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg968(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg969(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg970(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg971(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg972(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg973(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg974(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg975(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg976(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg977(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg978(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg979(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg980(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg981(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg982(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg983(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg984(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg985(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg986(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg987(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg988(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg989(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg990(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg991(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg992(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg993(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg994(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg995(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg996(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg997(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg998(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg999(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1000(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1001(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1002(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1003(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1004(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1005(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1006(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1007(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1008(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1009(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1010(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1011(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1012(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1013(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1014(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1015(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1016(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1017(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1018(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1019(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1020(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1021(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1022(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1023(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1024(b, min, max).
null(0) :- C(a, min, max) .
C(a, min, max) :- A(a, b) GROUP-BY(a) agg1025(b, min, max).
null(0) :- C(a, min, max) .


