%table A37(a:int4, b:int4)
%table B37(a:int4, b:int4, c:int4, d:int4)
%table C37(a:int4, min:int4, max:int4)

%function A37func001(a: int4, b: int4->c: int4, d: int4) {}
%function A37func002(a: int4, b: int4->c: int4, d: int4) {}
%function A37func003(a: int4, b: int4->c: int4, d: int4) {}
%function A37func004(a: int4, b: int4->c: int4, d: int4) {}
%function A37func005(a: int4, b: int4->c: int4, d: int4) {}
%function A37func006(a: int4, b: int4->c: int4, d: int4) {}
%function A37func007(a: int4, b: int4->c: int4, d: int4) {}
%function A37func008(a: int4, b: int4->c: int4, d: int4) {}


%aggregate A37agg001(a: int4->min: int4, max: int4) {ordered}
%aggregate A37agg002(a: int4->min: int4, max: int4) {ordered}
%aggregate A37agg003(a: int4->min: int4, max: int4) {ordered}
%aggregate A37agg004(a: int4->min: int4, max: int4) {ordered}
%aggregate A37agg005(a: int4->min: int4, max: int4) {ordered}
%aggregate A37agg006(a: int4->min: int4, max: int4) {ordered}
%aggregate A37agg007(a: int4->min: int4, max: int4) {ordered}
%aggregate A37agg008(a: int4->min: int4, max: int4) {ordered}


B37(a, b, c, d) :- A37(a, b), A37func001(a, b, c, d).
B37(a, b, c, d) :- A37(a, b), A37func002(a, b, c, d).
B37(a, b, c, d) :- A37(a, b), A37func003(a, b, c, d).
B37(a, b, c, d) :- A37(a, b), A37func004(a, b, c, d).
B37(a, b, c, d) :- A37(a, b), A37func005(a, b, c, d).
B37(a, b, c, d) :- A37(a, b), A37func006(a, b, c, d).
B37(a, b, c, d) :- A37(a, b), A37func007(a, b, c, d).
B37(a, b, c, d) :- A37(a, b), A37func008(a, b, c, d).



C37(a, min, max) :- A37(a, b) GROUP-BY(a) A37agg001(b, min, max).
null(0) :- C37(a, min, max) .
C37(a, min, max) :- A37(a, b) GROUP-BY(a) A37agg002(b, min, max).
null(0) :- C37(a, min, max) .
C37(a, min, max) :- A37(a, b) GROUP-BY(a) A37agg003(b, min, max).
null(0) :- C37(a, min, max) .
C37(a, min, max) :- A37(a, b) GROUP-BY(a) A37agg004(b, min, max).
null(0) :- C37(a, min, max) .
C37(a, min, max) :- A37(a, b) GROUP-BY(a) A37agg005(b, min, max).
null(0) :- C37(a, min, max) .
C37(a, min, max) :- A37(a, b) GROUP-BY(a) A37agg006(b, min, max).
null(0) :- C37(a, min, max) .
C37(a, min, max) :- A37(a, b) GROUP-BY(a) A37agg007(b, min, max).
null(0) :- C37(a, min, max) .
C37(a, min, max) :- A37(a, b) GROUP-BY(a) A37agg008(b, min, max).
null(0) :- C37(a, min, max) .




