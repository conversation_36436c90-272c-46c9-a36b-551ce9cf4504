/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "gm_udf.h"
#include "string.h"
#include "stdio.h"

#pragma pack(1)
// 输入字段--聚合字段
typedef struct INPUT {
    int32_t dtlReservedCount;
    uint8_t b[8];
    int32_t a;
    uint32_t strlenC;
    char *c;
} INPUT;
// 输出字段---三种类型
typedef struct OUTPUT {
    uint8_t b[8];
    int32_t a;
    uint32_t strlenC;
    char *c;
} OUTPUT;

#pragma pack(0)
// 分别先按整型，byte类型，str类型排序
int32_t dtl_agg_compare_agg(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    INPUT *inp1 = (INPUT *)tuple1;
    INPUT *inp2 = (INPUT *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        if (inp1->b[0] < inp2->b[0]) {
            return -1;
        } else if (inp1->b[0] > inp2->b[0]) {
            return 1;
        } else {
            // 取字符取错了，应该取第8位
            if (inp1->c[8] < inp2->c[8]) {
                return -1;
            } else if (inp1->c[8] > inp2->c[8]) {
                return 1;
            }
            return 0;
        }
    }
}
// 排完序，取最后一条记录
int32_t dtl_agg_func_agg(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    INPUT *inpStruct;
    OUTPUT *outStruct = GmUdfMemAlloc(ctx, sizeof(OUTPUT));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int ret;
    // 三个出参
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->a = inpStruct->a;
        for (int i = 0; i < 8; i++) {
            outStruct->b[i] = inpStruct->b[i];
        }
        outStruct->strlenC = inpStruct->strlenC;
        outStruct->c = (char *)GmUdfMemAlloc(ctx, outStruct->strlenC);
        if (outStruct->c == NULL) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        strcpy(outStruct->c, inpStruct->c);
    }

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
