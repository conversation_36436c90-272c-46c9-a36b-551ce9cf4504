/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : GMDBV502 迭代二 支持UDF
 Notes        : 本文件主要是规格约束测试
 History      :
 Author       : jiangshan/j00811785
 Modification : [2022.09.13]
*****************************************************************************/

#include "SupportUDF.h"

#if defined ENV_RTOSV2X
#define NAMESPACE_MAX_NUM 64 // iot上公共头文件中会创建一个namespace,所以iot减1
#else
#define NAMESPACE_MAX_NUM 64
#endif
#if defined ENV_RTOSV2X
#define AGG_MAX_NUM 32 // iot上公共头文件中会创建一个namespace,所以iot减1
#else
#define AGG_MAX_NUM 32
#endif

class SupportUDF_001_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SupportUDF_001_test::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    // 创建epoll监听线程
    int ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建连接
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void SupportUDF_001_test::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf ./planStrFile/*");
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 001.一个规则.d文件中定义1024个function，使用gmprecompiler编译生成.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_1024_functions");
    TestSetFileName(g_nsName);
    // 查看物理执行计划, 2024年3月19日变更，不支持gmprecompiler -s查看离线执行计划
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -s %s", g_toolPath, g_outputFile);
    system(g_command);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "init param failed. Exit with code %d", GMERR_INVALID_OPTION);
    ret = executeCommand(g_command, "[ERROR] The first option \"-s\" is undefined.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.一个规则.d文件中定义1025个function，使用gmprecompiler编译生成.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_1025_functions");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "there are 1025 UDFs which exceeds the maximum value 1024",
    "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.两个.d文件，每个.d文件定义1024个function，编译成2个so文件并加载
// 执行计划视图只展示了22个函数，一个PLAN_LIST字符超过13K会被截断
TEST_F(SupportUDF_001_test, DataLog_006_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = 2;
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_1024_functions_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_1024_functions_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_1024_functions_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_1024_functions_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_1024_functions_%03d", i);
        // 加载，加载第二个so失败
        (void)snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s -ns %s\n", g_toolPath, g_libName, g_testNameSpace);
        if (i == 1) {
            ret = executeCommand(g_command, "Command type: import_datalog, Import datalog file", "successfully");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d",GMERR_PROGRAM_LIMIT_EXCEEDED);
            ret = executeCommand(g_command, "Import datalog file unsucc", g_errorMsg);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 查看执行计划
    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, viewName);
    ret = executeCommand(g_command, "UDF: dtl_ext_func_func022");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 卸载
    for (int i = 1; i < num; i++) {
        (void)sprintf(g_nsName, "datalog_1024_functions_%03d", i);
        ret = TestUninstallDatalog(g_nsName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.64个.d文件，每个.d文件定义16个function，编译成64个so文件并加载
TEST_F(SupportUDF_001_test, DataLog_006_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = NAMESPACE_MAX_NUM;
    // 卸载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_nsName, "datalog_16_functions_%03d", i);
        ret = TestUninstallDatalog(g_nsName);
    }
    // 加载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_16_functions_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_16_functions_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_16_functions_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_16_functions_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_16_functions_%03d", i);
        // 加载
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    }
    // 再加载1个so, udf有1025个超过1024个上限
    (void)sprintf(g_libName, "./%s/datalog_function_%03d.so", g_orgFile, 65);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -s %s -c datalog -f %s -ns %s", g_toolPath, g_connServer,
        g_libName, g_testNameSpace);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    system(g_command);
    ret = executeCommand(g_command, "Import datalog file unsucc", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看执行计划
    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, viewName);
// 设备上加载63个so
#if defined(ENV_RTOSV2X)
    ret = executeCommand(g_command, "UDF: dtl_ext_func_A63func016", "index = 125");
    if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "index = 125", "UDF: dtl_ext_func_A63func016");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
#else
    ret = executeCommand(g_command, "UDF: dtl_ext_func_A64func016", "index = 127");
    if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "index = 127", "UDF: dtl_ext_func_A64func016");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
#endif
    // 卸载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_nsName, "datalog_16_functions_%03d", i);
        ret = TestUninstallDatalog(g_nsName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.一个规则.d文件中定义1024个aggregate，使用gmprecompiler编译生成.c文件
// agg函数只有一个算子，离线计划展示和func有区别
TEST_F(SupportUDF_001_test, DataLog_006_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_1024_aggregates");
    TestSetFileName(g_nsName);
    // 查看物理执行计划, 2024年3月19日变更，不支持gmprecompiler -s查看离线执行计划
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -s %s", g_toolPath, g_outputFile);
    system(g_command);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "init param failed. Exit with code %d", GMERR_INVALID_OPTION);
    ret = executeCommand(g_command, "[ERROR] The first option \"-s\" is undefined.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.一个规则.d文件中定义1025个aggregate，使用gmprecompiler编译生成.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_1025_aggregates");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "there are 1025 UDFs which exceeds the maximum value 1024",
    "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.两个.d文件，每个.d文件定义1024个aggregate，编译成2个so文件并加载
TEST_F(SupportUDF_001_test, DataLog_006_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = 2;
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_1024_aggregates_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_1024_aggregates_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_1024_aggregates_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_1024_aggregates_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_1024_aggregates_%03d", i);
        // 加载，加载第二个so失败
        (void)snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s -ns %s\n", g_toolPath, g_libName, g_testNameSpace);
        if (i == 1) {
            ret = executeCommand(g_command, "Command type: import_datalog, Import datalog file", "successfully");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1011000");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 查看执行计划
    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, viewName);
    // 执行计划视图只展示了975个函数，一个PLAN_LIST字符超过13K会被截断
    // 2023年9月14日特性合入，目前只展示981个函数
    ret = executeCommand(g_command, "Aggregate Function: dtl_agg_func_agg981");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 卸载
    for (int i = 1; i < num; i++) {
        (void)sprintf(g_nsName, "datalog_1024_aggregates_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.64个.d文件，每个.d文件定义16个aggregate，编译成64个so文件并加载
TEST_F(SupportUDF_001_test, DataLog_006_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = NAMESPACE_MAX_NUM;
    // 卸载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_nsName, "datalog_16_aggregates_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    // 加载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_16_aggregates_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_16_aggregates_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_16_aggregates_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_16_aggregates_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_16_aggregates_%03d", i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    }
    // 查看执行计划
    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, viewName);
#if defined(ENV_RTOSV2X)
    ret = executeCommand(g_command, "Aggregate Function: dtl_agg_func_A63agg016", "index = 125");
    if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "index = 125", "Aggregate Function: dtl_agg_func_A63agg016");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
#else
    ret = executeCommand(g_command, "Aggregate Function: dtl_agg_func_A64agg016", "index = 127");
        if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "index = 127", "Aggregate Function: dtl_agg_func_A64agg016");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
#endif
    // 卸载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_nsName, "datalog_16_aggregates_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 比较函数计算在1024的规格中
// 009.一个规则.d文件中定义512个排序函数（排序函数存在的情况下，aggregate函数肯定存在且个数相等，所以总共是1024个函数），使用gmprecompiler编译生成.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_1024_orders");
    TestSetFileName(g_nsName);
    // 查看物理执行计划, 2024年3月19日变更，不支持gmprecompiler -s查看离线执行计划
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -s %s", g_toolPath, g_outputFile);
    system(g_command);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "init param failed. Exit with code %d", GMERR_INVALID_OPTION);
    ret = executeCommand(g_command, "[ERROR] The first option \"-s\" is undefined.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 比较函数不计算在1024的规格中
// 010.一个规则.d文件中定义1025个排序函数（排序函数存在的情况下，aggregate函数肯定存在且个数相等，所以总共是1025个函数），使用gmprecompiler编译生成.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_1025_orders");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "there are 2050 UDFs which exceeds the maximum value 1024",
    "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.两个.d文件，每个.d文件定义512个排序函数（排序函数存在的情况下，aggregate函数肯定存在且个数相等，所以总共是1024个函数），编译成2个so文件并加载
TEST_F(SupportUDF_001_test, DataLog_006_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = 2;
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_1024_orders_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_1024_orders_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_1024_orders_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_1024_orders_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_1024_orders_%03d", i);
        // 加载，加载第二个so失败
        (void)snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s -ns %s\n", g_toolPath, g_libName, g_testNameSpace);
        if (i == 1) {
            ret = executeCommand(g_command, "Command type: import_datalog, Import datalog file", "successfully");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1011000");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 添加PTL_DATALOG_SO_INFO视图校验
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        "V\\$PTL_DATALOG_SO_INFO");
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 卸载
    for (int i = 1; i < num; i++) {
        (void)sprintf(g_nsName, "datalog_1024_orders_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.32个.d文件，每个.d文件定义16个排序函数（排序函数存在的情况下，aggregate函数肯定存在且个数相等，所以每个.d是16个函数），编译成64个so文件并加载
TEST_F(SupportUDF_001_test, DataLog_006_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = AGG_MAX_NUM;
    // 卸载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_nsName, "datalog_16_orders_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    // 加载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_16_orders_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_16_orders_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_16_orders_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_16_orders_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_16_orders_%03d", i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    }
    // 查看执行计划
    char const *viewName = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, viewName);
#if defined(ENV_RTOSV2X)
    ret = executeCommand(g_command, "Comparison Function: dtl_agg_compare_A31agg016", "index = 61");
    if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "index = 63", "Comparison Function: dtl_agg_compare_A31agg016");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
#else
    ret = executeCommand(g_command, "Comparison Function: dtl_agg_compare_A32agg016", "index = 63");
    if (ret != GMERR_OK) {
        ret = executeCommand(g_command, "index = 63", "Comparison Function: dtl_agg_compare_A32agg016");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
#endif
    // 卸载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_nsName, "datalog_16_orders_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.一个规则.d文件中定义1024个函数，其中有512个function函数、256个aggregate函数、512个排序函数，使用gmprecompiler编译生成.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_512_func_512_agg_512_order");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.一个规则.d文件中定义1025个函数，其中有513个function函数、256个aggregate函数、256个排序函数，使用gmprecompiler编译生成.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_513_func_512_agg_512_order");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "there are 1025 UDFs which exceeds the maximum value 1024",
    "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.两个.d文件，每个.d文件定义1024个函数，其中有512个function函数、256个aggregate函数、256个排序函数，编译成2个so文件并加载
TEST_F(SupportUDF_001_test, DataLog_006_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = 2;
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_512_func_512_agg_512_order_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_512_func_512_agg_512_order_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_512_func_512_agg_512_order_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_512_func_512_agg_512_order_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_512_func_512_agg_512_order_%03d", i);
        // 加载，加载第二个so失败
        (void)snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s -ns %s\n", g_toolPath, g_libName, g_testNameSpace);
        if (i == 1) {
            ret = executeCommand(g_command, "Command type: import_datalog, Import datalog file", "successfully");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1011000");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 卸载
    for (int i = 1; i < num; i++) {
        (void)sprintf(g_nsName, "datalog_512_func_512_agg_512_order_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.32个.d文件，每个.d文件定义16个函数，其中有16个function函数、8个aggregate函数、8个排序函数，编译成64个so文件并加载
TEST_F(SupportUDF_001_test, DataLog_006_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = AGG_MAX_NUM;
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_8_func_8_agg_8_order_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_8_func_8_agg_8_order_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_8_func_8_agg_8_order_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_8_func_8_agg_8_order_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_8_func_8_agg_8_order_%03d", i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    }
    // 卸载
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_nsName, "datalog_8_func_8_agg_8_order_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017. access_delta或者access_current全部大写
TEST_F(SupportUDF_001_test, DataLog_006_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_ACCESS_DELTA");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "invalid option in aggregate \"agg\": ACCESS_DELTA", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(g_nsName, "datalog_ACCESS_CURRENT");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "invalid option in aggregate \"agg\": ACCESS_CURRENT", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018. access_delta或者access_current大小写混用
TEST_F(SupportUDF_001_test, DataLog_006_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_AccESS_dEltA_01");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "invalid option in aggregate \"agg\": AccESS_dEltA", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(g_nsName, "datalog_ACCESS_current_01");
    TestSetFileName(g_nsName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "invalid option in aggregate \"agg\": ACCESS_current", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019. access_delta或者access_current字符串前中后包含其他字母、数字、符号
TEST_F(SupportUDF_001_test, DataLog_006_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    // option前面多了一个字母a
    (void)sprintf(g_inputFile, "./%s/datalog_aaccess_delta.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_aaccess_delta.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "invalid option in aggregate \"agg\": aaccess_delta", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // option中间多了一个下划线
    (void)sprintf(g_inputFile, "./%s/datalog_access__current.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access__current.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "invalid option in aggregate \"agg\": access__current", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // option后面多了一个数字
    (void)sprintf(g_inputFile, "./%s/datalog_access_current1.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current1.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "invalid option in aggregate \"agg\": access_current1", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020. access_delta或者access_current后面没有括号
TEST_F(SupportUDF_001_test, DataLog_006_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_no_bracket.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_no_bracket.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "Error: \"access_delta\" should have 1 to 1000 parameters, actually "
        "we have 0 parameters in relation \"agg\" near line 7.", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)sprintf(g_inputFile, "./%s/datalog_access_current_no_bracket.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_no_bracket.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "Error: \"access_current\" should have 1 to 1000 parameters, actually "
        "we have 0 parameters in relation \"agg\" near line 7.", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021. access_delta或者access_current中文括号、方括号、花括号、左右括号不一致
TEST_F(SupportUDF_001_test, DataLog_006_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    // 中文括号
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_bracket_err_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_bracket_err_01.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, unknown character near line 7", "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 方括号[]
    (void)sprintf(g_inputFile, "./%s/datalog_access_current_bracket_err_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_bracket_err_01.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "Failed to parse datalog program, unknown character near line 7", "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 花括号{}
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_bracket_err_02.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_bracket_err_02.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: '{' near line 7", "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 左右括号不一致
    (void)sprintf(g_inputFile, "./%s/datalog_access_current_bracket_err_02.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_bracket_err_02.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: '}' near line 8", "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022. access_delta或者access_current括号内容为空
TEST_F(SupportUDF_001_test, DataLog_006_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_bracket_null.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_bracket_null.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: ')' near line 7",
    "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(g_inputFile, "./%s/datalog_access_current_bracket_null.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_bracket_null.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: ')' near line 7",
    "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023. access_delta或者access_current括号内容为纯数字、包含非法字符
TEST_F(SupportUDF_001_test, DataLog_006_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_err_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_err_01.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: '*' near line 7",
    "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(g_inputFile, "./%s/datalog_access_current_err_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_err_01.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "\"111\" find namespace prefix unsuccessfully in rule near line 6",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024. access_delta或者access_current括号内容(table1,)、（,table1）、（table()）
TEST_F(SupportUDF_001_test, DataLog_006_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    // (table,)
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_err_02.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_err_02.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: ')' near line 7",
    "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // (,table)
    (void)sprintf(g_inputFile, "./%s/datalog_access_current_err_02.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_err_02.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: ',' near line 7",
    "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // (table())
    (void)sprintf(g_inputFile, "./%s/datalog_access_current_err_03.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_err_03.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: ')' near line 7",
    "Fail to parse datalog file. Exit with code 1009000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025. access_delta或者access_current表名没有定义过
TEST_F(SupportUDF_001_test, DataLog_006_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_err_03.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_err_03.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "\"c\" find namespace prefix unsuccessfully in rule near line 6",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(g_inputFile, "./%s/datalog_access_current_err_04.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_err_04.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "\"c\" find namespace prefix unsuccessfully in rule near line 6",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 026. access_delta或者access_current表名namespace里已经定义，但是表名没有使用namespace前缀
TEST_F(SupportUDF_001_test, DataLog_006_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_err_04.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_err_04.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "\"C\" find namespace prefix unsuccessfully in rule near line 9",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(g_inputFile, "./%s/datalog_access_current_err_05.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_err_05.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "\"C\" find namespace prefix unsuccessfully in rule near line 8",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027.access_delta或者access_current表名定义在后，使用在前
TEST_F(SupportUDF_001_test, DataLog_006_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_err_05.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_err_05.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028.规则文件中使用新增选项，access_delta或者access_current表名重复，分两种情况，全局表和namespace里的表
TEST_F(SupportUDF_001_test, DataLog_006_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    // 全局表，表名重复
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_err_06.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_err_06.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "duplicate table name in \"access_delta\" of relation \"agg\": C near line 8.",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // namespace表，表名重复
    (void)sprintf(g_inputFile, "./%s/datalog_access_current_err_06.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_current_err_06.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    ret = executeCommand(g_command, "duplicate table name in \"access_current\" of relation \"agg\": C near line 10.",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2022091907048
// 029.规则文件中使用新增选项，access_delta或者access_current里面既有全局表又有namespace里的表
TEST_F(SupportUDF_001_test, DataLog_006_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_delta_err_07.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_delta_err_07.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2022091907048
// 030. access_delta或者access_current不同namespace，表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_01.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2022091907048
// 031.access_delta或者access_current不使用using namespace，表名形式为n1.table1
TEST_F(SupportUDF_001_test, DataLog_006_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_02.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_02.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.access_delta或者access_current使用using namespace，表名形式为table1
TEST_F(SupportUDF_001_test, DataLog_006_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_03.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_03.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2022091907048
// 033.access_delta或者access_current使用using namespace，表名形式为n1.table1
TEST_F(SupportUDF_001_test, DataLog_006_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_04.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_04.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2022091907048
// 034. access_delta和access_current包含同一张表
TEST_F(SupportUDF_001_test, DataLog_006_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_01.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035. access_delta和access_current不同表
TEST_F(SupportUDF_001_test, DataLog_006_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_05.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_05.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036. access_delta和access_current使用先后顺序，access_delta在前，access_current在后
TEST_F(SupportUDF_001_test, DataLog_006_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_05.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_05.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037. access_delta和access_current使用先后顺序，access_delta在后，access_current在前
TEST_F(SupportUDF_001_test, DataLog_006_001_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_06.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_06.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 038. 规则文件中只使用access_delta
TEST_F(SupportUDF_001_test, DataLog_006_001_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_07.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_07.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 039. 规则文件中只使用access_current
TEST_F(SupportUDF_001_test, DataLog_006_001_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_08.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_08.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 040. 规则文件中包含多个access_delta
TEST_F(SupportUDF_001_test, DataLog_006_001_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_09.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_09.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "duplicate option in relation \"agg\": \"access_delta\"", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041. 规则文件中包含多个access_current
TEST_F(SupportUDF_001_test, DataLog_006_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_10.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_10.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "duplicate option in relation \"agg\": \"access_current\"", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042. access_delta或者access_current读写普通表、资源表、transient表
TEST_F(SupportUDF_001_test, DataLog_006_001_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_11.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_11.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期成功
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043. access_delta或者access_current读写func
TEST_F(SupportUDF_001_test, DataLog_006_001_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_12.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_12.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "wrong access table of relation \"agg\": "
        "no table or resource named \"func\" is found.",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 044. access_delta或者access_current读写agg
TEST_F(SupportUDF_001_test, DataLog_006_001_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_succ_13.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_succ_13.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期失败
    int ret = executeCommand(g_command, "wrong access table of relation", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045. access_delta或者access_current读输入表自己，编译的时候就校验
TEST_F(SupportUDF_001_test, DataLog_006_001_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_read_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_read_01.c", g_destFile);
    // .d->.c
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "illegal IR Plan", "dtl_ext_func_func udf contains current delta table",
        "Fail to rewrite. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 046. access_delta写输入表自己，编译的时候就校验
TEST_F(SupportUDF_001_test, DataLog_006_001_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_access_write_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_access_write_01.c", g_destFile);
    // .d->.c
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "illegal IR Plan", "dtl_ext_func_func udf contains current delta table",
        "Fail to rewrite. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 047. access_delta或者access_current读输出表自己，运行时校验
TEST_F(SupportUDF_001_test, DataLog_006_001_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_access_read_02");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);

    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 048. access_delta写输出表自己，运行时校验
TEST_F(SupportUDF_001_test, DataLog_006_001_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_access_write_02");
    TestSetFileName(g_nsName);
 
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行,func里对B_delta写操作
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // B(a, b, c) :- A(a, b), func(a, b, c).
    // 如果func里没有写B，结果应该是{{1, 2, 3, 1}, {2, 1, 3, 1}, {3, 3, 6, 1}}
    // func写B {{1, 2, 3, -2}, {2, 1, 3, 10}, {4, 4, 6, 10}, {5, 5, 6, -3}}
    // A表3条记录，func会执行三次，写B表也会执行三次，注意count预期值
    // 预期是{{1, 2, 3, -5}, {2, 1, 3, 31}, {3, 3, 6, 1}, {4, 4, 6, 30}, {5, 5, 6, -9}}
    int record = 5; // 输出表记录数
    int32_t result[record][4] = {{5, 5, 6, -9}, {2, 1, 3, 31}, {1, 2, 3, -5}, {4, 4, 6, 30}, {3, 3, 6, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049. func函数没有在.d文件中使用%function显示声明直接使用
TEST_F(SupportUDF_001_test, DataLog_006_001_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_err_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_err_01.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期报错
    int ret = executeCommand(g_command, "find namespace prefix unsuccessfully in rule near line 7",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 050. agg函数没有在.d文件中使用%aggregate显示声明直接使用
TEST_F(SupportUDF_001_test, DataLog_006_001_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_err_02.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_err_02.c", g_destFile);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    // 预期报错
    int ret = executeCommand(g_command, "find namespace prefix unsuccessfully in rule near line 11",
        "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// DTS2022092106572
// 051.规则文件中使用的function函数没有在udf.c文件中定义
TEST_F(SupportUDF_001_test, DataLog_006_001_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_01");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s -ns %s\n", g_toolPath, g_libName, g_testNameSpace);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 加载失败后再次加载正确的同名so
    (void)sprintf(g_nsName, "datalog_udf_01_001");
    TestSetFileName(g_nsName);

    // 再次加载
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s -ns %s\n", g_toolPath, g_libName, g_testNameSpace);
    ret = executeCommand(g_command, "Command type: import_datalog, Import datalog file", "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 原先卸载的是namespace的名称，现在改为so的名称
    ret = TestUninstallDatalog("datalog_udf_01_001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.规则文件中使用的aggregate函数没有在udf.c文件中定义
TEST_F(SupportUDF_001_test, DataLog_006_001_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_02");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}
// 053. aggregate函数在udf文件中命名没有使用默认前缀
TEST_F(SupportUDF_001_test, DataLog_006_001_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_agg_err_01");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054. function函数在udf文件中命名没有使用默认前缀
TEST_F(SupportUDF_001_test, DataLog_006_001_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_func_err_01");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055. aggregate函数在udf文件中命名默认前缀错误
TEST_F(SupportUDF_001_test, DataLog_006_001_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_agg_err_02");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 056. function函数在udf文件中命名默认前缀错误
TEST_F(SupportUDF_001_test, DataLog_006_001_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_func_err_02");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.使用%aggregate定义聚合函数agg，使用ordered选项，udf文件里没有定义排序函数
TEST_F(SupportUDF_001_test, DataLog_006_001_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_order_err_01");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.使用%aggregate定义聚合函数agg，使用ordered选项，udf文件里的排序函数名称没有使用默认前缀
TEST_F(SupportUDF_001_test, DataLog_006_001_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_order_err_02");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}
// 059.使用%aggregate定义聚合函数agg，使用ordered选项，udf文件里的排序函数名称默认前缀错误
TEST_F(SupportUDF_001_test, DataLog_006_001_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_order_err_03");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060.表中已经有31个索引，分组字段只有一个，没有显示定义为索引
TEST_F(SupportUDF_001_test, DataLog_006_001_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_31_indexes");
    TestSetFileName(g_nsName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    int ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 061.表中已经有32个索引，分组字段只有一个，没有显示定义为索引
TEST_F(SupportUDF_001_test, DataLog_006_001_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_32_indexes.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_32_indexes.c", g_destFile);
    // .d->.c,预期编译报错
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "has a maximum of 32 indexes, failed to add index for GROUP-BY fields",
    "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 062.表中已经有31个索引，分组字段有两个group by(a,b)，分组字段a和b没有显示定义为索引
TEST_F(SupportUDF_001_test, DataLog_006_001_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_31_indexes_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_31_indexes_01.c", g_destFile);
    // .d->.c,预期编译报warning
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Notice: auto added index1 on GROUP-BY fields for C",
        "Notice: auto added index31 on GROUP-BY fields for A", "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 063.表中已经有31个索引，分组字段有两个group by(a,b)，表中存在的索引是(a,b,c)
TEST_F(SupportUDF_001_test, DataLog_006_001_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_31_indexes_02.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_31_indexes_02.c", g_destFile);
    // .d->.c,预期编译报warning
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Notice: auto added index1 on GROUP-BY fields for C",
        "Notice: auto added index31 on GROUP-BY fields for A", "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 064.表中已经有31个索引，分组字段有两个group by(a,b)，表中存在的索引是(a)和(b)
TEST_F(SupportUDF_001_test, DataLog_006_001_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_31_indexes_03.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_31_indexes_03.c", g_destFile);
    // .d->.c,预期编译报warning
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Notice: auto added index1 on GROUP-BY fields for C",
        "Notice: auto added index31 on GROUP-BY fields for A", "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 065.表中已经有32个索引，分组字段有两个group by(a,b)，分组字段a和b没有显示定义为索引
TEST_F(SupportUDF_001_test, DataLog_006_001_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_32_indexes_01.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_32_indexes_01.c", g_destFile);
    // .d->.c,预期编译报错
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "has a maximum of 32 indexes, failed to add index for GROUP-BY fields",
    "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 066.表中已经有32个索引，分组字段有两个group by(a,b)，表中存在的索引是(a,b,c)
TEST_F(SupportUDF_001_test, DataLog_006_001_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_32_indexes_02.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_32_indexes_02.c", g_destFile);
    // .d->.c,预期编译报错
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "has a maximum of 32 indexes, failed to add index for GROUP-BY fields",
    "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 067.表中已经有32个索引，分组字段有两个group by(a,b)，表中存在的索引是(a)和(b)
TEST_F(SupportUDF_001_test, DataLog_006_001_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_inputFile, "./%s/datalog_udf_32_indexes_03.d", g_orgFile);
    (void)sprintf(g_outputFile, "./%s/datalog_udf_32_indexes_03.c", g_destFile);
    // .d->.c,预期编译报错
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "has a maximum of 32 indexes, failed to add index for GROUP-BY fields",
    "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 068.UDF函数里对表进行操作导致递归
TEST_F(SupportUDF_001_test, DataLog_006_001_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_loop_01");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写C表,触发func执行，func里写C表，形成死循环
    char tableC[] = "C";
    int32_t count[1][3] = {{1, 2, 1}};
    // 预期这里挂死
    ret = batchA(g_conn, g_stmt, tableC, count, 1);
    // 禁用流控会返回1011000错误码
    AW_MACRO_ASSERT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    TestDatalogCheckFlowRet(ret, GMERR_OK, GMERR_PROGRAM_LIMIT_EXCEEDED);
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 069.UDF函数里对全量表进行全表读和索引读操作
// 二级索引读出来的数据校验有点问题
TEST_F(SupportUDF_001_test, DataLog_006_001_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_org_01");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    // 写C表,C表和A表表结构一样
    int32_t countC[][3] = {{1, 2, 1}, {2, 2, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableC, countC, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写A表,触发func执行
    int32_t countA[][3] = {{1, 2, -3}, {2, 2, 2}, {4, 4, 1}};
    ret = batchA(g_conn, g_stmt, tableA, countA, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{4, 4, 8, 1}, {1, 2, 3, -1}, {2, 2, 4, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 070.UDF函数里对增量表进行全表读（增量表没有索引读）
TEST_F(SupportUDF_001_test, DataLog_006_001_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_delta_01");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 1; // 输出表记录数
    int32_t result[record][4] = {{1, 2, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 071.UDF函数里对增量表进行写操作
TEST_F(SupportUDF_001_test, DataLog_006_001_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_write_delta_01");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableD[] = "D";
    int32_t count[][3] = {{1, 2, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 1; // 输出表记录数
    int32_t result[1][4] = {{1, 2, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    // 查C表，验证写C_delta成功
    int32_t resultC[1][3] = {{3, 6, 1}};
    readOutB02(g_stmt, tableC, 1, resultC);
    // 查D表，验证写C_delta成功
    readOutB02(g_stmt, tableD, 1, resultC);

    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 072.UDF函数里对全量表进行索引读，索引不存在操作
TEST_F(SupportUDF_001_test, DataLog_006_001_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_index_read_org_01");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    // 写C表,C表和A表表结构一样
    int32_t countC[][3] = {{1, 2, 1}, {2, 2, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableC, countC, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写A表,触发func执行,func里读C表
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestDatalogCheckFlowRet(ret, GMERR_OK, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED));
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 073.UDF函数里对增量表C进行读操作，但是规则文件中的函数定义是access_delta(B)
//  B(a, b, c), C(a, b)，需要补充一个C字段比B字段少的用例
TEST_F(SupportUDF_001_test, DataLog_006_001_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_delta_02");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestDatalogCheckFlowRet(ret, GMERR_OK, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED));
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 074.UDF函数里对全量表C进行读操作，但是规则文件中的函数定义是access_current(B)
TEST_F(SupportUDF_001_test, DataLog_006_001_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_org_02");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    // 写C表,C表和A表表结构一样
    int32_t countC[][3] = {{1, 2, 1}, {2, 2, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableC, countC, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写A表,触发func执行
    // 规则文件是对B操作，读出来数据为0，函数内部报错
    int32_t countA[][3] = {{1, 2, -3}, {2, 2, 2}, {4, 4, 1}};
    ret = batchA(g_conn, g_stmt, tableA, countA, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestDatalogCheckFlowRet(ret, GMERR_OK, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED));
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 075.UDF函数里对增量表C进行写操作，但是规则文件中的函数定义是access_delta(B)
TEST_F(SupportUDF_001_test, DataLog_006_001_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_write_delta_02");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestDatalogCheckFlowRet(ret, GMERR_OK, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED));

    // 如果不写B表，输出应该是 {{1, 2, 3, 1}, {2, 1, 3, 1}, {3, 3, 6, 1}}
    // 函数里写了B: {{3, 6, 1, x}, {3, 6, 1, x}, {6, 12, 1, x}}
    // B表最终会是 {{1, 2, 3, 1}, {2, 1, 3, 1}, {3, 3, 6, 1}, {3, 6, 1, x}, {6, 12, 1, x}}
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 076.UDF函数里对全量表C进行读操作，但是规则文件中的函数定义是access_delta(C)
TEST_F(SupportUDF_001_test, DataLog_006_001_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_VALUE);
    (void)snprintf(g_errorCode04, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(4, g_errorCode01, g_errorCode02, g_errorCode03, g_errorCode04);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_org_03");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestDatalogCheckFlowRet(ret, GMERR_OK, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED));
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 077.UDF函数里对增量表C进行读操作，但是规则文件中的函数定义是access_current(C)
TEST_F(SupportUDF_001_test, DataLog_006_001_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_VALUE);
    (void)snprintf(g_errorCode04, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(4, g_errorCode01, g_errorCode02, g_errorCode03, g_errorCode04);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_delta_03");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestDatalogCheckFlowRet(ret, GMERR_OK, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED));
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 078.规则文件中定义access_delta(C,B)和access_current(C,B),但是udf函数里只对delta_C操作，没有对org_C进行操作
TEST_F(SupportUDF_001_test, DataLog_006_001_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_04");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 079.规则文件中定义access_delta(C,B)和access_current(C,B),但是udf函数里只对org_C操作，没有对delta_C进行操作
TEST_F(SupportUDF_001_test, DataLog_006_001_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_read_02");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 080.规则文件中定义access_delta(C,B)和access_current(C,B),但是udf函数里只对B操作，没有对C进行操作
TEST_F(SupportUDF_001_test, DataLog_006_001_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_read_03");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 081.规则文件中没有使用函数，UDF函数文件为空，gcc编译时包含空的UDF文件
TEST_F(SupportUDF_001_test, DataLog_006_001_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_no_udf");
    TestSetFileName(g_nsName);
    
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[record][3] = {{3, 3, 1}, {1, 2, 1}, {2, 1, 1}};
    // scan
    readOutB02(g_stmt, tableB, record, result);
    // 卸载
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 082.UDF函数.c文件中有语法错误
TEST_F(SupportUDF_001_test, DataLog_006_001_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_err_03");
    TestSetFileName(g_nsName);
    // .d->.c
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile, g_outputFile, g_udfFile, g_libName);
    // 没办法验证报错
    system(g_command);
}

// 083.UDF函数.c文件内函数名重复，参数不一样
TEST_F(SupportUDF_001_test, DataLog_006_001_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_err_04");
    TestSetFileName(g_nsName);
    // .d->.c
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile, g_outputFile, g_udfFile, g_libName);
    // 没办法验证报错
    system(g_command);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 084.UDF函数.c文件内函数名重复，参数也一样
TEST_F(SupportUDF_001_test, DataLog_006_001_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_err_05");
    TestSetFileName(g_nsName);
    // .d->.c
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile, g_outputFile, g_udfFile, g_libName);
    // 没办法验证报错
    system(g_command);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 085.规则文件中的function函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_err_03");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "have duplicate name", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 086.规则文件中的function函数名称和namespace名称一样
TEST_F(SupportUDF_001_test, DataLog_006_001_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_err_04");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 087.规则文件中的function在namespace中，函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_06");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "func_01";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 088.规则文件中的表在namespace中，函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_07");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "ns_01.func_01";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 089.规则文件中的函数和表在同一个namespace中，函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_08");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "have duplicate name", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 090.规则文件中的函数和表在不同的namespace中，函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_09");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "ns_02.func_01";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 091.规则文件中的agg函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_err_09");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "have duplicate name", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 092.规则文件中的agg函数名称和namespace名称一样
TEST_F(SupportUDF_001_test, DataLog_006_001_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_06");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {1, 1, 2}, {2, 3, 1}, {3, 3, 1}, {3, 5, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{2, 3, 3, 1}, {1, 1, 2, 1}, {3, 3, 5, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 093.规则文件中的agg在namespace中，函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_07");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "agg";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {1, 1, 2}, {2, 3, 1}, {3, 3, 1}, {3, 5, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{2, 3, 3, 1}, {1, 1, 2, 1}, {3, 3, 5, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 094.规则文件中的表在namespace中，函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_08");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "ns_01.agg";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {1, 1, 2}, {2, 3, 1}, {3, 3, 1}, {3, 5, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{2, 3, 3, 1}, {1, 1, 2, 1}, {3, 3, 5, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 095.规则文件中的函数和表在同一个namespace中，函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_09");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "have duplicate name", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 096.规则文件中的函数和表在不同的namespace中，函数名称和表名一样
TEST_F(SupportUDF_001_test, DataLog_006_001_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_10");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "ns_02.agg";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {1, 1, 2}, {2, 3, 1}, {3, 3, 1}, {3, 5, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{2, 3, 3, 1}, {1, 1, 2, 1}, {3, 3, 5, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 097.规则文件中的function函数名称和agg函数名称一样
TEST_F(SupportUDF_001_test, DataLog_006_001_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_08");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "have duplicate name", "Fail to verify datalog file. Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 098.规则文件中的function在namespace中，function函数名称和agg函数名称一样
TEST_F(SupportUDF_001_test, DataLog_006_001_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_09");
    (void)sprintf(g_inputFile, "./%s/%s.d", g_orgFile, g_nsName);
    (void)sprintf(g_outputFile, "./%s/%s.c", g_destFile, g_nsName);
    (void)sprintf(g_libName, "./%s/%s.so", g_destFile, g_nsName);

    char g_udfFile01[FILE_PATH] = {};
    char g_udfFile02[FILE_PATH] = {};
    (void)sprintf(g_udfFile01, "./%s/%s_udf_001.c", g_orgFile, g_nsName);
    (void)sprintf(g_udfFile02, "./%s/%s_udf_002.c", g_orgFile, g_nsName);

    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s %s -o %s \n", g_hFile, g_outputFile, g_udfFile01, g_udfFile02, g_libName);
    system(g_command);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    int32_t count[][3] = {{1, 2, 1}, {1, 1, 2}, {2, 3, 1}, {3, 3, 1}, {3, 5, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{2, 3, 3, 1}, {1, 1, 2, 1}, {3, 3, 5, 1}};
    AW_FUN_Log(LOG_STEP, "read table B start:");
    readOutB01(g_stmt, tableB, record, result);
    
    // 读C表
    int32_t resultC[5][4] = {{2, 3, 5, 1}, {1, 2, 3, 1}, {3, 5, 8, 1}, {3, 3, 6, 1}, {1, 1, 2, 1}};
    AW_FUN_Log(LOG_STEP, "read table C start:");
    readOutB01(g_stmt, tableC, 5, resultC);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 099.规则文件中的agg在namespace中，function函数名称和agg函数名称一样
TEST_F(SupportUDF_001_test, DataLog_006_001_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_10");
    (void)sprintf(g_inputFile, "./%s/%s.d", g_orgFile, g_nsName);
    (void)sprintf(g_outputFile, "./%s/%s.c", g_destFile, g_nsName);
    (void)sprintf(g_libName, "./%s/%s.so", g_destFile, g_nsName);

    char g_udfFile01[FILE_PATH] = {};
    char g_udfFile02[FILE_PATH] = {};
    (void)sprintf(g_udfFile01, "./%s/%s_udf_001.c", g_orgFile, g_nsName);
    (void)sprintf(g_udfFile02, "./%s/%s_udf_002.c", g_orgFile, g_nsName);

    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s %s -o %s \n", g_hFile, g_outputFile, g_udfFile01, g_udfFile02, g_libName);
    system(g_command);
    // 加载
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    int32_t count[][3] = {{1, 2, 1}, {1, 1, 2}, {2, 3, 1}, {3, 3, 1}, {3, 5, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{2, 3, 3, 1}, {1, 1, 2, 1}, {3, 3, 5, 1}};
    AW_FUN_Log(LOG_STEP, "read table B start:");
    readOutB01(g_stmt, tableB, record, result);
    
    // 读C表
    int32_t resultC[5][4] = {{2, 3, 5, 1}, {1, 2, 3, 1}, {3, 5, 8, 1}, {3, 3, 6, 1}, {1, 1, 2, 1}};
    AW_FUN_Log(LOG_STEP, "read table C start:");
    readOutB01(g_stmt, tableC, 5, resultC);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 100.两个UDF函数.c文件函数名重复，编译成一个so
TEST_F(SupportUDF_001_test, DataLog_006_001_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_access_read_02_100");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s %s -o %s \n", g_hFile, g_outputFile, g_udfFile, g_udfFile, g_libName);
    // 预期报错
    system(g_command);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 101.两个UDF函数.c文件函数名重复，编译成两个so
TEST_F(SupportUDF_001_test, DataLog_006_001_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    int ret;
    int num = 2;
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_inputFile, "./%s/datalog_access_read_02_101_%03d.d", g_orgFile, i);
        (void)sprintf(g_udfFile, "./%s/datalog_access_read_02_101_%03d_udf.c", g_orgFile, i);
        (void)sprintf(g_outputFile, "./%s/datalog_access_read_02_101_%03d.c", g_destFile, i);
        (void)sprintf(g_libName, "./%s/datalog_access_read_02_101_%03d.so", g_destFile, i);
        (void)sprintf(g_nsName, "datalog_access_read_02_101_%03d", i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    }
    for (int i = 1; i <= num; i++) {
        (void)sprintf(g_nsName, "datalog_access_read_02_101_%03d", i);
        TestUninstallDatalog(g_nsName);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 102.定义的UDF函数在规则文件中没有使用
TEST_F(SupportUDF_001_test, DataLog_006_001_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_read_org_02");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 运行
    int ret = 0;    // 写A表,触发func执行
    char tableA[] = "A";
    char tableB[] = "B";
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 2}, {3, 3, 1}};
    ret = batchA(g_conn, g_stmt, tableA, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 3; // 输出表记录数
    int32_t result[3][4] = {{1, 2, 3, 1}, {3, 3, 6, 1}, {2, 1, 3, 1}};
    // scan
    readOutB01(g_stmt, tableB, record, result);
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 103.规则文件.d定义的func函数没有使用
TEST_F(SupportUDF_001_test, DataLog_006_001_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_err_05");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "function \"sum\" is defined but not used in the rules", "Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 104.规则文件.d定义的agg函数没有使用
TEST_F(SupportUDF_001_test, DataLog_006_001_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_err_06");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "aggregate \"sum\" is defined but not used in the rules", "Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 105.规则文件.d文件中重复定义func函数
TEST_F(SupportUDF_001_test, DataLog_006_001_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_err_07");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "have duplicate name: func", "Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 106.规则文件.d文件中重复定义agg函数
TEST_F(SupportUDF_001_test, DataLog_006_001_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_err_08");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "have duplicate name: agg", "Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 107.规则文件.d文件声明函数，udf.c定义函数，但是gcc编译时，不包含udf.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_org_01_107");
    TestSetFileName(g_nsName);

    // 加载
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 108.规则文件.d文件声明函数，udf.c定义函数，但是gcc编译时，只编译udf.c文件
TEST_F(SupportUDF_001_test, DataLog_006_001_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_org_01_108");
    TestSetFileName(g_nsName);

    // .c->.so，只编译udf文件，预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile, g_udfFile, g_libName);
    system(g_command);
    // 加载，加载失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 109.一个规则文件.d文件对应多个UDF函数文件
TEST_F(SupportUDF_001_test, DataLog_006_001_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_05");
    TestSetFileName(g_nsName);
    char g_udfFile01[FILE_PATH] = {};
    char g_udfFile02[FILE_PATH] = {};
    (void)sprintf(g_udfFile01, "./%s/%s_udf_001.c", g_orgFile, g_nsName);
    (void)sprintf(g_udfFile02, "./%s/%s_udf_002.c", g_orgFile, g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so,多个udf文件
    (void)snprintf(g_command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s %s -o %s \n", g_hFile, g_outputFile, g_udfFile01, g_udfFile02, g_libName);
    system(g_command);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 110.相同.d和.c重复编译，so文件名相同，然后加载
TEST_F(SupportUDF_001_test, DataLog_006_001_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_org_01");
    TestSetFileName(g_nsName);

    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    int ret = TestUninstallDatalog(g_nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 111.相同.d和.c重复编译，so文件名不相同，然后加载
TEST_F(SupportUDF_001_test, DataLog_006_001_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_read_org_01");
    TestSetFileName(g_nsName);

    char g_libName01[FILE_PATH] = {0};
    char g_libName02[FILE_PATH] = {0};
    // 一个so只能加载一次，用例场景无效
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    // 无效场景 AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));

    // 卸载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(g_nsName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 112.function函数定义在namespace中，规则文件中没有使用using namespace ns1，直接使用function函数
TEST_F(SupportUDF_001_test, DataLog_006_001_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_01");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "\"func_01\" find namespace prefix unsuccessfully in rule near line 9",
        "Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 113.function函数定义在namespace中，规则文件中没有使用using namespace ns1，使用function函数时带namespace前缀
TEST_F(SupportUDF_001_test, DataLog_006_001_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_02");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 114.function函数定义在namespace中，规则文件中使用using namespace ns1，直接使用function函数
TEST_F(SupportUDF_001_test, DataLog_006_001_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_03");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 115.function函数定义在namespace中，规则文件中使用using namespace ns1，使用ns1.function的形式
TEST_F(SupportUDF_001_test, DataLog_006_001_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_04");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 116.不同namespace中定义相同名称的function函数
TEST_F(SupportUDF_001_test, DataLog_006_001_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_05");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 117.function函数定义在namepsace中，ns_01.func_01,udf文件中function函数名是dtl_ext_func_ns_01_func_01
TEST_F(SupportUDF_001_test, DataLog_006_001_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_func_02");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(g_nsName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 118.function函数定义在namepsace中，ns_01.func_01,udf文件中function函数名是dtl_ext_func_func_01
TEST_F(SupportUDF_001_test, DataLog_006_001_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_func_err_04");
    TestSetFileName(g_nsName);
    
    // 加载so
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 119.agg函数定义在namespace中，规则文件中没有使用using namespace ns1，直接使用agg函数
TEST_F(SupportUDF_001_test, DataLog_006_001_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_01");
    TestSetFileName(g_nsName);
    // .d->.c,预期失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "\"agg_01\" find namespace prefix unsuccessfully in rule near line 13", "Exit with code 1004009");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 120.agg函数定义在namespace中，规则文件中没有使用using namespace ns1，使用agg函数时带namespace前缀
TEST_F(SupportUDF_001_test, DataLog_006_001_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_02");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 121.agg函数定义在namespace中，规则文件中使用using namespace ns1，直接使用agg函数
TEST_F(SupportUDF_001_test, DataLog_006_001_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_03");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 122.agg函数定义在namespace中，规则文件中使用using namespace ns1，使用agg函数带前缀
TEST_F(SupportUDF_001_test, DataLog_006_001_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_04");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 123.不同namespace中定义相同名称的agg函数
TEST_F(SupportUDF_001_test, DataLog_006_001_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "datalog_udf_agg_05");
    TestSetFileName(g_nsName);
    // .d->.c,预期成功
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, g_inputFile, g_outputFile);
    int ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 124.agg函数定义在namepsace中，ns_01.agg_01,udf文件中agg函数名是dtl_agg_func_ns_01_agg_01
TEST_F(SupportUDF_001_test, DataLog_006_001_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_agg_succ_03");
    TestSetFileName(g_nsName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_libName));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestUninstallDatalog(g_nsName));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 125.agg函数定义在namepsace中，ns_01.agg_01,udf文件中agg函数名是dtl_agg_func_agg_01
TEST_F(SupportUDF_001_test, DataLog_006_001_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    (void)sprintf(g_orgFile, "constraint");
    (void)sprintf(g_destFile, "constraint");
    (void)sprintf(g_nsName, "udf_agg_err_04");
    TestSetFileName(g_nsName);
    
    // 加载so
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s \n", g_toolPath, g_libName);
    int ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1017001");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

