/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: namespaceTest.cpp
 * Description: datalog compilation
 * Author: wuxueqi 00495442
 * Create: 2022-08-01
 */

#include "datalog.h"
#include "t_datacom_lite.h"

using namespace std;

class namespaceDatalog : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testEnvClean();
    }
};

void namespaceDatalog::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    AW_CHECK_LOG_BEGIN();
}
void namespaceDatalog::TearDown()
{
    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  : namespace中未定义表
 允许ns为空, 但不允许.d文件中没有表
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/nsWithoutTable.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: there is no table/resource defined in the datalog program", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同一个文件中包含2个同名namespace, 无同名表, 规则互相访问
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/sameNsDiffTable.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同一个文件中包含2个同名namespace, 有同名表(关注同名不同字段)
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/sameNsSameTable.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: relations(table/resource/function/aggregate) have duplicate name: ns1.A", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同一个文件中包含2个不同名namespace, 规则不互相访问
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/diffNsNotInteract.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同一个文件中包含2个不同名namespace, 规则互相访问
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/diffNsInteract.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: \"B\" find namespace prefix unsuccessfully in rule near line 15.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 不同文件中包含2个同名namespace
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./datalogFile/namespace/diffFileSameNs1.d";
    char inputFile2[FILE_PATH] = "./datalogFile/namespace/diffFileSameNs2.d";
    char outputFile1[FILE_PATH] = "./output1.c";
    char outputFile2[FILE_PATH] = "./output2.c";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(command, 0, sizeof(command));
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile2, outputFile2);
    system(command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 空namespace的名称长度等于127个字符, 预期成功
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/nsNameLen.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 空namespace的名称长度大于127个字符, 预期成功
 空的ns为无效语句，在解析阶段就直接丢弃了，不会进后面的校验
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/nsNameLenAbnormal.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 一个文件中定义255个不同名namespace
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/nsCnt.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 一个文件中定义256个不同名namespace
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/nsCntAbnormal.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d", GMERR_SYNTAX_ERROR);
    int ret = executeCommand(
        command, "Error: can not parse datalog program, the number of namespace exceeds 255 near line 511.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 一个文件中定义256个同名namespace
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./datalogFile/namespace/nsCntSameNameAbnormal.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile1);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(command, 0, sizeof(command));
    char inputFile2[FILE_PATH] = "./datalogFile/namespace/nsCntSameName.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile2);
    system(command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 第一个文件定义255个namespace, 第二个文件定义255个namespace
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./datalogFile/namespace/diffFileNsCnt1.d";
    char inputFile2[FILE_PATH] = "./datalogFile/namespace/diffFileNsCnt2.d";
    char outputFile1[FILE_PATH] = "./output1.c";
    char outputFile2[FILE_PATH] = "./output2.c";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(command, 0, sizeof(command));
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile2, outputFile2);
    system(command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同一个文件中定义全局表和namespace, 且该ns访问全局表
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/globalTableAndNs.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 两个文件分别定义全局表和namespace, 且该ns访问全局表
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./datalogFile/namespace/diffFileGlobalTableAndNs1.d";
    char inputFile2[FILE_PATH] = "./datalogFile/namespace/diffFileGlobalTableAndNs2.d";
    char outputFile1[FILE_PATH] = "./output1.c";
    char outputFile2[FILE_PATH] = "./output2.c";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(command, 0, sizeof(command));
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile2, outputFile2);
    system(command);
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(
        command, "Error: \"globalTable\" find namespace prefix unsuccessfully in rule near line 6.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : namespace+table的名称长度等于127个字符
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/nsAndTableLen.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : namespace+table的名称长度大于127个字符
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/nsAndTableLenAbnormal.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: relation's name is too long (maximum length is 511): "
        "characters512characters512characters512characters512characters512characters512characters512characters512charac"
        "ters512charac."
        "ters512characters512characters512characters512characters512characters512characters512characters512characters51"
        "2characters512characters512characters512characters512characters512characters512characters512characters512chara"
        "cters512characters512characters512characters512characters512characters512characters512characters512characters5"
        "12characters512characters512characters512characters512cAAA near line 3.",
        errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : namespace名称为public
**************************************************************************** */
TEST_F(namespaceDatalog, DataLog_002_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/namespace/nsNameEqualPublic.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
