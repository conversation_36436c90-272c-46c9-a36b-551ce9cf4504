/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: PubResSub01.cpp
 * Description:
 * Author: youwanyong ywx1157510
 * Create: 2024-9-27
 */

#include "UniversalTools.h"
#include "t_datacom_lite.h"
SnUserDataT *userData = {0};

using namespace std;

class PubResSub : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int32_t ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {}
};

void PubResSub::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    g_isDebugMode = true;
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    int32_t ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void PubResSub::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  : 005.pusub资源表订阅不支持使用GmcSetRespMode，含GMC_RESP_SEND_FAILED_INDEX参数
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_092_002";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AW_FUN_Log(LOG_STEP, "不支持pubsub资源表订阅回调内设置failIndex.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback005, rsc0Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 1;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    // 给订阅回滚预留时间
    sleep(2);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.pusub资源表订阅不支持GmcSetSubFailedIndex
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_092_002";
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "pusub资源表订阅不支持GmcSetSubFailedIndex.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback006, rsc0Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 1;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 007.pusub资源中间表单批数据量为128，第100条数据回调设置发送errorcode，且资源字段设置非法预期不回滚当前批
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "1.加载so.第100条数据回调设置发送errorcode，且资源字段设置非法预期不回滚当前批");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback007, rsc0Get007);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;        //写入数据量
    int actualReciveNum = 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 数据写入正常
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 128, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 128, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 128, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 008.pusub资源中间表单批数据量为128，第100条数据回调设置发送errorcode，第101条资源字段设置非法
预期不回滚当前批
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so.第100条数据回调设置发送errorcode，第101条资源字段设置非法 预期不回滚当前批");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback007, rsc0Get008);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;        //写入数据量
    int actualReciveNum = 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
009.pusub资源中间表单批数据量为256，第228条数据回调设置发送errorcode预期当前批数据回滚，前一批128条正常回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so.第228条数据回调设置发送errorcode预期不回滚当前批，前一批128条正常回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback009, rsc0Get009);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                    //写入数据量
    int actualReciveNum = 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
010.pusub资源中间表单批数据量为256，第228条数据回调设置发送errorcode，后续资源字段设置非法预期不回滚当前批，前一批128条正常回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(
        LOG_STEP, "1.加载so.第228条数据回调设置发送errorcode，资源字段设置非法预期不回滚当前批，前一批128条正常回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback009, rsc0Get010);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                    //写入数据量
    int actualReciveNum = 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 011.pusub资源中间表单批数据量为128，第100条数据回调设置发送errorcode，预期回滚当前批100条数据
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so.第100条数据回调设置发送errorcode，预期回滚当前批");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback011, rsc0Get011);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;              //写入数据量
    int actualReciveNum = 100 + 100;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
012.pusub资源中间表单批数据量为256，第228条数据回调设置发送errorcode，预期回滚当前批前100条数据，前一批128条正常回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(
        LOG_STEP, "1.加载so.第228条数据回调设置发送errorcode，资源字段设置非法预期不回滚当前批，前一批128条正常回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback012, rsc0Get012);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                          //写入数据量
    int actualReciveNum = 128 + 100 + 100 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 013.pusub资源中间表单批数据量为257 输出表maxsize（257）第256条发送errorcode写入超限预期回滚当前批
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_003";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so. 输出表maxsize（257）第256条发送errorcode写入超限预期回滚当前批");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback013, rsc0Get013);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 257;                          //写入数据量
    int actualReciveNum = 128 + 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum - 1, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum - 1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum - 1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 014.pusub资源中间表正常设置资源字段发送errorcode后再次设置无效资源字段数据，预期当前批不回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(
        LOG_STEP, "1.加载so.pusub资源中间表正常设置资源字段发送errorcode后再次设置无效资源字段数据，预期当前批不回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback014, rsc0Get014);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;        //写入数据量
    int actualReciveNum = 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 015.pusub资源中间表报文超过2M第二批数据第100条数据发送errorcode，预期当前批数据正常回滚
 2024.10.10 当前不支持pubsub资源表单批订阅推送数据超过2M
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_004";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(
        LOG_STEP, "1.加载so.pusub资源中间表报文超过2M第二批数据第100条数据发送errorcode，预期当前批数据正常回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback015, rsc0Get015);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 60;         //写入数据量
    int actualReciveNum = 120;  // 订阅预期收到条数
    char strT[10] = {0};
    BigSmallAllTypeTableStruct *objIn1 =
        (BigSmallAllTypeTableStruct *)malloc(sizeof(BigSmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(BigSmallAllTypeTableStruct) * recordNum);
    BigSmallSetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, BigSmallAllTypeTableInpSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    // 资源字段设置正常,数据回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, BigAllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, BigAllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 016.pusub资源中间表报文超过2M第二批数据第100条数据发送errorcode且资源字段设置非法，预期当前批数据不回滚
 2024.10.10 当前不支持pubsub资源表单批订阅推送数据超过2M
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_004";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(
        LOG_STEP, "1.加载so.pusub资源中间表报文超过2M第二批数据第100条数据发送errorcode且资源字段设置非法，预期当前批数据不回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback015, rsc0Get016);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 60;         //写入数据量
    int actualReciveNum = 60;  // 订阅预期收到条数
    char strT[10] = {0};
    BigSmallAllTypeTableStruct *objIn1 =
        (BigSmallAllTypeTableStruct *)malloc(sizeof(BigSmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(BigSmallAllTypeTableStruct) * recordNum);
    BigSmallSetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, BigSmallAllTypeTableInpSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, BigAllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, BigAllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 017.pusub资源输出表单批数据量为128，第100条数据回调设置发送errorcode，且资源字段设置非法预期不回滚当前批
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so.第100条数据回调设置发送errorcode，且资源字段设置非法预期不回滚当前批");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback007, rsc0Get007);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;        //写入数据量
    int actualReciveNum = 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 数据写入正常
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 128, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 128, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 128, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 018.pusub资源输出表单批数据量为128，第100条数据回调设置发送errorcode，第101条资源字段设置非法
预期不回滚当前批
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so.，第100条数据回调设置发送errorcode，第101条资源字段设置非法 预期不回滚当前批");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback007, rsc0Get008);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;        //写入数据量
    int actualReciveNum = 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 数据写入正常
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 128, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 128, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 128, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
019.pusub资源输出表单批数据量为256，第228条数据回调设置发送errorcode预期当前批数据回滚，前一批128条正常回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so.第228条数据回调设置发送errorcode预期回滚当前批，前一批128条正常回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback009, rsc0Get009);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                    //写入数据量
    int actualReciveNum = 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
020.pusub资源输出表单批数据量为256，第228条数据回调设置发送errorcode，后续资源字段设置非法预期不回滚当前批，前一批128条正常回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(
        LOG_STEP, "1.加载so.第228条数据回调设置发送errorcode，资源字段设置非法预期不回滚当前批，前一批128条正常回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback009, rsc0Get010);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                    //写入数据量
    int actualReciveNum = 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 021.pusub资源输出表单批数据量为128，第100条数据回调设置发送errorcode，预期回滚当前批100条数据
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "1.加载so.第100条数据回调设置发送errorcode，预期回滚当前批");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback011, rsc0Get011);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;              //写入数据量
    int actualReciveNum = 100 + 100;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
022.pusub资源输出表单批数据量为256，第228条数据回调设置发送errorcode，预期回滚当前批前100条数据，前一批128条正常回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(
        LOG_STEP, "1.加载so.第228条数据回调设置发送errorcode，资源字段设置非法预期不回滚当前批，前一批128条正常回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback012, rsc0Get012);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                          //写入数据量
    int actualReciveNum = 128 + 100 + 100 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接收数据
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 023.pusub资源输出表单批数据量为257 输出表maxsize（257）第256条发送errorcode写入超限预期回滚当前批
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_003";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so. 输出表maxsize（255）第255条发送errorcode写入超限预期回滚当前批");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback013, rsc0Get013);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                          //写入数据量
    int actualReciveNum = 128 + 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅接受属具
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum - 1, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum - 1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum - 1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 024.pusub资源输出表正常设置资源字段发送errorcode后再次设置无效资源字段数据，预期当前批不回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(
        LOG_STEP, "1.加载so.pusub资源输出表正常设置资源字段发送errorcode后再次设置无效资源字段数据，预期当前批不回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback014, rsc0Get014);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;        //写入数据量
    int actualReciveNum = 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 025. block0 不同topo升级后对pubsub资源中间表写入正常数据，
 资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP,
        "1.加载so."
        "不同topo升级资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback025, rsc0Get025);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                          //写入数据量
    int actualReciveNum = 128 + 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 加载升级so
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV3.so -ns %s", g_testNameSpace);
    system(g_command);
    ret = writeRecord(g_connSync, g_stmtSync, "inp2", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 写入正常数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅数据接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
026.不同topo升级后对pubsub资源输出表写入正常数据，资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚。
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP,
        "1.加载so."
        "不同topo升级资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback025, rsc0Get025);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                          //写入数据量
    int actualReciveNum = 128 + 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 加载升级so
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV3.so -ns %s", g_testNameSpace);
    system(g_command);
    ret = writeRecord(g_connSync, g_stmtSync, "inp2", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅数据接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
027.不同topo升级过程中并发对pubsub资源中间表写入正常数据，资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚。
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP,
        "1.加载so."
        "不同topo升级资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback025, rsc0Get025);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                          //写入数据量
    int actualReciveNum = 128 + 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 加载升级so
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    ret = writeRecord(g_connSync, g_stmtSync, "inp2", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV3.so -ns %s", g_testNameSpace);
    system(g_command);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 写入正常数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅数据接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
028.不同topo升级过程中对pubsub资源输出表写入正常数据，资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚。
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP,
        "1.加载so."
        "不同topo升级资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback025, rsc0Get025);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                          //写入数据量
    int actualReciveNum = 128 + 128 + 128 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 加载升级so
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    ret = writeRecord(g_connSync, g_stmtSync, "inp2", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV3.so -ns %s", g_testNameSpace);
    system(g_command);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅数据接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
029.不同topo升级后对pubsub资源中间表写入正常数据，资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode且资源字段应答不合法，预期当前批数据不回滚。
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(
        LOG_STEP, "1.加载so.不同topo升级资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，当前批不回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback029, rsc0Get029);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                    //写入数据量
    int actualReciveNum = 128 + 100 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 加载升级so
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV3.so -ns %s", g_testNameSpace);
    system(g_command);
    ret = writeRecord(g_connSync, g_stmtSync, "inp2", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 写入正常数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅数据接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
void *LoadPatchSo(void *args)
{
    char cmd[1024];
    memset(cmd, 0, sizeof(cmd));
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV3.so -ns %s",
        g_testNameSpace);
    int32_t ret = executeCommand(cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  :
030.不同topo升级后对pubsub资源输出表写入正常数据，资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode且资源字段应答不合法，预期当前批数据不回滚。
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP,
        "1.加载so."
        "不同topo升级资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback029, rsc0Get029);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                    //写入数据量
    int actualReciveNum = 128 + 100 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 加载升级so
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV3.so -ns %s", g_testNameSpace);
    system(g_command);
    ret = writeRecord(g_connSync, g_stmtSync, "inp2", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅数据接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
031.不同topo升级过程中对pubsub资源中间表写入正常数据，资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode且资源字段应答不合法，预期当前批数据不回滚。
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP,
        "1.加载so."
        "不同topo升级资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback029, rsc0Get029);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                    //写入数据量
    int actualReciveNum = 128 + 100 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 加载升级so
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV2.so -s %s -ns %s", g_toolPath,
        g_connServer, g_testNameSpace);
    system(g_command);
    int32_t thread_num = 1;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, LoadPatchSo, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 并发写入数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp2", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 写入正常数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅数据接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
032.不同topo升级过程中对pubsub资源输出表写入正常数据，资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode且资源字段应答不合法，预期当前批数据不回滚
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP,
        "1.加载so."
        "不同topo升级资源字段应答合法，订阅回调推送256条数据第228条数据回应errorcode，预期当前批正常应答数据能够回滚");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsubNs2.gmjson", &userData, 1000, g_subName,
        snCallback029, rsc0Get029);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 256;                    //写入数据量
    int actualReciveNum = 128 + 100 + 128;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 加载升级so
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmimport -c datalog -upgrade ./datalog_file/DataLog_092_002_patchV2.so -s %s -ns %s", g_toolPath,
        g_connServer, g_testNameSpace);
    system(g_command);
    int32_t thread_num = 1;
    pthread_t client_thr[thread_num];
    // 并发写入数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp2", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[0], NULL, LoadPatchSo, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅数据接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    ret = readRecord(g_connSync, g_stmtSync, "ns2.inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "ns2.midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 033.so加载在非public下 ，DRT_CONN_SUBS_STAT视图校验订阅推送失败条数
**************************************************************************** */
TEST_F(PubResSub, DataLog_092_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char soName1[FILE_PATH] = "DataLog_092_002";
    g_isDebugMode = true;
    g_isRobackCurrentBatch = true;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "");
    AW_FUN_Log(LOG_STEP, "1.加载so.第100条数据回调设置发送errorcode，预期回滚当前批");
    int32_t ret;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char usrName[] = "ywy";
    const char *nsTest01 = "ywyNameSpace";
    ret = GmcCreateNamespace(g_stmtSync, nsTest01, usrName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, "ywyNameSpace");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(soName1, false, "ywyNameSpace");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    GmcStmtT *subStmt;
    ret = testSubConnect(&g_connSub, &subStmt, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData, 1000, g_subName,
        snCallback011, rsc0Get011);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.批写数据.");
    int recordNum = 128;              //写入数据量
    int actualReciveNum = 100 + 100;  // 订阅预期收到条数
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 资源字段设置错误,数据不回滚
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, actualReciveNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验回滚数据
    ret = readRecord(g_connSync, g_stmtSync, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_connSync, g_stmtSync, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 进行视图校验
    system("gmsysview -q V\\$DRT_CONN_SUBS_STAT -f NODE_NAME=subConnNameYWY");
    char cmd[] = "gmsysview -q V\\$DRT_CONN_SUBS_STAT -f NODE_NAME=subConnNameYWY";
    ret = executeCommand(cmd, "SUB_TRY_CNT: 2", "SUB_SEND_SUC_CNT: 2", "SUB_SEND_FAIL_CNT: 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1, "ywyNameSpace");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    ret = GmcDropNamespace(g_stmtSync, "ywyNameSpace");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
