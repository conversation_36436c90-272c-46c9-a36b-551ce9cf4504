/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: UniversalTools.h
 * Description: tool
 * Author: youwanyong ywx1157510
 * Create: 2024-09-02
 */

#ifndef __UNIVERSALTOOL_H__
#define __UNIVERSALTOOL_H__

#include "t_datacom_lite.h"
#include "StructDatalogTable.h"
#include "gmc_dlr.h"
bool g_isDebugMode = false;
// 是否回滚当前批;
bool g_isRobackCurrentBatch = false;

#define FILE_PATH 512
#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 512
const char *g_subModify = "{\"type\":\"modify\", \"msgTypes\":[\"new object\"]}";
const char *g_subDelete = "{\"type\":\"delete\", \"msgTypes\":[\"old object\"]}";
const char *g_subInitialLoad = "{\"type\":\"initial_load\", \"msgTypes\":[\"new object\"]}";
const char *g_subAge = "{\"type\":\"age\", \"msgTypes\":[\"old object\"]}";
const char *g_subInitialLoadEof = "{\"type\":\"initial_load_eof\"}";
char g_subJson[1024] = "";
char g_command[MAX_CMD_SIZE] = {0};

char g_outputDir[FILE_PATH] = "datalog_file";
typedef int (*FuncWrite)(GmcStmtT *stmt, void *t, bool isStateTableInput);
typedef int (*FuncWriteId)(GmcStmtT *stmt, int64_t value, int32_t count);
typedef int (*FuncRead)(GmcStmtT *stmt, void *t, int len, bool external, bool isResource);
typedef int (*FuncRead1)(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc);
typedef int (*FuncReadId)(GmcStmtT *stmt, int startid, int endid, int32_t count);
typedef int (*FuncReadSub)(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc);
// 用于表名排序
char *g_lableNameOrder[3] = {0};

// 多线程控制数据写入结构
typedef struct {
    int16_t index;  // 从多少开始写
    int16_t num;    // 写入数量
} PthreadControlT;  // 主键字段

// 记录订阅回调中不同事件的推送记录
typedef struct {
    int8_t a;
    int16_t b;
    int32_t c;
} PkProperT;  // 主键字段

typedef struct {
    PkProperT pk;
    int64_t updateDValue;
    uint32_t count;
    uint32_t upgradeVersion;
} SubValueT;  // 单个订阅事件推送记录

typedef struct {
    SubValueT *loadEventValue;
    SubValueT *modifyEventValue;
    SubValueT *deleteEventValue;
    SubValueT *ageEventValue;
    SubValueT *totalEventValue;
} SubEventValueT;  // 多个订阅事件推送记录
// 订阅&&DLR数据保存
typedef struct {
    SnUserDataT *userData;
    SubEventValueT *subEventValue;
    GmcDlrDataBufT *dlrGetSubBuf;
    int32_t symbolSubDlr;
} DlrAndSubT;
/**-------------------存储 DLR buf-----------------------**/
#define DLR_BUF_SIZE 1000
#define DLR_BUF_LEN 5000

// 申请DLR和订阅申请需要的内存
int32_t GetMemWithDlrAndSub(DlrAndSubT *dlrAndSubData, bool isNeedSub, bool isNeedDlr = false)
{
    int32_t ret = -1;
    dlrAndSubData->symbolSubDlr = 0;
    if (isNeedSub) {
        ret = testSnMallocUserData(&(dlrAndSubData->userData), 500 * 2);
        EXPECT_EQ(GMERR_OK, ret);
        // subData malloc 申请内存存放增量数据
        dlrAndSubData->dlrGetSubBuf = (GmcDlrDataBufT *)malloc(sizeof(GmcDlrDataBufT) * 500);
        if (dlrAndSubData->dlrGetSubBuf == NULL) {
            AW_FUN_Log(LOG_STEP, "alloc falil for dlr\n");
            return -1;
        }
        memset(dlrAndSubData->dlrGetSubBuf, sizeof(GmcDlrDataBufT) * 500, 0);
    }
    if (isNeedDlr) {
        // subData malloc 申请内存存放订阅推送数据
        dlrAndSubData->subEventValue = (SubEventValueT *)malloc(sizeof(SubEventValueT) * 500);
        if (dlrAndSubData->subEventValue == NULL) {
            AW_FUN_Log(LOG_STEP, "alloc falil for dlr\n");
            return -1;
        }
        memset(dlrAndSubData->subEventValue, sizeof(SubEventValueT) * 500, 0);

        dlrAndSubData->subEventValue->loadEventValue = (SubValueT *)malloc(sizeof(SubValueT) * 500);
        if (dlrAndSubData->subEventValue->loadEventValue == NULL) {
            AW_FUN_Log(LOG_STEP, "alloc falil for dlr\n");
            return -1;
        }
        memset(dlrAndSubData->subEventValue->loadEventValue, sizeof(SubValueT) * 500, 0);

        dlrAndSubData->subEventValue->modifyEventValue = (SubValueT *)malloc(sizeof(SubValueT) * 500);
        if (dlrAndSubData->subEventValue->modifyEventValue == NULL) {
            AW_FUN_Log(LOG_STEP, "alloc falil for dlr\n");
            return -1;
        }
        memset(dlrAndSubData->subEventValue->modifyEventValue, sizeof(SubValueT) * 500, 0);

        dlrAndSubData->subEventValue->deleteEventValue = (SubValueT *)malloc(sizeof(SubValueT) * 500);
        if (dlrAndSubData->subEventValue->deleteEventValue == NULL) {
            AW_FUN_Log(LOG_STEP, "alloc falil for dlr\n");
            return -1;
        }
        memset(dlrAndSubData->subEventValue->deleteEventValue, sizeof(SubValueT) * 500, 0);

        dlrAndSubData->subEventValue->ageEventValue = (SubValueT *)malloc(sizeof(SubValueT) * 500);
        if (dlrAndSubData->subEventValue->ageEventValue == NULL) {
            AW_FUN_Log(LOG_STEP, "alloc falil for dlr\n");
            return -1;
        }
        memset(dlrAndSubData->subEventValue->totalEventValue, sizeof(SubValueT) * 500, 0);
        dlrAndSubData->subEventValue->totalEventValue = (SubValueT *)malloc(sizeof(SubValueT) * 500);
        if (dlrAndSubData->subEventValue->totalEventValue == NULL) {
            AW_FUN_Log(LOG_STEP, "alloc falil for dlr\n");
            return -1;
        }
    }
    return GMERR_OK;
}

void FreeAllAllocMem(DlrAndSubT *dlrAndSubData)
{
    if (dlrAndSubData->userData != NULL) {
        testSnFreeUserData(dlrAndSubData->userData);
    } else {
        AW_FUN_Log(LOG_STEP, "free falil no alloc\n");
    }
    if (dlrAndSubData->dlrGetSubBuf != NULL) {
        free(dlrAndSubData->dlrGetSubBuf);
        free(dlrAndSubData->subEventValue->ageEventValue);
        free(dlrAndSubData->subEventValue->deleteEventValue);
        free(dlrAndSubData->subEventValue->loadEventValue);
        free(dlrAndSubData->subEventValue->modifyEventValue);
        free(dlrAndSubData->subEventValue);
    }
}

// 加载so文件
int LoadSoFile(const char *soName, bool isDistributed = false, const char *nsName = NULL)
{
    memset(g_command, 0, sizeof(g_command));
    if (isDistributed) {
        (void)snprintf(g_command, MAX_CMD_SIZE, "./%s/%s.so --distribute", g_outputDir, soName);
    } else {
        (void)snprintf(g_command, MAX_CMD_SIZE, "./%s/%s.so", g_outputDir, soName);
    }
    if (nsName) {
        return TestLoadDatalog(g_command, nsName);
    } else {
        return TestLoadDatalog(g_command);
    }
}

#pragma pack(1)
typedef struct {
    int32_t tabelMemtexValue;
    int32_t udfMemtexValue;
    int32_t planListMemtexValue;
} DatalogCtxValue;
#pragma pack(0)

/*
获取datalog 相关ctx value
表:COM_SHMEM_CTX catalog share memory context
udf:COM_DYN_CTX catalog dynamic memory context
plan list:COM_DYN_CTX datalog plan list memCtx
*/
void GetDatalogMemCtxValue(int32_t *tabelMemtexValue, int32_t *udfMemtexValue, int32_t *planListMemtexValue)
{
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME='catalog share memory context'|grep TOTAL_ALLOC_SIZE:|sed "
        "'s/[^0-9]//g'");
    TestGetResultCommand(command, tabelMemtexValue, NULL, 0);
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME='catalog share memory context'");
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='catalog dynamic memory context'|grep TOTAL_ALLOC_SIZE:|sed "
        "'s/[^0-9]//g'");
    TestGetResultCommand(command, udfMemtexValue, NULL, 0);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog plan list memCtx'|grep TOTAL_ALLOC_SIZE:|sed 's/[^0-9]//g'");
    TestGetResultCommand(command, planListMemtexValue, NULL, 0);
    return;
}

int CompareMemCtxValue(DatalogCtxValue *memctxValue1, DatalogCtxValue *memctxValue2)
{
    char command[MAX_CMD_SIZE] = {0};
    if (memctxValue1->tabelMemtexValue != memctxValue2->tabelMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "tableMemctx is InCrease!!!");
        AW_MACRO_EXPECT_EQ_INT(memctxValue1->tabelMemtexValue, memctxValue2->tabelMemtexValue);
        return -1;
    }
    if (memctxValue1->udfMemtexValue != memctxValue2->udfMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "udfMemtex is InCrease!!!");
        AW_MACRO_EXPECT_EQ_INT(memctxValue1->udfMemtexValue, memctxValue2->udfMemtexValue);
        return -1;
    }
    if (memctxValue1->planListMemtexValue != memctxValue2->planListMemtexValue) {
        AW_FUN_Log(LOG_DEBUG, "planListMemtex is InCrease!!!");
        AW_MACRO_EXPECT_EQ_INT(memctxValue1->planListMemtexValue, memctxValue2->planListMemtexValue);
        return -1;
    }
    return GMERR_OK;
}

void SystemSnprintf(const char *format, ...)
{
    va_list p;
    va_start(p, format);
    (void)vsnprintf(g_command, MAX_CMD_SIZE, format, p);
    va_end(p);
    system(g_command);
}

int Debug_executeCommand(char *cmd, const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL,
    const char *v4 = NULL, const char *v5 = NULL)
{
    int ret = 0;
    ret = executeCommand(cmd, v1, v2, v3, v4, v5);
    return ret;
}

template <typename StructObjT>
int writeRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, int res1 = GMERR_OK, bool isStateTabelInput = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    for (int i = 0; i < objLen; i++) {
        if (isStruct) {
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i), isStateTabelInput);
        }
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = GmcExecute(stmt);
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            int32_t deRet = ret;
            if (ret == GMERR_DATA_EXCEPTION) {
                // 校验errorcode
                int expectErrorCode = 1000;
                char expectErrorLabelName[] = "midRes";
                int64_t errorCode = 0;
                char errorLabelName[512] = {0};
                ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
                ret = deRet;
            }
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}
template <typename StructObjT>
int writeRecordAsync(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, int res1 = GMERR_OK, bool isStateTabelInput = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    AsyncUserDataT dataRev = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    for (int i = 0; i < objLen; i++) {
        if (isStruct) {
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i), isStateTabelInput);
        }
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            GmcAsyncRequestDoneContextT insertRequestCtx;
            insertRequestCtx.insertCb = insert_vertex_callback;
            insertRequestCtx.userData = &dataRev;
            ret = GmcExecuteAsync(stmt, &insertRequestCtx);
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
            ret = testWaitAsyncRecv(&dataRev);
            EXPECT_EQ(GMERR_OK, ret);
            ret = dataRev.status;
        }
    }
    if (isBatch) {
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &dataRev);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
        ret = dataRev.status;
        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}
//   update by pk
int UpdateByKeyID(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    NewALLTypeTableStruct *obj = (NewALLTypeTableStruct *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &obj->c, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
template <typename StructObjT>
int UpdateRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, int res1 = GMERR_OK, bool isStateTabelInput = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    for (int i = 0; i < objLen; i++) {
        if (isStruct) {
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i), isStateTabelInput);
        }
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = GmcExecute(stmt);
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

int writeRecordId(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int64_t startid, int64_t endid, int32_t count,
    FuncWriteId func, bool isBatch = true, bool isStruct = false, int queueLeve = -1, bool isFlowCtr = true)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置批写的数据的记录数
        ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    if (queueLeve != -1) {
        int getValue = 0;
        AW_MACRO_EXPECT_EQ_INT(0, TestGetConfigValueInt("datalogQueueNum", &getValue));
    }

    for (int i = startid; i < endid; i++) {
        ret = func(stmt, i, count);
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecordId] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecordId] GmcBatchAddDML fail, set tRet, i: %d, ret = %d.", i, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = isFlowCtr ? GmcExecute(stmt) : GmcExecute(stmt);
            if (ret) {
                AW_FUN_Log(LOG_INFO, "[writeRecordId] GmcExecute fail, ret = %d.", ret);
                break;
            }
        }
    }

    if (isBatch) {
        ret = isFlowCtr ? GmcBatchExecute(batch, &batchRet) : GmcBatchExecute(batch, &batchRet);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "[writeRecordId] GmcBatchExecute fail, ret = %d.", ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }

    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecordId] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

template <typename StructObjT>
int readRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncRead func,
    bool isExternal = false, bool checkRecord = true, int *outCnt = NULL, bool isResource = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        testGmcGetLastError();
        return ret;
    }
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isFinish;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish || ret != 0) {
            break;
        }
        ret = func(stmt, (void *)obj, objLen, isExternal, isResource);
        if ((checkRecord) && (ret)) {
            AW_FUN_Log(LOG_ERROR, "[readRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        cnt++;
    }
    if (outCnt) {
        *outCnt = cnt;
    }
    if ((checkRecord) && (objLen != cnt)) {
        tRet = (tRet == GMERR_OK) ? -1 : tRet;
        AW_FUN_Log(LOG_ERROR, "[readRecord]  set tRet, cnt = %d, objLen = %d, tRet = %d.", cnt, objLen, tRet);
    }

    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[readRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

// 加载升级的so
int TestLoadUpgradeDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    int32_t ret = 0;
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }

    (void)snprintf(loadCommand, commandSize, "%s/gmimport -s %s -c datalog -upgrade ./datalog_file/%s.so", g_toolPath,
        g_connServer, soName);
    ret = Debug_executeCommand(loadCommand, "successfully");
    if (ret) {
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

/*********************************DML*****************************************/
// a:int1, b:int2, c:int4, d:int64,  a:uint1, b:uint2, c:uint4, d:uint64,e31:byte10
int AllTypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    SmallAllTypeTableStruct *obj = (SmallAllTypeTableStruct *)t;
    int ret = 0;
    ret =
        GmcSetVertexProperty(stmt, "proper_0_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_0_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_1_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_1_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }
    if (obj->proper_1_int2 != obj->proper_2_int4) {
        obj->proper_1_int2 = obj->proper_2_int4;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_2_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_2_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_3_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_3_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_4_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_4_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_5_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_5_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_6_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_6_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_7_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_7_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret =
        GmcSetVertexProperty(stmt, "proper_8_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_8_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_9_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_9_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_10_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_10_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_11_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_11_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_12_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_12_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_13_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_13_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_14_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_14_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_15_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_15_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_16_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_16_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_17_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_17_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_18_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_18_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_19_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_19_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_20_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_20_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_21_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_21_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_22_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_22_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_23_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_23_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_24_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_24_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_25_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_25_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_26_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_26_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_27_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_27_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_28_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_28_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_29_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_29_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_30_byte10", GMC_DATATYPE_FIXED, obj->proper30Byte10, sizeof(obj->proper30Byte10));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper30Byte10: %s, ret = %d.", obj->proper30Byte10, ret);
        return ret;
    }
    if (g_isRobackCurrentBatch) {
        if (obj->proper_0_int1 == 2) {
            AW_FUN_Log(LOG_INFO, "g_isRobackCurrentBatch is True");
        }

        ret = GmcSetVertexProperty(
            stmt, "proper_31_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_31_int1: %d, ret = %d.", obj->proper_0_int1, ret);
            return ret;
        }

        ret = GmcSetVertexProperty(
            stmt, "proper_32_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_32_int2: %d, ret = %d.", obj->proper_1_int2, ret);
            return ret;
        }
    } else {
        // 资源字段设置值为默认值设置不合理推送失败，当前批数据不回滚
        bool isNull = true;
        if (obj->proper_0_int1 == 2) {
            AW_FUN_Log(LOG_INFO, "g_isRobackCurrentBatch is false");
        }
        obj->proper_0_int1 = 127;

        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_31_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        ret = GmcSetVertexProperty(
            stmt, "proper_31_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_31_int1: %d, ret = %d.", obj->proper_0_int1, ret);
            return ret;
        }
        obj->proper_1_int2 = 130;
        ret = GmcSetVertexProperty(
            stmt, "proper_32_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_32_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_32_int2: %d, ret = %d.", obj->proper_1_int2, ret);
            return ret;
        }
        obj->proper_1_int2 = 127;
        obj->proper_2_int4 = 127;
        obj->proper_3_int8 = 127;
        obj->proper_4_uint1 = 127;
        obj->proper_5_uint2 = 127;
        obj->proper_6_uint4 = 127;
        obj->proper_7_uint8 = 127;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_33_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_33_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_34_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_34_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_35_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_35_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_36_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_36_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_37_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_37_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_38_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_38_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_39_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_39_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_40_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_40_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_41_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_41_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_42_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_42_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_43_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_43_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_44_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_44_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_45_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_45_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_46_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_46_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_47_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_47_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_48_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_48_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_49_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_49_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_50_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_50_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_51_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_51_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_52_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_52_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_53_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_53_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_54_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_54_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_55_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_55_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_56_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_56_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_57_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_57_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_58_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_58_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_59_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_59_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_60_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_60_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_61_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_61_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_62_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_62_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// int1,byte512
int BigSmallAllTypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    BigSmallAllTypeTableStruct *obj = (BigSmallAllTypeTableStruct *)t;
    int ret = 0;
    ret =
        GmcSetVertexProperty(stmt, "proper_0_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_0_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }
    if (!g_isRobackCurrentBatch) {
        memset(obj->properByte512, 0x00, 512);
    }

    ret =
        GmcSetVertexProperty(stmt, "proper_1_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_1_int2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }
    ret =
        GmcSetVertexProperty(stmt, "proper_2_int4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_2_int4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret =
        GmcSetVertexProperty(stmt, "proper_3_int8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_3_int8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_4_uint1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_4_uint1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_5_uint2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_5_uint2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_6_uint4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_6_uint4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_7_uint8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_7_uint8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret =
        GmcSetVertexProperty(stmt, "proper_8_int1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_8_int1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret =
        GmcSetVertexProperty(stmt, "proper_9_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_9_int2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_10_int4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_10_int4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_11_int8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_11_int8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_12_uint1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_12_uint1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_13_uint2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_13_uint2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_14_uint4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_14_uint4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_15_uint8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_15_uint8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_16_int1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_16_int1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_17_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_17_int2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_18_int4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_18_int4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_19_int8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_19_int8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_20_uint1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_20_uint1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_21_uint2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_21_uint2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_22_uint4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_22_uint4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_23_uint8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_23_uint8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_24_int1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_24_int1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_25_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_25_int2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_26_int4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_26_int4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_27_int8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_27_int8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_28_uint1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_28_uint1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_29_uint2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_29_uint2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_30_byte10", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper30Byte10: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }
    if (g_isRobackCurrentBatch) {
        AW_FUN_Log(LOG_INFO, "g_isRobackCurrentBatch is True");
        ret = GmcSetVertexProperty(
            stmt, "proper_31_int1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_31_int1: %s, ret = %d.", obj->properByte512, ret);
            return ret;
        }

        ret = GmcSetVertexProperty(
            stmt, "proper_32_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_32_int2: %s, ret = %d.", obj->properByte512, ret);
            return ret;
        }
    } else {
        bool isNull = true;
        AW_FUN_Log(LOG_INFO, "g_isRobackCurrentBatch is false");
        ret =
            GmcGetVertexPropertyByName(stmt, "proper_31_int1", obj->properByte512, sizeof(obj->properByte512), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        memset(obj->properByte512, 0x00, 512);
        ret = GmcSetVertexProperty(
            stmt, "proper_31_int1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_31_int1: %s, ret = %d.", obj->properByte512, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(
            stmt, "proper_32_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_36_uint2: %s, ret = %d.", obj->properByte512, ret);
            return ret;
        }
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_33_int4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_33_int4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_34_int8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_34_int8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_35_uint1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_35_uint1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_36_uint2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_36_uint2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_37_uint4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_37_uint4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_38_uint8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_38_uint8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_39_int1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_39_int1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_40_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_40_int2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_41_int4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_41_int4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_42_int8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_42_int8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_43_uint1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_43_uint1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_44_uint2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_44_uint2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_45_uint4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_45_uint4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_46_uint8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_46_uint8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_47_int1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_47_int1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_48_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_48_int2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_49_int4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_49_int4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_50_int8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_50_int8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_51_uint1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_51_uint1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_52_uint2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_52_uint2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_53_uint4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_53_uint4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_54_uint8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_54_uint8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_55_int1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_55_int1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_56_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_56_int2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_57_int4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_57_int4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_58_int8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_58_int8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_59_uint1", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_59_uint1: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_60_uint2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_60_uint2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_61_uint4", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_61_uint4: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_62_uint8", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_62_uint8: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// int1,byte512
int BigSmallAllTypeTableInpSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    BigSmallAllTypeTableStruct *obj = (BigSmallAllTypeTableStruct *)t;
    int ret = 0;
    ret =
        GmcSetVertexProperty(stmt, "proper_0_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_0_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret =
        GmcSetVertexProperty(stmt, "proper_1_int2", GMC_DATATYPE_FIXED, obj->properByte512, sizeof(obj->properByte512));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_1_int2: %s, ret = %d.", obj->properByte512, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int AllTypeTableCmp(
    const AllTypeTableStruct *st1, const AllTypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->proper_0_int1 != st2->proper_0_int1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] proper_0_int1, st1: %d, st2: %d.", st1->proper_0_int1,
                    st2->proper_0_int1);
            }
        }

        if (st1->proper_1_int2 != st2->proper_1_int2) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] b, st1: %d, st2: %d.", st1->proper_1_int2, st2->proper_1_int2);
            }
        }

        if (st1->proper_2_int4 != st2->proper_2_int4) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] proper_2_int4, st1: %d, st2: %d.", st1->proper_2_int4,
                    st2->proper_2_int4);
            }
        }

        if (st1->proper_3_int8 != st2->proper_3_int8) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] proper_3_int8, st1: %d, st2: %d.", st1->proper_3_int8,
                    st2->proper_3_int8);
            }
        }

        if (st1->proper_4_uint1 != st2->proper_4_uint1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] proper_4_uint1, st1: %d, st2: %d.", st1->proper_4_uint1,
                    st2->proper_4_uint1);
            }
        }

        if (st1->proper_5_uint2 != st2->proper_5_uint2) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[AllTypeTableCmp] b, st1: %d, st2: %d.", st1->proper_5_uint2, st2->proper_5_uint2);
            }
        }

        if (st1->proper_6_uint4 != st2->proper_6_uint4) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] proper_2_int4, st1: %d, st2: %d.", st1->proper_6_uint4,
                    st2->proper_6_uint4);
            }
        }

        if (st1->proper_7_uint8 != st2->proper_7_uint8) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] proper_3_int8, st1: %d, st2: %d.", st1->proper_7_uint8,
                    st2->proper_7_uint8);
            }
        }
        for (int32_t j = 0; j < sizeof(st1->proper30Byte10); j++) {
            if (st1->proper30Byte10[j] != st2->proper30Byte10[j]) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] e, st1: %d, st2: %d.", st1->proper30Byte10[j],
                        st2->proper30Byte10[j]);
                }
            }
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] dtlReservedCount, st1: %d, st2: %d.", st1->dtlReservedCount,
                        st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int BigAllTypeTableCmp(const BigSmallAllTypeTableStruct *st1, const BigSmallAllTypeTableStruct *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        for (int32_t j = 0; j < sizeof(st1->properByte512); j++) {
            if (st1->properByte512[j] != st2->properByte512[j]) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[AllTypeTableCmp] e, st1: %s, st2: %s.", st1->properByte512[j],
                        st2->properByte512[j]);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}
// a:int1, b:int2, c:int4, d:int64,  a:uint1, b:uint2, c:uint4, d:uint64,e31:byte10
int AllTypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    AllTypeTableStruct *checkObj = (AllTypeTableStruct *)t;
    AllTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret =
        GmcGetVertexPropertyByName(stmt, "proper_0_int1", &getObj.proper_0_int1, sizeof(getObj.proper_0_int1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_0_int1' fail, ret = %d.", ret);
        return ret;
    }

    ret =
        GmcGetVertexPropertyByName(stmt, "proper_1_int2", &getObj.proper_1_int2, sizeof(getObj.proper_1_int2), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_1_int2' fail, ret = %d.", ret);
        return ret;
    }

    ret =
        GmcGetVertexPropertyByName(stmt, "proper_2_int4", &getObj.proper_2_int4, sizeof(getObj.proper_2_int4), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret =
        GmcGetVertexPropertyByName(stmt, "proper_3_int8", &getObj.proper_3_int8, sizeof(getObj.proper_3_int8), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_3_int8' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "proper_4_uint1", &getObj.proper_4_uint1, sizeof(getObj.proper_4_uint1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_4_uint1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "proper_5_uint2", &getObj.proper_5_uint2, sizeof(getObj.proper_5_uint2), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_5_uint2' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "proper_6_uint4", &getObj.proper_6_uint4, sizeof(getObj.proper_6_uint4), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_6_uint4' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "proper_7_uint8", &getObj.proper_7_uint8, sizeof(getObj.proper_7_uint8), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_7_uint8' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "proper_30_byte10", getObj.proper30Byte10, sizeof(getObj.proper30Byte10), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int4C1Int8C1StrC1ByteGet] get 'proper30Byte10' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (AllTypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)AllTypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// e31:byte512
int BigAllTypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    BigSmallAllTypeTableStruct *checkObj = (BigSmallAllTypeTableStruct *)t;
    BigSmallAllTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret =
        GmcGetVertexPropertyByName(stmt, "proper_0_int1", getObj.properByte512, sizeof(getObj.properByte512), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_0_int1' fail, ret = %d.", ret);
        return ret;
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (BigAllTypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)BigAllTypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// a:int1, b:int2, c:int4, d:int64,  a:uint1, b:uint2, c:uint4, d:uint64,e31:byte10
int AllTypeTableSetTimeout(GmcStmtT *stmt, void *t, bool isStateTableInput = false, int64_t timeOutValue = 0)
{
    SmallAllTypeTableStruct *obj = (SmallAllTypeTableStruct *)t;
    int ret = 0;
    ret =
        GmcSetVertexProperty(stmt, "proper_0_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_0_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_1_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_1_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_2_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_2_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_3_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_3_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    if (timeOutValue != 0) {
        obj->proper_3_int8 = (int64_t)obj->proper_2_int4;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_4_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_4_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_5_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_5_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_6_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_6_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_7_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_7_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret =
        GmcSetVertexProperty(stmt, "proper_8_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_8_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_9_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_9_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_10_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_10_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_11_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_11_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_12_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_12_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_13_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_13_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_14_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_14_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_15_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_15_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_16_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_16_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_17_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_17_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_18_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_18_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_19_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_19_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_20_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_20_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_21_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_21_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_22_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_22_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_23_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_23_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_24_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_24_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_25_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_25_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_26_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_26_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_27_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_27_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_28_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_28_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_29_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_29_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_30_byte10", GMC_DATATYPE_FIXED, obj->proper30Byte10, sizeof(obj->proper30Byte10));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper30Byte10: %s, ret = %d.", obj->proper30Byte10, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_31_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_31_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_32_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_32_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_33_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_33_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_34_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_34_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_35_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_35_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_36_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_36_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_37_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_37_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_38_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_38_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_39_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_39_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_40_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_40_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_41_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_41_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_42_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_42_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_43_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_43_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_44_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_44_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_45_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_45_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_46_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_46_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_47_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_47_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_48_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_48_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_49_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_49_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_50_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_50_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_51_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_51_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_52_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_52_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_53_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_53_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_54_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_54_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_55_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_55_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_56_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_56_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_57_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_57_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_58_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_58_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_59_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_59_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_60_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_60_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_61_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_61_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_62_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_62_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int AllTypeTableSetModifyPk(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    SmallAllTypeTableStruct *obj = (SmallAllTypeTableStruct *)t;
    int ret = 0;
    int8_t a = (obj->proper_0_int1 + 1) % 127;
    ret = GmcSetVertexProperty(stmt, "proper_0_int1", GMC_DATATYPE_INT8, &a, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_0_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_1_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_1_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_2_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_2_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_3_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_3_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_4_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_4_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_5_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_5_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_6_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_6_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_7_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_7_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret =
        GmcSetVertexProperty(stmt, "proper_8_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_8_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_9_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_9_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_10_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_10_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_11_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_11_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_12_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_12_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_13_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_13_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_14_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_14_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_15_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_15_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_16_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_16_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_17_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_17_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_18_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_18_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_19_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_19_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_20_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_20_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_21_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_21_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_22_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_22_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_23_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_23_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_24_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_24_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_25_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_25_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_26_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_26_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_27_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_27_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_28_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_28_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_29_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_29_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_30_byte10", GMC_DATATYPE_FIXED, obj->proper30Byte10, sizeof(obj->proper30Byte10));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper30Byte10: %s, ret = %d.", obj->proper30Byte10, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_31_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_31_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_32_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_32_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_33_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_33_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_34_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_34_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_35_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_35_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_36_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_36_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_37_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_37_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_38_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_38_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_39_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_39_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_40_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_40_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_41_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_41_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_42_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_42_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_43_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_43_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_44_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_44_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_45_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_45_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_46_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_46_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_47_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_47_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_48_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_48_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_49_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_49_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_50_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_50_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_51_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_51_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_52_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_52_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_53_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_53_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_54_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_54_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "proper_55_int1", GMC_DATATYPE_INT8, &obj->proper_0_int1, sizeof(obj->proper_0_int1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_55_int1: %d, ret = %d.", obj->proper_0_int1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_56_int2", GMC_DATATYPE_INT16, &obj->proper_1_int2, sizeof(obj->proper_1_int2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_56_int2: %d, ret = %d.", obj->proper_1_int2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_57_int4", GMC_DATATYPE_INT32, &obj->proper_2_int4, sizeof(obj->proper_2_int4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_57_int4: %d, ret = %d.", obj->proper_2_int4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_58_int8", GMC_DATATYPE_INT64, &obj->proper_3_int8, sizeof(obj->proper_3_int8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_58_int8: %d, ret = %d.", obj->proper_3_int8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_59_uint1", GMC_DATATYPE_UINT8, &obj->proper_4_uint1, sizeof(obj->proper_4_uint1));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_59_uint1: %d, ret = %d.", obj->proper_4_uint1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_60_uint2", GMC_DATATYPE_UINT16, &obj->proper_5_uint2, sizeof(obj->proper_5_uint2));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_60_uint2: %d, ret = %d.", obj->proper_5_uint2, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_61_uint4", GMC_DATATYPE_UINT32, &obj->proper_6_uint4, sizeof(obj->proper_6_uint4));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_61_uint4: %d, ret = %d.", obj->proper_6_uint4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "proper_62_uint8", GMC_DATATYPE_UINT64, &obj->proper_7_uint8, sizeof(obj->proper_7_uint8));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] proper_62_uint8: %d, ret = %d.", obj->proper_7_uint8, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

template <typename StructObjT>
int arrycmp(const StructObjT *arry1, const StructObjT *arry2, int len)
{
    for (int i = 0; i < len; i++) {
        if (arry1[i] > arry2[i]) {
            return 1;
        } else if (arry1[i] < arry2[i]) {
            return -1;
        }
    }
    return 0;
}
// 比较文件一致性
int32_t CompateFileIsSame(const char *actFile, const char *expectFile)
{
    char command1[MAX_CMD_SIZE] = {0};
    char command2[MAX_CMD_SIZE] = {0};

    int fileContent1 = 10;
    int fileContent2 = -1;
    (void)memset(command1, 0, sizeof(char) * (MAX_CMD_SIZE));
    (void)memset(command2, 0, sizeof(char) * (MAX_CMD_SIZE));
    (void)snprintf(command1, MAX_CMD_SIZE, "md5sum %s", actFile);
    (void)snprintf(command2, MAX_CMD_SIZE, "md5sum %s", expectFile);
    system(command1);
    system(command2);
    EXPECT_EQ(0, TestGetResultCommand(command1, &fileContent1, NULL, 0));
    EXPECT_EQ(0, TestGetResultCommand(command2, &fileContent2, NULL, 0));
    AW_FUN_Log(LOG_INFO, "验证文件内容.");

    AW_MACRO_EXPECT_EQ_INT(fileContent2, fileContent1);
    if (fileContent2 != fileContent1) {
        AW_FUN_Log(LOG_INFO, "file is not same");
        AW_FUN_Log(LOG_DEBUG, "----------actul-----------");
        SystemSnprintf("cat %s", actFile);
        AW_FUN_Log(LOG_DEBUG, "---------------------");

        AW_FUN_Log(LOG_DEBUG, "---------- expect-----------");
        SystemSnprintf("cat %s", expectFile);
        AW_FUN_Log(LOG_DEBUG, "---------------------");
        return -1;
    }
    return GMERR_OK;
}
// 创建外部表
void DatalogCreateExternalTable(GmcStmtT *stmt, const char *labelJsonPath)
{
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    GmcDropVertexLabel(stmt, "B");
    readJanssonFile(labelJsonPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    int32_t ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
}

int C1Int4C1Int8C1StrC1ByteSet(GmcStmtT *stmt, void *t, bool IsExternalTable)
{
    C1Int4C1Int8C1StrC1ByteT *obj = (C1Int4C1Int8C1StrC1ByteT *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int4C1Int8C1StrC1ByteSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_STRING, obj->b, strlen(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int4C1Int8C1StrC1ByteSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_FIXED, obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int4C1Int8C1StrC1ByteSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int4C1Int8C1StrC1ByteSet] a: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    if (!IsExternalTable) {
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(
                LOG_ERROR, "[C1Int4C1Int8C1StrC1ByteSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

int C1Int4C1Int8C1StrC1ByteCmp(const C1Int4C1Int8C1StrC1ByteT *st1, const C1Int4C1Int8C1StrC1ByteT *st2,
    bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[C1Int4C1Int8C1StrC1ByteCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            return ret;
        }
        if (arrycmp(st1->b, st2->b, st1->bLen) != 0) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[C1Int4C1Int8C1StrC1ByteCmp] b, st1: %s, st2: %s.", st1->b, st2->b);
            }
        }
        if (st1->c[0] != st2->c[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[C1Int4C1Int8C1StrC1ByteCmp] c, st1: %d, st2: %d.", st1->c[0], st2->c[0]);
            }
        }
        if (!(st1->d >= st2->d)) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[C1Int4C1Int8C1StrC1ByteCmp] d, st1: %ld, st2: %ld.", st1->d, st2->d);
            }
            return ret;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[C1Int4C1Int8C1StrC1ByteCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[C1Int4C1Int8C1StrC1ByteCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

const char *g_schemaJson = R"([{
    "type":"record",
    "name":"%s",
    "fields":[
        {"name":"F7", "type":"uint32",  "nullable":false},
	    {"name":"F0", "type":"char",    "nullable":false},
	    {"name":"F1", "type":"uchar",   "nullable":true},
        {"name":"F2", "type":"int8",    "nullable":true},
        {"name":"F3", "type":"uint8",   "nullable":true},
        {"name":"F4", "type":"int16",   "nullable":true},
        {"name":"F5", "type":"uint16",  "nullable":true},
        {"name":"F6", "type":"int32",   "nullable":true},
        {"name":"F8", "type":"boolean", "nullable":true},
        {"name":"F9", "type":"int64",   "nullable":true},
        {"name":"F10", "type":"uint64", "nullable":true},
        {"name":"F11", "type":"float",  "nullable":true},
        {"name":"F12", "type":"double", "nullable":true},
        {"name":"F13", "type":"time",   "nullable":true},
        {"name":"F14", "type":"string", "nullable":true, "size":100},
        {"name":"F15", "type":"bytes",  "nullable":true, "size":10 },
        {"name":"F16", "type":"fixed",  "nullable":true, "size":5 },
        {"name":"F17", "type":"uint32", "nullable":true}
    ],
    "keys":[
       {
            "node":"%s",
            "name":"T20_PK",
            "fields":["F7"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
])";
// 仅统计当前现存normal表不包含yang
int testGetNormalTableNum(uint32_t *tableNum)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    int ret = 0;
    uint32_t isDeleted = 0;
    if (tableNum == NULL) {
        printf("[testGetNormalTableNum]:null pointer error\n");
        return FAILED;
    } else {
        *tableNum = 0;
    }
    bool innerError = false;
    char command[1024];
    char const *view_name = "V\\$CATA_VERTEX_LABEL_INFO";
    ret = snprintf(command, 1024, "%s/gmsysview -q %s ", g_toolPath, view_name);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer[150] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        perror("popen fail");
        return FAILED;
    }
    // 判断视图信息
    char *tmp = fgets(buffer, 150, pf);
    // 无打印
    if (tmp == NULL) {
        printf("[CATA_VERTEX_LABEL_INFO]:no print\n");
        if (pclose(pf) == -1) {
            perror("pclose fail");
        }
        return FAILED;
    }
    // 无表
    if (strstr(buffer, "fetched all records, finish") != NULL) {
        printf("[CATA_VERTEX_LABEL_INFO]:no table left in server\n");
        if (pclose(pf) == -1) {
            perror("pclose fail");
            return FAILED;
        }
        return 0;
    }
    // 非预期报错，打印输出
    if (strstr(buffer, "index = 0") == NULL) {
        innerError = true;
        printf("[CATA_VERTEX_LABEL_INFO]inner error:\n%s\n", buffer);
    }
    // 继续打印视图错误信息或者累计表数目
    while (fgets(buffer, 150, pf) != NULL) {
        if (innerError) {  // 继续打印视图错误信息
            printf("%s\n", buffer);
        } else {
            if (strstr(buffer, "VERTEX_TYPE_NORMAL") != NULL) {
                (*tableNum)++;
            }
            if (strstr(buffer, "VERTEX_TYPE_DATALOG") != NULL) {
                (*tableNum)++;
            }
            if (strstr(buffer, "IS_DELETED: 1") != NULL) {
                isDeleted++;
            }
        }
    }

    // 查询TBM_TABLE_NUM
    char command2[1024];
    char const *view_name2 = "V\\$CATA_GENERAL_INFO";
    ret = snprintf(command2, 1024, "%s/gmsysview -q %s |grep TBM_TABLE_NUM |awk '{print $2}'", g_toolPath, view_name2);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer2[150] = {0};
    FILE *pf2 = popen(command2, "r");
    if (pf2 == NULL) {
        perror("popen fail");
        return FAILED;
    }

    int size = 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        (void)pclose(pf2);
        printf("malloc failed, %s.", strerror(errno));
        return FAILED;
    }
    memset(tmpResult, 0, size);

    while (fgets(buffer2, sizeof(buffer2), pf2) != NULL) {
        strcat(tmpResult, buffer2);
    }

    ret = pclose(pf2);
    if (ret == -1) {
        printf("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return FAILED;
    }

    int tbmTableNum = 0;
    tbmTableNum = atoi(tmpResult);
    free(tmpResult);

    *tableNum = *tableNum + tbmTableNum - isDeleted;

    printf("[CATA_VERTEX_LABEL_INFO]existTable_num: %d\n", *tableNum);
    if (pclose(pf) == -1) {
        perror("pclose fail");
        return FAILED;
    } else if (innerError) {
        return FAILED;
    } else {
        return 0;
    }
#endif
}
int32_t CreateMuiltTables(GmcStmtT *stmt, int32_t tableNums)
{
    int32_t ret = -1;
    char *schema = NULL;
    char configJson[128] = "{\"max_record_count\" : 10000}";
    char labelName[20] = "T20_all_type";
    uint32_t tableNum = 0;
    ret = testGetNormalTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    tableNums = tableNums - tableNum;
    char schemaJson[2048] = "";
    AW_FUN_Log(LOG_STEP, "create tableNum is %d", tableNums);
    for (int i = 0; i < tableNums; i++) {
        (void)sprintf(labelName, "T20_all_type%d", i);
        (void)sprintf(schemaJson, g_schemaJson, labelName, labelName);
        ret = GmcCreateVertexLabel(stmt, schemaJson, configJson);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema);
    }
    return tableNums;
}

void DropMuiltTables(GmcStmtT *stmt, int32_t tableNums)
{
    int32_t ret = -1;
    char labelName[20] = "T20_all_type";
    AW_FUN_Log(LOG_STEP, "Drop tableNum is %d", tableNums);
    for (int i = 0; i < tableNums; i++) {
        (void)sprintf(labelName, "T20_all_type%d", i);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
#pragma pack(1)
typedef struct {
    int32_t callBackTime = 0;  // 触发回调总次数
    int32_t dataNum = 0;       // 推送数据条数总数
    int32_t dataNumMid = 0;    // 推送数据条数总数
    int32_t dataNumOut = 0;    // 推送数据条数总数
} PubSubResCallBackT;          //
#pragma pack(0)
int32_t g_callBackTime = 0;                         // 触发回调总次数
int32_t g_dataNum = 0;                              // 推送数据条数总数
int32_t g_dataNumMid = 0;                           // 推送数据条数总数
int32_t g_dataNumOut = 0;                           // 推送数据条数总数
SmallAllTypeTableStruct g_callBackTimeDatas1[512];  // 统计正向收到消息数据
SmallAllTypeTableStruct g_callBackTimeDatas3[512];  // 统计回滚收到消息数据
// 校验回调函数中回滚的资源字段值是否和用户设置的一致
int CheckResourcePropertyValue(GmcStmtT *stmt, void *t, bool isExternal = false, bool isResource = false)
{
    SmallAllTypeTableStruct *checkObj = (SmallAllTypeTableStruct *)t;
    AllTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;
    checkObj->proper_3_int8 = (int64_t)checkObj->proper_2_int4;
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_31_int1", &getObj.proper_0_int1, sizeof(getObj.proper_0_int1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_0_int1' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(getObj.proper_0_int1, checkObj->proper_0_int1);
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_32_int2", &getObj.proper_1_int2, sizeof(getObj.proper_1_int2), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_1_int2' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(getObj.proper_1_int2, checkObj->proper_1_int2);
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_33_int4", &getObj.proper_2_int4, sizeof(getObj.proper_2_int4), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(getObj.proper_2_int4, checkObj->proper_2_int4);
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_34_int8", &getObj.proper_3_int8, sizeof(getObj.proper_3_int8), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_3_int8' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(getObj.proper_3_int8, checkObj->proper_3_int8);
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_4_uint1", &getObj.proper_35_uint1, sizeof(getObj.proper_4_uint1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_4_uint1' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    if (getObj.proper_35_uint1 != checkObj->proper_4_uint1) {
        AW_FUN_Log(LOG_ERROR, "getObj.proper_35_uint1!=checkObj->proper_4_uint1.");
        AW_FUN_Log(LOG_ERROR, "getObj.proper_35_uint1 = %u", getObj.proper_35_uint1);
        AW_FUN_Log(LOG_ERROR, "checkObj->proper_4_uint1= %u", checkObj->proper_4_uint1);
        return -1;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_5_uint2", &getObj.proper_36_uint2, sizeof(getObj.proper_5_uint2), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_5_uint2' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(getObj.proper_36_uint2, checkObj->proper_5_uint2);
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_6_uint4", &getObj.proper_37_uint4, sizeof(getObj.proper_6_uint4), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_6_uint4' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(getObj.proper_37_uint4, checkObj->proper_6_uint4);
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_7_uint8", &getObj.proper_38_uint8, sizeof(getObj.proper_7_uint8), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_7_uint8' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(getObj.proper_38_uint8, checkObj->proper_7_uint8);
    return ret;
}
// 校验回调函数中回滚的资源字段值是否和用户设置的一致(byte512)
int CheckResourcePropertyValueByte512(GmcStmtT *stmt, void *t, bool isExternal = false, bool isResource = false)
{
    BigSmallAllTypeTableStruct *checkObj = (BigSmallAllTypeTableStruct *)t;
    BigSmallAllTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(
        stmt, "proper_31_int1", &getObj.properByte512, sizeof(getObj.properByte512), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableGet] get 'proper_0_int1' fail, ret = %d.", ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = memcmp((char *)getObj.properByte512, (char *)checkObj->properByte512, 512);
    return ret;
}


GmcConnT *g_connSync = NULL, *g_connSub = NULL;
GmcConnT *g_connSync1 = NULL, *g_connSub1 = NULL;
GmcConnT *g_connSync2 = NULL, *g_connSub2 = NULL;
GmcStmtT *g_stmtSync = NULL;
GmcStmtT *g_stmtSync1 = NULL;
GmcStmtT *g_stmtSync2 = NULL;
const char *g_subConnName = "subConnNameYWY";
const char *g_subName = "subVertexLabelYWY";
const char *g_subName1 = "subVertexLabelYWY1";
const char *g_subName2 = "subVertexLabelYWY2";
const char *g_subConnName1 = "subConnNameYWY1";
const char *g_subConnName2 = "subConnNameYWY2";

typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    FuncRead1 func;
    bool isPubsubRsc;
} SnUserDataWithFuncT;

int createSubscription(GmcStmtT *stmtSync, GmcConnT *connSub, char *subJsonPath, SnUserDataWithFuncT *userData,
    uint32_t mallocCount, const char *subsName, GmcSubCallbackT userCb, FuncRead1 func)
{
    char *subInfo = NULL;
    if (g_isDebugMode) {
        AW_FUN_Log(LOG_INFO, "createSubscription begin");
    }
    readJanssonFile(subJsonPath, &subInfo);
    AW_MACRO_EXPECT_NOTNULL(subInfo);

    int ret = testSnMallocUserData(&userData->data, mallocCount * 2, mallocCount * 2);
    EXPECT_EQ(GMERR_OK, ret);
    userData->func = func;
    userData->isPubsubRsc = true;
    if (g_isDebugMode) {
        AW_FUN_Log(LOG_INFO, "createSubscription mid");
    }

    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subsName;
    tmpSubInfo.configJson = subInfo;
    GmcUnSubscribe(stmtSync, subsName);
    ret = GmcSubscribe(stmtSync, &tmpSubInfo, connSub, userCb, userData);
    free(subInfo);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}
int cancelSubscription(GmcStmtT *stmtSync, const char *subsName, SnUserDataWithFuncT *userData)
{
    int ret = GmcUnSubscribe(stmtSync, subsName);
    EXPECT_EQ(GMERR_OK, ret);
    if (userData->data != NULL) {
        testSnFreeUserData(userData->data);
    }

    return GMERR_OK;
}

int snFetch(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    testGmcGetLastError();
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    return GMERR_OK;
}

int snFetch005(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
                        ret = testGmcGetLastError(
                            "Feature is not supported. Resp only support GMC_RESP_SEND_BATCH_INSERT type.");
                        EXPECT_EQ(GMERR_OK, ret);
                    }

                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    return GMERR_OK;
}

int snFetch007(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 100) && (g_callBackTime == 1)) {
            // 发送errorcode
            int64_t errorCode = 1000;
            AW_FUN_Log(LOG_STEP, "0000");
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    return GMERR_OK;
}

int snFetch014(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 100) && (g_callBackTime == 1)) {
            // 发送errorcode
            int64_t errorCode = 1000;
            AW_FUN_Log(LOG_STEP, "0000");
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    return GMERR_OK;
}

int snFetch011(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 100) && (g_callBackTime == 1)) {
            // 发送errorcode
            int64_t errorCode = 1000;
            AW_FUN_Log(LOG_STEP, "0000");
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
    }
    return GMERR_OK;
}

int snFetch015(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 60) && (g_callBackTime == 1)) {
            // 发送errorcode
            int64_t errorCode = 1000;
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    return GMERR_OK;
}

int snFetch009(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 228) && (g_callBackTime == 2)) {
            // 不再处理当前批剩余数据，发送errorcode
            int64_t errorCode = 1000;
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    return GMERR_OK;
}

int snFetch013(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 256) && (g_callBackTime == 2)) {
            // 不再处理当前批剩余数据，发送errorcode
            int64_t errorCode = 1000;
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
    }
    return GMERR_OK;
}

int snFetch012(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 228) && (g_callBackTime == 2)) {
            // 不再处理当前批剩余数据，发送errorcode
            int64_t errorCode = 1000;
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
    }
    return GMERR_OK;
}
int snFetch025(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 228) && (g_callBackTime == 2)) {
            // 不再处理当前批剩余数据，发送errorcode
            int64_t errorCode = 1000;
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    return GMERR_OK;
}

int snFetch029(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData, GmcRespT *response)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret) {
                        printf("g_dataNum,%d", g_dataNum);
                    }
                    // 读输出表和pubsub型资源表
                    ret = func(subStmt, data->dataCheckIndexes, userDefinedData->isPubsubRsc);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (userDefinedData->isPubsubRsc) {
                        ret = GmcSubAddRespDML(response, subStmt);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
        if ((g_dataNum == 228) && (g_callBackTime == 2)) {
            // 不再处理当前批剩余数据，发送errorcode
            int64_t errorCode = 1000;
            ret = GmcSetStmtAttr(subStmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
    }
    return GMERR_OK;
}
// 005 GmcSetRespMode，含GMC_RESP_SEND_FAILED_INDEX参数
void snCallback005(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;

    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (g_isDebugMode) {
        AW_FUN_Log(LOG_INFO, "snCallback005");
    }
    // pubsub资源表不支持订阅回调内使用该接口
    ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = snFetch005(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallback006(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;

    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (g_isDebugMode) {
        AW_FUN_Log(LOG_INFO, "snCallback006");
    }
    ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t failedDataNum = 1;
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {10};
    ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch005(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void snCallback007(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch007(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallback014(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch014(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void snCallback015(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);
    AW_FUN_Log(LOG_STEP, "call back --------------------------");

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch015(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallback011(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch011(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallback009(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch009(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallback013(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch013(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallback012(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch012(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallback025(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch025(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void snCallback029(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch029(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallback010(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    g_callBackTime++;
    GmcRespT *response = NULL;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snFetch009(subStmt, info, userData, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data->callbackTimes++;

    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 获取订阅推送数据
int32_t GetPropertyValueFromSub(GmcStmtT *subStmt, SubValueT *subArray, int32_t length, uint32_t valueCount = 1)
{
    bool eof = false;
    int32_t ret = -1;
    int8_t valueA;
    ret = GmcGetVertexPropertyByName(subStmt, "a", &valueA, sizeof(valueA), &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        testGmcGetLastError();
        return ret;
    }
    int16_t valueB;
    ret = GmcGetVertexPropertyByName(subStmt, "b", &valueB, sizeof(valueB), &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valueC;
    ret = GmcGetVertexPropertyByName(subStmt, "c", &valueC, sizeof(valueC), &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t valueD;
    ret = GmcGetVertexPropertyByName(subStmt, "d", &valueD, sizeof(valueD), &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    subArray[length].pk.a = valueA;
    subArray[length].pk.b = valueB;
    subArray[length].pk.c = valueC;
    subArray[length].updateDValue = valueD;
    subArray[length].count = valueCount;
    AW_FUN_Log(LOG_STEP, "valueA is %d\n", valueA);
    return ret;
}

// 获取订阅实际生效数据
int32_t GetPropertyActualValueFromSub(GmcStmtT *subStmt, SubValueT *subArray, uint32_t valueCount = 1)
{
    bool eof = false;
    int32_t ret = -1;
    int8_t valueA;
    ret = GmcGetVertexPropertyByName(subStmt, "a", &valueA, sizeof(valueA), &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        testGmcGetLastError();
        return ret;
    }
    int16_t valueB;
    ret = GmcGetVertexPropertyByName(subStmt, "b", &valueB, sizeof(valueB), &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t valueC;
    ret = GmcGetVertexPropertyByName(subStmt, "c", &valueC, sizeof(valueC), &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t valueD;
    ret = GmcGetVertexPropertyByName(subStmt, "d", &valueD, sizeof(valueD), &eof);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    subArray[valueC].pk.a = valueA;
    subArray[valueC].pk.b = valueB;
    subArray[valueC].pk.c = valueC;
    subArray[valueC].updateDValue = valueD;
    subArray[valueC].count = valueCount;
    AW_FUN_Log(LOG_INFO, "a:%d,d:%d, count:%d \n", valueA, valueD, valueCount);
    return ret;
}

int rsc0Get(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0Get007(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    AW_FUN_Log(LOG_STEP, "1.g_dataNum is %d", g_dataNum);
    if (g_dataNum == 100) {
        g_isRobackCurrentBatch = false;
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

int rsc0Get014(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    AW_FUN_Log(LOG_STEP, "1.g_dataNum is %d", g_dataNum);
    if (g_dataNum > 100 && g_dataNum <= 128) {
        g_isRobackCurrentBatch = false;
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

int rsc0Get008(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    AW_FUN_Log(LOG_STEP, "1.g_dataNum is %d", g_dataNum);
    if (g_dataNum == 101) {
        g_isRobackCurrentBatch = false;
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

int rsc0Get015(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    bool isNull = true;

    BigSmallAllTypeTableStruct objIn1;
    int ret =
        GmcGetVertexPropertyByName(stmt, "proper_1_int2", objIn1.properByte512, sizeof(objIn1.properByte512), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;

    int32_t dtlReservedCount = 1;
    ret =
        GmcGetVertexPropertyByName(stmt, "proper_0_int1", &objIn1.proper_0_int1, sizeof(objIn1.proper_0_int1), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    int b = objIn1.proper_0_int1;
    for (int j = 0; j < 512; j++) {
        objIn1.properByte512[j] = 0xff;
    }
    AW_FUN_Log(LOG_STEP, "g_dataNum is %d", g_dataNum);
    if (g_dataNum == 60) {
        g_isRobackCurrentBatch = true;
    }
    // 收集正向订阅推送数据
    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_0_int1 = objIn1.proper_0_int1;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime == 2) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[59 - (g_dataNum - 61)].proper_0_int1, b);
        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValueByte512(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = BigSmallAllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

int rsc0Get016(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    bool isNull = true;

    BigSmallAllTypeTableStruct objIn1;
    int ret =
        GmcGetVertexPropertyByName(stmt, "proper_1_int2", objIn1.properByte512, sizeof(objIn1.properByte512), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;

    int32_t dtlReservedCount = 1;
    ret =
        GmcGetVertexPropertyByName(stmt, "proper_0_int1", &objIn1.proper_0_int1, sizeof(objIn1.proper_0_int1), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    int b = objIn1.proper_0_int1;
    for (int j = 0; j < 512; j++) {
        objIn1.properByte512[j] = 0xff;
    }
    AW_FUN_Log(LOG_STEP, "g_dataNum is %d", g_dataNum);
    if (g_dataNum == 60) {
        g_isRobackCurrentBatch = false;
    }
    // 收集正向订阅推送数据
    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_0_int1 = objIn1.proper_0_int1;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime == 2) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[59 - (g_dataNum - 61)].proper_0_int1, b);
        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValueByte512(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = BigSmallAllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}
// 回滚当前批
int rsc0Get011(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    AW_FUN_Log(LOG_STEP, "g_dataNum is %d.", g_dataNum);
    // 收集正向订阅推送数据
    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_1_int2 = a;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime == 2) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[99 - (g_dataNum - 101)].proper_1_int2, a);

        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

// 回滚当前批
int rsc0Get009(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    AW_FUN_Log(LOG_STEP, "1.g_dataNum is %d", g_dataNum);
    // 收集正向订阅推送数据
    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_1_int2 = a;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime == 3) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[127 - (g_dataNum - 257)].proper_1_int2, a);

        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 第228条设置资源字段不合理
    if (g_dataNum == 228) {
        g_isRobackCurrentBatch = false;
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

// 回滚当前批
int rsc0Get013(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    // 收集正向订阅推送数据
    if (g_callBackTime <= 2) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_1_int2 = a;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime > 2 && g_callBackTime <= 4) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[255 - (g_dataNum - 257)].proper_1_int2, a);

        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 第228条设置资源字段合理
    if (g_dataNum == 256) {
        g_isRobackCurrentBatch = true;
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

// 不回滚当前批
int rsc0Get010(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    // 收集正向订阅推送数据
    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_1_int2 = a;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime == 3) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[127 - (g_dataNum - 257)].proper_1_int2, a);

        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 第229条设置资源字段不合理
    if (g_dataNum == 229) {
        g_isRobackCurrentBatch = false;
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

// 回滚当前批
int rsc0Get012(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    AW_FUN_Log(LOG_STEP, "g_dataNum is %d.", g_dataNum);
    // 收集正向订阅推送数据
    if (g_callBackTime <= 2) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_1_int2 = a;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime > 2 && g_callBackTime <= 4) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[227 - (g_dataNum - 229)].proper_1_int2, a);

        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 第228条设置资源字段合理
    if (g_dataNum == 228) {
        g_isRobackCurrentBatch = true;
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

// 回滚当前批
int rsc0Get025(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    AW_FUN_Log(LOG_STEP, "g_dataNum is %d.", g_dataNum);
    // 收集正向订阅推送数据
    if (g_callBackTime <= 2) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_1_int2 = a;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime > 2 && g_callBackTime <= 4) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[255 - (g_dataNum - 257)].proper_1_int2, a);

        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 第228条设置资源字段合理
    if (g_dataNum == 228) {
        g_isRobackCurrentBatch = true;
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}

int rsc0Get029(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    AW_FUN_Log(LOG_STEP, "g_dataNum is %d.", g_dataNum);
    // 收集正向订阅推送数据
    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[g_dataNum - 1].proper_1_int2 = a;
    }

    // 回滚资源字段数据和非资源字段数据校验逻辑分开
    if (g_callBackTime == 3) {
        // 校验非字段字段回滚顺序及数据是否正确
        EXPECT_EQ(g_callBackTimeDatas1[127 - (g_dataNum - 229)].proper_1_int2, a);

        // 校验回滚数据资源字段已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 第228条设置资源字段合理
    if (g_dataNum == 228) {
        g_isRobackCurrentBatch = false;
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isRobackCurrentBatch = true;
    return ret;
}
int rsc0GetExternal(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(a % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if (g_callBackTime == 16) {
        g_callBackTimeDatas3[127 - (g_dataNum - 1873)].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetExternal128(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(a % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;

    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if (g_callBackTime == 4) {
        g_callBackTimeDatas3[127 - (g_dataNum - 131)].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetGmimport(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(a % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    if (g_dataNum == 510) {
        objIn1.dtlReservedCount = -1;
    }
    // 订阅推送失败，未发生回滚

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int g_isCount0;  // 控制是否回滚
int rsc0GetUpdate(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    int32_t c = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "proper_2_int4", &c, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(c % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = c;
    objIn1.proper_3_int8 = c;
    objIn1.proper_4_uint1 = c % 255;
    objIn1.proper_5_uint2 = c % 65535;
    objIn1.proper_6_uint4 = c;
    objIn1.proper_7_uint8 = c;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = c % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    if (dtlReservedCount < 0 && g_dataNum > 128 && g_isCount0 == 1) {
        objIn1.dtlReservedCount = 1;
        g_isCount0 = 2;
    }
    if ((g_callBackTime == 1) && (g_isCount0 == 1)) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if ((g_callBackTime == 3) && (g_isCount0 == 2)) {
        g_callBackTimeDatas3[127 - (g_dataNum - 257)].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        objIn1.proper_1_int2 = objIn1.proper_2_int4;
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetUpdateout(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    int32_t c = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "proper_2_int4", &c, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(c % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = c;
    objIn1.proper_3_int8 = c;
    objIn1.proper_4_uint1 = c % 255;
    objIn1.proper_5_uint2 = c % 65535;
    objIn1.proper_6_uint4 = c;
    objIn1.proper_7_uint8 = c;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = c % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    if (dtlReservedCount < 0 && g_dataNum > 128 && g_isCount0 == 1) {
        objIn1.dtlReservedCount = 1;
        g_isCount0 = 2;
    }
    if ((g_callBackTime == 1) && (g_isCount0 == 1)) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if ((g_callBackTime == 3) && (g_isCount0 == 2)) {
        g_callBackTimeDatas3[127 - (g_dataNum - 237)].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        objIn1.proper_1_int2 = objIn1.proper_2_int4;
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetDelete(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    int32_t c = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "proper_2_int4", &c, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(c % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = c;
    objIn1.proper_3_int8 = c;
    objIn1.proper_4_uint1 = c % 255;
    objIn1.proper_5_uint2 = c % 65535;
    objIn1.proper_6_uint4 = c;
    objIn1.proper_7_uint8 = c;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = c % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    if (dtlReservedCount < 0 && g_dataNum > 128 && g_isCount0 == 1) {
        objIn1.dtlReservedCount = 1;
        g_isCount0 = 2;
        AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
        AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    }
    if ((g_callBackTime == 1) && (g_isCount0 == 1)) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if ((g_callBackTime == 3) && (g_isCount0 == 2)) {
        g_callBackTimeDatas3[127 - (g_dataNum - 237)].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        objIn1.proper_1_int2 = objIn1.proper_2_int4;
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

void UnSubWhenRollBack()
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, g_subName);
    EXPECT_EQ(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
int rsc0GetWriteSelf(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    int32_t c = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "proper_2_int4", &c, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(c % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = c;
    objIn1.proper_3_int8 = c;
    objIn1.proper_4_uint1 = c % 255;
    objIn1.proper_5_uint2 = c % 65535;
    objIn1.proper_6_uint4 = c;
    objIn1.proper_7_uint8 = c;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = c % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    if (g_dataNum == 129) {

        AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
        AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetRollBackUnSub(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    int32_t c = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexPropertyByName(stmt, "proper_2_int4", &c, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(c % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = c;
    objIn1.proper_3_int8 = c;
    objIn1.proper_4_uint1 = c % 255;
    objIn1.proper_5_uint2 = c % 65535;
    objIn1.proper_6_uint4 = c;
    objIn1.proper_7_uint8 = c;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = c % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    if (g_dataNum == 129) {
        UnSubWhenRollBack();
        objIn1.dtlReservedCount = -1;
        AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
        AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    }
    if (g_dataNum < 129) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if (g_dataNum > 129 && g_callBackTime == 3) {
        g_callBackTimeDatas3[127 - (g_dataNum - 130)].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        objIn1.proper_1_int2 = objIn1.proper_2_int4;
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetDym(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(a % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;

    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 100].proper_1_int2 = objIn1.proper_1_int2;
    }
    if (g_callBackTime == 2) {
        g_callBackTimeDatas3[99 - (g_dataNum - 101)].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetPkSame(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = (int8_t)(a % 127);
    objIn1.proper_1_int2 = a;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;

    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if (g_callBackTime == 2) {
        g_callBackTimeDatas3[127 - (g_dataNum - 129)].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 比较第一批和第四批
int rsc0Get1(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;

    if (g_callBackTime < 3) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 129].proper_1_int2 = objIn1.proper_1_int2;
    } else {
        // 校验回滚数据已经为用户设置值
        if (g_callBackTime == 4) {
            ret = CheckResourcePropertyValue(stmt, &objIn1);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (g_dataNum % 129 != 0) {
            g_callBackTimeDatas3[(129 - (g_dataNum % 129))].proper_1_int2 = objIn1.proper_1_int2;
        } else {
            g_callBackTimeDatas3[0].proper_1_int2 = objIn1.proper_1_int2;
        }
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 响应的count和正向接收到的count不一致，count>0
int rsc0GetCount1(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    if (g_callBackTime == 4) {
        objIn1.dtlReservedCount = 1;
    }
    g_dataNum++;

    if (g_callBackTime == 3) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    } else {
        // 校验回滚数据已经为用户设置值
        if (g_callBackTime == 5) {
            ret = CheckResourcePropertyValue(stmt, &objIn1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (g_callBackTime == 5) {
            if (g_dataNum % 129 != 0) {
                g_callBackTimeDatas3[127 - (g_dataNum % 130)].proper_1_int2 = objIn1.proper_1_int2;
            }
        }
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 响应的count和正向接收到的count不一致，count=0
int rsc0GetCount2(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    if (g_callBackTime == 4) {
        objIn1.dtlReservedCount = 0;
    }
    g_dataNum++;

    if (g_callBackTime == 3) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    } else {
        // 校验回滚数据已经为用户设置值
        if (g_callBackTime == 5) {
            ret = CheckResourcePropertyValue(stmt, &objIn1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (g_callBackTime == 5) {
            if (g_dataNum % 129 != 0) {
                g_callBackTimeDatas3[127 - (g_dataNum % 130)].proper_1_int2 = objIn1.proper_1_int2;
            }
        }
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 响应的count和正向接收到的count不一致，count<0
int rsc0GetCount3(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &a, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = a % 127;
    objIn1.proper_1_int2 = a % 32767;
    objIn1.proper_2_int4 = a;
    objIn1.proper_3_int8 = a;
    objIn1.proper_4_uint1 = a % 255;
    objIn1.proper_5_uint2 = a % 65535;
    objIn1.proper_6_uint4 = a;
    objIn1.proper_7_uint8 = a;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = a % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    if (g_callBackTime == 2) {
        objIn1.dtlReservedCount = -1;
    }
    g_dataNum++;

    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    } else {
        // 校验回滚数据已经为用户设置值
        if (g_callBackTime == 3) {
            ret = CheckResourcePropertyValue(stmt, &objIn1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (g_callBackTime == 3) {
            if (g_dataNum % 129 != 0) {
                g_callBackTimeDatas3[127 - (g_dataNum % 130)].proper_1_int2 = objIn1.proper_1_int2;
            }
        }
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 过期数据推送时报错
int rsc0GetTimeout(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int64_t d = 0;
    int16_t b = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_3_int8", &d, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &b, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = b % 127;
    objIn1.proper_1_int2 = b % 32767;
    objIn1.proper_2_int4 = b;
    objIn1.proper_3_int8 = d;
    objIn1.proper_4_uint1 = b % 255;
    objIn1.proper_5_uint2 = b % 65535;
    objIn1.proper_6_uint4 = b;
    objIn1.proper_7_uint8 = b;
    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = b % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    g_dataNum++;
    if (g_dataNum == 258) {
        objIn1.dtlReservedCount = 1;
    }
    if (g_callBackTime == 3) {
        g_callBackTimeDatas1[(g_dataNum - 130) % 128].proper_1_int2 = objIn1.proper_1_int2;
    } else {
        // 校验回滚数据已经为用户设置值
        if (g_callBackTime == 5) {
            ret = CheckResourcePropertyValue(stmt, &objIn1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (g_callBackTime == 5) {
            g_callBackTimeDatas3[127 - (g_dataNum - 259)].proper_1_int2 = objIn1.proper_1_int2;
        }
    }

    if (isPubsubRsc) {
        ret = AllTypeTableSetTimeout(stmt, &objIn1, false, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetModifyPk(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t b = 0;
    bool isNull = true;
    uint8_t c = 0;
    g_dataNum++;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &b, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = GmcGetVertexPropertyByName(stmt, "proper_4_uint1", &c, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = b % 127;
    objIn1.proper_1_int2 = b % 32767;
    objIn1.proper_2_int4 = b;
    objIn1.proper_3_int8 = b;
    objIn1.proper_4_uint1 = (uint8_t)c;
    objIn1.proper_5_uint2 = (uint16_t)c;
    objIn1.proper_6_uint4 = (uint32_t)c;
    objIn1.proper_7_uint8 = (uint64_t)c;

    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = b % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;

    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if (g_callBackTime == 3) {
        g_callBackTimeDatas3[(257 - g_dataNum) % 128].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (isPubsubRsc) {
        if (g_callBackTime > 1) {
            // 修改主键值
            ret = AllTypeTableSetModifyPk(stmt, &objIn1, false);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = AllTypeTableSet(stmt, &objIn1, false);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return ret;
}

int rsc0GetOut(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t b = 0;
    bool isNull = true;
    uint8_t c = 0;
    g_dataNumOut++;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &b, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = GmcGetVertexPropertyByName(stmt, "proper_4_uint1", &c, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = b % 127;
    objIn1.proper_1_int2 = b % 32767;
    objIn1.proper_2_int4 = b;
    objIn1.proper_3_int8 = b;
    objIn1.proper_4_uint1 = (uint8_t)c;
    objIn1.proper_5_uint2 = (uint16_t)c;
    objIn1.proper_6_uint4 = (uint32_t)c;
    objIn1.proper_7_uint8 = (uint64_t)c;

    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = b % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetOutCheck(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t b = 0;
    bool isNull = true;
    uint8_t c = 0;
    g_dataNumOut++;
    g_dataNum++;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &b, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = GmcGetVertexPropertyByName(stmt, "proper_4_uint1", &c, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = b % 127;
    objIn1.proper_1_int2 = b % 32767;
    objIn1.proper_2_int4 = b;
    objIn1.proper_3_int8 = b;
    objIn1.proper_4_uint1 = (uint8_t)c;
    objIn1.proper_5_uint2 = (uint16_t)c;
    objIn1.proper_6_uint4 = (uint32_t)c;
    objIn1.proper_7_uint8 = (uint64_t)c;

    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = b % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;

    if (g_dataNum < 130) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 129].proper_1_int2 = objIn1.proper_1_int2;
    } else {
        g_callBackTimeDatas3[(258 - g_dataNum) % 129].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetOutFail(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t b = 0;
    bool isNull = true;
    uint8_t c = 0;
    g_dataNum++;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &b, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = GmcGetVertexPropertyByName(stmt, "proper_4_uint1", &c, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = b % 127;
    objIn1.proper_1_int2 = b % 32767;
    objIn1.proper_2_int4 = b;
    objIn1.proper_3_int8 = b;
    objIn1.proper_4_uint1 = (uint8_t)c;
    objIn1.proper_5_uint2 = (uint16_t)c;
    objIn1.proper_6_uint4 = (uint32_t)c;
    objIn1.proper_7_uint8 = (uint64_t)c;

    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = b % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    if (g_dataNum == 129) {
        objIn1.dtlReservedCount = -1;
    }

    if (g_dataNum < 129) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if (g_dataNum > 129) {
        g_callBackTimeDatas3[(257 - g_dataNum) % 128].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetMid(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t b = 0;
    bool isNull = true;
    uint8_t c = 0;
    g_dataNumMid++;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &b, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = GmcGetVertexPropertyByName(stmt, "proper_4_uint1", &c, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = b % 127;
    objIn1.proper_1_int2 = b % 32767;
    objIn1.proper_2_int4 = b;
    objIn1.proper_3_int8 = b;
    objIn1.proper_4_uint1 = (uint8_t)c;
    objIn1.proper_5_uint2 = (uint16_t)c;
    objIn1.proper_6_uint4 = (uint32_t)c;
    objIn1.proper_7_uint8 = (uint64_t)c;

    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = b % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    if (dtlReservedCount < 0) {
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int rsc0GetMidWait2S(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t b = 0;
    bool isNull = true;
    uint8_t c = 0;
    g_dataNumMid++;
    g_dataNum++;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &b, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    ret = GmcGetVertexPropertyByName(stmt, "proper_4_uint1", &c, sizeof(uint8_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = b % 127;
    objIn1.proper_1_int2 = b % 32767;
    objIn1.proper_2_int4 = b;
    objIn1.proper_3_int8 = b;
    objIn1.proper_4_uint1 = (uint8_t)c;
    objIn1.proper_5_uint2 = (uint16_t)c;
    objIn1.proper_6_uint4 = (uint32_t)c;
    objIn1.proper_7_uint8 = (uint64_t)c;

    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = b % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;
    if (dtlReservedCount > 0 && g_dataNum == 10) {
        sleep(2);
    }
    if (isPubsubRsc) {
        ret = AllTypeTableSet(stmt, &objIn1, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}
// 第二批正向推送数据不响应
int rsc0GetCallBackNoReSend(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc)
{
    int16_t b = 0;
    bool isNull = true;
    g_dataNum++;
    int ret = GmcGetVertexPropertyByName(stmt, "proper_1_int2", &b, sizeof(int16_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    SmallAllTypeTableStruct objIn1;
    objIn1.dtlReservedCount = 1;
    objIn1.upgradeVersion = 0;
    objIn1.proper_0_int1 = b % 127;
    objIn1.proper_1_int2 = b % 32767;
    objIn1.proper_2_int4 = b;
    objIn1.proper_3_int8 = b;
    objIn1.proper_4_uint1 = b % 255;
    objIn1.proper_5_uint2 = b % 65535;
    objIn1.proper_6_uint4 = b;

    objIn1.proper_7_uint8 = b;

    for (int j = 0; j < 10; j++) {
        objIn1.proper30Byte10[j] = b % 50;
    }
    int32_t dtlReservedCount = 1;
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCount, sizeof(int32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    objIn1.dtlReservedCount = dtlReservedCount;

    if (g_callBackTime == 1) {
        g_callBackTimeDatas1[(g_dataNum - 1) % 128].proper_1_int2 = objIn1.proper_1_int2;
    }
    if (g_callBackTime == 3) {
        g_callBackTimeDatas3[(257 - g_dataNum) % 128].proper_1_int2 = objIn1.proper_1_int2;
        // 校验回滚数据已经为用户设置值
        ret = CheckResourcePropertyValue(stmt, &objIn1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (isPubsubRsc) {
        if (g_callBackTime > 1) {
        } else {
            ret = AllTypeTableSet(stmt, &objIn1, false);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return ret;
}

// 检测回滚消息和正向消息是否一致
void CheckoutArrayIsSame(int32_t arrayLength = 128)
{
    for (int32_t i = 0; i < arrayLength; i++) {
        if (g_callBackTimeDatas1[i % arrayLength].proper_1_int2 !=
            g_callBackTimeDatas3[i % arrayLength].proper_1_int2) {
            AW_FUN_Log(LOG_INFO, "推送和回滚顺序不一致! 第%d轮", i);
            printf("g_callBackTimeDatas1[i % arrayLength].proper_1_int2=%d-----------\n",
                g_callBackTimeDatas1[i % arrayLength].proper_1_int2);
            printf("g_callBackTimeDatas3[i % arrayLength].proper_1_int2=%d-----------\n",
                g_callBackTimeDatas3[i % arrayLength].proper_1_int2);
            EXPECT_EQ(GMERR_OK, -1);
            return;
        }
    }
    AW_FUN_Log(LOG_INFO, "推送和回滚顺序一致");
}
void snCallbackExternal(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false, isNull = false;
    bool newFieldIsNull[2] = {true};
    bool newFieldIsExist[2] = {true};
    uint32_t f7Value = 0;
    AW_FUN_Log(LOG_INFO, "snCallbackExternal userData %p userData1 %p", userData, userData1);
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                AW_FUN_Log(LOG_INFO, "GMC_SUB_EVENT_INITIAL_LOAD_EOF");
                userData1->scanEofNum++;
                AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    AW_FUN_Log(LOG_INFO, "GMC_SUB_EVENT_MODIFY");
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    AW_FUN_Log(LOG_INFO, "GMC_SUB_EVENT_DELETE");
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    AW_FUN_Log(LOG_INFO, "GMC_SUB_EVENT_AGED");
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    AW_FUN_Log(LOG_INFO, "GMC_SUB_EVENT_INITIAL_LOAD");
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n", info->msgType,
                        info->eventType, __LINE__);
                    AW_FUN_Log(LOG_INFO, "default");
                    break;
                }
            }
            break;
        }
        userData1->subIndex++;

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void ReSetUserData(void *userData)
{
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    userData1->agedNum = 0;
    userData1->insertNum = 0;
    userData1->deleteNum = 0;
}

void snCallbackExternal1(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                data->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
}

// 建表
void CreateTestTable(const char *labelJsonPath, const char *configJson, const char *labelName)
{
    int32_t ret = -1;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "建%s表", labelName);
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)GmcDropVertexLabel(stmt, labelName);
    char *schema = NULL;
    readJanssonFile(labelJsonPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 删表
int32_t DropTestTable(const char *labelName)
{
    int32_t ret = -1;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "删%s表", labelName);
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        testGmcGetLastError();
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
// 生成byte变长字段
Status Generate_Bytes(uint8_t **bytes, uint32_t byteLen)
{
    uint8_t *byteArray = (uint8_t *)malloc(byteLen + 1);
    if (byteArray == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    for (uint32_t i = 0; i < byteLen; i++) {
        int num = i;
        while (num >= 10) {
            num = num % 10;
        }
        byteArray[i] = (uint8_t)num;
    }
    byteArray[byteLen] = '\n';
    *bytes = byteArray;
    return GMERR_OK;
}

// 设置写入值
// 二维数组赋值
void SetArrayValue(
    SmallAllTypeTableStruct *objIn1, int recordNum = 100, int32_t dtlReservedCount = 1, int startValue = 0)
{
    for (int i = startValue; i < recordNum; i++) {
        objIn1[i % 1000].dtlReservedCount = dtlReservedCount;
        objIn1[i % 1000].upgradeVersion = 0;
        objIn1[i % 1000].proper_0_int1 = (i + 1) % 127;
        objIn1[i % 1000].proper_1_int2 = (i + 1) % 32767;
        objIn1[i % 1000].proper_2_int4 = (i + 1);
        objIn1[i % 1000].proper_3_int8 = (i + 1);
        objIn1[i % 1000].proper_4_uint1 = (i + 1) % 255;
        objIn1[i % 1000].proper_5_uint2 = (i + 1) % 65535;
        objIn1[i % 1000].proper_6_uint4 = (i + 1);
        objIn1[i % 1000].proper_7_uint8 = (i + 1);
        for (int j = 0; j < 10; j++) {
            objIn1[i % 1000].proper30Byte10[j] = (i + 1) % 50;
        }
    }
}

void BigSmallSetArrayValue(
    BigSmallAllTypeTableStruct *objIn1, int recordNum = 100, int32_t dtlReservedCount = 1, int startValue = 0)
{
    for (int i = startValue; i < recordNum; i++) {
        objIn1[i % 1000].dtlReservedCount = dtlReservedCount;
        objIn1[i % 1000].upgradeVersion = 0;
        objIn1[i % 1000].proper_0_int1 = i;
        for (int j = 0; j < 512; j++) {
            objIn1[i % 1000].properByte512[j] = 0xff;
        }
    }
}
// 释放已申请的内存
void ToFreeAlloc(NewALLTypeTableStruct *objIn1, int recordNum = 100)
{
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].a4buf);
    }
    free(objIn1);
}
// 含所有数据类型
int NewAlLTypeTableSet(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    NewALLTypeTableStruct *obj = (NewALLTypeTableStruct *)t;
    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] e: %u, ret = %d.", obj->e, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, 128);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] f: %u, ret = %d.", obj->f, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, obj->g, 256);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] g: %u, ret = %d.", obj->g, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT8, &obj->a1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AlLTypeTableSet] a1: %d, ret = %d.", obj->a1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_UINT16, &obj->b1, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] b1: %d, ret = %d.", obj->b1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_UINT32, &obj->c1, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] c1: %d, ret = %d.", obj->c1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_UINT64, &obj->d1, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] d1: %d, ret = %d.", obj->d1, ret);
        return ret;
    }
    uint8_t value1 = obj->e1;
    ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_BITFIELD8, &value1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] e1: %d, ret = %d.", value1, ret);
        return ret;
    }

    uint16_t value2 = obj->f1;
    ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_BITFIELD16, &value2, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] f1: %d, ret = %d.", value2, ret);
        return ret;
    }

    uint32_t value3 = obj->g1;
    ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_BITFIELD32, &value3, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] g1: %d, ret = %d.", value3, ret);
        return ret;
    }

    uint64_t value4 = obj->a2;
    ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_BITFIELD64, &value4, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a2: %d, ret = %d.", value4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_STRING, obj->a3buf, (strlen((char *)obj->a3buf)));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a3: %s, ret = %d.", obj->a3buf, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_BYTES, obj->a4buf, obj->a4len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a4: %s, ret = %d.", obj->a4buf, ret);
        return ret;
    }
    if (!isStateTableInput) {
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
            return ret;
        }

        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

// 不含位域
int NewAlLTypeTableSet1(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    NewALLTypeTableStruct *obj = (NewALLTypeTableStruct *)t;
    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] e: %u, ret = %d.", obj->e, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, 128);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] f: %u, ret = %d.", obj->f, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, obj->g, 256);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] g: %u, ret = %d.", obj->g, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT8, &obj->a1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AlLTypeTableSet] a1: %d, ret = %d.", obj->a1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_UINT16, &obj->b1, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] b1: %d, ret = %d.", obj->b1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_UINT32, &obj->c1, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] c1: %d, ret = %d.", obj->c1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_UINT64, &obj->d1, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] d1: %d, ret = %d.", obj->d1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_STRING, obj->a3buf, (strlen((char *)obj->a3buf)));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a3: %s, ret = %d.", obj->a3buf, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_BYTES, obj->a4buf, obj->a4len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] a4: %s, ret = %d.", obj->a4buf, ret);
        return ret;
    }
    if (!isStateTableInput) {
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
            return ret;
        }

        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewAlLTypeTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

// 订阅json不需要带中括号
const char *g_subJsonPart = R"({
    "label_name":"%s",
    "comment":"status_merge",
     "subs_type":"status_merge",
    "events":
        [
            %s
        ],
    "is_reliable": false
})";

const char *g_subJsonPart1 = R"({
    "label_name":"%s",
    "comment":"status_merge",
     "subs_type":"status_merge",
    "events":
        [
            %s
        ],
    "is_reliable": false,
    %s
})";

int TestEnvInitNotReg(int runMode = -1, bool needStart = true)
{
    if (g_runStat == RUN_STAT_SUCC) {
        return 0;
    }
    int ret = 0;
    if (g_runStat == RUN_STAT_INIT) {
        while (ret < 500 && g_runStat == RUN_STAT_INIT) {
            ++ret;
            usleep(10000);
        }
        return 0;
    }
    g_runStat = RUN_STAT_INIT;
    pthread_mutex_init(&g_logLockTest, NULL);
    pthread_mutex_init(&g_logLockClient, NULL);
    pthread_mutex_init(&g_connLock, NULL);
    pthread_mutex_init(&g_connConcurrent, NULL);
    memset(g_logFilter, 0, sizeof(g_logFilter));
    pthread_mutex_init(&g_parseSchemaCtx.threadLock, NULL);
    if (g_isReadConfig == false) {
        getSysConfig();
    }

    if (runMode != -1) {
        g_runMode = runMode;
    }
    if (g_runMode == 1) {
        GmcLogAdptFuncsT log;
        log.userWriteFunc = testcaseWriteLog;
        log.handle = &g_logCtx;
        g_logCtx.fd = NULL;
        ret = GmcRegAdaptFuncs(GMC_ADPT_LOG, &log);
        if (ret != 0) {
            printf("[testEnvInit] GmcRegAdaptFuncs failed, ret = %d.\n", ret);
        }
    }
    ret = OpenEpollFunFromHPE();
    if (ret != 0) {
        printf("[testEnvInit] OpenEpollFunFromHPE failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = GmcInit();
    if (ret != 0) {
        printf("[testEnvInit] GmcInit failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = testPrepareNameSpace();
    if (ret != 0) {
        printf("[testEnvInit] testPrepareNameSpace failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }

    g_runStat = RUN_STAT_SUCC;
    return 0;
}

int NewAllTypeTableCmp(
    const NewALLTypeTableStruct *st1, const NewALLTypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (st1->d != st2->d) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d, st1: %d, st2: %d.", st1->d, st2->d);
            }
        }

        if (st1->e[0] != st2->e[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] e, st1: %d, st2: %d.", st1->e[0], st2->e[0]);
            }
        }

        if (st1->f[0] != st2->f[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] f, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->g[0] != st2->g[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] g, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->a1 != st2->a1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a1, st1: %d, st2: %d.", st1->a1, st2->a1);
            }
        }

        if (st1->b1 != st2->b1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b1, st1: %d, st2: %d.", st1->b1, st2->b1);
            }
        }

        if (st1->c1 != st2->c1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c1, st1: %d, st2: %d.", st1->c1, st2->c1);
            }
        }

        if (st1->d1 != st2->d1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d1, st1: %d, st2: %d.", st1->d1, st2->d1);
            }
        }

        if (st1->a1 != st2->e1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] e1, st1: %d, st2: %d.", st1->e1, st2->e1);
            }
        }

        if (st1->f1 != st2->f1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] f1, st1: %d, st2: %d.", st1->f1, st2->f1);
            }
        }

        if (st1->g1 != st2->g1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] g1, st1: %d, st2: %d.", st1->g1, st2->g1);
            }
        }

        if (st1->a2 != st2->a2) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a2, st1: %d, st2: %d.", st1->a2, st2->a2);
            }
        }

        if (arrycmp(st1->a3buf, st2->a3buf, st1->a3len) != 0) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a3, st1: %s, st2: %s.", st1->a3buf, st2->a3buf);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}
// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte128, g:byte256,uint1-uint8,uint1_8-uint8_64,str,byte
int NewTypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    NewALLTypeTableStruct *checkObj = (NewALLTypeTableStruct *)t;
    NewALLTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "g", getObj.g, sizeof(getObj.g), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a1, sizeof(getObj.a1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b1", &getObj.b1, sizeof(getObj.b1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c1", &getObj.c1, sizeof(getObj.c1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d1", &getObj.d1, sizeof(getObj.d1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }
    uint8_t value1 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "e1", &value1, sizeof(uint8_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'e1' fail, ret = %d.", ret);
        return ret;
    }
    getObj.e1 = value1;

    uint16_t value2 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "f1", &value2, sizeof(uint16_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'f1' fail, ret = %d.", ret);
        return ret;
    }
    getObj.f1 = value2;

    uint32_t value3 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "g1", &value3, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g1' fail, ret = %d.", ret);
        return ret;
    }
    getObj.g1 = value3;

    uint64_t value4 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "a2", &value4, sizeof(uint64_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a2' fail, ret = %d.", ret);
        return ret;
    }
    getObj.a2 = value4;

    getObj.a3len = checkObj[0].a3len;
    getObj.a3buf = (char *)malloc(getObj.a3len * sizeof(char));
    if (getObj.a3buf == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    // 获取字符串
    ret = GmcGetVertexPropertyByName(stmt, "a3", getObj.a3buf, getObj.a3len, &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "a4", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4len = length;
    uint8_t *expectBValue = NULL;
    ret = Generate_Bytes(&expectBValue, length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4buf = (uint8_t *)malloc(getObj.a4len + 1);
    if (getObj.a4buf == NULL) {
        free(expectBValue);
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "a4", getObj.a4buf, getObj.a4len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
    if (expectBValue) {
        ret = memcmp((char *)expectBValue, (char *)getObj.a4buf, length);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(expectBValue);
    } else {
        free(expectBValue);
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (NewAllTypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)NewAllTypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[NewAllTypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    // 释放内存
    free(getObj.a3buf);
    free(getObj.a4buf);
    return ret;
}

int NewAllTypeTableCmp1(
    const NewALLTypeTableStruct *st1, const NewALLTypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (st1->d != st2->d) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d, st1: %d, st2: %d.", st1->d, st2->d);
            }
        }

        if (st1->e[0] != st2->e[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] e, st1: %d, st2: %d.", st1->e[0], st2->e[0]);
            }
        }

        if (st1->f[0] != st2->f[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] f, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->g[0] != st2->g[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] g, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->a1 != st2->a1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a1, st1: %d, st2: %d.", st1->a1, st2->a1);
            }
        }

        if (st1->b1 != st2->b1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b1, st1: %d, st2: %d.", st1->b1, st2->b1);
            }
        }

        if (st1->c1 != st2->c1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c1, st1: %d, st2: %d.", st1->c1, st2->c1);
            }
        }

        if (st1->d1 != st2->d1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d1, st1: %d, st2: %d.", st1->d1, st2->d1);
            }
        }

        if (arrycmp(st1->a3buf, st2->a3buf, st1->a3len) != 0) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a3, st1: %s, st2: %s.", st1->a3buf, st2->a3buf);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}
// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte128, g:byte256,uint1-uint8,uint1_8-uint8_64,str,byte
int NewTypeTableGet1(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    NewALLTypeTableStruct *checkObj = (NewALLTypeTableStruct *)t;
    NewALLTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "g", getObj.g, sizeof(getObj.g), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a1, sizeof(getObj.a1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b1", &getObj.b1, sizeof(getObj.b1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c1", &getObj.c1, sizeof(getObj.c1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d1", &getObj.d1, sizeof(getObj.d1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    getObj.a3len = checkObj[0].a3len;
    getObj.a3buf = (char *)malloc(getObj.a3len * sizeof(char));
    if (getObj.a3buf == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    // 获取字符串
    ret = GmcGetVertexPropertyByName(stmt, "a3", getObj.a3buf, getObj.a3len, &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "a4", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4len = length;
    uint8_t *expectBValue = NULL;
    ret = Generate_Bytes(&expectBValue, length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4buf = (uint8_t *)malloc(getObj.a4len + 1);
    if (getObj.a4buf == NULL) {
        free(expectBValue);
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "a4", getObj.a4buf, getObj.a4len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
    if (expectBValue) {
        ret = memcmp((char *)expectBValue, (char *)getObj.a4buf, length);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(expectBValue);
    } else {
        free(expectBValue);
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (NewAllTypeTableCmp1(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)NewAllTypeTableCmp1(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[NewAllTypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    // 释放内存
    free(getObj.a3buf);
    free(getObj.a4buf);
    return ret;
}

// 数据重演
uint32_t dataReplayIncre(GmcConnT *conn, GmcStmtT *stmt, char *labelName, DlrAndSubT subData)
{
    int ret = 0;
    // 重演增量数据
    AW_FUN_Log(LOG_DEBUG, "[INFO]subData.symbolSubDlr: %d", subData.symbolSubDlr);
    for (int i = 0; i < subData.symbolSubDlr; i++) {
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_DLR);  // GMC_BATCH_NORMAL
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLAY);
        if (ret) {
            testGmcGetLastError("Current opeation type is not supported in datalog label, operationType is 20.");
            GmcBatchDestroy(batch);
            return ret;
        }
        EXPECT_EQ(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(stmt, batch, &subData.dlrGetSubBuf[i], &setEof);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "ret is no ok i =%d\n", i);
            testGmcGetLastError();
            GmcBatchDestroy(batch);
            free(subData.dlrGetSubBuf[i].buf);
            return ret;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            testGmcGetLastError();
            GmcBatchDestroy(batch);
            free(subData.dlrGetSubBuf[i].buf);
            AW_FUN_Log(LOG_STEP, "ret is no ok i =%d\n", i);
            return ret;
        }
        EXPECT_EQ(GMERR_OK, ret);
        free(subData.dlrGetSubBuf[i].buf);
        GmcBatchDestroy(batch);
    }
    return GMERR_OK;
}

// keyId删除数据
template <typename StructObjT>
int32_t DeletedataByPk(GmcStmtT *stmt, const char *labelName, StructObjT *t, int objLen)
{
    int32_t ret = 1000;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    NewALLTypeTableStruct *obj = (NewALLTypeTableStruct *)t;
    int32_t upgradeVersion = 0;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &(obj[i].a), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT16, &(obj[i].b), sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &(obj[i].c), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}
#endif /* __STRUCTDATALOGTABLE_H__ */
