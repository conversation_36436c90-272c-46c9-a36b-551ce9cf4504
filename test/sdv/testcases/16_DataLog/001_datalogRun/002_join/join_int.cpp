/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: join_int.cpp
 * Description: 连接-int
 * Author: qinqianbao 995465
 * Create: 2022-08-04
 */
#include "./base_test_include/tools_join_int.h"
#include "t_datacom_lite.h"

using namespace std;

class join_int : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void join_int::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    // 创建连接
    g_conn = NULL;
    g_stmt = NULL;
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    (void)snprintf(errorMsg2, sizeof(errorMsg1), "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    (void)snprintf(errorMsg3, sizeof(errorMsg1), "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void join_int::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) 批量写入A表数据, B表为空
TEST_F(join_int, DataLog_001_001_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_001";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A1";
    char tableB[] = "B1";
    char tableAB[] = "A1B1";
    int32_t count[][4] = {{1, 2, 1, 0}, {2, 1, 2, 0}, {3, 3, -2, 0}};

    // 输入
    ret = batchA(g_stmt, tableA, count, 3);  // B表为空-> out为空
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;  // 输出表记录数
    int32_t result[3][4] = {0};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) A表有数据，批量写入B表且与A_org数据不匹配
TEST_F(join_int, DataLog_001_002_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_002";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A2";
    char tableB[] = "B2";
    char tableAB[] = "A2B2";
    int32_t count1[][4] = {{1, 2, 1, 0}, {1, 1, 2, 0}, {2, 3, -2, 0}};
    int32_t count2[][4] = {{4, 3, 1, 0}, {5, 3, 2, 0}, {6, 3, -2, 0}};

    // 输入后  结果：A/B表数据不匹配-> out为空(A/B的b字段属性值不相等)
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;  // 输出表记录数
    int32_t result[3][4] = {0};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) B_delta表增量记录为+，对应A_org表记录为+(数据匹配)
TEST_F(join_int, DataLog_001_003_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_003";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A3";
    char tableB[] = "B3";
    char tableAB[] = "A3B3";
    int32_t count1[][4] = {{1, 1, 2, 0}, {2, 1, 1, 0}, {2, 2, 3, 0}};
    int32_t count2[][4] = {{1, 2, 2, 0}};

    // 输入后  结果：A/B表数据匹配-> out表输出 A(1 2 +1) join B(2 2 +1) -> 1 2 +1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 2;  // 输出表记录数
    int32_t result[3][4] = {{1, 2, 1, 0}, {2, 2, 1, 0}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) B_delta表增量记录为+，对应A_org表中记录为-
TEST_F(join_int, DataLog_001_004_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_004";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A4";
    char tableB[] = "B4";
    char tableAB[] = "A4B4";
    int32_t count1[][4] = {{1, 2, 1, 0}, {1, 1, 2, 0}, {2, 3, 2, 0}};
    int32_t count2[][4] = {{3, 1, 1, 0}};

    // 输入后  结果：A/B表数据匹配-> out表输出 A(2 3 -2) join B(3 1 +1) -> 2 1 -1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{2, 1, 1, 0}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) B_delta表增量记录为-，对应A_org表中记录为+
TEST_F(join_int, DataLog_001_005_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_005";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A5";
    char tableB[] = "B5";
    char tableAB[] = "A5B5";
    int32_t count1[][4] = {{1, 2, 1, 0}, {1, 1, 2, 0}, {2, 2, 3, 0}};
    int32_t count2[][4] = {{1, 1, 3, 0}};

    // 输入后  结果：A/B表数据匹配-> out表输出 A(1 1 -2) join B(1 3 -1) -> 1 3 -1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{1, 1, 1, 0}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) B_delta表增量记录为-，对应A_org表中记录为-
TEST_F(join_int, DataLog_001_006_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_006";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A6";
    char tableB[] = "B6";
    char tableAB[] = "A6B6";
    int32_t count1[][4] = {{1, 2, 1, 0}, {1, 1, 2, 0}, {2, 3, -2, 0}};
    int32_t count2[][4] = {{3, 2, -2, 0}};

    // 输入后  结果：A/B表数据匹配-> out表输出 A(2 3 -2) join B(3 2 -2) -> 2 2 +1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{2, 2, 1, 0}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) 有多种条路径得到同一条输出记录
// OUT表A/B-join_out，批量写入B表2,1,+2和3,1,+2且A_org中记录4,2,+3;4,3,+3(验证非归一化：4,1,+2)
TEST_F(join_int, DataLog_001_007_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_007";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A7";
    char tableB[] = "B7";
    char tableAB[] = "A7B7";
    int32_t count1[][4] = {{1, 4, 1, 0}, {1, 5, 1, 0}, {2, 3, 2, 0}, {2, 2, 3, 0}};
    int32_t count2[][4] = {{4, 3, 1, 0}, {5, 3, 2, 0}, {3, 2, -2, 0}, {2, 2, 1, 0}};

    // A_or    B_delta
    // 1 4 +1    4 3 +1
    // 1 5 1    5 3 +2
    // 2 3 +2    3 2 -2
    // 2 2 +3    2 2 +1
    // out
    // 1 3 +1
    // 1 3 +1
    // 2 2 -1
    // 2 2 +1
    // 输入后  结果：A/B表数据匹配-> out表输出 1,3,+2 备注：B_delta表有记录抵消
    ret = batchA(g_stmt, tableA, count1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{1, 3, 2, 0}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) OUT表A/B-join_out
// 验证归一化+非归一化
TEST_F(join_int, DataLog_001_008_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_008";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A8";
    char tableB[] = "B8";
    char tableAB[] = "A8B8";
    int32_t count1[][4] = {{0, 0, 2, 0}, {1, 1, 2, 0}, {2, 2, 2, 0}, {3, 3, 3, 0}, {4, 2, 3, 0}, {4, 3, 3, 0}};
    int32_t count2[][4] = {{2, 1, 2, 0}, {3, 1, 2, 0}};

    // 输入后  结果：A/B表数据匹配-> out表输出 2 1 +1、3 1 +1、4 1 +2
    ret = batchA(g_stmt, tableA, count1, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 3;  // 输出表记录数
    int32_t result[3][4] = {{2, 1, 1, 0}, {3, 1, 1, 0}, {4, 1, 2, 0}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) OUT表A/B-join_out 0 0 -1和0 0 -1 -> 0 0 -2
TEST_F(join_int, DataLog_001_009_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_009";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A9";
    char tableB[] = "B9";
    char tableAB[] = "A9B9";
    int32_t count1[][4] = {{0, 1, 1, 0}, {0, 2, 1, 0}};
    int32_t count2[][4] = {{1, 0, -1, 0}, {2, 0, -1, 0}};

    ret = batchA(g_stmt, tableA, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{0, 0, -2, 0}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.(普通自然连接-OUT(a , c) :- A(a , b) , B(b , c)) OUT表A/B-join_out 0 0 -1 then 0 0 +1 -> 最终无记录
TEST_F(join_int, DataLog_001_010_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_010";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A10";
    char tableB[] = "B10";
    char tableAB[] = "A10B10";
    int32_t count1[][4] = {{0, 0, -1}, {1, 1, -1}, {2, 2, -1}, {3, 3, -1}};
    int32_t count2[][4] = {{0, 0, 1}, {1, 1, 1}};
    int32_t count3[][4] = {{0, 0, -1}, {1, 1, -1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 0 0 -1、1 1 -1
    ret = batchA(g_stmt, tableA, count1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 2);  // 输出 0 0 -1 和 1 1 -1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count3, 2);  // 输出0 0 +1 和 1 1 +1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;  // 输出表记录数
    int32_t result[3][4] = {0};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.(过滤条件-OUT(a , b) :- A(a , 2) , B(a , b)) 批量写入A/B表, 数据不满足过滤条件
TEST_F(join_int, DataLog_001_011_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_011";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A11";
    char tableB[] = "B11";
    char tableAB[] = "A11B11";
    int32_t count1[][4] = {{1, 1, 1}, {3, 3, 3}, {4, 4, 4}, {5, 5, 5}, {6, 6, 6}};
    int32_t count2[][4] = {{1, 1, 1}, {3, 3, 3}, {4, 4, 4}, {5, 5, 5}, {6, 6, 6}};

    // 输入后  结果：A/B表数据匹配-> out表输出 空
    ret = batchA(g_stmt, tableA, count1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;  // 输出表记录数
    int32_t result[3][4] = {0};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.(过滤条件-OUT(a , b) :- A(a , 2) , B(a , b)) OUT表A/B-join_out, 批量写入B表记录+, A_org记录count+
TEST_F(join_int, DataLog_001_012_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_012";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A12";
    char tableB[] = "B12";
    char tableAB[] = "A12B12";
    int32_t count1[][4] = {{1, 2, 1}, {1, 1, 2}, {2, 3, -2}};
    int32_t count2[][4] = {{1, 2, 1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 1 2 +1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchA(g_stmt, tableB, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{1, 2, 1}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.(过滤条件-OUT(a , b) :- A(a , 2) , B(a , b)) OUT表A/B-join_out, 批量写入B表记录+, A_org记录count-
TEST_F(join_int, DataLog_001_013_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_013";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A13";
    char tableB[] = "B13";
    char tableAB[] = "A13B13";
    int32_t count1[][4] = {{1, 2, 1}, {1, 1, 2}, {2, 2, -2}};
    int32_t count4[][4] = {{2, 1, 1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 2 1 -1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count4, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{2, 1, -1}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.(过滤条件-OUT(a , b) :- A(a , 2) , B(a , b)) OUT表A/B-join_out, 批量写入B表记录-, A_org记录count+
TEST_F(join_int, DataLog_001_014_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_014";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A14";
    char tableB[] = "B14";
    char tableAB[] = "A14B14";
    int32_t count1[][4] = {{1, 2, 1}, {1, 1, 2}, {4, 2, 2}};
    int32_t count5[][4] = {{4, 3, -1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 4 3 -1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count5, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{4, 3, -1}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.(过滤条件-OUT(a , b) :- A(a , 2) , B(a , b)) OUT表A/B-join_out, 批量写入B表记录-, A_org记录count-
TEST_F(join_int, DataLog_001_015_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_015";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A15";
    char tableB[] = "B15";
    char tableAB[] = "A15B15";
    int32_t count1[][4] = {{1, 2, 1}, {1, 1, 2}, {4, 2, -2}};
    int32_t count2[][4] = {{7, 3, 1}, {5, 3, 2}, {6, 3, -2}};
    int32_t count3[][4] = {{4, 3, -2}};

    // 输入后  结果：A/B表数据匹配-> out表输出 4 3 +1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{4, 3, 1}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.(忽略字段-OUT(a , b) :- A(a , -) , B(a , b)) OUT表A/B-join_out, 批量写入B表记录+, A_org记录count+
TEST_F(join_int, DataLog_001_016_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_016";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A16";
    char tableB[] = "B16";
    char tableAB[] = "A16B16";
    int32_t count1[][4] = {{1, 2, 1}, {1, 1, 2}, {2, 3, -2}};
    int32_t count2[][4] = {{1, 2, 1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 1 2 +2
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{1, 2, 2}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.(忽略字段-OUT(a , b) :- A(a , -) , B(a , b)) OUT表A/B-join_out, 批量写入B表记录+, A_org记录count-
TEST_F(join_int, DataLog_001_017_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_017";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A17";
    char tableB[] = "B17";
    char tableAB[] = "A17B17";
    int32_t count1[][4] = {{1, 2, 1}, {1, 1, 2}, {2, 2, -2}};
    int32_t count2[][4] = {{2, 1, 1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 2 1 -1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{2, 1, -1}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.(忽略字段-OUT(a , b) :- A(a , -) , B(a , b)) OUT表A/B-join_out, 批量写入B表记录-, A_org记录count+
TEST_F(join_int, DataLog_001_018_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_018";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A18";
    char tableB[] = "B18";
    char tableAB[] = "A18B18";
    int32_t count1[][4] = {{1, 2, 1}, {1, 1, 2}, {4, 2, 2}};
    int32_t count2[][4] = {{4, 3, -1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 4 3 -1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{4, 3, -1}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.(忽略字段-OUT(a , b) :- A(a , -) , B(a , b)) OUT表A/B-join_out, 批量写入B表记录-, A_org记录count-
TEST_F(join_int, DataLog_001_019_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_019";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A19";
    char tableB[] = "B19";
    char tableAB[] = "A19B19";
    int32_t count1[][4] = {{1, 2, 1}, {1, 1, 2}, {4, 2, -2}};
    int32_t count2[][4] = {{7, 3, 1}, {5, 3, 2}, {6, 3, -2}};
    int32_t count3[][4] = {{4, 3, -2}};

    // 输入后  结果：A/B表数据匹配-> out表输出 4 3 +1
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{4, 3, 1}};
    // scan
    readOut(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.(综合场景-OUT(a , b) :- A(a , b, -) , B(a , b, 2)) OUT表A/B-join_out, 批量写入B表记录+, A_org记录count+
TEST_F(join_int, DataLog_001_020_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_020";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A20";
    char tableB[] = "B20";
    char tableAB[] = "A20B20";
    int32_t count1[][5] = {{1, 2, 1, 1}, {1, 1, 1, 2}, {2, 3, 1, -2}};
    int32_t count2[][5] = {{1, 1, 2, 1}, {5, 3, 3, 2}, {2, 3, 2, -2}};

    // 输入后  结果：A/B表数据匹配-> out表输出
    ret = batchCompreA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchCompreB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 2;  // 输出表记录数
    int32_t result[3][4] = {{1, 1, 1}, {2, 3, 1}};
    // scan
    readOutA20B20(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.(综合场景-OUT(a , b) :- A(a , b, -) , B(a , b, 2)) OUT表A/B-join_out, 批量写入B表记录+, A_org记录count-
TEST_F(join_int, DataLog_001_021_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_021";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A21";
    char tableB[] = "B21";
    char tableAB[] = "A21B21";
    int32_t count1[][5] = {{1, 2, 1, 1}, {1, 1, 1, -2}, {2, 3, 1, -2}};
    int32_t count2[][5] = {{4, 3, 1, 1}, {1, 1, 1, 2}, {2, 3, 2, -2}};
    int32_t count3[][5] = {{1, 2, 2, 2}};
    int32_t count4[][5] = {{1, 1, 2, 1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 2 1 -1
    ret = batchCompreA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchCompreB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchCompreB(g_stmt, tableB, count3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchCompreB(g_stmt, tableB, count4, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 3;  // 输出表记录数
    int32_t result[3][4] = {{2, 3, 1}, {1, 2, 1}, {1, 1, -1}};
    // scan
    readOutA20B20(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.(综合场景-OUT(a , b) :- A(a , b, -) , B(a , b, 2)) OUT表A/B-join_out, 批量写入B表记录-, A_org记录count+
TEST_F(join_int, DataLog_001_022_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_022";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A22";
    char tableB[] = "B22";
    char tableAB[] = "A22B22";
    int32_t count1[][5] = {{1, 2, 1, 1}, {1, 1, 1, 2}, {4, 2, 1, -2}};
    int32_t count2[][5] = {{1, 2, 2, -1}, {2, 3, 2, 1}, {4, 2, 2, -2}};

    // 输入后  结果：A/B表数据匹配-> out表输出 4 3 -1
    ret = batchCompreA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchCompreB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 2;  // 输出表记录数
    int32_t result[3][4] = {{1, 2, -1}, {4, 2, 1}};
    // scan
    readOutA20B20(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.(综合场景-OUT(a , b) :- A(a , b, -) , B(a , b, 2)) OUT表A/B-join_out, 批量写入B表记录-, A_org记录count-
TEST_F(join_int, DataLog_001_023_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_023";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A23";
    char tableB[] = "B23";
    char tableAB[] = "A23B23";
    int32_t count1[][5] = {{1, 2, 1, 1}, {1, 1, 1, 2}, {4, 2, 1, -2}};
    int32_t count2[][5] = {{7, 3, 1, 1}, {5, 3, 1, 2}, {6, 3, 2, -2}};
    int32_t count3[][5] = {{4, 2, 2, -2}};

    // 输入后  结果：A/B表数据匹配-> out表输出 1 2 +1
    ret = batchCompreA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchCompreB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchCompreB(g_stmt, tableB, count3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 1;  // 输出表记录数
    int32_t result[3][4] = {{4, 2, 1}};
    // scan
    readOutA20B20(g_stmt, tableAB, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.(普通笛卡尔积-out(a,b,c,d) :- A(a , b) , B(c , d)) B表有记录, 批量写入A表(count+)(OUT_count+)
TEST_F(join_int, DataLog_001_024_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_test_024";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A24";
    char tableB[] = "B24";
    char tableAB[] = "A24B24";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, 2}, {3, 3, 3}};
    int32_t count2[][4] = {{1, 1, 1}, {2, 2, 2}, {3, 3, 3}};

    // 输入后  结果：A/B表数据匹配-> out表输出 m*n
    ret = batchProductA24(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchProductB24(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 9;  // 输出表记录数
    int32_t result[9][5] = {{1, 1, 1, 1, 1}, {1, 1, 2, 2, 1}, {1, 1, 3, 3, 1}, {2, 2, 1, 1, 1}, {2, 2, 2, 2, 1},
        {2, 2, 3, 3, 1}, {3, 3, 1, 1, 1}, {3, 3, 2, 2, 1}, {3, 3, 3, 3, 1}};
    // scan
    readProductOut01(g_stmt, tableAB, record);

    ret = CheckDataMatch01(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest01, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.(综合笛卡尔积-out(a,b,c) :- A(a , b, 2) , B(-, 3 , c)) B表有记录, 批量写入A表(count+)(OUT_count+)
TEST_F(join_int, DataLog_001_025_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_test_025";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A25";
    char tableB[] = "B25";
    char tableAB[] = "A25B25";
    int32_t count1[][5] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 2, 1}, {4, 4, 2, 1}};
    int32_t count2[][5] = {{3, 3, 3, 1}, {4, 3, 4, 1}, {5, 5, 1, 1}};

    // 输入后  结果：A/B表数据匹配-> out表输出 m*n
    ret = batchProductA25(g_stmt, tableA, count1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchProductB25(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 6;  // 输出表记录数
    int32_t result[6][4] = {{2, 2, 3, 1}, {2, 2, 4, 1}, {3, 3, 3, 1}, {3, 3, 4, 1}, {4, 4, 3, 1}, {4, 4, 4, 1}};
    // scan
    readProductOut02(g_stmt, tableAB, record);

    ret = CheckDataMatch02(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest02, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.(交换律- A(a, b) join B(b, c)等价于B(b, c) join A(a, b)) 验证输出表的记录数一样
TEST_F(join_int, DataLog_001_026_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_026";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A26";
    char tableB[] = "B26";
    char tableAB[] = "A26B26";
    char tableA1[] = "A261";
    char tableB1[] = "B261";
    char tableA1B1[] = "A261B261";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};
    int32_t count2[][4] = {{3, 4, 1}, {1, 3, -1}, {2, 5, 1}};

    // A26B26
    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // A261B261
    ret = batchB(g_stmt, tableB1, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchA(g_stmt, tableA1, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 3;
    int32_t result[3][4] = {{1, 3, -1}, {2, 5, -1}, {3, 4, 1}};
    // scan
    readOut(g_stmt, tableAB, record);
    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);

    readOut(g_stmt, tableA1B1, record);

    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.(out- A(a, b) join B(b, c) 验证输入表超限(org无数据存在): 写其中一张表数据超限, 查询A/B的org数据
TEST_F(join_int, DataLog_001_027_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_027";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;    char tableA[] = "A27";
    char tableB[] = "B27";
    char tableAB[] = "A27B27";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};
    int32_t count2[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}, {4, 4, 4}};

    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;
    int32_t result[3][4] = {0};
    int recordA = 3;
    int32_t resultA[3][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};  // A表没有超限
    // scan AB
    readOut(g_stmt, tableAB, record);
    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan A
    readOut(g_stmt, tableA, recordA);
    ret = CheckDataMatch(recordA, resultA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    int recordB = 0;
    int32_t resultB[4][4] = {0};
    // scan B
    readOut(g_stmt, tableB, recordB);
    ret = CheckDataMatch(recordB, resultB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.(out- A(a, b) join B(b, c) 验证输入表超限(org有数据): 写其中一张表数据超限, 查询A/B的org数据
TEST_F(join_int, DataLog_001_028_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_028";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A28";
    char tableB[] = "B28";
    char tableAB[] = "A28B28";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};
    int32_t count2[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}};
    int32_t count3[][4] = {{1, 2, 1}};  // 如果这里输入1 1 1：记录数并没有增加, 而是count增加 ->原输入表org 1 1 2

    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB28(g_stmt, tableB, count3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 3;
    int32_t result[3][4] = {{1, 1, 1}, {2, 2, 1}, {3, 3, -1}};
    int recordA = 3;
    int32_t resultA[3][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};
    int recordB = 3;
    int32_t resultB[3][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}};
    // scan AB
    readOut(g_stmt, tableAB, record);
    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan A
    readOut(g_stmt, tableA, recordA);
    ret = CheckDataMatch(recordA, resultA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan B
    readOut(g_stmt, tableB, recordB);
    ret = CheckDataMatch(recordB, resultB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.(out- A(a, b) join B(b, c) 验证输出表超限(org无数据): 两表写数据超限, 查询A/B的org数据
TEST_F(join_int, DataLog_001_029_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_029";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A29";
    char tableB[] = "B29";
    char tableAB[] = "A29B29";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}, {4, 4, -4}};
    int32_t count2[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}, {4, 4, 4}};

    ret = batchA(g_stmt, tableA, count1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 4);  // 在写第二个表的时候才触发规则
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;
    int32_t result[4][4] = {0};
    int recordA = 4;
    int32_t resultA[4][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}, {4, 4, -4}};
    // 当前流控分为输入队列和输出队列，处理时属于两个独立的事务，入列（输出队列）成功事务已提交，无法回滚输入队列的事务。
    int recordB = 0;
    int32_t resultB[4][4] = {0};  // 第二个表的数据被回滚
    // scan AB
    readOut(g_stmt, tableAB, record);
    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan A
    readOut(g_stmt, tableA, recordA);
    ret = CheckDataMatch(recordA, resultA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan B
    readOut(g_stmt, tableB, recordB);
    ret = CheckDataMatch(recordB, resultB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.(out- A(a, b) join B(b, c) 验证输出表超限(org无数据): A表org超限, B表delta正常, 查询A/B的org数据
TEST_F(join_int, DataLog_001_030_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_030";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A30";
    char tableB[] = "B30";
    char tableAB[] = "A30B30";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}, {4, 3, -4}};  // 这里需要注意4 3 -4
    int32_t count2[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}};

    ret = batchA(g_stmt, tableA, count1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 3);  // 回滚B表
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;
    int32_t result[4][4] = {0};
    int recordA = 4;
    int32_t resultA[4][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}, {4, 3, -4}};
    int recordB = 0;
    int32_t resultB[3][4] = {0};  // 第二个表的数据被回滚
    // scan AB
    readOut(g_stmt, tableAB, record);
    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan A
    readOut(g_stmt, tableA, recordA);
    ret = CheckDataMatch(recordA, resultA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan B
    readOut(g_stmt, tableB, recordB);
    ret = CheckDataMatch(recordB, resultB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.(out- A(a, b) join B(b, c) 验证输出表超限(org无数据): A表org正常, B表delta超限, 查询A/B的org数据
TEST_F(join_int, DataLog_001_031_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_031";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A31";
    char tableB[] = "B31";
    char tableAB[] = "A31B31";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};
    int32_t count2[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}, {3, 4, -4}};

    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB28(g_stmt, tableB, count2, 4);  // 回滚B表
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;
    int32_t result[4][4] = {0};
    int recordA = 3;
    int32_t resultA[4][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};
    int recordB = 0;
    int32_t resultB[4][4] = {0};  // 第二个表的数据被回滚
    // scan AB
    readOut(g_stmt, tableAB, record);
    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan A
    readOut(g_stmt, tableA, recordA);
    ret = CheckDataMatch(recordA, resultA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan B
    readOut(g_stmt, tableB, recordB);
    ret = CheckDataMatch(recordB, resultB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.(out- A(a, b) join B(b, c) 验证输出表超限(org有数据): 写AB表, 查询A/B的org数据
TEST_F(join_int, DataLog_001_032_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_032";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A32";
    char tableB[] = "B32";
    char tableAB[] = "A32B32";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};
    int32_t count2[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}};
    int32_t count3[][4] = {{3, 4, -4}};

    ret = batchA(g_stmt, tableA, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB(g_stmt, tableB, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB28(g_stmt, tableB, count3, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 3;
    int32_t result[3][4] = {{1, 1, 1}, {2, 2, 1}, {3, 3, -1}};
    int recordA = 3;
    int32_t resultA[4][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}};
    int recordB = 3;
    int32_t resultB[3][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}};
    // scan AB
    readOut(g_stmt, tableAB, record);
    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan A
    readOut(g_stmt, tableA, recordA);
    ret = CheckDataMatch(recordA, resultA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan B
    readOut(g_stmt, tableB, recordB);
    ret = CheckDataMatch(recordB, resultB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.(out- A(a, b) join B(b, c) 验证输出表超限(org有数据): A表org超限, B表delta超限, 查询A/B的org数据
TEST_F(join_int, DataLog_001_033_joinIntTest)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    char fileName[128] = "join_int_033";
    LoadPrepare(fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(g_soNameTest));
    int ret = 0;
    char tableA[] = "A33";
    char tableB[] = "B33";
    char tableAB[] = "A33B33";
    int32_t count1[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}, {4, 4, 4}};
    int32_t count2[][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, -3}, {4, 4, -4}};

    ret = batchA(g_stmt, tableA, count1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchB33(g_stmt, tableB, count2, 4);  // 回滚B表
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 0;
    int32_t result[4][4] = {0};
    int recordA = 4;
    int32_t resultA[4][4] = {{1, 1, 1}, {2, 2, -2}, {3, 3, 3}, {4, 4, 4}};
    int recordB = 0;
    int32_t resultB[4][4] = {0};
    // scan AB
    readOut(g_stmt, tableAB, record);
    ret = CheckDataMatch(record, result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan A
    readOut(g_stmt, tableA, recordA);
    ret = CheckDataMatch(recordA, resultA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    // scan B
    readOut(g_stmt, tableB, recordB);
    ret = CheckDataMatch(recordB, resultB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 10);
    TestUninstallDatalog(fileName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
