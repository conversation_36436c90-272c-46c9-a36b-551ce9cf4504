/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: rel_case_datalog.h
 * Description: 连接-读写函数
 * Author: qinqianbao 995465
 * Create: 2022-08-04
 */
#ifndef REL_CASE_DATALOG_H
#define REL_CASE_DATALOG_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../struct_base_rel/struct_case_rel.h"

#define MAX_CMD_SIZE 1024
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_soNameTest[MAX_CMD_SIZE] = {0};

void LoadPrepare(char *fileName)
{
    (void)snprintf(g_soNameTest, MAX_CMD_SIZE, "./d_fileRelTest/%s.so", fileName);
}

// 结构化批量写 A表
int batchRelA1(GmcStmtT *stmt, GmcConnT *conn, char *labelName, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A1 objIn = (REL_CASE_A1){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {labelName, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA1Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A表
int batchRelA2(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][5], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A2 objIn = (REL_CASE_A2){0};
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA2Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A3
int batchRelA3(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A3 objIn = (REL_CASE_A3){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA3Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 B3
int batchRelB3(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_B3 objIn = (REL_CASE_B3){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseB3Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A4
int batchRelA4(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][4], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A4 objIn = (REL_CASE_A4){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA4Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 B4
int batchRelB4(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][4], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_B4 objIn = (REL_CASE_B4){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseB4Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A5
int batchRelA5(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A5 objIn = (REL_CASE_A5){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA5Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 B5
int batchRelB5(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_B5 objIn = (REL_CASE_B5){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseB5Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A60
int batchRelA60(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A60 objIn = (REL_CASE_A60){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA60Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    int expectResult = GMERR_OK;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        expectResult = GMERR_LOCK_NOT_AVAILABLE;
    }
    EXPECT_EQ(expectResult, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A61
int batchRelA61(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A61 objIn = (REL_CASE_A61){0};
    // insert

    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA61Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    int expectResult = GMERR_OK;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        expectResult = GMERR_LOCK_NOT_AVAILABLE;
    }
    EXPECT_EQ(expectResult, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A70
int batchRelA70(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][4], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A70 objIn = (REL_CASE_A70){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA70Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    int expectResult = GMERR_OK;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        expectResult = GMERR_LOCK_NOT_AVAILABLE;
    }
    EXPECT_EQ(expectResult, ret);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A71
int batchRelA71(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][4], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A71 objIn = (REL_CASE_A71){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value;
        RelCaseA71Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    int expectResult = GMERR_OK;
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {
        expectResult = GMERR_LOCK_NOT_AVAILABLE;
    }
    EXPECT_EQ(expectResult, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A8
int batchRelA8(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum)
{
    int ret = 0;
    REL_CASE_A8 objIn = (REL_CASE_A8){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        RelCaseA8Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A9
int batchRelA9(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum)
{
    int ret = 0;
    REL_CASE_A9 objIn = (REL_CASE_A9){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        RelCaseA9Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A10
int batchRelA10(GmcStmtT *stmt, GmcConnT *conn, char *label, int32_t count[][3], int dataNum)
{
    int ret = 0;
    REL_CASE_A10 objIn = (REL_CASE_A10){0};
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        RelCaseA10Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批量写 A11
int batchRelA11(GmcStmtT *stmt, char *label, char *outA, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    REL_CASE_A11 objIn = (REL_CASE_A11){0};
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    // prepare
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {label, 0, g_testNameSpace};

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value; // 始终改变第一个字段的值
        RelCaseA11Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}
#endif
