/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"
#define INSERT 0
#define DELETE 1
#define UPDATE 2

// 级联删除function
#pragma pack(1)
typedef struct {
    int32_t count;
    int32_t a;
    int32_t b;
} DEL;
#pragma pack(0)

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[128];
    int8_t g[256];
    int8_t a1;
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[128];
    int8_t g1[256];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[128];
    int8_t g2[256];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[128];
    int8_t g3[256];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int32_t a9;
} TupleA;
#pragma pack(0)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[128];
    int8_t g[256];
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[128];
    int8_t g1[256];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[128];
    int8_t g2[256];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[128];
    int8_t g3[256];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int32_t a9;
    int32_t a1Len;
    char *a1;
} TupleT;
#pragma pack(0)

// TIMEOUT
int32_t dtl_timeout_callback_E000(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple, GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录
    TupleA *input = (TupleA *)timeoutTuple;
    TupleA *output = GmUdfMemAlloc(ctx, sizeof(TupleA));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a * 2;
    output->b = input->b * 2;
    output->c = input->c * 2;
    output->d = input->d + (int64_t)(10 * 60 * 60 * 1000);
    output->dtlReservedCount = 2;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(TupleA);

    return GMERR_OK;
}

// UPDATE BY RANK save delta max  delta值， org表值
int32_t dtl_compare_tuple_H000(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    TupleA *inp1 = (TupleA *)tuple1;
    TupleA *inp2 = (TupleA *)tuple2;
    if (inp1->d < inp2->d) {
        return -1;  // delta save
    } else if (inp1->d > inp2->d) {
        return 1;  // org save
    } else {
        return 0;
    }
}

// UPDATE BY RANK save delta min
int32_t dtl_compare_tuple_I000(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    TupleA *inp1 = (TupleA *)tuple1;
    TupleA *inp2 = (TupleA *)tuple2;
    if (inp1->d < inp2->d) {
        return 1;
    } else if (inp1->d > inp2->d) {
        return -1;
    } else {
        return 0;
    }
}

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_ext_func_init()
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_init.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit()
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_uninit.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_O000(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(
        fp, "[%s] dtl_tbm_tbl_A, op = %d, a = %d, b = %d.\n", __FILE__, op, ((TupleT *)tuple)->a, ((TupleT *)tuple)->b);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_msg_notify_P000(GmMsgNotifyTupleChangeT *tups, uint32_t arrLen)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }
    for (uint32_t i = 0; i < arrLen; ++i) {
        uint32_t op = tups[i].op;
        TupleT *oldTup = (TupleT *)(tups[i].oldTup);
        TupleT *newTup = (TupleT *)(tups[i].newTup);
        if (op == INSERT) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_P000 insert a: %d, b: %d, count: %d, op = %d, \n", __FILE__,
                newTup->a, newTup->b, newTup->dtlReservedCount, tups[i].op);
        } else if (op == DELETE) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_P000 deltete a: %d, b: %d, count: %d, op = %d, \n", __FILE__,
                oldTup->a, oldTup->b, oldTup->dtlReservedCount, tups[i].op);
        } else if (op == UPDATE) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_P000 UPDATE a: %d, b: %d, count: %d, op = %d, \n", __FILE__,
                oldTup->a, oldTup->b, oldTup->dtlReservedCount, tups[i].op);
            (void)fprintf(fp, "[%s] dtl_msg_notify_P000 UPDATE a: %d, b: %d, count: %d, op = %d, \n", __FILE__,
                newTup->a, newTup->b, newTup->dtlReservedCount, tups[i].op);
        }
    }
    (void)fclose(fp);
    return GMERR_OK;
}

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int8_t se4[1];
    int8_t sf4[128];
    int8_t sg4[256];
    int8_t sa5;
    int16_t sb5;
    int32_t sc5;
    int64_t sd5;
    int8_t se5[1];
    int8_t sf5[128];
    int8_t sg5[256];
    int8_t sa6;
    int16_t sb6;
    int32_t sc6;
    int64_t sd6;
    int8_t se6[1];
    int8_t sf6[128];
    int8_t sg6[256];
    int8_t sa7;
    int16_t sb7;
    int32_t sc7;
    int64_t sd7;
    int8_t se7[1];
    int8_t sf7[128];
    int8_t sg7[256];
    int8_t sa8;
    int16_t sb8;
    int32_t sc8;
    int64_t sd8;
    int8_t se8[1];
    int8_t sf8[128];
    int32_t a9;
    int32_t sa9;
} TupleS;
#pragma pack(0)

int32_t dtl_ext_func_tran(void *tuple, GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    TupleS *result = (TupleS *)tuple;
    (void)fprintf(fp, "[%s] dtl_ext_func_tran\n", __FILE__);
    (void)fprintf(fp, "[%s] dtl_ext_func_tran, result->a9 = %d\n", __FILE__, result->a9);
    (void)fclose(fp);
    memcpy(result->se4, result->e4, 1);
    memcpy(result->se5, result->e5, 1);
    memcpy(result->se6, result->e6, 1);
    memcpy(result->se7, result->e7, 1);
    memcpy(result->se8, result->e8, 1);
    memcpy(result->sf4, result->f4, 128);
    memcpy(result->sf5, result->f5, 128);
    memcpy(result->sf6, result->f6, 128);
    memcpy(result->sf7, result->f7, 128);
    memcpy(result->sf8, result->f8, 128);
    memcpy(result->sg4, result->g4, 256);
    memcpy(result->sg5, result->g5, 256);
    memcpy(result->sg6, result->g6, 256);
    memcpy(result->sg7, result->g7, 256);
    result->sa9 = result->a9;
    result->sa5 = result->a5;
    result->sb5 = result->b5;
    result->sc5 = result->c5;
    result->sd5 = result->d5;
    result->sa6 = result->a6;
    result->sb6 = result->b6;
    result->sc6 = result->c6;
    result->sd6 = result->d6;
    result->sa7 = result->a7;
    result->sb7 = result->b7;
    result->sc7 = result->c7;
    result->sd7 = result->d7;
    result->sa8 = result->a8;
    result->sb8 = result->b8;
    result->sc8 = result->c8;
    result->sd8 = result->d8;
    int *p = &result->c5;
    int *s = &result->sc5;

    int *count = &result->dtlReservedCount;
    // 对应的状态变化
    // if p == 0 && count == 1 && s == 1, s = 0;
    // if p == 1 && count == 1 && s == 0, s = 1;
    // if p == 2 && count == 0, init, s = 1, count = 1;
    // if p == 3 && count == 1, delete, count = -1;

    if (*count == 0) {
        if (*p > 0) {
            *s = 1;
            *count = 1;
            return GMERR_OK;
        }
    } else if (*count == 1) {
        if (*s == 1 && *p == 0) {
            *s = 0;
            return GMERR_OK;
        }
        if (*s == 0 && *p == 1) {
            *s = 1;
            return GMERR_OK;
        }
        if (*p == 3) {
            *count = -1;
            return GMERR_OK;
        }
    } else {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_NO_DATA;
}

int32_t dtl_ext_func_funcA022(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA023(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA024(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA025(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA026(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA027(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA028(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA029(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA030(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA050(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA053(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA056(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA059(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA062(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA065(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA068(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA071(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA074(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA077(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA080(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[1];
    int8_t g[1];
    int8_t a1;
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[1];
    int8_t g1[1];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[1];
    int8_t g2[1];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[1];
    int8_t g3[1];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int32_t a9;
} TupleAgg;
#pragma pack(0)

#pragma pack(1)
typedef struct FuncAggIn {
    int32_t count;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int32_t a9;
} FuncAggIn;

typedef struct FuncAggOut {
    int64_t d4;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int32_t a9;
} FuncAggOut;
#pragma pack(0)

int32_t dtl_agg_func_aggA049(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA052(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA055(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA058(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA061(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(FuncAggOut);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return 103011;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return 103020;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA064(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA067(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA070(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA073(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA076(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_aggA079(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));

        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu001(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu002(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu003(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu004(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu005(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu006(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu007(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu008(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu009(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0010(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0011(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0012(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0013(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0014(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0015(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0016(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0017(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0018(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0019(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0020(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0021(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0022(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0023(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0024(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0025(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0026(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0027(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0028(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0029(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0030(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0031(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0032(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0033(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0034(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0035(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0036(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0037(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0038(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0039(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0040(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0041(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0042(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0043(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0044(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0045(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0046(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0047(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0048(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0049(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0050(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0051(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0052(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0053(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0054(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0055(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0056(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0057(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0058(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0059(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0060(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0061(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0062(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0063(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0064(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0065(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0066(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0067(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0068(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0069(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0070(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0071(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0072(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0073(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0074(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0075(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0076(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0077(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0078(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0079(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0080(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0081(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0082(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0083(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0084(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0085(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0086(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0087(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0088(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0089(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0090(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0091(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0092(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0093(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0094(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0095(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0096(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0097(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0098(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu0099(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00100(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00101(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00102(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00103(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00104(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00105(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00106(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00107(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00108(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00109(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00110(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00111(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00112(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00113(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00114(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00115(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00116(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00117(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00118(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00119(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00120(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00121(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00122(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00123(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00124(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00125(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00126(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00127(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00128(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00129(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00130(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00131(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00132(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00133(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00134(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00135(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00136(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00137(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00138(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00139(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00140(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00141(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00142(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00143(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00144(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00145(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00146(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00147(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00148(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00149(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00150(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00151(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00152(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00153(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00154(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00155(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00156(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00157(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00158(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00159(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00160(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00161(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00162(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00163(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00164(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00165(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00166(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00167(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00168(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00169(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00170(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00171(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00172(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00173(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00174(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00175(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00176(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00177(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00178(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00179(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00180(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00181(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00182(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00183(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00184(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00185(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00186(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00187(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00188(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00189(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00190(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00191(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00192(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00193(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00194(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00195(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00196(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00197(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00198(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00199(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00200(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00201(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00202(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00203(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00204(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00205(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00206(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00207(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00208(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00209(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00210(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00211(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00212(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00213(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00214(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00215(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00216(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00217(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00218(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00219(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00220(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00221(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00222(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00223(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00224(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00225(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00226(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00227(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00228(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00229(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00230(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00231(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00232(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00233(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00234(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00235(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00236(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00237(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00238(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00239(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00240(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00241(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00242(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00243(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00244(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00245(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00246(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00247(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00248(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00249(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00250(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00251(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00252(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00253(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00254(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00255(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00256(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00257(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00258(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00259(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00260(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00261(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00262(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00263(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00264(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00265(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00266(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00267(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00268(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00269(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00270(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00271(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00272(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00273(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00274(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00275(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00276(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00277(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00278(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00279(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00280(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00281(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00282(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00283(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00284(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00285(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00286(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00287(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00288(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00289(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00290(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00291(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00292(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00293(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00294(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00295(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00296(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00297(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00298(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00299(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00300(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00301(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00302(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00303(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00304(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00305(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00306(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00307(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00308(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00309(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00310(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00311(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00312(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00313(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00314(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00315(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00316(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00317(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00318(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00319(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00320(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00321(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00322(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00323(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00324(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00325(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00326(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00327(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00328(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00329(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00330(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00331(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00332(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00333(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00334(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00335(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00336(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00337(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00338(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00339(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00340(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00341(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00342(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00343(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00344(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00345(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00346(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00347(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00348(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00349(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00350(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00351(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00352(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00353(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00354(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00355(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00356(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00357(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00358(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00359(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00360(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00361(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00362(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00363(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00364(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00365(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00366(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00367(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00368(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00369(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00370(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00371(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00372(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00373(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00374(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00375(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00376(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00377(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00378(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00379(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00380(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00381(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00382(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00383(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00384(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00385(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00386(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00387(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00388(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00389(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00390(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00391(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00392(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00393(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00394(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00395(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00396(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00397(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00398(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00399(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00400(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00401(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00402(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00403(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00404(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00405(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00406(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00407(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00408(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00409(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00410(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00411(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00412(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00413(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00414(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00415(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00416(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00417(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00418(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00419(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00420(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00421(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00422(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00423(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00424(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00425(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00426(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00427(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00428(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00429(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00430(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00431(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00432(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00433(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00434(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00435(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00436(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00437(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00438(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00439(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00440(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00441(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00442(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00443(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00444(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00445(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00446(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00447(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00448(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00449(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00450(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00451(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00452(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00453(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00454(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00455(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00456(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00457(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00458(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00459(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00460(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00461(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00462(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00463(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00464(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00465(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00466(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00467(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00468(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00469(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00470(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00471(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00472(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00473(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00474(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00475(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00476(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00477(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00478(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00479(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00480(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00481(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00482(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00483(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00484(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00485(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00486(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00487(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00488(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00489(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00490(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00491(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00492(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00493(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00494(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00495(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00496(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00497(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00498(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00499(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00500(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00501(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00502(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00503(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00504(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00505(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00506(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00507(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00508(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00509(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00510(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00511(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00512(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00513(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00514(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00515(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00516(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00517(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00518(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00519(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00520(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00521(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00522(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00523(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00524(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00525(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00526(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00527(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00528(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00529(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00530(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00531(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00532(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00533(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00534(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00535(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00536(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00537(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00538(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00539(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00540(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00541(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00542(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00543(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00544(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00545(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00546(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00547(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00548(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00549(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00550(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00551(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00552(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00553(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00554(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00555(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00556(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00557(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00558(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00559(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00560(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00561(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00562(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00563(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00564(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00565(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00566(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00567(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00568(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00569(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00570(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00571(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00572(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00573(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00574(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00575(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00576(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00577(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00578(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00579(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00580(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00581(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00582(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00583(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00584(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00585(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00586(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00587(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00588(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00589(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00590(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00591(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00592(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00593(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00594(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00595(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00596(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00597(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00598(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00599(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00600(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00601(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00602(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00603(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00604(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00605(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00606(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00607(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00608(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00609(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00610(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00611(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00612(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00613(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00614(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00615(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00616(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00617(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00618(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00619(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00620(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00621(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00622(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00623(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00624(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00625(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00626(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00627(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00628(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00629(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00630(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00631(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00632(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00633(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00634(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00635(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00636(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00637(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00638(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00639(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00640(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00641(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00642(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00643(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00644(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00645(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00646(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00647(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00648(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00649(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00650(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00651(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00652(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00653(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00654(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00655(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00656(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00657(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00658(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00659(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00660(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00661(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00662(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00663(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00664(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00665(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00666(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00667(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00668(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00669(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00670(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00671(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00672(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00673(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00674(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00675(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00676(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00677(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00678(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00679(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00680(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00681(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00682(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00683(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00684(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00685(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00686(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00687(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00688(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00689(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00690(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00691(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00692(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00693(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00694(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00695(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00696(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00697(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00698(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00699(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00700(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00701(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00702(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00703(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00704(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00705(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00706(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00707(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00708(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00709(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00710(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00711(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00712(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00713(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00714(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00715(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00716(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00717(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00718(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00719(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00720(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00721(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00722(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00723(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00724(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00725(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00726(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00727(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00728(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00729(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00730(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00731(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00732(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00733(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00734(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00735(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00736(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00737(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00738(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00739(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00740(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00741(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00742(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00743(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00744(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00745(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00746(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00747(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00748(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00749(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00750(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00751(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00752(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00753(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00754(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00755(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00756(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00757(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00758(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00759(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00760(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00761(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00762(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00763(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00764(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00765(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00766(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00767(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00768(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00769(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00770(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00771(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00772(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00773(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00774(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00775(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00776(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00777(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00778(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00779(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00780(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00781(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00782(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00783(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00784(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00785(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00786(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00787(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00788(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00789(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00790(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00791(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00792(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00793(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00794(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00795(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00796(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00797(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00798(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00799(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00800(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00801(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00802(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00803(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00804(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00805(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00806(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00807(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00808(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00809(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00810(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00811(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00812(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00813(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00814(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00815(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00816(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00817(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00818(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00819(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00820(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00821(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00822(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00823(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00824(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00825(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00826(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00827(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00828(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00829(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00830(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00831(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00832(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00833(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00834(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00835(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00836(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00837(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00838(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00839(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00840(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00841(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00842(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00843(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00844(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00845(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00846(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00847(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00848(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00849(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00850(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00851(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00852(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00853(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00854(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00855(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00856(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00857(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00858(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00859(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00860(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00861(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00862(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00863(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00864(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00865(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00866(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00867(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00868(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00869(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00870(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00871(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00872(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00873(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00874(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00875(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00876(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00877(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00878(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00879(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00880(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00881(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00882(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00883(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00884(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00885(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00886(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00887(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00888(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00889(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00890(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00891(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00892(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00893(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00894(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00895(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00896(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00897(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00898(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00899(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00900(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00901(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00902(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00903(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00904(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00905(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00906(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00907(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00908(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00909(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00910(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00911(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00912(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00913(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00914(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00915(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcRu00916(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_addFunc0(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_addFunc1(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_addFunc2(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_addFunc3(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc4(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc5(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc6(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc7(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc8(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc9(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc10(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_addFunc11(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc12(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc13(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc14(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_addFunc15(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc16(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc17(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc18(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc19(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc20(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc21(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc22(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc23(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc24(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
int32_t dtl_ext_func_addFunc25(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}
