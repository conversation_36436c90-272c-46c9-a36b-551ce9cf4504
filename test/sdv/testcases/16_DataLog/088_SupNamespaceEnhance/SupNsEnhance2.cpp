/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: SupNsEnhance2.cpp
 * Description: Datalog支持多个namespace加固（热补丁升级access_current, access_delta）
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2024-08-30
 */
#include "SupNsEnhance.h"
#include "t_datacom_lite.h"

using namespace std;

class SupNsEnhance2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
#if defined(ENV_RTOSV2X)
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=11,200,300\"");
#endif
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
#if defined(ENV_RTOSV2X)
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
#endif
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SupNsEnhance2::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropNamespace(g_stmt, g_namespace1);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void SupNsEnhance2::TearDown()
{
    int ret;
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 001.创建ns1, block 0, agg(many_to_one), (access_delta和access_current), 进行热补丁升级
TEST_F(SupNsEnhance2, DataLog_088_002_001)
{
    int ret = 0;
    char soName[] = "SupNsEnhance2_001";
    char patchSoName[] = "datalogFile/SupNsEnhance2_001_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_001.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inpA";
    char tableOutput1[] = "outD";
    int32_t startNum = 1;
    int32_t endNum = 2;
    int expectTotalNum = 2;
    int expectsuccessNum = 2;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));

    AW_FUN_Log(LOG_STEP, "结果校验");
    const char *expectResult = R"(
dtlReservedCount = 1, upgradeVersion = 1,a = 2, b = 2
dtlReservedCount = 1, upgradeVersion = 1,a = 1, b = 1
dtlReservedCount = 1, upgradeVersion = 1,a = 2, b = 2
)";
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    char *result = NULL;
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expectResult, result));
    free(result);

    TestUninstallDatalog(soName, g_namespace1);
}

// 002.创建ns1, block 0, agg(many_to_many), (access_delta和access_current), 进行热补丁升级
TEST_F(SupNsEnhance2, DataLog_088_002_002)
{
    int ret = 0;
    char soName[] = "SupNsEnhance2_002";
    char patchSoName[] = "datalogFile/SupNsEnhance2_002_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_002.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inpA";
    char tableOutput1[] = "outD";
    int32_t startNum = 1;
    int32_t endNum = 2;
    int expectTotalNum = 2;
    int expectsuccessNum = 2;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));

    AW_FUN_Log(LOG_STEP, "结果校验");
    const char *expectResult = R"(
dtlReservedCount = 1, upgradeVersion = 1,a = 2, b = 2
dtlReservedCount = 1, upgradeVersion = 1,a = 1, b = 1
dtlReservedCount = 1, upgradeVersion = 1,a = 2, b = 2
)";
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    char *result = NULL;
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expectResult, result));
    free(result);

    TestUninstallDatalog(soName, g_namespace1);
}

// 003.创建ns1, block0 timeout, (access_current), 进行热补丁升级
TEST_F(SupNsEnhance2, DataLog_088_002_003)
{
    int ret = 0;
    char soName[] = "SupNsEnhance2_003";
    char patchSoName[] = "datalogFile/SupNsEnhance2_003_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_003.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inpA1";
    char tableInput2[] = "inpA2";
    int32_t startNum = 1;
    int32_t endNum = 5;
    int expectTotalNum = 5;
    int expectsuccessNum = 5;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA.txt");
    startNum = 1;
    endNum = 1;
    expectTotalNum = 1;
    expectsuccessNum = 1;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);  // 等待数据过期

    AW_FUN_Log(LOG_STEP, "结果校验");
    const char *expectResult = R"(
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 1, b = 1
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 2, b = 2
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 3, b = 3
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 4, b = 4
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 5, b = 5
)";
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    char *result = NULL;
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expectResult, result));
    free(result);

    TestUninstallDatalog(soName, g_namespace1);
}

// 004.创建ns1, block1 timeout, (access_current), 进行热补丁升级
TEST_F(SupNsEnhance2, DataLog_088_002_004)
{
    int ret = 0;
    char soName[] = "SupNsEnhance2_004";
    char patchSoName[] = "datalogFile/SupNsEnhance2_004_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_004.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inpA1";
    char tableInput2[] = "inpA2";
    int32_t startNum = 1;
    int32_t endNum = 5;
    int expectTotalNum = 5;
    int expectsuccessNum = 5;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA.txt");
    startNum = 1;
    endNum = 1;
    expectTotalNum = 1;
    expectsuccessNum = 1;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, false, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);  // 等待数据过期

    AW_FUN_Log(LOG_STEP, "结果校验");
    const char *expectResult = R"(
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 1, b = 1
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 2, b = 2
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 3, b = 3
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 4, b = 4
inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 5, b = 5
)";
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    char *result = NULL;
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expectResult, result));
    free(result);

    TestUninstallDatalog(soName, g_namespace1);
}

// 005.创建ns1, access_current(out1), patch.d显示声明block为1,
// 将已有的可更新输入表和同1个图上的表进行join, 预期编译成功, 执行成功
TEST_F(SupNsEnhance2, DataLog_088_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_005";
    char patchSoName[] = "datalogFile/SupNsEnhance2_005_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_005_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_005.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));

    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));

    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 006.创建ns1, access_current(out1), patch.d显示声明block为1,
// 将已有的可更新输入表和同1个图上的function进行join, 预期编译成功, 执行成功
TEST_F(SupNsEnhance2, DataLog_088_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_006";
    char patchSoName[] = "datalogFile/SupNsEnhance2_006_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_006_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_006.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));

    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));

    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 007.创建ns1, access_delta(out1), patch.d显示声明block为1,
// 将已有的可更新输入表和同1个图上的表进行join, 预期编译成功, 执行成功
TEST_F(SupNsEnhance2, DataLog_088_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_007";
    char patchSoName[] = "datalogFile/SupNsEnhance2_007_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_007_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_007.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));

    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));

    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 008.创建ns1, block为0, enableDML为0, 两个图, 加载升级so重做卡第一个图, 并发写第二个图能够拿到锁（func 3s,
// 写11条数据）
TEST_F(SupNsEnhance2, DataLog_088_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_008";
    char patchSoName[] = "datalogFile/SupNsEnhance2_008_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_008_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_008.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 10;
    int expectTotalNum = 10;
    int expectsuccessNum = 10;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(66);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 1;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 0;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 11;
    endNum = 20;
    expectTotalNum = 10;
    expectsuccessNum = 10;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(66);

    startNum = 1;
    endNum = 20;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        if (i <= 10) {
            ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 009.创建ns1, block为1, enableDML为0, 两个图, 加载升级so重做卡第一个图, 并发写第二个图能够拿到锁（func 3s,
// 写11条数据）
TEST_F(SupNsEnhance2, DataLog_088_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_009";
    char patchSoName[] = "datalogFile/SupNsEnhance2_009_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_009_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_009.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 10;
    int expectTotalNum = 10;
    int expectsuccessNum = 10;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(66);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 插入数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 11;
    endNum = 20;
    expectTotalNum = 10;
    expectsuccessNum = 10;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(66);

    startNum = 1;
    endNum = 20;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        if (i <= 10) {
            ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 010.创建ns1, block为0, lpm索引含namespace, access_current(含lpm索引的表), 热补丁升级, 再进行热补丁降级
TEST_F(SupNsEnhance2, DataLog_088_002_010)
{
    int ret = 0;
    char labelNameA[] = "ns1.A";
    char labelNameB[] = "ns1.B";
    char labelNameC[] = "ns1.C";
    char labelNameD[] = "ns1.D";
    char soName[] = "SupNsEnhance2_010";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_010_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_010_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_010.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    const char *ipAddr1 = "0.0.0.0";
    int32_t destIp1 = LpmTransIp(ipAddr1);
    int32_t vrId = 0;
    int32_t vrfIndex = 0;
    int8_t maskLen = 32;

    for (int i = 1; i <= 1; i++) {
        ret = Lpm4singleRecordInsert(g_stmt, labelNameB, i, vrId, vrfIndex, destIp1 + i, maskLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 1; i <= 1; i++) {
        ret = Lpm4SingleRecordInsertA(g_stmt, labelNameA, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA2.txt");
    ret = executeCommand(g_command,
        "dtlReservedCount = 1, upgradeVersion = 1, id = 1, vr_id = 0, vrf_index = 0, dest_ip_addr = 1, mask_len = 32.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    system("rm -rf /root/HotPatchRCA2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA2.txt");
    ret = executeCommand(g_command,
        "dtlReservedCount = 1, upgradeVersion = 0, id = 1, vr_id = 0, vrf_index = 0, dest_ip_addr = 1, mask_len = 32.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName, g_namespace1);
}

// 011.创建ns1, 通过function(access_current中间表), 将2个图图合成一张图, patch.d显示声明block为1, 修改r0规则
TEST_F(SupNsEnhance2, DataLog_088_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_011";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_011_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_011_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_011.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 012.创建ns1, 通过function(access_delta中间表), 将2个图图合成一张图, patch.d显示声明block为0, 修改r0规则
TEST_F(SupNsEnhance2, DataLog_088_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_012";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_012_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_012_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_012.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 1;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 013.创建ns1, agg(many_to_one)通过access_current输出表out2, 将2个图图合成一张图, patch.d显示声明block为1, 修改r1规则
TEST_F(SupNsEnhance2, DataLog_088_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_013";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_013_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_013_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_013.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 014.创建ns1, agg(many_to_one)通过access_delta输入表mid2, 将2个图图合成一张图, patch.d显示声明block为0, 修改r2规则
TEST_F(SupNsEnhance2, DataLog_088_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_014";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_014_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_014_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_014.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 1;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 015.创建ns1, agg(many_to_many)通过access_current输出表out2, 将2个图图合成一张图, patch.d显示声明block为1, 修改r1规则
TEST_F(SupNsEnhance2, DataLog_088_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_015";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_015_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_015_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_015.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        expectDtlReservedCount = 2;  // 重新写入mid1,count+1
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        expectDtlReservedCount = 2;
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        expectDtlReservedCount = 2;
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 016.创建ns1, agg(many_to_many)通过access_delta输入表inp2, 将2个图图合成一张图, patch.d显示声明block为1, 修改r1规则
TEST_F(SupNsEnhance2, DataLog_088_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_016";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_016_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_016_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_016.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        expectDtlReservedCount = 2;  // 重新写入mid1,count+1
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        expectDtlReservedCount = 2;
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        expectDtlReservedCount = 2;
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 017.创建ns1, block 0, 同一个拓扑图中, 热补丁升级r0规则join r1规则中的输入表, 进行热补丁升级
TEST_F(SupNsEnhance2, DataLog_088_002_017)
{
    int ret = 0;
    char soName[] = "SupNsEnhance2_017";
    char patchSoName[] = "datalogFile/SupNsEnhance2_017_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_017_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_017.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据, 读数据");
    char tableInput1[] = "inpA";
    char tableInput2[] = "inpB";
    char tableInput3[] = "inpC";
    char tableOutput1[] = "outD";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput3, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t dtlReservedCountValue = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput3, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName2, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }

    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput3, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 1;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t dtlReservedCountValue = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput3, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 主键读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName2, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }

    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput3, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t dtlReservedCountValue = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput3, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 018.创建ns1, block 1, 同一个拓扑图中, 热补丁升级r0规则join r1规则中的输入表, 进行热补丁升级
TEST_F(SupNsEnhance2, DataLog_088_002_018)
{
    int ret = 0;
    char soName[] = "SupNsEnhance2_018";
    char patchSoName[] = "datalogFile/SupNsEnhance2_018_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_018_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_018.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据, 读数据");
    char tableInput1[] = "inpA";
    char tableInput2[] = "inpB";
    char tableInput3[] = "inpC";
    char tableOutput1[] = "outD";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput3, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t dtlReservedCountValue = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput3, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName2, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }

    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput3, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t dtlReservedCountValue = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput3, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 主键读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName2, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }

    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput3, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        int32_t dtlReservedCountValue = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput3, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
            dtlReservedCountValue, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 019.创建ns1, block 0, 写数据卡锁（热补丁升级超过1级hung死时间）, 热补丁升级失败(线程1批写数据执行15s,
// 线程2加载升级so)
TEST_F(SupNsEnhance2, DataLog_088_002_019)
{
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_019";
    char patchSoName[] = "datalogFile/SupNsEnhance2_019_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_019_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_019.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, Thread1WriteInput1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    ret = pthread_create(&thrArr[1], NULL, Thread2LoadUpgradeSo, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 2; i++) {
        pthread_join(thrArr[i], NULL);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 020.创建ns1, block 1, 写数据卡锁（热补丁升级超过1级hung死时间）, 热补丁升级成功, 热补丁降级失败
TEST_F(SupNsEnhance2, DataLog_088_002_020)
{
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_020";
    char patchSoName[] = "datalogFile/SupNsEnhance2_020_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_020_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_020.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, Thread1WriteInput1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    ret = pthread_create(&thrArr[1], NULL, Thread3LoadRollbackSo, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 2; i++) {
        pthread_join(thrArr[i], NULL);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 021.创建ns1, 通过function(access_delta)中间表, 将2个图图合成一张图, patch.d显示声明block为1, 修改r1规则
TEST_F(SupNsEnhance2, DataLog_088_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_021";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_021_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_021_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_021.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 022.创建ns1, 通过function(access_delta中间表), 将2个图图合成一张图, patch.d显示声明block为1, 修改r2规则
TEST_F(SupNsEnhance2, DataLog_088_002_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_022";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_022_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_022_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_022.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 023.agg(many_to_one)通过access_current中间表mid2, 将2个图图合成一张图, patch.d显示声明block为1, 修改r2规则
TEST_F(SupNsEnhance2, DataLog_088_002_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_023";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_023_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_023_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_023.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 024.agg(many_to_one)通过access_current中间表mid2, 将2个图图合成一张图, patch.d显示声明block为1, 修改r3规则
TEST_F(SupNsEnhance2, DataLog_088_002_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soName[] = "SupNsEnhance2_024";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_024_rollbackV2.so";
    char patchSoName[] = "datalogFile/SupNsEnhance2_024_patchV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_024.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据, 读数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    char tableInput2[] = "inp2";
    char tableMiddle2[] = "mid2";
    char tableOutput2[] = "out2";
    int32_t startNum = 1;
    int32_t endNum = 100;
    int expectTotalNum = 100;
    int expectsuccessNum = 100;
    int32_t dtlReservedCountValue = 1;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));
    startNum = 101;
    endNum = 200;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 200;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁降级, 写数据, 读数据");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, g_namespace1));
    startNum = 201;
    endNum = 300;
    expectTotalNum = 100;
    expectsuccessNum = 100;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleInsert(g_stmt, g_conn, tableInput2, startNum, endNum, expectTotalNum, expectsuccessNum, GMERR_OK,
        dtlReservedCountValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 300;
    for (int32_t i = startNum; i <= endNum; i++) {
        int32_t upgradeVersionIndex = 0;
        int32_t primaryIndex = i;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMiddle2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName, g_namespace1);
}

// 025.重做dml并发, 重做失败([(长事务监控(默认60s)/事务锁超时时间(默认1s))+2]+1)*事务锁超时时间(默认1s),
// (重做过程让线程2DML拿到锁) 欧拉63s,设备150s
TEST_F(SupNsEnhance2, DataLog_088_002_025)
{
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail -cfgVal 0");

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char const *labelConfig = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    readJanssonFile("schemaFile/out1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    char soName[] = "SupNsEnhance2_025";
    char patchSoName[] = "datalogFile/SupNsEnhance2_025_patchV2.so";
    char rollbackSoName[] = "datalogFile/SupNsEnhance2_025_rollbackV2.so";
    ret = TestLoadDatalog("./datalogFile/SupNsEnhance2_025.so", g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inp1";
    char tableMiddle1[] = "mid1";
    char tableOutput1[] = "out1";
    int32_t startNum = 1;
    int32_t endNum = 5;
    int expectTotalNum = 5;
    int expectsuccessNum = 5;
    ret = batchSingleInsert(g_stmt, g_conn, tableInput1, startNum, endNum, expectTotalNum, expectsuccessNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, g_namespace1));

    usleep(5500 * 1000);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t insertValue = 11;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_outTable, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &insertValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT32, &insertValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#ifdef RUN_INDEPENDENT
    sleep(65);  // 超63秒重做线程抢锁失败, 进入回滚
#else
    sleep(160);  // 超160秒重做线程抢锁失败, 进入回滚
#endif
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(6);  // 已重做完一条数据, 回滚需6秒

    AW_FUN_Log(LOG_STEP, "校验结果");
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f SO_NAME=%s -s %s", g_toolPath, g_viewName2, soName,
        g_connServer);
    ret = executeCommand(g_command, "REDO_FAIL_ROLL_BACK_SUC");
    while (ret != GMERR_OK && tryCount < 5) {
        sleep(1);
        ret = executeCommand(g_command, "REDO_FAIL_ROLL_BACK_SUC");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    TestUninstallDatalog(soName, g_namespace1);
    ret = GmcDropVertexLabel(g_stmt, g_outTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
}
