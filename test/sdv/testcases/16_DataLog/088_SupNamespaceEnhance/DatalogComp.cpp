/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: DatalogComp.cpp
 * Description: .d文件中的namespace与access编译测试
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2024-10-10
 */
#include "t_datacom_lite.h"
#include "SupNsEnhance.h"

using namespace std;

class DatalogComp : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogComp ::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void DatalogComp ::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001.2个namespace中，function(access_current)读另一个ns中普通表，patch.d升级含function的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_001.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_001.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_001_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_001_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.2个namespace中，function(access_delta)写另一个ns中可更新表，patch.d升级含function的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_002.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_002.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_002_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_002_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.多个namespace中，function(access_current)读另一个ns中部分可更新表，patch.d升级含function的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_003.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_003.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_003_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_003_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.多个namespace中，function(access_delta)写另一个ns中表，patch.d升级含function的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_004.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_004.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_004_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_004_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.多个namespace中，function(access_current, access_delta, access_kv)读写另一个ns中全类型表，
// patch.d升级含function的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_005_001.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_005_001.c";
    char oldFile1[1024] = "./datalogFile3/DatalogComp_005_001_rule.d";
    char patchFile1[1024] = "./datalogFile3/DatalogComp_005_001_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile1, patchFile1);
    system(g_command);
    ret = executeCommand(g_command,
        "not allowed to upgrade due to relation \"ns1.mid1\" and state_transfer UDF \"ns5.tran\" in same rule topo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char inputFile2[1024] = "./datalogFile3/DatalogComp_005_002.d";
    char outputFile2[1024] = "./datalogFile3/DatalogComp_005_002.c";
    char oldFile2[1024] = "./datalogFile3/DatalogComp_005_002_rule.d";
    char patchFile2[1024] = "./datalogFile3/DatalogComp_005_002_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile2, outputFile2);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile2, patchFile2);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 006.function(access_current, access_delta)读写另一个ns中表，patch.d升级含function的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_006.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_006.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_006_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_006_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.2个namespace中，aggregate(many_to_one, access_current)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_007.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_007.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_007_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_007_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.多个namespace中，aggregate(many_to_one, access_current)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_008.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_008.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_008_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_008_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.2个namespace中，aggregate(many_to_one, access_delta)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_009.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_009.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_009_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_009_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 010.多个namespace中，aggregate(many_to_one, access_delta)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_010.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_010.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_010_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_010_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 011.多个namespace中，aggregate(many_to_one, access_kv)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_011.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_011.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_011_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_011_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 012.多个namespace中，aggregate(many_to_one，access_current，access_delta，access_kv)读写另一个ns中全类型表，
// patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_012.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_012.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_012_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_012_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 013.aggregate(many_to_one, access_current， access_delta)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_013.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_013.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_013_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_013_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 014.2个namespace中，aggregate(many_to_many, access_current)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_014.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_014.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_014_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_014_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 015.多个namespace中，aggregate(many_to_many, access_current)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_015.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_015.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_015_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_015_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 016.2个namespace中，aggregate(many_to_many, access_delta)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_016.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_016.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_016_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_016_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 017.多个namespace中，aggregate(many_to_many, access_delta)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_017.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_017.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_017_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_017_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 018.多个namespace中，aggregate(many_to_many, access_kv)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_018.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_018.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_018_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_018_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 019.2个namespace中，aggregate(many_to_many, access_current， access_delta)读另一个ns中表，
// patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_019.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_019.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_019_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_019_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 020.aggregate(many_to_many, access_current， access_delta)读另一个ns中表，patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_020.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_020.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_020_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_020_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 021.热补丁新增规则,与transient(finish表进行join)，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_021.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_021.c";
    char oldFile[1024] = "./datalogFile3/DatalogComp_021_rule.d";
    char patchFile[1024] = "./datalogFile3/DatalogComp_021_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "table/function:\"ns2.inp3\" and table/function:\"ns2.out2\" should be in same topo in rule near line 12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 022.过期表(access_current,access_kv)读另一个ns中表，patch.d升级含过期表的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_022_001.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_022_001.c";
    char oldFile1[1024] = "./datalogFile3/DatalogComp_022_001_rule.d";
    char patchFile1[1024] = "./datalogFile3/DatalogComp_022_001_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile1, patchFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char inputFile2[1024] = "./datalogFile3/DatalogComp_022_002.d";
    char outputFile2[1024] = "./datalogFile3/DatalogComp_022_002.c";
    char oldFile2[1024] = "./datalogFile3/DatalogComp_022_002_rule.d";
    char patchFile2[1024] = "./datalogFile3/DatalogComp_022_002_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile2, outputFile2);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile2, patchFile2);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 023.多个namespace覆盖function(access_current, access_delta, access_kv)，
// aggregate(many_to_one, access_current, access_delta, access_kv)，
// aggregate(many_to_many, access_current, access_delta, access_kv)，
// patch.d升级含aggregate的规则，编译patch.d
TEST_F(DatalogComp, DataLog_088_005_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/DatalogComp_023_001.d";
    char outputFile1[1024] = "./datalogFile3/DatalogComp_023_001.c";
    char oldFile1[1024] = "./datalogFile3/DatalogComp_023_001_rule.d";
    char patchFile1[1024] = "./datalogFile3/DatalogComp_023_001_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile1, patchFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char inputFile2[1024] = "./datalogFile3/DatalogComp_023_002.d";
    char outputFile2[1024] = "./datalogFile3/DatalogComp_023_002.c";
    char oldFile2[1024] = "./datalogFile3/DatalogComp_023_002_rule.d";
    char patchFile2[1024] = "./datalogFile3/DatalogComp_023_002_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile2, outputFile2);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile2, patchFile2);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char inputFile3[1024] = "./datalogFile3/DatalogComp_023_003.d";
    char outputFile3[1024] = "./datalogFile3/DatalogComp_023_003.c";
    char oldFile3[1024] = "./datalogFile3/DatalogComp_023_003_rule.d";
    char patchFile3[1024] = "./datalogFile3/DatalogComp_023_003_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile3, outputFile3);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile3, patchFile3);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
