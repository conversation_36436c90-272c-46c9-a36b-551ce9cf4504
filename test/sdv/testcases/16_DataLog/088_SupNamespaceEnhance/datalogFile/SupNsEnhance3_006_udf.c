/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2024-03-04
 */

#include <string.h>
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} B;
#pragma pack(0)

const char *g_logName = "/root/SupNsEnhance3.txt";

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    A *a = (A *)tuple;
    a->b = a->a;

    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }

    // 读current表
    B *input1 = NULL;
    GmUdfReaderT *reader = NULL;
    int ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&input1), ret == GMERR_OK) {
        (void)fprintf(fp, "a = %d, ", ((B *)input1)->a);
        (void)fprintf(fp, "b = %d, ", ((B *)input1)->b);
        (void)fprintf(fp, "dtlReservedCount = %d, ", ((B *)input1)->dtlReservedCount);
        (void)fprintf(fp, "upgradeVersion = %d.\n", ((B *)input1)->upgradeVersion);
    }

    GmUdfDestroyReader(ctx, reader);

    (void)fclose(fp);

    return GMERR_OK;
}
