%table inp1(a12:int1, a22:int2, a32:int4, a42:int8, b12:uint1, b22:uint2, b32:uint4, b42:uint8, c12:byte1, c22:byte2, c32:byte4, c42:byte8,c52:byte16, c62:byte32, c72:byte64, c82:byte128, c92:byte256, c102:byte512,
d9:str, d10:byte){index(0(a12, a22, a32, a42, b12, b22, b32, b42)), update}

%function funcinp1(a12:int1, b12:uint1, c12:byte1 -> ao12:int1, bo12:uint1, co12:byte1)

%table notifytable(a12:int1, a22:int2, a32:int4, a42:int8, b12:uint1, b22:uint2, b32:uint4, b42:uint8, c12:byte1, c22:byte2, c32:byte4, c42:byte8,c52:byte16, c62:byte32, c72:byte64, c82:byte128, c92:byte256, c102:byte512,
d9:str, d10:byte)
{index(0(a12, a22, a32, a42, b12, b22, b32, b42)), notify, update}


notifytable(ao12, a22, a32, a42, bo12, b22, b32, b42, co12, c22, c32, c42, c52, c62, c72, c82, c92, c102, d9, d10) :- inp1(
 a12, a22, a32, a42, b12, b22, b32, b42, c12, c22, c32, c42, c52, c62, c72, c82, c92, c102, d9, d10), funcinp1(a12, b12, c12, ao12, bo12, co12).
