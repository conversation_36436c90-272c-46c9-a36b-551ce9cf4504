/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : datalog 热补丁新增语法语义校验约束(表升级回放)
 Notes        :
 History      :
 Author       : youwanyong ywx1157510
 Modification : 2023/10/10
**************************************************************************** */
#include "DlgSpeCon.h"
using namespace std;

class DlgSpeCon : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void DlgSpeCon::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
}

void DlgSpeCon::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void DlgSpeCon::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void DlgSpeCon::TearDown()
{
    GmcDropVertexLabel(g_stmt, "N000");
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  :   076.表不支持回放：同一条规则不支持新增表join tuple输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTuple_001";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则不支持新增表join tuple输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   077.表不支持回放：同一条规则新增表not join tuple输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTuple_002";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join tuple输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   078.表不支持回放：同一条规则新增function join tuple输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTuple_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "rule r1 should not change struct except add one table, add function or delete function near line 5.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join tuple输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   079.表不支持回放：同一条规则修改规则 tuple为输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTuple_004";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 tuple为输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   080.表不支持回放：同一条规则含tuple输入表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTuple_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含tuple输入表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   081.表不支持回放：同一条规则新增表join finish输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinFinish_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join finish输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   082.表不支持回放：同一条规则新增表not join finish输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinFinish_002";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join finish输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   083.表不支持回放：同一条规则新增function join finish输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinFinish_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join finish输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   084.表不支持回放：同一条规则修改规则 finish为输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinFinish_004";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 finish为输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   085.表不支持回放：同一条规则含finish为输入表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinFinish_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "the old rule \"r1\" should be altered because function \"func\" is modified near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含finish为输入表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   086.表不支持回放：同一条规则新增表join 过期完全可更新输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimeUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimeUpdate_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join 过期完全可更新输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   087.表不支持回放：同一条规则新增表not join过期完全可更新输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimeUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimeUpdate_002";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join过期完全可更新输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   088.表不支持回放：同一条规则新增function join 过期完全可更新输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimeUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimeUpdate_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join 过期完全可更新输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   089.表不支持回放：同一条规则修改规则 过期完全可更新为输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimeUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimeUpdate_004";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 过期完全可更新为输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   090.表不支持回放：同一条规则含过期完全可更新为输入表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimeUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimeUpdate_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含过期完全可更新为输入表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   091.表不支持回放：同一条规则新增表 join 过期部分可更新输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimePartUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimePartUpdate_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表 join 过期部分可更新输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   092.表不支持回放：同一条规则新增表 not join 过期部分可更新输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimePartUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimePartUpdate_002";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表 not join 过期部分可更新输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   093.表不支持回放：同一条规则新增function join过期部分可更新输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimePartUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimePartUpdate_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join过期部分可更新输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   094.表不支持回放：同一条规则修改规则 过期部分更新为输入表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimePartUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimePartUpdate_004";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 过期部分更新为输入表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   095.表不支持回放：同一条规则含过期部分可更新输入表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinTimePartUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinTimePartUpdate_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含过期部分可更新输入表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   096.表不支持回放：同一条规则新增function join tuple中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidTuple_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join tuple中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   097.表不支持回放：同一条规则新增表not join tuple中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidTuple_002";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join tuple中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   098.表不支持回放：同一条规则新增表join tuple中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidTuple_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join tuple中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   099.表不支持回放：同一条规则修改规则 tuple为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidTuple_004";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "function \"func\" is defined but not used in the rules near line 34.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 tuple为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   100.表不支持回放：同一条规则含tuple输入表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidTuple";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidTuple_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含tuple输入表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   101.表不支持回放：同一条规则新增function join finish中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidFinish_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join finish中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   102.表不支持回放：同一条规则新增表not join finish中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidFinish_002";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join finish中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   103.表不支持回放：同一条规则新增表join finish中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidFinish_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "rule r2 should not change struct except add one table, add function or delete function near line 5.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join finish中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   104.表不支持回放：同一条规则修改规则 finish为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidFinish_004";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "function \"func\" is defined but not used in the rules near line 34.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 finish为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   105.表不支持回放：同一条规则含finish中间表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidFinish";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidFinish_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含finish中间表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   106.表不支持回放：同一条规则新增表join pubsub资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinMidPubsub";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidPubsub_001";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "redo was not supported on intermediate table \"K000\", redo intermediate table only "
        "support normal, sequential resource, transient table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join pubsub资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   107.表不支持回放：同一条规则新增表not join pubsub资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinMidPubsub";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidPubsub_002";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "redo was not supported on intermediate table \"K000\", redo intermediate table only support normal, "
        "sequential resource, transient table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join pubsub资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   108.表不支持回放：同一条规则新增function join pubsub资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinMidPubsub";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidPubsub_003";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "redo was not supported on intermediate table \"K000\", redo intermediate table only "
        "support normal, sequential resource, transient table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join pubsub资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   109.表不支持回放：同一条规则修改规则 pubsub资源表为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinMidPubsub";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidPubsub_004";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "redo was not supported on intermediate table \"K000\", redo intermediate table only "
        "support normal, sequential resource, transient table.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 pubsub资源表为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   110.表不支持回放：同一条规则含pubsub资源表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinMidPubsub";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidPubsub_005";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 3.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含pubsub资源表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   111.表不支持回放：同一条规则新增表join filed表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidField";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidField_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join filed表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   112.表不支持回放：同一条规则新增表not join filed表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidField";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidField_002";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join filed表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   113.表不支持回放：同一条规则新增function join filed表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidField";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidField_003";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "rule r2 should not change struct except add one table, add function or delete function near line 5.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join filed表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   114.表不支持回放：同一条规则修改规则 filed为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidField";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidField_004";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "function \"func\" is defined but not used in the rules near line 34.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 filed为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   115.表不支持回放：同一条规则含filed 表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidField";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidField_005";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含filed 表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   116.表不支持回放：同一条规则新增表join 状态表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidState";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidState_001";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "T000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "not allowed to upgrade due to relation \"T000\" and state_transfer UDF \"tran\" in same rule topo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join 状态表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   117.表不支持回放：同一条规则新增表not join 状态表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidState";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidState_002";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "T000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "not allowed to upgrade due to relation \"T000\" and state_transfer UDF \"tran\" in same rule topo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join 状态表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   118.表不支持回放：同一条规则新增function join 状态表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidState";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidState_003";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "T000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "not allowed to upgrade due to relation \"T000\" and state_transfer UDF \"tran\" in same rule topo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join 状态表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   119.表不支持回放：同一条规则修改规则 状态表为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidState";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidState_004";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "T000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "not allowed to upgrade due to relation \"T000\" and state_transfer UDF \"tran\" in same rule topo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 状态表为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   120.表不支持回放：同一条规则含状态表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinMidState";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinMidState_005";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "T000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"tran\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含状态表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   121.表不支持回放：同一条规则新增表join pubsub可更新表为输出表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinOutUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutUpdate_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join pubsub可更新表为输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   122.表不支持回放：同一条规则新增表not join pubsub可更新表为输出表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinOutUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutUpdate_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join pubsub可更新表为输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   123.表不支持回放：同一条规则新增function join pubsub可更新表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinOutUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutUpdate_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "rule r2 should not change struct except add one table, add function or delete function near line 5.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join pubsub可更新表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   124.表不支持回放：同一条规则修改规则 pubsub可更新表为输出表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinOutUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutUpdate_004";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "function \"func\" is defined but not used in the rules near line 34.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 pubsub可更新表为输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   125.表不支持回放：同一条规则含pubsub可更新表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "UpgradeJoinOutUpdate";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutUpdate_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含pubsub可更新表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   126.同一条规则新增表支持join 消息通过表
 Author       : youwanyong
 当前支持2025.5.29 DTS2025052715954
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutMsgNotify";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutMsgNotify_001";

    int32_t ret = 0;

    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表支持回放：同一条规则新增表join 消息通过表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   127.表支持回放：同一条规则新增表not join 消息通知表
 Author       : youwanyong
  当前支持2025.5.29 DTS2025052715954
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutMsgNotify";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutMsgNotify_002";

    int32_t ret = 0;

    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表支持回放：同一条规则新增表not join 消息通知表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   128.表不支持回放：同一条规则新增function join 消通知表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutMsgNotify";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutMsgNotify_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "rule r2 should not change struct except add one table, add function or delete function near line 5.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join 消通知表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   129.表支持回放：同一条规则修改规则 消息通知表为输出表
 Author       : youwanyong
 当前支持2025.5.29 DTS2025052715954
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutMsgNotify";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutMsgNotify_004";

    int32_t ret = 0;

    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表支持回放：同一条规则修改规则 消息通知表为输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   130.表不支持回放：同一条规则含msgnotify表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutMsgNotify";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutMsgNotify_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含msgnotify表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   131.表不支持回放：同一条规则新增表join 外部表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutExternal";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutExternal_001";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表join 外部表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   132.表不支持回放：同一条规则新增表not join 外部表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutExternal";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutExternal_002";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增表not join 外部表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   133.表不支持回放：同一条规则新增function join 外部表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutExternal";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutExternal_003";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "rule r2 should not change struct except add one table, add function or delete function near line 5.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则新增function join 外部表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   134.表不支持回放：同一条规则修改规则 外部表为输出表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutExternal";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutExternal_004";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "function \"func\" is defined but not used in the rules near line 32.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则修改规则 外部表为输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   135.表不支持回放：同一条规则含外部表  修改function实现
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoinOutExternal";
    char upgradeFileName[FILE_PATH] = "UpgradeJoinOutExternal_005";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret =
        executeCommand(command, "unsupported upgrade for function \"func\" , patch op type is \"alter\", near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含外部表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   136.表升级时，所在规则或所在topo图聚合函数中存在access_delta且为不支持回放的表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_136";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_136_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_136_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级时，所在规则或所在topo图聚合函数中存在access_delta且为不支持回放的表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   137.表升级时，所在规则或所在topo图普通function函数中存在access_delta且为不支持回放的表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_137";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A022", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A022", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_137_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_137_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级时，所在规则或所在topo图聚合函数中存在access_delta且为不支持回放的表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   138.表升级时，所在规则或所在topo图状态转移函数中存在access_delta且为不支持回放的表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_138";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_138_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_138_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "not allowed to upgrade due to relation \"T000\" and state_transfer UDF \"tran\" in same rule topo.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级时，所在规则或所在topo图状态转移函数中存在access_delta且为不支持回放的表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   139.表升级时，所在规则或所在topo图聚合函数中存在access_current且为不支持回放的表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_139";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_139_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_139_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级时，所在规则或所在topo图聚合函数中存在access_current且为不支持回放的表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :  140.表升级时，所在规则或所在topo图普通function函数中存在access_current且为不支持回放的表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_140";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A022", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A022", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_140_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_140_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级时，所在规则或所在topo图聚合函数中存在access_current且为不支持回放的表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   141.表升级时，所在规则或所在topo图状态转移函数中存在access_current且为不支持回放的表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_141";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_141_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_141_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "not allowed to upgrade due to relation \"T000\" and state_transfer UDF \"tran\" in same rule topo.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级时，所在规则或所在topo图状态转移函数中存在access_current且为不支持回放的表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   142.新增可更新输入表，相同topo含支持回放的表udf中access_delataaccess_current含可回放的表升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, DISABLED_Datalog_051_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_142";
    char soName1[FILE_PATH] = "Datalog_051_142_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A019", objIn1, recordNum, TupleFullPropertyByte1Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A019", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_142_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_142_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "not allowed to upgrade due to relation \"T000\" and state_transfer UDF \"tran\" in same rule topo.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(
        LOG_STEP, "新增可更新输入表，相同topo含支持回放的表udf中access_delataaccess_current含可回放的表升级成功.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   143.新增function，相同topo含支持回放的表udf中access_delataaccess_current含可回放的表升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_143";
    char soName1[FILE_PATH] = "Datalog_051_143_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A019", objIn1, recordNum, TupleFullPropertyByte1Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A019", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_143_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_143_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "output table:\"A000\" accessed(delta) by func:\"func\" is not supported to redo near old file line 104.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增function，相同topo含支持回放的表udf中access_delataaccess_current含可回放的表升级成功.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   144.修改function实现，相同topo含支持回放的表udf中access_delataaccess_current含可回放的表升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_144";
    char soName1[FILE_PATH] = "Datalog_051_144_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = (i + 1);
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A019", objIn1, recordNum, TupleFullPropertyByte1Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A019", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");
    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_144_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_144_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "output table:\"A000\" accessed(delta) by func:\"func\" is not supported to redo near old file line 104.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(
        LOG_STEP, "修改function实现，相同topo含支持回放的表udf中access_delataaccess_current含可回放的表升级成功.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   145.修改规则括号内内容，相同topo含支持回放的表udf中access_delataaccess_current含可回放的表升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_145";
    char soName1[FILE_PATH] = "Datalog_051_145_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = (i + 1);
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A019", objIn1, recordNum, TupleFullPropertyByte1Set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A019", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");
    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_145_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_145_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command,
        "output table:\"A000\" accessed(delta) by func:\"func\" is not supported to redo near old file line 104.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(
        LOG_STEP, "修改规则括号内内容，相同topo含支持回放的表udf中access_delataaccess_current含可回放的表升级成功.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
