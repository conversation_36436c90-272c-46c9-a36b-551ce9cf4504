/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : datalog 热补丁新增语法语义校验约束(udf升级)
 Notes        :
 History      :
 Author       : youwanyong ywx1157510
 Modification : 2023/10/10
**************************************************************************** */
#include "DlgSpeCon.h"
using namespace std;

class DlgSpeCon : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void DlgSpeCon::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
}

void DlgSpeCon::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void DlgSpeCon::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void DlgSpeCon::TearDown()
{
    GmcDropVertexLabel(g_stmt, "N000");
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    AW_CHECK_LOG_END();
}

class DlgSpeCon2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void DlgSpeCon2::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=4000\"");
#ifdef ENV_RTOSV2X
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxConnNum=115\"");
#endif
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
}

void DlgSpeCon2::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

void DlgSpeCon2::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void DlgSpeCon2::TearDown()
{
    GmcDropVertexLabel(g_stmt, "N000");
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    AW_CHECK_LOG_END();
}


/* ****************************************************************************
 Description  :   241.不支持新增function 变量入参为忽略符
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_241)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_241";
    char upgradeFileName[FILE_PATH] = "Datalog_051_241";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A020", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A020", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "A022", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the field of function \"func\" can not be '-' near line 3.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持新增function 变量入参为忽略符.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   242.不支持新增function 变量出参为常量
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_242)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_242";
    char upgradeFileName[FILE_PATH] = "Datalog_051_242";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A020", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A020", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "A022", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "output field \"11\" of function \"func\" can not be constant value or \"-\" near line 3.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持新增function 变量出参为常量.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   243.不支持新增function 变量出参为忽略符
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_243)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_243";
    char upgradeFileName[FILE_PATH] = "Datalog_051_243";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A020", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A020", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "A022", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the field of function \"func\" can not be '-' near line 3.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持新增function 变量出参为忽略符.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   244.不支持修改聚合规则GROUP-BY字段为常量或忽略符
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_244)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_244";
    char upgradeFileName[FILE_PATH] = "Datalog_051_244";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "A051", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_001_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持修改聚合规则GROUP-BY字段为常量或忽略符.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   245.不支持修改聚合规则聚合函数字段为常量或忽略符
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_245)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_245";
    char upgradeFileName[FILE_PATH] = "Datalog_051_245";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "A051", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_001_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持修改聚合规则GROUP-BY字段为常量或忽略符.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   246.不支持修改聚合规则右表字段为常量或忽略符
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_246)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_246";
    char upgradeFileName[FILE_PATH] = "Datalog_051_246";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "A051", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_001_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持修改聚合规则GROUP-BY字段为常量或忽略符.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   247.不支持增删 聚合规则groupby字段
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_247)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_247";
    char upgradeFileName[FILE_PATH] = "Datalog_051_247";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "A051", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_001_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持修改聚合规则GROUP-BY字段为常量或忽略符.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   248.不支持增删聚合函数出参入参
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_248)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_247";
    char upgradeFileName[FILE_PATH] = "Datalog_051_247";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A049", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "A051", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    (void)sprintf(inputFile2, "./Datalog_File/%s_001_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'aggregate' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持修改聚合规则GROUP-BY字段为常量或忽略符.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   249.不支持修改级联删除表的数量(增加或减少)
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_249)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051_249";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_249_001_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 减少
    ret = executeCommand(command, "altered table 'A022' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    char command1[MAX_CMD_SIZE] = {0};
    char inputFile3[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile4[FILE_PATH] = "./Datalog_File/Datalog_051_249_002_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile3, inputFile4);

    // 增加
    ret = executeCommand(command, "altered table 'A022' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表不支持修改级联删除表的数量(增加或减少).");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   250.不支持修改级联删除表的顺序
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_250)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051_249";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_249_003_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 修改
    ret = executeCommand(command, "altered table 'A022' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持修改级联删除表的数量(增加或减少).");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   251.不支持新增输入表作为级联删除的主表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_251)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_251";
    char nsName[128] = "Datalog_051_251";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_251_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_251_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 修改
    ret = executeCommand(command, "\"G000\" find namespace prefix unsuccessfully in rule near line 5.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "不支持新增输入表作为级联删除的主表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   252.含1023个udf升级新增一个udf升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_252)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_252";
    char soName1[FILE_PATH] = "Datalog_051_252_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 1000;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1023;
        objIn2[i].upgradeVersion = 0;

        ret = writeRecord(g_conn, g_stmt, "A002", objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ret = %d, i = %d\n", ret, i);
        }
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = 1;
    }
    // 加载升级so后后台需要重做数据需要时间
    sleep(200);
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "含1023个udf升级新增一个udf升级成功.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   253.含1024个udf升级新增一个udf升级失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_253)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_253";
    char upgradeFileName[FILE_PATH] = "Datalog_051_253";
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 1000;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1024;
        objIn2[i].upgradeVersion = 0;

        ret = writeRecord(g_conn, g_stmt, "A002", objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "there are 1025 UDFs which exceeds the maximum value 1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "含1024个udf升级新增一个udf升级失败.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   254.含1024个udf升级新增完全可更新输入表含比较函数升级失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_254)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_254";
    char upgradeFileName[FILE_PATH] = "Datalog_051_254";
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 1000;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1024;
        objIn2[i].upgradeVersion = 0;

        ret = writeRecord(g_conn, g_stmt, "A002", objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "there are 1025 UDFs which exceeds the maximum value 1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "含1024个udf升级新增完全可更新输入表含比较函数升级失败.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   255.含1024个udf升级新增部分可更新表含比较函数升级失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_255)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_255";
    char upgradeFileName[FILE_PATH] = "Datalog_051_255";
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 1000;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1024;
        objIn2[i].upgradeVersion = 0;

        ret = writeRecord(g_conn, g_stmt, "A002", objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "there are 1025 UDFs which exceeds the maximum value 1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "含1024个udf升级新增部分可更新表含比较函数升级失败.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   256.含1023个udf升级新增完全可更新输入表含比较函数升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_256)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_256";
    char soName1[FILE_PATH] = "Datalog_051_256_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 1000;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1023;
        objIn2[i].upgradeVersion = 0;

        ret = writeRecord(g_conn, g_stmt, "A002", objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(200);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "A003", objIn2, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = 1;
        objIn2[i].dtlReservedCount = 1023;
    }
    sleep(5);
    // 加载升级so后后台需要重做数据需要时间
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "含1023个udf升级新增完全可更新输入表含比较函数升级成功.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   257.含1023个udf升级新增部分可更新表含比较函数升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_257)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_257";
    char soName1[FILE_PATH] = "Datalog_051_257_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 1000;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1023;
        objIn2[i].upgradeVersion = 0;

        ret = writeRecord(g_conn, g_stmt, "A002", objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(200);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "A003", objIn2, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = 1;
        objIn2[i].dtlReservedCount = 1023;
    }
    sleep(200);
    // 加载升级so后后台需要重做数据需要时间
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "含1023个udf升级新增部分可更新表含比较函数升级成功.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   258.含2000张表新增function升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon2, Datalog_051_258)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_258";
    char soName1[FILE_PATH] = "Datalog_051_258_patchV2";
    AW_ADD_TRUNCATION_WHITE_LIST(1, "GMWARN-0, Operate successfully. Tables have been redo");
    int32_t ret = 0;
    int32_t inpTableNum = 999;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 999;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        if ((i + 2) < 10) {
            (void)sprintf(tableName, "A00%d", (i + 2));
        } else if ((i + 2) < 100) {
            (void)sprintf(tableName, "A0%d", (i + 2));
        } else {
            (void)sprintf(tableName, "A%d", (i + 2));
        }
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
        ret = writeRecord(g_conn, g_stmt, tableName, objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 1;
    }
    // 加载升级so后后台需要重做数据需要时间
    sleep(20);
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "含1000张表新增function升级成功.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   259.含2000张表新增输入表升级失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon2, Datalog_051_259)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_259";
    char upgradeFileName[FILE_PATH] = "Datalog_051_259";
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 999;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        if ((i + 2) < 10) {
            (void)sprintf(tableName, "A00%d", (i + 2));
        } else if ((i + 2) < 100) {
            (void)sprintf(tableName, "A0%d", (i + 2));
        } else {
            (void)sprintf(tableName, "A%d", (i + 2));
        }
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
        ret = writeRecord(g_conn, g_stmt, tableName, objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "there are 2001 tables/resources which exceeds the maximum value 2000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "含2000张表新增function升级成功.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   260.含2000张表修改规则实现升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon2, Datalog_051_260)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_260";
    char soName1[FILE_PATH] = "Datalog_051_260_patchV2";
    AW_ADD_TRUNCATION_WHITE_LIST(1, "GMWARN-0, Operate successfully. Tables have been redo");
    int32_t ret = 0;
    int32_t inpTableNum = 999;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 999;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        if ((i + 2) < 10) {
            (void)sprintf(tableName, "A00%d", (i + 2));
        } else if ((i + 2) < 100) {
            (void)sprintf(tableName, "A0%d", (i + 2));
        } else {
            (void)sprintf(tableName, "A%d", (i + 2));
        }
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
        ret = writeRecord(g_conn, g_stmt, tableName, objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 1;
    }
    sleep(3);
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "含2000张表新增function升级成功.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   261.含1999张表升级新增输入表升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon2, Datalog_051_261)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_261";
    char soName1[FILE_PATH] = "Datalog_051_261_patchV2";
    AW_ADD_TRUNCATION_WHITE_LIST(1, "GMWARN-0, Operate successfully. Tables have been redo");
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 998;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        if ((i + 2) < 10) {
            (void)sprintf(tableName, "A00%d", (i + 2));
        } else if ((i + 2) < 100) {
            (void)sprintf(tableName, "A0%d", (i + 2));
        } else {
            (void)sprintf(tableName, "A%d", (i + 2));
        }
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
        ret = writeRecord(g_conn, g_stmt, tableName, objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = 1;
    }
    sleep(5);
    ret = writeRecord(g_conn, g_stmt, "A1000", objIn2, 1, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "含1999张表升级新增输入表升级成功.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   262.含1999张表升级新增2张输入表升级失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon2, Datalog_051_262)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_262";
    char upgradeFileName[FILE_PATH] = "Datalog_051_262";
    int32_t ret = 0;


    // 1.编译加载原so
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 998;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    TupleFullPropertyByte1 *objIn2 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    char tableName[6] = "A000";
    for (int i = 0; i < recordNum; i++) {
        if ((i + 2) < 10) {
            (void)sprintf(tableName, "A00%d", (i + 2));
        } else if ((i + 2) < 100) {
            (void)sprintf(tableName, "A0%d", (i + 2));
        } else {
            (void)sprintf(tableName, "A%d", (i + 2));
        }
        objIn1[0].a = (i + 1) % 128;
        objIn1[0].b = (i + 1) % 32768;
        objIn1[0].c = (i + 1) % 10;
        objIn1[0].d = i + 1;
        objIn1[0].e[0] = 0;
        objIn1[0].f[0] = 0;
        objIn1[0].g[0] = 0;
        objIn1[0].dtlReservedCount = 1;
        objIn1[0].upgradeVersion = 0;

        objIn2[i].a = (i + 1) % 128;
        objIn2[i].b = (i + 1) % 32768;
        objIn2[i].c = (i + 1) % 10;
        objIn2[i].d = i + 1;
        objIn2[i].e[0] = 0;
        objIn2[i].f[0] = 0;
        objIn2[i].g[0] = 0;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
        ret = writeRecord(g_conn, g_stmt, tableName, objIn1, 1, TupleFullPropertyByte1Set, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A001", objIn2, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "there are 2001 tables/resources which exceeds the maximum value 2000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "含1999张表升级新增2张输入表升级失败.");
    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   0263.表不支持回放：不同规则  其中一条含function，修改规则，使就规则中表join function
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_0263)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "UpgradeJoin";
    char upgradeFileName[FILE_PATH] = "UpgradeJoin";

    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", upgradeFileName);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "rule \"r0\" should add updatable input table or new function to join near line 5.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "表不支持回放：同一条规则含finish为输入表  修改function实现.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
