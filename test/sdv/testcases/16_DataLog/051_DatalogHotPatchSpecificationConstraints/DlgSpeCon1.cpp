/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : datalog 热补丁新增语法语义校验约束(新增表升级)
 Notes        :
 History      :
 Author       : youwanyong ywx1157510
 Modification : 2023/10/10
**************************************************************************** */
#include "DlgSpeCon.h"
using namespace std;

class DlgSpeCon : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void DlgSpeCon::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
}

void DlgSpeCon::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void DlgSpeCon::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void DlgSpeCon::TearDown()
{
    GmcDropVertexLabel(g_stmt, "N000");
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  :   001.表升级不支持新增finish表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_001_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the upgraded table \"FinishD000\" should be input updatable table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增finish表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   002.表升级不支持新增普通输入表（不含任何关键字）
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_002_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the upgraded table \"FinishD000\" should be input updatable table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增普通输入表（不含任何关键字）.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   003.表升级不支持新增普通表（含过期字段）
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_003_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the upgraded table \"FinishD000\" should be input updatable table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增普通表（含过期字段）.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   004.表升级不支持新增tuple表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_004_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the upgraded table \"FinishD000\" should be input updatable table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增tuple表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   005.表升级不支持新增完全可更新表(带过期字段)
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_005_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "upgrade table \"FinishD000\" should not be timeout table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增完全可更新表(带过期字段).");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   006.表升级不支持新增部分可更新表(带过期字段)
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_006_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "upgrade table \"FinishD000\" should not be timeout table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增部分可更新表(带过期字段).");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   007.新增完全可更新表作为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_007_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "updatable table \"FinishD000\" should not be an intermediate table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增完全可更新表作为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   008.新增部分可更新表作为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_008_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "updatable table \"FinishD000\" should not be an intermediate table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增部分可更新表作为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   009.新增完全可更新表不显示声明主键索引
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_009_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "index0 should be defined explicitly for table \"FinishD000\" near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增完全可更新表不显示声明主键索引");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   010.新增部分可更新表不显示声明主键索引
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051";
    char nsName[128] = "Datalog_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_010_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "index0 should be defined explicitly for table \"FinishD000\" near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增部分可更新表不显示声明主键索引.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   011.新增完全可更新表显示声明不连续主键索引升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, DISABLED_Datalog_051_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_011";
    char soName1[FILE_PATH] = "Datalog_051_011_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    ret = writeRecord(g_conn, g_stmt, "FinishD000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    sleep(5);
    ret = readRecord(g_conn, g_stmt, "FinishD000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "新增完全可更新表显示声明不连续主键索引升级成功.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   012.新增部分可更新表显示声明不连续主键索引升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, DISABLED_Datalog_051_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_012";
    char soName1[FILE_PATH] = "Datalog_051_012_patchV2";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    ret = writeRecord(g_conn, g_stmt, "FinishD000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    sleep(2);
    ret = readRecord(g_conn, g_stmt, "FinishD000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "新增完全可更新表显示声明不连续主键索引升级成功.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   013.新增可更新表含比较函数，udf.c中未声明
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_013";
    char soName1[FILE_PATH] = "Datalog_051_013_patchV2";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./Datalog_File/%s.so", g_toolPath, soName1);

    // 数字
    ret =
        executeCommand(command, "find function's address, name=dtl_compare_tuple_FinishD000. ret = 1017001.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }
    AW_FUN_Log(LOG_STEP, "新增可更新表含比较函数，udf.c中未声明.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   014.新增可更新表但规则未使用
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_014";
    char nsName[128] = "Datalog_051_014";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_014_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_014_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "table \"FinishD000\" is defined but not used in the rules near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增可更新表但规则未使用");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   015.新增可更新表规则右表存在重复字段
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_015";
    char nsName[128] = "Datalog_051_015";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_015_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_015_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "relation \"FinishD000\" has duplicate field \"a\" near line 8.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增可更新表规则右表存在重复字段.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   016.表升级支持新增普通表为中间表（不含任何关键字)
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_016";
    char nsName[128] = "Datalog_051_016";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_016_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_016_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级支持新增普通表为中间表（不含任何关键字).");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   017.表升级支持新增tuple表为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_017";
    char nsName[128] = "Datalog_051_017";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_017_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_017_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级支持新增tuple表为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   018.表升级支持新增transient filed表为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_018";
    char nsName[128] = "Datalog_051_018";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_018_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_018_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级支持新增transient filed表为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   019.表升级支持新增finish表为中间表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_019";
    char nsName[128] = "Datalog_051_019";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_019_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_019_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级支持新增finish表为中间表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   020.表升级不支持新增固定资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_051_020";
    char nsName[128] = "Datalog_051_020";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_020_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_020_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "resource \"J1000\" can not be allowed to upgrade near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增固定资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   021.表升级不支持新增pubsub资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_051_021";
    char nsName[128] = "Datalog_051_021";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_021_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_021_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "resource \"K1000\" can not be allowed to upgrade near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增pubsub资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   022.表升级不支持新增状态表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_022";
    char nsName[128] = "Datalog_051_022";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "T000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_022_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_022_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the upgraded table \"R0001\" should be input updatable table near line 3.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增状态表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   023.输出表表升级支持新增完全可更新表不含比较函数
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_023";
    char nsName[128] = "Datalog_051_023";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_023_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_023_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持新增完全可更新表不含比较函数.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   024.输出表表升级支持新增pubsub表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_024";
    char nsName[128] = "Datalog_051_024";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_024_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_024_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增pubsub表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   025.输出表表升级支持新增普通输出表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_025";
    char nsName[128] = "Datalog_051_025";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_025_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_025_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级支持新增普通输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   026.输出表表升级不支持新增消息通知表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_026";
    char nsName[128] = "Datalog_051_026";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_026_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_026_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the upgraded table \"FinishD000\" should be input updatable table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增消息通知表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   027.输出表表升级支持新增tbm表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_027";
    char nsName[128] = "Datalog_051_027";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_027_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_027_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级支持新增tbm表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   028.输出表表升级支持新增外部表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_028";
    char nsName[128] = "Datalog_051_028";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_028_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_028_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级支持新增外部表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   029.输出表新增部分可更新表作为输出表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_029";
    char nsName[128] = "Datalog_051_029";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_023_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_023_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表新增部分可更新表作为输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   030.新增表与旧.d中表同名升级失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_030";
    char nsName[128] = "Datalog_051_030";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_030_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_030_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "the upgraded table \"C000\" is defined in old file near line 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增部分可更新表作为输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   031.新增表join时作为右表第一张表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_031";
    char nsName[128] = "Datalog_051_031";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_031_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_031_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "rule r2 should not change struct except add one table, add function or delete function near line 7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增表join时作为右表第一张表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   032.新增表时notjoin 作为右表第一张表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_032";
    char nsName[128] = "Datalog_051_032";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_032_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_032_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(
        command, "rule r2 should not change struct except add one table, add function or delete function near line 7.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "新增表时notjoin 作为右表第一张表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   033.表升级不支持删除finish表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_033";
    char nsName[128] = "Datalog_051_033";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_033_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_033_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"Finish000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除finish表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   034.表升级不支持删除普通输入表不含任何关键字
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_034";
    char nsName[128] = "Datalog_051_034";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_034_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_034_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"Finish000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除普通输入表不含任何关键字.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   035.表升级不支持删除过期表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_035";
    char nsName[128] = "Datalog_051_035";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_035_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_035_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"Finish000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除过期表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   036.表升级不支持删除tuple表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_036";
    char nsName[128] = "Datalog_051_036";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_036_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_036_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"Finish000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除tuple表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   037.表升级不支持删除完全可更新表带过期字段
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_037";
    char nsName[128] = "Datalog_051_037";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_037_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_037_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"Finish000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除完全可更新表带过期字段.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   038.表升级不支持删除部分可更新表带过期字段
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_038";
    char nsName[128] = "Datalog_051_038";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_038_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_038_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"Finish000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除部分可更新表带过期字段.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   039.表升级不支持删除完全可更新表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_039";
    char nsName[128] = "Datalog_051_039";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_039_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_039_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"Finish000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除完全可更新表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   040.表升级不支持删除部分可更新表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_040";
    char nsName[128] = "Datalog_051_040";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_040_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_040_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"Finish000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除部分可更新表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   041.中间表表升级不支持删除普通表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_041";
    char nsName[128] = "Datalog_051_041";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_041_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_041_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"B000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除普通表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   042.中间表表升级不支持删除tuple表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_042";
    char nsName[128] = "Datalog_051_042";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_042_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_042_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"B000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持删除tuple表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   043.中间表表升级不支持删除transient filed表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_043";
    char nsName[128] = "Datalog_051_043";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_043_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_043_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"B000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持删除transient filed表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  044.中间表表升级不支持删除finish表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_044";
    char nsName[128] = "Datalog_051_044";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_044_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_044_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"B000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持删除finish表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  045.中间表表升级不支持删除固定资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_051_045";
    char nsName[128] = "Datalog_051_045";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_045_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_045_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'resource' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持删除固定资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  046.中间表表升级不支持删除pubsub资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_051_046";
    char nsName[128] = "Datalog_051_046";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_046_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_046_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'resource' near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持删除固定资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  047.中间表表升级不支持删除状态表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_047";
    char nsName[128] = "Datalog_051_047";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "T000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_047_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_047_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"S000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增状态表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :  048.输出表表升级不支持删除完全可更新表不含比较函数
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_048";
    char nsName[128] = "Datalog_051_048";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_048_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_048_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"A000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持删除完全可更新表不含比较函数.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  049.输出表表升级不支持删除pubsub表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_051_049";
    char nsName[128] = "Datalog_051_049";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_049_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_049_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"A000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持删除pubsub表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  050.输出表表升级不支持删除普通输出表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_050";
    char nsName[128] = "Datalog_051_050";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_050_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_050_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"A000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持删除普通输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  051.输出表表升级不支持删除消息通知表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_051";
    char nsName[128] = "Datalog_051_051";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_051_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_051_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"A000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持删除消息通知表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  052.输出表表升级不支持删除tbm表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_052";
    char nsName[128] = "Datalog_051_052";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_052_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_052_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"A000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持删除tbm表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  053.输出表表升级不支持删除外部表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_053";
    char nsName[128] = "Datalog_051_053";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_053_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_053_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "drop table \"N000\" is not supported in delta datalog file near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持删除外部表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  054.表升级不支持修改输入表finish表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_054";
    char nsName[128] = "Datalog_051_054";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_054_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_054_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'Finish000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持修改输入表finish表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :  055.表升级不支持修改输入表普通输入表不含任何关键字
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_055";
    char nsName[128] = "Datalog_051_055";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_055_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_055_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'Finish000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持修改输入表普通输入表不含任何关键字.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  056.表升级不支持修改输入表过期表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_056";
    char nsName[128] = "Datalog_051_056";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_056_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_056_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'Finish000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持修改输入表过期表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  057.表升级不支持修改输入表tuple表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_057";
    char nsName[128] = "Datalog_051_057";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_057_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_057_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'Finish000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持修改输入表tuple表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  058.表升级不支持修改输入表完全可更新表带过期字段
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_058";
    char nsName[128] = "Datalog_051_058";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_058_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_058_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'Finish000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持修改输入表完全可更新表带过期字段.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  059.表升级不支持修改输入表部分可更新表带过期字段
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_059";
    char nsName[128] = "Datalog_051_059";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_059_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_059_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'Finish000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持修改输入表部分可更新表带过期字段.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  060.表升级不支持修改输入表部分可更新表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_060";
    char nsName[128] = "Datalog_051_060";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_060_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_060_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'Finish000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持修改输入表部分可更新表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  061.表升级不支持修改输入表完全可更新表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_061";
    char nsName[128] = "Datalog_051_061";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_061_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_061_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'Finish000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持修改输入表完全可更新表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  062.中间表表升级不支持修改中间表普通表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_062";
    char nsName[128] = "Datalog_051_062";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_062_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_062_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'B000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持修改中间表普通表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  063.中间表表升级不支持修改中间表tuple表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_063";
    char nsName[128] = "Datalog_051_063";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_063_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_063_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'B000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持修改中间表普通表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  064.中间表表升级不支持修改中间表transient filed表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_064";
    char nsName[128] = "Datalog_051_064";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_064_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_064_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'B000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持修改中间表transient filed表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  065.中间表表升级不支持修改中间表finish表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_065";
    char nsName[128] = "Datalog_051_065";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_065_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_065_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'B000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持修改中间表finish表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  066.中间表表升级不支持修改中间表固定资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_051_066";
    char nsName[128] = "Datalog_051_066";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_066_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_066_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, " altered table 'J000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持修改中间表固定资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  067.中间表表升级不支持修改中间表pubsub资源表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_051_067";
    char nsName[128] = "Datalog_051_067";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_pubsubResource = true;
    ret = writeRecord(g_conn, g_stmt, "L000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "M000", objIn1, recordNum, TupleFullPropertyByte1Get, false, true, NULL, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_067_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_067_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'K000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "中间表表升级不支持修改中间表pubsub资源表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  068.中间表表升级不支持修改中间表状态表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_068";
    char nsName[128] = "Datalog_051_068";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret =
        writeRecord(g_conn, g_stmt, "S000", objIn1, recordNum, TupleFullPropertyByte1Set, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "T000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_068_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_068_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'R000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "表升级不支持新增状态表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  069.输出表表升级不支持修改输出表完全可更新表不含比较函数
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_069";
    char nsName[128] = "Datalog_051_069";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_069_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_069_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'A000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持修改输出表完全可更新表不含比较函数.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  070.输出表表升级不支持修改输出表pubsub表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_051_070";
    char nsName[128] = "Datalog_051_070";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_070_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_070_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'A000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持修改输出表pubsub表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  071.输出表表升级不支持修改输出表普通输出表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_071";
    char nsName[128] = "Datalog_051_071";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "A000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_071_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_071_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'A000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持修改输出表普通输出表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  072.输出表表升级不支持修改输出表消息通知表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_072";
    char nsName[128] = "Datalog_051_072";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_072_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_072_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'A000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持修改输出表消息通知表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  073.输出表表升级不支持修改输出表tbm表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_073";
    char nsName[128] = "Datalog_051_073";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_073_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_073_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'A000' can should not change tbm table type near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持修改输出表tbm表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  074.输出表表升级不支持修改输出表外部表
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_074";
    char nsName[128] = "Datalog_051_074";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 10;
    TupleFullPropertyByte1 *objIn1 = (TupleFullPropertyByte1 *)malloc(sizeof(TupleFullPropertyByte1) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 128;
        objIn1[i].b = (i + 1) % 32768;
        objIn1[i].c = i + 1;
        objIn1[i].d = 10000;
        objIn1[i].e[0] = 0;
        objIn1[i].f[0] = 0;
        objIn1[i].g[0] = 0;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "Finish000", objIn1, recordNum, TupleFullPropertyByte1Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.so内写入数据并校验.");

    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_074_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_074_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'N000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "输出表表升级不支持修改输出表外部表.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  075.旧.d声明全部字段为主键，增量.d隐式声明全部字段为主键预期升级失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(DlgSpeCon, Datalog_051_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_051_075";
    char nsName[128] = "Datalog_051_075";
    int32_t ret = 0;


    // 1.编译加载原so
    DatalogCreateExternalTable();

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载原so.");

    // 2.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "./Datalog_File/Datalog_051_075_rule.d";
    char inputFile2[FILE_PATH] = "./Datalog_File/Datalog_051_075_patch.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 数字
    ret = executeCommand(command, "altered table 'A000' can only be tbm table near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    AW_FUN_Log(LOG_STEP, "旧.d声明全部字段为主键,增量.d隐式声明全部字段为主键预期升级失败.");

    AW_FUN_Log(LOG_STEP, "3.编译加载升级so.");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
