/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: DlgSpeCon.h
 * Description: Datalog HotPatch Specification Constraints
 * Author: youwanyong ywx1157510
 * Create: 2023-10-10
 */

#ifndef __DLGSPECON_H__
#define __DLGSPECON_H__
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "DatalogRun.h"
#include "StructDatalogTable.h"

#define MAX_NAME_LENGTH 512
#define FILE_PATH 512
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
bool g_pubsubResource = false;
char g_outputDir[FILE_PATH] = "Datalog_File";

typedef enum {
    PKONEDELETE,  // 主键字段为一个字段
    PKTWODELETE,  // 主键字段为二个字段
    SECDELETE,    // 二级索引删除
} DELETETYPE;

// 创建外部表
void DatalogCreateExternalTable()
{
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./Datalog_File/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    int32_t ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
}

// 加载so文件
int LoadSoFile(const char *soName)
{
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, FILE_PATH, "./%s/%s.so", g_outputDir, soName);
    return TestLoadDatalog(g_command);
}

// 加载升级的so
int TestLoadUpgradeDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(loadCommand, commandSize, "%s/gmimport -s %s -c datalog -upgrade ./Datalog_File/%s.so", g_toolPath,
        g_connServer, soName);

    system(loadCommand);
    return 0;
}

int BatchInsertByte256(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false, int32_t begin = 0)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = begin; i < dataNum; i++) {
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[2] = {0};
        uint8_t value7[2] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};
        int stringLen = 10;
        // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, 0, stringLen + 1);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // b1
        ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e2", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g2", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e3", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g3:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a4
        ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d4
        ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e4:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e4", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f4:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g4:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g4", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a5
        ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b5
        ret = GmcSetVertexProperty(stmt, "b5", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c5
        ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d5
        ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e5:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e5", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f5:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g5:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g5", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a6
        ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b6
        ret = GmcSetVertexProperty(stmt, "b6", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c6
        ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d6
        ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e6:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e6", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f6:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g6:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g6", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a7
        ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b7
        ret = GmcSetVertexProperty(stmt, "b7", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c7
        ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d7
        ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e7:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e7", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f7:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g7:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g7", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a8
        ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b8
        ret = GmcSetVertexProperty(stmt, "b8", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c8
        ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d8
        ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e8:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e8", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f8:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a9
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(buf);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_OUT_OF_MEMORY) {
        result = GMERR_OUT_OF_MEMORY;
    }
    if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        result = ret;
    }
    AW_MACRO_EXPECT_EQ_INT(result, ret);
    if (ret) {
        testGmcGetLastError();
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int BatchDeleteByte256(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false, DELETETYPE Deletetype = PKTWODELETE)
{
    int ret = 0;

    for (int i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[1] = {0};
        uint8_t value7[1] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};
        int stringLen = 10;
        // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, 0, stringLen + 1);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        if (Deletetype == SECDELETE) {
            ret = GmcSetIndexKeyId(stmt, 4);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            value5[0] = count[i][4];
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, value5, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else if (Deletetype == PKONEDELETE) {
            ret = GmcSetIndexKeyId(stmt, 0);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // upgradeVersion
            int32_t upgradeVersion = 0;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyId(stmt, 0);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // upgradeVersion
            int32_t upgradeVersion = 0;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            testGmcGetLastError();
            free(buf);
            break;
        }

        free(buf);
    }
    return ret;
}

int BatchInsertByteOne(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false, int32_t begin = 0)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int stringLen = 10;
    // string
    uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
    if (buf == NULL) {
        return -1;
    }
    memset(buf, 0, stringLen + 1);

    for (int i = begin; i < dataNum; i++) {
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[1] = {0};
        uint8_t value7[1] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};

        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // b1
        ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte1
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte1
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e2", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte1
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte1
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g2", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e3", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte1
        value6[0] = count[i][5];
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value8, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value6, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        //  g3:byte1
        value7[0] = count[i][6];
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value9, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value7, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // a4
        ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d4
        if (strstr(labelName, "S000") != NULL) {
        } else {
            ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        //  e4:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e4", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f4:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g4:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g4", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a5
        ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b5
        ret = GmcSetVertexProperty(stmt, "b5", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c5
        ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d5
        ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e5:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e5", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f5:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g5:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g5", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a6
        ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b6
        ret = GmcSetVertexProperty(stmt, "b6", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c6
        ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d6
        ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e6:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e6", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f6:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g6:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g6", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a7
        ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b7
        ret = GmcSetVertexProperty(stmt, "b7", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c7
        ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d7
        ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e7:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e7", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f7:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g7:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g7", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a8
        ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b8
        ret = GmcSetVertexProperty(stmt, "b8", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c8
        ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d8
        ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e8:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e8", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f8:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a9
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_OUT_OF_MEMORY) {
        result = GMERR_OUT_OF_MEMORY;
    }
    AW_MACRO_EXPECT_EQ_INT(result, ret);
    if (ret && ret != GMERR_OUT_OF_MEMORY) {
        testGmcGetLastError();
    }
    free(buf);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}
// delete根据主键删除
int BatchDeleteByteOne(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false)
{
    int ret = 0;

    for (int i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[1] = {0};
        uint8_t value7[1] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};
        int stringLen = 10;
        // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, 0, stringLen + 1);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // upgradeVersion
        int32_t upgradeVersion = 0;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d

        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        if (A1IsStr) {
            ret = GmcSetIndexKeyValue(stmt, 8, GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 8, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // b1
        ret = GmcSetIndexKeyValue(stmt, 9, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetIndexKeyValue(stmt, 10, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetIndexKeyValue(stmt, 11, GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetIndexKeyValue(stmt, 12, GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte1
        value6[0] = count[i][5];
        ret = GmcSetIndexKeyValue(stmt, 13, GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte1
        value7[0] = count[i][6];
        ret = GmcSetIndexKeyValue(stmt, 14, GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetIndexKeyValue(stmt, 15, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetIndexKeyValue(stmt, 16, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetIndexKeyValue(stmt, 17, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetIndexKeyValue(stmt, 18, GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetIndexKeyValue(stmt, 19, GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte1
        value6[0] = count[i][5];
        ret = GmcSetIndexKeyValue(stmt, 20, GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte1
        value7[0] = count[i][6];
        ret = GmcSetIndexKeyValue(stmt, 21, GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetIndexKeyValue(stmt, 22, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetIndexKeyValue(stmt, 23, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetIndexKeyValue(stmt, 24, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetIndexKeyValue(stmt, 25, GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetIndexKeyValue(stmt, 26, GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte1
        value6[0] = count[i][5];
        if (A1IsStr) {
            ret = GmcSetIndexKeyValue(stmt, 27, GMC_DATATYPE_FIXED, value8, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 27, GMC_DATATYPE_FIXED, value6, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        value7[0] = count[i][6];
        if (A1IsStr) {
            ret = GmcSetIndexKeyValue(stmt, 28, GMC_DATATYPE_FIXED, value9, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 28, GMC_DATATYPE_FIXED, value7, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // a4
        ret = GmcSetIndexKeyValue(stmt, 29, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        GmcSetIndexKeyValue(stmt, 30, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetIndexKeyValue(stmt, 31, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            result = GMERR_OUT_OF_MEMORY;
        }
        AW_MACRO_EXPECT_EQ_INT(result, ret);
        if (ret) {
            testGmcGetLastError();
        }

        free(buf);
    }
    return ret;
}

int BatchInsert(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum, int result = GMERR_OK,
    int32_t begin = 0, int32_t countValue = 1)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = begin; i < dataNum; i++) {
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[2] = {0};
        uint8_t value7[2] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};
        int stringLen = 10;
        // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, 0, stringLen + 1);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, value6, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, value7, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b1
        ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_FIXED, value6, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_FIXED, value7, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e2", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_FIXED, value6, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g2", GMC_DATATYPE_FIXED, value7, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e3", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value6, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g3:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value7, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a4
        ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d4
        ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e4:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e4", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f4:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g4:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g4", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a5
        ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b5
        ret = GmcSetVertexProperty(stmt, "b5", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c5
        ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d5
        ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e5:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e5", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f5:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g5:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g5", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a6
        ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b6
        ret = GmcSetVertexProperty(stmt, "b6", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c6
        ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d6
        ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e6:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e6", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f6:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g6:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g6", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a7
        ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b7
        ret = GmcSetVertexProperty(stmt, "b7", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c7
        ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d7
        ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e7:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e7", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f7:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g7:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g7", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a8
        ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b8
        ret = GmcSetVertexProperty(stmt, "b8", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c8
        ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d8
        ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e8:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e8", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f8:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a9
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &countValue, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(buf);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_OUT_OF_MEMORY) {
        result = GMERR_OUT_OF_MEMORY;
    }
    AW_MACRO_EXPECT_EQ_INT(result, ret);
    if (ret) {
        testGmcGetLastError();
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int TupleFullPropertyByte1Set(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    TupleFullPropertyByte1 *obj = (TupleFullPropertyByte1 *)t;
    int ret = 0;
    // a
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    // b
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d
    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  e:byte1
    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f:byte2
    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  g:byte2
    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, &obj->g, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a1
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // b1
    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c1
    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d1
    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  e1:byte1
    ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f1:byte1
    ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  g1:byte1
    ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_FIXED, &obj->g, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a2
    ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // b2
    ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c2
    ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d2
    ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  e2:byte1
    ret = GmcSetVertexProperty(stmt, "e2", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f2:byte1
    ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  g:byte1
    ret = GmcSetVertexProperty(stmt, "g2", GMC_DATATYPE_FIXED, &obj->g, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a3
    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // b3
    ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c3
    ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d3
    ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  e3:byte1
    ret = GmcSetVertexProperty(stmt, "e3", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f3:byte1
    ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  g3:byte1
    ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, &obj->g, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // a4
    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // b4
    ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c4
    ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d4
    if (!isStateTabelInput) {
        ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //  e4:byte1
    ret = GmcSetVertexProperty(stmt, "e4", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f4:byte128
    ret = GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  g4:byte256
    ret = GmcSetVertexProperty(stmt, "g4", GMC_DATATYPE_FIXED, &obj->g, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a5
    ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // b5
    ret = GmcSetVertexProperty(stmt, "b5", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c5
    ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d5
    ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  e5:byte1
    ret = GmcSetVertexProperty(stmt, "e5", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f5:byte128
    ret = GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  g5:byte256
    ret = GmcSetVertexProperty(stmt, "g5", GMC_DATATYPE_FIXED, &obj->g, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a6
    ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // b6
    ret = GmcSetVertexProperty(stmt, "b6", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c6
    ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d6
    ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  e6:byte1
    ret = GmcSetVertexProperty(stmt, "e6", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f6:byte1
    ret = GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  g6:byte1
    ret = GmcSetVertexProperty(stmt, "g6", GMC_DATATYPE_FIXED, &obj->g, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a7
    ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // b7
    ret = GmcSetVertexProperty(stmt, "b7", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c7
    ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d7
    ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  e7:byte1
    ret = GmcSetVertexProperty(stmt, "e7", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f7:byte1
    ret = GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  g7:byte1
    ret = GmcSetVertexProperty(stmt, "g7", GMC_DATATYPE_FIXED, &obj->g, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a8
    ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // b8
    ret = GmcSetVertexProperty(stmt, "b8", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c8
    ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // d8
    ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  e8:byte1
    ret = GmcSetVertexProperty(stmt, "e8", GMC_DATATYPE_FIXED, &obj->e, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // f8:byte1
    ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_FIXED, &obj->f, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int stringLen = 10;
    // string
    uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
    if (buf == NULL) {
        return -1;
    }
    memset(buf, 0, stringLen + 1);
    (void)snprintf((char *)buf, stringLen + 1, "b%09d", obj->c);
    // a9
    if (g_pubsubResource) {
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(buf);
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int TupleFullPropertyByte1Cmp(
    const TupleFullPropertyByte1 *st1, const TupleFullPropertyByte1 *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] a, st1: %d, st2: %d.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] b, st1: %d, st2: %d.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] c, st1: %d, st2: %d.", st1->c, st2->c)) : ({});
            break;
        }
        if (st1->d != st2->d) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] d, st1: %d, st2: %d.", st1->d, st2->d)) : ({});
            if (st1->d == 10000 || st2->d == 10000) {
            } else {
                break;
            }
        }

        if (st1->e[0] != st2->e[0]) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] e, st1: %d, st2: %d.", st1->e[0], st2->e[0])) :
                      ({});
            break;
        }
        if (st1->f[0] != st2->f[0]) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] f, st1: %d, st2: %d.", st1->f[0], st2->f[0])) :
                      ({});
            break;
        }
        if (st1->g[0] != st2->g[0]) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] g, st1: %d, st2: %d.", st1->g[0], st2->g[0])) :
                      ({});
            break;
        }

        if (st1->dtlReservedCount != st2->dtlReservedCount) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount);
            }
            break;
        }
        if (st1->upgradeVersion != st2->upgradeVersion) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[TupleFullPropertyByte1Cmp] upgradeVersion, st1: %d, st2: %d.",
                    st1->upgradeVersion, st2->upgradeVersion);
            }
            break;
        }
        ret = 0;
    } while (0);
    return ret;
}
int TupleFullPropertyByte1Get(GmcStmtT *stmt, void *t, int len, bool isExternal, bool isResource = false)
{
    TupleFullPropertyByte1 *checkObj = (TupleFullPropertyByte1 *)t;
    TupleFullPropertyByte1 getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", &getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", &getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "g", &getObj.g, sizeof(getObj.g), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'g' fail, ret = %d.", ret);
        return ret;
    }
    int stringLen = 10;
    if (isResource) {
        stringLen = 1;
    }

    // string
    uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
    if (buf == NULL) {
        return -1;
    }
    uint8_t *buf1 = (uint8_t *)malloc(stringLen + 1);
    if (buf1 == NULL) {
        return -1;
    }

    memset(buf, 0, stringLen + 1);
    (void)snprintf((char *)buf, stringLen + 1, "b%09d", getObj.c);
    if (isResource) {
        (void)snprintf((char *)buf, stringLen + 1, "1");
    }
    if (g_pubsubResource) {
    } else {
        ret = GmcGetVertexPropertyByName(stmt, "a9", buf1, stringLen + 1, &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'a9' fail, ret = %d.", ret);
            return ret;
        }
        ret = strcmp((char *)buf1, (char *)buf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(buf);
    free(buf1);

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[TupleFullPropertyByte1Get] get 'upgradeVersion' fail, ret = %d.", ret);
        return ret;
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (TupleFullPropertyByte1Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    return ret;
}
char g_tableName[128] = "cap";
char g_configJson[128] = "{\"max_record_count\":100000, \"isFastReadUncommitted\": false}";
int CreateKvTable()
{
    int ret = 0;
    // 创建kv表
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvDropTable(stmt, g_tableName);
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "para1";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char key1[32] = "para2";
    char value1[32] = "aaaaaa";
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1) + 1;
    kvInfo1.value = value1;
    kvInfo1.valueLen = strlen(value1) + 1;
    ret = GmcKvSet(stmt, kvInfo1.key, kvInfo1.keyLen, kvInfo1.value, kvInfo1.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo2 = {0};
    char key2[32] = "para3";
    char value2[64] = {0};
    memset(value2, 'c', 63);
    kvInfo2.key = key2;
    kvInfo2.keyLen = strlen(key2) + 1;
    kvInfo2.value = value2;
    kvInfo2.valueLen = strlen(value2) + 1;
    ret = GmcKvSet(stmt, kvInfo2.key, kvInfo2.keyLen, kvInfo2.value, kvInfo2.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo3 = {0};
    char key3[32] = "para4";
    int64_t value3 = 1000;
    kvInfo3.key = key3;
    kvInfo3.keyLen = strlen(key3) + 1;
    kvInfo3.value = &value3;
    kvInfo3.valueLen = sizeof(int64_t);
    ret = GmcKvSet(stmt, kvInfo3.key, kvInfo3.keyLen, kvInfo3.value, kvInfo3.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview show cap");
    return ret;
}

#endif /* __DLGSPECON_H__ */
