/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: countInFuncTest.cpp
 * Description: udf支持dtlReservedCount值传入
 * Author: <PERSON><PERSON><PERSON><PERSON> ywx1252574
 * Create: 2023-09-13
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "t_rd_sn.h"
#include "DatalogRunEnhance.h"

#define MAX_CMD_SIZE 1024

class DlgSpeCon : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};
void DlgSpeCon::SetUp()
{
    int ret = 0;
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void DlgSpeCon::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
// 211.支持将括号内变量修改为常量
TEST_F(DlgSpeCon, DataLog_051_211)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_211.d";
    char soFileName[] = "Dtl3files/Datalog_051_211.so";
    char soName[] = "Datalog_051_211";
    char dPatchName[] = "Dtl3files/Datalog_051_211_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_211_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1, 1, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    int32_t data1[3] = {2, 2, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 1", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 212.支持将括号内常量修改为变量
TEST_F(DlgSpeCon, DataLog_051_212)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_212.d";
    char soFileName[] = "Dtl3files/Datalog_051_212.so";
    char soName[] = "Datalog_051_212";
    char dPatchName[] = "Dtl3files/Datalog_051_212_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_212_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 2, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    int32_t data1[3] = {3, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 3", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 213.支持将规则括号内忽略符变更为常量
TEST_F(DlgSpeCon, DataLog_051_213)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_213.d";
    char soFileName[] = "Dtl3files/Datalog_051_213.so";
    char soName[] = "Datalog_051_213";
    char dPatchName[] = "Dtl3files/Datalog_051_213_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_213_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 5, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    int32_t data1[3] = {3, 2, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 2", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 3", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 214.支持将规则括号内常量变更为忽略符
TEST_F(DlgSpeCon, DataLog_051_214)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_214.d";
    char soFileName[] = "Dtl3files/Datalog_051_214.so";
    char soName[] = "Datalog_051_214";
    char dPatchName[] = "Dtl3files/Datalog_051_214_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_214_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    int32_t data1[3] = {3, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 3", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 215.支持将规则括号内忽略符变更为变量
TEST_F(DlgSpeCon, DataLog_051_215)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_215.d";
    char soFileName[] = "Dtl3files/Datalog_051_215.so";
    char soName[] = "Datalog_051_215";
    char dPatchName[] = "Dtl3files/Datalog_051_215_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_215_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 2, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    int32_t data1[3] = {3, 6, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 2", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 3", "\"b\": 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 216.支持将规则括号内变量转变为忽略符
TEST_F(DlgSpeCon, DataLog_051_216)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_216.d";
    char soFileName[] = "Dtl3files/Datalog_051_216.so";
    char soName[] = "Datalog_051_216";
    char dPatchName[] = "Dtl3files/Datalog_051_216_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_216_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 4", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    int32_t data1[3] = {3, 6, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 2", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 3", "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 217.不支持修改规则中join 表顺序
TEST_F(DlgSpeCon, DataLog_051_217)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_217.d";
    char soFileName[] = "Dtl3files/Datalog_051_217.so";
    char soName[] = "Datalog_051_217";
    char dPatchName[] = "Dtl3files/Datalog_051_217_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_217_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_217_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "rule r0 should not change struct except add one table, add function or delete function near line 3.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 218.不支持修改规则中join function顺序
TEST_F(DlgSpeCon, DataLog_051_218)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_218.d";
    char soFileName[] = "Dtl3files/Datalog_051_218.so";
    char soName[] = "Datalog_051_218";
    char dPatchName[] = "Dtl3files/Datalog_051_218_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_218_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_218_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 9");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "rule r0 should not change struct except add one table, add function or delete function near line 3.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 219.不支持修改规则中not join 表顺序
TEST_F(DlgSpeCon, DataLog_051_219)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_219.d";
    char soFileName[] = "Dtl3files/Datalog_051_219.so";
    char soName[] = "Datalog_051_219";
    char dPatchName[] = "Dtl3files/Datalog_051_219_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_219_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_219_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_in2[] = "inpC";
    char labelName_out[] = "outD";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    int32_t data2[3] = {4, 5, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data2[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data2[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data2[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "rule r0 should not change struct except add one table, add function or delete function near line 3.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 220.不支持对precedence规则进行新增或修改(表的数数量增加或减少)
TEST_F(DlgSpeCon, DataLog_051_220)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_220add.d";
    char soFileName[] = "Dtl3files/Datalog_051_220add.so";
    char soName[] = "Datalog_051_220add";
    char dPatchName[] = "Dtl3files/Datalog_051_220add_patch.d";
    char dPatchName1[] = "Dtl3files/Datalog_051_220min_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_220add_patchV2.so";
    char soPatchName1[] = "Dtl3files/Datalog_051_220min_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_220add_rule.d";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "syntax error");
    ASSERT_EQ(GMERR_OK, ret);

    // 加载减少升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName1);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName1);
    ret = executeCommand(g_command, "syntax error");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 221.不支持删除已存在的投影规则
TEST_F(DlgSpeCon, DataLog_051_221)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_221.d";
    char soFileName[] = "Dtl3files/Datalog_051_221.so";
    char soName[] = "Datalog_051_221";
    char dPatchName[] = "Dtl3files/Datalog_051_221_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_221_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_221_rule.d";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "drop rule \"r0\" is not supported near line 3.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 222.不支持删除已存在的join规则
TEST_F(DlgSpeCon, DataLog_051_222)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_222.d";
    char soFileName[] = "Dtl3files/Datalog_051_222.so";
    char soName[] = "Datalog_051_222";
    char dPatchName[] = "Dtl3files/Datalog_051_222_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_222_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_222_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outC";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "drop rule \"r1\" is not supported near line 3.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 223.不支持删除已存在的not join规则
TEST_F(DlgSpeCon, DataLog_051_223)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_223.d";
    char soFileName[] = "Dtl3files/Datalog_051_223.so";
    char soName[] = "Datalog_051_223";
    char dPatchName[] = "Dtl3files/Datalog_051_223_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_223_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_223_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_in2[] = "inpC";
    char labelName_out[] = "outD";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    int32_t data2[3] = {4, 5, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data2[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data2[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data2[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 5");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "drop rule \"r0\" is not supported near line 3.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 224.支持新增输入表时新增规则投影到NULL(0)
TEST_F(DlgSpeCon, DataLog_051_224)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_224.d";
    char soFileName[] = "Dtl3files/Datalog_051_224.so";
    char soName[] = "Datalog_051_224";
    char dPatchName[] = "Dtl3files/Datalog_051_224_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_224_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_224_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpC";
    char labelName_out[] = "outB";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {4, 5, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 0", "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_in1);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 0", "\"a\": 4", "\"b\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 225.支持新增输入表时新增规则含join
TEST_F(DlgSpeCon, DataLog_051_225)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_225.d";
    char soFileName[] = "Dtl3files/Datalog_051_225.so";
    char soName[] = "Datalog_051_225";
    char dPatchName[] = "Dtl3files/Datalog_051_225_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_225_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_225_rule.d";
    char labelName_in[] = "inpA";

    char labelName_out[] = "outB";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "Serialize done");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 226.支持新增输入表时新增规则含not join
TEST_F(DlgSpeCon, DataLog_051_226)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_226.d";
    char soFileName[] = "Dtl3files/Datalog_051_226.so";
    char soName[] = "Datalog_051_226";
    char dPatchName[] = "Dtl3files/Datalog_051_226_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_226_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_226_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outD";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "Serialize done");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 227.不支持新增function时新增规则含not join
TEST_F(DlgSpeCon, DataLog_051_227)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_227.d";
    char soFileName[] = "Dtl3files/Datalog_051_227.so";
    char soName[] = "Datalog_051_227";
    char dPatchName[] = "Dtl3files/Datalog_051_227_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_227_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_227_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outD";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret =
        executeCommand(g_command, "NOT table \"funcA\" should not appear in the same rule with function near line 5.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 228.不支持新增function时新增规则含join
TEST_F(DlgSpeCon, DataLog_051_228)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_228.d";
    char soFileName[] = "Dtl3files/Datalog_051_228.so";
    char soName[] = "Datalog_051_228";
    char dPatchName[] = "Dtl3files/Datalog_051_228_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_228_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_228_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outC";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "Serialize done");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 229.不支持新增function时新增规则投影到NULL(0)
TEST_F(DlgSpeCon, DataLog_051_229)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_229.d";
    char soFileName[] = "Dtl3files/Datalog_051_229.so";
    char soName[] = "Datalog_051_229";
    char dPatchName[] = "Dtl3files/Datalog_051_229_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_229_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_229_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outC";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "Serialize done");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 230.支持一条规则新增多个udf
TEST_F(DlgSpeCon, DataLog_051_230)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_230.d";
    char soFileName[] = "Dtl3files/Datalog_051_230.so";
    char soName[] = "Datalog_051_230";
    char dPatchName[] = "Dtl3files/Datalog_051_230_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_230_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_230_rule.d";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "Serialize done");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 231.不支持一条规则新增多张表
TEST_F(DlgSpeCon, DataLog_051_231)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_231.d";
    char soFileName[] = "Dtl3files/Datalog_051_231.so";
    char soName[] = "Datalog_051_231";
    char dPatchName[] = "Dtl3files/Datalog_051_231_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_231_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_231_rule.d";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "only allowed to add one table or function, or remove a func at the end of rule \"r0\" near line 5.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 232.支持变更括号内字段变量的顺序
TEST_F(DlgSpeCon, DataLog_051_232)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_232.d";
    char soFileName[] = "Dtl3files/Datalog_051_232.so";
    char soName[] = "Datalog_051_232";
    char dPatchName[] = "Dtl3files/Datalog_051_232_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_232_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    int32_t data1[3] = {4, 6, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 3", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 6", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 233.同一个补丁内，新增输入表，相同topo不同行规则括号内内容变更升级成功
TEST_F(DlgSpeCon, DataLog_051_233)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_233.d";
    char soFileName[] = "Dtl3files/Datalog_051_233.so";
    char soName[] = "Datalog_051_233";
    char dPatchName[] = "Dtl3files/Datalog_051_233_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_233_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_233_rule.d";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "Serialize done");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 234.同一个补丁内，新增输入表，同行规则旧表join顺序不支持变更
TEST_F(DlgSpeCon, DataLog_051_234)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_234.d";
    char soFileName[] = "Dtl3files/Datalog_051_234.so";
    char soName[] = "Datalog_051_234";
    char dPatchName[] = "Dtl3files/Datalog_051_234_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_234_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_234_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "rule r0 should not change struct except add one table, add function or delete function near line 5.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 235.同一个补丁内，新增输入表，同行规则表not join顺序不支持变更
TEST_F(DlgSpeCon, DataLog_051_235)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_235.d";
    char soFileName[] = "Dtl3files/Datalog_051_235.so";
    char soName[] = "Datalog_051_235";
    char dPatchName[] = "Dtl3files/Datalog_051_235_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_235_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_235_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_in2[] = "inpC";
    char labelName_out[] = "outD";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {4, 5, 1};
    int32_t data2[3] = {1, 2, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data2[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data2[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data2[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_in1);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 4", "\"b\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_in2);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1", "\"b\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "rule r0 should not change struct except add one table, add function or delete function near line 4.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 236.同一个补丁内，新增function，相同topo不同行规则括号内内容变更升级成功
TEST_F(DlgSpeCon, DataLog_051_236)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_236.d";
    char soFileName[] = "Dtl3files/Datalog_051_236.so";
    char soName[] = "Datalog_051_236";
    char dPatchName[] = "Dtl3files/Datalog_051_236_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_236_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "successfully");
    ASSERT_EQ(GMERR_OK, ret);
    sleep(1);

    int32_t data1[3] = {4, 6, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 2", "\"b\": 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1", "\"a\": 4", "\"b\": 11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 237.同一个补丁内，新增function，同行规则表not join顺序不支持变更
TEST_F(DlgSpeCon, DataLog_051_237)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_237.d";
    char soFileName[] = "Dtl3files/Datalog_051_237.so";
    char soName[] = "Datalog_051_237";
    char dPatchName[] = "Dtl3files/Datalog_051_237_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_237_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_237_rule.d";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outD";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "NOT table \"funcA\" should not appear in the same rule with function near line 4");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 238.同一个补丁内，新增function，同行规则旧表join顺序不支持变更
TEST_F(DlgSpeCon, DataLog_051_238)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_238.d";
    char soFileName[] = "Dtl3files/Datalog_051_238.so";
    char soName[] = "Datalog_051_238";
    char dPatchName[] = "Dtl3files/Datalog_051_238_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_238_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_238_rule.d";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outC";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    int32_t data1[3] = {3, 4, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "rule r0 should not change struct except add one table, add function or delete function near line 5.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 239.不支持新增function 变量入参在左边的function中未出现
TEST_F(DlgSpeCon, DataLog_051_239)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_239.d";
    char soFileName[] = "Dtl3files/Datalog_051_239.so";
    char soName[] = "Datalog_051_239";
    char dPatchName[] = "Dtl3files/Datalog_051_239_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_239_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_239_rule.d";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command,
        "rule r0 should not change struct except add one table, add function or delete function near line 5.");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 240.不支持新增function 变量入参在左边的表中未出现
TEST_F(DlgSpeCon, DataLog_051_240)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char dFileName[] = "Dtl3files/Datalog_051_240.d";
    char soFileName[] = "Dtl3files/Datalog_051_240.so";
    char soName[] = "Datalog_051_240";
    char dPatchName[] = "Dtl3files/Datalog_051_240_patch.d";
    char soPatchName[] = "Dtl3files/Datalog_051_240_patchV2.so";
    char ruleName[] = "Dtl3files/Datalog_051_240_rule.d";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";
    // 加载原.so
    (void)TestUninstallDatalog(soName);
    ret = TestLoadDatalog(soFileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {2, 3, 1};
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s", &labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2", "\"b\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载增加升级.so
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade %s", &soPatchName);
    ret = executeCommand(g_command, "no such file or directory");
    ASSERT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -u %s %s", &ruleName, &dPatchName);
    ret = executeCommand(g_command, "the function \"funcB\" can not find inputfield \"c\" in left table");
    ASSERT_EQ(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
