%version v1.0.0 -> v2.0.0
%alter function tran( d4:int8,e4:byte1,f4:byte1, g4:byte1 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte1, g5:byte1 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte1, g6:byte1 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte1, g7:byte1 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte1, a9:str -> e41:byte1, f41:byte1, g41:byte1 ,a51:int1, b51:int2, c51:int4, d51:int8, e51:byte1, f51:byte1, g51:byte1 ,a61:int1, b61:int2, c61:int4, d61:int8, e61:byte1, f61:byte1, g61:byte1 ,a71:int1, b71:int2, c71:int4, d71:int8, e71:byte1, f71:byte1, g71:byte1 ,a81:int1, b81:int2, c81:int4, d81:int8, e81:byte1, f81:byte1, a91:str) { state_transfer, access_delta(B000)}
