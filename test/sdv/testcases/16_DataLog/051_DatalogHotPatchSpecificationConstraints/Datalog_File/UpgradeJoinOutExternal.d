%version v1.0.0
%table N000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte1, g3:byte1 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte1, g4:byte1 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte1, g5:byte1 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte1, g6:byte1 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte1, g7:byte1 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte1, a9:str)
{ index(0(a,b)), external}

%table B000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte1, g3:byte1,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte1, g4:byte1,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte1, g5:byte1,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte1, g6:byte1,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte1, g7:byte1,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte1, a9:str)
{
index(0(a,b)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))
}

%table C000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte1, g3:byte1,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte1, g4:byte1,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte1, g5:byte1,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte1, g6:byte1,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte1, g7:byte1,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte1, a9:str)
{
index(0(a,b)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))
}

%table Finish000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte1, g3:byte1,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte1, g4:byte1,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte1, g5:byte1,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte1, g6:byte1,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte1, g7:byte1,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte1, a9:str)
{
index(0(a,b)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))
}

%function func(a: int4 -> b: int4){ }

// 投影规则
B000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- C000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).
N000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- B000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).
N000(a,b,c51,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- Finish000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), func(c,c51).
