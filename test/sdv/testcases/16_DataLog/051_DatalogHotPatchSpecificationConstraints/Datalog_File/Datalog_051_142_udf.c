/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"
#define INSERT 0
#define DELETE 1
#define UPDATE 2

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[1];
    int8_t g[1];
    int8_t a1;
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[1];
    int8_t g1[1];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[1];
    int8_t g2[1];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[1];
    int8_t g3[1];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a9Len;
    char *a9;
} TupleAgg;
#pragma pack(0)

#pragma pack(1)
typedef struct FuncAggIn {
    int32_t count;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a9Len;
    char *a9;
} FuncAggIn;

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[1];
    int8_t g[1];
    int8_t a1;
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[1];
    int8_t g1[1];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[1];
    int8_t g2[1];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[1];
    int8_t g3[1];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a9Len;
    char *a9;
} TupleA;
#pragma pack(0)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[1];
    int8_t g[1];
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[1];
    int8_t g1[1];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[1];
    int8_t g2[1];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[1];
    int8_t g3[1];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a1Len;
    char *a1;
    int32_t a9Len;
    char *a9;
} TupleT;
#pragma pack(0)
const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_init.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_uninit.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_O000(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(
        fp, "[%s] dtl_tbm_tbl_A, op = %d, a = %d, b = %d.\n", __FILE__, op, ((TupleT *)tuple)->a, ((TupleT *)tuple)->b);
    (void)fclose(fp);
    return GMERR_OK;
}

// 级联删除function
#pragma pack(1)
typedef struct {
    int32_t count;
    int32_t a;
    int32_t b;
} DEL;
#pragma pack(0)
int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);
    reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 1, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);
    reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 2, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);
    reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 3, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 4, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 5, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 6, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    GmUdfDestroyReader(ctx, reader);
    return GMERR_OK;
}
