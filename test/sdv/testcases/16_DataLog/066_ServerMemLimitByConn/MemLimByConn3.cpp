/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: MemLimByConn2.cpp
 * Description: Server Memory Is Limited By Connection
 * Author: youwanyong ywx1157510
 * Create: 2024-1-15
 */

#ifdef FEATURE_YANG

#include "t_datacom_lite.h"
#include "MuiIdxSupRolBak.h"
using namespace std;

class YangMemLimByConn : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 单线程异步标识
        g_isOneThreadEpoll = true;
        ret = createEpollOneThread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        g_isOneThreadEpoll = false;
        closeEpollOneThread();
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void YangMemLimByConn::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    char errorMsg5[errCodeLen] = {0};
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    char errorMsg6[errCodeLen] = {0};
    (void)snprintf(errorMsg6, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(6, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5, errorMsg6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void YangMemLimByConn::TearDown()
{
    AW_CHECK_LOG_END();
}

class MemLimByConn : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void MemLimByConn::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void MemLimByConn::TearDown()
{
    AW_CHECK_LOG_END();
}

int32_t GetLimitMemValue(const char *PName = (char *)"MemLimByConn2")
{
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    char command[MAX_CMD_SIZE];
    snprintf(command,
        MAX_CMD_SIZE,
        "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=$(gmsysview -q V\\$QRY_SESSION -f CONN_ID=$(gmsysview -q "
        "V\\$DRT_CONN_STAT -f CLT_PROC_NAME=%s|grep CONN_ID| awk 'NR==1{print $2}')|grep MEMCTX_NAME| awk 'NR==1{print "
        "$2}')|grep THRESHOLD_ON_TREE| sed 's/[^0-9]//g'",
        PName);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_INFO, "popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, 64, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

// subtree 查询
bool testYangJson1IsEqualInner(const json_t *jsonA, const json_t *jsonB);
bool testYangJson1IsEqualReal(const json_t *valueA, const json_t *valueB)
{
    double doubleA = json_real_value(valueA);
    double doubleB = json_real_value(valueB);
    if (fabs(doubleA - doubleB) < 1e-6) {
        return true;
    }
    return false;
}
bool testYangJson1IsEqualField(const json_t *valueA, const json_t *valueB)
{
    if (json_typeof(valueA) == JSON_STRING) {
        return strcmp(json_string_value(valueA), json_string_value(valueB)) == 0;
    }
    if (json_typeof(valueA) == JSON_INTEGER) {
        return json_integer_value(valueA) == json_integer_value(valueB);
    }
    if (json_typeof(valueA) == JSON_REAL) {
        return testYangJson1IsEqualReal(valueA, valueB);
    }
    return true;
}
bool testYangJson1IsEqualArray(const json_t *valueA, const json_t *valueB)
{
    bool isEqual = true;
    uint32_t sizeA = (uint32_t)json_array_size(valueA);
    uint32_t sizeB = (uint32_t)json_array_size(valueB);
    if (sizeA != sizeB) {
        return false;
    }
    for (uint32_t i = 0; i < sizeA; ++i) {
        json_t *itemA = json_array_get(valueA, i);
        json_t *itemB = json_array_get(valueB, i);
        if (json_typeof(itemA) == JSON_OBJECT) {
            isEqual = testYangJson1IsEqualInner(itemA, itemB);
        } else {
            isEqual = testYangJson1IsEqualField(itemA, itemB);
        }
        if (!isEqual) {
            return false;
        }
    }
    return true;
}
bool testYangJson1IsEqualInner(const json_t *jsonA, const json_t *jsonB)
{
    bool isEqual = true;
    void *itA = json_object_iter((json_t *)jsonA);
    void *itB = json_object_iter((json_t *)jsonB);

    if (itA) {
        const char *keyA = json_object_iter_key(itA);

        while ((strcmp(keyA, "ID") == 0) || (strcmp(keyA, "PID") == 0)) {
            itA = json_object_iter_next((json_t *)jsonA, itA);
            if (itA == NULL) {
                return true;
            }
            keyA = json_object_iter_key(itA);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    if (itB) {
        const char *keyB = json_object_iter_key(itB);

        while ((strcmp(keyB, "ID") == 0) || (strcmp(keyB, "PID") == 0)) {
            itB = json_object_iter_next((json_t *)jsonA, itB);
            if (itB == NULL) {
                return true;
            }
            keyB = json_object_iter_key(itB);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }

    while (itA && itB) {
        const char *keyA = json_object_iter_key(itA);
        json_t *valueA = json_object_iter_value(itA);
        const char *keyB = json_object_iter_key(itB);
        json_t *valueB = json_object_iter_value(itB);

        if ((json_typeof(valueA) != json_typeof(valueB)) || (strcmp(keyA, keyB) != 0)) {
            return false;
        }
        if (json_typeof(valueA) == JSON_OBJECT) {
            isEqual = testYangJson1IsEqualInner(valueA, valueB);
        } else if (json_typeof(valueA) == JSON_ARRAY) {
            isEqual = testYangJson1IsEqualArray(valueA, valueB);
        } else {
            isEqual = testYangJson1IsEqualField(valueA, valueB);
        }
        if (!isEqual) {
            return false;
        }
        itA = json_object_iter_next((json_t *)jsonA, itA);
        itB = json_object_iter_next((json_t *)jsonB, itB);
    }

    return itA == itB;
}
bool testYangJson1IsEqual(const char *json1, const char *json2)
{
    json_error_t jsonError;
    json_t *jsonA = json_loads(json1, JSON_REJECT_DUPLICATES, &jsonError);
    json_t *jsonB = json_loads(json2, JSON_REJECT_DUPLICATES, &jsonError);
    bool isEqual = testYangJson1IsEqualInner(jsonA, jsonB);
    json_decref(jsonA);
    json_decref(jsonB);
    return isEqual;
}

void CheckT2ListOrderResult(GmcStmtT *stmt_sync, char *suntreeReturnJson = NULL)
{
    // 进行subtree查询
    char *containernodeListJson = NULL;
    readJanssonFile("schema_file/List_node.json", &containernodeListJson);
    EXPECT_NE((void *)NULL, containernodeListJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containernodeListJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    GmcFetchRetT *fetchRet = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecute(stmt_sync, &filters, &fetchRet));
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
    EXPECT_TRUE(isEnd);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    EXPECT_TRUE(jsonReply != NULL);
    printf("%s\n", jsonReply[0]);
    if (suntreeReturnJson != NULL) {
        EXPECT_TRUE(testYangJson1IsEqual(jsonReply[0], suntreeReturnJson));
    }
    free(containernodeListJson);
    GmcYangFreeFetchRet(fetchRet);
}

struct FetchRetCbParam123 {
    int step;
    GmcStmtT *stmt;
    Status expectStatus;                    // 预期的操作状态
    uint32_t filterMode;                    // 过滤模式，使用枚举GmcSubtreeFilterModeE设置值
    uint32_t lastExpectIdx;                 // 分批查询上次查询期望结果的最后索引
    std::vector<std::string> &expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
};
string testYangTreeToStr(GmcStmtT *stmt, const GmcYangTreeT *reply, bool isDiff)
{
    string res = "";
    if (!isDiff) {
        char *replyJson = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangTreeToJson(reply, &replyJson));
        res = string(replyJson);
    }
    return res;
}
void CheckTreeReply(const GmcYangTreeT **yangTree, uint32_t count, FetchRetCbParam123 *param, bool isDiff = false)
{
    uint32_t idx = param->lastExpectIdx;
    EXPECT_TRUE(param->expectReply.size() >= (idx + count));  // 断言防止越界
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            strcmp(param->expectReply[idx + i].c_str(), "{}");
            continue;
        }
        std::string reply = testYangTreeToStr(param->stmt, yangTree[i], isDiff);
        EXPECT_TRUE(testYangJson1IsEqual(reply.c_str(), param->expectReply[idx + i].c_str())) << "replyJson:\n"
                                                                                              << reply << endl;
        GmcYangFreeTree(yangTree[i]);
    }
}

void AsyncYangFetchRetCb(void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    FetchRetCbParam123 *param = reinterpret_cast<FetchRetCbParam123 *>(userData);
    EXPECT_EQ(param->expectStatus, status) << errMsg;
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (status == param->expectStatus) {
        param->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    return;
}
// userData结构
struct SubtreeFilterCbParam {
    int step;
    int32_t expectStatus;         // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};

int testWaitAsyncSubtreeRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    struct epoll_event events[MAX_EPOLL_EVENT_COUNT];
    SubtreeFilterCbParam *userdata1 = (SubtreeFilterCbParam *)userData;
    int num = userdata1->step;
    if (num != 0) {
        printf("%d\n", num);
    }
    while (userdata1->step != expRecvNum) {
        int fdCount = epoll_wait(g_epollDataOneThread.userEpollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            if (g_runMode == 1) {
                GmcHandleEvent(events[fdCount].data.fd);
            } else {
                GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
            }
        }
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d,\actually recived num : %d\n",
                (double)duration / 1000000,
                expRecvNum,
                userdata1->step);
            return -1;  // 接收超时
        }
    }
    if (isAutoReset) {
        userdata1->step = 0;
    }
    return 0;
}

int testWaitAsyncWhenRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    struct epoll_event events[MAX_EPOLL_EVENT_COUNT];
    YangValidateUserDataT *userdata1 = (YangValidateUserDataT *)userData;
    int num = userdata1->recvNum;
    if (num != 0) {
        printf("%d\n", num);
    }
    while (userdata1->recvNum != expRecvNum) {
        int fdCount = epoll_wait(g_epollDataOneThread.userEpollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            if (g_runMode == 1) {
                GmcHandleEvent(events[fdCount].data.fd);
            } else {
                GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
            }
        }
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d,\actually recived num : %d\n",
                (double)duration / 1000000,
                expRecvNum,
                userdata1->recvNum);
            return -1;  // 接收超时
        }
    }
    if (isAutoReset) {
        userdata1->recvNum = 0;
    }
    return 0;
}
struct ValidateParam {
    std::atomic_uint32_t *step;
    int32_t exceptStatus;         // 预期的操作状态
    GmcValidateResT validateRes;  // 预期返回的mandatory校验结果
    bool isValidateErrorPath;
    GmcErrorPathCodeE expectedErrCode;
    uint32_t expectedErrClauseIndex;
    const char *expectedErrMsg;
    const char *expectedErrPath;
    uint64_t startTime;
    bool printTime;
    bool printSize;
};
int testWaitAsyncWhenDataRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    struct epoll_event events[MAX_EPOLL_EVENT_COUNT];
    ValidateParam *userdata1 = (ValidateParam *)userData;
    int num = *(userdata1->step);
    if (num != 0) {
        printf("%d\n", num);
    }
    while (*(userdata1->step) != expRecvNum) {
        int fdCount = epoll_wait(g_epollDataOneThread.userEpollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            if (g_runMode == 1) {
                GmcHandleEvent(events[fdCount].data.fd);
            } else {
                GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
            }
        }
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d,\actually recived num : %d\n",
                (double)duration / 1000000,
                expRecvNum,
                userdata1->step);
            return -1;  // 接收超时
        }
    }
    if (isAutoReset) {
        userdata1->step = 0;
    }
    return 0;
}

// savePoint
int32_t CreateSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    AsyncUserDataT userData = {0};
    ret = GmcTransCreateSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    if (userData.status != expstatus) {
        AW_FUN_Log(LOG_DEBUG, "create savepoint error code:%d\n", userData.status);
        return userData.status;
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
    return userData.status;
}

void RollbackSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    AsyncUserDataT userData = {0};
    ret = GmcTransRollBackSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "rollback savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

int UseAllBreakYangToFullMem()
{
    int ret = 0;
    GmcBatchT *batch = NULL;
    const char *configTrans = "{\"max_record_count\" : 10000000, \"isFastReadUncommitted\":0, \"auto_increment\":1}";
    // 乐观可重复读事务
    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;

    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    AsyncUserDataT data{0};
    GmcDropNamespace(g_stmt_sync, g_namespace);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    char *vlabelSchema = NULL;
    char *vlabelEdgeSchema = NULL;

    readJanssonFile("schema_file/Yang_095_AlLBreaK.gmjson", &vlabelSchema);
    EXPECT_NE((void *)NULL, vlabelSchema);
    ret = TestYangCreateMulLabel(g_stmt_async, vlabelSchema, configTrans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schema_file/Yang_095_AlLBreaK_Edge.gmjson", &vlabelEdgeSchema);
    EXPECT_NE((void *)NULL, vlabelEdgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelEdgeSchema, configTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);
    free(vlabelEdgeSchema);

    // 开启乐观可重复读写数据直到内存满
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第一次写数据写表直到内存满，事务提交失败，事务回滚

    InsertAllBreakLabel(batch, g_stmt_async, 1);
    int i = 2;
    while (ret != GMERR_OUT_OF_MEMORY && i < 901) {
        ret = MergeInsertAllBreakList(batch, g_stmt_async, i);
        i++;
    }
    GmcBatchDestroy(batch);
    AW_FUN_Log(LOG_STEP, "yang table insert record num is %d.", i - 2);
    return 0;
}

int YangSubtreePartBreak()
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    connOptions.srvMemCtxLimit = 2;
    int ret = 0;
    AsyncUserDataT data{0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcNodeT *root = NULL;
    const char *complexroot = "T0";
    ret = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema_file/Yang_095_AlLBreaK.gmjson", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    char *suntreeReturnJson1 = NULL;
    readJanssonFile("schema_file/Yang_095_AlLBreaK.gmjson", &suntreeReturnJson1);
    EXPECT_NE((void *)NULL, suntreeReturnJson1);
    char *suntreeReturnJson2 = NULL;
    std::vector<std::string> reply(2);
    reply[0] = suntreeReturnJson;
    reply[1] = suntreeReturnJson1;
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OUT_OF_MEMORY,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncYangFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    free(suntreeReturnJson1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int YangSubtreeAllBreak()
{
    sleep(10);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    int ret = 0;
    AsyncUserDataT data{0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcNodeT *root = NULL;
    const char *complexroot = "T0";
    ret = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema_file/Yang_095_AlLBreaK.gmjson", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    char *suntreeReturnJson1 = NULL;
    readJanssonFile("schema_file/Yang_095_AlLBreaK.gmjson", &suntreeReturnJson1);
    EXPECT_NE((void *)NULL, suntreeReturnJson1);
    char *suntreeReturnJson2 = NULL;
    std::vector<std::string> reply(2);
    reply[0] = suntreeReturnJson;
    reply[1] = suntreeReturnJson1;
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncYangFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    free(suntreeReturnJson1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// 建表操作封装
void TestCreateAllBreakLabel()
{
    int ret = 0;
    AsyncUserDataT data{0};
    char *vlabelSchema = NULL;
    char *vlabelEdgeSchema = NULL;
    const char *configTrans = "{\"max_record_count\" : 10000000, \"isFastReadUncommitted\":0, \"auto_increment\":1}";
    readJanssonFile("schema_file/Yang_095_AlLBreaK.gmjson", &vlabelSchema);
    EXPECT_NE((void *)NULL, vlabelSchema);
    ret = TestYangCreateMulLabel(g_stmt_async, vlabelSchema, configTrans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schema_file/Yang_095_AlLBreaK_Edge.gmjson", &vlabelEdgeSchema);
    EXPECT_NE((void *)NULL, vlabelEdgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelEdgeSchema, configTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);
    free(vlabelEdgeSchema);
}

void TestCreateVertexLabel()
{
    char *schema = NULL;
    char *schemaConfig = NULL;
    const char *labelName = "Vertex_095";
    readJanssonFile("./schema_file/Vertex_095.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    readJanssonFile("./schema_file/Vertex_095.gmconfig", &schemaConfig);
    EXPECT_NE((void *)NULL, schemaConfig);

    // 删除可能存在表
    int ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    if (ret == GMERR_UNDEFINED_TABLE) {
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    AW_FUN_Log(LOG_STEP, "建表.");
    ret = GmcCreateVertexLabel(g_stmt_sync, schema, schemaConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    free(schemaConfig);
}

// CreateSavepoint
void StartServerWith()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=8\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    int64_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    // 悲观读已提交事务配置
    g_prtrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_prtrx_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_prtrx_config.readOnly = false;
    g_prtrx_config.trxType = GMC_PESSIMISITIC_TRX;
    // 乐观可重复读事务配置
    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;
}

// CreateSavepoint
void StartServerWith592()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=8\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int64_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    // 悲观读已提交事务配置
    g_prtrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_prtrx_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_prtrx_config.readOnly = false;
    g_prtrx_config.trxType = GMC_PESSIMISITIC_TRX;
    // 乐观可重复读事务配置
    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;
}

void StartServerWith74()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=78\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=8\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int64_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 悲观读已提交事务配置
    g_prtrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_prtrx_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_prtrx_config.readOnly = false;
    g_prtrx_config.trxType = GMC_PESSIMISITIC_TRX;
    // 乐观可重复读事务配置
    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;
}

void StartServerWith156()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=156\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=8\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int64_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 悲观读已提交事务配置
    g_prtrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_prtrx_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_prtrx_config.readOnly = false;
    g_prtrx_config.trxType = GMC_PESSIMISITIC_TRX;
    // 乐观可重复读事务配置
    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;
}

void StartServerWith312()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=312\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=8\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=124\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int64_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 悲观读已提交事务配置
    g_prtrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_prtrx_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_prtrx_config.readOnly = false;
    g_prtrx_config.trxType = GMC_PESSIMISITIC_TRX;
    // 乐观可重复读事务配置
    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;
}
void StopServer()
{

    int64_t ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

// 开启事务写vertex
int TestWriteVertexLabelRollBack(GmcConnT *conn = g_conn_sync, GmcStmtT *stmt = g_stmt_sync, int32_t startValue = 0)
{
    // 开启事务写数据直到内存满事务回滚
    const char *keyName = "index_vertex_01";
    int ret = GmcTransStart(conn, &g_prtrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int firstCycles = startValue;
    int secondCycles = 0;
    const char *labelName = "Vertex_095";
    while (!ret) {
        if (firstCycles % 100 == 0) {
            AW_FUN_Log(LOG_STEP, "firstCycles =%d.", firstCycles);
        }
        ret = InsertVetexLabelByNode(stmt, labelName, firstCycles, firstCycles + 1);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "GMERR_OUT_OF_MEMORY = %d.", firstCycles);
        }
        firstCycles++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    secondCycles = firstCycles - 2;
    sleep(30);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预期内存满时插入数据失败
    ret = ScanVertexLabelByNode(stmt, labelName, keyName, firstCycles - 2, firstCycles - 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
    sleep(30);

    // 查询数据预期数据被回滚掉
    int dataNum = firstCycles - 2;
    int cycle = dataNum % 1000;
    AW_FUN_Log(LOG_STEP, "查询数据预期数据被回滚掉.");
    while (dataNum >= 0 && cycle > 0) {
        ret = ScanVertexLabelByNode(stmt, labelName, keyName, dataNum, dataNum + 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
        dataNum--;
        cycle--;
    }
    return secondCycles;
}

void TestWriteVertexLabelCommit(
    int secondCycles, GmcConnT *conn = g_conn_sync, GmcStmtT *stmt = g_stmt_sync, bool isNeedSleep = false)
{
    const char *labelName = "Vertex_095";
    const char *keyName = "index_vertex_01";
    int ret = GmcTransStart(conn, &g_prtrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dataNum1 = secondCycles;
    int cycle = secondCycles % 1000;
    int dataNum = secondCycles;
    while (dataNum >= 0 && cycle > 0) {
        if (secondCycles % 1000 == 0) {
            AW_FUN_Log(LOG_STEP, "dataNum =%d.", dataNum);
        }
        ret = InsertVetexLabelByNode(stmt, labelName, dataNum, dataNum + 1);
        dataNum = dataNum - 1;
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isNeedSleep) {
        sleep(120);
        ret = GmcTransCommit(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
        ret = GmcTransRollBack(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcTransCommit(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 预期第二次写入成功
    int cycle2 = dataNum1 % 1000;
    AW_FUN_Log(LOG_STEP, "查询数据预期第二次写入成功%d条.", dataNum1);
    while (dataNum1 >= 0 && cycle2 > 0) {
        ret = ScanVertexLabelByNode(stmt, labelName, keyName, dataNum1, dataNum1 + 1);
        // 事务超时回滚没有数据
        if (isNeedSleep) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        dataNum1--;
        cycle2--;
    }
}

void TestWriteVertexLabelNeedRollBack(
    int secondCycles, GmcConnT *conn = g_conn_sync, GmcStmtT *stmt = g_stmt_sync, bool isNeedSleep = false)
{
    const char *labelName = "Vertex_095";
    const char *keyName = "index_vertex_01";
    int ret = GmcTransStart(conn, &g_prtrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dataNum1 = secondCycles;
    int cycle = secondCycles % 1000;
    int dataNum = secondCycles;
    while (secondCycles >= 0 && cycle > 0) {
        if (secondCycles % 1000 == 0) {
            AW_FUN_Log(LOG_STEP, "secondCycles =%d.", secondCycles);
        }
        ret = InsertVetexLabelByNode(stmt, labelName, dataNum, dataNum + 1);
        dataNum = dataNum - 1;
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待数据写入完成
    sleep(5);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预期第二次写入成功
    int cycle2 = dataNum1 % 1000;
    AW_FUN_Log(LOG_STEP, "查询数据预期第二次写入成功%d条.", dataNum1);
    while (dataNum1 >= 0 && cycle2 > 0) {
        ret = ScanVertexLabelByNode(stmt, labelName, keyName, dataNum1, dataNum1 + 1);
        // 事务超时回滚没有数据

        AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
        dataNum1--;
        cycle2--;
    }
}

// 异步事务回滚
void TestAsyncRollBack(GmcConnT *conn = g_conn_async)
{
    AsyncUserDataT data{0};
    int ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 存在动态内存满是收不到消息超时的情况
    EXPECT_TRUE(data.status == GMERR_OK || data.status == GMERR_REQUEST_TIME_OUT);
}
// session占用内存异常临时规避方案
void DisconnctAndConnect()
{
    AW_FUN_Log(LOG_STEP, "session占用内存异常临时规避方案.");
    AsyncUserDataT data{0};
    int ret = 0;
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}
// yang全打散删表
void TestDropAllBreakLabelAndNameSpace()
{
    int i = 0;
    int ret = 0;
    DisconnctAndConnect();
    AsyncUserDataT data{0};
    char edgelabelName[20] = "lableName";
    // 删表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

// yang创建乐观可重复读namespace
void TestCreateNameSpace()
{
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    AsyncUserDataT data{0};
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    int ret = 0;
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    ret = testWaitAsyncRecvOneThread(&data);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

// 写全打散数据直到内存满
int TestWriteAllBreakLabel(int dataNum = 0, bool isNeedSavePoint = false, bool isNeedRollBackSavePoint = false,
    bool isNeedRollBack = true, int savePointNum = 100, bool isNeedSleep = false)
{
    GmcBatchT *batch = NULL;

    int ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    system("gmsysview -q V\\$COM_DYN_CTX >mem1.txt");
    InsertAllBreakLabel(batch, g_stmt_async, 1);
    system("gmsysview -q V\\$COM_DYN_CTX >mem2.txt");
    int i = 2;
    char savepointName[10] = "sp";
    char pointName[10] = "NULL";
    (void)sprintf(pointName, "%s%d", savepointName, i);
    while (!ret && dataNum >= 0) {
        ret = MergeInsertAllBreakList(batch, g_stmt_async, i);
        if (i % 100 == 0) {
            AW_FUN_Log(LOG_STEP, "MergeInsertAllBreakList%d ret = %d", i, ret);
        }

        if (ret) {
            AW_FUN_Log(LOG_STEP, "record num is %d.", i);
            break;
        }
        if (!isNeedRollBack) {
            dataNum--;
        }
        i++;
        if (isNeedSavePoint && !ret && i < savePointNum) {
            ret = CreateSavepoint(g_conn_async, pointName);
            AW_FUN_Log(LOG_STEP, "CreateSavepoint num is %d.", i - 2);
        }
    }
    if (isNeedRollBackSavePoint) {
        RollbackSavepoint(g_conn_async, pointName);
    }
    if (ret || isNeedRollBack) {
        AW_FUN_Log(LOG_STEP, "事务回滚");
        TestAsyncRollBack();
    } else {
        if (isNeedSleep) {
            AW_FUN_Log(LOG_STEP, "超时事务回滚");
            sleep(120);
            ret = testTransCommitAsync(g_conn_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
            TestAsyncRollBack();
        } else {
            AW_FUN_Log(LOG_STEP, "事务提交");
            ret = testTransCommitAsync(g_conn_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    GmcBatchDestroy(batch);

    return i - 2;
}

// 部分打散建表
void TestCreatePartBreakLabel()
{
    int ret = 0;
    AsyncUserDataT data{0};
    const char *partConfigTrans =
        "{\"max_record_count\" : 10000, \"isFastReadUncommitted\":0, \"auto_increment\":1, \"yang_model\":1}";
    char *vlabelSchema = NULL;
    char *vlabelEdgeSchema = NULL;

    readJanssonFile("schema_file/Yang_095_PartBreaK.gmjson", &vlabelSchema);
    EXPECT_NE((void *)NULL, vlabelSchema);
    ret = TestYangCreateMulLabel(g_stmt_async, vlabelSchema, partConfigTrans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Yang_095_PartBreaK_Edge.gmjson", &vlabelEdgeSchema);
    EXPECT_NE((void *)NULL, vlabelEdgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelEdgeSchema, partConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    free(vlabelEdgeSchema);
}

void ModelCheck(GmcStmtT *stmt)
{
#ifdef FEATURE_YANG_VALIDATION
    int ret = 0;
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncWhenRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    if (checkData.validateRes == false) {
        AW_FUN_Log(LOG_INFO, "GmcYangValidateModelAsync result is false, failcount is %d.", checkData.failCount);
    }
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
#endif
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char
// *errMsg);*/
void AsyncValidateCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    ValidateParam *param = (ValidateParam *)userData;
    EXPECT_EQ(param->exceptStatus, status) << errMsg;
    // 只有返回无异常时才去校验mandatory
    if (GMERR_OK == status) {
        AW_MACRO_EXPECT_EQ_INT(validateRes.validateRes, param->validateRes.validateRes);
        AW_MACRO_EXPECT_EQ_INT(0, param->validateRes.failCount);
    }
    (*(param->step))++;
}
void WhenDataCheck(
    GmcStmtT *stmt, bool exceptRes, int32_t exceptStatus = GMERR_OUT_OF_MEMORY, uint32_t apiSupport = GMERR_OK)
{
    int ret = 0;
#ifdef FEATURE_YANG_VALIDATION
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes{.validateRes = exceptRes};
    ValidateParam param = {.step = &step, .exceptStatus = exceptStatus, .validateRes = validateRes};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_WHEN, .cfgJson = NULL};
    ret = GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(apiSupport, ret);

    ret = testWaitAsyncWhenDataRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(exceptStatus, param.exceptStatus);
#endif
}
string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue)
                            : GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        cout << "actual diff：\n" << res;
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}
void FetchDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
void testFetchAndDeparseDiff(
    GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data, int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

// 无数据直接插入（merge/replace/insert），单子节点
static vector<string> expectDiffCreateBase = {"T0:create[(priKey(ID:1)),(NULL)]\n"
                                              "T0.F0:create(100)\n"
                                              "T0.F1:create(100)\n"
                                              "T0.F2:create(string)\n"
                                              "T0.T1:create[(priKey(PID:1)),(NULL)]\n"
                                              "T1.ID:create(1)\n"
                                              "T1.F0:create(100)\n"
                                              "T1.F1:create(100)\n"
                                              "T1.F2:create(string)\n"};
// 开启diff
bool g_isOpenDiff = false;

// when数据校验
bool g_isWhenCheck = false;
// 写部分打散数据知道内存满
int TestWritePartBreakLabel(int dataNum = 0, bool isNeedSavePoint = false, bool isNeedRollBackSavePoint = false,
    bool isNeedRollBack = true, int savePointNum = 100, bool isNeedSleep = false)
{
    GmcBatchT *batch = NULL;

    // 开启乐观可重复读写数据直到内存满
    int ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    InsertPartBreakLabel(batch, g_stmt_async, 1);
    int i = 2;
    char savepointName[10] = "sp";
    char pointName[10] = "NULL";
    (void)sprintf(pointName, "%s%d", savepointName, i);
    while (!ret) {
        ret = MergeInsertPartBreakList(batch, g_stmt_async, i);
        i++;
        if (dataNum && dataNum < i) {
            break;
        }
        if (isNeedSavePoint && i < savePointNum && !ret) {
            printf("CreateSavepoint begin\n");
            ret = CreateSavepoint(g_conn_async, pointName);
            printf("CreateSavepoint = %d\n", i);
        }
    }
    if (g_isOpenDiff) {
        AsyncUserDataT data = {0};
        testFetchAndDeparseDiff(
            g_stmt_async, batch, expectDiffCreateBase, data, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    }
    if (g_isWhenCheck) {
        // 数据校验
        WhenDataCheck(g_stmt_async, true);
    }
    if (isNeedRollBackSavePoint) {
        RollbackSavepoint(g_conn_async, pointName);
    }
    if (ret || isNeedRollBack || g_isWhenCheck || g_isOpenDiff) {
        AW_FUN_Log(LOG_STEP, "事务回滚");
        TestAsyncRollBack();
    } else {
        if (isNeedSleep) {
            AW_FUN_Log(LOG_STEP, "超时事务回滚");
            sleep(120);
            ret = testTransCommitAsync(g_conn_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

            TestAsyncRollBack();
        } else {
            AW_FUN_Log(LOG_STEP, "事务提交");
            ret = testTransCommitAsync(g_conn_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    GmcBatchDestroy(batch);
    return i - 2;
}
// yang部分打散删表
void TestDropPartBreakLabelAndNameSpace()
{
    int i = 0;
    int ret = 0;
    AsyncUserDataT data{0};
    // 删边删表
    AW_FUN_Log(LOG_STEP, "删边.");
    i = 3;
    char edgelabelName[20] = "lableName";
    // 删表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

// 图模型线程函数
void *ThreadVertexRollBack(void *args)
{
    int valueIndex = *(int *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务写数据直到内存满事务回滚
    int secondCycles = 0;
    secondCycles = TestWriteVertexLabelRollBack(conn, stmt, valueIndex * 1000);

    // 第二次开启事务写数据预期成功
    AW_FUN_Log(LOG_STEP, "第二次开启事务写数据预期成功.");
    TestWriteVertexLabelNeedRollBack(secondCycles, conn, stmt);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadVertexCommit(void *args)
{
    int valueIndex = *(int *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务写数据直到内存满事务回滚
    int secondCycles = 0;
    secondCycles = TestWriteVertexLabelRollBack(conn, stmt, valueIndex * 1000);

    // 第二次开启事务写数据预期成功
    AW_FUN_Log(LOG_STEP, "第二次开启事务写数据预期成功.");
    TestWriteVertexLabelCommit(secondCycles, conn, stmt);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 图模型线程函数部分事务提交部分事务回滚
void *ThreadVertex(void *args)
{
    int valueIndex = *(int *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务写数据直到内存满事务回滚
    int secondCycles = 0;
    secondCycles = TestWriteVertexLabelRollBack(conn, stmt, valueIndex * 1000);

    // 第二次开启事务写数据预期成功
    AW_FUN_Log(LOG_STEP, "第二次开启事务写数据预期成功.");
    if (valueIndex % 2 == 0) {
        TestWriteVertexLabelNeedRollBack(secondCycles, conn, stmt);
    } else {
        TestWriteVertexLabelCommit(secondCycles, conn, stmt);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 事务超时回滚
void *ThreadVertexCommitTimeout(void *args)
{
    int valueIndex = *(int *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务写数据直到内存满事务回滚
    int secondCycles = 0;
    secondCycles = TestWriteVertexLabelRollBack(conn, stmt, valueIndex * 1000);

    // 第二次开启事务写数据预期成功
    AW_FUN_Log(LOG_STEP, "第二次开启事务写数据预期成功.");
    TestWriteVertexLabelCommit(secondCycles, conn, stmt, true);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 图模型
void TestWriteAllBreakRollBack(GmcConnT *conn, GmcStmtT *stmt, bool isNeedSleep = false, int32_t dataValue = 2)
{
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    int ret = 0;
    int i = dataValue;
    while (true) {
        i = dataValue;
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 开启乐观可重复读写数据直到内存满
        ret = testTransStartAsync(conn, g_mstrx_config);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置批处理batch参数
        ret = testBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 第一次写数据写表直到内存满，事务提交失败，事务回滚
        while (!ret) {
            ret = MergeInsertAllBreakList(batch, stmt, i, conn);
            i++;
            usleep(10);
        }
        AW_FUN_Log(LOG_STEP, "(GMERR_OUT_OF_MEMORY %d 事务回滚", i);
        sleep(15);
        TestAsyncRollBack(conn);
        if (ret == GMERR_CONNECTION_FAILURE) {
            sleep(1);
            continue;
        } else {
            break;
        }
    }
    sleep(30);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testTransStartAsync(conn, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    int cycle = (i - 2) % 1000;
    while (!ret && cycle > 0) {
        ret = MergeInsertPartBreakList(batch, stmt, i, conn);
        i--;
        cycle--;
        usleep(10);
    }
    if (isNeedSleep) {
        AW_FUN_Log(LOG_STEP, "事务超时等待");
        sleep(125);
        ret = testTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
        AW_FUN_Log(LOG_STEP, "超时事务回滚");
        TestAsyncRollBack(conn);
    } else {
        AW_FUN_Log(LOG_STEP, "事务回滚");
        TestAsyncRollBack(conn);
    }
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestWriteAllBreakOrCommit(GmcConnT *conn, GmcStmtT *stmt, bool isNeedSleep = false, int32_t dataValue = 2)
{
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 开启乐观可重复读写数据直到内存满
    int ret = testTransStartAsync(conn, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    int i = dataValue;
    while (!ret) {
        ret = MergeInsertAllBreakList(batch, stmt, i, conn);
        i++;
        usleep(10);
    }
    AW_FUN_Log(LOG_STEP, "(GMERR_OUT_OF_MEMORY %d 事务回滚", i);
    sleep(30);
    TestAsyncRollBack(conn);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    sleep(30);
    ret = testTransStartAsync(conn, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    int cycle = (i - 2) % 1000;
    while (!ret && cycle > 0) {
        ret = MergeInsertPartBreakList(batch, stmt, i, conn);
        i--;
        usleep(10);
        cycle--;
    }
    if (isNeedSleep) {
        AW_FUN_Log(LOG_STEP, "超时事务回滚");
        sleep(125);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
        TestAsyncRollBack(conn);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "事务提交成功");
    }
    GmcBatchDestroy(batch);
}
// 树模型
int TestWritePartBreakRollBack(GmcConnT *conn, GmcStmtT *stmt, bool isNeedSleep = false, int32_t dataValue = 2)
{
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    // 开启乐观可重复读写数据直到内存满
    int ret = testTransStartAsync(conn, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    int i = dataValue;
    while (!ret) {
        ret = MergeInsertPartBreakList(batch, stmt, i, conn);
        usleep(1000);
        i++;
    }
    AW_FUN_Log(LOG_STEP, "(GMERR_OUT_OF_MEMORY 事务回滚", i);
    AW_FUN_Log(LOG_STEP, "事务回滚");
    sleep(30);
    TestAsyncRollBack(conn);
    sleep(30);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testTransStartAsync(conn, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    int cycle2 = (i - 2) % 10000;
    while (!ret && ((i - 2) % 10000) > 0 && cycle2 > 0) {
        ret = MergeInsertPartBreakList(batch, stmt, i, conn);
        i--;
        cycle2--;
    }
    if (isNeedSleep) {
        AW_FUN_Log(LOG_STEP, "事务超时等待");
        sleep(125);
        ret = testTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
        TestAsyncRollBack(conn);
    } else {
        AW_FUN_Log(LOG_STEP, "事务回滚");
        TestAsyncRollBack(conn);
    }
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int TestWritePartBreakCommit(GmcConnT *conn, GmcStmtT *stmt, bool isNeedSleep = false, int32_t dataValue = 2)
{
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    // 开启乐观可重复读写数据直到内存满
    int ret = testTransStartAsync(conn, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    int i = dataValue;
    while (!ret) {
        ret = MergeInsertPartBreakList(batch, stmt, i, conn);
        i++;
    }
    AW_FUN_Log(LOG_STEP, "(GMERR_OUT_OF_MEMORY %d 事务回滚", i);
    AW_FUN_Log(LOG_STEP, "事务回滚");
    sleep(30);
    TestAsyncRollBack(conn);
    sleep(30);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testTransStartAsync(conn, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    int cycle2 = (i - 2) % 10000;
    while (!ret && ((i - 2) % 10000) > 0 && cycle2 > 0) {
        ret = MergeInsertPartBreakList(batch, stmt, i, conn);
        i--;
        cycle2--;
    }
    if (isNeedSleep) {
        AW_FUN_Log(LOG_STEP, "事务超时等待");
        sleep(125);
        ret = testTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
        TestAsyncRollBack(conn);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "事务提交");
        ret = testTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int TestWritePartBreak(GmcConnT *conn, GmcStmtT *stmt, int32_t dataValue = 2)
{
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    int ret = 0;
    ret = testTransStartAsync(conn, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第一次写数据写表直到内存满，事务提交失败，事务回滚
    int cycle = dataValue;
    while (cycle > 0) {
        ret = MergeInsertPartBreakList(batch, stmt, cycle, conn);
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "事务提交");
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return ret;
}

// 图模型线程函数
void *WriteAllBreakRollBack(void *args)
{
    int ret;
    int valueIndex = *(int *)args;
    GmcBatchT *batch = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    TestWriteAllBreakRollBack(conn, stmt, true, valueIndex * 1000);
    return NULL;
}

// 图模型线程函数
void *WriteAllBreakCommit(void *args)
{
    int ret;
    int valueIndex = *(int *)args;
    GmcBatchT *batch = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    TestWriteAllBreakRollBack(conn, stmt, true, valueIndex * 1000);
    return NULL;
}

// 树模型线程函数
void *WritePartBreakRollBack(void *args)
{
    int ret;
    int valueIndex = *(int *)args;
    GmcBatchT *batch = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    TestWritePartBreakRollBack(conn, stmt, true, valueIndex * 10000);
    return NULL;
}

void *WritePartBreakCommit(void *args)
{
    int ret;
    int valueIndex = *(int *)args;
    GmcBatchT *batch = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    if (valueIndex == 1) {
        TestWritePartBreakCommit(conn, stmt, false, valueIndex * 10000);
    } else {
        TestWritePartBreakCommit(conn, stmt, true, valueIndex * 10000);
    }
    return NULL;
}

// 042.yang设置连接为内存阈值为最小值，进行DDL语义校验
TEST_F(YangMemLimByConn, DataLog_066_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcBatchT *batch = NULL;

    int ret = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    int32_t expectLimitValue = 200;  // 2MB 0KB 0B
    int32_t actualLimitValue = 0;
    // 建连
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    connOptions.srvMemCtxLimit = expectLimitValue / 100;  // MB
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 乐观可重复读事务配置
    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;

    // yang创建乐观可重复读namespace
    TestCreateNameSpace();
    // 建表
    TestCreatePartBreakLabel();
    system("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=T0");

    // 进行模型定义校验
    ModelCheck(g_stmt_async);
    // 删yang表
    TestDropPartBreakLabelAndNameSpace();

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.yang设置连接为内存阈值为最小值，进行DML校验
TEST_F(YangMemLimByConn, DataLog_066_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcBatchT *batch = NULL;

    // 内存不足回去lasterror失败
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    // lasterror获取失败导致errorpath解析失败
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

    int ret = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    int32_t expectLimitValue = 200;  // 2MB 0KB 0B
    int32_t actualLimitValue = 0;
    // 建连
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    connOptions.srvMemCtxLimit = expectLimitValue / 100;  // MB
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 乐观可重复读事务配置
    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;

    // yang创建乐观可重复读namespace
    TestCreateNameSpace();
    // 建表
    TestCreatePartBreakLabel();
    system("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=T0");
    // 进行模型定义校验
    ModelCheck(g_stmt_async);

    // 开启乐观可重复读写数据直到内存满
    int recordNum = TestWritePartBreakLabel();
    AW_FUN_Log(LOG_STEP, "GMERR_OUT_OF_MEMORY record num is %d.", recordNum);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn_async = NULL;
    g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    sleep(5);

    // 第二次写数据写表，事务提交成功
    g_isWhenCheck = true;
    recordNum = TestWritePartBreakLabel(recordNum - 10, false, false, false);
    AW_FUN_Log(LOG_STEP, "insert record success num is %d.", recordNum);
    system("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=T0");

    // 删yang表
    TestDropPartBreakLabelAndNameSpace();

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

#endif
