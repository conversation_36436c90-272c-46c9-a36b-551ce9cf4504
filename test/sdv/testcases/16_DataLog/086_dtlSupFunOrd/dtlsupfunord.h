/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: hotpatcblock.h
 * Description:
 * Author: luyang 00618033
 * Create: 2024-4-2
 */

#ifndef DTLSUPFUNORD_H
#define DTLSUPFUNORD_H

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <pthread.h>
#include <vector>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "share_function.h"
#include "DatalogRun.h"

#define PRINT_INFO 1
#define FILE_PATH 512
#define LABEL_NAME 512

const char *g_tbmlogName = "TbmRunLog.txt";

const char *g_msglogName = "msgNotifyRunLog.txt";

const char *g_funclogName = "funcLog.txt";

char g_hFile[FILE_PATH] = "../../../../../pub/include/";

char const *g_viewName = "V\\$PTL_DATALOG_PATCH_INFO";

char const *g_viewName1 = "V\\$DATALOG_PLAN_EXPLAIN_INFO";
char const *g_viewName2 = "V\\$CATA_NAMESPACE_INFO";

char g_cfgName[MAX_CMD_SIZE] = {0};

// 存放错误信息
char g_errorMsg[MAX_CMD_SIZE] = {0};

// 存放日志白名单错误码
char g_errorCode01[MAX_CMD_SIZE] = {0};
char g_errorCode02[MAX_CMD_SIZE] = {0};
char g_errorCode03[MAX_CMD_SIZE] = {0};

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

char g_tableName[128] = "capv5";
char g_configJson[128] = "{\"max_record_count\":100000, \"isFastReadUncommitted\": false}";

int g_threadWait = 0;
pthread_mutex_t g_threadLock;

const char *g_schemaJson1 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int64"},
            {"name" : "b", "type" : "int64"},
            {"name" : "c", "type" : "int64"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

/*--------------------------------------编译、加载------------------------------------*/
void CompileTest(char *inputFilePath, char *outputFilePath, char *soName, bool haveUdf)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char datalogFile[FILE_PATH] = {0};
    char outputFile[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    char udfFile[FILE_PATH] = {0};
    // 赋值
    (void)sprintf(datalogFile, "%s/%s.d", inputFilePath, soName);
    (void)sprintf(outputFile, "%s/%s.c", outputFilePath, soName);
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    if (haveUdf) {
        (void)sprintf(udfFile, "%s/%s_udf.c", inputFilePath, soName);
    }
    // .d->.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, datalogFile, outputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    if (haveUdf) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile,
            outputFile, udfFile, libName);
    } else {
        (void)snprintf(
            command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile, outputFile, libName);
    }
    system(command);
}

/*--------------------------------编译生成patch.c+full.d、加载升级patchso---------------------------------*/
void CompileUpgradeAndRollBackTest(char *inputFilePath, char *outputFilePath, char *soName, bool havePatchUdf,
    int upgradeCnt)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    char rollLibName[FILE_PATH] = {0};
    char udfPatchFile[FILE_PATH] = {0};

    if (upgradeCnt == 1) {
        (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
        (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
        (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, upgradeCnt + 1);
        (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);
        (void)snprintf(rollbackPatchCName, FILE_PATH, "%s/%s_patch_rollback.c", outputFilePath, soName);
        if (havePatchUdf) {
            (void)snprintf(udfPatchFile, FILE_PATH, "%s/%s_patch_udf.c", outputFilePath, soName);
        }
    } else {
        (void)snprintf(ruleFile, FILE_PATH, "%s/%s_ruleV%d.d", inputFilePath, soName, upgradeCnt);
        (void)snprintf(patchFile, FILE_PATH, "%s/%s_ruleV%d_patch.d", inputFilePath, soName, upgradeCnt);
        (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, upgradeCnt + 1);
        (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_ruleV%d_patch.c", outputFilePath, soName, upgradeCnt);
        (void)snprintf(rollbackPatchCName, FILE_PATH, "%s/%s_ruleV%d_patch_rollback.c", outputFilePath, soName,
            upgradeCnt);
        if (havePatchUdf) {
            (void)snprintf(udfPatchFile, FILE_PATH, "%s/%s_ruleV%d_patch_udf.c", outputFilePath, soName, upgradeCnt);
        }
    }
    (void)snprintf(libName, FILE_PATH, "%s/%s_patchV%d.so", outputFilePath, soName, upgradeCnt + 1);
    (void)snprintf(rollLibName, FILE_PATH, "%s/%s_rollbackV%d.so", outputFilePath, soName, upgradeCnt + 1);
    // rule.d + patch.d -> patch.c + ruleV2.d + patch_rollback.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    if (havePatchUdf) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile,
            patchCOutputFile, udfPatchFile, libName);
    } else {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile,
            patchCOutputFile, libName);
    }
    system(command);
    // 生成回滚so
    (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile,
        rollbackPatchCName, rollLibName);
    system(command);
}

// 加载升级的so
int TestLoadUpgradeDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(
        loadCommand, commandSize, "%s/gmimport -s %s -c datalog -upgrade %s", g_toolPath, g_connServer, soName);

    return system(loadCommand);
}

// 加载回滚的so
int TestLoadRollbackDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(
        loadCommand, commandSize, "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer, soName);

    return system(loadCommand);
}

/*----------------------------------------表结构--------------------------------------------*/
#pragma pack(1)
// (a, b, c)
typedef struct TagC3Int8 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} C3Int8T;

typedef struct TagC3Int8C1Int4 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int32_t d;
} C3Int8C1Int4T;

typedef struct TagC4Int8 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} C4Int8T;

typedef struct TagC5Int8 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
    int64_t e;
} C5Int8T;

typedef struct TagC1Int1C2Int4 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t ifIndex;
    int8_t attr;
    int32_t value;
} C1Int1C2Int4T;

typedef struct TagThreadData {
    char labelName[128];
    C3Int8T *obj;
    int objLen;
} ThreadDataT;

#pragma pack(0)

// struct模式设置
int C3Int8Set(GmcStmtT *stmt, void *t)
{
    C3Int8T *obj = (C3Int8T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int C5Int8Set(GmcStmtT *stmt, void *t)
{
    C5Int8T *obj = (C5Int8T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_INT64, &obj->e, sizeof(obj->e));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] e: %d, ret = %d.", obj->e, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int C1Int1C2Int4Set(GmcStmtT *stmt, void *t)
{
    C1Int1C2Int4T *obj = (C1Int1C2Int4T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] ifIndex: %d, ret = %d.", obj->ifIndex, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "attr", GMC_DATATYPE_INT8, &obj->attr, sizeof(obj->attr));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] attr: %d, ret = %d.", obj->attr, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "value", GMC_DATATYPE_INT32, &obj->value, sizeof(obj->value));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] value: %d, ret = %d.", obj->value, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }
    return ret;
}

int C3Int8TimeoutSet(GmcStmtT *stmt, void *t)
{
    C3Int8T *obj = (C3Int8T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    // 改成按分钟为颗粒度
    int64_t cValue = int64_t(obj->c * 1000 * 60);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &cValue, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// 输出表数据校验
int C3Int8Cmp(const C3Int8T *st1, const C3Int8T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] c, st1: %ld, st2: %ld.", st1->c, st2->c)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                    st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int C5Int8Cmp(const C5Int8T *st1, const C5Int8T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] c, st1: %ld, st2: %ld.", st1->c, st2->c)) : ({});
            break;
        }
        if (st1->d != st2->d) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] d, st1: %ld, st2: %ld.", st1->d, st2->d)) : ({});
            break;
        }
        if (st1->e != st2->e) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] e, st1: %ld, st2: %ld.", st1->e, st2->e)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                    st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int C3Int8C1Int4Cmp(const C3Int8C1Int4T *st1, const C3Int8C1Int4T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] c, st1: %ld, st2: %ld.", st1->c, st2->c)) : ({});
            break;
        }
        if (st1->d != st2->d) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] d, st1: %ld, st2: %ld.", st1->d, st2->d)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] upgradeVersion, st1: %d, st2: %d.",
                    st1->upgradeVersion, st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

// 不校验过期字段值
int C3Int8TimeoutCmp(const C3Int8T *st1, const C3Int8T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] upgradeVersion, st1: %d, st2: %d.",
                    st1->upgradeVersion, st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

// 读表的数据
// 读（a, b, c）
int C3Int8Get(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d", getObj.a, getObj.b, getObj.c);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8Cmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}
// 读（a, b, c, d, e）
int C5Int8Get(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C5Int8T *checkObj = (C5Int8T *)t;
    C5Int8T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'd' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "e", &getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'e' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld %ld %ld", getObj.a, getObj.b, getObj.c, getObj.d,
            getObj.e, getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %dl", getObj.a, getObj.b, getObj.c, getObj.d, getObj.e);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C5Int8Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C5Int8Cmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}
// 读(a, b, c, d)
int C3Int8C1Int4Get(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8C1Int4T *checkObj = (C3Int8C1Int4T *)t;
    C3Int8C1Int4T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'd' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Gett] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d %d", getObj.a, getObj.b, getObj.c, getObj.d,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d", getObj.a, getObj.b, getObj.c, getObj.d);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8C1Int4Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8C1Int4Cmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// 读过期表的表
int C3Int8TimeoutGet(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld %ld %ld", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld", getObj.a, getObj.b, getObj.c);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8TimeoutCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8TimeoutCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// 读(a, b, c), notify表
int C3Int8RescGet(GmcStmtT *stmt, void *t, int len, bool isResourcePubSub)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'upgradeVersion' fail, ret = %d.", ret);
        return ret;
    }

    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO][C3Int8RescGet]OUT_LABEL_RESULT: %ld %ld %ld %d %d ", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    ret = -1;
    // 校验
    for (int i = 0; i < len; i++) {
        if (C3Int8Cmp(&getObj, &checkObj[i], NULL, NULL) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8Cmp(&getObj, &checkObj[0], NULL, NULL);
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

int C3Int8C1Int4RescGet(GmcStmtT *stmt, void *t, int len, bool isResourcePubSub)
{
    C3Int8C1Int4T *checkObj = (C3Int8C1Int4T *)t;
    C3Int8C1Int4T getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    // pubsub 资源表
    if (isResourcePubSub) {
        getObj.d = 1;
    } else {
        ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'e' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'upgradeVersion' fail, ret = %d.", ret);
        return ret;
    }

    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO][C3Int8C1Int4RescGet]OUT_LABEL_RESULT: %ld %ld %ld %d %d %d ", getObj.a, getObj.b,
            getObj.c, getObj.d, getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    ret = -1;
    // 校验
    for (int i = 0; i < len; i++) {
        if (C3Int8C1Int4Cmp(&getObj, &checkObj[i], NULL, NULL) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8C1Int4Cmp(&getObj, &checkObj[0], NULL, NULL);
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] count: %d.", getObj.dtlReservedCount);
    }
    // 设置字段发回服务端
    if (isResourcePubSub) {
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &getObj.a, sizeof(getObj.a));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] a: %d, ret = %d.", getObj.a, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &getObj.b, sizeof(getObj.b));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] b: %d, ret = %d.", getObj.b, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &getObj.c, sizeof(getObj.c));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] c: %d, ret = %d.", getObj.c, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &getObj.d, sizeof(getObj.d));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] d: %d, ret = %d.", getObj.d, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(
                LOG_ERROR, "[C3Int8C1Int4RescGet] dtlReservedCount: %d, ret = %d.", getObj.dtlReservedCount, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &getObj.upgradeVersion, sizeof(getObj.dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] upgradeVersion: %d, ret = %d.", getObj.dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

// 获取文件中数据，存到buffer中
int TestViewData(char *cmd, char **result)
{
    int ret = 0;
    FILE *fd = popen(cmd, "r");
    int size = 10240;
    *result = (char *)malloc(sizeof(char) * size);
    memset(*result, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat((char *)*result, buf);
    }

    ret = pclose(fd);
    return ret;
}
// 校验热补丁重做视图信息
int CheckHotPatchView(char *soName = NULL, const char *expectOutput = NULL)
{
    int ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s > test.txt", g_toolPath, g_viewName,
        g_connServer, soName);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *actualResult = NULL;
    (void)snprintf(command, MAX_CMD_SIZE, "cat test.txt");
    ret = TestViewData(command, &actualResult);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 预期值属于实际值的一部分
    const char *result = strstr(actualResult, expectOutput);
    if (result != NULL) {
        EXPECT_STRNE(NULL, result);
        free(actualResult);
    } else {
        free(actualResult);
        AW_FUN_Log(LOG_STEP, "There is no data what you need!");
        (void)SystemSnprintf("cat test.txt");
        return -1;
    }
    return ret;
}
// 获取动态内存视图和共享视图的字段值
int TestGetCTXStr(char *value, int len, char *view, char *filter)
{
    char command[1024] = {0};
    int ret = snprintf(command, sizeof(command),
        "%s/gmsysview -q V\\$%s -f CTX_NAME=\"%s\"| grep "
        "\"TOTAL_ALLOC_SIZE:\" | awk -F \":\" '{print $2}'",
        g_toolPath, view, filter);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, NULL, value, len);
    if (ret) {
        return FAILED;
    }
    return 0;
}
// 回滚rb_*.gmjson的数量
int GetRollBackGmjsonCnt(const char *folderPath, const char *prefix, const char *suffix, int *value)
{
    int ret = 0;
    int cnt = 0;
    DIR *dir;
    struct dirent *ent;
    if ((dir = opendir(folderPath)) != NULL) {
        while ((ent = readdir(dir)) != NULL) {
            if (ent->d_type == DT_REG) {  // 判断是否为普通文件
                char *filename = ent->d_name;
                if (strncmp(filename, prefix, strlen(prefix)) == 0 &&
                    strcmp(filename + strlen(filename) - strlen(suffix), suffix) == 0) {
                    cnt++;
                    if (cnt < 10) {
                        AW_FUN_Log(LOG_STEP, "filename is %s\n", filename);  // 输出符合要求的文件名
                    }
                }
            }
        }
        closedir(dir);
    } else {
        perror("Unable to open directory");
        return -1;
    }
    *value = cnt;
    return 0;
}

/*--------------------------------并发--------------------------------------------------*/
void *ThreadDtlUdfDyn(void *args)
{
    int ret = 0;
    int cnt = 0;
    while (true && cnt < 15) {
        // 进入udf
        sleep(1);
        // 查看V$COM_DTL_UDF_DYN_CTX视图
        (void)snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, "V\\$COM_DTL_UDF_DYN_CTX", g_connServer);
        system(g_command);
        ret = executeCommand(g_command, "CTX_NAME: dtl_ext_func_ns1_func", "PARENT_CTX_NAME: datalog");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestGetPatchStateStr(char *value, int len, char *soName)
{
    char command[1024] = {0};
    int ret = snprintf(command, sizeof(command), "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep PATCH_STATE", g_toolPath,
        g_viewName, g_connServer, soName);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, NULL, value, len);
    if (ret) {
        return FAILED;
    }
    return 0;
}

// 并发查询热补丁视图
void *ThreadScanPatchView(void *args)
{
    char *soName = (char *)args;
    char patchState[128] = {0};
    // 循环并发查询视图
    while (1) {
        //  查看热补丁视图的状态
        int ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 重做成功或者重做失败跳出循环
        // 变更4月20号，重做失败会回滚：REDO_FAIL_ROLL_BACK_SUC和REDO_FAIL_ROLL_BACK_FAIL状态
        if (strstr(patchState, "SUCCESS") != NULL || strstr(patchState, "REDO_FAIL_ROLL_BACK_SUC") != NULL ||
            strstr(patchState, "REDO_FAIL_ROLL_BACK_FAIL")) {
            break;
        }
    }
}

// 获取CATA_VERTEX_LABEL_INFO视图中，datalog表的记录
int GetCataDatalogCount(int *value, const char *labelname)
{
    int ret = 0;
    char command[1024] = {0};
    ret = snprintf(
        command, 1024, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s | grep index", labelname);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

// 非轻量化创建kv表并插入数据
int CreateKvTable()
{
    int ret = 0;
    // 创建kv表
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvDropTable(stmt, g_tableName);
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "para1";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char key1[32] = "para2";
    char value1[32] = "aaaaaa";
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1) + 1;
    kvInfo1.value = value1;
    kvInfo1.valueLen = strlen(value1) + 1;
    ret = GmcKvSet(stmt, kvInfo1.key, kvInfo1.keyLen, kvInfo1.value, kvInfo1.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo2 = {0};
    char key2[32] = "para3";
    char value2[64] = {0};
    memset(value2, 'c', 63);
    kvInfo2.key = key2;
    kvInfo2.keyLen = strlen(key2) + 1;
    kvInfo2.value = value2;
    kvInfo2.valueLen = strlen(value2) + 1;
    ret = GmcKvSet(stmt, kvInfo2.key, kvInfo2.keyLen, kvInfo2.value, kvInfo2.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo3 = {0};
    char key3[32] = "para4";
    int64_t value3 = 1000;
    kvInfo3.key = key3;
    kvInfo3.keyLen = strlen(key3) + 1;
    kvInfo3.value = &value3;
    kvInfo3.valueLen = sizeof(int64_t);
    ret = GmcKvSet(stmt, kvInfo3.key, kvInfo3.keyLen, kvInfo3.value, kvInfo3.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview show capv5");
    return ret;
}

int ReadTablePKScan(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int64_t aValue, int64_t bValue, int64_t expectCValue)
{
    int ret = 0;
    int32_t upVerVal = -1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取upgradeVersion值并设置
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &aValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &bValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish;
    bool isNull = true;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int64_t readC;
        ret = GmcGetVertexPropertyByName(stmt, "c", &readC, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectCValue, readC);
    }
    return ret;
}

int TestGetNamespaceIDVal(int *value, const char *nsName)
{
    char command[1024] = {0};
    int ret = snprintf(command, sizeof(command),
        "%s/gmsysview -q %s -s %s -f NAMESPACE_NAME=%s | grep "
        "NAMESPACE_ID | awk '{print $2}' | sed 's/ //g'",
        g_toolPath, g_viewName2, g_connServer, nsName);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

// 校验不同ns空间的so的执行计划
int CheckPlan(GmcConnT *conn, GmcStmtT *stmt, const char *nsName, const char *patchSoName, const char *rollbackSoName)
{
    int ret = 0;
    // 获取命名空间ID
    int nsId = 0;
    ret = TestGetNamespaceIDVal(&nsId, nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 运行
    ret = GmcUseNamespace(stmt, nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(conn, stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看执行计划
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f NAMESPACE_ID=%d", g_toolPath, g_viewName1,
        g_connServer, nsId);
    ret = executeCommand(g_command, "  ->  DefaultDeltaMerge on Label(inp1)", "      ->  UDF: dtl_ext_func_func1",
        "        ->  IndexScan on Label(inp2) Using Index[0]=0", "          ->  UDF: dtl_ext_func_func",
        "          ->  SeqScan on Label(inp1)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, nsName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查看执行计划
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f NAMESPACE_ID=%d", g_toolPath, g_viewName1,
        g_connServer, nsId);
    ret = executeCommand(g_command, "  ->  DefaultDeltaMerge on Label(inp1)", "      ->  UDF: dtl_ext_func_func2",
        "        ->  UDF: dtl_ext_func_func1", "          ->  IndexScan on Label(inp2) Using Index[0]=0",
        "            ->  UDF: dtl_ext_func_func");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 校验so视图相关信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$PTL_DATALOG_SO_INFO -s %s -f NAMESPACE_NAME=%s",
        g_toolPath, g_connServer, nsName);
    ret = executeCommand(g_command, "TABLE_NUM: 10", "UDF_NUM: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 加载降级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, nsName));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查看执行计划
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f NAMESPACE_ID=%d", g_toolPath, g_viewName1,
        g_connServer, nsId);
    ret = executeCommand(g_command, "  ->  DefaultDeltaMerge on Label(inp1)", "      ->  UDF: dtl_ext_func_func1",
        "        ->  IndexScan on Label(inp2) Using Index[0]=0", "          ->  UDF: dtl_ext_func_func",
        "          ->  SeqScan on Label(inp1)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    return ret;
}

/*---------------------------------------------并发场景-----------------------------------------------------*/
// V2x事务锁时间变成30s；写12条数据36s
void *ThreadBatchWriteDatalogTable(void *args)
{
    AW_FUN_Log(LOG_STEP, "[ThreadBatchWriteDatalogTable]insert datalog table test start.");
    int ret = 0;
    const char *labelName = (char *)args;
    int recordNum = 12;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写表
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName, objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, labelName, g_connServer,
        g_testNameSpace);
    system(g_command);
    char message[128] = {0};
    (void)snprintf(message, 128, "%d", recordNum);
    ret = executeCommand(g_command, labelName, message);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[ThreadBatchWriteDatalogTable]insert datalog table test end.");
}

// 插入6条数据
void *ThreadBatchWriteDatalogTable1(void *args)
{
    AW_FUN_Log(LOG_STEP, "[ThreadBatchWriteDatalogTable]insert datalog table test start.");
    int ret = 0;
    const char *labelName = (char *)args;
    int recordNum = 6;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写表
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName, objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    system(g_command);
    char message[128] = {0};
    (void)snprintf(message, 128, "%d", recordNum);
    ret = executeCommand(g_command, labelName, message);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[ThreadBatchWriteDatalogTable]insert datalog table test end.");
}

// 并发写表拿不到锁
void *ThreadSingleWriteDatalogTable(void *args)
{
    AW_FUN_Log(LOG_STEP, "[ThreadSingleWriteDatalogTable]insert datalog table test start.");
    int ret = 0;
    const char *labelName = (char *)args;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn1[1] = {{1, 0, 5, 5, 1}};
    ret = writeRecord(conn, stmt, labelName, objIn1, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "[ThreadSingleWriteDatalogTable]insert datalog table test end.");
}

// 并发加载卸载so
void *ThreadLoadUnloadSo(void *args)
{
    AW_FUN_Log(LOG_STEP, "load unload so test start.");
    const char *soName = (char *)args;
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestUninstallDatalog(soName));
    AW_FUN_Log(LOG_STEP, "load unload so test end.");
}

// 并发加载升级so
void *ThreadLoadUpgradeSo(void *args)
{
    AW_FUN_Log(LOG_STEP, "load upgrade so test start.");
    const char *patchSoName = (char *)args;
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_STEP, "load upgrade so test end.");
}

// 并发加载降级so
void *ThreadLoadRollbackSo(void *args)
{
    AW_FUN_Log(LOG_STEP, "load upgrade so test start.");
    const char *rollbackSoName = (char *)args;
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_STEP, "load rollback so failed test end.");
}

#endif
