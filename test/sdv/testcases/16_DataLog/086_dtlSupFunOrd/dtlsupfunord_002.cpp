/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.1.0 迭代二Datalog支持指定函数join顺序--执行测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.08.12]
*****************************************************************************/
#include "dtlsupfunord.h"
#include "DatalogRun.h"

using namespace std;

class dtlsupfunord_002_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlsupfunord_002_test::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtlsupfunord_002_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

class dtlsupfunord_002_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlsupfunord_002_test2::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtlsupfunord_002_test2::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 001.规则含普通function，后面加{}，对输入表写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test001";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.规则只含表和 not join，后面加{}，对输入表写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test002";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 1] = {{1, 0, 1, 1, 1}, {2, 0, 2, 3, 5}};
    C3Int8T objIn3[recordNum - 2] = {{1, 0, 1, 2, 100}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum - 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 待开发问题单解好，再验证
/* ****************************************************************************
 Description  : 003.规则只含表join，含no_reorder，对输入表写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test003";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 1] = {{1, 0, 1, 1, 1}, {2, 0, 2, 3, 5}};
    C3Int8T objIn3[recordNum - 1] = {{1, 0, 1, 1, 100}, {1, 0, 2, 3, 100}};
    C3Int8T objIn4[recordNum - 1] = {{1, 0, 1, 1, 1}, {1, 0, 2, 3, 5}};
    C3Int8T objIn5[recordNum - 2] = {{1, 0, 1, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum - 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns2.inp2", objIn2, recordNum - 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn3, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn4, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.显示定义规则名，规则含普通function,function在规则中间，含no_reorder,对输入表非结构化单写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 1}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum -2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.显示定义规则名，规则含普通function，function在规则中间，含no_reorder,对输入表进行非结构化批写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 1}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}};
    // 非结构化批写
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum -2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.显示定义规则名，规则含普通function，function在规则中间，含no_reorder,对输入表进行结构化批写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 1}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}};
    // 结构化批写
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum -2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 待问题修复再看
/* ****************************************************************************
 Description  : 007.规则含1个function，function放在规则最后面含no_reorder，对输入表写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test005";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 1}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}};
    // 结构化批写
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum -2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.规则含多个function，含no_reorder前一个function出参做后一个函数的入参，对输入表写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test006";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 0, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 1}};
    C3Int8T objIn2[recordNum - 4] = {{1, 0, 0, 1, 1}};
    // 结构化批写
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum -4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 待问题解决后，测试
/* ****************************************************************************
 Description  : 009.规则含多个function以及join表达到上限16个含no_reorder，对输入表写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test007";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 1}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}};
    // 结构化批写
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp4", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp5", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum -2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.原始.d覆盖所有表类型，含namespace，规则含function左表覆盖所有表类型含no_reorder，对输入表写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test008";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[14] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8},
        {-1, 0, 1, 1, 2}, {-1, 0, 1, 2, 3}, {-1, 0, 2, 2, 4}, {-1, 0, 3, 3, 6}, {-1, 0, 4, 4, 8}, {1, 1, 1, 1, 2},
        {1, 1, 2, 2, 4}, {1, 1, 4, 4, 8}, {1, 1, 5, 4, 9}};
    // pubsub资源型表推送的数据
    C3Int8C1Int4T objPub2[14] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 2, 2, 1}, {1, 0, 2, 2, 4, 1}, {1, 0, 3, 3, 3, 1},
        {1, 0, 4, 4, 8, 1}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifyrs2";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfoout1.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 14;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系, pubsub资源表
    char *sub_info02 = NULL;
    readJanssonFile("./schema_file/subInfors2.json", &sub_info02);
    EXPECT_NE((void *)NULL, sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C3Int8C1Int4RescGet;
    userData02->funcType = 1;
    userData02->objLen = 5;
    userData02->obj = objPub2;
    userData02->isResourcePubSub = true;
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallback, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 1}, {1, 0, 2, 2, 1}, {1, 0, 3, 3, 1}, {1, 0, 4, 4, 1}};
    C3Int8C1Int4T objIn4[8] = {{1, 0, 1, 1, 2, 0}, {1, 0, 1, 2, 2, 1}, {1, 0, 2, 2, 4, 2}, {1, 0, 3, 3, 3, 3},
        {1, 0, 4, 4, 8, 4}, {1, 0, 2, 2, 2, 5}, {1, 0, 4, 4, 4, 6}, {1, 0, 8, 8, 8, 7}};
    C3Int8T objTimeout[recordNum] = {{1, 0, 1, 1, -1}, {1, 0, 1, 2, -3}, {2, 0, 2, 6, 4}, {3, 0, 3, 3, 3},
        {4, 0, 4, 4, 8}};
    C3Int8T objTimeout2[recordNum] = {{2, 0, 2, 2, 9}, {2, 0, 2, 4, 7}, {2, 0, 2, 6, 4}, {3, 0, 3, 3, 3},
        {4, 0, 4, 4, 8}};
    // 校验输入表
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp6", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp7", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp8", objTimeout, recordNum, C3Int8TimeoutSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待过期表过期
    sleep(3);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.inp8", objTimeout2, recordNum, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp8 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out4", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out4 read complete!!!");
    // 校验out5输出表
    ret = readRecord(g_conn, g_stmt, "ns1.out5", objIn4, 8, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out5 read complete!!!");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);

    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    int tableCnt01 = 0;
    // 查看catalog视图
    ret = GetCataDatalogCount(&tableCnt01, "out1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验表的数目
    AW_FUN_Log(LOG_DEBUG, "卸载so之后，tableCnt01 value：%d", tableCnt01);
    AW_MACRO_EXPECT_EQ_INT(tableCnt01, 0);

    // 查看tbm表记录
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_tbmlogName);
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_msglogName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.规则含union_delete级联删除，含no_reorder,对输入表写数据及删数据，查询数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test009";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 1}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}, {1, 0, 4, 4, 1}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 10}};
    C3Int8T objIn4[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}};
    // 非结构化批写
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn3, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn4, recordNum -2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn4, recordNum -2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    C3Int8T objIn5[recordNum] = {{-1, 0, 1, 1, 1}, {-1, 0, 1, 2, 3}, {-2, 0, 2, 3, 5}, {-3, 0, 2, 4, 6},
        {-5, 0, 4, 4, 1}};
    C3Int8T objIn6[0] = {};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "ns1.inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "ns1.inp1", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "ns1.inp3", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "ns1.inp3", "0");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn6, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 two read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn6, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 two read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.function中含acess_delta、access_current、access_kv；所在规则含no_reorder,对输入表写数据，查询表中数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test010";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}, {3, 0, 2, 4, 6}, {5, 0, 4, 4, 1}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}};
    C3Int8T objIn3[recordNum + 2] = {{1, 0, 1, 1, 1}, {2, 0, 1, 2, 3}, {3, 0, 2, 3, 5}, {4, 0, 2, 4, 6},
        {5, 0, 4, 4, 1}, {1, 0, 1, 1, 2}, {1, 0, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 4, 6}, {1, 0, 4, 4, 1}};
    // 结构化批写
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, true, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.inp3", objIn3, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum -2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out2 read complete!!!");
    (void)SystemSnprintf("cat /root/_datalog_/%s", g_funclogName);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.原始.d规则不含no_reorder,patch.d修改规则新增{}.;对输入表写数据，加载升降级so，并对输入表写数据，查询表数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test011";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    // 主键读输出表中的1条数据进行校验
    ret = ReadTablePKScan(g_conn, g_stmt, "out1", 1, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 校验升级之后重做的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out1", 2, 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级后对输入表插入数据
    C3Int8T objIn5[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn5, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn5, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    // 加载降级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    system("gmsysview count out1");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn7[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn8[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out1 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.原始.d多个规则存在含function含no_reorder，block 0模式下，patch.d对新增输入表与原先含no_reorder规则进行join，加载升降级so，查询数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test012";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record out2");
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 对inp5写数据
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn7[recordNum - 3] = {{1, upVerVal, 1, 2, 3}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn7, recordNum - 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    system("gmsysview record out2");
    ret = readRecord(g_conn, g_stmt, "out3", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");

    // 校验升级之后重做的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out1", 2, 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级后对输入表插入数据
    C3Int8T objIn9[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    // 加载降级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn10[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn11[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    C3Int8T objIn12[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    C3Int8T objIn13[recordNum - 3] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 3, 3, 6}};
    C3Int8T objIn14[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn12, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn13, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn14, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out3 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.原始.d多个规则存在含function含no_reorder，block 0模式下，patch.d修改原先含no_reorder规则，加载升降级so，查询数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test013";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record out2");
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 6}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn7[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 2, 100}, {1, upVerVal, 2, 2, 100},
        {1, upVerVal, 3, 3, 100}, {1, upVerVal, 4, 4, 100}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn7, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    system("gmsysview record out2");
    ret = readRecord(g_conn, g_stmt, "out3", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");

    // 校验升级之后重做的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out1", 2, 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级后对输入表插入数据
    C3Int8T objIn9[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    // 加载降级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn10[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn11[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    C3Int8T objIn12[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    C3Int8T objIn13[recordNum - 3] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 3, 3, 6}};
    C3Int8T objIn14[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn12, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn13, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn14, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out3 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.原始.d多个规则存在含function含no_reorder，block 1模式下，patch.d修改原先含no_reorder规则，加载升降级so，查询数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test014";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record out2");
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 6}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn7[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 2, 100}, {1, upVerVal, 2, 2, 100},
        {1, upVerVal, 3, 3, 100}, {1, upVerVal, 4, 4, 100}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn7, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    system("gmsysview record out2");
    ret = readRecord(g_conn, g_stmt, "out3", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");

    // 校验升级之后重做的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out1", 2, 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级后对输入表插入数据
    C3Int8T objIn9[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    // 加载降级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn10[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn11[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    C3Int8T objIn12[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    C3Int8T objIn13[recordNum - 3] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 3, 3, 6}};
    C3Int8T objIn14[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn12, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn13, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn14, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out3 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.原始.d多个规则存在含function含no_reorder，block 0模式下，patch.d修改原先含no_reorder规则的function实现，加载升降级so，查询数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test015";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record out2");
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 2}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 9}, {1, upVerVal, 4, 4, 16}};
    C3Int8T objIn7[recordNum - 3] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 2, 2, 4}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 2, 100}, {1, upVerVal, 2, 2, 100},
        {1, upVerVal, 3, 3, 100}, {1, upVerVal, 4, 4, 100}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn7, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    system("gmsysview record out2");
    ret = readRecord(g_conn, g_stmt, "out3", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");

    // 校验升级之后重做的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out1", 2, 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级后对输入表插入数据
    C3Int8T objIn9[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    // 加载降级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn10[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn11[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    C3Int8T objIn12[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    C3Int8T objIn13[recordNum - 3] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 3, 3, 6}};
    C3Int8T objIn14[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn12, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn13, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn14, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out3 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.原始.d多个规则存在含function含no_reorder，block 1模式下，patch.d修改原先含no_reorder规则的function实现，加载升降级so，查询数据符合预期
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test016";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record out2");
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 2}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 9}, {1, upVerVal, 4, 4, 16}};
    C3Int8T objIn7[recordNum - 3] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 2, 2, 4}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 2, 100}, {1, upVerVal, 2, 2, 100},
        {1, upVerVal, 3, 3, 100}, {1, upVerVal, 4, 4, 100}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn7, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    system("gmsysview record out2");
    ret = readRecord(g_conn, g_stmt, "out3", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");

    // 校验升级之后重做的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out1", 2, 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级后对输入表插入数据
    C3Int8T objIn9[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    // 加载降级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn10[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn11[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    C3Int8T objIn12[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    C3Int8T objIn13[recordNum - 3] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 3, 3, 6}};
    C3Int8T objIn14[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn12, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn13, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn14, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out3 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.原始.d多个规则存在含function含no_reorder，block 0模式下，覆盖新增表、修改规则、修改function实现，循环加载升降级so1000次，无内存泄漏
**************************************************************************** */
TEST_F(dtlsupfunord_002_test2, DataLog_086_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum -2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    // 返回卸载rollbackV3.so和加载patchV3.so看内存
#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    
    pthread_t thr_arr[1];
    // 循环升降级1000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(10);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
#ifndef ENV_RTOSV2X
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.原始.d多个规则存在含function含no_reorder，block 1模式下，覆盖新增表、修改规则、修改function实现，循环加载升降级so1000次，无内存泄漏
**************************************************************************** */
TEST_F(dtlsupfunord_002_test2, DataLog_086_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum -2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    // 返回卸载rollbackV3.so和加载patchV3.so看内存
#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    
    pthread_t thr_arr[1];
    // 循环升降级1000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(10);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
#ifndef ENV_RTOSV2X
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.block 0模式下，新增规则含no_reorder、新增输入表、修改规则、修改function实现、新增规则，循环加载升降级so1000次，无内存泄漏
**************************************************************************** */
TEST_F(dtlsupfunord_002_test2, DataLog_086_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum -2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    // 返回卸载rollbackV3.so和加载patchV3.so看内存
#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    
    pthread_t thr_arr[1];
    // 循环升降级1000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(10);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
#ifndef ENV_RTOSV2X
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.block 1模式下，新增规则含no_reorder、新增输入表、修改规则、修改function实现、新增规则，循环加载升降级so1000次，无内存泄漏
**************************************************************************** */
TEST_F(dtlsupfunord_002_test2, DataLog_086_002_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum -2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    // 返回卸载rollbackV3.so和加载patchV3.so看内存
#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    
    pthread_t thr_arr[1];
    // 循环升降级1000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(10);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
#ifndef ENV_RTOSV2X
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.原始.d规则含no_reorder，对输入表进行异步写操作
**************************************************************************** */
TEST_F(dtlsupfunord_002_test, DataLog_086_002_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test015";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;

    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecordAsync(connAsync, stmtAsync, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync(connAsync, stmtAsync, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync(connAsync, stmtAsync, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record out2");
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 加载升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 2}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 9}, {1, upVerVal, 4, 4, 16}};
    C3Int8T objIn7[recordNum - 3] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 2, 2, 4}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 2, 100}, {1, upVerVal, 2, 2, 100},
        {1, upVerVal, 3, 3, 100}, {1, upVerVal, 4, 4, 100}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn7, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    system("gmsysview record out2");
    ret = readRecord(g_conn, g_stmt, "out3", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");

    // 校验升级之后重做的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out1", 2, 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级后对输入表插入数据
    C3Int8T objIn9[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecordAsync(connAsync, stmtAsync, "inp1", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync(connAsync, stmtAsync, "inp2", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync(connAsync, stmtAsync, "inp3", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    // 加载降级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn10[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn11[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret =writeRecordAsync(connAsync, stmtAsync, "inp1", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync(connAsync, stmtAsync, "inp2", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync(connAsync, stmtAsync, "inp3", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");
    C3Int8T objIn12[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    C3Int8T objIn13[recordNum - 3] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 3, 3, 6}};
    C3Int8T objIn14[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn12, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn13, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn14, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out3 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开异步连接
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
