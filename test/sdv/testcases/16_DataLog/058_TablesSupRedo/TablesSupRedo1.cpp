/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : datalog 热补丁支持timeout表,transient表,notify表和外部表的重做
 Notes        :
 History      :
 Author       : youwanyong ywx1157510
 Modification : 2023/11/01
**************************************************************************** */
#include "TablesSupRedo.h"
using namespace std;

class TablesSupRedo : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void TablesSupRedo::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
}

void TablesSupRedo::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("rm -rf /root/_datalog_/");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

void TablesSupRedo::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void TablesSupRedo::TearDown()
{
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    AW_CHECK_LOG_END();
}

// 需重启修改配置项
class TablesSupRedo1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void TablesSupRedo1::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxLockTimeOut=10000\" \"workerHungThreshold=11,200,300\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
}

void TablesSupRedo1::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("rm -rf /root/_datalog_/");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void TablesSupRedo1::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_DATA_EXCEPTION);
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void TablesSupRedo1::TearDown()
{
    GmcDropVertexLabel(g_stmt, "N000");
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    AW_CHECK_LOG_END();
}
/* ****************************************************************************
 Description  :   001.过期表join一个function（function内sleep2s，设置datalogUpgradeFetchSize为1）
 批写10条22s后过期数据，升级so，查询数据 ，预期升级成功，升级期间数据未过期
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo, Datalog_058_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 该场景用例在过期线程获取不到表锁事务锁超时时服务端会报12002
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_058_001";
    char soName1[FILE_PATH] = "Datalog_058_001_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_001_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条22s后过期数据---------------------------*/
    // 插入数据
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = 22 * 1000;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    // 预置过期字段时间值
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    int64_t currentTime;
    GetTimeMs(&currentTime);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "校验数据，当前系统时间为%lld.", currentTime);
    system("gmsysview record ns1.A");
    // 校验输入表和输出表
    AW_FUN_Log(LOG_INFO, "读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    system("gmsysview record ns1.A");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    // 查询补丁状态
    AW_FUN_Log(LOG_INFO, "查询补丁状态,等待重做完成");
    sleep(20);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        system("cat /root/_datalog_/RunLog.txt>test.txt");
        if (ret == -1) {
            sleep(1);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != 0) {
        system(cmd);
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    // 确保过期线程触发
    sleep(5);
    system("cat /root/_datalog_/RunLog.txt>test.txt");
    // 校验过期udf是否触发
    ret = Debug_executeCommand((char *)"md5sum test.txt", "d712717d68179fc11cf6f1eb2b0771ca");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级后写数据
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验升级后写输入表和输出表
    AW_FUN_Log(LOG_INFO, "升级后 读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 降级到最低版本时查询补丁状态，降级期间才能查到完整降级状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: REDOING", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v2.0.0]->[v1.0.0]", "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(20);
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        system("cat /root/_datalog_/RunLog.txt>test3.txt");
        if (ret == -1) {
            sleep(1);
        }
    }
    if (ret != 0) {
        system(cmd);
    }
    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    // 校验过期udf是否触发
    system("cat /root/_datalog_/RunLog.txt>test3.txt");
    sleep(25);  // 确保过期线程触发
    system("cat /root/_datalog_/RunLog.txt>test4.txt");
    ret = Debug_executeCommand((char *)"md5sum test4.txt", "021f395e2ad0d76c814edcc2c590f2dc");
    if (ret != 0) {
        ret = Debug_executeCommand((char *)"md5sum test4.txt", "6c458a1779cabf212de35c555af914ee");
    }
    if (ret != 0) {
        ret = Debug_executeCommand((char *)"cat test4.txt|wc -l", "44");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 过期数据推送顺序随机
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "------------test4.txt---------begin----");
        system("cat test4.txt");
        AW_FUN_Log(LOG_INFO, "------------test4.txt---------end----");
        // 避免打屏延迟
        sleep(5);
        AW_FUN_Log(LOG_INFO, "-----------test3.txt-------begin------");
        system("cat test3.txt");
        AW_FUN_Log(LOG_INFO, "-----------test3.txt-------end------");
    }
    // 预期当前读取数据重做后数据被删除
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, 0, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 0, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据");

    // 校验升级后写输入表和输出表
    AW_FUN_Log(LOG_INFO, "降级后 读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 10, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    char cmd1[1024];
    memset(cmd1, 0, sizeof(cmd1));
    char cmd2[1024];
    memset(cmd2, 0, sizeof(cmd2));
    (void)snprintf(cmd1, 1024, "%s/gmsysview count ns1.B -s %s -ns %s", g_toolPath, g_connServer, g_testNameSpace);
    (void)snprintf(cmd2, 1024, "%s/gmsysview count ns1.A -s %s -ns %s", g_toolPath, g_connServer, g_testNameSpace);
    ret = Debug_executeCommand(cmd1, "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd2, "10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 数据过期
    sleep(25);
    system("gmsysview record ns1.A");
    system("gmsysview count ns1.A");
    system("gmsysview count ns1.B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 0, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, 0, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    GetTimeMs(&currentTime);
    AW_FUN_Log(LOG_INFO, "当前卸载前系统时间为%lld.", currentTime);
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetTimeMs(&currentTime);
    AW_FUN_Log(LOG_INFO, "当前卸载后系统时间为%lld.", currentTime);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_001");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   002.过期表相关topo升级，预期写入数据，数据过期后能够升级成功
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_002";
    char soName1[FILE_PATH] = "Datalog_058_002_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_002_rollbackV2.so";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    int32_t isableWrite = 1;     // 设置允许DML并发
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t ret = 0;

    // 设置事务锁超时时间
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
      // 设置允许DML和重做并发
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条22s后过期数据---------------------------*/
    // 插入数据
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = 22 * 1000;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    // 预置过期字段时间值
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    int64_t currentTime;
    GetTimeMs(&currentTime);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "校验数据，当前系统时间为%lld.", currentTime);

    // 校验输入表和输出表
    AW_FUN_Log(LOG_INFO, "读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 确保数据在过期，但是升级时能够抢到锁
    sleep(38);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    ret = TestLoadUpgradeDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "加载完成.");
    sleep(2);

    // 读数据,预期数据已被过期
    char cmd1[1024];
    memset(cmd1, 0, sizeof(cmd1));
    char cmd2[1024];
    memset(cmd2, 0, sizeof(cmd2));
    (void)snprintf(cmd1, 1024, "%s/gmsysview count ns1.B -s %s -ns %s", g_toolPath, g_connServer, g_testNameSpace);
    (void)snprintf(cmd2, 1024, "%s/gmsysview count ns1.A -s %s -ns %s", g_toolPath, g_connServer, g_testNameSpace);
    ret = Debug_executeCommand(cmd1, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd2, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
        system(cmd);
    }
    // 查询补丁状态，预期重做完成
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v1.0.0]->[v2.0.0]", "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }

    GetTimeMs(&currentTime);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据校验数据，当前系统时间为%lld.", currentTime);
    // 校验升级后写输入表和输出表
    AW_FUN_Log(LOG_INFO, "升级后 读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    // 确保数据在过期，但是升级时能够抢到锁
    sleep(38);
    system("gmsysview record ns1.A");
    AW_FUN_Log(LOG_INFO, "加载降级so");
    char rollBackCmd[128] = "gmimport -c datalog -rollback ./Datalog_File/Datalog_058_002_rollbackV2.so";
    (void)sprintf(rollBackCmd, "gmimport -c datalog -rollback ./Datalog_File/Datalog_058_002_rollbackV2.so -ns %s",
        g_testNameSpace);
    system(rollBackCmd);
    // 读数据,预期数据已被过期
    ret = Debug_executeCommand(cmd1, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd2, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }

    GetTimeMs(&currentTime);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据，当前系统时间为%lld.", currentTime);

    // 校验升级后写输入表和输出表
    AW_FUN_Log(LOG_INFO, "降级后 读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 10, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");
    // 查询补丁状态
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    GetTimeMs(&currentTime);
    sleep(40);
    AW_FUN_Log(LOG_INFO, "当前系统时间为%lld.", currentTime);
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_002");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   003.批写10条数据，设置事务锁超时时间为10s，过期表均join一个function（function内sleep2s）
 超时时间设置为22s，写入数据后sleep(10s）升级so,过期数据时不允许升级
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_002";
    char soName1[FILE_PATH] = "Datalog_058_002_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_002_rollbackV2.so";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t isableWrite = 1;     // 设置允许DML并发
    int32_t ret = 0;

    // 设置事务锁超时时间
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    // 设置允许DML和重做并发
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条22s后过期数据---------------------------*/
    // 插入数据
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = 22 * 1000;  // 改为22升级函数会被调用两次
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    // 预置过期字段时间值
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    int64_t currentTime;
    GetTimeMs(&currentTime);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "校验数据，当前系统时间为%lld.", currentTime);

    // 校验输入表和输出表
    AW_FUN_Log(LOG_INFO, "读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    // 确保数据在过期，升级时抢不到锁
    sleep(11);
    /*---------------------------升级so---------------------------*/
    ClearFileContent();  // 文件用于记录重做期间udf是否被触发
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so预期加载失败.");
    AW_MACRO_EXPECT_EQ_INT(-1, TestLoadUpgradeDatalog(soName1));

    // 查询补丁状态，预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_002");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    sleep(25);
    // 读数据,预期数据已被过期
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record ns1.B -s %s -ns %s |wc -l", g_toolPath, g_connServer,
        g_testNameSpace);
    ret = Debug_executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record ns1.A -s %s -ns %s |wc -l", g_toolPath, g_connServer,
        g_testNameSpace);
    ret = Debug_executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }

    sleep(10);
    AW_FUN_Log(LOG_INFO, "再次升级so后写数据校验数据，当前系统时间为%lld.", currentTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    GetTimeMs(&currentTime);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验升级后写输入表和输出表
    AW_FUN_Log(LOG_INFO, "升级后 读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    // 确保数据在过期，但是升级时能够抢到锁
    sleep(38);
    ClearFileContent();  // 文件用于记录重做期间udf是否被触发
    AW_FUN_Log(LOG_INFO, "手动加载降级so");
    char rollBackCmd[256] = "gmimport -c datalog -rollback ./Datalog_File/Datalog_058_002_rollbackV2.so";
    (void)sprintf(rollBackCmd, "gmimport -c datalog -rollback ./Datalog_File/Datalog_058_002_rollbackV2.so -ns %s",
        g_testNameSpace);
    system(rollBackCmd);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    // 查询补丁状态当前补丁已加载成功
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }

    GetTimeMs(&currentTime);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据，当前系统时间为%lld.", currentTime);

    // 校验升级后写输入表和输出表
    AW_FUN_Log(LOG_INFO, "降级后 读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    GetTimeMs(&currentTime);
    AW_FUN_Log(LOG_INFO, "当前系统时间为%lld.", currentTime);
    sleep(38);
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_002");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   004.enableDatalogDmlWhenUpgrading设置为1，批写10条数据，设置事务锁超时时间为10s，
 过期表均join一个function（function内sleep2s）超时时间设置为22s，开启多个线程线程sleep 10s后进行升级，
 线程二写入数据过期字段值设置为0，读数据，验证升级重做时允许DML并发
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_004";
    char soName1[FILE_PATH] = "Datalog_058_002_patchV4";
    char soName2[FILE_PATH] = "Datalog_058_002_rollbackV4.so";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 是否允许升级重做时并发dml
    int32_t ret = 0;

    // 设置事务锁超时时间
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    // 允许升级重做时并发DML
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    /*---------------------------预置20条22s后过期数据---------------------------*/
    // 插入数据
    int recordNum = 20;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        // 一批数据重做前过期
        if (i >= 10) {
            objIn1[i].d = 12 * 1000;
        } else {
            // 一批数据重做期间还存在
            objIn1[i].d = 100 * 1000;
        }
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写20条数据
    // 预置过期字段时间值
    AW_FUN_Log(LOG_INFO, "批写20条数据.");
    int64_t currentTime;
    GetTimeMs(&currentTime);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "校验数据，当前系统时间为%lld.", currentTime);

    // 校验输入表和输出表
    AW_FUN_Log(LOG_INFO, "读输入表A 过期字段值：%lld", objIn1[0].d);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 开启多个线程
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, ThreadLoadUpgradeSo, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, ThreadWriteTimeoutData, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_004");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   005.支持tuple表作为输入表时，so进行升级重做
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_003";
    char soName1[FILE_PATH] = "Datalog_058_003_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_003_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t ret = 0;

    // 设置事务锁超时时间
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    int32_t index = 0;
    for (int i = 0; i < recordNum; i++) {
        for (int32_t j = 0; j < 2; j++) {
            objIn2[index].a = i + 1;
            objIn2[index].bLen = 10;
            objIn2[index].b = strT;
            (void)snprintf((char *)objIn2[index].b, 10, "s%08d", i + 1);
            for (int j = 0; j < 256; j++) {
                objIn2[index].c[j] = i + 1;
            }
            objIn2[index].d = i + 1;
            objIn2[index].dtlReservedCount = i + 1;
            if (j == 0) {
                objIn2[index].upgradeVersion = 0;
            } else {
                objIn2[index].upgradeVersion = 1;
            }
            index++;
        }
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn2, recordNum + 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据.");
    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    cycle = 10;  // 重做时间存在波动增加视图校验重试次数
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        ret = Debug_executeCommand(
            cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);

    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn2, recordNum + 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_003");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   006.支持tuple表作为中间表，so进行升级重做
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_006";
    char soName1[FILE_PATH] = "Datalog_058_006_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_006_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.C", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.C");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.C");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount -= i + 1;
    }
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);

    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, 1, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);
    sleep(1);
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据.");
    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    // 查询补丁状态
    sleep(1);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据.");
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_006");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   007.支持finish表作为输入表，so进行升级重做
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_007";
    char soName1[FILE_PATH] = "Datalog_058_007_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_007_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t ret = 0;

    // 设置事务锁超时时间
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    int32_t index = 0;
    for (int i = 0; i < recordNum; i++) {
        for (int32_t j = 0; j < 2; j++) {
            objIn2[index].a = i + 1;
            objIn2[index].bLen = 10;
            objIn2[index].b = strT;
            (void)snprintf((char *)objIn2[index].b, 10, "s%08d", i + 1);
            for (int j = 0; j < 256; j++) {
                objIn2[index].c[j] = i + 1;
            }
            objIn2[index].d = i + 1;
            objIn2[index].dtlReservedCount = i + 1;
            if (j == 0) {
                objIn2[index].upgradeVersion = 0;
            } else {
                objIn2[index].upgradeVersion = 1;
            }
            index++;
        }
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn2, recordNum + 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据.");
    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    cycle = 10;  // 重做时间存在波动增加视图校验重试次数
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        ret = Debug_executeCommand(
            cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);

    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn2, recordNum + 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_007");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   008.支持finish表作为中间表，so进行升级重做
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_008";
    char soName1[FILE_PATH] = "Datalog_058_008_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_008_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.C", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.C");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.C");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount -= i + 1;
    }
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);

    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, 1, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);
    sleep(1);
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据.");
    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_008");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   009.支持field的表作为中间表，so进行升级重做
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_009";
    char soName1[FILE_PATH] = "Datalog_058_009_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_009_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = 0;  // transient field表需要先写入为0数据才能写入为1数据
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.C", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.C");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.C");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount -= i + 1;
    }
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);

    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);
    sleep(1);
    ret = writeRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据.");
    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.C", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_009");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   010.支持外部表作为输出表，so进行升级重做
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_010";
    char soName1[FILE_PATH] = "Datalog_058_010_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_010_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 单次重做条数
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelInfoPath[40] = "./Datalog_File/B_10000.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;  // transient field表需要先写入为0数据才能写入为1数据
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount -= i + 1;
    }

    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(1);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后写数据校验数据.");
    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_010");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   011.enableDatalogDmlWhenUpgrading设置为0升级期间操作外部表
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_011";
    char soName1[FILE_PATH] = "Datalog_058_011_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_011_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 0;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelInfoPath[40] = "./Datalog_File/B_10000.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = i;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * (recordNum + 1));
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < (recordNum + 1); i++) {
        if (i == 0) {
            objIn2[i].a = i;
            objIn2[i].bLen = 10;
            objIn2[i].b = strT;
            (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
            for (int j = 0; j < 256; j++) {
                objIn2[i].c[j] = i + 1;
            }
            objIn2[i].d = i + 1;
            objIn2[i].dtlReservedCount = i + 1;
            objIn2[i].upgradeVersion = 0;
        } else {
            objIn2[i].a = i + 10;
            objIn2[i].bLen = 10;
            objIn2[i].b = strT;
            (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1 + 10);
            for (int j = 0; j < 256; j++) {
                objIn2[i].c[j] = i + 1 + 10;
            }
            objIn2[i].d = i + 1 + 10;
            objIn2[i].dtlReservedCount = i + 1 + 10;
            objIn2[i].upgradeVersion = 0;
        }
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 开启多个线程一个线程升级so一个线程并发写入数据
    // 开启多个线程
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, ThreadLoadExternUpgradeSo, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, ThreadWriteExternTable, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    /*---------------------------升级so---------------------------*/
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount -= (objIn1[i].dtlReservedCount * 2);
    }

    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, 1, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn2, recordNum + 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(12);
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, recordNum * 2, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_011");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   012.enableDatalogDmlWhenUpgrading设置为1升级期间操作外部表
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_011";
    char soName1[FILE_PATH] = "Datalog_058_011_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_011_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelInfoPath[40] = "./Datalog_File/B_10000.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = i;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * (recordNum + 1));
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < (recordNum + 1); i++) {
        if (i == 0) {
            objIn2[i].a = i;
            objIn2[i].bLen = 10;
            objIn2[i].b = strT;
            (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
            for (int j = 0; j < 256; j++) {
                objIn2[i].c[j] = i + 1;
            }
            objIn2[i].d = i + 1;
            objIn2[i].dtlReservedCount = i + 1;
            objIn2[i].upgradeVersion = 0;
        } else {
            objIn2[i].a = i + 10;
            objIn2[i].bLen = 10;
            objIn2[i].b = strT;
            (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1 + 10);
            for (int j = 0; j < 256; j++) {
                objIn2[i].c[j] = i + 1 + 10;
            }
            objIn2[i].d = i + 1 + 10;
            objIn2[i].dtlReservedCount = i + 1 + 10;
            objIn2[i].upgradeVersion = 0;
        }
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 开启多个线程一个线程升级so一个线程并发写入数据
    // 开启多个线程
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, ThreadLoadExternUpgradeSo, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, ThreadWriteExternTable, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    /*---------------------------升级so---------------------------*/
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount -= (objIn1[i].dtlReservedCount * 2);
    }

    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, 1, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn2, recordNum + 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(12);
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, recordNum * 2, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_011");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   013.外部表表记录数存在限制，重做条数操作（最大记录数，重做失败
（2024.3.21 升级失败后，当前不保证行为的正确性待迭代四支持回滚再校验） 
Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_013";
    char soName1[FILE_PATH] = "Datalog_058_013_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_013_rollbackV2.so";
    int32_t FetchSzieValue = 2147483647;  // 支持的最大重做条数
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelInfoPath[40] = "./Datalog_File/B.gmjson";
    DatalogCreateExternalTable(labelInfoPath);
    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    int recordNum = 2000;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i; 
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写1000条数据
    recordNum = 1000;
    AW_FUN_Log(LOG_INFO, "批写1000条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 2147483647",
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
        objIn1[i].dtlReservedCount -= i + 1;
    }
    // 
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级失败后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(1);
    // 查询补丁状态
    ret = Debug_executeCommand(cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 2147483647",
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级失败后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_013");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   014.外部表表记录数存在限制，重做条数操作，1，重做失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_013";
    char soName1[FILE_PATH] = "Datalog_058_013_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_013_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 支持的最大重做条数
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelInfoPath[40] = "./Datalog_File/B.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 1000;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写1000条数据
    AW_FUN_Log(LOG_INFO, "批写1000条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = -(i + 1);
    }

    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    // 插入的语义都会往外部表写数据
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, 999, C1Int4C1Int8C1StrC1ByteSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表

    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "B read complete!!!");
#ifndef ENV_RTOSV2X
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(4);
    // 查询补丁状态
    ret = Debug_executeCommand(cmd,
        "UPGRADE_VERSION: 0",
        "DATALOG_UPGRADE_FETCH_SIZE: 1",
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
        "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = Debug_executeCommand(cmd,
        "UPGRADE_VERSION: 0",
        "DATALOG_UPGRADE_FETCH_SIZE: 1",
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
        "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    // 升级失败数据未重做
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
#endif
    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_013");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   015.外部表表记录数存在限制，重做条数操作 最大重做值，重做失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_013";
    char soName1[FILE_PATH] = "Datalog_058_013_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_013_rollbackV2.so";
    int32_t FetchSzieValue = 999;  // 支持的最大重做条数
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelInfoPath[40] = "./Datalog_File/B.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 1000;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写1000条数据
    AW_FUN_Log(LOG_INFO, "批写1000条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 999", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = -(i + 1);
    }

    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    // 插入的语义都会往外部表写数据
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, 999, C1Int4C1Int8C1StrC1ByteSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    // A表数据不确定

    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(1);
    // 查询补丁状态
    ret = Debug_executeCommand(cmd,
        "UPGRADE_VERSION: 0",
        "DATALOG_UPGRADE_FETCH_SIZE: 999",
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
        "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = Debug_executeCommand(cmd,
        "UPGRADE_VERSION: 0",
        "DATALOG_UPGRADE_FETCH_SIZE: 999",
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
        "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    // 升级失败数据未重做
    ret = readRecord(g_conn, g_stmt, "B", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_013");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   016.支持pubsub可更新表作为输出表，so进行升级重做
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_016";
    char soName1[FILE_PATH] = "Datalog_058_016_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_016_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    // 创建订阅
    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = i;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * (recordNum + 1));
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < (recordNum + 1); i++) {
        if (i == 0) {
            objIn2[i].a = i;
            objIn2[i].bLen = 10;
            objIn2[i].b = strT;
            (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1);
            for (int j = 0; j < 256; j++) {
                objIn2[i].c[j] = i + 1;
            }
            objIn2[i].d = i + 1;
            objIn2[i].dtlReservedCount = i + 1;
            objIn2[i].upgradeVersion = 0;
        } else {
            objIn2[i].a = i + 10;
            objIn2[i].bLen = 10;
            objIn2[i].b = strT;
            (void)snprintf((char *)objIn2[i].b, 10, "s%08d", i + 1 + 10);
            for (int j = 0; j < 256; j++) {
                objIn2[i].c[j] = i + 1 + 10;
            }
            objIn2[i].d = i + 1 + 10;
            objIn2[i].dtlReservedCount = i + 1 + 10;
            objIn2[i].upgradeVersion = 0;
        }
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*---------------------------升级so---------------------------*/
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = -(i + 1);
    }
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 9, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 20, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(12);
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 9, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_016");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   017.构造升级失败场景，验证升级回滚正常（部分阻塞
 2024.3.21 当前升级失败后，预期不保证，待迭代四后验证
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_016";
    char soName1[FILE_PATH] = "Datalog_058_016_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_016_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    // 创建订阅
    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = i;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*---------------------------升级so---------------------------*/
    // 升级重做时阻塞回调
    g_isInvokePubsubHasNobyteCallBackCount.needSleep = true;
    g_isInvokePubsubHasNobyteCallBackCount.negativeCount = 1;
    g_isInvokePubsubHasNobyteCallBackCount.needSleepTime = 20;
    g_isInvokePubsubHasNobyteCallBackCount.lowupgradeVersion = 0;
    g_isInvokePubsubHasNobyteCallBackCount.highupgradeVersion = 0;
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: REDO_FAIL");
        sleep(2);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级删除旧数据%d条", g_isInvokePubsubHasNobyteCallBackCount.lowupgradeVersion);
    AW_FUN_Log(LOG_INFO, "升级写入新数据%d条", g_isInvokePubsubHasNobyteCallBackCount.highupgradeVersion);
    ret = Debug_executeCommand(cmd,
        "UPGRADE_VERSION: 0",
        "DATALOG_UPGRADE_FETCH_SIZE: 1",
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
        "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = -(i + 1);
    }
    g_isInvokePubsubHasNobyteCallBackCount.needSleep = false;
    g_isInvokePubsubHasNobyteCallBackCount.negativeCount = 0;
    g_isInvokePubsubHasNobyteCallBackCount.needSleepTime = 0;
    g_isInvokePubsubHasNobyteCallBackCount.lowupgradeVersion = 0;
    g_isInvokePubsubHasNobyteCallBackCount.highupgradeVersion = 0;
    sleep(20);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表有一条count - +抵消
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    g_isInvokePubsubHasNobyteCallBackCount.lowupgradeVersion = 0;
    g_isInvokePubsubHasNobyteCallBackCount.highupgradeVersion = 0;
    system("gmsysview count ns1.A");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(1);
    // 查询补丁状态
    ret = Debug_executeCommand(cmd,
        "UPGRADE_VERSION: 0",
        "DATALOG_UPGRADE_FETCH_SIZE: 1",
        "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC",
        "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级删除旧数据%d条", g_isInvokePubsubHasNobyteCallBackCount.lowupgradeVersion);
    AW_FUN_Log(LOG_INFO, "降级写入新数据%d条", g_isInvokePubsubHasNobyteCallBackCount.highupgradeVersion);
    system("gmsysview count ns1.A");
    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_016");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   018.tuple和finish作为输入投影到tuple和finish最后投影给feild表输出表为外部表和notify可更新表
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_017";
    char soName1[FILE_PATH] = "Datalog_058_017_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_017_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建外部表
    char labelInfoPath[40] = "./Datalog_File/B1.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    // 创建订阅
    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.inpTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inpFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验表数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].a = 0;
    }
    system("gmsysview count");
    sleep(5);
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].a = 0;
    }
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "read complete!!!");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*---------------------------升级so---------------------------*/
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r2", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r3", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r4", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r5", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = -(i + 1);
    }
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inpTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inpFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.inpTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inpFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 4, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(2);
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 4, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_017");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   019.so不含（transient tuple和 transient finish表中间表含transient
feild表输出表为外部表和notify可更新表验证数据升级前后一致性 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_019";
    char soName1[FILE_PATH] = "Datalog_058_019_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_019_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建外部表
    char labelInfoPath[40] = "./Datalog_File/B1.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    // 创建订阅
    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验表数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].a = 0;
    }
    system("gmsysview count");
    sleep(5);
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.mid1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.mid2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].a = 0;
    }
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "read complete!!!");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*---------------------------升级so---------------------------*/
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r2", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r3", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r4", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r5", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inp1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inp2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.mid1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.mid2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = -(i + 1);
    }
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(2);
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_019");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   020.规则升级，新增右表为旧表
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_020";
    char soName1[FILE_PATH] = "Datalog_058_020_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_020_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1,errorMsg2);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建外部表
    char labelInfoPath[40] = "./Datalog_File/B1.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    // 当前仅支持新增右表为可更新表，其余表均不支持
    // 写数据
    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 100000;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "inpTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpTimeout", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outTimeout", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级so

    /*---------------------------升级so---------------------------*/
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 10000;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: inpTimeout");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: outFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: outTimeout");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    ret = readRecord(g_conn, g_stmt, "outTimeout", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 降级so
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "outTimeout", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 进行其它场景不支持的旧表作为右表
    // 3.编译加载升级so
    char command[MAX_CMD_SIZE] = {0};
    char inputFile1[FILE_PATH] = "";
    (void)sprintf(inputFile1, "./Datalog_File/%s_rule.d", soName);
    char inputFile2[FILE_PATH] = "";
    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", "Datalog_058_020_001");
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // finish
    ret = executeCommand(command, "rule \"r1\" should add updatable input table or function to join near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", "Datalog_058_020_002");
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // tuple
    ret = executeCommand(command, "duplicate table \"inpFinish\" not allowed in JOIN rule near line 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    (void)sprintf(inputFile2, "./Datalog_File/%s_patch.d", "Datalog_058_020_003");
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s", g_toolPath, inputFile1, inputFile2);

    // 普通表
    ret = executeCommand(command, "rule \"r1\" should add updatable input table or function to join near line 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        system(command);
    }

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_020");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   021.重做时表的顺序的交互指定输入表顺序（finish，tuple，timeout，普通表）
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_021";
    char soName1[FILE_PATH] = "Datalog_058_021_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_021_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建外部表
    char labelInfoPath[40] = "./Datalog_File/B1.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    // 写数据
    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 100000;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    int64_t currentTime;
    GetTimeMs(&currentTime);
    AW_FUN_Log(LOG_INFO, "当前系统时间为%lld.", currentTime);
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "inpTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpTimeout", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inpNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "校验已写入数据.");
    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outTimeout", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级so
    ClearFileContent();  // 文件用于记录重做期间udf是否被触发
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取用例数据，强校验数据回滚顺序
    char expectFile[] = "./Datalog_File/expectPrecedenceInp.txt";
    FileContentCompare(expectFile);

    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 100000;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: inpTimeout");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: outFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: outTimeout");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    ret = readRecord(g_conn, g_stmt, "outTimeout", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 降级so
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    // 查询补丁状态
    // 降级so需要时间
    sleep(5);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "outTimeout", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_021");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   022.重做时表的顺序的交互指定中间表顺序（finish，tuple，field，普通表）
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_022";
    char soName1[FILE_PATH] = "Datalog_058_022_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_022_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建外部表
    char labelInfoPath[40] = "./Datalog_File/B1.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);
    // 写数据
    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "inpNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "校验已写入数据.");
    ret = readRecord(g_conn, g_stmt, "inpNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record midFinish");
     ret = Debug_executeCommand((char *)"gmsysview count midFinish", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级so
    ClearFileContent();  // 文件用于记录重做期间udf是否被触发
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取用例数据，强校验数据回滚顺序
    char expectFile[] = "./Datalog_File/expectPrecedenceMid.txt";
    FileContentCompare(expectFile);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: inpNormal");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: outFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: outNormal");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: inpNormal");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: outTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: outField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "outNormal", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    system("gmsysview count midFinish");
    ret = Debug_executeCommand((char *)"gmsysview count midFinish", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    sleep(5);
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord(g_conn, g_stmt, "outTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "outNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "outField", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count midFinish");
    ret = Debug_executeCommand((char *)"gmsysview count midFinish", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_022");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    free(objIn1);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   023.重做时表的顺序的交互指定输出表顺序（外部表，notify可更新表，普通表）
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_023";
    char soName1[FILE_PATH] = "Datalog_058_023_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_023_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建外部表
    char labelInfoPath[40] = "./Datalog_File/B1.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 创建pubsub表订阅
    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请pubsub表订阅");

    // 创建外部表订阅
    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./Datalog_File/pubsub_precedence_B1.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);
    // 写数据
    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.inpNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "校验已写入数据.");
    ret = readRecord(g_conn, g_stmt, "ns1.inpNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 10, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 10, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级so
    ClearFileContent();  // 文件用于记录重做期间udf是否被触发
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // precedence指定含notify表和其它表顺序不可靠不进行强校验
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 100000;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r2", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpNormal");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpNormal");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.outNormal");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "ns1.inpNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.outNormal", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 1, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 9, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表数据删除不存在的数据不会去推送
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_UPDATE, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_DELETE, 9, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 降级so
    /*---------------------------降级---------------------------*/
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = readRecord(g_conn, g_stmt, "ns1.inpNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.outNormal", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "B1", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 9, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 9, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_UPDATE, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_DELETE, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataF->data);
    free(userDataF);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_023");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 024.tuple和finish作为输入投影到tuple和finish最后投影给feild表输出表为外部表和notify可更新表进行连续升级
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_017";
    char soName1[FILE_PATH] = "Datalog_058_017_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_017_rollbackV2.so";
    int32_t FetchSzieValue = 1;      // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;  // 不允许升级重做时并发
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建外部表
    char labelInfoPath[40] = "./Datalog_File/B1.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    // 创建订阅
    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum * 2; i++) {
        objIn1[i].a = 0;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.inpTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inpFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验表数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].a = 0;
    }
    system("gmsysview count");
    sleep(5);
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].a = 0;
    }
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "read complete!!!");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*---------------------------升级so---------------------------*/
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r2", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r3", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r4", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r5", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = -(i + 1);
    }
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inpTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inpFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "升级后写数据");

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
        objIn1[i].dtlReservedCount = i + 1;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.inpTuple", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inpFinish", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后写输入表和输出表
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 4, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.A read complete!!!");
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    AW_FUN_Log(LOG_STEP, "热补丁第二次升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog((char *)"Datalog_058_017_patchV3"));
    sleep(1);
    // 查询视图
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 2", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v2.0.0]->[v3.0.0]");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "ADD_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁第三次升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog((char *)"Datalog_058_017_patchV4"));
    sleep(1);
    // 查询视图
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 3", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v3.0.0]->[v4.0.0]");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "ADD_UDFS", "UDF_NAME: dtl_ext_func_ns1_func");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁第四次升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog((char *)"Datalog_058_017_patchV5"));
    sleep(1);
    // 查询视图
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 4", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v4.0.0]->[v5.0.0]");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_UDFS", "UDF_NAME: dtl_ext_func_ns1_func");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*---------------------------降级---------------------------*/
    // 降级到版本4
    AW_FUN_Log(LOG_STEP, "降级到版本4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo((char *)"./Datalog_File/Datalog_058_017_rollbackV5.so"));
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 3", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v5.0.0]->[v4.0.0]");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "ADD_UDFS", "UDF_NAME: dtl_ext_func_ns1_func");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //     // 降级到版本3
    AW_FUN_Log(LOG_STEP, "降级到版本3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo((char *)"./Datalog_File/Datalog_058_017_rollbackV4.so"));
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 2", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v4.0.0]->[v3.0.0]");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "ADD_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //     // 降级到版本2
    AW_FUN_Log(LOG_STEP, "降级到版本2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo((char *)"./Datalog_File/Datalog_058_017_rollbackV3.so"));
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v3.0.0]->[v2.0.0]");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r1", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: fake_B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpTuple");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpAdd");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.midFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.inpFinish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 降级到版本1
    AW_FUN_Log(LOG_STEP, "降级到版本1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo((char *)"./Datalog_File/Datalog_058_017_rollbackV2.so"));
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "VERSION: [v2.0.0]->[v1.0.0]");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.B", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "fake_B1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    sleep(2);
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    ret = readRecord(g_conn, g_stmt, "ns1.midField", objIn1, 2, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "B1", objIn1, 4, C1Int4C1Int8C1StrC1ByteGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.C read complete!!!");
    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "B1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_017");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   025.function 内含access current access_kv，多线程，线程1开启事务写kv表不提交
，线程2升级so Author       : youwanyong
2024.3.21  access_delta重做不支持含access_delta 表，删除该关键字
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_003";
    char soName1[FILE_PATH] = "Datalog_058_003_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_003_rollbackV2.so";
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t ret = 0;

    // 设置事务锁超时时间
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    int32_t index = 0;
    for (int i = 0; i < recordNum; i++) {
        for (int32_t j = 0; j < 2; j++) {
            objIn2[index].a = i + 1;
            objIn2[index].bLen = 10;
            objIn2[index].b = strT;
            (void)snprintf((char *)objIn2[index].b, 10, "s%08d", i + 1);
            for (int j = 0; j < 256; j++) {
                objIn2[index].c[j] = i + 1;
            }
            objIn2[index].d = i + 1;
            objIn2[index].dtlReservedCount = i + 1;
            if (j == 0) {
                objIn2[index].upgradeVersion = 0;
            } else {
                objIn2[index].upgradeVersion = 1;
            }
            index++;
        }
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 开启多个线程
    // 开启多个线程
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 写kv表不提交直到重做失败
    ret = pthread_create(&client_thr[0], NULL, ThreadWriteKV, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写kv表时开始重做直到失败
    ret = pthread_create(&client_thr[1], NULL, ThreadLoadUpgradeSOFail, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_003");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  026.function 内含access current access_kv，多线程，线程1重做时写kv表 ，线程2升级so
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo, Datalog_058_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_026";
    char soName1[FILE_PATH] = "Datalog_058_026_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_026_rollbackV2.so";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t FetchSzieValue = 10;  // 单次重做条数
    int32_t ret = 0;

    // 设置事务锁超时时间
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = i + 1;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    int32_t index = 0;
    for (int i = 0; i < recordNum; i++) {
        for (int32_t j = 0; j < 2; j++) {
            objIn2[index].a = i + 1;
            objIn2[index].bLen = 10;
            objIn2[index].b = strT;
            (void)snprintf((char *)objIn2[index].b, 10, "s%08d", i + 1);
            for (int j = 0; j < 256; j++) {
                objIn2[index].c[j] = i + 1;
            }
            objIn2[index].d = i + 1;
            objIn2[index].dtlReservedCount = i + 1;
            if (j == 0) {
                objIn2[index].upgradeVersion = 0;
            } else {
                objIn2[index].upgradeVersion = 1;
            }
            index++;
        }
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(2);
    // 开启多个线程
    int32_t thread_num = 1;
    pthread_t client_thr[thread_num];
    // 重做时写kv表
    ret = pthread_create(&client_thr[0], NULL, ThreadWriteKVWhenIsRedoing, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);

    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn2, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_FUN_Log(LOG_INFO, "降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    sleep(10);

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);

    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);

    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn2, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_026");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   027.开启多个线程，线程1进行so升级重做，线程2升级重做时插入，删除，更新旧数据
 Author       : youwanyong
**************************************************************************** */
TEST_F(TablesSupRedo1, Datalog_058_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[FILE_PATH] = "Datalog_058_026";
    char soName1[FILE_PATH] = "Datalog_058_026_patchV2";
    char soName2[FILE_PATH] = "./Datalog_File/Datalog_058_026_rollbackV2.so";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int32_t FetchSzieValue = 1;  // 单次重做条数
    int32_t isDmlWhenUpgrading = 1;
    int32_t ret = 0;

    // 设置事务锁超时时间
    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 设置重做条数
    SystemSnprintf((char *)"gmadmin -cfgName datalogUpgradeFetchSize -cfgVal %d", FetchSzieValue);
    SystemSnprintf((char *)"gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal %d", isDmlWhenUpgrading);

    /*---------------------------预置10条数据---------------------------*/
    int recordNum = 10;
    char strT[10] = {0};
    // 插入数据
    C1Int4C1Int8C1StrC1ByteT *objIn1 = (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].bLen = 10;
        objIn1[i].b = strT;
        (void)snprintf((char *)objIn1[i].b, 10, "s%08d", i + 1);
        for (int j = 0; j < 256; j++) {
            objIn1[i].c[j] = i + 1;
        }
        objIn1[i].d = 0;
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }

    C1Int4C1Int8C1StrC1ByteT *objIn2 =
        (C1Int4C1Int8C1StrC1ByteT *)malloc(sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C1Int4C1Int8C1StrC1ByteT) * recordNum * 2);
    int32_t index = 0;
    for (int i = 0; i < recordNum; i++) {
        for (int32_t j = 0; j < 2; j++) {
            objIn2[index].a = i + 1;
            objIn2[index].bLen = 10;
            objIn2[index].b = strT;
            (void)snprintf((char *)objIn2[index].b, 10, "s%08d", i + 1);
            for (int j = 0; j < 256; j++) {
                objIn2[index].c[j] = i + 1;
            }
            objIn2[index].d = i + 1;
            objIn2[index].dtlReservedCount = i + 1;
            if (j == 0) {
                objIn2[index].upgradeVersion = 0;
            } else {
                objIn2[index].upgradeVersion = 1;
            }
            index++;
        }
    }

    // 批写10条数据
    AW_FUN_Log(LOG_INFO, "批写10条数据.");
    ret = writeRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns1.A", objIn1, recordNum, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");

    /*---------------------------升级so---------------------------*/
    // 编译加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(soName1));
    sleep(1);
    // 开启多个线程
    int32_t thread_num = 1;
    pthread_t client_thr[thread_num];
    // 重做时并发写数据
    ret = pthread_create(&client_thr[0], NULL, ThreadWriteWhenIsRedoing, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 1", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "DROP_RULES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_TRIGGER_TABLES", "TABLE_NAME: ns1.A", "REDO_RELATED_TABLES");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = Debug_executeCommand(cmd, "REDO_RELATED_TABLES", "TABLE_NAME: ns1.A", "TABLE_NAME: ns1.B");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 1;
    }
    int32_t getUpgradeVersion = -1;
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);

    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }

    AW_FUN_Log(LOG_INFO, "升级后，读输出表B");
    ret = readRecord(g_conn, g_stmt, "ns1.B", objIn2, 1, C1Int4C1Int8C1StrC1ByteGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "ns1.B read complete!!!");
    /*---------------------------降级---------------------------*/
    AW_FUN_Log(LOG_INFO, "降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    sleep(10);

    // 写数据
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = 0;
    }
    GetTableUpgradeVersionByApi((char *)"ns1.A", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(objIn1[0].upgradeVersion, getUpgradeVersion);

    // 校验升级后写输入表和输出表
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    // 查询补丁状态
    ret = Debug_executeCommand(
        cmd, "UPGRADE_VERSION: 0", "DATALOG_UPGRADE_FETCH_SIZE: 1", "PATCH_STATE: SUCCESS", "LOAD_PROCESS: 100%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);

    AW_FUN_Log(LOG_INFO, "降级后，读输出表B");
    char cmd1[1024];
    memset(cmd1, 0, sizeof(cmd1));
    char cmd2[1024];
    memset(cmd2, 0, sizeof(cmd2));
    (void)snprintf(cmd1, 1024, "%s/gmsysview count ns1.A -s %s -ns %s", g_toolPath, g_connServer, g_testNameSpace);
    (void)snprintf(cmd2, 1024, "%s/gmsysview count ns1.B -s %s -ns %s", g_toolPath, g_connServer, g_testNameSpace);
    system(cmd1);
    ret = Debug_executeCommand(cmd1, "19");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd2);
    ret = Debug_executeCommand(cmd2, "19");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "卸载前查询数据.");
    system("gmsysview count");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询补丁状态预期为空
    ret = Debug_executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO", "Datalog_058_026");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
