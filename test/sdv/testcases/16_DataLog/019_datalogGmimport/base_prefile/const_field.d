// 1.left table, project with table
namespace ns1 {
%table A(a: int4, b: int4, c: int4)
%table B(a: int4, b: int4)

A(a, b, 1) :- B(a, b).
}

// 2.left table, project with transient
namespace ns2 {
%table A(a: int4, b: int4, c: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4){ transient(tuple) }
%table D(a: int4, b: int4){ transient(field(b)) }

C(a, 1) :- B(a, -).
D(a, 0) :- B(a, -).
A(a, b, c) :- C(a, b), D(a, c).
}

// 3.left table, project with resource
namespace ns3 {
%table A(a: int4, b: int4, c: int4)
%table B(a: int4, b: int4)
%resource rsc0(a: int4, b: int4 -> c: int4) { sequential(max_size(10)) }

rsc0(a, 1, -) :- B(a, -).
A(a, b, c) :- rsc0(a, b, c).
}

// 4.left table, not join
namespace ns4 {
%table A(a: int4, b: int4, c: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4)

A(a, b, 1) :- B(a, b), NOT C(a, b).
}

// 5.left table, join
namespace ns5 {
%table A(a: int4, b: int4, c: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4)
%function func(a: int4 -> b: int4)

A(a, b, 1) :- B(a, b), C(a, b).
A(a, c, 1) :- B(a, b), func(b, c).
}

// 6.right table, project with table
namespace ns6 {
%table A(a: int4)
%table B(a: int4, b: int4)

A(a) :- B(a, 1).
}

// 7.right table, project with transient
namespace ns7 {
%table A(a: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4){ transient(tuple) }
%table D(a: int4, b: int4){ transient(field(b)) }

D(a, 0) :- B(a, -).
A(a) :- C(a, 1).
A(a) :- D(a, 0).
}

// 8.right table, project with resource
namespace ns8 {
%table A(a: int4)
%table B(a: int4, b: int4)
%resource rsc0(a: int4 -> b: int4) { sequential(max_size(10)) }

rsc0(a, -) :- B(a, -).
A(a) :- rsc0(a, 1).
}

// 9.right table, not join
namespace ns9 {
%table A(a: int4, b: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4, c: int4)

A(a, b) :- B(a, b), NOT C(a, b, 1).
}

// 10.right table, join
namespace ns10 {
%table A(a: int4, b: int4, c: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4, c: int4)
%function func(a: int4 -> b: int4)

A(a, b, c) :- B(a, b), C(b, c, 1).
A(a, b, c) :- C(a, b, 1), func(a, c).
}
