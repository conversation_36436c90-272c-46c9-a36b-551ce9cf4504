/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: NewSubAndDlr01.cpp
 * Description: External NewSub And Dlr
 * Author: youwanyong ywx1157510
 * Create: 2024-9-02
 */

#include "UniversalTools.h"
#include "t_datacom_lite.h"
SnUserDataT *userData = {0};

using namespace std;

class NewSubAndDlr : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int32_t ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {}
};

void NewSubAndDlr::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    GmcSignalRegisterNotify();
    int32_t ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void NewSubAndDlr::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

// 不含信号注册函数
class NewSubAndDlr1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        TestEnvInitNotReg();
    }
    static void TearDownTestCase()
    {}
};

void NewSubAndDlr1::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void NewSubAndDlr1::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  : 001.pubsub资源中间表不支持状态合并订阅
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "test pubsub资源中间表不支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", userData, 1000, g_subName, snCallback);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.pubsub资源输出表不支持状态合并订阅
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_002";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "pubsub资源输出表不支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载含so.");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", userData, 1000, g_subName, snCallback);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.单机模式下的tbm表不支持状态合并订阅
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_003";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "单机模式下的tbm表不支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", userData, 1000, g_subName, snCallback);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.单机模式下的notify表不支持状态合并订阅
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_004";
    int32_t ret = -1;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "单机模式下的notify表不支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", userData, 1000, g_subName, snCallback);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.非状态合并的外部表不支持状态合并订阅
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_005";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "非状态合并的外部表不支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_005.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub005.gmjson", userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.客户端信号未注册，状态订阅失败
**************************************************************************** */
TEST_F(NewSubAndDlr1, DataLog_090_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    char soName1[FILE_PATH] = "DataLog_090_005";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "非状态合并的外部表不支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    int chanRingLen = 256;
    ret = TestGmcConnectPrivate(&g_connSub, NULL, GMC_CONN_TYPE_SUB, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub005.gmjson", userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.状态合并外部表支持状态合并订阅
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_005";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "状态合并外部表支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    int32_t ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub005.gmjson", userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.分布式模式下，notify表(外部表)支持状态合并订阅
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_008";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "分布式模式下，notify表(外部表)支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    int32_t ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub008.gmjson", userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.分布式模式下，tbm表(外部表)支持状态合并订阅
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_003";
    GmcStmtT *stmt;
    GmcConnT *conn;
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "分布式模式下，tbm表(外部表)支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    int32_t ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.状态合并订阅支持 delete，modify，initial_load，age，initial_load_eof
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "状态合并外部表支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.为三张表创建状态合并订阅.");

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*订阅delete，modify事件后delete删除数据再插入相同数据*/
    AW_FUN_Log(LOG_STEP, "3.同一个订阅通道订阅delete，modify age 事件后开启对账delete删除数据再插入相同数据再.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // N000
    ret = testSnMallocUserData(&userData, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    SnUserDataT *userData1 = {0};
    ret = testSnMallocUserData(&userData1, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData1, 1000, g_subName1, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableC
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    SnUserDataT *userData2 = {0};
    ret = testSnMallocUserData(&userData2, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData2, 1000, g_subName2, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    uint32_t count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");

    count = 1;
    SetArrayValue(objIn1, strT, recordNum, count);
    // 再次插入数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    // 对账老化数据
    // N000
    ret = GmcBeginCheck(g_stmtSync, tableName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    ret = GmcBeginCheck(g_stmtSync, tableB, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableB, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tableC
    ret = GmcBeginCheck(g_stmtSync, tableC, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableC, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_AGED, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_AGED, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_AGED, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_DATA);

    // 再次插入数据
    count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    count = 1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*订阅initial_load，initial_load_eof事件后对账老化部分数据*/
    AW_FUN_Log(LOG_STEP, "4.订阅initial_load initial_load_eof事件.");
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoad);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoadEof);
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_connSub = NULL;
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ReSetUserData(userData);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ReSetUserData(userData1);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData1, 1000, g_subName1, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ReSetUserData(userData2);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData2, 1000, g_subName2, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int cycle = 100;
    ret = 1;
    while (ret != 0 && cycle > 0) {
        ret = executeCommand(
            (char *)"gmsysview -q V\\$QRY_STMG_SUBS_CURSOR_INFO -f LABEL_NAME=tableB", "READY_CONSUME_DATA_COUNT: 0");
        sleep(1);
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("N000", objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(tableB, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(tableC, objIn1, recordNum, NewTypeTableGet1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = executeCommand((char *)"gmsysview count N000", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableB", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableC", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.状态合并订阅支持 delete，modify，age，initial_load_eof，initial_load
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "状态合并外部表支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.为三张表创建状态合并订阅.");

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*订阅delete，modify事件后delete删除数据再插入相同数据*/
    AW_FUN_Log(LOG_STEP, "3.同一个订阅通道订阅delete，modify age 事件后开启对账delete删除数据再插入相同数据再.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // N000
    ret = testSnMallocUserData(&userData, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    SnUserDataT *userData1 = {0};
    ret = testSnMallocUserData(&userData1, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData1, 1000, g_subName1, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableC
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    SnUserDataT *userData2 = {0};
    ret = testSnMallocUserData(&userData2, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData2, 1000, g_subName2, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    uint32_t count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");

    count = 1;
    SetArrayValue(objIn1, strT, recordNum, count);
    // 再次插入数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    // 对账老化数据
    // N000
    ret = GmcBeginCheck(g_stmtSync, tableName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    ret = GmcBeginCheck(g_stmtSync, tableB, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableB, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tableC
    ret = GmcBeginCheck(g_stmtSync, tableC, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableC, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_DATA);

    // 再次插入数据
    count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    count = 1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*订阅initial_load，initial_load_eof事件后对账老化部分数据*/
    AW_FUN_Log(LOG_STEP, "4.订阅initial_load initial_load_eof事件.");
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoad);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoadEof);
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_connSub = NULL;
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ReSetUserData(userData);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ReSetUserData(userData1);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData1, 1000, g_subName1, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ReSetUserData(userData2);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData2, 1000, g_subName2, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int cycle = 100;
    ret = 1;
    while (ret != 0 && cycle > 0) {
        ret = executeCommand(
            (char *)"gmsysview -q V\\$QRY_STMG_SUBS_CURSOR_INFO -f LABEL_NAME=tableB", "READY_CONSUME_DATA_COUNT: 0");
        sleep(1);
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("N000", objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(tableB, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(tableC, objIn1, recordNum, NewTypeTableGet1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = executeCommand((char *)"gmsysview count N000", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableB", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableC", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 012.状态合并订阅支持 delete，modify，initial_load，initial_load_eof,，age
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "状态合并外部表支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.为三张表创建状态合并订阅.");

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*订阅delete，modifyinitial_load,initial_load_eof事件后delete删除数据再插入相同数据*/
    AW_FUN_Log(LOG_STEP, "3.同一个订阅通道订阅delete，modify事件后delete删除数据再插入相同数据.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoad);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoadEof);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // N000
    ret = testSnMallocUserData(&userData, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    SnUserDataT *userData1 = {0};
    ret = testSnMallocUserData(&userData1, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData1, 1000, g_subName1, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableC
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    SnUserDataT *userData2 = {0};
    ret = testSnMallocUserData(&userData2, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData2, 1000, g_subName2, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    uint32_t count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");

    count = 1;
    SetArrayValue(objIn1, strT, recordNum, count);
    // 再次插入数据
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*订阅age事件后对账老化部分数据*/
    AW_FUN_Log(LOG_STEP, "4.订阅age事件后对账老化部分数据.");
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_connSub = NULL;
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ReSetUserData(userData);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ReSetUserData(userData1);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData1, 1000, g_subName1, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ReSetUserData(userData2);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData2, 1000, g_subName2, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 对账老化数据
    // N000
    ret = GmcBeginCheck(g_stmtSync, tableName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    ret = GmcBeginCheck(g_stmtSync, tableB, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableB, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tableC
    ret = GmcBeginCheck(g_stmtSync, tableC, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableC, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_AGED, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_AGED, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_AGED, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = executeCommand((char *)"gmsysview count N000", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableB", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableC", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 013.状态合并订阅支持 delete，initial_load，age，initial_load_eof,modify
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 删除一条老化线程标记删除的数据但还存在的数据日志报错1001000
    AddWhiteList(GMERR_NO_DATA);
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "状态合并外部表支持状态合并订阅.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.为三张表创建状态合并订阅.");

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*订阅delete，modify事件后delete删除数据再插入相同数据*/
    AW_FUN_Log(LOG_STEP, "3.同一个订阅通道订阅delete，modify事件后delete删除数据再插入相同数据.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoad);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoadEof);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // N000
    ret = testSnMallocUserData(&userData, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    SnUserDataT *userData1 = {0};
    ret = testSnMallocUserData(&userData1, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData1, 1000, g_subName1, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableC
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    SnUserDataT *userData2 = {0};
    ret = testSnMallocUserData(&userData2, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData2, 1000, g_subName2, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对账老化数据
    // N000
    ret = GmcBeginCheck(g_stmtSync, tableName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    ret = GmcBeginCheck(g_stmtSync, tableB, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableB, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count tableB");
    // 删除数据
    uint32_t count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");
    AddWhiteList(GMERR_NO_DATA);

    // tableC
    ret = GmcBeginCheck(g_stmtSync, tableC, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableC, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    count = 1;
    SetArrayValue(objIn1, strT, recordNum, count);
    // 再次插入数据
    ret =
        writeRecord(g_connSync, g_stmtSync, tableB, objIn1, recordNum, NewAlLTypeTableSet, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(
        g_connSync, g_stmtSync, tableC, objIn1, recordNum, NewAlLTypeTableSet1, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(
        g_connSync, g_stmtSync, tableName, objIn1, recordNum, NewAlLTypeTableSet, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = executeCommand((char *)"gmsysview count N000", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableB", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableC", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("N000", objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(tableB, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(tableC, objIn1, recordNum, NewTypeTableGet1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 014.状态合并订阅支持modify，initial_load，age，initial_load_eof, delete
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    // 后台线程存在锁竞争
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "状态合并订阅支持modify，initial_load，age，initial_load_eof, delete.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.为三张表创建状态合并订阅.");

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*订阅状态合并事件后对账老化数据再插入相同数据再删除数据*/
    AW_FUN_Log(LOG_STEP, "3.订阅状态合并事件后对账老化数据再插入相同数据再删除数据.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoad);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoadEof);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // N000
    ret = testSnMallocUserData(&userData, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    SnUserDataT *userData1 = {0};
    ret = testSnMallocUserData(&userData1, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData1, 1000, g_subName1, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableC
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    SnUserDataT *userData2 = {0};
    ret = testSnMallocUserData(&userData2, 1000 * 2, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData2, 1000, g_subName2, snCallbackExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对账老化数据
    // N000
    ret = GmcBeginCheck(g_stmtSync, tableName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tableB
    ret = GmcBeginCheck(g_stmtSync, tableB, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableB, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tableC
    ret = GmcBeginCheck(g_stmtSync, tableC, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmtSync, tableC, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t count = 1;
    system("gmsysview count tableC");
    char cmd[256] = "";
    (void)sprintf(cmd, "gmsysview -q V\\$QRY_AGE_TASK -f LABEL_NAME=tableC");
    ret = executeCommand(cmd, "TASK_STATUS: FINISHED");
    int32_t cycle = 10;
    while (cycle > 0 && ret != GMERR_OK) {
        ret = executeCommand(cmd, "TASK_STATUS: FINISHED");
        if (ret != GMERR_OK) {
            sleep(2);
        }
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(cmd);
    // 再次插入数据
    ret =
        writeRecord(g_connSync, g_stmtSync, tableB, objIn1, recordNum, NewAlLTypeTableSet, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(
        g_connSync, g_stmtSync, tableC, objIn1, recordNum, NewAlLTypeTableSet1, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(
        g_connSync, g_stmtSync, tableName, objIn1, recordNum, NewAlLTypeTableSet, true, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    // 删除数据
    count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_DELETE, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_DELETE, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    ret = executeCommand((char *)"gmsysview count N000", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableB", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview count tableC", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("N000", objIn1, 0, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(tableB, objIn1, 0, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(tableC, objIn1, 0, NewTypeTableGet1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 015.（三种）状态合并订阅不支持 insert，update，replace,merge
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "状态合并订阅不支持 insert，update，replace,merge.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    int32_t ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub015.gmjson", userData, 1000, g_subName, snCallback);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    // tbm
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub015_1.gmjson", userData, 1000, g_subName, snCallback);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    // notify
    ret = createSubscription(
        g_stmtSync, g_connSub, (char *)"datalog_file/pubsub015_2.gmjson", userData, 1000, g_subName, snCallback);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 016.pubsub资源中间表不支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_001";
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "test pubsub资源中间表不支持DLR.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub016.gmjson", userData, 1000, g_subName,
        snCallbackWhithDlrNoOk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 触发推送，调用DLR失败
    ret = executeCommand((char *)"gmimport -c vdata -f ./datalog_file/inp.gmdata", (char *)"success");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.pubsub资源输出表不支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_002";
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "test pubsub资源输出表不支持DLR.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub016.gmjson", userData, 1000, g_subName,
        snCallbackWhithDlrNoOk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 触发推送，调用DLR失败
    ret = executeCommand((char *)"gmimport -c vdata -f ./datalog_file/inp.gmdata", (char *)"success");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.notify表不支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_004";
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "test notify表不支持DLR.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub016.gmjson", userData, 1000, g_subName,
        snCallbackWhithDlrNoOk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 触发推送，调用DLR失败
    ret = executeCommand((char *)"gmimport -c vdata -f ./datalog_file/inp.gmdata", (char *)"success");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 019.普通外部表老订阅不支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_005";
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "test 普通外部表老订阅不支持DLR.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_005.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    int32_t ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub019.gmjson", userData, 1000, g_subName,
        snCallbackWhithDlrNoOk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    // 触发推送，调用DLR失败
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "D000", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 020.状态合并外部表老订阅不支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_005";
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "test 状态合并外部表老订阅不支持DLR.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_005.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"data_sync_label\":true, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataT *userData = {0};
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub019.gmjson", userData, 1000, g_subName,
        snCallbackWhithDlrNoOk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 触发推送，调用DLR失败
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "D000", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ToFreeAlloc(objIn1, recordNum);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 021.状态合并外部表新订阅支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "状态合并外部表新订阅支持DLR.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "3.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // N000
    ret = testSnMallocUserData(&userData, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackWhithDlrIsOk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    uint32_t count = -1;
    
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 022.分布式模式下tbm表支持状态合并订阅支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "状态合并外部表新订阅支持DLR.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "3.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // N000
    ret = testSnMallocUserData(&userData, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackWhithDlrIsOk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    uint32_t count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count tableB");

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 023.分布式模式下notify表支持状态合并订阅支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "状态合并外部表新订阅支持DLR.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "3.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // N000
    ret = testSnMallocUserData(&userData, 1000 * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscriptionNoSubFile(
        g_stmtSync, g_connSub, (char *)g_subJson, userData, 1000, g_subName, snCallbackWhithDlrIsOk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    uint32_t count = -1;
    SetArrayValue(objIn1, strT, recordNum, count);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count tableC");

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 024.重演数据支持写入普通表
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "重演数据支持写入普通表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 10;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据为0和为+-1数据.");
    ret = SetArrayValue(objIn1 + 1, strT, 1, -1);
    ret = SetArrayValue(objIn1 + 2, strT, 1, 0);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 3, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 0, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 025.重演数据不支持写入输入表
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    char soName2[FILE_PATH] = "DataLog_090_007_1";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "重演数据不支持写入输入表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(soName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    char dlrTable[20] = "ns1.inp";
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 0, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.重演数据不支持写入中间表
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    char soName2[FILE_PATH] = "DataLog_090_007_1";
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "重演数据不支持写入输入表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(soName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "sleep(5)s.");
    sleep(5);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    char dlrTable[10] = "ns1.mid";
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 0, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.重演数据不支持写入非外部表的输出表
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    char soName2[FILE_PATH] = "DataLog_090_007_1";
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "重演数据不支持写入输入表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    char dlrTable[20] = "ns1.tableC";
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 0, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 写写并发，不同线程写不同主键数据
void *InsertValueData(void *args)
{
    PthreadControlT begin = *(PthreadControlT *)args;
    printf("begin is…%d\n", begin.index);
    int recordNum = 500;
    int32_t ret = -1;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = writeRecord(conn, stmt, "inp", objIn1 + begin.index, begin.num, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ToFreeAlloc(objIn1, recordNum);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 更新数据
void *UpdateValueData(void *args)
{
    PthreadControlT begin = *(PthreadControlT *)args;
    printf("begin is…%d\n", begin.index);
    int recordNum = 500;
    int32_t ret = -1;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].d += 100;
    }

    // 主键更新数据
    ret = UpdateRecord(
        conn, stmt, "inp", objIn1 + begin.index, begin.num, UpdateByKeyID, false, false, GMERR_PRIMARY_KEY_VIOLATION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ToFreeAlloc(objIn1, recordNum);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 删除数据
void *DeleteValueData(void *args)
{
    PthreadControlT begin = *(PthreadControlT *)args;
    printf("begin is…%d\n", begin.index);
    int recordNum = 500;
    int32_t ret = -1;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum; i++) {
        objIn1[i].d += 100;
    }
    ret = DeletedataByPk(stmt, "inp", objIn1 + begin.index, begin.num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ToFreeAlloc(objIn1, recordNum);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 028.单机写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "单机写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {50, 50};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 100, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.(单机模式）写更新并发 写同主键数据 更新旧数据，插入写相同数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "单机写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 50, 150);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 030.(单机模式）写更新并发 写不同主键数据 更新旧数据，插入写不同数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "单机写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum / 2, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 031.(单机模式）写更新并发 写不同主键数据 更新新数据，插入写不同数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）写更新并发 写不同主键数据 更新新数据，插入写不同数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 50, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + 50, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 032.(单机模式）写删除并发 同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）写删除并发 同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程并发删除插入同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(5);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 0;
    // 读重演表数据
    count = readRecord(dlrTable, objIn1 + 50, recordNum / 2, NewTypeTableGet, true);
    realCount = readRecord(tableName, objIn1 + 50, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(realCount, count);
    system("gmsysview count N000");
    system("gmsysview count dlrTable");
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 033.(单机模式）写删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）写删除并发 不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程并发删除插入同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {50, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 034.(单机模式）更新旧数据删除并发 同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）更新旧数据删除并发 同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 不写
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum / 2, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.更新旧数据删除并发 同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0, 100 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 合并订阅推送数据条数不稳定，变更重演数据校验方式
    // 读外部表
    int32_t n000Count = 10000;
    n000Count = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(n000Count, ret);

    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 035.(单机模式）更新新数据与删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）更新新数据与删除并发 不同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 不写
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum / 2, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.更新新数据与删除并发 不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 50;
    while (dlrAndSubData.subEventValue->deleteEventValue[count].pk.a != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->deleteEventValue[count].pk.a) {
                realCount--;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(realCount, ret);
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 036.(单机模式）更新旧数据与删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）更新旧数据与删除并发 不同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 不写
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.更新新数据与删除并发 不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 50, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 50;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + 50, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 037.(单机模式）写更新旧数据删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）写更新旧数据删除并发 不同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.写更新旧数据删除并发 不同主键数据.");
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value3 = {100, 50};
    ret = pthread_create(&client_thr[2], NULL, InsertValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 150;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }
    count = 0;
    while (dlrAndSubData.subEventValue->deleteEventValue[count].pk.a != 0) {
        count++;
        realCount--;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 150, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(realCount, ret);
    if (realCount != ret) {
        system("gmsysview count dlrTable");
    }
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 038.(单机模式）写更新新数据删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）写更新新数据删除并发 不同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 500;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 100, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.写更新新数据删除并发 不同主键数据.");
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {150, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value3 = {100, 50};
    ret = pthread_create(&client_thr[2], NULL, InsertValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 50, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 100;
    count = 0;
    while (dlrAndSubData.subEventValue->deleteEventValue[count].pk.a != 0) {
        count++;
        realCount--;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + 100, 50, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (realCount != ret) {
        system("gmsysview count dlrTable");
    }
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 039.(单机模式）写更新旧数据删除并发 同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(单机模式）写更新旧数据删除并发 同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 500;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 50, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.写更新旧数据删除并发 同主键数据.");
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value3 = {0, 50};
    ret = pthread_create(&client_thr[2], NULL, InsertValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 0;
    while (count < 150) {
        if (dlrAndSubData.subEventValue->totalEventValue[count].count == 1) {
            int32_t value = dlrAndSubData.subEventValue->totalEventValue[count].pk.c;
            objIn1[value].d = dlrAndSubData.subEventValue->totalEventValue[count].updateDValue;
            AW_FUN_Log(LOG_STEP, "realCount value is %d\n", value);
            realCount++;
        }
        count++;
    }
    system("gmsysview count N000");
    system("gmsysview count dlrTable");
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 040.分布式写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "分布式写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {50, 50};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 100, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.(分布式）写更新并发 写同主键数据 更新旧数据，插入写相同数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "单机写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 50, 150);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 042.(分布式）写更新并发 写不同主键数据 更新旧数据，插入写不同数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "单机写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum / 2, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 043.(分布式）写更新并发 写不同主键数据 更新新数据，插入写不同数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）写更新并发 写不同主键数据 更新新数据，插入写不同数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 50, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + 50, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 044.(分布式）写删除并发 同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）写删除并发 同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程并发删除插入同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 50;
    while (dlrAndSubData.subEventValue->deleteEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->deleteEventValue[count].pk.a) {
                realCount--;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + 50, recordNum / 2, NewTypeTableGet, true);
    if (realCount != ret) {
        system("gmsysview record dlrTable");
        system("gmsysview count dlrTable");
        realCount = readRecord(tableName, objIn1 + 50, recordNum / 2, NewTypeTableGet, true);
    }
    AW_MACRO_EXPECT_EQ_INT(realCount, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 045.(分布式）写删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）写删除并发 不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程并发删除插入同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {50, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 046.(分布式）更新旧数据删除并发 同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）更新旧数据删除并发 同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 不写
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum / 2, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.更新旧数据删除并发 同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0, 100 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 外部表
    int32_t ret1 = readRecord(tableName, objIn1, recordNum / 2, NewTypeTableGet, true);
    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(ret1, ret);
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 047.(分布式）更新新数据与删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）更新新数据与删除并发 不同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 不写
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum / 2, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.更新新数据与删除并发 不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 50;
    while (dlrAndSubData.subEventValue->deleteEventValue[count].pk.a != 0) {
        for (int32_t i = 0; i < recordNum / 2; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->deleteEventValue[count].pk.a) {
                realCount--;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(realCount, ret);
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 048.(分布式）更新旧数据与删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）更新旧数据与删除并发 不同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 不写
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.更新新数据与删除并发 不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 50, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 50;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + 50, recordNum / 2, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count N000");
    system("gmsysview count dlrTable");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 049.(分布式）写更新旧数据删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）写更新旧数据删除并发 不同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.写更新旧数据删除并发 不同主键数据.");
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {50, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value3 = {100, 50};
    ret = pthread_create(&client_thr[2], NULL, InsertValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 150;
    while (dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue != 0) {
        for (int32_t i = 0; i < recordNum; i++) {
            if (objIn1[i].a == dlrAndSubData.subEventValue->modifyEventValue[count].pk.a) {
                objIn1[i].d = dlrAndSubData.subEventValue->modifyEventValue[count].updateDValue;
            }
        }
        count++;
    }
    count = 0;
    while (dlrAndSubData.subEventValue->deleteEventValue[count].pk.a != 0) {
        count++;
        realCount--;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 150, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(realCount, ret);
    if (realCount != ret) {
        system("gmsysview count N000");
        system("gmsysview count dlrTable");
    }

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 050.(分布式）写更新新数据删除并发 不同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）写更新新数据删除并发 不同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 500;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 100, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.写更新新数据删除并发 不同主键数据.");
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {150, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value3 = {100, 50};
    ret = pthread_create(&client_thr[2], NULL, InsertValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 50, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50, 50 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 100;
    count = 0;
    while (dlrAndSubData.subEventValue->deleteEventValue[count].pk.a != 0) {
        count++;
        realCount--;
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + 100, 50, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (realCount != ret) {
        system("gmsysview count N000");
        system("gmsysview count dlrTable");
    }

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 051.(分布式）写更新旧数据删除并发 同主键数据
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "(分布式）写更新旧数据删除并发 同主键数据");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 500;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 50, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.写更新旧数据删除并发 同主键数据.");
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    // 插入后50条
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, UpdateValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value3 = {0, 50};
    ret = pthread_create(&client_thr[2], NULL, InsertValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0, 100 * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    int32_t realCount = 0;
    while (count < 150) {
        if (dlrAndSubData.subEventValue->totalEventValue[count].count == 1) {
            int32_t value = dlrAndSubData.subEventValue->totalEventValue[count].pk.c;
            objIn1[value].d = dlrAndSubData.subEventValue->totalEventValue[count].updateDValue;
            AW_FUN_Log(LOG_STEP, "realCount value is %d\n", value);
            realCount++;
        }
        count++;
    }
    system("gmsysview count N000");
    system("gmsysview count dlrTable");
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 052.分布式persist设置为true，取消状态合并订阅查询视图预期订阅不存在
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_005";
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "分布式persist设置为true，取消状态合并订阅查询视图.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建外部表
    char tableName[10] = "N000";
    // 用例运行完查询订阅视图
    system("./execeptionSub001");
    char cmd[] = "gmsysview -q V\\$CATA_LABEL_SUBS_INFO";
    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");
    ret = executeCommand(cmd, "subVertexLabelYWY", "CHANNEL_KEY: null");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 053.设置persist为false，取消状态合并订阅查询视图
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_005";
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "设置persist为false，取消状态合并订阅查询视图.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建外部表
    char tableName[10] = "N000";
    // 用例运行完查询订阅视图
    system("./execeptionSub002");
    char cmd[] = "gmsysview -q V\\$CATA_LABEL_SUBS_INFO";
    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");
    ret = executeCommand(cmd, "subVertexLabelYWY", "CHANNEL_KEY: null");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 054.三张外部表设置不同priority订阅优先级
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_054";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "重演数据支持写入普通表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_054_001.gmjson";
    CreateTestTable(lableJsonpath1, configJson, tableB);
    char lableJsonpath2[100] = "./datalog_file/DataLog_090_054_002.gmjson";
    CreateTestTable(lableJsonpath2, configJson, tableC);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 10;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    char priority[] = "\"priority\": 0";
    sprintf(g_subJson, g_subJsonPart1, tableName, subContent, priority);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallback054);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    char priority1[] = "\"priority\": 1";
    sprintf(g_subJson, g_subJsonPart1, tableB, subContent, priority1);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallback054);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    char priority2[] = "\"priority\": 2";
    sprintf(g_subJson, g_subJsonPart1, tableC, subContent, priority1);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallback054);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 1, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");
    sleep(1);

    // 校验表触发顺序
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 055.外部表(0)，notify(2)，tbm（1）表设置不同priority订阅优先级
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "重演数据支持写入普通表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 10;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    char priority[] = "\"priority\": 0";
    sprintf(g_subJson, g_subJsonPart1, tableName, subContent, priority);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallback054);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    char priority1[] = "\"priority\": 1";
    sprintf(g_subJson, g_subJsonPart1, tableB, subContent, priority1);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallback054);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    char priority2[] = "\"priority\": 2";
    sprintf(g_subJson, g_subJsonPart1, tableC, subContent, priority1);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallback054);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 1, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");
    sleep(1);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 056.CATA_LABEL_SUBS_INFO
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "重演数据支持写入普通表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 10;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据为0和为+-1数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 3, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 视图查询
    char cmd[] = "gmsysview -q V\\$CATA_LABEL_SUBS_INFO";
    ret = executeCommand(cmd, "subVertexLabelYWY");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "subVertexLabelYWY1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "subVertexLabelYWY2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 057.QRY_STMG_SUBS_CURSOR_INFO
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "重演数据支持写入普通表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 10;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据为0和为+-1数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 3, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 视图查询
    char cmd[] = "gmsysview -q V\\$QRY_STMG_SUBS_CURSOR_INFO";
    ret = executeCommand(cmd, "subVertexLabelYWY", "LABEL_NAME: N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = executeCommand(cmd, "subVertexLabelYWY1", "LABEL_NAME: tableB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = executeCommand(cmd, "subVertexLabelYWY2", "LABEL_NAME: tableC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 058.CATA_VERTEX_LABEL_INFO
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "重演数据支持写入普通表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置100条数据
    int recordNum = 10;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tbm
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // notify
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "2.写入数据为0和为+-1数据.");
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 3, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp");
    system("gmsysview count tableC");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 视图查询
    char cmd[] = "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=N000";
    ret = executeCommand(cmd, "SUBS_TYPE: 1", "\"data_sync_label\": true");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmd1[] = "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=tableB";
    ret = executeCommand(cmd1, "SUBS_TYPE: 1", "\"data_sync_label\": true");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd2[] = "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=tableC";
    ret = executeCommand(cmd2, "SUBS_TYPE: 1", "\"data_sync_label\": true");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);

    ToFreeAlloc(objIn1, recordNum);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 059.分布式加载，含外部表，notify，tbm热升级后delete，modify，initial_load支持DLR
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    char patchSoName[FILE_PATH] = "DataLog_090_007_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "分布式加载，含外部表，notify，tbm热升级后delete，modify，initial_load支持DLR.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char dlrTable1[10] = "dlrTable1";
    char dlrTable2[10] = "dlrTable2";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync1, &g_stmtSync1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync2, &g_stmtSync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub2, NULL, 1, g_epoll_reg_info, g_subConnName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subInitialLoad);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableB, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tableB
    ret = createSubscriptionAndDlr(
        g_stmtSync1, g_connSub1, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    memset(g_subJson, 0, sizeof(g_subJson));
    sprintf(g_subJson, g_subJsonPart, tableC, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);
    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // tableC
    ret = createSubscriptionAndDlr(
        g_stmtSync2, g_connSub2, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 100};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PthreadControlT value2 = {0, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PthreadControlT value3 = {50, 50};
    ret = pthread_create(&client_thr[2], NULL, UpdateValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[2], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int cycle = 100;
    ret = 1;
    while (ret != 0 && cycle > 0) {
        ret = executeCommand(
            (char *)"gmsysview -q V\\$QRY_STMG_SUBS_CURSOR_INFO -f LABEL_NAME=tableB", "READY_CONSUME_DATA_COUNT: 0");
        sleep(1);
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char lableJsonpath2[100] = "./datalog_file/DataLog_090_059_001.gmjson";
    CreateTestTable(lableJsonpath2, configJson1, dlrTable1);
    ret = dataReplayIncre(g_connSync1, g_stmtSync1, dlrTable1, dlrAndSubData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char lableJsonpath3[100] = "./datalog_file/DataLog_090_059_002.gmjson";
    CreateTestTable(lableJsonpath3, configJson1, dlrTable2);
    ret = dataReplayIncre(g_connSync2, g_stmtSync2, dlrTable2, dlrAndSubData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync1, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync2, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < 50; i++) {
        objIn1[50 + i].d += 100;
    }
    system("gmsysview count");

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + 50, 50, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(dlrTable1, objIn1 + 50, 50, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(dlrTable2, objIn1 + 50, 50, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync1, g_stmtSync1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync2, g_stmtSync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 060.向外部表导入129条数据，预期只推送一次
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "单机写写并发 创建状态合并订阅后，多线程不同线程写不同主键数据.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.写129条数据.");
    int32_t thread_num = 1;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 129};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 129);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预期订阅回调触发次数不为2
    AW_MACRO_ASSERT_EQ_INT(g_callBackTime, 129);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
061.加载两个同名so不同namespace，输出表都是外部表输入表都是过期表，循环往so1写数据后重演写入so2，so1数据过期，so2写入so1，过期so2，循环20次
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "循环写so1,so2.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {50, 50};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 100, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
062.加载两个分布式so不同namespace，一个输出表是notify一个输出表是tbm，输入表都是过期表，循环往so1写数据后重演写入so2，so1数据过期，so2写入so1，过期so2，循环20次
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName1[FILE_PATH] = "DataLog_090_007";
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = -1;
    AW_FUN_Log(LOG_STEP, "循环写so1,so2.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";
    char tableB[10] = "tableB";
    char tableC[10] = "tableC";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建状态合并订阅.");
    char demo[] = ",";
    memset(g_subJson, 0, sizeof(g_subJson));
    char subContent[1024] = {};
    strcat(subContent, g_subDelete);
    strcat(subContent, demo);
    strcat(subContent, g_subModify);
    strcat(subContent, demo);
    strcat(subContent, g_subAge);
    sprintf(g_subJson, g_subJsonPart, tableName, subContent);
    AW_FUN_Log(LOG_STEP, "%s\n", g_subJson);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "2.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 50};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    PthreadControlT value2 = {50, 50};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    char lableJsonpath1[100] = "./datalog_file/DataLog_090_024.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    ret = dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(NewALLTypeTableStruct) * recordNum);
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 100, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 063.分布式模式下，不支持access_delta含notify表
**************************************************************************** */
TEST_F(NewSubAndDlr, DataLog_090_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    char soName1[FILE_PATH] = "DataLog_090_004";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "分布式模式下，不支持access_delta含notify表.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/DataLog_090_006.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    int32_t ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
