/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2024-03-04
 */

#include <string.h>
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
} A;
#pragma pack(0)

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    A *a = (A *)tuple;
    a->b = a->a;
    return GMERR_OK;
}
