/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: HotPatchTest.cpp
 * Description: datalog热补丁加固测试(precedence, namespace)
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2023-10-31
 */
#include "HotPatchDfx.h"
#include "t_datacom_lite.h"

using namespace std;

class HotPatchTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void HotPatchTest ::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void HotPatchTest ::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 100.precedence指定输入表执行顺序，热补丁升级触发重做，重做顺序正确
TEST_F(HotPatchTest, DataLog_053_100)
{
    char ns[] = "public";
    char soName[] = "HotPatch_001";
    char patchSoName[] = "datalogFile2/HotPatch_001_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_001.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableInput3[] = "inp3";
    int32_t data[][5] = {{1, 1, 1, 1, 0}, {2, 2, 2, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput2, data, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput3, data, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/RunLogInsert.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "结果校验");
    const char *expectResult1 = R"(
dtl_ext_func_func3: a = 1, b = 1, count = 1
dtl_ext_func_func3: a = 2, b = 2, count = 1
dtl_ext_func_func2: a = 1, b = 1, count = 1
dtl_ext_func_func2: a = 2, b = 2, count = 1
dtl_ext_func_func1: a = 1, b = 1, count = 1
dtl_ext_func_func1: a = 2, b = 2, count = 1
)";

    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/RunLogInsert.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expectResult1, result));
    free(result);

    TestUninstallDatalog(soName);
}

// 101.precedence指定中间表执行顺序，热补丁升级触发重做，重做顺序正确
TEST_F(HotPatchTest, DataLog_053_101)
{
    char ns[] = "public";
    char soName[] = "HotPatch_002";
    char patchSoName[] = "datalogFile2/HotPatch_002_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_002.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableInput3[] = "inp3";
    int32_t data[][5] = {{1, 1, 1, 1, 0}, {2, 2, 2, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/RunLogInsert.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "结果校验");
const char *expectResult2 = R"(
dtl_ext_func_func3: a = 1, b = 1, count = 1
dtl_ext_func_func2: a = 1, b = 1, count = 1
dtl_ext_func_func1: a = 1, b = 1, count = 1
dtl_ext_func_func3: a = 2, b = 2, count = 1
dtl_ext_func_func2: a = 2, b = 2, count = 1
dtl_ext_func_func1: a = 2, b = 2, count = 1
)";
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/RunLogInsert.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expectResult2, result));
    free(result);

    TestUninstallDatalog(soName);
}

// 102.precedence指定tbm输出表执行顺序，热补丁升级触发重做，重做顺序正确
TEST_F(HotPatchTest, DataLog_053_102)
{
    char ns[] = "public";
    char soName[] = "HotPatch_003";
    char patchSoName[] = "datalogFile2/HotPatch_003_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_003.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "inp1";
    int32_t data[][5] = {{1, 1, 1, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/TbmRunLog.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "结果校验");
const char *expectResult3 = R"(
dtl_tbm_tbl_tbm1, op = 1, a = 1.
dtl_tbm_tbl_tbm1, op = 1, dtlReservedCount = -1.
dtl_tbm_tbl_tbm2, op = 1, a = 1.
dtl_tbm_tbl_tbm2, op = 1, dtlReservedCount = -1.
dtl_tbm_tbl_tbm3, op = 1, a = 1.
dtl_tbm_tbl_tbm3, op = 1, dtlReservedCount = -1.
dtl_tbm_tbl_tbm3, op = 0, a = 10.
dtl_tbm_tbl_tbm3, op = 0, dtlReservedCount = 1.
dtl_tbm_tbl_tbm2, op = 0, a = 10.
dtl_tbm_tbl_tbm2, op = 0, dtlReservedCount = 1.
dtl_tbm_tbl_tbm1, op = 0, a = 10.
dtl_tbm_tbl_tbm1, op = 0, dtlReservedCount = 1.
)";
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/TbmRunLog.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expectResult3, result));
    free(result);

    TestUninstallDatalog(soName);
}

// 103.带ns的.d文件进行热补丁升级，升级成功
TEST_F(HotPatchTest, DataLog_053_103)
{
    char ns[] = "public";
    char soName[] = "HotPatch_004";
    char patchSoName[] = "datalogFile2/HotPatch_004_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_004.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "ns1.inp1";
    char tableInput2[] = "inp2";
    int32_t data[][5] = {{1, 1, 1, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput2, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "查询视图");
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 104.patch.d文件带原始.d中的ns表进行热补丁升级，升级成功
TEST_F(HotPatchTest, DataLog_053_104)
{
    char ns[] = "public";
    char soName[] = "HotPatch_005";
    char patchSoName[] = "datalogFile2/HotPatch_005_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_005.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "ns1.inp1";
    char tableInput2[] = "inp2";
    int32_t data[][5] = {{1, 1, 1, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput2, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "查询视图");
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 105.2个ns规则，使用对方ns中的表进行热补丁升级
TEST_F(HotPatchTest, DataLog_053_105)
{
    char ns[] = "public";
    char soName[] = "HotPatch_006";
    char patchSoName[] = "datalogFile2/HotPatch_006_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_006.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "ns1.inp1";
    char tableInput2[] = "ns2.inp2";
    char tableOutput1[] = "ns1.out1";
    char tableOutput2[] = "ns2.out2";
    int32_t data[][5] = {{1, 1, 1, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput2, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "查询视图");
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOutput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOutput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 106.多个ns中的规则，进行不同的规则升级（新增表join，新增function，修改规则，修改function实现）
TEST_F(HotPatchTest, DataLog_053_106)
{
    char ns[] = "public";
    char soName[] = "HotPatch_007";
    char patchSoName[] = "datalogFile2/HotPatch_007_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_007.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "ns1.inp1";
    char tableInput2[] = "ns2.inp2";
    char tableInput3[] = "ns3.inp3";
    char tableInput4[] = "ns4.inp4";
    char tableOutput1[] = "ns1.out1";
    char tableOutput2[] = "ns2.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns4.out4";
    int32_t data[][5] = {{2, 1, 3, 1, 0}, {3, 1, 5, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput2, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput3, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput4, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "查询视图");
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableOutput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOutput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(
        g_command, "\"dtlReservedCount\": 1", "\"upgradeVersion\": 1", "\"a\": 2", "\"b\": 1", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOutput3,
        g_connServer, g_testNameSpace);
    ret = executeCommand(
        g_command, "\"dtlReservedCount\": 1", "\"upgradeVersion\": 1", "\"a\": 1", "\"b\": 2", "\"c\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableOutput4,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 107.ns中的规则，连续进行热补丁升级降级
TEST_F(HotPatchTest, DataLog_053_107)
{
    char ns[] = "public";
    char soName[] = "HotPatch_008";
    char patchSoName[FILE_PATH] = {0};
    char inputFilePath[FILE_PATH] = "./datalogFile2";
    char outputFilePath[FILE_PATH] = "./datalogFile2";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_008.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "ns1.inp1";
    char tableOutput1[] = "ns1.out1";
    int32_t data[][5] = {{1, 1, 1, 1, 0}, {2, 2, 2, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁第一次升级");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    (void)sprintf(patchSoName, "%s/%s_patchV%d.so", outputFilePath, soName, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = executeCommand(g_command, "VERSION: [v1.0.0]->[v2.0.0]", "UPGRADE_VERSION: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁第二次升级");
    (void)sprintf(patchSoName, "%s/%s_patchV%d.so", outputFilePath, soName, 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = executeCommand(g_command, "VERSION: [v2.0.0]->[v3.0.0]", "UPGRADE_VERSION: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁第三次升级");
    (void)sprintf(patchSoName, "%s/%s_patchV%d.so", outputFilePath, soName, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = executeCommand(g_command, "VERSION: [v3.0.0]->[v4.0.0]", "UPGRADE_VERSION: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁第一次降级");
    (void)sprintf(patchSoName, "%s/%s_rollbackV%d.so", outputFilePath, soName, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(patchSoName));
    ret = executeCommand(g_command, "VERSION: [v4.0.0]->[v3.0.0]", "UPGRADE_VERSION: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁第二次降级");
    (void)sprintf(patchSoName, "%s/%s_rollbackV%d.so", outputFilePath, soName, 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(patchSoName));
    ret = executeCommand(g_command, "VERSION: [v3.0.0]->[v2.0.0]", "UPGRADE_VERSION: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁第三次降级");
    (void)sprintf(patchSoName, "%s/%s_rollbackV%d.so", outputFilePath, soName, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(patchSoName));
    ret = executeCommand(g_command, "VERSION: [v2.0.0]->[v1.0.0]", "UPGRADE_VERSION: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 108.2个ns规则，使用对方ns中的function进行热补丁升级，编译报错
TEST_F(HotPatchTest, DataLog_053_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile2/HotPatch_009.d";
    char inputFile2[1024] = "./datalogFile2/HotPatch_009_patch.d";
    char outputFile1[1024] = "./datalogFile2/HotPatch_009.c";
    char outputFile2[1024] = "./datalogFile2/HotPatch_009_rule.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, outputFile2, inputFile2);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 109.源.d中声明namespace，增量.d中使用using namespace的方式修改规则，join已存在的表
TEST_F(HotPatchTest, DataLog_053_109)
{
    char ns[] = "public";
    char soName[] = "HotPatch_010";
    char patchSoName[] = "datalogFile2/HotPatch_010_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_010.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    char tableInput1[] = "ns1.inp1";
    char tableInput2[] = "ns2.inp2";
    char tableOutput1[] = "ns1.out1";
    char tableOutput2[] = "ns2.out2";
    int32_t data[][5] = {{1, 1, 1, 1, 0}};
    ret = batchInput(g_stmt, tableInput1, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchInput(g_stmt, tableInput2, data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "查询视图");
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableInput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOutput1,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableOutput2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"upgradeVersion\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 110.源.d中声明namspace，using namespace 并在全局中定义同名表，新增规则中使用该表，编译报错
TEST_F(HotPatchTest, DataLog_053_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile2/HotPatch_011.d";
    char inputFile2[1024] = "./datalogFile2/HotPatch_011_patch.d";
    char outputFile1[1024] = "./datalogFile2/HotPatch_011.c";
    char outputFile2[1024] = "./datalogFile2/HotPatch_011_rule.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, outputFile2, inputFile2);
    system(g_command);
    ret = executeCommand(g_command, "Error: \"inp1\" is ambiguous defined near line 5.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 111.patch.d中没有using namespace，编译报错
TEST_F(HotPatchTest, DataLog_053_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile2/HotPatch_012.d";
    char inputFile2[1024] = "./datalogFile2/HotPatch_012_patch.d";
    char outputFile1[1024] = "./datalogFile2/HotPatch_012.c";
    char outputFile2[1024] = "./datalogFile2/HotPatch_012_rule.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, outputFile2, inputFile2);
    system(g_command);
    ret = executeCommand(g_command, "Error: upgraded relation \"out1\" is not defined in old datalog.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 112.源.d文件ns覆盖常见的表与规则进行热补丁升级，升级成功
TEST_F(HotPatchTest, DataLog_053_112)
{
    char ns[] = "public";
    char soName[] = "alltype004";
    char patchSoName[] = "datalogFile2/alltype004_patchV2.so";
    int ret = 0;
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("schemaFile/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    TestLoadDatalog("./datalogFile2/alltype004.so");

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "查询视图");
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(soName);
}

// DTS2024011715590
// 113.加载升级so，等待重做完成, 线程1,2,3对inp1,2,3写数据, 线程1写入成功, 线程2,3锁不可用
TEST_F(HotPatchTest, DataLog_053_113)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char ns[] = "public";
    char soName[] = "HotPatch_013";
    char patchSoName[] = "datalogFile2/HotPatch_013_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_013.so");

    AW_FUN_Log(LOG_STEP, "插入数据");
    char labelName1[] = "inp1";
    char labelName2[] = "inp2";
    char labelName3[] = "inp3";
    int32_t startNum = 1;
    int32_t endNum = 2;
    ret = batchSingleWrite(g_stmt, g_conn, labelName1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    endNum = 3;
    ret = batchSingleWrite(g_stmt, g_conn, labelName2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_STEP, "等待数据重做完成");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(4);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }

    ret = batchSingleWrite(g_stmt, g_conn, labelName3, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr_arr[3];
    ret = pthread_create(&thr_arr[0], NULL, Thread1WriteInput1, NULL); // GMERR_OK
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    ret = pthread_create(&thr_arr[1], NULL, Thread1WriteInput2, NULL); // 1012002
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, Thread1WriteInput3, NULL); // 1012002
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);

    TestUninstallDatalog(soName);
}

// 114.加载升级so，等待重做完成, 线程1,2,3对inp1,2,3写数据, 线程1,2,3写入成功
TEST_F(HotPatchTest, DataLog_053_114)
{
    char ns[] = "public";
    char soName[] = "HotPatch_013";
    char patchSoName[] = "datalogFile2/HotPatch_013_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_013.so");

    AW_FUN_Log(LOG_STEP, "插入数据");
    char labelName1[] = "inp1";
    char labelName2[] = "inp2";
    char labelName3[] = "inp3";
    int32_t startNum = 1;
    int32_t endNum = 2;
    ret = batchSingleWrite(g_stmt, g_conn, labelName1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    endNum = 3;
    ret = batchSingleWrite(g_stmt, g_conn, labelName2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_STEP, "等待数据重做完成");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(4);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }

    ret = batchSingleWrite(g_stmt, g_conn, labelName3, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr_arr[3];
    ret = pthread_create(&thr_arr[0], NULL, Thread1WriteInput1, NULL); // GMERR_OK
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(15 *1000 *100);
    ret = pthread_create(&thr_arr[1], NULL, Thread2WriteInput2, NULL); // GMERR_OK
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, Thread2WriteInput3, NULL); // GMERR_OK
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);

    TestUninstallDatalog(soName);
}

// 115.加载升级so，等待重做完成, 线程1,2,3对inp1,2,3写数据, 线程1,2写入成功, 线程3锁不可用
TEST_F(HotPatchTest, DataLog_053_115)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char ns[] = "public";
    char soName[] = "HotPatch_013";
    char patchSoName[] = "datalogFile2/HotPatch_013_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile2/HotPatch_013.so");

    AW_FUN_Log(LOG_STEP, "插入数据");
    char labelName1[] = "inp1";
    char labelName2[] = "inp2";
    char labelName3[] = "inp3";
    int32_t startNum = 1;
    int32_t endNum = 2;
    ret = batchSingleWrite(g_stmt, g_conn, labelName1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    endNum = 3;
    ret = batchSingleWrite(g_stmt, g_conn, labelName2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_STEP, "等待数据重做完成");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(4);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }

    startNum = 4;
    endNum = 4;
    ret = batchSingleWrite(g_stmt, g_conn, labelName1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 3;
    endNum = 4;
    ret = batchSingleWrite(g_stmt, g_conn, labelName3, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr_arr[3];
    ret = pthread_create(&thr_arr[0], NULL, Thread1WriteInput1, NULL); // GMERR_OK
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(15 *1000 *100);
    ret = pthread_create(&thr_arr[1], NULL, Thread3WriteInput2, NULL); // GMERR_OK
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, Thread3WriteInput3, NULL); // 1012002
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);

    TestUninstallDatalog(soName);
}
