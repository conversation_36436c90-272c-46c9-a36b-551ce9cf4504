/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: HotPatchRCA.cpp
 * Description: datalog热补丁加固测试(precedence, namespace)
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2023-10-31
 */
#include "HotPatchDfx.h"
#include "t_datacom_lite.h"

using namespace std;

class HotPatchRCA : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void HotPatchRCA ::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void HotPatchRCA ::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 201.block 0，access_delta（outD），进行热补丁升级
TEST_F(HotPatchRCA, DISABLED_DataLog_053_201)
{
    char soName[] = "HotPatchRCA_001";
    char patchSoName[] = "datalogFile3/HotPatchRCA_001_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_001.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    int32_t startNum = 1;
    int32_t endNum = 2;
    char tableInput1[] = "inpA";
    char tableOutput1[] = "outD";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "undo, cnt = 0, a = 1, b = 1, dtlReservedCount = 1, upgradeVersion = 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "redo, cnt = 0, a = 2, b = 2, dtlReservedCount = 1, upgradeVersion = 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "redo, cnt = 0, a = 2, b = 2, dtlReservedCount = 1, upgradeVersion = 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "redo, cnt = 0, a = 1, b = 1, dtlReservedCount = 1, upgradeVersion = 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 202.block 0，access_delta（inpA2），进行热补丁升级
TEST_F(HotPatchRCA, DISABLED_DataLog_053_202)
{
    char soName[] = "HotPatchRCA_002";
    char patchSoName[] = "datalogFile3/HotPatchRCA_002_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_002.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    int32_t startNum = 1;
    int32_t endNum = 2;
    char tableInput1[] = "inpA";
    char tableOutput1[] = "outD";
    char tableOutput2[] = "outD2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s |grep upgradeVersion |wc -l", g_toolPath,
        tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 203.block 0，agg(many_to_one)，(access_delta和access_current)，进行热补丁升级
TEST_F(HotPatchRCA, DataLog_053_203)
{
    char soName[] = "HotPatchRCA_003";
    char patchSoName[] = "datalogFile3/HotPatchRCA_003_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_003.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    int32_t startNum = 1;
    int32_t endNum = 2;
    char tableInput1[] = "inpA";
    char tableOutput1[] = "outD";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "dtlReservedCount = 1, upgradeVersion = 1,a = 2, b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "dtlReservedCount = 1, upgradeVersion = 1,a = 1, b = 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "dtlReservedCount = 1, upgradeVersion = 1,a = 2, b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 204.block 0，agg(many_to_many)，(access_delta和access_current)，进行热补丁升级
TEST_F(HotPatchRCA, DataLog_053_204)
{
    char soName[] = "HotPatchRCA_004";
    char patchSoName[] = "datalogFile3/HotPatchRCA_004_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_004.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    int32_t startNum = 1;
    int32_t endNum = 2;
    char tableInput1[] = "inpA";
    char tableOutput1[] = "outD";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "dtlReservedCount = 1, upgradeVersion = 1,a = 2, b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "dtlReservedCount = 1, upgradeVersion = 1,a = 1, b = 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "dtlReservedCount = 1, upgradeVersion = 1,a = 2, b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 205.block0 timeout，(access_current)，进行热补丁升级
TEST_F(HotPatchRCA, DataLog_053_205)
{
    char soName[] = "HotPatchRCA_005";
    char patchSoName[] = "datalogFile3/HotPatchRCA_005_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_005.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    int32_t startNum = 1;
    int32_t endNum = 5;
    char tableInput1[] = "inpA1";
    char tableInput2[] = "inpA2";
    ret = RCABatchSingleWriteTimeOut(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA.txt");
    startNum = 1;
    endNum = 1;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWriteTimeOut(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3); // 等待数据过期

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 1, b = 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 2, b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 3, b = 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 4, b = 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 5, b = 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 206.block1 timeout，(access_current)，进行热补丁升级
TEST_F(HotPatchRCA, DataLog_053_206)
{
    char soName[] = "HotPatchRCA_006";
    char patchSoName[] = "datalogFile3/HotPatchRCA_006_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_006.so");

    AW_FUN_Log(LOG_STEP, "写入数据");
    int32_t startNum = 1;
    int32_t endNum = 5;
    char tableInput1[] = "inpA1";
    char tableInput2[] = "inpA2";
    ret = RCABatchSingleWriteTimeOut(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA.txt");
    startNum = 1;
    endNum = 1;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWriteTimeOut(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3); // 等待数据过期

    AW_FUN_Log(LOG_STEP, "结果校验");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 1, b = 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 2, b = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 3, b = 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 4, b = 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "inpA2, dtlReservedCount = 1, upgradeVersion = 0,a = 5, b = 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 210.patch.d显示声明block为1，修改r1和r0规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_210)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_010.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_010.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_010_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_010_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r0\"(near line5) and rule:\"r1\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 211.patch.d显示声明block为1，修改r3和r0规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_211)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_011.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_011.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_011_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_011_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r0\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 212.patch.d显示声明block为1，修改r3和r2规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_212)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_012.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_012.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_012_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_012_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r2\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 213.patch.d显示声明block为1，修改r3和r2和r1规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_213)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_013.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_013.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_013_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_013_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r2\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 214.patch.d显示声明block为1，修改r3和r2和r1和r0规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_214)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_014.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_014.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_014_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_014_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r2\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 215.patch.d显示声明block为1，修改r3和r4规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_215)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_015.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_015.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_015_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_015_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r4\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 216.patch.d显示声明block为1，修改r3和r2规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_216)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_016.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_016.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_016_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_016_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r2\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 217.patch.d显示声明block为1，修改r3和r4规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_217)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_017.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_017.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_017_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_017_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r4\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 218.patch.d显示声明block为1，修改r3和r4规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_218)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_018.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_018.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_018_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_018_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r4\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 219.patch.d显示声明block为1，修改r3和r4规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_219)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_019.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_019.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_019_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_019_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r4\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 220.patch.d显示声明block为1，修改r3和r5规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_220)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_020.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_020.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_020_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_020_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r5\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 221.patch.d显示声明block为1，修改r3和r7规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_221)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_021.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_021.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_021_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_021_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r7\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 222.patch.d显示声明block为1，修改r4和r5和r6规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_222)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_022.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_022.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_022_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_022_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 223.patch.d显示声明block为1，修改r3和r7规则，预期编译报错(many_to_one, access_delta)
TEST_F(HotPatchRCA, DataLog_053_223)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_023.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_023.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_023_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_023_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r7\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 224.patch.d显示声明block为1，修改r3和r7规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_224)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_024.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_024.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_024_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_024_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 225.patch.d显示声明block为1，修改r3和r4规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_225)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_025.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_025.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_025_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_025_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r4\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 226.patch.d显示声明block为1，修改r3和r5规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_226)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_026.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_026.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_026_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_026_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r5\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 227.patch.d显示声明block为1，修改r3和r7规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_227)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_027.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_027.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_027_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_027_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r7\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 228.patch.d显示声明block为1，修改r4和r5和r6规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_228)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_028.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_028.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_028_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_028_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 229.patch.d显示声明block为1，修改r3和r7规则，预期编译报错(many_to_many, access_delta)
TEST_F(HotPatchRCA, DataLog_053_229)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_029.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_029.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_029_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_029_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r7\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 230.patch.d显示声明block为1，修改r3和r7规则，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_230)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_030.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_030.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_030_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_030_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command,
        "rule:\"r7\"(near line5) and rule:\"r3\"(near line4) are in same topo, should not alter at same time");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 231.分三个补丁依次修改r3, r4, r7规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_231)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_031.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_031.c";
    char oldFile1[1024] = "./datalogFile3/HotPatchRCA_031_rule.d";
    char patchFile1[1024] = "./datalogFile3/HotPatchRCA_031_patch.d";

    char oldFile2[1024] = "./datalogFile3/HotPatchRCA_031_ruleV2.d";
    char patchFile2[1024] = "./datalogFile3/HotPatchRCA_031_ruleV2_patch.d";

    char oldFile3[1024] = "./datalogFile3/HotPatchRCA_031_ruleV3.d";
    char patchFile3[1024] = "./datalogFile3/HotPatchRCA_031_ruleV3_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile1, patchFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile2, patchFile2);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile3, patchFile3);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 232.path.d显示声明block为1，新增一张可更新表，与原先的表进行join，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_232)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_032.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_032.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_032_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_032_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "\"%block 1\" not support topo combine, and rule:\"r0\" involve topo combine mid1 "
                                    "and inp1_1 are in different topo near line 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 233.path.d显示声明block为1，新增一张可更新表，与原先的function进行join，预期编译报错
TEST_F(HotPatchRCA, DataLog_053_233)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_033.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_033.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_033_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_033_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "\"%block 1\" not support topo combine, and rule:\"r1\" involve topo combine mid2 "
                                    "and inp1_1 are in different topo near line 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 234.原始.d含两张图，patch.d显示声明block为1，将已有的可更新输入表和另外一个图的输入表进行join，预期编译表错
TEST_F(HotPatchRCA, DataLog_053_234)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_034.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_034.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_034_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_034_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "\"%block 1\" not support topo combine, and rule:\"r7\" involve topo combine out4 "
                                    "and inp1 are in different topo near line 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 235.原始.d含两张图，patch.d显示声明block为1，将已有的可更新输入表和另外一个图的fun1进行join，预期编译表错
TEST_F(HotPatchRCA, DataLog_053_235)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_035.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_035.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_035_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_035_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "\"%block 1\" not support topo combine, and rule:\"r7\" involve topo combine out4 "
                                    "and inp1 are in different topo near line 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 236.原始.d含两张图，patch.d显示声明block为1，将已有的可更新输入表和另外一个图的fun2进行join，预期编译表错
TEST_F(HotPatchRCA, DataLog_053_236)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_036.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_036.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_036_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_036_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "\"%block 1\" not support topo combine, and rule:\"r7\" involve topo combine out4 "
                                    "and inp1 are in different topo near line 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 237.patch.d显示声明block为1，将已有的可更新输入表和同1个图图上的表进行join，预期编译成功，执行成功
TEST_F(HotPatchRCA, DataLog_053_237)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_037";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_037_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_037_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_037.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 238.patch.d显示声明block为1，将已有的可更新输入表和同1个图图上的function进行join，预期编译成功，执行成功
TEST_F(HotPatchRCA, DataLog_053_238)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_038";
    char patchSoName[] = "datalogFile3/HotPatchRCA_038_patchV2.so";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_038_rollbackV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_038.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 239.patch.d显示声明block为1，将已有的可更新输入表和同1个图上的表进行join，预期编译成功，执行成功
TEST_F(HotPatchRCA, DataLog_053_239)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_039";
    char patchSoName[] = "datalogFile3/HotPatchRCA_039_patchV2.so";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_039_rollbackV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_039.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 240.patch.d显示声明block为1，将已有的可更新输入表和同1个图图上的function进行join，预期编译成功，执行成功
TEST_F(HotPatchRCA, DISABLED_DataLog_053_240)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_040";
    char patchSoName[] = "datalogFile3/HotPatchRCA_040_patchV2.so";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_040_rollbackV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_040.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 241.patch.d显示声明block为0，修改r0、r4规则预期编译成功
TEST_F(HotPatchRCA, DataLog_053_241)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_041.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_041.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_041_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_041_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 242.patch.d显示声明block为1，修改r2、r4规则预期编译成功
TEST_F(HotPatchRCA, DataLog_053_242)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_042.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_042.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_042_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_042_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 243.access_current(inp2)，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_243)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_043.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_043.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_043_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_043_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 244.access_current(mid2)，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_244)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_044.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_044.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_044_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_044_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 245.access_current(out2)，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_245)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_045.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_045.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_045_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_045_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 246.access_delta(inp2)，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_246)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_046.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_046.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_046_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_046_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 247.access_delta(mid2)，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_247)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_047.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_047.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_047_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_047_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 248.access_delta(out2)，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_248)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_048.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_048.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_048_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_048_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "output table:\"out2\" accessed(delta) by func:\"func\" is not supported to redo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 249.access_current(inp2)，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_249)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_049.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_049.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_049_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_049_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 250.access_current(mid2)，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_250)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_050.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_050.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_050_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_050_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 251.access_current(out2)patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_251)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_051.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_051.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_051_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_051_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 252.access_delta(inp2)，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_252)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_052.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_052.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_052_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_052_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 253.access_delta(mid2)，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_253)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_053.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_053.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_053_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_053_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 254.access_delta(out2)，patch.d显示声明block为1，修改r1规则，预期编译失败
// 备注Release编译成功
TEST_F(HotPatchRCA, DataLog_053_254)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_054.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_054.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_054_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_054_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "output table:\"out2\" accessed(delta) by func:\"func\" is not supported to redo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 255.access_current(inp2)，many_to_one，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_255)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_055.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_055.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_055_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_055_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 256.access_current(mid2)，many_to_one，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_256)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_056.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_056.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_056_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_056_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 257.access_current(out2)，many_to_one，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_257)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_057.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_057.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_057_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_057_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 258.access_delta(inp2)，many_to_one，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_258)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_058.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_058.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_058_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_058_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 259.access_delta(mid2)，many_to_one，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_259)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_059.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_059.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_059_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_059_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 260.access_delta(out2)，many_to_one，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_260)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_060.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_060.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_060_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_060_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "output table:\"out2\" accessed(delta) by func:\"agg\" is not supported to redo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 261.access_current(inp2)，many_to_one，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_261)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_061.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_061.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_061_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_061_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 262.access_current(mid2)，many_to_one，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_262)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_062.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_062.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_062_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_062_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 263.access_current(out2)，many_to_one，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_263)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_063.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_063.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_063_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_063_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 264.access_delta(inp2)，many_to_one，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_264)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_064.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_064.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_064_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_064_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 265.access_delta(mid2)，many_to_one，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_265)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_065.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_065.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_065_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_065_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 266.access_delta(out2)，many_to_one，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_266)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_066.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_066.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_066_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_066_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}// 267.access_current(inp2)，many_to_many，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_267)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_067.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_067.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_067_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_067_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 268.access_current(mid2)，many_to_many，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_268)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_068.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_068.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_068_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_068_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 269.access_current(out2)，many_to_many，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_269)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_069.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_069.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_069_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_069_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 270.access_delta(inp2)，many_to_many，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_270)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_070.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_070.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_070_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_070_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 271.access_delta(mid2)，many_to_many，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_271)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_071.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_071.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_071_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_071_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 272.access_delta(out2)，many_to_many，patch.d显示声明block为0，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_272)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_072.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_072.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_072_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_072_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "output table:\"out2\" accessed(delta) by func:\"agg\" is not supported to redo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 273.access_current(inp2)，many_to_many，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_273)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_073.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_073.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_073_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_073_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 274.access_current(mid2)，many_to_many，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_274)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_074.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_074.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_074_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_074_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 275.access_current(out2)，many_to_many，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_275)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_075.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_075.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_075_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_075_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 276.access_delta(inp2)，many_to_many，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_276)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_076.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_076.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_076_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_076_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 277.access_delta(mid2)，many_to_many，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_277)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_077.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_077.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_077_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_077_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 278.access_delta(out2)，many_to_many，patch.d显示声明block为1，修改r1规则，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_278)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_078.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_078.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_078_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_078_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 279.block为0，enableDML为0，两个图，加载升级so重做卡第一个图，并发写第二个图能够拿到锁（func 3s, 写11条数据）
TEST_F(HotPatchRCA, DataLog_053_279)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    char soName[] = "HotPatchRCA_079";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_079_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_079_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_079.so");

    int32_t startNum = 1;
    int32_t endNum = 11;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 1;
    endNum = 10;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(66);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableInput1, g_testNameSpace);
    ret = executeCommand(g_command, "11");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableInput2, g_testNameSpace);
    ret = executeCommand(g_command, "10");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    startNum = 11;
    endNum = 20;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(66);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableInput1, g_testNameSpace);
    ret = executeCommand(g_command, "11");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableInput2, g_testNameSpace);
    ret = executeCommand(g_command, "20");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 280.block为0，enableDML为1，两个图，加载升级so重做卡第一个图，并发写第二个图能够拿到锁（func 3s, 写11条数据）
TEST_F(HotPatchRCA, DataLog_053_280)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");
    char soName[] = "HotPatchRCA_079";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_079_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_079_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_079.so");

    int32_t startNum = 1;
    int32_t endNum = 11;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 1;
    endNum = 10;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(66);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableInput1, g_testNameSpace);
    ret = executeCommand(g_command, "11");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableInput2, g_testNameSpace);
    ret = executeCommand(g_command, "10");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    startNum = 11;
    endNum = 20;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(66);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableInput1, g_testNameSpace);
    ret = executeCommand(g_command, "11");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput1, g_testNameSpace);
    ret = executeCommand(g_command, "11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableInput2, g_testNameSpace);
    ret = executeCommand(g_command, "20");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -ns %s", g_toolPath, tableOutput2, g_testNameSpace);
    ret = executeCommand(g_command, "20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    TestUninstallDatalog(soName);
}

// 281.block为0，lpm索引含namespace，access_current(含lpm索引的表)，热补丁升级，再进行热补丁降级
TEST_F(HotPatchRCA, DataLog_053_281)
{
    char labelNameA[] = "ns1.A";
    char labelNameB[] = "ns1.B";
    char labelNameC[] = "ns1.C";
    char labelNameD[] = "ns1.D";
    char soName[] = "HotPatchRCA_081";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_081_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_081_patchV2.so";
    TestLoadDatalog("./datalogFile3/HotPatchRCA_081.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    const char *ipAddr1 = "0.0.0.0";
    int32_t destIp1 = LpmTransIp(ipAddr1);
    int32_t vrId = 0;
    int32_t vrfIndex = 0;
    int8_t maskLen = 32;

    for (int i = 1; i <= 1; i++) {
        ret = Lpm4singleRecordInsert(g_stmt, labelNameB, i, vrId, vrfIndex, destIp1 + i, maskLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 1; i <= 1; i++) {
        ret = Lpm4SingleRecordInsertA(g_stmt, labelNameA, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA2.txt");
    ret = executeCommand(g_command,
        "dtlReservedCount = 1, upgradeVersion = 1, id = 1, vr_id = 0, vrf_index = 0, dest_ip_addr = 1, mask_len = 32.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    system("rm -rf /root/HotPatchRCA2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA2.txt");
    ret = executeCommand(g_command,
        "dtlReservedCount = 1, upgradeVersion = 0, id = 1, vr_id = 0, vrf_index = 0, dest_ip_addr = 1, mask_len = 32.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(soName);
}

// 282.block为1，lpm索引含namespace，access_current(含lpm索引的表)，热补丁升级，再进行热补丁降级
TEST_F(HotPatchRCA, DataLog_053_282)
{
    char labelNameA[] = "ns1.A";
    char labelNameB[] = "ns1.B";
    char labelNameC[] = "ns1.C";
    char labelNameD[] = "ns1.D";
    char soName[] = "HotPatchRCA_082";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_082_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_082_patchV2.so";
    TestLoadDatalog("./datalogFile3/HotPatchRCA_082.so");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "插入数据");
    const char *ipAddr1 = "0.0.0.0";
    int32_t destIp1 = LpmTransIp(ipAddr1);
    int32_t vrId = 0;
    int32_t vrfIndex = 0;
    int8_t maskLen = 32;

    for (int i = 1; i <= 1; i++) {
        ret = Lpm4singleRecordInsert(g_stmt, labelNameB, i, vrId, vrfIndex + i, destIp1 + i, maskLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 1; i <= 1; i++) {
        ret = Lpm4SingleRecordInsertA(g_stmt, labelNameA, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    system("rm -rf /root/HotPatchRCA2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA2.txt");
    ret = executeCommand(g_command,
        "dtlReservedCount = 1, upgradeVersion = 1, id = 1, vr_id = 0, vrf_index = 0, dest_ip_addr = 1, mask_len = 32.");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    system("rm -rf /root/HotPatchRCA2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/HotPatchRCA2.txt");
    ret = executeCommand(g_command,
        "dtlReservedCount = 1, upgradeVersion = 0, id = 1, vr_id = 0, vrf_index = 0, dest_ip_addr = 1, mask_len = 32.");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    // 卸载
    TestUninstallDatalog(soName);
}

// 283.变长bytes和位域，显示声明block为0，进行热补丁升级
TEST_F(HotPatchRCA, DataLog_053_283)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_083";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_083_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_083_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_083.so");

    int32_t startNum = 1;
    int32_t endNum = 1;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableOut1[] = "out1";
    ret = RCABatchSingleWriteByteAndBitfield(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWriteByteAndBitfield(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s", g_toolPath, tableInput1, g_testNameSpace);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1", "\"upgradeVersion\": 1", "\"a\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s", g_toolPath, tableInput2, g_testNameSpace);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1", "\"upgradeVersion\": 1", "\"a\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s", g_toolPath, tableOut1, g_testNameSpace);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1", "\"upgradeVersion\": 1", "\"a\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(soName);
}

// 284.变长bytes和位域，显示声明block为1，enableDML 0，进行热补丁升级, 5*1级hung死值(euler是3s，设备11s)
TEST_F(HotPatchRCA, DataLog_053_284)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);

    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    char soName[] = "HotPatchRCA_084";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_084_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_084_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_084.so");

    int32_t startNum = 1;
    int32_t endNum = 12;
    char tableInput1[] = "inp1";
    char tableMid1[] = "mid1";
    char tableOut1[] = "out1";
    ret = RCABatchSingleWriteByteAndBitfield(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    startNum = 20;
    endNum = 20;

    sleep(1);

    //每次重试都会与5*1级hung比较，重试时间按事务锁时间，欧拉是1s/次，设备是30s/次，欧拉精确返回1012002，设备返回1016004.
    ret = RCABatchSingleWriteByteAndBitfield(g_stmt, g_conn, tableInput1, startNum, endNum);
#if defined(ENV_EULER)
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
#endif

    // 卸载
    AW_FUN_Log(LOG_STEP, "查询视图");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(35);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }
    TestUninstallDatalog(soName);
}

// 285.变长bytes和位域，显示声明block为1，enableDML 1，进行热补丁升级, 5*1级hung死值(euler是3s，设备11s)
TEST_F(HotPatchRCA, DataLog_053_285)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);

    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");
    char soName[] = "HotPatchRCA_084";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_084_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_084_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_084.so");

    int32_t startNum = 1;
    int32_t endNum = 12;
    char tableInput1[] = "inp1";
    char tableMid1[] = "mid1";
    char tableOut1[] = "out1";
    ret = RCABatchSingleWriteByteAndBitfield(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    startNum = 20;
    endNum = 20;

    sleep(1);

    //每次重试都会与5*1级hung比较，重试时间按事务锁时间，欧拉是1s/次，设备是30s/次，欧拉精确返回1012002，设备返回1016004.
    ret = RCABatchSingleWriteByteAndBitfield(g_stmt, g_conn, tableInput1, startNum, endNum);
#if defined(ENV_EULER)
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
#endif

    // 卸载
    AW_FUN_Log(LOG_STEP, "查询视图");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK) {
        sleep(35);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    }
    TestUninstallDatalog(soName);
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
}

// 286.block1，2个topo图，都使用同一个function，patch.d修改r1，预期编译失败
TEST_F(HotPatchRCA, DataLog_053_286)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_086.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_086.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_086_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_086_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "\"%block 1\" not support topo combine, and rule:\"r1\" involve topo combine out2 "
                                    "and inp1 are in different topo near line 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 287.block0，2个topo图，都使用同一个function，patch.d修改r1，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_287)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_087.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_087.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_087_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_087_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 288.block1，2个topo图，都使用同一个agg，patch.d修改r1，预期编译失败
TEST_F(HotPatchRCA, DataLog_053_288)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_088.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_088.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_088_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_088_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "\"%block 1\" not support topo combine, and rule:\"r1\" involve topo combine out1 "
                                    "and inp2 are in different topo near line 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 289.block0，2个topo图，都使用同一个agg，patch.d修改r1，预期编译成功
TEST_F(HotPatchRCA, DataLog_053_289)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFile3/HotPatchRCA_089.d";
    char outputFile1[1024] = "./datalogFile3/HotPatchRCA_089.c";
    char oldFile[1024] = "./datalogFile3/HotPatchRCA_089_rule.d";
    char patchFile[1024] = "./datalogFile3/HotPatchRCA_089_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 290.通过access_current中间表，将2个图图合成一张图，patch.d显示声明block为1，修改r0规则
TEST_F(HotPatchRCA, DataLog_053_290)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_090";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_090_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_090_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_090.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 291.通过access_current中间表，将2个图图合成一张图，patch.d显示声明block为0，修改r0规则
TEST_F(HotPatchRCA, DataLog_053_291)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_091";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_091_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_091_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_091.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 292.通过access_current中间表，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_292)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_092";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_092_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_092_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_092.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 293.通过access_current中间表，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_293)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_093";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_093_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_093_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_093.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 294.通过access_current中间表，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_294)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_094";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_094_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_094_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_094.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 295.通过access_current中间表，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_295)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_095";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_095_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_095_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_095.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 296.通过access_current中间表，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则，新增inp1进行join
TEST_F(HotPatchRCA, DataLog_053_296)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_096";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_096_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_096_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_096.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 297.通过access_current中间表，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则，新增inp2进行join
TEST_F(HotPatchRCA, DataLog_053_297)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_097";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_097_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_097_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_097.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 298.通过access_delta中间表，将2个图图合成一张图，patch.d显示声明block为1，修改r0规则
TEST_F(HotPatchRCA, DataLog_053_298)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_098";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_098_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_098_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_098.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 299.通过access_delta中间表，将2个图图合成一张图，patch.d显示声明block为0，修改r0规则
TEST_F(HotPatchRCA, DataLog_053_299)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_099";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_099_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_099_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_099.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 300.通过access_delta中间表，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_300)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_100";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_100_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_100_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_100.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 301.通过access_delta中间表，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_301)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_101";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_101_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_101_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_101.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 302.通过access_delta中间表，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_302)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_102";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_102_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_102_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_102.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 303.通过access_delta中间表，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_303)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_103";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_103_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_103_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_103.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 304.通过access_delta中间表，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则，新增inp1进行join
TEST_F(HotPatchRCA, DataLog_053_304)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_104";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_104_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_104_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_104.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 305.通过access_delta中间表，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则，新增inp2进行join
TEST_F(HotPatchRCA, DataLog_053_305)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_105";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_105_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_105_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_105.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 306.通过access_current输入表和输出表，将2个图图合成一张图，patch.d显示声明block为1，修改r0规则
TEST_F(HotPatchRCA, DataLog_053_306)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_106";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_106_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_106_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_106.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 307.通过access_current输入表和输出表，将2个图图合成一张图，patch.d显示声明block为0，修改r0规则
TEST_F(HotPatchRCA, DataLog_053_307)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_107";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_107_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_107_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_107.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 308.通过access_current输入表和输出表，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_308)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_108";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_108_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_108_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_108.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 309.通过access_current输入表和输出表，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_309)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_109";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_109_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_109_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_109.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 310.通过access_delta输入表，将2个图图合成一张图，patch.d显示声明block为1，修改r0规则
TEST_F(HotPatchRCA, DataLog_053_310)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_110";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_110_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_110_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_110.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 311.通过access_delta输入表，将2个图图合成一张图，patch.d显示声明block为0，修改r0规则
TEST_F(HotPatchRCA, DataLog_053_311)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_111";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_111_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_111_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_111.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 312.通过access_delta输入表，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_312)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_112";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_112_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_112_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_112.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// many_to_one, access_current
// 313.agg(many_to_one)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_313)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_113";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_113_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_113_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_113.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 314.agg(many_to_one)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_314)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_114";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_114_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_114_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_114.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 315.agg(many_to_one)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_315)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_115";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_115_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_115_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_115.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 316.agg(many_to_one)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_316)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_116";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_116_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_116_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_116.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 317.agg(many_to_one)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_317)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_117";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_117_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_117_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_117.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 318.agg(many_to_one)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_318)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_118";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_118_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_118_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_118.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 319.agg(many_to_one)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_319)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_119";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_119_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_119_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_119.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 320.agg(many_to_one)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_320)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_120";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_120_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_120_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_120.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 321.agg(many_to_one)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_321)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_121";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_121_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_121_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_121.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 322.agg(many_to_one)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_322)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_122";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_122_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_122_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_122.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 323.agg(many_to_one)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_323)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_123";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_123_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_123_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_123.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 324.agg(many_to_one)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_324)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_124";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_124_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_124_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_124.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 325.agg(many_to_one)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_325)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_125";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_125_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_125_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_125.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 326.agg(many_to_one)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_326)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_126";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_126_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_126_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_126.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 327.agg(many_to_one)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_327)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_127";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_127_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_127_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_127.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 328.agg(many_to_one)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_328)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_128";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_128_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_128_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_128.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 329.agg(many_to_one)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_329)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_129";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_129_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_129_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_129.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 330.agg(many_to_one)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_330)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_130";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_130_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_130_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_130.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// many_to_one, access_delta
// 331.agg(many_to_one)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_331)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_131";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_131_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_131_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_131.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 332.agg(many_to_one)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_332)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_132";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_132_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_132_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_132.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 333.agg(many_to_one)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_333)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_133";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_133_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_133_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_133.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 334.agg(many_to_one)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_334)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_134";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_134_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_134_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_134.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 335.agg(many_to_one)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_335)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_135";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_135_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_135_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_135.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 336.agg(many_to_one)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_336)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_136";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_136_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_136_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_136.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 337.agg(many_to_one)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_337)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_137";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_137_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_137_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_137.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 338.agg(many_to_one)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_338)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_138";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_138_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_138_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_138.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 339.agg(many_to_one)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_339)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_139";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_139_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_139_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_139.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 340.agg(many_to_one)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_340)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_140";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_140_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_140_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_140.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 341.agg(many_to_one)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_341)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_141";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_141_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_141_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_141.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 342.agg(many_to_one)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_342)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_142";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_142_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_142_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_142.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 343.agg(many_to_one)通过access_delta输入表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_343)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_143";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_143_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_143_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_143.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 344.agg(many_to_one)通过access_delta输入表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_344)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_144";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_144_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_144_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_144.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 345.agg(many_to_one)通过access_delta输入表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_345)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_145";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_145_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_145_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_145.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// many_to_many, access_current
// 346.agg(many_to_many)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_346)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_146";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_146_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_146_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_146.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 347.agg(many_to_many)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_347)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_147";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_147_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_147_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_147.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 348.agg(many_to_many)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_348)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_148";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_148_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_148_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_148.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 349.agg(many_to_many)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_349)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_149";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_149_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_149_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_149.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 350.agg(many_to_many)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_350)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_150";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_150_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_150_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_150.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 351.agg(many_to_many)通过access_current输出表out2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_351)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_151";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_151_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_151_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_151.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 352.agg(many_to_many)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_352)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_152";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_152_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_152_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_152.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 353.agg(many_to_many)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_353)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_153";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_153_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_153_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_153.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 354.agg(many_to_many)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_354)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_154";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_154_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_154_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_154.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 355.agg(many_to_many)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_355)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_155";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_155_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_155_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_155.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 356.agg(many_to_many)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_356)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_156";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_156_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_156_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_156.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 357.agg(many_to_many)通过access_current输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_357)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_157";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_157_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_157_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_157.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 358.agg(many_to_many)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_358)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_158";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_158_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_158_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_158.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 359.agg(many_to_many)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_359)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_159";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_159_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_159_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_159.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 360.agg(many_to_many)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_360)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_160";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_160_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_160_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_160.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 361.agg(many_to_many)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_361)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_161";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_161_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_161_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_161.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 362.agg(many_to_many)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_362)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_162";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_162_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_162_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_162.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 363.agg(many_to_many)通过access_current中间表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_363)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_163";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_163_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_163_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_163.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// many_to_many, access_delta
// 364.agg(many_to_many)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_364)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_164";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_164_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_164_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_164.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 365.agg(many_to_many)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_365)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_165";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_165_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_165_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_165.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 366.agg(many_to_many)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_366)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_166";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_166_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_166_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_166.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 367.agg(many_to_many)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_367)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_167";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_167_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_167_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_167.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 368.agg(many_to_many)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_368)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_168";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_168_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_168_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_168.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 369.agg(many_to_many)通过access_delta输入表inp2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_369)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_169";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_169_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_169_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_169.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 370.agg(many_to_many)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_370)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_170";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_170_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_170_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_170.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 371.agg(many_to_many)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_371)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_171";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_171_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_171_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_171.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 372.agg(many_to_many)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_372)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_172";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_172_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_172_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_172.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 373.agg(many_to_many)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_373)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_173";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_173_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_173_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_173.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 374.agg(many_to_many)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_374)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_174";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_174_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_174_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_174.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 375.agg(many_to_many)通过access_delta输入表mid2，将2个图图合成一张图，patch.d显示声明block为0，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_375)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_175";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_175_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_175_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_175.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 376.agg(many_to_many)通过access_delta输入表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r1规则
TEST_F(HotPatchRCA, DataLog_053_376)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_176";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_176_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_176_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_176.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 377.agg(many_to_many)通过access_delta输入表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r2规则
TEST_F(HotPatchRCA, DataLog_053_377)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_177";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_177_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_177_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_177.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 378.agg(many_to_many)通过access_delta输入表out2，将2个图图合成一张图，patch.d显示声明block为1，修改r3规则
TEST_F(HotPatchRCA, DataLog_053_378)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soName[] = "HotPatchRCA_178";
    char rollbackSoName[] = "datalogFile3/HotPatchRCA_178_rollbackV2.so";
    char patchSoName[] = "datalogFile3/HotPatchRCA_178_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile3/HotPatchRCA_178.so");

    int32_t startNum = 1;
    int32_t endNum = 100;
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级");
    startNum = 101;
    endNum = 200;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RCABatchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁降级");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    ret = CheckCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}
