/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.0.0 迭代一Datalog粗粒度锁性能优化-并发可靠性测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.01.20]
*****************************************************************************/
#include "dataloglockopt.h"
#include "DatalogRun.h"

using namespace std;

class dtllockopt_001_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtllockopt_001_test::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtllockopt_001_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf /root/_datalog_/");
}

class dtllockopt_001_test1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtllockopt_001_test1::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtllockopt_001_test1::TearDown()
{
    AW_CHECK_LOG_END();
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf /root/_datalog_/");
}

class dtllockopt_001_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,6\"");
#endif
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
#endif
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();;
};

void dtllockopt_001_test2::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtllockopt_001_test2::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf /root/_datalog_/");
}
/* ****************************************************************************
 Description  : 001.线程1进行DML操作，线程2加载另外1个so（拿不到datalog service锁），预期加载失败
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#ifndef ENV_RTOSV2X
    system("top -b -d1 -n60 -H 1 >> DataLog_064_001_001.txt &");
#endif
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test001";
    char soName1[FILE_PATH] = "test002";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(libName1, "%s/%s.so", outputFilePath, soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行18s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    // 加载升级so, 加载sodatalog service是1级hung死时间 euler:3s、V2x设备:11s
    sleep(2);
    pthread_create(&thr_arr[1], NULL, ThreadLoadDatalogSo, (void *)libName1);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.线程1进行DML操作，线程2加载另外1个so(拿到datalog service锁 )，预期加载成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test001";
    char soName1[FILE_PATH] = "test002";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(libName1, "%s/%s.so", outputFilePath, soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 加载升级so, 加载sodatalog service是1级hung死时间 euler:3s、V2x设备:11s
    sleep(13);
    pthread_create(&thr_arr[1], NULL, ThreadLoadDatalogSo1, (void *)libName1);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.线程1进行DML操作，线程2卸载另外1个so(拿不到datalog service锁 )，预期加载失败
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#ifndef ENV_RTOSV2X
    system("top -b -d1 -n60 -H 1 >> DataLog_064_001_003.txt &");
#endif
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test001";
    char soName1[FILE_PATH] = "test002";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(libName1, "%s/%s.so", outputFilePath, soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName1));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行18s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    // 加载升级so, 加载sodatalog service是1级hung死时间 euler:3s、V2x设备:11s; 仿真环境：15s
    sleep(2);
    pthread_create(&thr_arr[1], NULL, ThreadLoadUnloadSo, (void *)soName1);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.线程1进行DML操作，线程2卸载另外1个so(拿到datalog service锁 )，预期加载成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test001";
    char soName1[FILE_PATH] = "test002";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(libName1, "%s/%s.so", outputFilePath, soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName1));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 加载升级so, 加载sodatalog service是1级hung死时间 euler:3s、V2x设备:11s
    sleep(13);
    pthread_create(&thr_arr[1], NULL, ThreadLoadUnloadSo1, (void *)soName1);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.线程1对notify表进行DML操作，卡时间，线程2订阅notify表拿不到锁(事务锁)，预期报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test003";
    const char *subName01 = "subNotifyout1";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
     // 一个线程写datalog输入表，执行36s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    sleep(2);
    pthread_create(&thr_arr[1], NULL, ThreadSubsNotify, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.线程1对notify表进行DML操作，卡时间，线程2订阅notify表拿到锁(事务锁)，预期订阅成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test003";
    int ret = 0;
    const char *subName01 = "subNotifyout1";
    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 等待14.5s
    usleep(1000 * 14500);
    pthread_create(&thr_arr[1], NULL, ThreadSubsNotify1, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.线程1对pubsub资源表进行DML操作，卡时间，线程2订阅pubsub资源表拿不到锁(事务锁)，预期报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    const char *subName01 = "subNamers2";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行36s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    // 需要保证写线程先执行, sleep 1.5s
    usleep(1000 * 1500);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    pthread_create(&thr_arr[1], NULL, ThreadSubsPubsubRes, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.线程1对pubsub资源表进行DML操作，卡时间，线程2订阅pubsub资源表拿到锁(事务锁)，预期订阅成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    const char *subName01 = "subNamers2";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 等待14.5s
    usleep(1000 * 14500);
    pthread_create(&thr_arr[1], NULL, ThreadSubsPubsubRes1, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.线程1对外部表进行DML操作，卡时间，线程2订阅外部表拿不到锁(事务锁)，预期报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test005";
    const char *subName01 = "subNameextern";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行36s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    // 2024年2月19号变更  extern表加锁移到merge算子里，所以订阅外部表能拿到锁
    // 2024年2月22号变更 加锁移到前面，能卡住
    sleep(2);
    pthread_create(&thr_arr[1], NULL, ThreadSubsExternal, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.线程1对外部表进行DML操作，卡时间，线程2订阅外部表拿到锁(事务锁)，预期订阅成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test005";
    const char *subName01 = "subNameextern";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 等待14.5s
    usleep(1000 * 14500);
    pthread_create(&thr_arr[1], NULL, ThreadSubsExternal1, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.线程1对notify表进行DML操作，卡时间，线程2取消订阅notify表拿不到锁(事务锁)，预期报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test003";
    const char *subName01 = "subNotifyout1";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 创建订阅关系
     // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    ret = testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfo023.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataT *userData01;
    ret = testSnMallocUserData(&userData01, 6 * 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snYlogNotifyCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行36s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    sleep(2);
    pthread_create(&thr_arr[1], NULL, ThreadCancelSubs, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    testSnFreeUserData(userData01);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.线程1对notify表进行DML操作，卡时间，线程2取消订阅notify表拿到锁(事务锁)，预期取消订阅成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test003";
    const char *subName01 = "subNotifyout1";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 创建订阅关系
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    ret = testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfo023.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataT *userData01;
    ret = testSnMallocUserData(&userData01, 6 * 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snYlogNotifyCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 等待14.5s
    usleep(1000 * 14500);
    pthread_create(&thr_arr[1], NULL, ThreadCancelSubs1, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    testSnFreeUserData(userData01);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    // 断连
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.线程1对pubsub资源表进行dml操作，卡时间，线程2取消订阅pubsub资源表拿不到锁(事务锁)，，预期报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    const char *subName01 = "subNamers2";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 创建订阅关系
     // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    ret = testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfors2.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataT *userData01;
    ret = testSnMallocUserData(&userData01, 6 * 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snYlogpubSubResCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行36s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    sleep(2);
    pthread_create(&thr_arr[1], NULL, ThreadCancelSubs, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    testSnFreeUserData(userData01);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.线程1对pubsub资源表进行dml操作，卡时间，线程2取消订阅pubsub资源表拿到锁(事务锁)，预期取消订阅成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    const char *subName01 = "subNamers2";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 创建订阅关系
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    ret = testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfors2.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataT *userData01;
    ret = testSnMallocUserData(&userData01, 6 * 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snYlogpubSubResCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 等待14.5s
    usleep(1000 * 14500);
    pthread_create(&thr_arr[1], NULL, ThreadCancelSubs1, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    testSnFreeUserData(userData01);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    // 断连
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.线程1对外部表进行DML操作，卡时间，线程2取消订阅外部表拿不到锁(事务锁)，预期报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test005";
    const char *subName01 = "subNameextern";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 创建订阅关系
     // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    ret = testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfoextern.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataT *userData01;
    ret = testSnMallocUserData(&userData01, 6 * 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snYlogExternalCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行36s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    // 2024年2月19号变更  extern表加锁移到merge算子里，所以取消订阅外部表能拿到锁
    // 2024年2月22号变更 加锁移到前面，能卡住
    sleep(2);
    pthread_create(&thr_arr[1], NULL, ThreadCancelSubs, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    testSnFreeUserData(userData01);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.线程1对外部表进行DML操作，卡时间，线程2取消订阅外部表拿到锁(事务锁)，预期取消订阅成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test005";
    const char *subName01 = "subNameextern";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 创建订阅关系
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName";
    ret = testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfoextern.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataT *userData01;
    ret = testSnMallocUserData(&userData01, 6 * 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snYlogExternalCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 等待14.5s
    usleep(1000 * 14500);
    pthread_create(&thr_arr[1], NULL, ThreadCancelSubs1, (void *)subName01);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    testSnFreeUserData(userData01);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.1个dml操作，多次访问kv表（不同kv表func函数，过期回调函数，agg函数，状态转移函数），预期正常，无死锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test006";
    int ret = 0;

    char schemaJson[10240] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, "A");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(schemaJson, g_schemaJson1, "A1");
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestLabelInfoT labelInfo1 = {(char *)"A", 0, g_testNameSpace};
    TestLabelInfoT labelInfo2 = {(char *)"A1", 0, g_testNameSpace};
    
    // 创建5个kv表
    ret = CreateFiveKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int recordNum = 5;

    // 结构化批写A表和A1表
    C3Int8T obj1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 1}, {2, 0, 2, 2, 1}, {3, 0, 3, 3, 1}, {4, 0, 4, 4, 1}};
    C3Int8T obj2[recordNum] = {{1, 0, 1, 1, -1}, {1, 0, 1, 2, 2000}, {2, 0, 2, 2, 6000}, {3, 0, 3, 3, 1},
        {4, 0, 4, 4, -4}};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批写的数据的记录数
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 结构化写
    ret = testGmcPrepareStmtByLabelName(g_stmt, "A", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        ret = testStructSetVertexWithBuf(g_stmt, &obj1[i], &labelInfo1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, "A1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        ret = testStructSetVertexWithBuf(g_stmt, &obj2[i], &labelInfo2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);

    system("gmsysview record A");
    system("gmsysview record A1");

    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);
    // 删除kv表
    ret = DeleteFiveKVTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.1个dml操作，写多张外部表，预期正常
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test007";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);

    // 切换namespace为public

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    GmcDropVertexLabel(g_stmt, "extern1");
    GmcDropVertexLabel(g_stmt, "extern3");
    GmcDropVertexLabel(g_stmt, "extern4");
    GmcDropVertexLabel(g_stmt, "extern5");
    for (int i = 1; i < 6; i++) {
        char tempName[128] = {0};
        (void)snprintf(tempName, 128, "./schema_file/external%02d.gmjson", i);
        readJanssonFile(tempName, &schema);
        EXPECT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schema);
        schema = NULL;
    }
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 对输入表写数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    system("gmsysview count extern");
    system("gmsysview count extern1");
    // 删除外部表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "extern1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "extern3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "extern4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "extern5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.显示声明block为0，enableDatalogDmlWhenUpgrading配置项为1，
 升级后并发度变低；测试升级前，重做过程中，重做完成后，并发情况
**************************************************************************** */
TEST_F(dtllockopt_001_test1, DataLog_064_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 2, 2}, {1, 0, 1, 1, 1}, {2, 0, 2, 2, 2}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 4}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 2, 2}, {1, 0, 1, 1, 1}, {1, 0, 2, 2, 2}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 4}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 2;
    pthread_t thr_arr[threadNum];
    ThreadDataT td[threadNum];
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].labelName, 128, "inp%d", i + 2);
        td[i].objLen = 1;
        td[i].obj = (C3Int8T *)malloc(5 * sizeof(C3Int8T));
        memset(td[i].obj, 0, 5 * sizeof(C3Int8T));
        for (int j = 0; j < td[i].objLen; j++) {
            td[i].obj[j].a = 1;
            td[i].obj[j].b = 2;
            td[i].obj[j].c = 2;
            td[i].obj[j].dtlReservedCount = 1;
        }
    }
    // 升级前并发写inp2和inp3
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable, (void *)&td[0]);
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable, (void *)&td[1]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].labelName, 128, "inp%d", i + 2);
        td[i].objLen = 2;
        memset(td[i].obj, 0, 5 * sizeof(C3Int8T));
        for (int j = 0; j < td[i].objLen; j++) {
            td[i].obj[j].a = 1 + j;
            td[i].obj[j].b = 1 + j;
            td[i].obj[j].c = 1 + j;
            td[i].obj[j].dtlReservedCount = 1;
        }
    }

    // 加载第一次升级so, 重做需要12s，重做过程中，inp2和inp3不能并发
    // inp3表重做6s，inp2和inp1重做各3s
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // sleep 11.5s
    usleep(1000 * 11500);
    // 升级前并发写inp2和inp3， inp2拿到锁执行6s
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable, (void *)&td[0]);
    // sleep 1s
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：30s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable1, (void *)&td[1]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 完成重做之后  inp2和inp3不能并发
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].labelName, 128, "inp%d", i + 2);
        td[i].objLen = 2;
        memset(td[i].obj, 0, 5 * sizeof(C3Int8T));
        for (int j = 0; j < td[i].objLen; j++) {
            td[i].obj[j].a = 3 + j;
            td[i].obj[j].b = 3 + j;
            td[i].obj[j].c = 3 + j;
            td[i].obj[j].dtlReservedCount = 1;
        }
    }
    // 对inp2插入数据，执行6s，inp3拿不到事务锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable, (void *)&td[0]);
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：30s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable1, (void *)&td[1]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    
    for (int i = 0; i < threadNum; i++) {
        free(td[i].obj);
    }
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// block为1模式不支持不同TOPO表join，用例下架
/* ****************************************************************************
 Description  : 020.显示声明block为1，升级后并发度变低；重做过程中，并发情况
**************************************************************************** */
TEST_F(dtllockopt_001_test1, DISABLED_DataLog_064_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test009";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 2;
    pthread_t thr_arr[threadNum];
    ThreadDataT td[threadNum];
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].labelName, 128, "inp%d", i + 2);
        td[i].objLen = 1;
        td[i].obj = (C3Int8T *)malloc(td[i].objLen * sizeof(C3Int8T));
        memset(td[i].obj, 0, td[i].objLen * sizeof(C3Int8T));
        for (int j = 0; j < td[i].objLen; j++) {
            td[i].obj[j].a = 1;
            td[i].obj[j].b = 1;
            td[i].obj[j].c = 2;
            td[i].obj[j].dtlReservedCount = 1;
        }
    }
    // 升级前并发写inp2和inp3
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable, (void *)&td[0]);
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable, (void *)&td[1]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].labelName, 128, "inp%d", i + 2);
        td[i].objLen = 1;
        td[i].obj = (C3Int8T *)malloc(td[i].objLen * sizeof(C3Int8T));
        for (int j = 0; j < td[i].objLen; j++) {
            td[i].obj[j].a = 2;
            td[i].obj[j].b = 2;
            td[i].obj[j].c = 4;
            td[i].obj[j].dtlReservedCount = 1;
        }
    }

    // 加载第一次升级so, 重做需要6s，重做过程中，inp2和inp3不能并发
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    usleep(1000 * 5500);
    // 升级前并发写inp2和inp3
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable, (void *)&td[0]);
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable1, (void *)&td[1]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 完成重做之后  inp2和inp3不能并发
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].labelName, 128, "inp%d", i + 2);
        td[i].objLen = 1;
        memset(td[i].obj, 0, td[i].objLen * sizeof(C3Int8T));
        for (int j = 0; j < td[i].objLen; j++) {
            td[i].obj[j].a = 4;
            td[i].obj[j].b = 4;
            td[i].obj[j].c = 8;
            td[i].obj[j].dtlReservedCount = 1;
        }
    }
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable, (void *)&td[0]);
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable1, (void *)&td[1]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    
    for (int i = 0; i < threadNum; i++) {
        free(td[i].obj);
    }
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.线程1循环写删TransientFinish表，线程2循环直连读TransientFinish表，预期读不到数据
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test010";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 循环执行
    pthread_create(&thr_arr[0], NULL, ThreadCycleWriteTable, (void *)"inp1");
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadCycleReadTable, (void *)"inp1");

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.线程1循环更新输入表，线程2循环直连读输入表，预期表数据量不变
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test011";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 对inp1写1000条数据
    int recordNum = 1000;
    // 写表
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr_arr[2];
    // 循环执行
    pthread_create(&thr_arr[0], NULL, ThreadCycleUpdateTable, (void *)"inp1");
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadCycleReadTable, (void *)"inp1");

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.线程1循环写删TransientFinish表，线程2循环CS读TransientFinish表，预期读不到数据
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test010";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 循环执行
    pthread_create(&thr_arr[0], NULL, ThreadCycleWriteTable, (void *)"inp1");
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadCycleReadTableCS, (void *)"inp1");

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.线程1循环更新输入表，线程2循环CS读输入表，预期表数据量不变
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test011";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));

    // 对inp1写1000条数据
    int recordNum = 1000;
    // 写表
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr_arr[2];
    // 循环执行
    pthread_create(&thr_arr[0], NULL, ThreadCycleUpdateTable, (void *)"inp1");
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadCycleReadTableCS, (void *)"inp1");

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.先对inp2表写些数据，线程1对inp1表进行insert数据，线程2并发直连读inp2表，线程3并发CS读inp2表，
 线程4并发利用gmsysview count读inp1表（GmcConnOptionsSetCSRead）
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test012";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 6;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8},
        {4, 0, 5, 5, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8},
        {1, 0, 5, 5, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行18s；线程2并发直连读inp2表；线程3并发CS读inp2表；线程4并发利用gmsysview count 读inp1表
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadCycleReadTable1, (void *)"inp2");
    pthread_create(&thr_arr[2], NULL, ThreadCycleReadTableCS1, (void *)"inp2");
    // 拿不到事务锁，gmsysview count inp1默认走的cs读; 加latch锁，超时时间是一级hung，euler：3s；V2x设备：11s；仿真环境：15s
    pthread_create(&thr_arr[3], NULL, ThreadGmsysviewCountTable, (void *)"inp1");

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024012204759
// 只能上架euler
/* ****************************************************************************
 Description  : 026.先对inp2表写些数据，线程1对inp1表进行insert数据，线程2并发直连读inp1表，
 线程3并发CS读inp1表，线程4并发利用gmsysview record读inp1表（GmcConnOptionsSetCSRead）
**************************************************************************** */
TEST_F(dtllockopt_001_test2, DataLog_064_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test012";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；线程2并发直连读inp2表；线程3并发CS读inp2表；线程4并发利用gmsysview count 读inp1表
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadCycleReadTable1, (void *)"inp1");
    pthread_create(&thr_arr[2], NULL, ThreadCycleReadTableCS2, (void *)"inp1");
    // 拿不到事务锁，gmsysview record inp1默认走的cs读
    pthread_create(&thr_arr[3], NULL, ThreadGmsysviewRecordTable, (void *)"inp1");

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.A表和B表join，线程1写A表，线程2并发写B表拿不到锁，预期报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test012";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

     // 一个线程写datalog输入表，执行36s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp2");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.A表和B表join，线程1写A表，线程2并发写B表拿到锁，预期执行成功
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test012";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；线程2并发写inp2拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // sleep 14.5s
    usleep(1000 * 14500);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp2");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.4个输入表是一张图，通过join关联，4个线程分别写A，B，D，E表，当只有A表能拿到事务锁时，
 其它三个输入表报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test013";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 12;
    // 写表
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行36s；并发写inp2, inp3, inp4拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    sleep(2);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp2");
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp3");
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 030.4个输入表是一张图，通过join关联，4个线程分别写A，B，D，E表，四张表都能拿到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test013";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；并发写inp2, inp3, inp4拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    usleep(1000 * 14500);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp2");
    // 等待3s，保证后面的拿到锁
    sleep(3);
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp3");
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 只上架euler
/* ****************************************************************************
 Description  : 031.4个输入表是一张图，通过join关联，4个线程分别写A，B，D，E表，A表和B表能拿到锁，另外2张表拿不到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test013";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；并发写inp2拿到锁；inp3，inp4拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    usleep(1000 * 14500);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp2");
    // 后面2个线程拿不到锁
    sleep(1);
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp3");
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.4个输入表是一张图，通过access_delta关联，4个线程分别写A，B，D，E表，
 当只有A表能拿到事务锁时，其它三个输入表报1012002错误码
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test014";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；并发写inp2, inp3, inp4拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    sleep(2);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp2");
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp3");
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.4个输入表是一张图，通过access_delta关联，4个线程分别写A，B，D，E表，四张表都能拿到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test014";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；并发写inp2, inp3, inp4拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    usleep(1000 * 14500);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp2");
    // 等待3s，保证后面的拿到锁
    sleep(3);
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp3");
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 只上架euler
/* ****************************************************************************
 Description  : 034.4个输入表是一张图，通过access_delta关联，4个线程分别写A，B，D，E表，A表和B表能拿到锁，另外2张表拿不到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test014";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；并发写inp2拿到锁；inp3，inp4拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    usleep(1000 * 14500);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp2");
    // 后面2个线程拿不到锁
    sleep(1);
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp3");
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.4个输入表是一张图，通过access_current关联，4个线程分别写A，B，D，E表，
 A表和D表肯定能拿到锁，B和E表拿不到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test015";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；并发写inp2, inp4拿不到锁;并发写inp3拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    sleep(2);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp2");
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp3");
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.4个输入表是一张图，通过access_current关联，4个线程分别写A，B，D，E表，四张表都能拿到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test015";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；并发写inp2, inp3, inp4拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp3");
    usleep(1000 * 14500);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp2");
    // 等待3s，保证后面的拿到锁
    sleep(3);
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 只上架euler
/* ****************************************************************************
 Description  : 037.4个输入表是一张图，通过access_current关联，4个线程分别写A，B，D，E表，
 A表和D表肯定能拿到锁，B拿到锁；E拿不到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test015";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行15s；并发写inp2、inp3拿到锁；inp4拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp3");
    usleep(1000 * 14500);
    // 事务锁时间，euler：1s V2x设备：10s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable3, (void *)"inp2");
    // 后面1个线程拿不到锁
    sleep(1);
    pthread_create(&thr_arr[3], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp4");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.规则中含notify表，查询事务锁视图，能够查到notify表加锁情况
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test003";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 查事务锁加锁视图
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    system(g_command);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.原始.d中含pubsub资源型表，查询事务锁视图，能够查到pubsub资源型表的加锁情况
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test004";
    const char *subName01 = "subNamers2";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[1];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    sleep(2);
    // 查事务锁加锁视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    system(g_command);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thr_arr[0], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 040.原始.d中仅含外部表，查询事务锁视图，预期能查到加锁情况
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test005";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[1];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 事务锁时间，euler：1s V2x设备：10s
    sleep(2);
    // 查事务锁加锁视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    system(g_command);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    pthread_join(thr_arr[0], NULL);
    
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.join表和func函数共16个，线程1写1个表，线程2并发写其他的表拿不到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test016";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 12;
    // 写表
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    for (int i = 2; i < 12; i++) {
        char labelName[128] = {0};
        (void)snprintf(labelName, 128, "inp%d", i);
        ret = writeRecord(g_conn, g_stmt, labelName, objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    system("gmsysview count");
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp2 read complete!!!");
    // 线程数
    int threadNum = 4;
    pthread_t thr_arr[threadNum];

    // 线程1批写inp1表，执行36s；线程2并发写inp2和inp11拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    sleep(1);
    // 事务锁时间，euler：1s V2x设备：10s
    // 2024年1月份变更V2x环境事务锁时间变成30s
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp2");
    pthread_create(&thr_arr[2], NULL, ThreadSingleWriteDatalogTable2, (void *)"inp11");
    
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 042.写表的递归场景，原始B表和F表有5条数据，对A表写1条数据，触发func递归写A表5次，预期A表有6条数据
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test017";
    int ret = 0;

    char libName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    system("gmsysview count");

    // 对输入表inp1写入1条数据
    C3Int8T objIn2[1] = {{1, 0, 0, 0, 0}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn2, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "inp1", g_connServer);
    system(g_command);
    char message[128] = {0};
    (void)snprintf(message, 128, "%d", recordNum + 1);
    ret = executeCommand(g_command, "inp1", message);
    C3Int8T objIn3[recordNum + 1] = {0};
    for (int i = 0; i < recordNum + 1; i++) {
        objIn3[i].a = i;
        objIn3[i].b = i;
        objIn3[i].c = i;
        objIn3[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 043.写表的递归场景，原始B表和F表有9条数据，对A表写1条数据，触发func递归写A表9次，预期A表有10条数据
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test017";
    int ret = 0;

    char libName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 9;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    system("gmsysview count");

    // 对输入表inp1写入1条数据
    C3Int8T objIn2[1] = {{1, 0, 0, 0, 0}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn2, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    char message[128] = {0};
    (void)snprintf(message, 128, "%d", recordNum + 1);
    ret = executeCommand(g_command, "inp1", message);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn3[recordNum + 1] = {0};
    for (int i = 0; i < recordNum + 1; i++) {
        objIn3[i].a = i;
        objIn3[i].b = i;
        objIn3[i].c = i;
        objIn3[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 044.写表的递归场景，原始B表和F表有10条数据，对A表写1条数据，触发func递归写A表10次，预期A表无数据
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test017";
    int ret = 0;

    char libName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 11;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    system("gmsysview count");

    // 对输入表inp1写入1条数据，递归深度超限；会写入失败
    C3Int8T objIn2[1] = {{1, 0, 0, 0, 0}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn2, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn3[0] = {};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 045.写表的递归场景，原始B表和F表有2条数据，笛卡尔积，对A表写1条数据，触发func递归死循环，预期A表无数据
**************************************************************************** */
TEST_F(dtllockopt_001_test, DataLog_064_001_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test018";
    int ret = 0;

    char libName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    system("gmsysview count");

    // 对输入表inp1写入1条数据
    C3Int8T objIn2[1] = {{1, 0, 0, 0, 0}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn2, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn3[0] = {};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 046.重做过程中，显示开启事务写外部表，sleep160s，不提交事务，使得重做线程拿不到锁
**************************************************************************** */
TEST_F(dtllockopt_001_test1, DataLog_064_001_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test005";
    int ret = 0;

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);

    char libName[FILE_PATH] = {0};
    char libName1[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    // euler:重试62次：63s   v2x设备：重试4次：30s*5=150s
    // 重试次数：长事务监控时间（60s）/ 事务锁时间 + 2
    // 事务合并
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    usleep(1000 * 5500);

    pthread_t thr_arr[2];

    // 一个线程写datalog输入表，euler执行70s，V2x环境160s，重做失败回滚成功
    pthread_create(&thr_arr[0], NULL, ThreadwriteExternTable, (void *)"extern");

    pthread_join(thr_arr[0], NULL);

    // 等待回滚成功
    sleep(10);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s ", g_toolPath, "extern", g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "extern", "6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview record extern");

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024052409157
/* ****************************************************************************
 Description  : 047.客户端超时时间设置6s，实际服务端超时时间为1s（6s-5s），对输入表批写2条数据，执行2s
 写入失败
**************************************************************************** */
TEST_F(dtllockopt_001_test1, DataLog_064_001_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test019";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 建立连接
    YangConnOptionT connOptions = {0};
    connOptions.msgReadTimeout = 6000;  // 6s超时
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    int recordNum = 2;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}};
    ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 048.客户端超时时间设置5s，实际服务端超时时间为5s，对输入表批写4条数据，执行4s
 写入成功
**************************************************************************** */
TEST_F(dtllockopt_001_test1, DataLog_064_001_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test019";
    int ret = 0;
 
    char libName[FILE_PATH] = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 建立连接
    YangConnOptionT connOptions = {0};
    connOptions.msgReadTimeout = 5000;  // 5s超时
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 插入数据
    int recordNum = 4;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 1, 3, 4}, {1, 0, 1, 4, 5}};
    ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
 
/* ****************************************************************************
 Description  : 049.客户端超时时间设置5s，实际服务端超时时间为5s，对输入表批写6条数据，执行6s
 写入失败
**************************************************************************** */
TEST_F(dtllockopt_001_test1, DataLog_064_001_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test019";
    int ret = 0;
 
    char libName[FILE_PATH] = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 建立连接
    YangConnOptionT connOptions = {0};
    connOptions.msgReadTimeout = 5000;  // 5s超时
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 插入数据
    int recordNum = 6;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
 
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
 
/* ****************************************************************************
 Description  : 050.客户端超时时间为默认60s，实际服务端超时时间为55s，对输入表批写60条数据，执行60s
 写入失败
**************************************************************************** */
TEST_F(dtllockopt_001_test1, DataLog_064_001_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test019";
    int ret = 0;
 
    char libName[FILE_PATH] = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 建立连接
    YangConnOptionT connOptions = {0};
    connOptions.msgReadTimeout = 60000;  // 6s超时
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 插入数据
    int recordNum = 60;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
 
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
