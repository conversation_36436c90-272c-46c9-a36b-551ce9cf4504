/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.0.0 迭代二Datalog热补丁阻塞合并数据重做-执行测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.02.08]
*****************************************************************************/
#include "dtlblomerhotredo.h"
#include "DatalogRun.h"

using namespace std;

class dtlblomer_002_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlblomer_002_test::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtlblomer_002_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

class dtlblomer_002_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // euler环境修改内存配置项
#if defined RUN_INDEPENDENT
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxTotalDynSize=48\""); // 总动态内存48M
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxSysDynSize=16\""); // 系统动态内存16M
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=8\"");
#endif
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/modifyCfg.sh recover"); // 还原配置项
#endif
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlblomer_002_test2::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtlblomer_002_test2::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 001.显示声明block为1，%alter含外部表规则，跟原先规则一样，加载升降级so，数据不会触发推送
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 切换namespace为public, 加载外部表


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema); 

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[6] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    GmcConnT *conn_sn_sync2 = NULL;
    GmcStmtT *stmt_sn_sync2 = NULL;
    const char *subConnName = "subConnName026";
    const char *subConnName2 = "subConnName027";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subextern";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    testSubConnect(&conn_sn_sync2, &stmt_sn_sync2, 1, g_epoll_reg_info, subConnName2, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    char *sub_info02 = NULL;
    readJanssonFile("./schema_file/subInfopub.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 2;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);
    // 订阅外部表
    readJanssonFile("./schema_file/subInfoextern.json", &sub_info02);
    EXPECT_NE((void *)NULL, sub_info01);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson =sub_info02;
    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync2, snCallbackExternal, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 插入数据
    int recordNum = 2;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, NULL, false, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, NULL, false, true, g_schemaJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    system("gmsysview record extern");
    ret = readRecord(g_conn, g_stmt, "out2", objIn1, recordNum, C3Int8Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "extern read complete!!!");
    // datalog对外部表写数据，是merge操作
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: fake_extern
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: fake_extern
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade extern read complete!!!");
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback extern read complete!!!");
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync2, stmt_sn_sync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    testSnFreeUserData(userData);
    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.显示声明block为1，%alter含notify表规则，跟原先规则一样，加载升降级so，数据不会触发推送
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 切换namespace为public, 加载外部表


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema); 

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[6] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifyrs2";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfopub.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 2;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 插入数据
    int recordNum = 2;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn1, recordNum, C3Int8Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "extern read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r3
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp2
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: pub
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: pub
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade extern read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback extern read complete!!!");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.显示声明block为1，%alter含TBM表规则，跟原先规则一样，加载升降级so，数据不会触发推送
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 切换namespace为public, 加载外部表


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema); 

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[6] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifyrs2";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfopub.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 2;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 插入数据
    int recordNum = 2;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out2", objIn1, recordNum, C3Int8Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "extern read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade extern read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = readRecord(g_conn, g_stmt, "extern", objIn1, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback extern read complete!!!");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_069_002_003.txt");
    
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对这个视图
/* ****************************************************************************
 Description  : 004.显示声明block为1，datalogUpgradeFetchSize设置为1；对输入表写10条数据，加载升降级so，
 触发重做，查询数据符合预期（10个事务 * 2）
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // datalogUpgradeFetchSize设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
   
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 10;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out1", objIn1, recordNum, C3Int8Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    system("gmsysview -q V\\$STORAGE_TRX_STAT");
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    C3Int8T objIn2[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i;
        objIn2[i].b = i;
        objIn2[i].c = 10;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = upVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);

    C3Int8T objIn3[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].a = i;
        objIn3[i].b = i;
        objIn3[i].c = i;
        objIn3[i].dtlReservedCount = 1;
        objIn3[i].upgradeVersion = upVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    system("gmsysview -q V\\$STORAGE_TRX_STAT");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.显示声明block为1，datalogUpgradeFetchSize设置为3；对输入表写10条数据，
 加载升降级so，触发重做，查询数据符合预期（4个事务 * 2）
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // datalogUpgradeFetchSize设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 3);
    system(g_command);
   
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 10;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out1", objIn1, recordNum, C3Int8Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    system("gmsysview -q V\\$STORAGE_TRX_STAT");
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    C3Int8T objIn2[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i;
        objIn2[i].b = i;
        objIn2[i].c = 10;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = upVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);

    C3Int8T objIn3[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].a = i;
        objIn3[i].b = i;
        objIn3[i].c = i;
        objIn3[i].dtlReservedCount = 1;
        objIn3[i].upgradeVersion = upVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    system("gmsysview -q V\\$STORAGE_TRX_STAT");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.显示声明block为1，datalogUpgradeFetchSize设置为2147483647；
 对输入表写10条数据，加载升降级so，触发重做，查询数据符合预期（1个事务 * 2）
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // datalogUpgradeFetchSize设置为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);
   
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 10;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "out1", objIn1, recordNum, C3Int8Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    system("gmsysview -q V\\$STORAGE_TRX_STAT");
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    C3Int8T objIn2[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i;
        objIn2[i].b = i;
        objIn2[i].c = 10;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = upVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);

    C3Int8T objIn3[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn3[i].a = i;
        objIn3[i].b = i;
        objIn3[i].c = i;
        objIn3[i].dtlReservedCount = 1;
        objIn3[i].upgradeVersion = upVerVal1;
    }
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    system("gmsysview -q V\\$STORAGE_TRX_STAT");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.显示声明block为1，覆盖所有表类型，agg左表非group-by字段进行交换
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_007)
{   
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;
    int32_t upVerVal5 = -1;
    int32_t upVerVal6 = -1;
    int32_t upVerVal7 = -1;
    int32_t upVerVal8 = -1;
    int32_t upVerVal9 = -1;
    int32_t upVerVal10 = -1;
    int32_t upVerVal11 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[25] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8},
        {-1, 0, 1, 1, 2}, {-1, 0, 1, 2, 3}, {-1, 0, 2, 2, 4}, {-1, 0, 3, 3, 6}, {-1, 0, 4, 4, 8}, {1, 0, 1, 1, 2},
        {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}, {1, 0, 5, 4, 9}, {-1, 0, 1, 1, 2}, {1, 0, 1, 1, 2}, {1, 0, 1, 2, 3},
        {-1, 0, 2, 2, 4}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {-1, 0, 4, 4, 8}, {1, 0, 4, 4, 8}, {1, 0, 5, 5, 10},
        {-1, 0, 5, 4, 9}, {1, 0, 5, 4, 9}};
    // pubsub资源型表推送的数据
    C3Int8C1Int4T objPub2[5] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 2, 2, 1}, {1, 0, 2, 2, 4, 1}, {1, 0, 3, 3, 3, 1},
        {1, 0, 4, 4, 8, 1}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifyrs2";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfo023.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 25;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系, pubsub资源表
    char *sub_info02 = NULL;
    readJanssonFile("./schema_file/subInfors2.json", &sub_info02);
    EXPECT_NE((void *)NULL, sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C3Int8C1Int4RescGet;
    userData02->funcType = 1;
    userData02->objLen = 5;
    userData02->obj = objPub2;
    userData02->isResourcePubSub = true;
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallback, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 插入数据
    int recordNum = 5;
    int recordAgg = 10;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objAgg[recordAgg] = {{1, 0, 1, 1, 2}, {1, 0, 1, 1, 3}, {2, 0, 1, 1, 10}, {2, 0, 2, 2, 1}, {2, 0, 2, 2, 10}, 
        {2, 0, 2, 2, -1}, {3, 0, 3, 3, 1}, {1, 0, 3, 3, 100}, {1, 0, 3, 3, 10}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 1] = {{1, 0, 1, 1, 15}, {1, 0, 2, 2, 10}, {1, 0, 3, 3, 111}, {1, 0, 4, 4, 8}};
    C3Int8C1Int4T objIn4[10] = {{1, 0, 1, 1, 2, 0}, {1, 0, 1, 2, 2, 1}, {1, 0, 2, 2, 4, 2}, {1, 0, 3, 3, 3, 3},
        {1, 0, 4, 4, 8, 4}, {1, 0, 2, 2, 2, 5}, {1, 0, 10, 10, 10, 6},{1, 0, 1, 1, 1, 7}, {1, 0, 100, 100, 100, 8},
        {1, 0, 8, 8, 8, 9}};
    C3Int8T objTimeout[recordNum] = {{1, 0, 1, 1, -1}, {1, 0, 1, 2, -3}, {2, 0, 2, 6, 4}, {3, 0, 3, 3, 3},
        {4, 0, 4, 4, 8}};
    C3Int8T objTimeout2[recordNum] = {{2, 0, 2, 2, 9}, {2, 0, 2, 4, 7}, {2, 0, 2, 6, 4}, {3, 0, 3, 3, 3},
        {4, 0, 4, 4, 8}};
    // 校验输入表
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对many_to_one的agg输入表写数据
    ret = writeRecord(g_conn, g_stmt, "inp4", objAgg, recordAgg, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对many_to_many的agg输入表写数据
    ret = writeRecord(g_conn, g_stmt, "inp6", objAgg, recordAgg, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp7", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp8", objTimeout, recordNum, C3Int8TimeoutSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待过期表过期
    sleep(3);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp8", objTimeout2, recordNum, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp8 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn3, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out4 read complete!!!");
    // 校验out5输出表
    ret = readRecord(g_conn, g_stmt, "out5", objIn4, 10, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out5 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out4", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal4);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out5", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal4);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp6", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal6);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal7);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "extern", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 报错
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AW_MACRO_EXPECT_EQ_INT(-1, upVerVal8);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp8", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal9);

    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 2, 2}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 2, 4}, {1, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 2}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal4, 1, 1, 15}, {1, upVerVal4, 2, 2, 10}, {1, upVerVal4, 3, 3, 111},
        {1, upVerVal4, 4, 4, 8}};
    C3Int8T objIn9[10] = {{1, upVerVal5, 1, 1, 2}, {1, upVerVal5, 2, 2, 4}, {1, upVerVal5, 4, 4, 8},
        {1, upVerVal5, 1, 2, 2}, {1, upVerVal5, 3, 3, 3}, {1, upVerVal5, 2, 2, 2}, {1, upVerVal5, 10, 10, 10},
        {1, upVerVal5, 1, 1, 1}, {1, upVerVal5, 100, 100, 100}, {1, upVerVal5, 8, 8, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn8, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out4 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out5", objIn9, 10, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out5 read complete!!!");

    // 插入数据
    // 升级后对输入表插入数据
    ret = writeRecord(g_conn, g_stmt, "tb1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn10[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    C3Int8T objIn11[recordNum + 2] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 2}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}, {1, upVerVal3, 5, 4, 9}, {1, upVerVal3, 5, 5, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验out3输出表的数据
    ret = readRecord(g_conn, g_stmt, "out3", objIn11, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 two read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal10);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal11);

    C3Int8T objIn12[recordNum + 2] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3},
        {1, 0, 4, 4, 8}, {1, 0, 5, 5, 5}, {1, 0, 5, 4, 9}};
    
    ret = readRecord(g_conn, g_stmt, "out2", objIn12, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn12, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out3 read complete!!!");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);

    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    // 查看tbm表记录
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_tbmlogName);
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_msglogName);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008显示声明block为1，%alter含TBM表规则，触发存在部分数据合并和不合并场景，查询数据符合预期
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule006";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 4}, {2, 0, 5, 5, 4}, {3, 0, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    // %alter rule r2 out2(a, b, 4) :- inp1(a, b, -). 推送的是更新操作op为2
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_069_002_008.txt");

#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.覆盖access_delta中间表，输入表；access_current输入表，中间表，输出表的场景
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule007";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 4}, {2, 0, 5, 5, 4}, {3, 0, 6, 6, 12}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}, {1, 0, 5, 5, 10}, {1, 0, 6, 6, 12}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 4}, {1, 0, 5, 5, 4}, {1, 0, 6, 6, 12}};
    C3Int8T objIn4[recordNum + 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 4}, {1, 0, 4, 4, 8},
        {1, 0, 5, 5, 4}, {1, 0, 5, 5, 10}, {1, 0, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 校验out1 out2 out22 out3 out2 out42表中的数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out22", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out22 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn4, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out4 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out42", objIn4, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out42 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "after upgrade");
    system("gmsysview count");
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 6, 6, 12}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 10, 2}, {1, upVerVal, 2, 10, 2}, {1, upVerVal, 4, 10, 4},
        {1, upVerVal, 5, 10, 5}, {1, upVerVal, 6, 10, 6}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal, 1, 10, 2}, {1, upVerVal, 2, 10, 2}, {1, upVerVal, 4, 10, 4},
        {1, upVerVal, 5, 10, 5}, {1, upVerVal, 6, 10, 6}};
    C3Int8T objIn8[recordNum + 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 6, 6, 12},
        {1, upVerVal, 4, 4, 4}, {1, upVerVal, 4, 4, 8}, {1, upVerVal, 5, 5, 4}, {1, upVerVal, 5, 5, 10}};

    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out22", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out22 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn4, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out4 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out42", objIn8, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out42 read complete!!!");
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out22", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out22 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn4, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out4 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out42", objIn4, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out42 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.原始.d两张图含namespace和precedence，第一次升级显示声明block为0，
 对一个图升级，第二次升级显示声明block为0，对另外一张图利用已有的输入表进行join成一张图，预期能产生join数据，upgradeVersion为2
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;
    int32_t upVerVal5 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 10}, {1, upVerVal1, 1, 2, 10}, {1, upVerVal1, 2, 2, 10},
        {1, upVerVal1, 3, 3, 10}, {1, upVerVal1, 4, 4, 10}};
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns1.out2 read complete!!!");
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal3);
    C3Int8T objIn5[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 2, 4},
        {1, upVerVal2, 3, 3, 3}, {1, upVerVal2, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal3, 1, 1, 10}, {1, upVerVal3, 1, 2, 10}, {1, upVerVal3, 2, 2, 10},
        {1, upVerVal3, 3, 3, 10}, {1, upVerVal3, 4, 4, 10}};
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after two upgrade ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after two upgrade ns1.out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    system("gmsysview record ns1.out1");
    system("gmsysview record ns1.out2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    // 连续降级 ns1.out1表的upgradeVersion字段降不到0
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal4);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal5);

    C3Int8T objIn7[recordNum] = {{1, upVerVal4, 1, 1, 1}, {1, upVerVal4, 1, 2, 3}, {1, upVerVal4, 2, 2, 4},
        {1, upVerVal4, 3, 3, 3}, {1, upVerVal4, 4, 4, 8}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal5, 1, 1, 1}, {1, upVerVal5, 1, 2, 3}, {1, upVerVal5, 2, 2, 4},
        {1, upVerVal5, 3, 3, 3}, {1, upVerVal5, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback ns1.out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.原始.d两张图含namespace和precedence，第一次升级显示声明block为0，
 对一个图升级，第二次升级显示声明block为1，对另外一张图进行升级
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule009";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 10}, {1, upVerVal1, 1, 2, 10}, {1, upVerVal1, 2, 2, 10},
        {1, upVerVal1, 3, 3, 10}, {1, upVerVal1, 4, 4, 10}};
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns1.out2 read complete!!!");
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    system("gmsysview record ns1.inp1");
    system("gmsysview record ns1.inp2");
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal3);
    C3Int8T objIn5[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 2, 4},
        {1, upVerVal2, 3, 3, 3}, {1, upVerVal2, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal3, 1, 1, 10}, {1, upVerVal3, 1, 2, 10}, {1, upVerVal3, 2, 2, 10},
        {1, upVerVal3, 3, 3, 10}, {1, upVerVal3, 4, 4, 10}};
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after two upgrade ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after two upgrade ns1.out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    system("gmsysview record ns1.out1");
    system("gmsysview record ns1.out2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal4);

    system("gmsysview record ns1.out1");
    system("gmsysview record ns1.out2");

    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback ns1.out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.分三次升级，第一次block为1修改一个图，第二次block为0修改将2个图合成1个图，
 第三次block为1修改1个规则
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule010";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 10}, {1, upVerVal1, 1, 2, 10}, {1, upVerVal1, 2, 2, 10},
        {1, upVerVal1, 3, 3, 10}, {1, upVerVal1, 4, 4, 10}};
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after two upgrade ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after two upgrade ns1.out2 read complete!!!");
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    AW_FUN_Log(LOG_DEBUG, "after two upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(01, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal3);
    C3Int8T objIn5[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 2, 4},
        {1, upVerVal2, 3, 3, 3}, {1, upVerVal2, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal3, 1, 1, 20}, {1, upVerVal3, 1, 2, 20}, {1, upVerVal3, 2, 2, 20},
        {1, upVerVal3, 3, 3, 20}, {1, upVerVal3, 4, 4, 20}};
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after three upgrade ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after three upgrade ns1.out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    system("gmsysview record ns1.out1");
    system("gmsysview record ns1.out2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal4);

    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback ns1.out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.原始.d两张图，第一次升级显示声明block为0，对一个图升级，第二次升级新增一张空表并对空表写数据，
 第三次升级显示声明block为1，对上面1张图升级，第四次升级显示声明block为0，将两个图合成1张图；同时降级成功；upgradeVersion都是最新的
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule011";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;
    int32_t upVerVal5 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char patchSoName3[FILE_PATH] = {0};
    char patchSoName4[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    char rollbackSoName3[FILE_PATH] = {0};
    char rollbackSoName4[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(patchSoName3, "%s/%s_patchV4.so", outputFilePath, soName);
    (void)sprintf(patchSoName4, "%s/%s_patchV5.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName3, "%s/%s_rollbackV4.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName4, "%s/%s_rollbackV5.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 0",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal1);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}};
    system("gmsysview count");
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after two upgrade ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after two upgrade ns1.out2 read complete!!!");
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName4));
    AW_FUN_Log(LOG_DEBUG, "after two upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 2", "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1",
        "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, upVerVal3);
    C3Int8T objIn5[recordNum] = {{1, upVerVal2, 1, 1, 10}, {1, upVerVal2, 1, 2, 10}, {1, upVerVal2, 2, 2, 10},
        {1, upVerVal2, 3, 3, 10}, {1, upVerVal2, 4, 4, 10}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after three upgrade ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after three upgrade ns1.out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName4));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName3));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    system("gmsysview record ns1.out1");
    system("gmsysview record ns1.out2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal4);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal5);

    C3Int8T objIn7[recordNum] = {{1, upVerVal4, 1, 1, 1}, {1, upVerVal4, 1, 2, 3}, {1, upVerVal4, 2, 2, 4},
        {1, upVerVal4, 3, 3, 3}, {1, upVerVal4, 4, 4, 8}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal5, 1, 1, 1}, {1, upVerVal5, 1, 2, 3}, {1, upVerVal5, 2, 2, 4},
        {1, upVerVal5, 3, 3, 3}, {1, upVerVal5, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "ns1.out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback ns1.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns1.out2", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback ns1.out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.显示声明block为1，datalogUpgradeFetchSize设置为1，重做时，表资源超限；查看数据符合预期；资源无泄漏
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab001";
    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planListDynMem01[128] = {0};
    char planListDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    while (cycleCnt < totalCycleCnt) {
        // 建连前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem01 is %s", planListDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 创建连接
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        // 插入数据
        // 更新时，1个事务（删除再插入），需要占用1个maxsize大小的空间
        int recordNum = 5;
        C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {2, 0, 2, 2, 2}, {3, 0, 3, 3, 3},
            {4, 0, 4, 4, 8}};
        C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 10}, {1, 0, 2, 2, 10}};
        ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验输入表
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        // 加载升级so
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
        AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
        sleep(2);

        // 校验热补丁视图重做状态
        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
        system(g_command);
        ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验重做后的数据
        // 变更4月20号，重做失败，数据会回滚
        system("gmsysview count");
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载
        ret = TestUninstallDatalog(soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(10);
        // 断连后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        AW_FUN_Log(LOG_DEBUG, "after unload so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem02 is %s", planListDynMem02);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planListDynMem01, planListDynMem01) != 0) {
                failCnt++;
            }
        cycleCnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.显示声明block为1，datalogUpgradeFetchSize设置为2147483647；重做时，表资源超限；查看数据符合预期；资源无泄漏
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab001";
    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planListDynMem01[128] = {0};
    char planListDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    while (cycleCnt < totalCycleCnt) {
        // 建连前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem01 is %s", planListDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 创建连接
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        // 插入数据
        int recordNum = 5;
        C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {2, 0, 2, 2, 2}, {3, 0, 3, 3, 3},
            {4, 0, 4, 4, 8}};
        C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 10}, {1, 0, 2, 2, 10}};
        ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验输出表
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        // 加载升级so
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
        AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
        sleep(2);

        // 校验热补丁视图重做状态
        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
        system(g_command);
        ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        system("gmsysview count");
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
  
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载
        ret = TestUninstallDatalog(soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(10);
        // 断连后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        AW_FUN_Log(LOG_DEBUG, "after unload so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem02 is %s", planListDynMem02);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planListDynMem01, planListDynMem01) != 0) {
                failCnt++;
            }
        cycleCnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.显示声明block为1，datalogUpgradeFetchSize设置为1，修改udf实现，第三个事务返回失败，热补丁重做失败；
 资源无泄漏
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab002";
    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planListDynMem01[128] = {0};
    char planListDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    while (cycleCnt < totalCycleCnt) {
        // 建连前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem01 is %s", planListDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 创建连接
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        // 插入数据
        int recordNum = 5;
        C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 20}, {2, 0, 2, 2, 3}, {3, 0, 3, 3, 30},
            {4, 0, 4, 4, 8}};
        C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 20}, {1, 0, 2, 2, 3}, {1, 0, 3, 3, 30},
            {1, 0, 4, 4, 8}};
        ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验输入表
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        // 加载升级so
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
        AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
        sleep(2);

        // 校验热补丁视图重做状态
        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
        system(g_command);
        ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验重做后的数据
       // 变更4月20号，重做失败，数据会回滚
        system("gmsysview count");
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载
        ret = TestUninstallDatalog(soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(10);
        // 断连后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        AW_FUN_Log(LOG_DEBUG, "after unload so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem02 is %s", planListDynMem02);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planListDynMem01, planListDynMem01) != 0) {
                failCnt++;
            }
        cycleCnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.显示声明block为1，datalogUpgradeFetchSize设置为2147483647，修改udf实现，第三个事务返回失败，热补丁重做失败；
 资源无泄漏
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab002";
    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planListDynMem01[128] = {0};
    char planListDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    while (cycleCnt < totalCycleCnt) {
        // 建连前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem01 is %s", planListDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 创建连接
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        // 插入数据
        int recordNum = 5;
        C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 20}, {2, 0, 2, 2, 3}, {3, 0, 3, 3, 30},
            {4, 0, 4, 4, 8}};
        C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 20}, {1, 0, 2, 2, 3}, {1, 0, 3, 3, 30},
            {1, 0, 4, 4, 8}};
        ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验输入表
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        // 加载升级so
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
        AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
        sleep(2);

        // 校验热补丁视图重做状态
        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
        system(g_command);
        ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验重做后的数据
        C3Int8T objIn3[recordNum] = {{1, 0, 1, 10, 1}, {1, 0, 1, 20, 2}, {1, 0, 2, 2, 3}, {1, 0, 3, 3, 30},
            {1, 0, 4, 4, 8}};
        system("gmsysview count");
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载
        ret = TestUninstallDatalog(soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(10);
        // 断连后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        AW_FUN_Log(LOG_DEBUG, "after unload so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem02 is %s", planListDynMem02);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planListDynMem01, planListDynMem01) != 0) {
                failCnt++;
            }
        cycleCnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.显示声明block为1，datalogUpgradeFetchSize设置为1，修改udf实现，第5个事务返回失败，热补丁重做失败；资源无泄漏
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab003";
    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planListDynMem01[128] = {0};
    char planListDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    while (cycleCnt < totalCycleCnt) {
        // 建连前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem01 is %s", planListDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 创建连接
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        // 插入数据
        int recordNum = 5;
        C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 20}, {2, 0, 2, 2, 3}, {3, 0, 3, 3, 30},
            {4, 0, 4, 4, 8}};
        C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 20}, {1, 0, 2, 2, 3}, {1, 0, 3, 3, 30},
            {1, 0, 4, 4, 8}};
        ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验输入表
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        // 加载升级so
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
        AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
        sleep(2);

        // 校验热补丁视图重做状态
        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
        system(g_command);
        ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验重做后的数据
        // 变更4月20号，重做失败，数据会回滚
        system("gmsysview count");
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载
        ret = TestUninstallDatalog(soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(10);
        // 断连后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        AW_FUN_Log(LOG_DEBUG, "after unload so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem02 is %s", planListDynMem02);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planListDynMem01, planListDynMem01) != 0) {
                failCnt++;
            }
        cycleCnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 修改配置项内存配置项
/* ****************************************************************************
 Description  : 019.对原始.d中的表写大量数据，加载升级so被防呆防住，资源无泄漏
**************************************************************************** */
TEST_F(dtlblomer_002_test2, DataLog_069_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "reliab003";
    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planListDynMem01[128] = {0};
    char planListDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    while (cycleCnt < totalCycleCnt) {
        // 建连前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem01 is %s", planListDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 创建连接
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        // 插入数据
        int recordNum = 40000;
        C3Int8T objIn1[recordNum] = {0};
        for (int i = 0; i < 40000; i++) {
            objIn1[i].a = i;
            objIn1[i].b = i;
            objIn1[i].c = i;
            objIn1[i].dtlReservedCount = 1;
        }
        
        ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        system("gmsysview count");

        // 加载升级so
        AW_MACRO_ASSERT_NE_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
        AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
        sleep(2);

        // 校验热补丁视图重做状态
        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
        system(g_command);
        ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载
        ret = TestUninstallDatalog(soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(10);
        // 断连后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planListDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan list memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        AW_FUN_Log(LOG_DEBUG, "after unload so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planListDynMem02 is %s", planListDynMem02);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planListDynMem01, planListDynMem01) != 0) {
                failCnt++;
            }
        cycleCnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024030604624
/* ****************************************************************************
 Description  : 020.对原始.d中的规则含多个相关function进行升级，block为0模式
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule012";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    // 对升级so输入表写数据
    C3Int8T objIn4[2] = {{1, 0, 5, 5, 25}, {2, 0, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, 2, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn5[recordNum + 2] = {{1, upVerVal, 5, 5, 25}, {1, upVerVal, 6, 6, 36}, {1, upVerVal, 1, 1, 1},
        {1, upVerVal, 1, 2, 2}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 3, 3, 9}, {1, upVerVal, 4, 4, 16}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal, 5, 5, 25}, {1, upVerVal, 1, 1, 1}, {1, upVerVal, 2, 2, 4}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn7[recordNum + 2] = {{1, upVerVal, 5, 5, 10}, {1, upVerVal, 6, 6, 12}, {1, upVerVal, 1, 1, 2},
        {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 3, 3, 6}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn8[recordNum - 1] = {{1, upVerVal, 6, 6, 12}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn8, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out2 read complete!!!");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.对原始.d中的规则含多个相关function进行升级，block为1模式
**************************************************************************** */
TEST_F(dtlblomer_002_test, DataLog_069_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule013";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    // 对升级so输入表写数据
    C3Int8T objIn4[2] = {{1, 0, 5, 5, 25}, {2, 0, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, 2, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn5[recordNum + 2] = {{1, upVerVal, 5, 5, 25}, {1, upVerVal, 6, 6, 36}, {1, upVerVal, 1, 1, 1},
        {1, upVerVal, 1, 2, 2}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 3, 3, 9}, {1, upVerVal, 4, 4, 16}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal, 5, 5, 25}, {1, upVerVal, 1, 1, 1}, {1, upVerVal, 2, 2, 4}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn7[recordNum + 2] = {{1, upVerVal, 5, 5, 10}, {1, upVerVal, 6, 6, 12}, {1, upVerVal, 1, 1, 2},
        {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 3, 3, 6}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn8[recordNum - 1] = {{1, upVerVal, 6, 6, 12}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn8, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out2 read complete!!!");
    AW_FUN_Log(LOG_STEP, "test end.");
}
