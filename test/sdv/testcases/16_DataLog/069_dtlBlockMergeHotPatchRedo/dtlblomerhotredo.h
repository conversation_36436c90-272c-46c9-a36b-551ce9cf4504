/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: dataloglockopt.h
 * Description:
 * Author: luyang 00618033
 * Create: 2024-2-6
 */

#ifndef DTLBLOMERHOTREDO_H
#define DTLBLOMERHOTREDO_H

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <pthread.h>
#include <vector>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "share_function.h"
#include "DatalogRun.h"

#define PRINT_INFO 1
#define FILE_PATH 512
#define LABEL_NAME 512

const char *g_tbmlogName = "TbmRunLog.txt";

const char *g_msglogName = "msgNotifyRunLog.txt";

const char *g_funclogName = "funcLog.txt";

char g_hFile[FILE_PATH] = "../../../../../pub/include/";

char const *g_viewName = "V\\$PTL_DATALOG_PATCH_INFO";

char const *g_viewName2 = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";

char g_cfgName[MAX_CMD_SIZE] = {0};

// 存放错误信息
char g_errorMsg[MAX_CMD_SIZE] = {0};

// 存放日志白名单错误码
char g_errorCode01[MAX_CMD_SIZE] = {0};
char g_errorCode02[MAX_CMD_SIZE] = {0};
char g_errorCode03[MAX_CMD_SIZE] = {0};

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

char g_tableName[128] = "cap";
char g_configJson[128] = "{\"max_record_count\":100000, \"isFastReadUncommitted\": false}";

int g_threadWait = 0;
pthread_mutex_t g_threadLock;

const char *g_schemaJson1 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int64"},
            {"name" : "b", "type" : "int64"},
            {"name" : "c", "type" : "int64"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

/*--------------------------------------编译、加载------------------------------------*/
void CompileTest(char *inputFilePath, char *outputFilePath, char *soName, bool haveUdf)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char datalogFile[FILE_PATH] = {0};
    char outputFile[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    char udfFile[FILE_PATH] = {0};
    // 赋值
    (void)sprintf(datalogFile, "%s/%s.d", inputFilePath, soName);
    (void)sprintf(outputFile, "%s/%s.c", outputFilePath, soName);
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    if (haveUdf) {
        (void)sprintf(udfFile, "%s/%s_udf.c", inputFilePath, soName);
    }
    // .d->.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, datalogFile, outputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    if (haveUdf) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile,
            outputFile, udfFile, libName);
    } else {
        (void)snprintf(
            command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile, outputFile, libName);
    }
    system(command);
}

/*--------------------------------编译生成patch.c+full.d、加载升级patchso---------------------------------*/
void CompileUpgradeAndRollBackTest(char *inputFilePath, char *outputFilePath, char *soName, bool havePatchUdf,
    int upgradeCnt)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    char rollLibName[FILE_PATH] = {0};
    char udfPatchFile[FILE_PATH] = {0};

    if (upgradeCnt == 1) {
        (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
        (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
        (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, upgradeCnt + 1);
        (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);
        (void)snprintf(rollbackPatchCName, FILE_PATH, "%s/%s_patch_rollback.c", outputFilePath, soName);
        if (havePatchUdf) {
            (void)snprintf(udfPatchFile, FILE_PATH, "%s/%s_patch_udf.c", outputFilePath, soName);
        }
    } else {
        (void)snprintf(ruleFile, FILE_PATH, "%s/%s_ruleV%d.d", inputFilePath, soName, upgradeCnt);
        (void)snprintf(patchFile, FILE_PATH, "%s/%s_ruleV%d_patch.d", inputFilePath, soName, upgradeCnt);
        (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, upgradeCnt + 1);
        (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_ruleV%d_patch.c", outputFilePath, soName, upgradeCnt);
        (void)snprintf(rollbackPatchCName, FILE_PATH, "%s/%s_ruleV%d_patch_rollback.c", outputFilePath, soName,
            upgradeCnt);
        if (havePatchUdf) {
            (void)snprintf(udfPatchFile, FILE_PATH, "%s/%s_ruleV%d_patch_udf.c", outputFilePath, soName, upgradeCnt);
        }
    }
    (void)snprintf(libName, FILE_PATH, "%s/%s_patchV%d.so", outputFilePath, soName, upgradeCnt + 1);
    (void)snprintf(rollLibName, FILE_PATH, "%s/%s_rollbackV%d.so", outputFilePath, soName, upgradeCnt + 1);
    // rule.d + patch.d -> patch.c + ruleV2.d + patch_rollback.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    if (havePatchUdf) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile,
            patchCOutputFile, udfPatchFile, libName);
    } else {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile,
            patchCOutputFile, libName);
    }
    system(command);
    // 生成回滚so
    (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile,
        rollbackPatchCName, rollLibName);
    system(command);
}

// 加载升级的so
int TestLoadUpgradeDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(
        loadCommand, commandSize, "%s/gmimport -s %s -c datalog -upgrade %s", g_toolPath, g_connServer, soName);

    return system(loadCommand);
}

// 加载回滚的so
int TestLoadRollbackDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(
        loadCommand, commandSize, "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer, soName);

    return system(loadCommand);
}

/*----------------------------------------表结构--------------------------------------------*/
#pragma pack(1)
// (a, b, c)
typedef struct TagC3Int8 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} C3Int8T;

typedef struct TagC3Int8C1Int4 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int32_t d;
} C3Int8C1Int4T;

typedef struct TagC4Int8 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} C4Int8T;

typedef struct TagC1Int1C2Int4 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t ifIndex;
    int8_t attr;
    int32_t value;
} C1Int1C2Int4T;

typedef struct TagThreadData {
    char labelName[128];
    C3Int8T *obj;
    int objLen;
} ThreadDataT;

#pragma pack(0)

// struct模式设置
int C3Int8Set(GmcStmtT *stmt, void *t)
{
    C3Int8T *obj = (C3Int8T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int C1Int1C2Int4Set(GmcStmtT *stmt, void *t)
{
    C1Int1C2Int4T *obj = (C1Int1C2Int4T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] ifIndex: %d, ret = %d.", obj->ifIndex, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "attr", GMC_DATATYPE_INT8, &obj->attr, sizeof(obj->attr));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] attr: %d, ret = %d.", obj->attr, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "value", GMC_DATATYPE_INT32, &obj->value, sizeof(obj->value));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] value: %d, ret = %d.", obj->value, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }
    return ret;
}

int C3Int8TimeoutSet(GmcStmtT *stmt, void *t)
{
    C3Int8T *obj = (C3Int8T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    // 改成按分钟为颗粒度
    int64_t cValue = int64_t(obj->c * 1000 * 60);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &cValue, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// 输出表数据校验
int C3Int8Cmp(const C3Int8T *st1, const C3Int8T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] c, st1: %ld, st2: %ld.", st1->c, st2->c)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                    st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int C3Int8C1Int4Cmp(const C3Int8C1Int4T *st1, const C3Int8C1Int4T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] c, st1: %ld, st2: %ld.", st1->c, st2->c)) : ({});
            break;
        }
        if (st1->d != st2->d) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] d, st1: %ld, st2: %ld.", st1->d, st2->d)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] upgradeVersion, st1: %d, st2: %d.",
                    st1->upgradeVersion, st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

// 不校验过期字段值
int C3Int8TimeoutCmp(const C3Int8T *st1, const C3Int8T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] upgradeVersion, st1: %d, st2: %d.",
                    st1->upgradeVersion, st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

// 读表的数据
// 读（a, b, c）
int C3Int8Get(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d", getObj.a, getObj.b, getObj.c);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8Cmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}
// 读(a, b, c, d)
int C3Int8C1Int4Get(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8C1Int4T *checkObj = (C3Int8C1Int4T *)t;
    C3Int8C1Int4T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'd' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Gett] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d %d", getObj.a, getObj.b, getObj.c, getObj.d,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d", getObj.a, getObj.b, getObj.c, getObj.d);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8C1Int4Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8C1Int4Cmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// 读过期表的表
int C3Int8TimeoutGet(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld %ld %ld", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld", getObj.a, getObj.b, getObj.c);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8TimeoutCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8TimeoutCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// 读(a, b, c), notify表
int C3Int8RescGet(GmcStmtT *stmt, void *t, int len, bool isResourcePubSub)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'upgradeVersion' fail, ret = %d.", ret);
        return ret;
    }

    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO][C3Int8RescGet]OUT_LABEL_RESULT: %ld %ld %ld %d %d ", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    ret = -1;
    // 校验
    for (int i = 0; i < len; i++) {
        if (C3Int8Cmp(&getObj, &checkObj[i], NULL, NULL) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8Cmp(&getObj, &checkObj[0], NULL, NULL);
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

int C3Int8C1Int4RescGet(GmcStmtT *stmt, void *t, int len, bool isResourcePubSub)
{
    C3Int8C1Int4T *checkObj = (C3Int8C1Int4T *)t;
    C3Int8C1Int4T getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    // pubsub 资源表
    if (isResourcePubSub) {
        getObj.d = 1;
    } else {
        ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'e' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'upgradeVersion' fail, ret = %d.", ret);
        return ret;
    }

    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO][C3Int8C1Int4RescGet]OUT_LABEL_RESULT: %ld %ld %ld %d %d %d ", getObj.a, getObj.b,
            getObj.c, getObj.d, getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    ret = -1;
    // 校验
    for (int i = 0; i < len; i++) {
        if (C3Int8C1Int4Cmp(&getObj, &checkObj[i], NULL, NULL) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8C1Int4Cmp(&getObj, &checkObj[0], NULL, NULL);
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] count: %d.", getObj.dtlReservedCount);
    }
    // 设置字段发回服务端
    if (isResourcePubSub) {
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &getObj.a, sizeof(getObj.a));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] a: %d, ret = %d.", getObj.a, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &getObj.b, sizeof(getObj.b));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] b: %d, ret = %d.", getObj.b, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &getObj.c, sizeof(getObj.c));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] c: %d, ret = %d.", getObj.c, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &getObj.d, sizeof(getObj.d));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] d: %d, ret = %d.", getObj.d, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(
                LOG_ERROR, "[C3Int8C1Int4RescGet] dtlReservedCount: %d, ret = %d.", getObj.dtlReservedCount, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &getObj.upgradeVersion, sizeof(getObj.dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] upgradeVersion: %d, ret = %d.", getObj.dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

// 获取文件中数据，存到buffer中
int TestViewData(char *cmd, char **result)
{
    int ret = 0;
    FILE *fd = popen(cmd, "r");
    int size = 10240;
    *result = (char *)malloc(sizeof(char) * size);
    memset(*result, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat((char *)*result, buf);
    }

    ret = pclose(fd);
    return ret;
}
// 校验热补丁重做视图信息
int CheckHotPatchView(char *soName = NULL, const char *expectOutput = NULL)
{
    int ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s -f SO_NAME=%s > test.txt", g_toolPath, g_viewName,
        g_connServer, soName);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *actualResult = NULL;
    (void)snprintf(command, MAX_CMD_SIZE, "cat test.txt");
    ret = TestViewData(command, &actualResult);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 预期值属于实际值的一部分
    const char *result = strstr(actualResult, expectOutput);
    if (result != NULL) {
        EXPECT_STRNE(NULL, result);
        free(actualResult);
    } else {
        free(actualResult);
        AW_FUN_Log(LOG_STEP, "There is no data what you need!");
        (void)SystemSnprintf("cat test.txt");
        return -1;
    }
    return ret;
}

// 订阅相关
void snCallbackExternal(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    bool isNull;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT:
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                case GMC_SUB_EVENT_DELETE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                printf("------inset-----\n");
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                printf("------delete-----\n");
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                printf("------merge-----\n");
                break;
            }
            default: {
                AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                break;
            }
        }
    }
}

// 获取动态内存视图和共享视图的字段值
int TestGetCTXStr(char *value, int len, char *view, char *filter)
{
    char command[1024] = {0};
    int ret = snprintf(command, sizeof(command), "%s/gmsysview -q V\\$%s -f CTX_NAME=\"%s\"| grep "
        "\"TOTAL_ALLOC_SIZE:\" | awk -F \":\" '{print $2}'", g_toolPath, view, filter);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, NULL, value, len);
    if (ret) {
        return FAILED;
    }
    return 0;
}

// 非轻量化创建kv表并插入数据
int CreateKvTable()
{
    int ret = 0;
    // 创建kv表
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvDropTable(stmt, g_tableName);
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "para1";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    GmcKvTupleT kvInfo1 = {0};
    char key1[32] = "para2";
    char value1[32] = "aaaaaa";
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1) + 1;
    kvInfo1.value = value1;
    kvInfo1.valueLen = strlen(value1) + 1;
    ret = GmcKvSet(stmt, kvInfo1.key, kvInfo1.keyLen, kvInfo1.value, kvInfo1.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo2 = {0};
    char key2[32] = "para3";
    char value2[64] = {0};
    memset(value2, 'c', 63);
    kvInfo2.key = key2;
    kvInfo2.keyLen = strlen(key2) + 1;
    kvInfo2.value = value2;
    kvInfo2.valueLen = strlen(value2) + 1;
    ret = GmcKvSet(stmt, kvInfo2.key, kvInfo2.keyLen, kvInfo2.value, kvInfo2.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo3 = {0};
    char key3[32] = "para4";
    int64_t value3 = 1000;
    kvInfo3.key = key3;
    kvInfo3.keyLen = strlen(key3) + 1;
    kvInfo3.value = &value3;
    kvInfo3.valueLen = sizeof(int64_t);
    ret = GmcKvSet(stmt, kvInfo3.key, kvInfo3.keyLen, kvInfo3.value, kvInfo3.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview show cap");
    return ret;
}
/*---------------------------------------------并发相关-------------------------------------------------------*/
void *ThreadBatchWriteDatalogTable(void *args)
{
    AW_FUN_Log(LOG_STEP, "insert datalog table test start.");
    const char *labelName = (char *)args;
    int ret = 0;
    int recordNum = 5;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写表
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(conn, stmt, labelName, objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "insert datalog table test end.");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, labelName, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, labelName, "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 拿不到锁
void *ThreadwriteExternTable(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换namespace
    // 写外部表，走fastpath流程
    int64_t insertValue = 6;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadwriteExternTable1(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换namespace
    // 开启默认类型事务
    ret = GmcTransStart(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写外部表，走fastpath流程
    int64_t insertValue = 6;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_mutex_lock(&g_threadLock);
    g_threadWait++;
    pthread_mutex_unlock(&g_threadLock);
    // euler sleep3s;V2x设备sleep65s
#ifdef RUN_INDEPENDENT
    sleep(3);
#else
    sleep(65);
#endif
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadwriteExternTable2(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换namespace
    // 开启默认类型事务
    ret = GmcTransStart(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写外部表，走fastpath流程
    int64_t insertValue = 6;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &insertValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_mutex_lock(&g_threadLock);
    g_threadWait++;
    pthread_mutex_unlock(&g_threadLock);
    // euler sleep70s;V2x设备sleep155s
#ifdef RUN_INDEPENDENT
    sleep(70);
#else
    sleep(155);
#endif
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadDeleteExternTableRecord(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换namespace
    int64_t deleteValue = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &deleteValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &deleteValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count extern");
    system("gmsysview record extern");

    sleep(2);
   
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *ThreadDeleteExternTable(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换namespace
    ret = GmcDropVertexLabel(stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 并发加载升级so
void *ThreadLoadUpgradeSo(void *args)
{
    AW_FUN_Log(LOG_STEP, "load upgrade so test start.");
    const char *patchSoName = (char *)args;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_STEP, "load upgrade so test end.");
}
// 并发写inp2
void *ThreadSingleWriteDatalogTable(void *args)
{
    int ret = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn1[1] = {{1, 0, 5, 5, 10}};

    ret = writeRecord(conn, stmt, "inp2", objIn1, 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *ThreadSingleWriteDatalogTable1(void *args)
{
    int ret = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn1[1] = {{1, 0, 5, 5, 10}};

    ret = writeRecord(conn, stmt, "inp2", objIn1, 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 并发DQL接口
// 直连读
void *ThreadCycleReadTable(void *args)
{
    AW_FUN_Log(LOG_STEP, "read datalog table test start.");
    const char *labelName = (char *)args;
    int ret = 0;
    int recordNum = 5;
    int tryCnt = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    while (true && tryCnt < 1000) {
        // 读表
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            cnt++;
        }
        usleep(1000 * 100);
        tryCnt++;
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 并发读200次
void *ThreadCycleReadTable1(void *args)
{
    AW_FUN_Log(LOG_STEP, "read datalog table test start.");
    const char *labelName = (char *)args;
    int ret = 0;
    int recordNum = 5;
    int tryCnt = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    while (true && tryCnt < 200) {
        // 读表
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            cnt++;
        }
        usleep(1000 * 100);
        tryCnt++;
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// CS读
void *ThreadCycleReadTableCS(void *args)
{
    AW_FUN_Log(LOG_STEP, "read datalog table test start.");
    const char *labelName = (char *)args;
    int ret = 0;
    int recordNum = 5;
    int tryCnt = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt, 0, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    while (true && tryCnt < 1000) {
        // 读表
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            cnt++;
        }
        usleep(1000 * 100);
        tryCnt++;
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadCycleReadTableCS1(void *args)
{
    AW_FUN_Log(LOG_STEP, "read datalog table test start.");
    const char *labelName = (char *)args;
    int ret = 0;
    int recordNum = 5;
    int tryCnt = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt, 0, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    while (true && tryCnt < 200) {
        // 读表
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            cnt++;
        }
        usleep(1000 * 100);
        tryCnt++;
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// CS读拿不到锁
void *ThreadCycleReadTableCS2(void *args)
{
    AW_FUN_Log(LOG_STEP, "read datalog table test start.");
    const char *labelName = (char *)args;
    int ret = 0;
    int recordNum = 5;
    int tryCnt = 0;
    int cycleCnt = 5;
#if defined ENV_RTOSV2X
    cycleCnt = 1;
#endif
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt, 0, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    while (true && tryCnt < cycleCnt) {
        // 读表
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
        usleep(1000 * 100);
        tryCnt++;
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 并发gmsysview count读默认走的CS读；gmsysview record默认走的是直连读
void *ThreadGmsysviewCountTable(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, labelName, g_connServer,
        g_testNameSpace);
    ret = system(command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
}

void *ThreadGmsysviewCountTable1(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, labelName, g_connServer,
        g_testNameSpace);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 并发gmsysview record读默认走直连读死等；
void *ThreadGmsysviewRecordTable(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, labelName, g_connServer,
        g_testNameSpace);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestGetPatchStateStr(char *value, int len, char *soName)
{
    char command[1024] = {0};
    int ret = snprintf(command, sizeof(command), "%s/gmsysview -q %s -s %s -f SO_NAME=%s| grep PATCH_STATE",
        g_toolPath, g_viewName, g_connServer, soName);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, NULL, value, len);
    if (ret) {
        return FAILED;
    }
    return 0;
}

// 并发查询热补丁视图，最多执行600s
void *ThreadScanPatchView(void *args)
{
    char *soName = (char *)args;
    char patchState[128] = {0};
    int tryCnt = 0;
    // 循环并发查询视图
    while (true && tryCnt < 600) {
        //  查看热补丁视图的状态
        int ret = TestGetPatchStateStr(patchState, 128, soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 重做成功或者重做失败跳出循环
        // 变更4月20号，重做失败会回滚：REDO_FAIL_ROLL_BACK_SUC和REDO_FAIL_ROLL_BACK_FAIL
        if (strstr(patchState, "SUCCESS") != NULL || strstr(patchState, "REDO_FAIL_ROLL_BACK_SUC") != NULL ||
            strstr(patchState, "REDO_FAIL_ROLL_BACK_FAIL")) {
            break;
        }
        sleep(1);
        tryCnt++;
    }
}

void *ThreadWriteKvTable(void *args)
{
    int ret = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等锁失败，写kv表中的数据失败
    GmcKvTupleT kvInfo = {0};
    char key[32] = "para5";
    int64_t value = 1000;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int64_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 显示开启事务写kv表
void *ThreadWriteKvTable1(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换namespace
    // 开启默认类型事务
    ret = GmcTransStart(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等锁失败，写kv表中的数据失败
    GmcKvTupleT kvInfo = {0};
    char key[32] = "para5";
    int64_t value = 1000;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int64_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_mutex_lock(&g_threadLock);
    g_threadWait++;
    pthread_mutex_unlock(&g_threadLock);
    // euler sleep70s;V2x设备sleep155s
#ifdef RUN_INDEPENDENT
    sleep(70);
#else
    sleep(155);
#endif
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 显示开启事务写kv表
void *ThreadWriteKvTable2(void *args)
{
    int ret = 0;
    const char *labelName = (char *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换namespace
    // 开启默认类型事务
    ret = GmcTransStart(conn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等锁失败，写kv表中的数据失败
    GmcKvTupleT kvInfo = {0};
    char key[32] = "para5";
    int64_t value = 1000;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int64_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_mutex_lock(&g_threadLock);
    g_threadWait++;
    pthread_mutex_unlock(&g_threadLock);
    // euler sleep60s;V2x设备sleep100s
#ifdef RUN_INDEPENDENT
    sleep(60);
#else
    sleep(100);
#endif
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadTruncateKvTable(void *args)
{
    int ret = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等锁成功
    ret = GmcKvTruncateTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*----------------------------------------校验表tbm、function中数据-------------------------------------------*/
int VerifyForSyntaxLog2(char *actualLogfile, char *expectLogfile)
{
    int ret = 0;
    FILE *fp1 = fopen(actualLogfile, "r");
    if (!fp1) {
        return -1;
    }
    FILE *fp2 = fopen(expectLogfile, "r");
    if (!fp2) {
        return -1;
    }
    char actualVal[128][512] = {0};
    char expectVal[128][512] = {0};
    int index1 = 0;
    int index2 = 0;
    while (!feof(fp1)) {
        (void)fgets(actualVal[index1], sizeof(actualVal[index1]), fp1);
        index1++;
    }
    while (!feof(fp2)) {
        (void)fgets(expectVal[index2], sizeof(expectVal[index2]), fp2);
        index2++;
    }
    ret = pclose(fp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pclose(fp2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(index1, index2);
    AW_FUN_Log(LOG_STEP, "index1 is %d, index2 is %d", index1, index2);
    for (uint32_t i = 0; i < index2; i++) {
        uint32_t j = 0;
        for (; j < index1; j++) {
            if (strcmp(actualVal[j], expectVal[i]) == 0) {
                break;
            }
        }
        if (j == index1) {
            AW_MACRO_EXPECT_NE_INT(j, index1);
            ret = -1;
            AW_FUN_Log(LOG_STEP, "check fail val is %s", expectVal[i]);
        }
    }
    return ret;
}

#endif
