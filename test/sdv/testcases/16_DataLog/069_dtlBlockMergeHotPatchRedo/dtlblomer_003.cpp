/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.0.0 迭代二Datalog热补丁阻塞合并数据重做-可靠性测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.02.18]
*****************************************************************************/
#include "dtlblomerhotredo.h"
#include "DatalogRun.h"

using namespace std;

class dtlblomer_003_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlblomer_003_test::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void dtlblomer_003_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

// 2024年2月20日变更，不同图重做以及并发写无锁
/* ****************************************************************************
 Description  : 001.显示声明block为1，定义2个图，线程1对第一个图中的数据写数据卡时间超过5*1级hung死时间，线程2并发写另外1个图，预期执行dml成功
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 10;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    // 线程1加载升级so，线程2并发写另外1个图
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable, NULL);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }
   
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.显示声明block为1，定义2个图，线程1对第一个图中的数据写数据卡时间小于5*1级hung死时间，线程2并发写另外1个图，预期执行dml成功
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 10;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    // 线程1加载升级so，线程2并发写另外1个图
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so
    sleep(50);
    pthread_create(&thr_arr[1], NULL, ThreadSingleWriteDatalogTable1, NULL);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }
   
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.显示声明block为1，线程1加载升级so卡时间33s，线程2gmsysview count读触发表 ，线程3gmsysview record读触发表；
 线程4直连读触发表；线程5CS读触发表  （都拿到锁）
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 10;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 线程1加载升级so，线程2gmsysview count读触发表；线程3gmsysview record读触发表；线程4直连读触发表；线程5CS读触发表
    int threadNum = 6;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadGmsysviewCountTable, (void *)"inp1");
    sleep(1);
    pthread_create(&thr_arr[2], NULL, ThreadGmsysviewRecordTable, (void *)"inp1");
    pthread_create(&thr_arr[3], NULL, ThreadCycleReadTable1, (void *)"inp1");
    pthread_create(&thr_arr[4], NULL, ThreadCycleReadTableCS2, (void *)"inp1");
    pthread_create(&thr_arr[5], NULL, ThreadScanPatchView, (void *)soName);

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.显示声明block为1，线程1加载升级so卡时间20s，线程2gmsysview count读触发表 ，
 线程3gmsysview record读触发表；线程4直连读触发表；线程5CS读触发表  
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 线程1加载升级so，线程2gmsysview count读触发表；线程3gmsysview record读触发表；线程4直连读触发表；线程5CS读触发表
    int threadNum = 6;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 等待29.5s
    usleep(1000 * 29500);
    pthread_create(&thr_arr[1], NULL, ThreadGmsysviewCountTable1, (void *)"inp1");
    pthread_create(&thr_arr[2], NULL, ThreadGmsysviewRecordTable, (void *)"inp1");
    pthread_create(&thr_arr[3], NULL, ThreadCycleReadTable1, (void *)"inp1");
    pthread_create(&thr_arr[4], NULL, ThreadCycleReadTableCS1, (void *)"inp1");
    pthread_create(&thr_arr[5], NULL, ThreadScanPatchView, (void *)soName);

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.显示声明block为1，线程1加载升级so卡时间33s，线程2gmsysview count读非触发表 ，
 线程3gmsysview record读触发表；线程4直连读触发表；线程5CS读非触发表
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 10;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count inp2");

    // 线程1加载升级so，线程2gmsysview count读非触发表；线程3gmsysview record读触发表；线程4直连读触发表；线程5CS读非触发表
    // gmsysview count inp2会对所有表加latch锁
    int threadNum = 6;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 并发写inp1写数据
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[2], NULL, ThreadGmsysviewRecordTable, (void *)"inp1");
    pthread_create(&thr_arr[3], NULL, ThreadCycleReadTable1, (void *)"inp1");
    pthread_create(&thr_arr[4], NULL, ThreadCycleReadTableCS1, (void *)"inp2");
    pthread_create(&thr_arr[5], NULL, ThreadScanPatchView, (void *)soName);
    sleep(57);
    // 拿到latch锁， 一级hung死时间
    pthread_create(&thr_arr[1], NULL, ThreadGmsysviewCountTable1, (void *)"inp2");

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.显示声明block为1，线程1加载升级so卡事务锁时间，线程2并发利用fastpath接口写外部表能拿不到锁
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 6;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 线程1加载升级so，线程2利用fastpath接口写外部表拿不到事务    v2x：30s  euler：1s
    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadwriteExternTable, (void *)"extern");
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }
 
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "extern", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "extern", "6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.显示声明block为1，线程1加载升级so卡事务锁时间，线程2并发利用fastpath接口删外部表数据拿到锁（事务锁时间），
 预期执行dml成功
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为1，能删除一条数据
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 线程1加载升级so，线程2利用fastpath接口写外部表拿不到事务    v2x：30s  euler：1s
    int threadNum = 2;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so, sleep29.5s
    usleep(1000 * 29500);
    pthread_create(&thr_arr[1], NULL, ThreadDeleteExternTableRecord, (void *)"extern");
 
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "extern", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "extern", "4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 当前外部表卡不住，变更能卡住时间
/* ****************************************************************************
 Description  : 008.显示声明block为1，线程1加载升级so卡事务锁时间，线程2并发利用fastpath接口
 删除外部表拿不到锁（DDL 事务锁时间），重做成功
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 6;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 线程1加载升级so，线程2利用fastpath接口写外部表拿不到事务    v2x：30s  euler：1s
    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadDeleteExternTable, (void *)"extern");
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态， 外部表被删除，重做失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.显示声明block为1，线程1显示开启事务，写外部表卡时间，大于2倍的事务锁时间小于3倍的事务锁时间；
 线程2并发加载升级so，预期数据重做成功，查看热补丁视图
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    g_threadWait = 0;

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 热补丁重做重试次数：长事务监控时间/事务锁时间 + 2
    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadwriteExternTable1, (void *)"extern");
    // 等待fastpath那边拿到外部表的锁
    while (true) {
        if (g_threadWait > 0) {
            break;
        }
        usleep(1000);
    }
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "extern", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "extern", "6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.显示声明block为1，线程1显示开启事务，写外部表卡时间，大于重做重试的次数的时间；
 线程2并发加载升级so，预期数据重做失败，查看热补丁视图
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    g_threadWait = 0;

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadwriteExternTable2, (void *)"extern");
    // 等待fastpath那边拿到外部表的锁
    while (true) {
        if (g_threadWait > 0) {
            break;
        }
        usleep(1000);
    }
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "extern", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "extern", "6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.显示声明block为1，线程1加载升级so卡事务锁时间，线程2并发利用fastpath接口写kv拿不到锁（事务锁时间），预期报12002错误码
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 10;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadWriteKvTable, NULL);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 删除kv表
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.显示声明block为1，线程1加载升级so卡事务锁时间，线程2并发利用fastpath接口删kv表数据拿到锁（事务锁时间），预期执行dml成功
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // sleep 29.5s
    usleep(1000 * 29500);
    pthread_create(&thr_arr[1], NULL, ThreadTruncateKvTable, NULL);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 删除kv表
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.显示声明block为1，线程1显示开启事务，写kv卡时间，大于重做重试的次数的时间；线程2并发加载升级so，预期数据重做失败，查看热补丁视图
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    g_threadWait = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadWriteKvTable1, NULL);
    // 等待fastpath那边拿到kv表的锁
    while (true) {
        if (g_threadWait > 0) {
            break;
        }
        usleep(1000);
    }
    // 重做线程拿不到锁
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 删除kv表
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.显示声明block为1，线程1显示开启事务，写kv卡时间，小于重做重试的次数的时间；线程2并发加载升级so，预期数据重做成功，查看热补丁视图
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    g_threadWait = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadWriteKvTable2, NULL);
    // 等待fastpath那边拿到kv表的锁
    while (true) {
        if (g_threadWait > 0) {
            break;
        }
        usleep(1000);
    }
    // 重做线程拿到锁   重试次数：长事务监控/事务锁时间 + 2
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 删除kv表
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1加载升级so卡事务锁时间，
 线程2并发利用fastpath接口写外部表拿不到锁（事务锁时间），预期执行dml失败
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 11;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 线程1加载升级so，线程2利用fastpath接口写外部表拿不到事务    v2x：30s  euler：1s
    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadwriteExternTable, (void *)"extern");
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "extern", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "extern", "11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1加载升级so卡事务锁时间，
 线程2并发利用fastpath接口删外部表数据拿到锁（事务锁时间），预期执行dml成功
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 修改datalogUpgradeFetchSize为2147483647，当成1个事务；重做之后，外部表数据保持不变
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 线程1加载升级so，线程2利用fastpath接口删外表， 一条数据拿到锁    v2x：30s  euler：1s
    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so, sleep29.5s
    usleep(1000 * 29500);
    pthread_create(&thr_arr[1], NULL, ThreadDeleteExternTableRecord, (void *)"extern");
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "extern", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "extern", "4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 当前外部表卡不住，变更能卡住, 当前block为0：undo和redo还未合并
/* ****************************************************************************
 Description  : 017.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1加载升级so卡事务锁时间，
 线程2并发利用fastpath接口删除外部表拿不到锁（事务锁时间），预期重做成功
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 11;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 线程1加载升级so，线程2利用fastpath接口写外部表拿不到事务    v2x：30s  euler：1s
    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadDeleteExternTable, (void *)"extern");
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态， 外部表被删除，重做失败
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1显示开启事务，写外部表卡时间，
 大于2倍的事务锁时间小于3倍的事务锁时间；线程2并发加载升级so，预期数据重做成功，查看热补丁视图
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    g_threadWait = 0;

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 热补丁重做重试次数：长事务监控时间/事务锁时间 + 2
    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadwriteExternTable1, (void *)"extern");
    // 等待fastpath那边拿到外部表的锁
    while (true) {
        if (g_threadWait > 0) {
            break;
        }
        usleep(1000);
    }
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "extern", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "extern", "6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 变更：重试次数是 （60/事务锁超时时间） + 2
// 如果是在外部表加锁超时了，重试总时间相当于 重试次数*（锁耗时）
/* ****************************************************************************
 Description  : 019.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1显示开启事务，写外部表卡时间，
 大于重做重试的次数的时间；线程2并发加载升级so，预期数据重做失败，查看热补丁视图
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    g_threadWait = 0;

    // 切换namespace为public
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadwriteExternTable2, (void *)"extern");
    // 等待fastpath那边拿到外部表的锁
    while (true) {
        if (g_threadWait > 0) {
            break;
        }
        usleep(1000);
    }
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "extern", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "extern", "6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1加载升级so卡事务锁时间，
 线程2并发利用fastpath接口写kv拿不到锁（事务锁时间），预期报12002错误码
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 11;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadWriteKvTable, NULL);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 删除kv表
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1加载升级so卡事务锁时间，
 线程2并发利用fastpath接口删kv表数据拿到锁（事务锁时间），预期执行dml成功
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // sleep 29.5s
    usleep(1000 * 29500);
    pthread_create(&thr_arr[1], NULL, ThreadTruncateKvTable, NULL);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 删除kv表
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1显示开启事务，写kv卡时间，
 大于重做重试的次数的时间；线程2并发加载升级so，预期数据重做失败，查看热补丁视图
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    g_threadWait = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadWriteKvTable1, NULL);
    // 等待fastpath那边拿到kv表的锁
    while (true) {
        if (g_threadWait > 0) {
            break;
        }
        usleep(1000);
    }
    // 重做线程拿不到锁
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 删除kv表
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.显示声明block为0，enableDatalogDmlWhenUpgrading设置为0，线程1显示开启事务，写kv卡时间，
 小于重做重试的次数的时间；线程2并发加载升级so，预期数据重做成功，查看热补丁视图
**************************************************************************** */
TEST_F(dtlblomer_003_test, DataLog_069_003_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    g_threadWait = 0;

    // 创建kv表
    ret = CreateKvTable();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int threadNum = 3;
    pthread_t thr_arr[threadNum];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadWriteKvTable2, NULL);
    // 等待fastpath那边拿到kv表的锁
    while (true) {
        if (g_threadWait > 0) {
            break;
        }
        usleep(1000);
    }
    // 重做线程拿到锁   重试次数：长事务监控/事务锁时间 + 2
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    pthread_create(&thr_arr[2], NULL, ThreadScanPatchView, (void *)soName);
   
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 删除kv表
    ret = GmcKvDropTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_mutex_destroy(&g_threadLock);

    AW_FUN_Log(LOG_STEP, "test end.");
}
