/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: ruleTest.cpp
 * Description: datalog state
 * Author: youwanyong ywx1157510
 * Create: 2023-03-05
 */

#include "datalogState.h"
#include "t_datacom_lite.h"
using namespace std;

class datalogState : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
    }
};

void datalogState::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void datalogState::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
    int32_t e;
    int32_t f;
    int32_t g;
    int32_t h;
    int32_t i;
    int32_t j;
    int32_t k;
    int32_t l;
    int32_t m;
    int32_t n;
    int32_t o;
    int32_t p;
} B;

char *g_schemaJson = (char *)R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "int32"},
            {"name" : "d", "type" : "int32"},
            {"name" : "e", "type" : "int32"},
            {"name" : "f", "type" : "int32"},
            {"name" : "g", "type" : "int32"},
            {"name" : "h", "type" : "int32"},
            {"name" : "i", "type" : "int32"},
            {"name" : "j", "type" : "int32"},
            {"name" : "k", "type" : "int32"},
            {"name" : "l", "type" : "int32"},
            {"name" : "m", "type" : "int32"},
            {"name" : "n", "type" : "int32"},
            {"name" : "o", "type" : "int32"},
            {"name" : "p", "type" : "int32"}
        ]
    } ])";

// 全表扫描查询
int readFullMemTable(GmcStmtT *stmt, char *tableOut)
{
    int ret = 0;
    // scan
    B objOut = (B){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    char schemaJson[1024] = "";
    (void)sprintf(schemaJson, g_schemaJson, tableOut);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {tableOut, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &objOut, &deseri, &deseriCtx, false, &labelInfo);

    // scan 全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, tableOut, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // out
        cnt++;
    }

    return cnt;
}

// 批量写 表  A(a , b)
int batchFullMemA(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][17], int dataNum)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        // a
        int32_t value1 = count[i][0];
        int stringLen = 10;  // string
        uint8_t *buf1 = (uint8_t *)malloc(stringLen + 1);
        if (buf1 == NULL) {
            return -1;
        }
        memset(buf1, stringLen + 1, 0);
        (void)snprintf((char *)buf1, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_STRING, buf1, (strlen((char *)buf1)));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t value2 = count[i][1];  // string
        uint8_t *buf2 = (uint8_t *)malloc(stringLen + 1);
        if (buf2 == NULL) {
            return -1;
        }
        memset(buf2, stringLen + 1, 0);
        (void)snprintf((char *)buf2, stringLen + 1, "b%08d", value2);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_STRING, buf2, (strlen((char *)buf2)));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t value3 = count[i][2];  // string
        uint8_t *buf3 = (uint8_t *)malloc(stringLen + 1);
        if (buf3 == NULL) {
            return -1;
        }
        memset(buf3, stringLen + 1, 0);
        (void)snprintf((char *)buf3, stringLen + 1, "b%08d", value3);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_STRING, buf3, (strlen((char *)buf3)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][3];
        uint8_t *buf15 = (uint8_t *)malloc(stringLen + 1);
        if (buf15 == NULL) {
            return -1;
        }
        memset(buf15, stringLen + 1, 0);
        (void)snprintf((char *)buf15, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_STRING, buf15, (strlen((char *)buf15)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][4];
        uint8_t *buf4 = (uint8_t *)malloc(stringLen + 1);
        if (buf4 == NULL) {
            return -1;
        }
        memset(buf4, stringLen + 1, 0);
        (void)snprintf((char *)buf4, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_STRING, buf4, (strlen((char *)buf4)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][5];
        uint8_t *buf5 = (uint8_t *)malloc(stringLen + 1);
        if (buf5 == NULL) {
            return -1;
        }
        memset(buf5, stringLen + 1, 0);
        (void)snprintf((char *)buf5, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_STRING, buf5, (strlen((char *)buf5)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][6];
        uint8_t *buf6 = (uint8_t *)malloc(stringLen + 1);
        if (buf6 == NULL) {
            return -1;
        }
        memset(buf6, stringLen + 1, 0);
        (void)snprintf((char *)buf6, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_STRING, buf6, (strlen((char *)buf6)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][7];
        uint8_t *buf7 = (uint8_t *)malloc(stringLen + 1);
        if (buf7 == NULL) {
            return -1;
        }
        memset(buf7, stringLen + 1, 0);
        (void)snprintf((char *)buf7, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "h", GMC_DATATYPE_STRING, buf7, (strlen((char *)buf7)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][8];
        uint8_t *buf8 = (uint8_t *)malloc(stringLen + 1);
        if (buf8 == NULL) {
            return -1;
        }
        memset(buf8, stringLen + 1, 0);
        (void)snprintf((char *)buf8, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "i", GMC_DATATYPE_STRING, buf8, (strlen((char *)buf8)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][9];
        uint8_t *buf9 = (uint8_t *)malloc(stringLen + 1);
        if (buf9 == NULL) {
            return -1;
        }
        memset(buf9, stringLen + 1, 0);
        (void)snprintf((char *)buf9, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "j", GMC_DATATYPE_STRING, buf9, (strlen((char *)buf9)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][10];
        uint8_t *buf10 = (uint8_t *)malloc(stringLen + 1);
        if (buf10 == NULL) {
            return -1;
        }
        memset(buf10, stringLen + 1, 0);
        (void)snprintf((char *)buf10, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "k", GMC_DATATYPE_STRING, buf10, (strlen((char *)buf10)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][13];
        uint8_t *buf13 = (uint8_t *)malloc(stringLen + 1);
        if (buf13 == NULL) {
            return -1;
        }
        memset(buf13, stringLen + 1, 0);
        (void)snprintf((char *)buf13, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "l", GMC_DATATYPE_STRING, buf13, (strlen((char *)buf13)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][11];
        uint8_t *buf11 = (uint8_t *)malloc(stringLen + 1);
        if (buf11 == NULL) {
            return -1;
        }
        memset(buf11, stringLen + 1, 0);
        (void)snprintf((char *)buf11, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "m", GMC_DATATYPE_STRING, buf11, (strlen((char *)buf11)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][12];
        uint8_t *buf12 = (uint8_t *)malloc(stringLen + 1);
        if (buf12 == NULL) {
            return -1;
        }
        memset(buf12, stringLen + 1, 0);
        (void)snprintf((char *)buf12, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "n", GMC_DATATYPE_STRING, buf12, (strlen((char *)buf12)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][14];
        uint8_t *buf14 = (uint8_t *)malloc(stringLen + 1);
        if (buf14 == NULL) {
            return -1;
        }
        memset(buf14, stringLen + 1, 0);
        (void)snprintf((char *)buf14, stringLen + 1, "b%08d", value1);
        ret = GmcSetVertexProperty(stmt, "o", GMC_DATATYPE_STRING, buf14, (strlen((char *)buf14)));
        EXPECT_EQ(GMERR_OK, ret);

        value1 = count[i][15];
        ret = GmcSetVertexProperty(stmt, "p", GMC_DATATYPE_INT32, &value1, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        value1 = count[i][16];
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(buf2);
        free(buf1);
        free(buf3);
        free(buf4);
        free(buf5);
        free(buf6);
        free(buf7);
        free(buf8);
        free(buf9);
        free(buf10);
        free(buf11);
        free(buf12);
        free(buf13);
        free(buf14);
        free(buf15);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 内存满设置属性值
void set_VertexProperty_string(GmcStmtT *stmt, char *f14_value)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
}

//  写入hashcluster索引值完全相同
int wtriteOtherTable(GmcStmtT *stmt, const char *labelName)
{
    int ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t i_max = 1;
    char TestValue[524] = {};
    uint64_t f7_value = i_max;
    memset(TestValue, 'a', 523);
    while (ret == 0) {
        ret = GmcSetVertexProperty(g_stmt, (char *)"F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_string(g_stmt, TestValue);
        ret = GmcExecute(g_stmt);
        if (i_max % 5000 == 0) {
            AW_FUN_Log(LOG_INFO, "%lld record insert success, No:%lld ", i_max, (i_max / 5000));
        }
        if (i_max >= 255000) {
            AW_FUN_Log(LOG_INFO, "%lld record insert success ", i_max);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        i_max++;
        f7_value++;
    }
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "insert %lld record success, memory is full ", i_max);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/* ****************************************************************************
 Description  : 018.内存满，向触发表写不同主键数据直到内存满
**************************************************************************** */
TEST_F(datalogState, DataLog_031_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_018";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用其它vertex表先插入一些数据


    char *schema = NULL;
    const char labelName[] = "T20_004_004";
    char label_config[] = "{\"max_record_count\":1000000,\"auto_increment\":1, \"isFastReadUncommitted\":1}";
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("datalog_dml/T20_004_004.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // 写满内存
    ret = wtriteOtherTable(g_stmt, labelName);    /* A(k, p) :- D(K, p).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";
    int i = 0;
    int result = 0;
    int propertyNum = 17;  // 加上count
    int32_t count[300][17] = {};
    int dataNum = 300;  // 每次插入的数据数量
    while (!result) {
        int memProperteyValue = i * 300;
        for (int j = 0; j < dataNum; j++) {
            for (int k = 0; k < propertyNum; k++) {
                count[j][k] = memProperteyValue++;
            }
        }

        int record = i*300;
        ret = readFullMemTable(g_stmt, tableA);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        ret = readFullMemTable(g_stmt, tableD);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        ret = readFullMemTable(g_stmt, tableB);
        AW_MACRO_EXPECT_EQ_INT(record, ret);

        // 写入数据
        result = batchFullMemA(g_conn, g_stmt, tableD, count, 300);

        if (i % 50 == 0 && !result) {
            AW_FUN_Log(LOG_STEP, "now insert count is %d.", (i + 1)*300);
        }
        i++;
    }
    AW_FUN_Log(LOG_STEP, "datalog total insert %d.", i*300);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, result);
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, labelName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
