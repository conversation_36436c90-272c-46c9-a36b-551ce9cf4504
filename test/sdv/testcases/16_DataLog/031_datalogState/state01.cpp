/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: ruleTest.cpp
 * Description: datalog state
 * Author: youwanyong ywx1157510
 * Create: 2023-03-05
 */
#include <time.h>
#include "datalogState.h"
#include "t_datacom_lite.h"
using namespace std;

class datalogState : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void datalogState::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}
void datalogState::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

const char *expectOutput = R"()";

/* ****************************************************************************
 Description  : 001.定义状态表，状态函数，状态表无数据，触发表写相同主键数据，
使状态表中产生数据，状态函数中查看触发表delata 并查询状态表记录数
**************************************************************************** */
TEST_F(datalogState, DataLog_031_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_CHECK_LOG_BEGIN(0);
    char nsName[128] = "state_031_001";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // 关闭日志折叠防止受其它用例影响日志检测
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    testGmcGetLastError();
    /* B(k, s) :- A(k, p), tran(p, s).
     C(k, T) :- B(k, T), D(K, T).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";

    AW_FUN_Log(LOG_STEP, "1.第一次向表A插入一条数据");
    int32_t count[][3] = {{1, 2, 1}, {1, 23, 2}, {1, 4, -2}, {1, 5, 1}, {1, 6, 2}, {1, 7, -2}, {1, 8, 1}, {1, 9, 2},
        {1, 12, -2}, {1, 13, -2}};  // tuple merge顺序不定一条
    // 写入10条数据
    ret = batchA(g_conn, g_stmt, tableA, count, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 1;
    AW_FUN_Log(LOG_STEP, "校验结果");
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t result[][3] = {{1, 1, 1}};
    ret = CompareValue(1, result, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发字段值为3  count 字段为1 删除同主键数据
    int32_t count1[][3] = {{1, 22, 1}, {1, 3, -2}, {1, 4, -2}, {1, 5, 1}, {1, 6, 2}, {1, 7, -2}, {1, 8, 1}, {1, 9, 2},
        {1, 12, -2}, {1, 13, -2}};

    ret = batchA(g_conn, g_stmt, tableA, count1, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 校验结果
    AW_FUN_Log(LOG_STEP, "校验结果");
    ret = readTable(g_stmt, tableB);
    record = 0;
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t result1[][3] = {};
    ret = CompareValue(0, result1, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran", "Access Current: A");
    queryUdfView("B", "Index Key: (upgradeVersion, a) = (Para(0), Para(1))", "Output: dtlReservedCount, upgradeVersion, k, s");

    const char * logFilter = "UDF func:dtl_ext_func_tran exceed";
    ret = CheckLog(logFilter, 1);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次开启日志折叠
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.定义状态表，状态函数中 写触发表delta，读状态表org，状态表无数据，
触发表写入不同主键数据，全表扫描触发表，全表扫描状态表，
触发表写入同主键数据，全表扫描触发表，全表扫描状态表
**************************************************************************** */
TEST_F(datalogState, DataLog_031_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_002";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* H(k, p) :- A(k, p).
B(k, s) :- A(k, p), tran(p, s).
C(K, T) :- B(K, T), D(K, T).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableH[] = "H";

    AW_FUN_Log(LOG_STEP, "1.触发表A写入不主键数据");
    int32_t count[][3] = {
        {2, 3, 2}, {3, 4, -2}, {4, 5, 1}, {5, 6, 2}, {6, 7, -2}, {7, 8, 1}, {8, 9, 2}, {9, 12, -2}, {10, -10, -1}};

    // 写入9条数据
    ret = batchA(g_conn, g_stmt, tableH, count, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 全表扫描验证数据
    int record = 10;
    ret = readTable(g_stmt, tableH);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 10;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 8;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    AW_FUN_Log(LOG_STEP, "2.触发表A写入同主键数据");  // 普通表同主键批量会优先第一条无论正负
    int32_t count1[][3] = {
        {1, -2, 1}, {1, 3, 2}, {1, 4, -2}, {1, 5, 1}, {1, 6, 2}, {1, 7, -2}, {1, 8, 1}, {1, 9, 2}, {1, 12, -2}};

    // 写入9条数据
    ret = batchA(g_conn, g_stmt, tableH, count1, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    record = 10;
    int32_t countA[][3] = {{2, 3, 1}, {3, 4, -1}, {4, 5, 1}, {5, 6, 1}, {6, 7, -1}, {7, 8, 1}, {8, 9, 1}, {9, 12, -1},
        {10, -10, -1}, {-10, -10, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufTest, 0, 100);

    record = 8;
    int32_t countB[][3] = {{2, 3, 1}, {3, 4, 1}, {4, 5, 1}, {5, 6, 1}, {6, 7, 1}, {7, 8, 1}, {8, 9, 1}, {9, 12, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 查询udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran", "Access Delta: H", "Access Current: B");
    queryUdfView("B", "Index Key: (upgradeVersion, a) = (Para(0), Para(1))", " Output: dtlReservedCount, upgradeVersion, k, s");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.定义状态表，状态函数，状态表有数据，写其它输入表，使状态表中数据删除，状态函数查看触发表org
**************************************************************************** */
TEST_F(datalogState, DataLog_031_003)
{

    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_003";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* A(k, p) :- D(K, p).
  B(k, s) :- A(k, p), tran(p, s).
  C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";
    char tableC[] = "C";

    AW_FUN_Log(LOG_STEP, "1.向输入表写入数据");
    int32_t countA1[][3] = {
        {2, 3, 2}, {3, 4, -2}, {4, 5, 1}, {5, 6, 2}, {6, 7, -2}, {7, 8, 1}, {8, 9, 2}, {9, 12, -2}, {10, -10, -1}};

    // 写入9条数据
    ret = batchA(g_conn, g_stmt, tableD, countA1, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 全表扫描验证数据
    int record = 10;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // 全表扫描验证数据
    record = 8;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // 写表A删除表B
    int32_t countA2[][3] = {{2, 3, -2}, {3, 4, 2}, {4, 5, -1}, {5, 6, -2}, {6, 7, 2}, {7, 8, -1}, {8, 9, -2},
        {9, 12, 2}, {10, -10, 1}, {2, -1, 2}, {3, -1, -2}, {4, -1, 1}, {5, -1, 2}, {6, -1, -2}, {7, -1, 1}, {8, -1, 2},
        {9, -1, -2}, {10, -10, -1}};

    // 写入9条数据
    ret = batchA(g_conn, g_stmt, tableD, countA2, 18);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    int32_t countA3[][3] = {
        {2, -1, 1},
        {3, -1, -1},
        {4, -1, 1},
        {5, -1, 1},
        {6, -1, -1},
        {7, -1, 1},
        {8, -1, 1},
        {9, -1, -1},
        {10, -10, -1},
        {-3, -3, 1},
    };

    // 全表扫描验证数据
    record = 10;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA3, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 全表扫描验证数据
    record = 0;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // 查询执行计划udf视图
    // 查询udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran", "Access Delta: D", "Access Current: B");
    queryUdfView(
        "B", "InsertTable on Label(C)", "Output: dtlReservedCount, upgradeVersion, k, s", "Projection: (dtlReservedCount, upgradeVersion, k, s)");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
 004.定义状态表，状态函数，状态表有数据，写触发表，使状态表中数据删除，触发表含agg，agg中写触发表的delta
**************************************************************************** */
TEST_F(datalogState, DataLog_031_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_004";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* A(k, c) :- D(k, p) GROUP-BY(k) funcA(p, c).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s)..*/
    char tableA[] = "ns1.A";
    char tableB[] = "ns1.B";
    char tableD[] = "ns1.D";

    AW_FUN_Log(LOG_STEP, "1.触发表A写入一条数据数据");  // 触发udf写表A
    int32_t count[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}};

    // 写入1条数据
    ret = batchA(g_conn, g_stmt, tableD, count, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count1[][3] = {{1, 2, -1}, {2, 2, -1}, {3, 2, -1}};
    ret = batchA(g_conn, g_stmt, tableD, count1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int32_t count2[][3] = {{1, -1, 1}, {2, -1, 1}, {3, -1, 1}};

    ret = batchA(g_conn, g_stmt, tableD, count2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 全表扫描验证数据
    int record = 0;
    int32_t countA[][3] = {{1, 2, 3}, {2, -1, 1}, {3, -1, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 0;
    ret = readTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, count2, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 3;
    int32_t countB[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 查询执行计划udf视图
    queryUdfView("ns1.A", "UDF: dtl_ext_func_tran");
    queryUdfView("ns1.D", "Aggregate Function: dtl_agg_func_ns1_funcA", "Access Delta: ns1.A");
    queryUdfView("ns1.B", "InsertTable on Label(ns1.C)", "Output: dtlReservedCount, upgradeVersion, k, s",
        "Projection: (dtlReservedCount, upgradeVersion, k, s)");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
 005.定义状态表，状态函数，状态表有数据，写触发表，使状态表中数据增加删除，触发表含funtion，function中写触发表的delta
**************************************************************************** */
TEST_F(datalogState, DataLog_031_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_005";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* A(k, p) :- D(K, p).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";

    AW_FUN_Log(LOG_STEP, "1.写输入表D");  // 触发udf写表A
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};

    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableD, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 5;
    int32_t countA[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    int32_t countD[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 5;
    int32_t countB[][3] = {{1, 1, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 查询执行计划udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran");
    queryUdfView("D", " UDF: dtl_ext_func_funcA");
    queryUdfView(
        "B", "InsertTable on Label(C)", "Output: dtlReservedCount, upgradeVersion, k, s", "Projection: (dtlReservedCount, upgradeVersion, k, s)");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.定义状态表，状态函数，状态表有数据， int64
写触发表，使状态表中数据删除，触发表含timeout
**************************************************************************** */
TEST_F(datalogState, DataLog_031_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_006";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    struct timespec ts;

    (void)clock_gettime(CLOCK_MONOTONIC, &ts);
    AW_FUN_Log(LOG_STEP, "1.写timeout表A");
    int64_t count[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 2, 1}, {4, 1, 1}, {5, 1, 1}};
    // 写入不同主键数据是状态表数据增加       从32变为64
    ret = batchTimeoutA(g_conn, g_stmt, tableA, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    int64_t record = 5;
    (void)clock_gettime(CLOCK_MONOTONIC, &ts);
    ret = readTimeoutTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int64_t countA[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 2, 1}, {4, 1, 1}, {5, 1, 1}};
    for (int i = 0; i < 5; i++) {
        int64_t midValue = countA[i][0];
        countA[i][0] = midValue + (int64_t)(ts.tv_sec);
    }

    ret = CompareTimeoutValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest1, 0, 100);

    record = 2;
    int64_t countC[][3] = {{1, 1, 1}, {3, 1, 1}};
    for (int i = 0; i < 2; i++) {
        int64_t midValue = countC[i][0];
        countC[i][0] = midValue + (int64_t)(ts.tv_sec);
    }
    ret = readTimeoutTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareTimeoutValue(record, countC, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest1, 0, 100);

    int64_t countB[][3] = {{1, 1, 1}, {3, 1, 1}};
    for (int i = 0; i < 2; i++) {
        int64_t midValue = countB[i][0];
        countB[i][0] = midValue + (int64_t)(ts.tv_sec);
    }
    ret = readTimeoutTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareTimeoutValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest1, 0, 100);

    sleep(8);  // 默认3s过期
    record = 0;
    ret = readTimeoutTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    record = 2;
    ret = readTimeoutTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = readTimeoutTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 查询执行计划udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran");
    queryUdfView("C", "DefaultDeltaMerge on Label(C)");
    queryUdfView(
        "B", "InsertTable on Label(C)", "dtlReservedCount, upgradeVersion, k, s", "Projection: (dtlReservedCount, upgradeVersion, k, s)");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.定义状态表，状态函数，输出表为外部表，写触发表，使状态表中数据增加及删除
**************************************************************************** */
TEST_F(datalogState, DataLog_031_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_007";
    char labelName_out[64] = "C";

    // 创建外部表
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "C");
    GmcDropVertexLabel(g_stmt, labelName_out);
    readJanssonFile("./datalog_dml/C.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    int32_t ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* A(k, p) :- D(k, p).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";
    char tableC[] = "C";

    AW_FUN_Log(LOG_STEP, "1.写输入表D");
    int32_t count[][3] = {{1, 2, 1}, {2, 2, 2}, {3, 2, 3}, {4, 2, 4}, {5, 2, 5}};

    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableD, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    int record = 5;
    int32_t countA[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    int32_t countD[][3] = {{1, 2, 1}, {2, 2, 2}, {3, 2, 3}, {4, 2, 4}, {5, 2, 5}};
    ret = readTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    int32_t countB[][3] = {{1, 1, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    int32_t count2[][3] = {{1, 2, -1}, {2, 2, -2}, {3, 2, -3}, {4, 2, -4}, {5, 2, -5}};
    // 写入5条数据 删除表A中数据
    ret = batchA(g_conn, g_stmt, tableD, count2, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 0;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t count3[][3] = {{1, 3, -1}, {2, 3, -2}, {3, 3, -3}, {4, 3, -4}, {5, 3, -5}};
    // 写入5条数据 删除表B中数据
    ret = batchA(g_conn, g_stmt, tableD, count3, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = readTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.定义状态表，状态函数，输出表为tbm表，写触发表，使状态表中数据增加及删除
**************************************************************************** */
TEST_F(datalogState, DataLog_031_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_009";
    system("mkdir -p /root/datalog_file/");
    system("chmod 777 -R /root/datalog_file/");
    (void)SystemSnprintf("> %s", g_logName);

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* A(k, c) :- D(k, c).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";
    char tableC[] = "C";

    AW_FUN_Log(LOG_STEP, "1.写输入表D");
    int32_t count[][3] = {{1, 2, 1}, {2, 2, 2}, {3, 2, 3}, {4, 2, 4}, {5, 2, 5}};

    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableD, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    int record = 5;
    int32_t countA[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    int32_t countD[][3] = {{1, 2, 1}, {2, 2, 2}, {3, 2, 3}, {4, 2, 4}, {5, 2, 5}};
    ret = readTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    int32_t countB[][3] = {{1, 1, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    int32_t count2[][3] = {{1, 2, -1}, {2, 2, -2}, {3, 2, -3}, {4, 2, -4}, {5, 2, -5}};
    // 写入5条数据 删除表A中数据
    ret = batchA(g_conn, g_stmt, tableD, count2, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 0;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t count3[][3] = {{1, 3, -1}, {2, 3, -2}, {3, 3, -3}, {4, 3, -4}, {5, 3, -5}};
    // 写入5条数据 删除表B中数据
    ret = batchA(g_conn, g_stmt, tableD, count3, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // TBM 查询

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)SystemSnprintf("cat %s", g_logName);
    system("rm -rf /root/datalog_file/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *expect_gmjson1 = R"(
{
    "version": "2.0",
    "type": "record",
    "name": "A",
    "ylog_type": "normal_table",
    "inout_type": "intermediate",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "a",
            "type": "int32"
        },
        {
            "name": "b",
            "type": "int32"
        }
    ],
    "keys": [
        {
            "name": "pk",
            "index_name": "0",
            "index": {
                "type": "primary"
            },
            "node": "A",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "a"
            ]
        }
    ]
}
)";

const char *expect_gmjson2 = R"(
{
    "version": "2.0",
    "type": "record",
    "name": "B",
    "ylog_type": "state",
    "inout_type": "intermediate",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "k",
            "type": "int32"
        },
        {
            "name": "s",
            "type": "int32"
        }
    ],
    "keys": [
        {
            "name": "pk",
            "index_name": "0",
            "index": {
                "type": "primary"
            },
            "node": "B",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "k"
            ]
        }
    ]
}
)";

const char *expect_gmjson3 = R"(
{
    "version": "2.0",
    "type": "record",
    "name": "uninit",
    "ylog_type": "function",
    "inout_type": "inout_undefined",
    "ylog_udf_prefix": [
        "dtl_ext_func_"
    ],
    "fields": []
}
)";

const char *expect_gmjson4 = R"(
{
    "version": "2.0",
    "type": "record",
    "name": "D",
    "ylog_type": "normal_table",
    "inout_type": "input",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "a",
            "type": "int32"
        },
        {
            "name": "b",
            "type": "int32"
        }
    ],
    "keys": [
        {
            "name": "pk",
            "index_name": "0",
            "index": {
                "type": "primary"
            },
            "node": "D",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "a"
            ]
        }
    ]
}
)";

const char *expect_gmjson5 = R"(
{
    "version": "2.0",
    "type": "record",
    "name": "tran",
    "ylog_type": "function",
    "inout_type": "input",
    "ylog_udf_prefix": [
        "dtl_ext_func_"
    ],
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "p",
            "type": "int32"
        },
        {
            "name": "s",
            "type": "int32"
        }
    ]
}
)";

const char *expect_gmjson6 = R"(
{
    "version": "2.0",
    "type": "record",
    "name": "tran",
    "ylog_type": "function",
    "inout_type": "input",
    "ylog_udf_prefix": [
        "dtl_ext_func_"
    ],
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "p",
            "type": "int32"
        },
        {
            "name": "s",
            "type": "int32"
        }
    ]
}
)";

/* ****************************************************************************
 Description  : 010.datalog支持使用gmconvert转化含状态表.d为gmjson文件
**************************************************************************** */
TEST_F(datalogState, DataLog_031_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 指定一个含自定义主键的普通表的.d文件，使用gmconvert 转换为gmjson文件
    char inPath[256] = "datalog_dml/state_031_007.d";
    char outPath[256] = "datalog_dml";
    snprintf(g_command, MAX_CMD_SIZE, "gmconvert -i %s -o %s", inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    int ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验A.gmjson
    char *result = NULL;

    snprintf(g_command, MAX_CMD_SIZE, "cat datalog_dml/A.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(expect_gmjson1, result));
    free(result);

    // 校验B.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat datalog_dml/B.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(expect_gmjson2, result));
    free(result);

    // 校验tran.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat datalog_dml/tran.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(expect_gmjson6, result));
    free(result);

    // 校验D.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat datalog_dml/D.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "%s", result);
    EXPECT_STRNE(NULL, strstr(expect_gmjson4, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.开启悲观可串行化事务事务提交向状态表中写入数据及删除数据的操作
**************************************************************************** */
TEST_F(datalogState, DISABLED_DataLog_031_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_005";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* A(k, p) :- D(K, p).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";

    AW_FUN_Log(LOG_STEP, "1.写输入表D");  // 触发udf写表A
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};

    // 事务隔离级别
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    // 开启事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableD, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务提交
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int record = 5;
    // 读数据
    int32_t countA[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    int32_t countD[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 5;
    int32_t countB[][3] = {{1, 1, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 查询执行计划udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran");
    queryUdfView("D", " UDF: dtl_ext_func_funcA");
    queryUdfView(
        "B", "InsertTable on Label(C)", "Output: k, s, dtlReservedCount", "Projection: (k, s, dtlReservedCount)");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.开启悲观可串行化事务事务回滚向状态表中写入数据及删除删除数据的操作
**************************************************************************** */
TEST_F(datalogState, DISABLED_DataLog_031_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_005";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* A(k, p) :- D(K, p).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";

    AW_FUN_Log(LOG_STEP, "1.写输入表D");  // 触发udf写表A
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    // 事务隔离级别
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    // 开启事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableD, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务回滚
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int record = 0;
    // 读数据
    int32_t countA[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    int32_t countD[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 0;
    int32_t countB[][3] = {{1, 1, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 查询执行计划udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran");
    queryUdfView("D", " UDF: dtl_ext_func_funcA");
    queryUdfView(
        "B", "InsertTable on Label(C)", "Output: k, s, dtlReservedCount", "Projection: (k, s, dtlReservedCount)");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.流控场景，定义状态表，状态函数，状态表无数据，
写触发表，使状态表中产生数据先增加后减少
**************************************************************************** */
TEST_F(datalogState, DataLog_031_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_005";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* A(k, p) :- D(K, p).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";

    AW_FUN_Log(LOG_STEP, "1.写输入表D");  // 触发udf写表A
    int32_t count[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};

    // 写入5条数据
    ret = batchQueueA(g_conn, g_stmt, tableD, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record = 5;
    int32_t countA[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    int32_t countD[][3] = {{1, 2, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 5;
    int32_t countB[][3] = {{1, 1, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 查询执行计划udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran");
    queryUdfView("D", " UDF: dtl_ext_func_funcA");
    queryUdfView(
        "B", "InsertTable on Label(C)", "Output: dtlReservedCount, upgradeVersion, k, s", "Projection: (dtlReservedCount, upgradeVersion, k, s)");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.多个状态表对应一个触发表，写触发表，使状态表中数据增加删除
**************************************************************************** */
TEST_F(datalogState, DataLog_031_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_012";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* B1(k, s) :- A(k, p), tran1(p, s).
C(K, T) :- B1(K, T), D(K, T).
B2(k, s) :- A(k, p), tran2(p, s).
C(K, T) :- B2(K, T), D(K, T).
B3(k, s) :- A(k, p), tran3(p, s).
C(K, T) :- B3(K, T), D(K, T).
B4(k, s) :- A(k, p), tran4(p, s).
C(K, T) :- B4(K, T), D(K, T).
B5(k, s) :- A(k, p), tran5(p, s).
C(K, T) :- B5(K, T), D(K, T)
*/
    char tableA[] = "A";
    char tableB1[] = "B1";
    char tableB2[] = "B2";
    char tableB3[] = "B3";
    char tableB4[] = "B4";
    char tableB5[] = "B5";
    char tableC[] = "C";

    // 写触发表A
    AW_FUN_Log(LOG_STEP, "1.写触发表A");
    int32_t countA1[][3] = {{1, 1, 1}, {2, 2, 1}, {3, 3, 1}, {4, 4, 1}, {5, 5, 1}};
    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableA, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 5;
    // 查询状态表数据
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA1, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 1;
    ret = readTable(g_stmt, tableB1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countB1[][3] = {{1, 1, 1}};
    ret = CompareValue(record, countB1, tableB1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countB2[][3] = {{2, 2, 1}};
    ret = CompareValue(record, countB2, tableB2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countB3[][3] = {{3, 3, 1}};
    ret = CompareValue(record, countB3, tableB3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB4);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countB4[][3] = {{4, 4, 1}};
    ret = CompareValue(record, countB4, tableB4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB5);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countB5[][3] = {{5, 5, 1}};
    ret = CompareValue(record, countB5, tableB5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 删除触发表A数据
    AW_FUN_Log(LOG_STEP, "2.删除触发表A数据.");
    int32_t countA2[][3] = {{1, 1, -1}, {2, 2, -1}, {3, 3, -1}, {4, 4, -1}, {5, 5, -1}};
    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableA, countA2, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 0;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 查询状态表B数据
    record = 1;
    ret = readTable(g_stmt, tableB1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB1, tableB1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB2, tableB2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB3, tableB3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB4);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB4, tableB4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB5);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB5, tableB5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 插入触发表数据是状态表中数据删除
    AW_FUN_Log(LOG_STEP, "3.插入触发表数据是状态表中数据删除.");
    int32_t countA3[][3] = {{1, 2, -1}, {2, 3, -1}, {3, 4, -1}, {4, 5, -1}, {5, 6, -1}};
    ret = batchA(g_conn, g_stmt, tableA, countA3, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  查询状态表中数据
    record = 5;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    record = 0;
    ret = readTable(g_stmt, tableB1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    record = 1;
    ret = readTable(g_stmt, tableB2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = readTable(g_stmt, tableB3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = readTable(g_stmt, tableB4);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = readTable(g_stmt, tableB5);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.多个触发表对应一个状态表写触发表，使状态表中数据增加删除
**************************************************************************** */
TEST_F(datalogState, DataLog_031_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_015";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* B1(k, s) :- A(k, p), tran1(p, s).
C(K, T) :- B1(K, T), D(K, T).
B2(k, s) :- A(k, p), tran2(p, s).
C(K, T) :- B2(K, T), D(K, T).
B3(k, s) :- A(k, p), tran3(p, s).
C(K, T) :- B3(K, T), D(K, T).
B4(k, s) :- A(k, p), tran4(p, s).
C(K, T) :- B4(K, T), D(K, T).
B5(k, s) :- A(k, p), tran5(p, s).
C(K, T) :- B5(K, T), D(K, T)
*/

    char tableB[] = "B";
    char tableA1[] = "A1";
    char tableA2[] = "A2";
    char tableA3[] = "A3";
    char tableA4[] = "A4";
    char tableA5[] = "A5";
    char tableC[] = "C";

    // 写触发表A
    AW_FUN_Log(LOG_STEP, "1.写所有触发表");
    int32_t countA1[][3] = {{1, 1, 1}, {2, 2, 1}, {3, 3, 1}, {4, 4, 1}, {5, 5, 1}};
    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableA1, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA2, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA3, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA4, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA5, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 5;
    // 查询状态表数据
    ret = readTable(g_stmt, tableA1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA1, tableA1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 5;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA1, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 删除触发表中各自对应的状态表数据
    AW_FUN_Log(LOG_STEP, "2.删除触发表中各自对应的状态表数据.");
    int32_t countA11[][3] = {{1, 1, -1}};
    // A1写入1条数据
    ret = batchA(g_conn, g_stmt, tableA1, countA11, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 4;
    ret = readTable(g_stmt, tableA1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA21[][3] = {{2, 2, -1}};
    // A2写入1条数据
    ret = batchA(g_conn, g_stmt, tableA2, countA21, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 4;
    ret = readTable(g_stmt, tableA2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA31[][3] = {{3, 3, -1}};
    // A3写入1条数据
    ret = batchA(g_conn, g_stmt, tableA3, countA31, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 4;
    ret = readTable(g_stmt, tableA3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA41[][3] = {{4, 4, -1}};
    // A4写入1条数据
    ret = batchA(g_conn, g_stmt, tableA4, countA41, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 4;
    ret = readTable(g_stmt, tableA4);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA51[][3] = {{5, 5, -1}};
    // A5写入1条数据
    ret = batchA(g_conn, g_stmt, tableA5, countA51, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 4;
    ret = readTable(g_stmt, tableA5);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // // 查询状态表B数据
    record = 5;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA1, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 插入触发表数据是状态表中数据删除
    AW_FUN_Log(LOG_STEP, "3.插入触发表数据使状态表中数据删除.");
    int32_t countA12[][3] = {{1, 2, -1}};
    // A1写入1条数据
    ret = batchA(g_conn, g_stmt, tableA1, countA12, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 5;
    ret = readTable(g_stmt, tableA1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA22[][3] = {{2, 3, -1}};
    // A2写入1条数据
    ret = batchA(g_conn, g_stmt, tableA2, countA22, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 5;
    ret = readTable(g_stmt, tableA2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA32[][3] = {{3, 4, -1}};
    // A3写入1条数据
    ret = batchA(g_conn, g_stmt, tableA3, countA32, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 5;
    ret = readTable(g_stmt, tableA3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA42[][3] = {{4, 5, -1}};
    // A4写入1条数据
    ret = batchA(g_conn, g_stmt, tableA4, countA42, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 5;
    ret = readTable(g_stmt, tableA4);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA52[][3] = {{5, 6, -1}};
    // A5写入1条数据
    ret = batchA(g_conn, g_stmt, tableA5, countA52, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 5;
    ret = readTable(g_stmt, tableA5);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    record = 0;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.正常定义一个状态函数，触发表为普通输入表，正常定义多个状态表，该状态函数和每个状态表组成一条规则
**************************************************************************** */
TEST_F(datalogState, DataLog_031_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_016";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /*
B(k, s) :- A(k, p), tran1(p, s).
C(K, T) :- B(K, T).
B(k, s) :- A(k, p), tran2(p, s).
C(K, T) :- B(K, T).
B(k, s) :- A(k, p), tran3(p, s).
C(K, T) :- B(K, T).
B(k, s) :- A(k, p), tran4(p, s).
C(K, T) :- B(K, T).
B(k, s) :- A(k, p), tran5(p, s).
C(K, T) :- B(K, T).
*/

    char tableB[] = "B";
    char tableA[] = "A";
    char tableC[] = "C";

    // 写触发表A
    AW_FUN_Log(LOG_STEP, "1.写所有触发表");
    int32_t countA1[][3] = {{1, 1, 1}, {2, 2, 1}, {3, 3, 1}, {4, 4, 1}, {5, 5, 1}};
    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableA, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 5;
    // 查询状态表数据
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA1, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 5;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA1, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 删除触发表中各自对应的状态表数据
    AW_FUN_Log(LOG_STEP, "2.删除触发表中各自对应的状态表数据.");
    int32_t countA11[][3] = {{1, 1, -1}, {2, 2, -1}, {3, 3, -1}, {4, 4, -1}, {5, 5, -1}};
    // A1写入1条数据
    ret = batchA(g_conn, g_stmt, tableA, countA11, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 0;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // // 查询状态表B数据
    record = 1;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countA21[][3] = {{1, 1, 1}};
    ret = CompareValue(record, countA21, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 插入触发表数据是状态表中数据删除
    AW_FUN_Log(LOG_STEP, "3.插入触发表数据使状态表中数据删除.");
    int32_t countA12[][3] = {{1, 2, -1}};
    // A写入5条数据
    ret = batchA(g_conn, g_stmt, tableA, countA12, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 1;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    record = 0;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.多个触发表对应多个状态表写触发表，使状态表中数据增加删除
**************************************************************************** */
TEST_F(datalogState, DataLog_031_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_017";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /*
B1(k, s) :- A1(k, p), tran1(p, s).
C(K, T) :- B2(K, T).
B2(k, s) :- A2(k, p), tran2(p, s).
C(K, T) :- B2(K, T).
B3(k, s) :- A3(k, p), tran3(p, s).
C(K, T) :- B3(K, T).
B4(k, s) :- A4(k, p), tran4(p, s).
C(K, T) :- B4(K, T).
B5(k, s) :- A5(k, p), tran5(p, s).
C(K, T) :- B5(K, T).
*/
    char tableB1[] = "B1";
    char tableB2[] = "B2";
    char tableB3[] = "B3";
    char tableB4[] = "B4";
    char tableB5[] = "B5";
    char tableA1[] = "A1";
    char tableA2[] = "A2";
    char tableA3[] = "A3";
    char tableA4[] = "A4";
    char tableA5[] = "A5";
    char tableC[] = "C";

    // 写5个触发表A
    AW_FUN_Log(LOG_STEP, "1.写所有触发表");
    int32_t countA1[][3] = {{1, 1, 1}, {2, 2, 1}, {3, 3, 1}, {4, 4, 1}, {5, 5, 1}};
    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableA1, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA2, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA3, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA4, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA5, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 查询状态表数据
    int record = 1;
    ret = readTable(g_stmt, tableB1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = readTable(g_stmt, tableB2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = readTable(g_stmt, tableB3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = readTable(g_stmt, tableB4);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = readTable(g_stmt, tableB5);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 删除触发表中各自对应的状态表数据
    AW_FUN_Log(LOG_STEP, "2.删除触发表中各自对应的状态表数据.");
    int32_t countA11[][3] = {{1, 1, -1}, {2, 2, -1}, {3, 3, -1}, {4, 4, -1}, {5, 5, -1}};
    // A1写入1条数据
    ret = batchA(g_conn, g_stmt, tableA1, countA11, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 0;
    ret = readTable(g_stmt, tableA1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = batchA(g_conn, g_stmt, tableA2, countA11, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 0;
    ret = readTable(g_stmt, tableA2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = batchA(g_conn, g_stmt, tableA3, countA11, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 0;
    ret = readTable(g_stmt, tableA3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = batchA(g_conn, g_stmt, tableA4, countA11, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 0;
    ret = readTable(g_stmt, tableA4);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    ret = batchA(g_conn, g_stmt, tableA5, countA11, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 0;
    ret = readTable(g_stmt, tableA5);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 查询状态表B数据
    record = 1;
    ret = readTable(g_stmt, tableB1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    // 插入触发表数据是状态表中数据删除
    AW_FUN_Log(LOG_STEP, "3.插入触发表数据使状态表中数据删除.");
    int32_t countA111[][3] = {{1, 2, -1}, {2, 3, -1}, {3, 4, -1}, {4, 5, -1}, {5, 6, -1}};
    ret = batchA(g_conn, g_stmt, tableA1, countA111, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA2, countA111, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA3, countA111, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA4, countA111, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = batchA(g_conn, g_stmt, tableA5, countA111, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 0;
    ret = readTable(g_stmt, tableB1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    record = 1;
    ret = readTable(g_stmt, tableB2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countA21[][3] = {{1, 2, 1}};
    ret = CompareValue(record, countA21, tableB2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countA31[][3] = {{2, 3, 1}};
    ret = CompareValue(record, countA31, tableB3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB4);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countA41[][3] = {{3, 4, 1}};
    ret = CompareValue(record, countA41, tableB4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableB5);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countA51[][3] = {{4, 5, 1}};
    ret = CompareValue(record, countA51, tableB5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.写触发表，订阅接收notify输出表消息
**************************************************************************** */
TEST_F(datalogState, DataLog_031_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_019";
    GmcConnT *subConn;
    const char *subName = "subVertexLabel";
    const char *subConnName = "subConnName";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    int ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // .d文件加载：创建表和连接规则
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t dataStart = 1;
    uint32_t writeCount = 5, batchNum = 1;
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmt, subConn, (char *)"datalog_dml/sub19.gmjson", &userData1,
        dataStart + writeCount * batchNum, subName, snCallback, inpGetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* A(k, p) :- D(K, p).
B(k, s) :- A(k, p), tran(p, s).
C(k, s) :- B(k, s).*/
    char tableA[] = "A";
    char tableB[] = "B";
    char tableD[] = "D";

    AW_FUN_Log(LOG_STEP, "1.写输入表D");  // 触发udf写表A
    int32_t count[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};

    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableD, count, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 5;
    // 读数据
    int32_t countA[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    int32_t countD[][3] = {{1, 2, 1}, {2, 2, 1}, {3, 2, 1}, {4, 2, 1}, {5, 2, 1}};
    ret = readTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 5;
    int32_t countB[][3] = {{1, 1, 1}, {2, 1, 1}, {3, 1, 1}, {4, 1, 1}, {5, 1, 1}};
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countB, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 订阅接收表C
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询执行计划udf视图
    queryUdfView("A", "UDF: dtl_ext_func_tran");
    queryUdfView("B", "InsertTable on Label(C)", "Output: dtlReservedCount, upgradeVersion, k, s",
        "Projection: (dtlReservedCount, upgradeVersion, k, s)");

    ret = cancelSubscription(g_stmt, subName, &userData1, dataStart, writeCount * batchNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    ret = testGmcDisconnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 函数的触发顺序                  -------------------------------------------
/* ****************************************************************************
 Description  : 020.正常定义多个状态函数，触发表为普通输入表，正常定义1个状态表，每个状态函数和该状态表组成一条规则
**************************************************************************** */
TEST_F(datalogState, DataLog_031_020)
{
    // 多个触发函数对应一个状态表，会先计算delta后等所有的function执行完成后再写入到org表中，对于多个规则对应一张表的情乱搞
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "state_031_020";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    /* B(k, s) :- A(k, p), tran1(p, s).
C(K, T) :- B(K, T).
B(k, s) :- A(k, p), tran2(p, s).
C(K, T) :- B(K, T).
B(k, s) :- A(k, p), tran3(p, s).
C(K, T) :- B(K, T).
B(k, s) :- A(k, p), tran4(p, s).
C(K, T) :- B(K, T). 
B(k, s) :- A(k, p), tran5(p, s).
C(K, T) :- B(K, T).
*/

    char tableB[] = "B";
    char tableA[] = "A";
    char tableC[] = "C";

    // 写触发表A
    AW_FUN_Log(LOG_STEP, "1.写所有触发表");
    int32_t countA1[][3] = {{1, 1, 1}, {2, 2, 1}, {3, 3, 1}, {4, 4, 1}, {5, 5, 1}};
    // 写入5条数据
    ret = batchA(g_conn, g_stmt, tableA, countA1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int record = 5;
    // 查询状态表数据
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA1, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 5;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA1, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 删除触发表中各自对应的状态表数据
    AW_FUN_Log(LOG_STEP, "2.删除触发表中各自对应的状态表数据.");
    int32_t countA11[][3] = {{1, 1, -1}, {2, 2, -1}, {3, 3, -1}, {4, 4, -1}, {5, 5, -1}};
    // A写入5条数据   +1 0  0  0  0
    //  0  +1  +1  +1  +1
    //  0  0   0   -1  -1
    //   0  0   0   1    1

    ret = batchA(g_conn, g_stmt, tableA, countA11, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 0;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t countA111[][3] = {{1, 1, 1}};

    // 查询状态表B数据
    record = 1;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA111, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 插入触发表数据是状态表中数据删除
    AW_FUN_Log(LOG_STEP, "3.插入触发表数据使状态表中数据删除.");
    int32_t countA12[][3] = {{1, 2, -1}, {2, 3, -1}, {3, 4, -1}, {4, 5, -1}, {5, 6, -1}};
    // A1写入1条数据
    ret = batchA(g_conn, g_stmt, tableA, countA12, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    record = 5;
    ret = readTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, countA12, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 3;
    ret = readTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countA13[][3] = {{2, 3, 1}, {3, 4, 1}, {4, 5, 1}};
    ret = CompareValue(record, countA13, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = readTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    int32_t countC[][3] = {{2, 3, 5}, {3, 4, 5}, {4, 5, 5}};
    ret = CompareValue(record, countC, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
