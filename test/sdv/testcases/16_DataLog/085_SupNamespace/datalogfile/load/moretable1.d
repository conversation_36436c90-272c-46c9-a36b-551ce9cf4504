%table inp1(a:int8, b:int8, c:int8)
%table inp2(a:int8, b:int8, c:int8)
%table inp3(a:int8, b:int8, c:int8)
%table inp4(a:int8, b:int8, c:int8)
%table inp5(a:int8, b:int8, c:int8)
%table inp6(a:int8, b:int8, c:int8)
%table inp7(a:int8, b:int8, c:int8)
%table inp8(a:int8, b:int8, c:int8)
%table inp9(a:int8, b:int8, c:int8)
%table inp10(a:int8, b:int8, c:int8)
%table inp11(a:int8, b:int8, c:int8)
%table inp12(a:int8, b:int8, c:int8)
%table inp13(a:int8, b:int8, c:int8)
%table inp14(a:int8, b:int8, c:int8)
%table inp15(a:int8, b:int8, c:int8)
%table inp16(a:int8, b:int8, c:int8)
%table inp17(a:int8, b:int8, c:int8)
%table inp18(a:int8, b:int8, c:int8)
%table inp19(a:int8, b:int8, c:int8)
%table inp20(a:int8, b:int8, c:int8)
%table inp21(a:int8, b:int8, c:int8)
%table inp22(a:int8, b:int8, c:int8)
%table inp23(a:int8, b:int8, c:int8)
%table inp24(a:int8, b:int8, c:int8)
%table inp25(a:int8, b:int8, c:int8)
%table inp26(a:int8, b:int8, c:int8)
%table inp27(a:int8, b:int8, c:int8)
%table inp28(a:int8, b:int8, c:int8)
%table inp29(a:int8, b:int8, c:int8)
%table inp30(a:int8, b:int8, c:int8)
%table inp31(a:int8, b:int8, c:int8)
%table inp32(a:int8, b:int8, c:int8)
%table inp33(a:int8, b:int8, c:int8)
%table inp34(a:int8, b:int8, c:int8)
%table inp35(a:int8, b:int8, c:int8)
%table inp36(a:int8, b:int8, c:int8)
%table inp37(a:int8, b:int8, c:int8)
%table inp38(a:int8, b:int8, c:int8)
%table inp39(a:int8, b:int8, c:int8)
%table inp40(a:int8, b:int8, c:int8)
%table inp41(a:int8, b:int8, c:int8)
%table inp42(a:int8, b:int8, c:int8)
%table inp43(a:int8, b:int8, c:int8)
%table inp44(a:int8, b:int8, c:int8)
%table inp45(a:int8, b:int8, c:int8)
%table inp46(a:int8, b:int8, c:int8)
%table inp47(a:int8, b:int8, c:int8)
%table inp48(a:int8, b:int8, c:int8)
%table inp49(a:int8, b:int8, c:int8)
%table inp50(a:int8, b:int8, c:int8)
%table inp51(a:int8, b:int8, c:int8)
%table inp52(a:int8, b:int8, c:int8)
%table inp53(a:int8, b:int8, c:int8)
%table inp54(a:int8, b:int8, c:int8)
%table inp55(a:int8, b:int8, c:int8)
%table inp56(a:int8, b:int8, c:int8)
%table inp57(a:int8, b:int8, c:int8)
%table inp58(a:int8, b:int8, c:int8)
%table inp59(a:int8, b:int8, c:int8)
%table inp60(a:int8, b:int8, c:int8)
%table inp61(a:int8, b:int8, c:int8)
%table inp62(a:int8, b:int8, c:int8)
%table inp63(a:int8, b:int8, c:int8)
%table inp64(a:int8, b:int8, c:int8)
%table inp65(a:int8, b:int8, c:int8)
%table inp66(a:int8, b:int8, c:int8)
%table inp67(a:int8, b:int8, c:int8)
%table inp68(a:int8, b:int8, c:int8)
%table inp69(a:int8, b:int8, c:int8)
%table inp70(a:int8, b:int8, c:int8)
%table inp71(a:int8, b:int8, c:int8)
%table inp72(a:int8, b:int8, c:int8)
%table inp73(a:int8, b:int8, c:int8)
%table inp74(a:int8, b:int8, c:int8)
%table inp75(a:int8, b:int8, c:int8)
%table inp76(a:int8, b:int8, c:int8)
%table inp77(a:int8, b:int8, c:int8)
%table inp78(a:int8, b:int8, c:int8)
%table inp79(a:int8, b:int8, c:int8)
%table inp80(a:int8, b:int8, c:int8)
%table inp81(a:int8, b:int8, c:int8)
%table inp82(a:int8, b:int8, c:int8)
%table inp83(a:int8, b:int8, c:int8)
%table inp84(a:int8, b:int8, c:int8)
%table inp85(a:int8, b:int8, c:int8)
%table inp86(a:int8, b:int8, c:int8)
%table inp87(a:int8, b:int8, c:int8)
%table inp88(a:int8, b:int8, c:int8)
%table inp89(a:int8, b:int8, c:int8)
%table inp90(a:int8, b:int8, c:int8)
%table inp91(a:int8, b:int8, c:int8)
%table inp92(a:int8, b:int8, c:int8)
%table inp93(a:int8, b:int8, c:int8)
%table inp94(a:int8, b:int8, c:int8)
%table inp95(a:int8, b:int8, c:int8)
%table inp96(a:int8, b:int8, c:int8)
%table inp97(a:int8, b:int8, c:int8)
%table inp98(a:int8, b:int8, c:int8)
%table inp99(a:int8, b:int8, c:int8)
%table inp100(a:int8, b:int8, c:int8)
%table inp101(a:int8, b:int8, c:int8)
%table inp102(a:int8, b:int8, c:int8)
%table inp103(a:int8, b:int8, c:int8)
%table inp104(a:int8, b:int8, c:int8)
%table inp105(a:int8, b:int8, c:int8)
%table inp106(a:int8, b:int8, c:int8)
%table inp107(a:int8, b:int8, c:int8)
%table inp108(a:int8, b:int8, c:int8)
%table inp109(a:int8, b:int8, c:int8)
%table inp110(a:int8, b:int8, c:int8)
%table inp111(a:int8, b:int8, c:int8)
%table inp112(a:int8, b:int8, c:int8)
%table inp113(a:int8, b:int8, c:int8)
%table inp114(a:int8, b:int8, c:int8)
%table inp115(a:int8, b:int8, c:int8)
%table inp116(a:int8, b:int8, c:int8)
%table inp117(a:int8, b:int8, c:int8)
%table inp118(a:int8, b:int8, c:int8)
%table inp119(a:int8, b:int8, c:int8)
%table inp120(a:int8, b:int8, c:int8)
%table inp121(a:int8, b:int8, c:int8)
%table inp122(a:int8, b:int8, c:int8)
%table inp123(a:int8, b:int8, c:int8)
%table inp124(a:int8, b:int8, c:int8)
%table inp125(a:int8, b:int8, c:int8)
%table inp126(a:int8, b:int8, c:int8)
%table inp127(a:int8, b:int8, c:int8)
%table inp128(a:int8, b:int8, c:int8)
%table inp129(a:int8, b:int8, c:int8)
%table inp130(a:int8, b:int8, c:int8)
%table inp131(a:int8, b:int8, c:int8)
%table inp132(a:int8, b:int8, c:int8)
%table inp133(a:int8, b:int8, c:int8)
%table inp134(a:int8, b:int8, c:int8)
%table inp135(a:int8, b:int8, c:int8)
%table inp136(a:int8, b:int8, c:int8)
%table inp137(a:int8, b:int8, c:int8)
%table inp138(a:int8, b:int8, c:int8)
%table inp139(a:int8, b:int8, c:int8)
%table inp140(a:int8, b:int8, c:int8)
%table inp141(a:int8, b:int8, c:int8)
%table inp142(a:int8, b:int8, c:int8)
%table inp143(a:int8, b:int8, c:int8)
%table inp144(a:int8, b:int8, c:int8)
%table inp145(a:int8, b:int8, c:int8)
%table inp146(a:int8, b:int8, c:int8)
%table inp147(a:int8, b:int8, c:int8)
%table inp148(a:int8, b:int8, c:int8)
%table inp149(a:int8, b:int8, c:int8)
%table inp150(a:int8, b:int8, c:int8)
%table inp151(a:int8, b:int8, c:int8)
%table inp152(a:int8, b:int8, c:int8)
%table inp153(a:int8, b:int8, c:int8)
%table inp154(a:int8, b:int8, c:int8)
%table inp155(a:int8, b:int8, c:int8)
%table inp156(a:int8, b:int8, c:int8)
%table inp157(a:int8, b:int8, c:int8)
%table inp158(a:int8, b:int8, c:int8)
%table inp159(a:int8, b:int8, c:int8)
%table inp160(a:int8, b:int8, c:int8)
%table inp161(a:int8, b:int8, c:int8)
%table inp162(a:int8, b:int8, c:int8)
%table inp163(a:int8, b:int8, c:int8)
%table inp164(a:int8, b:int8, c:int8)
%table inp165(a:int8, b:int8, c:int8)
%table inp166(a:int8, b:int8, c:int8)
%table inp167(a:int8, b:int8, c:int8)
%table inp168(a:int8, b:int8, c:int8)
%table inp169(a:int8, b:int8, c:int8)
%table inp170(a:int8, b:int8, c:int8)
%table inp171(a:int8, b:int8, c:int8)
%table inp172(a:int8, b:int8, c:int8)
%table inp173(a:int8, b:int8, c:int8)
%table inp174(a:int8, b:int8, c:int8)
%table inp175(a:int8, b:int8, c:int8)
%table inp176(a:int8, b:int8, c:int8)
%table inp177(a:int8, b:int8, c:int8)
%table inp178(a:int8, b:int8, c:int8)
%table inp179(a:int8, b:int8, c:int8)
%table inp180(a:int8, b:int8, c:int8)
%table inp181(a:int8, b:int8, c:int8)
%table inp182(a:int8, b:int8, c:int8)
%table inp183(a:int8, b:int8, c:int8)
%table inp184(a:int8, b:int8, c:int8)
%table inp185(a:int8, b:int8, c:int8)
%table inp186(a:int8, b:int8, c:int8)
%table inp187(a:int8, b:int8, c:int8)
%table inp188(a:int8, b:int8, c:int8)
%table inp189(a:int8, b:int8, c:int8)
%table inp190(a:int8, b:int8, c:int8)
%table inp191(a:int8, b:int8, c:int8)
%table inp192(a:int8, b:int8, c:int8)
%table inp193(a:int8, b:int8, c:int8)
%table inp194(a:int8, b:int8, c:int8)
%table inp195(a:int8, b:int8, c:int8)
%table inp196(a:int8, b:int8, c:int8)
%table inp197(a:int8, b:int8, c:int8)
%table inp198(a:int8, b:int8, c:int8)
%table inp199(a:int8, b:int8, c:int8)
%table inp200(a:int8, b:int8, c:int8)
%table inp201(a:int8, b:int8, c:int8)
%table inp202(a:int8, b:int8, c:int8)
%table inp203(a:int8, b:int8, c:int8)
%table inp204(a:int8, b:int8, c:int8)
%table inp205(a:int8, b:int8, c:int8)
%table inp206(a:int8, b:int8, c:int8)
%table inp207(a:int8, b:int8, c:int8)
%table inp208(a:int8, b:int8, c:int8)
%table inp209(a:int8, b:int8, c:int8)
%table inp210(a:int8, b:int8, c:int8)
%table inp211(a:int8, b:int8, c:int8)
%table inp212(a:int8, b:int8, c:int8)
%table inp213(a:int8, b:int8, c:int8)
%table inp214(a:int8, b:int8, c:int8)
%table inp215(a:int8, b:int8, c:int8)
%table inp216(a:int8, b:int8, c:int8)
%table inp217(a:int8, b:int8, c:int8)
%table inp218(a:int8, b:int8, c:int8)
%table inp219(a:int8, b:int8, c:int8)
%table inp220(a:int8, b:int8, c:int8)
%table inp221(a:int8, b:int8, c:int8)
%table inp222(a:int8, b:int8, c:int8)
%table inp223(a:int8, b:int8, c:int8)
%table inp224(a:int8, b:int8, c:int8)
%table inp225(a:int8, b:int8, c:int8)
%table inp226(a:int8, b:int8, c:int8)
%table inp227(a:int8, b:int8, c:int8)
%table inp228(a:int8, b:int8, c:int8)
%table inp229(a:int8, b:int8, c:int8)
%table inp230(a:int8, b:int8, c:int8)
%table inp231(a:int8, b:int8, c:int8)
%table inp232(a:int8, b:int8, c:int8)
%table inp233(a:int8, b:int8, c:int8)
%table inp234(a:int8, b:int8, c:int8)
%table inp235(a:int8, b:int8, c:int8)
%table inp236(a:int8, b:int8, c:int8)
%table inp237(a:int8, b:int8, c:int8)
%table inp238(a:int8, b:int8, c:int8)
%table inp239(a:int8, b:int8, c:int8)
%table inp240(a:int8, b:int8, c:int8)
%table inp241(a:int8, b:int8, c:int8)
%table inp242(a:int8, b:int8, c:int8)
%table inp243(a:int8, b:int8, c:int8)
%table inp244(a:int8, b:int8, c:int8)
%table inp245(a:int8, b:int8, c:int8)
%table inp246(a:int8, b:int8, c:int8)
%table inp247(a:int8, b:int8, c:int8)
%table inp248(a:int8, b:int8, c:int8)
%table inp249(a:int8, b:int8, c:int8)
%table inp250(a:int8, b:int8, c:int8)
%table inp251(a:int8, b:int8, c:int8)
%table inp252(a:int8, b:int8, c:int8)
%table inp253(a:int8, b:int8, c:int8)
%table inp254(a:int8, b:int8, c:int8)
%table inp255(a:int8, b:int8, c:int8)
%table inp256(a:int8, b:int8, c:int8)
%table inp257(a:int8, b:int8, c:int8)
%table inp258(a:int8, b:int8, c:int8)
%table inp259(a:int8, b:int8, c:int8)
%table inp260(a:int8, b:int8, c:int8)
%table inp261(a:int8, b:int8, c:int8)
%table inp262(a:int8, b:int8, c:int8)
%table inp263(a:int8, b:int8, c:int8)
%table inp264(a:int8, b:int8, c:int8)
%table inp265(a:int8, b:int8, c:int8)
%table inp266(a:int8, b:int8, c:int8)
%table inp267(a:int8, b:int8, c:int8)
%table inp268(a:int8, b:int8, c:int8)
%table inp269(a:int8, b:int8, c:int8)
%table inp270(a:int8, b:int8, c:int8)
%table inp271(a:int8, b:int8, c:int8)
%table inp272(a:int8, b:int8, c:int8)
%table inp273(a:int8, b:int8, c:int8)
%table inp274(a:int8, b:int8, c:int8)
%table inp275(a:int8, b:int8, c:int8)
%table inp276(a:int8, b:int8, c:int8)
%table inp277(a:int8, b:int8, c:int8)
%table inp278(a:int8, b:int8, c:int8)
%table inp279(a:int8, b:int8, c:int8)
%table inp280(a:int8, b:int8, c:int8)
%table inp281(a:int8, b:int8, c:int8)
%table inp282(a:int8, b:int8, c:int8)
%table inp283(a:int8, b:int8, c:int8)
%table inp284(a:int8, b:int8, c:int8)
%table inp285(a:int8, b:int8, c:int8)
%table inp286(a:int8, b:int8, c:int8)
%table inp287(a:int8, b:int8, c:int8)
%table inp288(a:int8, b:int8, c:int8)
%table inp289(a:int8, b:int8, c:int8)
%table inp290(a:int8, b:int8, c:int8)
%table inp291(a:int8, b:int8, c:int8)
%table inp292(a:int8, b:int8, c:int8)
%table inp293(a:int8, b:int8, c:int8)
%table inp294(a:int8, b:int8, c:int8)
%table inp295(a:int8, b:int8, c:int8)
%table inp296(a:int8, b:int8, c:int8)
%table inp297(a:int8, b:int8, c:int8)
%table inp298(a:int8, b:int8, c:int8)
%table inp299(a:int8, b:int8, c:int8)
%table inp300(a:int8, b:int8, c:int8)
%table inp301(a:int8, b:int8, c:int8)
%table inp302(a:int8, b:int8, c:int8)
%table inp303(a:int8, b:int8, c:int8)
%table inp304(a:int8, b:int8, c:int8)
%table inp305(a:int8, b:int8, c:int8)
%table inp306(a:int8, b:int8, c:int8)
%table inp307(a:int8, b:int8, c:int8)
%table inp308(a:int8, b:int8, c:int8)
%table inp309(a:int8, b:int8, c:int8)
%table inp310(a:int8, b:int8, c:int8)
%table inp311(a:int8, b:int8, c:int8)
%table inp312(a:int8, b:int8, c:int8)
%table inp313(a:int8, b:int8, c:int8)
%table inp314(a:int8, b:int8, c:int8)
%table inp315(a:int8, b:int8, c:int8)
%table inp316(a:int8, b:int8, c:int8)
%table inp317(a:int8, b:int8, c:int8)
%table inp318(a:int8, b:int8, c:int8)
%table inp319(a:int8, b:int8, c:int8)
%table inp320(a:int8, b:int8, c:int8)
%table inp321(a:int8, b:int8, c:int8)
%table inp322(a:int8, b:int8, c:int8)
%table inp323(a:int8, b:int8, c:int8)
%table inp324(a:int8, b:int8, c:int8)
%table inp325(a:int8, b:int8, c:int8)
%table inp326(a:int8, b:int8, c:int8)
%table inp327(a:int8, b:int8, c:int8)
%table inp328(a:int8, b:int8, c:int8)
%table inp329(a:int8, b:int8, c:int8)
%table inp330(a:int8, b:int8, c:int8)
%table inp331(a:int8, b:int8, c:int8)
%table inp332(a:int8, b:int8, c:int8)
%table inp333(a:int8, b:int8, c:int8)
%table inp334(a:int8, b:int8, c:int8)
%table inp335(a:int8, b:int8, c:int8)
%table inp336(a:int8, b:int8, c:int8)
%table inp337(a:int8, b:int8, c:int8)
%table inp338(a:int8, b:int8, c:int8)
%table inp339(a:int8, b:int8, c:int8)
%table inp340(a:int8, b:int8, c:int8)
%table inp341(a:int8, b:int8, c:int8)
%table inp342(a:int8, b:int8, c:int8)
%table inp343(a:int8, b:int8, c:int8)
%table inp344(a:int8, b:int8, c:int8)
%table inp345(a:int8, b:int8, c:int8)
%table inp346(a:int8, b:int8, c:int8)
%table inp347(a:int8, b:int8, c:int8)
%table inp348(a:int8, b:int8, c:int8)
%table inp349(a:int8, b:int8, c:int8)
%table inp350(a:int8, b:int8, c:int8)
%table inp351(a:int8, b:int8, c:int8)
%table inp352(a:int8, b:int8, c:int8)
%table inp353(a:int8, b:int8, c:int8)
%table inp354(a:int8, b:int8, c:int8)
%table inp355(a:int8, b:int8, c:int8)
%table inp356(a:int8, b:int8, c:int8)
%table inp357(a:int8, b:int8, c:int8)
%table inp358(a:int8, b:int8, c:int8)
%table inp359(a:int8, b:int8, c:int8)
%table inp360(a:int8, b:int8, c:int8)
%table inp361(a:int8, b:int8, c:int8)
%table inp362(a:int8, b:int8, c:int8)
%table inp363(a:int8, b:int8, c:int8)
%table inp364(a:int8, b:int8, c:int8)
%table inp365(a:int8, b:int8, c:int8)
%table inp366(a:int8, b:int8, c:int8)
%table inp367(a:int8, b:int8, c:int8)
%table inp368(a:int8, b:int8, c:int8)
%table inp369(a:int8, b:int8, c:int8)
%table inp370(a:int8, b:int8, c:int8)
%table inp371(a:int8, b:int8, c:int8)
%table inp372(a:int8, b:int8, c:int8)
%table inp373(a:int8, b:int8, c:int8)
%table inp374(a:int8, b:int8, c:int8)
%table inp375(a:int8, b:int8, c:int8)
%table inp376(a:int8, b:int8, c:int8)
%table inp377(a:int8, b:int8, c:int8)
%table inp378(a:int8, b:int8, c:int8)
%table inp379(a:int8, b:int8, c:int8)
%table inp380(a:int8, b:int8, c:int8)
%table inp381(a:int8, b:int8, c:int8)
%table inp382(a:int8, b:int8, c:int8)
%table inp383(a:int8, b:int8, c:int8)
%table inp384(a:int8, b:int8, c:int8)
%table inp385(a:int8, b:int8, c:int8)
%table inp386(a:int8, b:int8, c:int8)
%table inp387(a:int8, b:int8, c:int8)
%table inp388(a:int8, b:int8, c:int8)
%table inp389(a:int8, b:int8, c:int8)
%table inp390(a:int8, b:int8, c:int8)
%table inp391(a:int8, b:int8, c:int8)
%table inp392(a:int8, b:int8, c:int8)
%table inp393(a:int8, b:int8, c:int8)
%table inp394(a:int8, b:int8, c:int8)
%table inp395(a:int8, b:int8, c:int8)
%table inp396(a:int8, b:int8, c:int8)
%table inp397(a:int8, b:int8, c:int8)
%table inp398(a:int8, b:int8, c:int8)
%table inp399(a:int8, b:int8, c:int8)
%table inp400(a:int8, b:int8, c:int8)
%table inp401(a:int8, b:int8, c:int8)
%table inp402(a:int8, b:int8, c:int8)
%table inp403(a:int8, b:int8, c:int8)
%table inp404(a:int8, b:int8, c:int8)
%table inp405(a:int8, b:int8, c:int8)
%table inp406(a:int8, b:int8, c:int8)
%table inp407(a:int8, b:int8, c:int8)
%table inp408(a:int8, b:int8, c:int8)
%table inp409(a:int8, b:int8, c:int8)
%table inp410(a:int8, b:int8, c:int8)
%table inp411(a:int8, b:int8, c:int8)
%table inp412(a:int8, b:int8, c:int8)
%table inp413(a:int8, b:int8, c:int8)
%table inp414(a:int8, b:int8, c:int8)
%table inp415(a:int8, b:int8, c:int8)
%table inp416(a:int8, b:int8, c:int8)
%table inp417(a:int8, b:int8, c:int8)
%table inp418(a:int8, b:int8, c:int8)
%table inp419(a:int8, b:int8, c:int8)
%table inp420(a:int8, b:int8, c:int8)
%table inp421(a:int8, b:int8, c:int8)
%table inp422(a:int8, b:int8, c:int8)
%table inp423(a:int8, b:int8, c:int8)
%table inp424(a:int8, b:int8, c:int8)
%table inp425(a:int8, b:int8, c:int8)
%table inp426(a:int8, b:int8, c:int8)
%table inp427(a:int8, b:int8, c:int8)
%table inp428(a:int8, b:int8, c:int8)
%table inp429(a:int8, b:int8, c:int8)
%table inp430(a:int8, b:int8, c:int8)
%table inp431(a:int8, b:int8, c:int8)
%table inp432(a:int8, b:int8, c:int8)
%table inp433(a:int8, b:int8, c:int8)
%table inp434(a:int8, b:int8, c:int8)
%table inp435(a:int8, b:int8, c:int8)
%table inp436(a:int8, b:int8, c:int8)
%table inp437(a:int8, b:int8, c:int8)
%table inp438(a:int8, b:int8, c:int8)
%table inp439(a:int8, b:int8, c:int8)
%table inp440(a:int8, b:int8, c:int8)
%table inp441(a:int8, b:int8, c:int8)
%table inp442(a:int8, b:int8, c:int8)
%table inp443(a:int8, b:int8, c:int8)
%table inp444(a:int8, b:int8, c:int8)
%table inp445(a:int8, b:int8, c:int8)
%table inp446(a:int8, b:int8, c:int8)
%table inp447(a:int8, b:int8, c:int8)
%table inp448(a:int8, b:int8, c:int8)
%table inp449(a:int8, b:int8, c:int8)
%table inp450(a:int8, b:int8, c:int8)
%table inp451(a:int8, b:int8, c:int8)
%table inp452(a:int8, b:int8, c:int8)
%table inp453(a:int8, b:int8, c:int8)
%table inp454(a:int8, b:int8, c:int8)
%table inp455(a:int8, b:int8, c:int8)
%table inp456(a:int8, b:int8, c:int8)
%table inp457(a:int8, b:int8, c:int8)
%table inp458(a:int8, b:int8, c:int8)
%table inp459(a:int8, b:int8, c:int8)
%table inp460(a:int8, b:int8, c:int8)
%table inp461(a:int8, b:int8, c:int8)
%table inp462(a:int8, b:int8, c:int8)
%table inp463(a:int8, b:int8, c:int8)
%table inp464(a:int8, b:int8, c:int8)
%table inp465(a:int8, b:int8, c:int8)
%table inp466(a:int8, b:int8, c:int8)
%table inp467(a:int8, b:int8, c:int8)
%table inp468(a:int8, b:int8, c:int8)
%table inp469(a:int8, b:int8, c:int8)
%table inp470(a:int8, b:int8, c:int8)
%table inp471(a:int8, b:int8, c:int8)
%table inp472(a:int8, b:int8, c:int8)
%table inp473(a:int8, b:int8, c:int8)
%table inp474(a:int8, b:int8, c:int8)
%table inp475(a:int8, b:int8, c:int8)
%table inp476(a:int8, b:int8, c:int8)
%table inp477(a:int8, b:int8, c:int8)
%table inp478(a:int8, b:int8, c:int8)
%table inp479(a:int8, b:int8, c:int8)
%table inp480(a:int8, b:int8, c:int8)
%table inp481(a:int8, b:int8, c:int8)
%table inp482(a:int8, b:int8, c:int8)
%table inp483(a:int8, b:int8, c:int8)
%table inp484(a:int8, b:int8, c:int8)
%table inp485(a:int8, b:int8, c:int8)
%table inp486(a:int8, b:int8, c:int8)
%table inp487(a:int8, b:int8, c:int8)
%table inp488(a:int8, b:int8, c:int8)
%table inp489(a:int8, b:int8, c:int8)
%table inp490(a:int8, b:int8, c:int8)
%table inp491(a:int8, b:int8, c:int8)
%table inp492(a:int8, b:int8, c:int8)
%table inp493(a:int8, b:int8, c:int8)
%table inp494(a:int8, b:int8, c:int8)
%table inp495(a:int8, b:int8, c:int8)
%table inp496(a:int8, b:int8, c:int8)
%table inp497(a:int8, b:int8, c:int8)
%table inp498(a:int8, b:int8, c:int8)
%table inp499(a:int8, b:int8, c:int8)
%table inp500(a:int8, b:int8, c:int8)
%table out1(a:int8, b:int8, c:int8)
%table out2(a:int8, b:int8, c:int8)
%table out3(a:int8, b:int8, c:int8)
%table out4(a:int8, b:int8, c:int8)
%table out5(a:int8, b:int8, c:int8)
%table out6(a:int8, b:int8, c:int8)
%table out7(a:int8, b:int8, c:int8)
%table out8(a:int8, b:int8, c:int8)
%table out9(a:int8, b:int8, c:int8)
%table out10(a:int8, b:int8, c:int8)
%table out11(a:int8, b:int8, c:int8)
%table out12(a:int8, b:int8, c:int8)
%table out13(a:int8, b:int8, c:int8)
%table out14(a:int8, b:int8, c:int8)
%table out15(a:int8, b:int8, c:int8)
%table out16(a:int8, b:int8, c:int8)
%table out17(a:int8, b:int8, c:int8)
%table out18(a:int8, b:int8, c:int8)
%table out19(a:int8, b:int8, c:int8)
%table out20(a:int8, b:int8, c:int8)
%table out21(a:int8, b:int8, c:int8)
%table out22(a:int8, b:int8, c:int8)
%table out23(a:int8, b:int8, c:int8)
%table out24(a:int8, b:int8, c:int8)
%table out25(a:int8, b:int8, c:int8)
%table out26(a:int8, b:int8, c:int8)
%table out27(a:int8, b:int8, c:int8)
%table out28(a:int8, b:int8, c:int8)
%table out29(a:int8, b:int8, c:int8)
%table out30(a:int8, b:int8, c:int8)
%table out31(a:int8, b:int8, c:int8)
%table out32(a:int8, b:int8, c:int8)
%table out33(a:int8, b:int8, c:int8)
%table out34(a:int8, b:int8, c:int8)
%table out35(a:int8, b:int8, c:int8)
%table out36(a:int8, b:int8, c:int8)
%table out37(a:int8, b:int8, c:int8)
%table out38(a:int8, b:int8, c:int8)
%table out39(a:int8, b:int8, c:int8)
%table out40(a:int8, b:int8, c:int8)
%table out41(a:int8, b:int8, c:int8)
%table out42(a:int8, b:int8, c:int8)
%table out43(a:int8, b:int8, c:int8)
%table out44(a:int8, b:int8, c:int8)
%table out45(a:int8, b:int8, c:int8)
%table out46(a:int8, b:int8, c:int8)
%table out47(a:int8, b:int8, c:int8)
%table out48(a:int8, b:int8, c:int8)
%table out49(a:int8, b:int8, c:int8)
%table out50(a:int8, b:int8, c:int8)
%table out51(a:int8, b:int8, c:int8)
%table out52(a:int8, b:int8, c:int8)
%table out53(a:int8, b:int8, c:int8)
%table out54(a:int8, b:int8, c:int8)
%table out55(a:int8, b:int8, c:int8)
%table out56(a:int8, b:int8, c:int8)
%table out57(a:int8, b:int8, c:int8)
%table out58(a:int8, b:int8, c:int8)
%table out59(a:int8, b:int8, c:int8)
%table out60(a:int8, b:int8, c:int8)
%table out61(a:int8, b:int8, c:int8)
%table out62(a:int8, b:int8, c:int8)
%table out63(a:int8, b:int8, c:int8)
%table out64(a:int8, b:int8, c:int8)
%table out65(a:int8, b:int8, c:int8)
%table out66(a:int8, b:int8, c:int8)
%table out67(a:int8, b:int8, c:int8)
%table out68(a:int8, b:int8, c:int8)
%table out69(a:int8, b:int8, c:int8)
%table out70(a:int8, b:int8, c:int8)
%table out71(a:int8, b:int8, c:int8)
%table out72(a:int8, b:int8, c:int8)
%table out73(a:int8, b:int8, c:int8)
%table out74(a:int8, b:int8, c:int8)
%table out75(a:int8, b:int8, c:int8)
%table out76(a:int8, b:int8, c:int8)
%table out77(a:int8, b:int8, c:int8)
%table out78(a:int8, b:int8, c:int8)
%table out79(a:int8, b:int8, c:int8)
%table out80(a:int8, b:int8, c:int8)
%table out81(a:int8, b:int8, c:int8)
%table out82(a:int8, b:int8, c:int8)
%table out83(a:int8, b:int8, c:int8)
%table out84(a:int8, b:int8, c:int8)
%table out85(a:int8, b:int8, c:int8)
%table out86(a:int8, b:int8, c:int8)
%table out87(a:int8, b:int8, c:int8)
%table out88(a:int8, b:int8, c:int8)
%table out89(a:int8, b:int8, c:int8)
%table out90(a:int8, b:int8, c:int8)
%table out91(a:int8, b:int8, c:int8)
%table out92(a:int8, b:int8, c:int8)
%table out93(a:int8, b:int8, c:int8)
%table out94(a:int8, b:int8, c:int8)
%table out95(a:int8, b:int8, c:int8)
%table out96(a:int8, b:int8, c:int8)
%table out97(a:int8, b:int8, c:int8)
%table out98(a:int8, b:int8, c:int8)
%table out99(a:int8, b:int8, c:int8)
%table out100(a:int8, b:int8, c:int8)
%table out101(a:int8, b:int8, c:int8)
%table out102(a:int8, b:int8, c:int8)
%table out103(a:int8, b:int8, c:int8)
%table out104(a:int8, b:int8, c:int8)
%table out105(a:int8, b:int8, c:int8)
%table out106(a:int8, b:int8, c:int8)
%table out107(a:int8, b:int8, c:int8)
%table out108(a:int8, b:int8, c:int8)
%table out109(a:int8, b:int8, c:int8)
%table out110(a:int8, b:int8, c:int8)
%table out111(a:int8, b:int8, c:int8)
%table out112(a:int8, b:int8, c:int8)
%table out113(a:int8, b:int8, c:int8)
%table out114(a:int8, b:int8, c:int8)
%table out115(a:int8, b:int8, c:int8)
%table out116(a:int8, b:int8, c:int8)
%table out117(a:int8, b:int8, c:int8)
%table out118(a:int8, b:int8, c:int8)
%table out119(a:int8, b:int8, c:int8)
%table out120(a:int8, b:int8, c:int8)
%table out121(a:int8, b:int8, c:int8)
%table out122(a:int8, b:int8, c:int8)
%table out123(a:int8, b:int8, c:int8)
%table out124(a:int8, b:int8, c:int8)
%table out125(a:int8, b:int8, c:int8)
%table out126(a:int8, b:int8, c:int8)
%table out127(a:int8, b:int8, c:int8)
%table out128(a:int8, b:int8, c:int8)
%table out129(a:int8, b:int8, c:int8)
%table out130(a:int8, b:int8, c:int8)
%table out131(a:int8, b:int8, c:int8)
%table out132(a:int8, b:int8, c:int8)
%table out133(a:int8, b:int8, c:int8)
%table out134(a:int8, b:int8, c:int8)
%table out135(a:int8, b:int8, c:int8)
%table out136(a:int8, b:int8, c:int8)
%table out137(a:int8, b:int8, c:int8)
%table out138(a:int8, b:int8, c:int8)
%table out139(a:int8, b:int8, c:int8)
%table out140(a:int8, b:int8, c:int8)
%table out141(a:int8, b:int8, c:int8)
%table out142(a:int8, b:int8, c:int8)
%table out143(a:int8, b:int8, c:int8)
%table out144(a:int8, b:int8, c:int8)
%table out145(a:int8, b:int8, c:int8)
%table out146(a:int8, b:int8, c:int8)
%table out147(a:int8, b:int8, c:int8)
%table out148(a:int8, b:int8, c:int8)
%table out149(a:int8, b:int8, c:int8)
%table out150(a:int8, b:int8, c:int8)
%table out151(a:int8, b:int8, c:int8)
%table out152(a:int8, b:int8, c:int8)
%table out153(a:int8, b:int8, c:int8)
%table out154(a:int8, b:int8, c:int8)
%table out155(a:int8, b:int8, c:int8)
%table out156(a:int8, b:int8, c:int8)
%table out157(a:int8, b:int8, c:int8)
%table out158(a:int8, b:int8, c:int8)
%table out159(a:int8, b:int8, c:int8)
%table out160(a:int8, b:int8, c:int8)
%table out161(a:int8, b:int8, c:int8)
%table out162(a:int8, b:int8, c:int8)
%table out163(a:int8, b:int8, c:int8)
%table out164(a:int8, b:int8, c:int8)
%table out165(a:int8, b:int8, c:int8)
%table out166(a:int8, b:int8, c:int8)
%table out167(a:int8, b:int8, c:int8)
%table out168(a:int8, b:int8, c:int8)
%table out169(a:int8, b:int8, c:int8)
%table out170(a:int8, b:int8, c:int8)
%table out171(a:int8, b:int8, c:int8)
%table out172(a:int8, b:int8, c:int8)
%table out173(a:int8, b:int8, c:int8)
%table out174(a:int8, b:int8, c:int8)
%table out175(a:int8, b:int8, c:int8)
%table out176(a:int8, b:int8, c:int8)
%table out177(a:int8, b:int8, c:int8)
%table out178(a:int8, b:int8, c:int8)
%table out179(a:int8, b:int8, c:int8)
%table out180(a:int8, b:int8, c:int8)
%table out181(a:int8, b:int8, c:int8)
%table out182(a:int8, b:int8, c:int8)
%table out183(a:int8, b:int8, c:int8)
%table out184(a:int8, b:int8, c:int8)
%table out185(a:int8, b:int8, c:int8)
%table out186(a:int8, b:int8, c:int8)
%table out187(a:int8, b:int8, c:int8)
%table out188(a:int8, b:int8, c:int8)
%table out189(a:int8, b:int8, c:int8)
%table out190(a:int8, b:int8, c:int8)
%table out191(a:int8, b:int8, c:int8)
%table out192(a:int8, b:int8, c:int8)
%table out193(a:int8, b:int8, c:int8)
%table out194(a:int8, b:int8, c:int8)
%table out195(a:int8, b:int8, c:int8)
%table out196(a:int8, b:int8, c:int8)
%table out197(a:int8, b:int8, c:int8)
%table out198(a:int8, b:int8, c:int8)
%table out199(a:int8, b:int8, c:int8)
%table out200(a:int8, b:int8, c:int8)
%table out201(a:int8, b:int8, c:int8)
%table out202(a:int8, b:int8, c:int8)
%table out203(a:int8, b:int8, c:int8)
%table out204(a:int8, b:int8, c:int8)
%table out205(a:int8, b:int8, c:int8)
%table out206(a:int8, b:int8, c:int8)
%table out207(a:int8, b:int8, c:int8)
%table out208(a:int8, b:int8, c:int8)
%table out209(a:int8, b:int8, c:int8)
%table out210(a:int8, b:int8, c:int8)
%table out211(a:int8, b:int8, c:int8)
%table out212(a:int8, b:int8, c:int8)
%table out213(a:int8, b:int8, c:int8)
%table out214(a:int8, b:int8, c:int8)
%table out215(a:int8, b:int8, c:int8)
%table out216(a:int8, b:int8, c:int8)
%table out217(a:int8, b:int8, c:int8)
%table out218(a:int8, b:int8, c:int8)
%table out219(a:int8, b:int8, c:int8)
%table out220(a:int8, b:int8, c:int8)
%table out221(a:int8, b:int8, c:int8)
%table out222(a:int8, b:int8, c:int8)
%table out223(a:int8, b:int8, c:int8)
%table out224(a:int8, b:int8, c:int8)
%table out225(a:int8, b:int8, c:int8)
%table out226(a:int8, b:int8, c:int8)
%table out227(a:int8, b:int8, c:int8)
%table out228(a:int8, b:int8, c:int8)
%table out229(a:int8, b:int8, c:int8)
%table out230(a:int8, b:int8, c:int8)
%table out231(a:int8, b:int8, c:int8)
%table out232(a:int8, b:int8, c:int8)
%table out233(a:int8, b:int8, c:int8)
%table out234(a:int8, b:int8, c:int8)
%table out235(a:int8, b:int8, c:int8)
%table out236(a:int8, b:int8, c:int8)
%table out237(a:int8, b:int8, c:int8)
%table out238(a:int8, b:int8, c:int8)
%table out239(a:int8, b:int8, c:int8)
%table out240(a:int8, b:int8, c:int8)
%table out241(a:int8, b:int8, c:int8)
%table out242(a:int8, b:int8, c:int8)
%table out243(a:int8, b:int8, c:int8)
%table out244(a:int8, b:int8, c:int8)
%table out245(a:int8, b:int8, c:int8)
%table out246(a:int8, b:int8, c:int8)
%table out247(a:int8, b:int8, c:int8)
%table out248(a:int8, b:int8, c:int8)
%table out249(a:int8, b:int8, c:int8)
%table out250(a:int8, b:int8, c:int8)
%table out251(a:int8, b:int8, c:int8)
%table out252(a:int8, b:int8, c:int8)
%table out253(a:int8, b:int8, c:int8)
%table out254(a:int8, b:int8, c:int8)
%table out255(a:int8, b:int8, c:int8)
%table out256(a:int8, b:int8, c:int8)
%table out257(a:int8, b:int8, c:int8)
%table out258(a:int8, b:int8, c:int8)
%table out259(a:int8, b:int8, c:int8)
%table out260(a:int8, b:int8, c:int8)
%table out261(a:int8, b:int8, c:int8)
%table out262(a:int8, b:int8, c:int8)
%table out263(a:int8, b:int8, c:int8)
%table out264(a:int8, b:int8, c:int8)
%table out265(a:int8, b:int8, c:int8)
%table out266(a:int8, b:int8, c:int8)
%table out267(a:int8, b:int8, c:int8)
%table out268(a:int8, b:int8, c:int8)
%table out269(a:int8, b:int8, c:int8)
%table out270(a:int8, b:int8, c:int8)
%table out271(a:int8, b:int8, c:int8)
%table out272(a:int8, b:int8, c:int8)
%table out273(a:int8, b:int8, c:int8)
%table out274(a:int8, b:int8, c:int8)
%table out275(a:int8, b:int8, c:int8)
%table out276(a:int8, b:int8, c:int8)
%table out277(a:int8, b:int8, c:int8)
%table out278(a:int8, b:int8, c:int8)
%table out279(a:int8, b:int8, c:int8)
%table out280(a:int8, b:int8, c:int8)
%table out281(a:int8, b:int8, c:int8)
%table out282(a:int8, b:int8, c:int8)
%table out283(a:int8, b:int8, c:int8)
%table out284(a:int8, b:int8, c:int8)
%table out285(a:int8, b:int8, c:int8)
%table out286(a:int8, b:int8, c:int8)
%table out287(a:int8, b:int8, c:int8)
%table out288(a:int8, b:int8, c:int8)
%table out289(a:int8, b:int8, c:int8)
%table out290(a:int8, b:int8, c:int8)
%table out291(a:int8, b:int8, c:int8)
%table out292(a:int8, b:int8, c:int8)
%table out293(a:int8, b:int8, c:int8)
%table out294(a:int8, b:int8, c:int8)
%table out295(a:int8, b:int8, c:int8)
%table out296(a:int8, b:int8, c:int8)
%table out297(a:int8, b:int8, c:int8)
%table out298(a:int8, b:int8, c:int8)
%table out299(a:int8, b:int8, c:int8)
%table out300(a:int8, b:int8, c:int8)
%table out301(a:int8, b:int8, c:int8)
%table out302(a:int8, b:int8, c:int8)
%table out303(a:int8, b:int8, c:int8)
%table out304(a:int8, b:int8, c:int8)
%table out305(a:int8, b:int8, c:int8)
%table out306(a:int8, b:int8, c:int8)
%table out307(a:int8, b:int8, c:int8)
%table out308(a:int8, b:int8, c:int8)
%table out309(a:int8, b:int8, c:int8)
%table out310(a:int8, b:int8, c:int8)
%table out311(a:int8, b:int8, c:int8)
%table out312(a:int8, b:int8, c:int8)
%table out313(a:int8, b:int8, c:int8)
%table out314(a:int8, b:int8, c:int8)
%table out315(a:int8, b:int8, c:int8)
%table out316(a:int8, b:int8, c:int8)
%table out317(a:int8, b:int8, c:int8)
%table out318(a:int8, b:int8, c:int8)
%table out319(a:int8, b:int8, c:int8)
%table out320(a:int8, b:int8, c:int8)
%table out321(a:int8, b:int8, c:int8)
%table out322(a:int8, b:int8, c:int8)
%table out323(a:int8, b:int8, c:int8)
%table out324(a:int8, b:int8, c:int8)
%table out325(a:int8, b:int8, c:int8)
%table out326(a:int8, b:int8, c:int8)
%table out327(a:int8, b:int8, c:int8)
%table out328(a:int8, b:int8, c:int8)
%table out329(a:int8, b:int8, c:int8)
%table out330(a:int8, b:int8, c:int8)
%table out331(a:int8, b:int8, c:int8)
%table out332(a:int8, b:int8, c:int8)
%table out333(a:int8, b:int8, c:int8)
%table out334(a:int8, b:int8, c:int8)
%table out335(a:int8, b:int8, c:int8)
%table out336(a:int8, b:int8, c:int8)
%table out337(a:int8, b:int8, c:int8)
%table out338(a:int8, b:int8, c:int8)
%table out339(a:int8, b:int8, c:int8)
%table out340(a:int8, b:int8, c:int8)
%table out341(a:int8, b:int8, c:int8)
%table out342(a:int8, b:int8, c:int8)
%table out343(a:int8, b:int8, c:int8)
%table out344(a:int8, b:int8, c:int8)
%table out345(a:int8, b:int8, c:int8)
%table out346(a:int8, b:int8, c:int8)
%table out347(a:int8, b:int8, c:int8)
%table out348(a:int8, b:int8, c:int8)
%table out349(a:int8, b:int8, c:int8)
%table out350(a:int8, b:int8, c:int8)
%table out351(a:int8, b:int8, c:int8)
%table out352(a:int8, b:int8, c:int8)
%table out353(a:int8, b:int8, c:int8)
%table out354(a:int8, b:int8, c:int8)
%table out355(a:int8, b:int8, c:int8)
%table out356(a:int8, b:int8, c:int8)
%table out357(a:int8, b:int8, c:int8)
%table out358(a:int8, b:int8, c:int8)
%table out359(a:int8, b:int8, c:int8)
%table out360(a:int8, b:int8, c:int8)
%table out361(a:int8, b:int8, c:int8)
%table out362(a:int8, b:int8, c:int8)
%table out363(a:int8, b:int8, c:int8)
%table out364(a:int8, b:int8, c:int8)
%table out365(a:int8, b:int8, c:int8)
%table out366(a:int8, b:int8, c:int8)
%table out367(a:int8, b:int8, c:int8)
%table out368(a:int8, b:int8, c:int8)
%table out369(a:int8, b:int8, c:int8)
%table out370(a:int8, b:int8, c:int8)
%table out371(a:int8, b:int8, c:int8)
%table out372(a:int8, b:int8, c:int8)
%table out373(a:int8, b:int8, c:int8)
%table out374(a:int8, b:int8, c:int8)
%table out375(a:int8, b:int8, c:int8)
%table out376(a:int8, b:int8, c:int8)
%table out377(a:int8, b:int8, c:int8)
%table out378(a:int8, b:int8, c:int8)
%table out379(a:int8, b:int8, c:int8)
%table out380(a:int8, b:int8, c:int8)
%table out381(a:int8, b:int8, c:int8)
%table out382(a:int8, b:int8, c:int8)
%table out383(a:int8, b:int8, c:int8)
%table out384(a:int8, b:int8, c:int8)
%table out385(a:int8, b:int8, c:int8)
%table out386(a:int8, b:int8, c:int8)
%table out387(a:int8, b:int8, c:int8)
%table out388(a:int8, b:int8, c:int8)
%table out389(a:int8, b:int8, c:int8)
%table out390(a:int8, b:int8, c:int8)
%table out391(a:int8, b:int8, c:int8)
%table out392(a:int8, b:int8, c:int8)
%table out393(a:int8, b:int8, c:int8)
%table out394(a:int8, b:int8, c:int8)
%table out395(a:int8, b:int8, c:int8)
%table out396(a:int8, b:int8, c:int8)
%table out397(a:int8, b:int8, c:int8)
%table out398(a:int8, b:int8, c:int8)
%table out399(a:int8, b:int8, c:int8)
%table out400(a:int8, b:int8, c:int8)
%table out401(a:int8, b:int8, c:int8)
%table out402(a:int8, b:int8, c:int8)
%table out403(a:int8, b:int8, c:int8)
%table out404(a:int8, b:int8, c:int8)
%table out405(a:int8, b:int8, c:int8)
%table out406(a:int8, b:int8, c:int8)
%table out407(a:int8, b:int8, c:int8)
%table out408(a:int8, b:int8, c:int8)
%table out409(a:int8, b:int8, c:int8)
%table out410(a:int8, b:int8, c:int8)
%table out411(a:int8, b:int8, c:int8)
%table out412(a:int8, b:int8, c:int8)
%table out413(a:int8, b:int8, c:int8)
%table out414(a:int8, b:int8, c:int8)
%table out415(a:int8, b:int8, c:int8)
%table out416(a:int8, b:int8, c:int8)
%table out417(a:int8, b:int8, c:int8)
%table out418(a:int8, b:int8, c:int8)
%table out419(a:int8, b:int8, c:int8)
%table out420(a:int8, b:int8, c:int8)
%table out421(a:int8, b:int8, c:int8)
%table out422(a:int8, b:int8, c:int8)
%table out423(a:int8, b:int8, c:int8)
%table out424(a:int8, b:int8, c:int8)
%table out425(a:int8, b:int8, c:int8)
%table out426(a:int8, b:int8, c:int8)
%table out427(a:int8, b:int8, c:int8)
%table out428(a:int8, b:int8, c:int8)
%table out429(a:int8, b:int8, c:int8)
%table out430(a:int8, b:int8, c:int8)
%table out431(a:int8, b:int8, c:int8)
%table out432(a:int8, b:int8, c:int8)
%table out433(a:int8, b:int8, c:int8)
%table out434(a:int8, b:int8, c:int8)
%table out435(a:int8, b:int8, c:int8)
%table out436(a:int8, b:int8, c:int8)
%table out437(a:int8, b:int8, c:int8)
%table out438(a:int8, b:int8, c:int8)
%table out439(a:int8, b:int8, c:int8)
%table out440(a:int8, b:int8, c:int8)
%table out441(a:int8, b:int8, c:int8)
%table out442(a:int8, b:int8, c:int8)
%table out443(a:int8, b:int8, c:int8)
%table out444(a:int8, b:int8, c:int8)
%table out445(a:int8, b:int8, c:int8)
%table out446(a:int8, b:int8, c:int8)
%table out447(a:int8, b:int8, c:int8)
%table out448(a:int8, b:int8, c:int8)
%table out449(a:int8, b:int8, c:int8)
%table out450(a:int8, b:int8, c:int8)
%table out451(a:int8, b:int8, c:int8)
%table out452(a:int8, b:int8, c:int8)
%table out453(a:int8, b:int8, c:int8)
%table out454(a:int8, b:int8, c:int8)
%table out455(a:int8, b:int8, c:int8)
%table out456(a:int8, b:int8, c:int8)
%table out457(a:int8, b:int8, c:int8)
%table out458(a:int8, b:int8, c:int8)
%table out459(a:int8, b:int8, c:int8)
%table out460(a:int8, b:int8, c:int8)
%table out461(a:int8, b:int8, c:int8)
%table out462(a:int8, b:int8, c:int8)
%table out463(a:int8, b:int8, c:int8)
%table out464(a:int8, b:int8, c:int8)
%table out465(a:int8, b:int8, c:int8)
%table out466(a:int8, b:int8, c:int8)
%table out467(a:int8, b:int8, c:int8)
%table out468(a:int8, b:int8, c:int8)
%table out469(a:int8, b:int8, c:int8)
%table out470(a:int8, b:int8, c:int8)
%table out471(a:int8, b:int8, c:int8)
%table out472(a:int8, b:int8, c:int8)
%table out473(a:int8, b:int8, c:int8)
%table out474(a:int8, b:int8, c:int8)
%table out475(a:int8, b:int8, c:int8)
%table out476(a:int8, b:int8, c:int8)
%table out477(a:int8, b:int8, c:int8)
%table out478(a:int8, b:int8, c:int8)
%table out479(a:int8, b:int8, c:int8)
%table out480(a:int8, b:int8, c:int8)
%table out481(a:int8, b:int8, c:int8)
%table out482(a:int8, b:int8, c:int8)
%table out483(a:int8, b:int8, c:int8)
%table out484(a:int8, b:int8, c:int8)
%table out485(a:int8, b:int8, c:int8)
%table out486(a:int8, b:int8, c:int8)
%table out487(a:int8, b:int8, c:int8)
%table out488(a:int8, b:int8, c:int8)
%table out489(a:int8, b:int8, c:int8)
%table out490(a:int8, b:int8, c:int8)
%table out491(a:int8, b:int8, c:int8)
%table out492(a:int8, b:int8, c:int8)
%table out493(a:int8, b:int8, c:int8)
%table out494(a:int8, b:int8, c:int8)
%table out495(a:int8, b:int8, c:int8)
%table out496(a:int8, b:int8, c:int8)
%table out497(a:int8, b:int8, c:int8)
%table out498(a:int8, b:int8, c:int8)
%table out499(a:int8, b:int8, c:int8)
%table out500(a:int8, b:int8, c:int8)
out1(a, b, c):-inp1(a, b, c).
out2(a, b, c):-inp2(a, b, c).
out3(a, b, c):-inp3(a, b, c).
out4(a, b, c):-inp4(a, b, c).
out5(a, b, c):-inp5(a, b, c).
out6(a, b, c):-inp6(a, b, c).
out7(a, b, c):-inp7(a, b, c).
out8(a, b, c):-inp8(a, b, c).
out9(a, b, c):-inp9(a, b, c).
out10(a, b, c):-inp10(a, b, c).
out11(a, b, c):-inp11(a, b, c).
out12(a, b, c):-inp12(a, b, c).
out13(a, b, c):-inp13(a, b, c).
out14(a, b, c):-inp14(a, b, c).
out15(a, b, c):-inp15(a, b, c).
out16(a, b, c):-inp16(a, b, c).
out17(a, b, c):-inp17(a, b, c).
out18(a, b, c):-inp18(a, b, c).
out19(a, b, c):-inp19(a, b, c).
out20(a, b, c):-inp20(a, b, c).
out21(a, b, c):-inp21(a, b, c).
out22(a, b, c):-inp22(a, b, c).
out23(a, b, c):-inp23(a, b, c).
out24(a, b, c):-inp24(a, b, c).
out25(a, b, c):-inp25(a, b, c).
out26(a, b, c):-inp26(a, b, c).
out27(a, b, c):-inp27(a, b, c).
out28(a, b, c):-inp28(a, b, c).
out29(a, b, c):-inp29(a, b, c).
out30(a, b, c):-inp30(a, b, c).
out31(a, b, c):-inp31(a, b, c).
out32(a, b, c):-inp32(a, b, c).
out33(a, b, c):-inp33(a, b, c).
out34(a, b, c):-inp34(a, b, c).
out35(a, b, c):-inp35(a, b, c).
out36(a, b, c):-inp36(a, b, c).
out37(a, b, c):-inp37(a, b, c).
out38(a, b, c):-inp38(a, b, c).
out39(a, b, c):-inp39(a, b, c).
out40(a, b, c):-inp40(a, b, c).
out41(a, b, c):-inp41(a, b, c).
out42(a, b, c):-inp42(a, b, c).
out43(a, b, c):-inp43(a, b, c).
out44(a, b, c):-inp44(a, b, c).
out45(a, b, c):-inp45(a, b, c).
out46(a, b, c):-inp46(a, b, c).
out47(a, b, c):-inp47(a, b, c).
out48(a, b, c):-inp48(a, b, c).
out49(a, b, c):-inp49(a, b, c).
out50(a, b, c):-inp50(a, b, c).
out51(a, b, c):-inp51(a, b, c).
out52(a, b, c):-inp52(a, b, c).
out53(a, b, c):-inp53(a, b, c).
out54(a, b, c):-inp54(a, b, c).
out55(a, b, c):-inp55(a, b, c).
out56(a, b, c):-inp56(a, b, c).
out57(a, b, c):-inp57(a, b, c).
out58(a, b, c):-inp58(a, b, c).
out59(a, b, c):-inp59(a, b, c).
out60(a, b, c):-inp60(a, b, c).
out61(a, b, c):-inp61(a, b, c).
out62(a, b, c):-inp62(a, b, c).
out63(a, b, c):-inp63(a, b, c).
out64(a, b, c):-inp64(a, b, c).
out65(a, b, c):-inp65(a, b, c).
out66(a, b, c):-inp66(a, b, c).
out67(a, b, c):-inp67(a, b, c).
out68(a, b, c):-inp68(a, b, c).
out69(a, b, c):-inp69(a, b, c).
out70(a, b, c):-inp70(a, b, c).
out71(a, b, c):-inp71(a, b, c).
out72(a, b, c):-inp72(a, b, c).
out73(a, b, c):-inp73(a, b, c).
out74(a, b, c):-inp74(a, b, c).
out75(a, b, c):-inp75(a, b, c).
out76(a, b, c):-inp76(a, b, c).
out77(a, b, c):-inp77(a, b, c).
out78(a, b, c):-inp78(a, b, c).
out79(a, b, c):-inp79(a, b, c).
out80(a, b, c):-inp80(a, b, c).
out81(a, b, c):-inp81(a, b, c).
out82(a, b, c):-inp82(a, b, c).
out83(a, b, c):-inp83(a, b, c).
out84(a, b, c):-inp84(a, b, c).
out85(a, b, c):-inp85(a, b, c).
out86(a, b, c):-inp86(a, b, c).
out87(a, b, c):-inp87(a, b, c).
out88(a, b, c):-inp88(a, b, c).
out89(a, b, c):-inp89(a, b, c).
out90(a, b, c):-inp90(a, b, c).
out91(a, b, c):-inp91(a, b, c).
out92(a, b, c):-inp92(a, b, c).
out93(a, b, c):-inp93(a, b, c).
out94(a, b, c):-inp94(a, b, c).
out95(a, b, c):-inp95(a, b, c).
out96(a, b, c):-inp96(a, b, c).
out97(a, b, c):-inp97(a, b, c).
out98(a, b, c):-inp98(a, b, c).
out99(a, b, c):-inp99(a, b, c).
out100(a, b, c):-inp100(a, b, c).
out101(a, b, c):-inp101(a, b, c).
out102(a, b, c):-inp102(a, b, c).
out103(a, b, c):-inp103(a, b, c).
out104(a, b, c):-inp104(a, b, c).
out105(a, b, c):-inp105(a, b, c).
out106(a, b, c):-inp106(a, b, c).
out107(a, b, c):-inp107(a, b, c).
out108(a, b, c):-inp108(a, b, c).
out109(a, b, c):-inp109(a, b, c).
out110(a, b, c):-inp110(a, b, c).
out111(a, b, c):-inp111(a, b, c).
out112(a, b, c):-inp112(a, b, c).
out113(a, b, c):-inp113(a, b, c).
out114(a, b, c):-inp114(a, b, c).
out115(a, b, c):-inp115(a, b, c).
out116(a, b, c):-inp116(a, b, c).
out117(a, b, c):-inp117(a, b, c).
out118(a, b, c):-inp118(a, b, c).
out119(a, b, c):-inp119(a, b, c).
out120(a, b, c):-inp120(a, b, c).
out121(a, b, c):-inp121(a, b, c).
out122(a, b, c):-inp122(a, b, c).
out123(a, b, c):-inp123(a, b, c).
out124(a, b, c):-inp124(a, b, c).
out125(a, b, c):-inp125(a, b, c).
out126(a, b, c):-inp126(a, b, c).
out127(a, b, c):-inp127(a, b, c).
out128(a, b, c):-inp128(a, b, c).
out129(a, b, c):-inp129(a, b, c).
out130(a, b, c):-inp130(a, b, c).
out131(a, b, c):-inp131(a, b, c).
out132(a, b, c):-inp132(a, b, c).
out133(a, b, c):-inp133(a, b, c).
out134(a, b, c):-inp134(a, b, c).
out135(a, b, c):-inp135(a, b, c).
out136(a, b, c):-inp136(a, b, c).
out137(a, b, c):-inp137(a, b, c).
out138(a, b, c):-inp138(a, b, c).
out139(a, b, c):-inp139(a, b, c).
out140(a, b, c):-inp140(a, b, c).
out141(a, b, c):-inp141(a, b, c).
out142(a, b, c):-inp142(a, b, c).
out143(a, b, c):-inp143(a, b, c).
out144(a, b, c):-inp144(a, b, c).
out145(a, b, c):-inp145(a, b, c).
out146(a, b, c):-inp146(a, b, c).
out147(a, b, c):-inp147(a, b, c).
out148(a, b, c):-inp148(a, b, c).
out149(a, b, c):-inp149(a, b, c).
out150(a, b, c):-inp150(a, b, c).
out151(a, b, c):-inp151(a, b, c).
out152(a, b, c):-inp152(a, b, c).
out153(a, b, c):-inp153(a, b, c).
out154(a, b, c):-inp154(a, b, c).
out155(a, b, c):-inp155(a, b, c).
out156(a, b, c):-inp156(a, b, c).
out157(a, b, c):-inp157(a, b, c).
out158(a, b, c):-inp158(a, b, c).
out159(a, b, c):-inp159(a, b, c).
out160(a, b, c):-inp160(a, b, c).
out161(a, b, c):-inp161(a, b, c).
out162(a, b, c):-inp162(a, b, c).
out163(a, b, c):-inp163(a, b, c).
out164(a, b, c):-inp164(a, b, c).
out165(a, b, c):-inp165(a, b, c).
out166(a, b, c):-inp166(a, b, c).
out167(a, b, c):-inp167(a, b, c).
out168(a, b, c):-inp168(a, b, c).
out169(a, b, c):-inp169(a, b, c).
out170(a, b, c):-inp170(a, b, c).
out171(a, b, c):-inp171(a, b, c).
out172(a, b, c):-inp172(a, b, c).
out173(a, b, c):-inp173(a, b, c).
out174(a, b, c):-inp174(a, b, c).
out175(a, b, c):-inp175(a, b, c).
out176(a, b, c):-inp176(a, b, c).
out177(a, b, c):-inp177(a, b, c).
out178(a, b, c):-inp178(a, b, c).
out179(a, b, c):-inp179(a, b, c).
out180(a, b, c):-inp180(a, b, c).
out181(a, b, c):-inp181(a, b, c).
out182(a, b, c):-inp182(a, b, c).
out183(a, b, c):-inp183(a, b, c).
out184(a, b, c):-inp184(a, b, c).
out185(a, b, c):-inp185(a, b, c).
out186(a, b, c):-inp186(a, b, c).
out187(a, b, c):-inp187(a, b, c).
out188(a, b, c):-inp188(a, b, c).
out189(a, b, c):-inp189(a, b, c).
out190(a, b, c):-inp190(a, b, c).
out191(a, b, c):-inp191(a, b, c).
out192(a, b, c):-inp192(a, b, c).
out193(a, b, c):-inp193(a, b, c).
out194(a, b, c):-inp194(a, b, c).
out195(a, b, c):-inp195(a, b, c).
out196(a, b, c):-inp196(a, b, c).
out197(a, b, c):-inp197(a, b, c).
out198(a, b, c):-inp198(a, b, c).
out199(a, b, c):-inp199(a, b, c).
out200(a, b, c):-inp200(a, b, c).
out201(a, b, c):-inp201(a, b, c).
out202(a, b, c):-inp202(a, b, c).
out203(a, b, c):-inp203(a, b, c).
out204(a, b, c):-inp204(a, b, c).
out205(a, b, c):-inp205(a, b, c).
out206(a, b, c):-inp206(a, b, c).
out207(a, b, c):-inp207(a, b, c).
out208(a, b, c):-inp208(a, b, c).
out209(a, b, c):-inp209(a, b, c).
out210(a, b, c):-inp210(a, b, c).
out211(a, b, c):-inp211(a, b, c).
out212(a, b, c):-inp212(a, b, c).
out213(a, b, c):-inp213(a, b, c).
out214(a, b, c):-inp214(a, b, c).
out215(a, b, c):-inp215(a, b, c).
out216(a, b, c):-inp216(a, b, c).
out217(a, b, c):-inp217(a, b, c).
out218(a, b, c):-inp218(a, b, c).
out219(a, b, c):-inp219(a, b, c).
out220(a, b, c):-inp220(a, b, c).
out221(a, b, c):-inp221(a, b, c).
out222(a, b, c):-inp222(a, b, c).
out223(a, b, c):-inp223(a, b, c).
out224(a, b, c):-inp224(a, b, c).
out225(a, b, c):-inp225(a, b, c).
out226(a, b, c):-inp226(a, b, c).
out227(a, b, c):-inp227(a, b, c).
out228(a, b, c):-inp228(a, b, c).
out229(a, b, c):-inp229(a, b, c).
out230(a, b, c):-inp230(a, b, c).
out231(a, b, c):-inp231(a, b, c).
out232(a, b, c):-inp232(a, b, c).
out233(a, b, c):-inp233(a, b, c).
out234(a, b, c):-inp234(a, b, c).
out235(a, b, c):-inp235(a, b, c).
out236(a, b, c):-inp236(a, b, c).
out237(a, b, c):-inp237(a, b, c).
out238(a, b, c):-inp238(a, b, c).
out239(a, b, c):-inp239(a, b, c).
out240(a, b, c):-inp240(a, b, c).
out241(a, b, c):-inp241(a, b, c).
out242(a, b, c):-inp242(a, b, c).
out243(a, b, c):-inp243(a, b, c).
out244(a, b, c):-inp244(a, b, c).
out245(a, b, c):-inp245(a, b, c).
out246(a, b, c):-inp246(a, b, c).
out247(a, b, c):-inp247(a, b, c).
out248(a, b, c):-inp248(a, b, c).
out249(a, b, c):-inp249(a, b, c).
out250(a, b, c):-inp250(a, b, c).
out251(a, b, c):-inp251(a, b, c).
out252(a, b, c):-inp252(a, b, c).
out253(a, b, c):-inp253(a, b, c).
out254(a, b, c):-inp254(a, b, c).
out255(a, b, c):-inp255(a, b, c).
out256(a, b, c):-inp256(a, b, c).
out257(a, b, c):-inp257(a, b, c).
out258(a, b, c):-inp258(a, b, c).
out259(a, b, c):-inp259(a, b, c).
out260(a, b, c):-inp260(a, b, c).
out261(a, b, c):-inp261(a, b, c).
out262(a, b, c):-inp262(a, b, c).
out263(a, b, c):-inp263(a, b, c).
out264(a, b, c):-inp264(a, b, c).
out265(a, b, c):-inp265(a, b, c).
out266(a, b, c):-inp266(a, b, c).
out267(a, b, c):-inp267(a, b, c).
out268(a, b, c):-inp268(a, b, c).
out269(a, b, c):-inp269(a, b, c).
out270(a, b, c):-inp270(a, b, c).
out271(a, b, c):-inp271(a, b, c).
out272(a, b, c):-inp272(a, b, c).
out273(a, b, c):-inp273(a, b, c).
out274(a, b, c):-inp274(a, b, c).
out275(a, b, c):-inp275(a, b, c).
out276(a, b, c):-inp276(a, b, c).
out277(a, b, c):-inp277(a, b, c).
out278(a, b, c):-inp278(a, b, c).
out279(a, b, c):-inp279(a, b, c).
out280(a, b, c):-inp280(a, b, c).
out281(a, b, c):-inp281(a, b, c).
out282(a, b, c):-inp282(a, b, c).
out283(a, b, c):-inp283(a, b, c).
out284(a, b, c):-inp284(a, b, c).
out285(a, b, c):-inp285(a, b, c).
out286(a, b, c):-inp286(a, b, c).
out287(a, b, c):-inp287(a, b, c).
out288(a, b, c):-inp288(a, b, c).
out289(a, b, c):-inp289(a, b, c).
out290(a, b, c):-inp290(a, b, c).
out291(a, b, c):-inp291(a, b, c).
out292(a, b, c):-inp292(a, b, c).
out293(a, b, c):-inp293(a, b, c).
out294(a, b, c):-inp294(a, b, c).
out295(a, b, c):-inp295(a, b, c).
out296(a, b, c):-inp296(a, b, c).
out297(a, b, c):-inp297(a, b, c).
out298(a, b, c):-inp298(a, b, c).
out299(a, b, c):-inp299(a, b, c).
out300(a, b, c):-inp300(a, b, c).
out301(a, b, c):-inp301(a, b, c).
out302(a, b, c):-inp302(a, b, c).
out303(a, b, c):-inp303(a, b, c).
out304(a, b, c):-inp304(a, b, c).
out305(a, b, c):-inp305(a, b, c).
out306(a, b, c):-inp306(a, b, c).
out307(a, b, c):-inp307(a, b, c).
out308(a, b, c):-inp308(a, b, c).
out309(a, b, c):-inp309(a, b, c).
out310(a, b, c):-inp310(a, b, c).
out311(a, b, c):-inp311(a, b, c).
out312(a, b, c):-inp312(a, b, c).
out313(a, b, c):-inp313(a, b, c).
out314(a, b, c):-inp314(a, b, c).
out315(a, b, c):-inp315(a, b, c).
out316(a, b, c):-inp316(a, b, c).
out317(a, b, c):-inp317(a, b, c).
out318(a, b, c):-inp318(a, b, c).
out319(a, b, c):-inp319(a, b, c).
out320(a, b, c):-inp320(a, b, c).
out321(a, b, c):-inp321(a, b, c).
out322(a, b, c):-inp322(a, b, c).
out323(a, b, c):-inp323(a, b, c).
out324(a, b, c):-inp324(a, b, c).
out325(a, b, c):-inp325(a, b, c).
out326(a, b, c):-inp326(a, b, c).
out327(a, b, c):-inp327(a, b, c).
out328(a, b, c):-inp328(a, b, c).
out329(a, b, c):-inp329(a, b, c).
out330(a, b, c):-inp330(a, b, c).
out331(a, b, c):-inp331(a, b, c).
out332(a, b, c):-inp332(a, b, c).
out333(a, b, c):-inp333(a, b, c).
out334(a, b, c):-inp334(a, b, c).
out335(a, b, c):-inp335(a, b, c).
out336(a, b, c):-inp336(a, b, c).
out337(a, b, c):-inp337(a, b, c).
out338(a, b, c):-inp338(a, b, c).
out339(a, b, c):-inp339(a, b, c).
out340(a, b, c):-inp340(a, b, c).
out341(a, b, c):-inp341(a, b, c).
out342(a, b, c):-inp342(a, b, c).
out343(a, b, c):-inp343(a, b, c).
out344(a, b, c):-inp344(a, b, c).
out345(a, b, c):-inp345(a, b, c).
out346(a, b, c):-inp346(a, b, c).
out347(a, b, c):-inp347(a, b, c).
out348(a, b, c):-inp348(a, b, c).
out349(a, b, c):-inp349(a, b, c).
out350(a, b, c):-inp350(a, b, c).
out351(a, b, c):-inp351(a, b, c).
out352(a, b, c):-inp352(a, b, c).
out353(a, b, c):-inp353(a, b, c).
out354(a, b, c):-inp354(a, b, c).
out355(a, b, c):-inp355(a, b, c).
out356(a, b, c):-inp356(a, b, c).
out357(a, b, c):-inp357(a, b, c).
out358(a, b, c):-inp358(a, b, c).
out359(a, b, c):-inp359(a, b, c).
out360(a, b, c):-inp360(a, b, c).
out361(a, b, c):-inp361(a, b, c).
out362(a, b, c):-inp362(a, b, c).
out363(a, b, c):-inp363(a, b, c).
out364(a, b, c):-inp364(a, b, c).
out365(a, b, c):-inp365(a, b, c).
out366(a, b, c):-inp366(a, b, c).
out367(a, b, c):-inp367(a, b, c).
out368(a, b, c):-inp368(a, b, c).
out369(a, b, c):-inp369(a, b, c).
out370(a, b, c):-inp370(a, b, c).
out371(a, b, c):-inp371(a, b, c).
out372(a, b, c):-inp372(a, b, c).
out373(a, b, c):-inp373(a, b, c).
out374(a, b, c):-inp374(a, b, c).
out375(a, b, c):-inp375(a, b, c).
out376(a, b, c):-inp376(a, b, c).
out377(a, b, c):-inp377(a, b, c).
out378(a, b, c):-inp378(a, b, c).
out379(a, b, c):-inp379(a, b, c).
out380(a, b, c):-inp380(a, b, c).
out381(a, b, c):-inp381(a, b, c).
out382(a, b, c):-inp382(a, b, c).
out383(a, b, c):-inp383(a, b, c).
out384(a, b, c):-inp384(a, b, c).
out385(a, b, c):-inp385(a, b, c).
out386(a, b, c):-inp386(a, b, c).
out387(a, b, c):-inp387(a, b, c).
out388(a, b, c):-inp388(a, b, c).
out389(a, b, c):-inp389(a, b, c).
out390(a, b, c):-inp390(a, b, c).
out391(a, b, c):-inp391(a, b, c).
out392(a, b, c):-inp392(a, b, c).
out393(a, b, c):-inp393(a, b, c).
out394(a, b, c):-inp394(a, b, c).
out395(a, b, c):-inp395(a, b, c).
out396(a, b, c):-inp396(a, b, c).
out397(a, b, c):-inp397(a, b, c).
out398(a, b, c):-inp398(a, b, c).
out399(a, b, c):-inp399(a, b, c).
out400(a, b, c):-inp400(a, b, c).
out401(a, b, c):-inp401(a, b, c).
out402(a, b, c):-inp402(a, b, c).
out403(a, b, c):-inp403(a, b, c).
out404(a, b, c):-inp404(a, b, c).
out405(a, b, c):-inp405(a, b, c).
out406(a, b, c):-inp406(a, b, c).
out407(a, b, c):-inp407(a, b, c).
out408(a, b, c):-inp408(a, b, c).
out409(a, b, c):-inp409(a, b, c).
out410(a, b, c):-inp410(a, b, c).
out411(a, b, c):-inp411(a, b, c).
out412(a, b, c):-inp412(a, b, c).
out413(a, b, c):-inp413(a, b, c).
out414(a, b, c):-inp414(a, b, c).
out415(a, b, c):-inp415(a, b, c).
out416(a, b, c):-inp416(a, b, c).
out417(a, b, c):-inp417(a, b, c).
out418(a, b, c):-inp418(a, b, c).
out419(a, b, c):-inp419(a, b, c).
out420(a, b, c):-inp420(a, b, c).
out421(a, b, c):-inp421(a, b, c).
out422(a, b, c):-inp422(a, b, c).
out423(a, b, c):-inp423(a, b, c).
out424(a, b, c):-inp424(a, b, c).
out425(a, b, c):-inp425(a, b, c).
out426(a, b, c):-inp426(a, b, c).
out427(a, b, c):-inp427(a, b, c).
out428(a, b, c):-inp428(a, b, c).
out429(a, b, c):-inp429(a, b, c).
out430(a, b, c):-inp430(a, b, c).
out431(a, b, c):-inp431(a, b, c).
out432(a, b, c):-inp432(a, b, c).
out433(a, b, c):-inp433(a, b, c).
out434(a, b, c):-inp434(a, b, c).
out435(a, b, c):-inp435(a, b, c).
out436(a, b, c):-inp436(a, b, c).
out437(a, b, c):-inp437(a, b, c).
out438(a, b, c):-inp438(a, b, c).
out439(a, b, c):-inp439(a, b, c).
out440(a, b, c):-inp440(a, b, c).
out441(a, b, c):-inp441(a, b, c).
out442(a, b, c):-inp442(a, b, c).
out443(a, b, c):-inp443(a, b, c).
out444(a, b, c):-inp444(a, b, c).
out445(a, b, c):-inp445(a, b, c).
out446(a, b, c):-inp446(a, b, c).
out447(a, b, c):-inp447(a, b, c).
out448(a, b, c):-inp448(a, b, c).
out449(a, b, c):-inp449(a, b, c).
out450(a, b, c):-inp450(a, b, c).
out451(a, b, c):-inp451(a, b, c).
out452(a, b, c):-inp452(a, b, c).
out453(a, b, c):-inp453(a, b, c).
out454(a, b, c):-inp454(a, b, c).
out455(a, b, c):-inp455(a, b, c).
out456(a, b, c):-inp456(a, b, c).
out457(a, b, c):-inp457(a, b, c).
out458(a, b, c):-inp458(a, b, c).
out459(a, b, c):-inp459(a, b, c).
out460(a, b, c):-inp460(a, b, c).
out461(a, b, c):-inp461(a, b, c).
out462(a, b, c):-inp462(a, b, c).
out463(a, b, c):-inp463(a, b, c).
out464(a, b, c):-inp464(a, b, c).
out465(a, b, c):-inp465(a, b, c).
out466(a, b, c):-inp466(a, b, c).
out467(a, b, c):-inp467(a, b, c).
out468(a, b, c):-inp468(a, b, c).
out469(a, b, c):-inp469(a, b, c).
out470(a, b, c):-inp470(a, b, c).
out471(a, b, c):-inp471(a, b, c).
out472(a, b, c):-inp472(a, b, c).
out473(a, b, c):-inp473(a, b, c).
out474(a, b, c):-inp474(a, b, c).
out475(a, b, c):-inp475(a, b, c).
out476(a, b, c):-inp476(a, b, c).
out477(a, b, c):-inp477(a, b, c).
out478(a, b, c):-inp478(a, b, c).
out479(a, b, c):-inp479(a, b, c).
out480(a, b, c):-inp480(a, b, c).
out481(a, b, c):-inp481(a, b, c).
out482(a, b, c):-inp482(a, b, c).
out483(a, b, c):-inp483(a, b, c).
out484(a, b, c):-inp484(a, b, c).
out485(a, b, c):-inp485(a, b, c).
out486(a, b, c):-inp486(a, b, c).
out487(a, b, c):-inp487(a, b, c).
out488(a, b, c):-inp488(a, b, c).
out489(a, b, c):-inp489(a, b, c).
out490(a, b, c):-inp490(a, b, c).
out491(a, b, c):-inp491(a, b, c).
out492(a, b, c):-inp492(a, b, c).
out493(a, b, c):-inp493(a, b, c).
out494(a, b, c):-inp494(a, b, c).
out495(a, b, c):-inp495(a, b, c).
out496(a, b, c):-inp496(a, b, c).
out497(a, b, c):-inp497(a, b, c).
out498(a, b, c):-inp498(a, b, c).
out499(a, b, c):-inp499(a, b, c).
out500(a, b, c):-inp500(a, b, c).
