/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.1.0 迭代二Datalog支持多个namespace--热升级加载测试/部分执行测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.08.16]
*****************************************************************************/
#include "dtlhotpatch.h"

using namespace std;

class nshotpatch_001_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void nshotpatch_001_test::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建nsp1
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void nshotpatch_001_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    // 删除创建的nsp1
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

class nshotpatch_001_test1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
    }
    static void TearDownTestCase()
    {
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void nshotpatch_001_test1::SetUp()
{
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh \"userPolicyMode=2\" ");
    system("sh ${TEST_HOME}/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
}
void nshotpatch_001_test1::TearDown()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_CHECK_LOG_END();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("rm -rf /root/_datalog_/");
}

// 重启服务，避免受前序用例的影响
class nshotpatch_001_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void nshotpatch_001_test2::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建nsp1
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void nshotpatch_001_test2::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    // 删除创建的nsp1
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}
/* ****************************************************************************
 Description  : 001.gmimport工具实现指定nsp1加载so，指定nsp1升级so，使用-upgrade so名字长度正好为128，指定nsp1降级，预期成功
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchCopySoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackCopySoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 修改patchV2名字长度等于128
    (void)sprintf(tempName, "%s_patchV2", soName);
    offset = strlen(tempName);
    memset_s(tempName + offset, 128 - offset, 'b', 128 - offset);
    offset += 128 - offset;
    tempName[127] = '\0';
    (void)sprintf(patchCopySoName, "%s/%s.so", outputFilePath, tempName);
    (void)SystemSnprintf("rm -rf %s", patchCopySoName);
    (void)SystemSnprintf("cp %s %s", patchSoName, patchCopySoName);
    // 修改rollbackV2名字长度等于128
    (void)sprintf(tempName, "%s_rollbackV2", soName);
    offset = strlen(tempName);
    memset_s(tempName + offset, 128 - offset, 'b', 128 - offset);
    offset += 128 - offset;
    tempName[127] = '\0';
    (void)sprintf(rollbackCopySoName, "%s/%s.so", outputFilePath, tempName);
    (void)SystemSnprintf("rm -rf %s", rollbackCopySoName);
    (void)SystemSnprintf("cp %s %s", rollbackSoName, rollbackCopySoName);
    // 向nsp1命名空间加载升级so
    ret = TestLoadUpgradeSo(patchCopySoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 向nsp1命名空间加载降级so
    ret = TestLoadRollbackSo(rollbackCopySoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.gmimport工具实现不指定namespace(null)加载so，指定nsp1升级so，预期失败
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 向nsp1命名空间加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.gmimport工具实现指定nsp1加载so，指定nsp1升级so，不指定namespace降级，预期失败
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 向nsp1命名空间加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 在public命名空间加载降级so
    ret = TestLoadRollbackSo(rollbackSoName);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.gmimport工具实现指定nsp1不加载so，直接指定nsp1升级so，预期失败
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 向nsp1命名空间加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.gmimport工具实现指定nsp1加载so，指定nsp1升级so，重复指定nsp1升级so，预期失败
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 向nsp1命名空间加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 再加载相同升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.gmimport工具实现加载同一个so至10个不同ns, 并行指定升降级，预期加载成功
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    char nsName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 创建10个namespace
    for (int i = 0; i < 10; i++) {
        (void)sprintf(nsName, "ns%d", i + 1);
        ret = GmcCreateNamespace(g_stmt, nsName, g_user);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, nsName));
    }
    // 线程数
    int threadNum = 10;
    pthread_t thr_arr[threadNum];
    ThreadUpgradeT td[threadNum];
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].patchSoName, 128, patchSoName);
        (void)snprintf(td[i].rollbackSoName, 128, rollbackSoName);
        (void)snprintf(td[i].nsName, 128, "ns%d", i+1);
    }
    // 创建线程
    for (int i = 0; i < threadNum; i++) {
        ret = pthread_create(&thr_arr[i], NULL, ThreadUpgrade, (void *)&td[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 卸载so并删除namesapce
    for (int i = 0; i < 10; i++) {
        (void)sprintf(nsName, "ns%d", i + 1);
        ret = TestUninstallDatalog(soName, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmt, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.多个ns中的规则，进行不同的规则升级（新增表join，新增function，修改规则，修改function实现）, 
 指定namespace(含有表同名命名空间 nsp1)进行热补丁升级，升级成功
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test002";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    char nsName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 创建10个namespace
    for (int i = 0; i < 2; i++) {
        (void)sprintf(nsName, "nsp%d", i + 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, nsName));
    }
    // 线程数
    int threadNum = 2;
    pthread_t thr_arr[threadNum];
    ThreadUpgradeT td[threadNum];
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].patchSoName, 128, patchSoName);
        (void)snprintf(td[i].rollbackSoName, 128, rollbackSoName);
        (void)snprintf(td[i].nsName, 128, "nsp%d", i+1);
    }
    // 创建线程
    for (int i = 0; i < threadNum; i++) {
        ret = pthread_create(&thr_arr[i], NULL, ThreadUpgrade, (void *)&td[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 卸载so并删除namesapce
    for (int i = 0; i < 2; i++) {
        (void)sprintf(nsName, "nsp%d", i + 1);
        ret = TestUninstallDatalog(soName, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.创建nsp1，新增function的热补丁、修改function规则的热补丁、新增表和规则的热补丁混合加载,预期成功
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    char soName2[FILE_PATH] = "test002";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char libName2[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(libName2, "%s/%s.so", outputFilePath, soName2);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV2.so", outputFilePath, soName2);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV2.so", outputFilePath, soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName2, g_namespace1));
    // 加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = TestLoadUpgradeSo(patchSoName2, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 加载降级so
    ret = TestLoadRollbackSo(rollbackSoName, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadRollbackSo(rollbackSoName2, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "SO_NAME: test002", "NAMESPACE_NAME: nsp1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName2, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.gmimport工具实现指定nsp1加载so，原文件包含所有类型的表和udf，指定nsp1升级so，指定nsp1降级，预期成功
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "alltype001";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    
    // 切换namespace为public
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schemafile/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    // 加载原始so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 加载降级so
    ret = TestLoadRollbackSo(rollbackSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "SO_NAME: alltype001", "NAMESPACE_NAME: nsp1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.gmimport工具实现指定nsp1加载so，权限校验，userPolicy设置为2，缺少热升级权限，导入权限，加载升级so失败
**************************************************************************** */
TEST_F(nshotpatch_001_test1, DataLog_085_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建nsp1
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 运行
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    const int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record out2 -ns nsp1");
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");
    // 撤销系统权限
    char SysPrivFile1[128] = "privFile/sysUdfCrete.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s", g_toolPath, SysPrivFile1);
    system(g_command);
    // 加载升级so
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -s %s -c datalog -upgrade %s -ns %s", g_toolPath,
        g_connServer, patchSoName, g_namespace1);
    ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1018004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除创建的nsp1
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.gmimport工具实现指定nsp1加载so，权限校验，userPolicy设置为2，导入升级系统权限，加载成功
**************************************************************************** */
TEST_F(nshotpatch_001_test1, DataLog_085_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    // 导入权限升级so加载成功
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建nsp1
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 运行
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    const int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record out2 -ns nsp1");
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");
    // 加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "SO_NAME: test001", "NAMESPACE_NAME: nsp1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    // 对inp6插入数据
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后的数据
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 6}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn7[recordNum - 3] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 3, 3, 6}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 2, 100}, {1, upVerVal, 2, 2, 100},
        {1, upVerVal, 3, 3, 100}, {1, upVerVal, 4, 4, 100}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn7, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    system("gmsysview record out2 -ns nsp1");
    ret = readRecord(g_conn, g_stmt, "out3", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");

    // 升级后对输入表插入数据
    C3Int8T objIn9[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1 -ns nsp1");
    // 加载降级so
    ret = TestLoadRollbackSo(rollbackSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);

    // 校验加载回滚so之后的数据
    C3Int8T objIn10[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn11[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn11, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1 -ns nsp1");
    C3Int8T objIn12[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    C3Int8T objIn13[recordNum - 3] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 3, 3, 6}};
    C3Int8T objIn14[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn12, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn13, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn14, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert inp1, out3 read complete!!!");
    // 卸载
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除创建的nsp1
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.创建nsp1，升级时，重做数据过程中固定资源表资源不足，升级失败回滚成功
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test003";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 修改datalogUpgradeFetchSize为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载原始so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 运行
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    const int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8C1Int4T objIn2[recordNum - 3] = {{1, 0, 1, 1, 2, 0}, {1, 0, 1, 2, 2, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "rs1", objIn2, recordNum - 3, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rs1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 3, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so，重做失败回滚成功
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "SO_NAME: test003", "NAMESPACE_NAME: nsp1", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "rs1", objIn2, recordNum - 3, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade rs1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 3, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "out1",
        g_connServer, g_namespace1);
    ret = executeCommand(g_command, "out1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.创建nsp1，升级1成功，升级2成功，反复卸载升级2多次（1000次）不加sleep，资源无泄漏
**************************************************************************** */
TEST_F(nshotpatch_001_test2, DataLog_085_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 运行
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    const int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum - 3] = {{1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");

    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, g_namespace1));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 6}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载第二次升级so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, g_namespace1));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn7[recordNum] = {{1, upVerVal1, 1, 1, 2}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 6}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    // 返回卸载rollbackV3.so和加载patchV3.so看内存
#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    
    // 每次重做完成，才往下执行
    pthread_t thr_arr[1];
    // 循环升降级1000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, g_namespace1, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, g_namespace1, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, g_namespace1, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, g_namespace1, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(10);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif
    // 卸载
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.创建nsp1，单条规则表加function数量为15新增function升级,写入数据成功
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test004";
    int ret = 0;
    int upVerVal = 0;
    int upVerVal1 = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 修改datalogUpgradeFetchSize为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载原始so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 运行
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    const int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "SO_NAME: test004", "NAMESPACE_NAME: nsp1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    // 对inp6插入数据
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后的数据
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    // 升级后对输入表插入数据
    C3Int8T objIn7[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
     ret = writeRecord(g_conn, g_stmt, "inp6", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out2 -ns nsp1");
    // 加载降级so
    ret = TestLoadRollbackSo(rollbackSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn8[recordNum - 1] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn9[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn9, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn10[recordNum] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8},
        {1, upVerVal1, 5, 4, 9}, {1, upVerVal1, 6, 6, 12}};
    C3Int8T objIn11[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert data, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn11, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert data, out2 read complete!!!");
    // 卸载so
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.创建nsp1，显示设置block为0，enableDatalogDmlWhenUpgrading为0时，
 patch.d中新增的输入表表与输入表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion会变动
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test005";
    int ret = 0;
    int upVerVal = 0;
    int upVerVal1 = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 加载原始so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, g_namespace1));
    // 运行
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    const int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    ret = TestLoadUpgradeSo(patchSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "SO_NAME: test005", "NAMESPACE_NAME: nsp1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 获取upgradeVersion，升级1次upgradeVersion由0变成1
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    // 对inp6插入数据
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验升级后的数据
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    // 升级后对输入表插入数据
    C3Int8T objIn5[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn5, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn5, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn5, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
     ret = writeRecord(g_conn, g_stmt, "inp6", objIn5, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out2 -ns nsp1");
    // 加载降级so
    ret = TestLoadRollbackSo(rollbackSoName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum + 2] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    // 对输入表插入数据，查询输出表输出符合预期
    C3Int8T objIn7[2] = {{1, 100, 6, 10, 1}, {1, 100, 6, 6, 12}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn7, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn8[recordNum + 4] = {{1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4},
        {1, upVerVal1, 3, 3, 3}, {1, upVerVal1, 4, 4, 8}, {1, upVerVal1, 5, 5, 5}, {1, upVerVal1, 5, 4, 9},
        {1, upVerVal1, 6, 10, 1}, {1, upVerVal1, 6, 6, 12}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert data, out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn8, recordNum + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback insert data, out2 read complete!!!");
    // 卸载so
    ret = TestUninstallDatalog(soName, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 可能存在后台线程数满，导致加载升级so报错
/* ****************************************************************************
 Description  : 016.加载同一个so至不同namespace 10个(nsp1. nsp2), 插入数据执行36s，并行指定升降级，日志内无12002错误码，预期重做成功
**************************************************************************** */
TEST_F(nshotpatch_001_test, DataLog_085_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    char errorCode03[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char outputFilePath[FILE_PATH] = "./datalogfile/upgrade";
    char soName[FILE_PATH] = "test006";
    int ret = 0;
    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char tempName[128] = {0};
    char nsName[128] = {0};
    int offset = 0;
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    // 创建10个namespace
    for (int i = 0; i < 10; i++) {
        (void)sprintf(nsName, "ns%d", i + 1);
        ret = GmcCreateNamespace(g_stmt, nsName, g_user);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName, nsName));
    }
    // 线程数
    int threadNum = 10;
    pthread_t thr_arr[threadNum];
    ThreadUpgradeT td[threadNum];
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(td[i].patchSoName, 128, patchSoName);
        (void)snprintf(td[i].rollbackSoName, 128, rollbackSoName);
        (void)snprintf(td[i].nsName, 128, "ns%d", i+1);
    }
    // 创建线程执行dml操作和升降级so
    for (int i = 0; i < threadNum; i++) {
        ret = pthread_create(&thr_arr[i], NULL, ThreadNsDMLAndUpgrade, (void *)&td[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 卸载so并删除namesapce
    for (int i = 0; i < 10; i++) {
        (void)sprintf(nsName, "ns%d", i + 1);
        ret = TestUninstallDatalog(soName, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmt, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
