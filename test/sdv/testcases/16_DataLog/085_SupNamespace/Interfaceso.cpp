/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: Interfaceso.cpp
 * Description: datalog Namespace
 * Author: qibingsen 00880292
 * Create: 2024-08-12
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "supportnamespace.h"

int ret;
char filepath[MAX_NAME_LENGTH] = "./datalogfile/load/normal.so";
char wrongfilepath[MAX_NAME_LENGTH] = "./datalogfile/load/norma.so";
char soname[MAX_NAME_LENGTH] = "normal";
char wrongsoname[MAX_NAME_LENGTH] = "norma";
class SupNamespaceDatalogTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SupNamespaceDatalogTest::SetUpTestCase()
{
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void SupNamespaceDatalogTest::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupNamespaceDatalogTest::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void SupNamespaceDatalogTest::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    GmcUseNamespace(g_stmt, g_namespace1);
    GmcDropNamespace(g_stmt, g_namespace1);
    GmcUseNamespace(g_stmt, g_namespace2);
    GmcDropNamespace(g_stmt, g_namespace2);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    
    printf("\n======================TEST:END========================\n");
}

//001. 接口实现加载时不指定namespace(null), 卸载时不指定namespace(null), 预期成功 
TEST_F(SupNamespaceDatalogTest, DataLog_085_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class SupNamespaceDatalogTest1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SupNamespaceDatalogTest1::SetUpTestCase()
{
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void SupNamespaceDatalogTest1::TearDownTestCase()
{
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupNamespaceDatalogTest1::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void SupNamespaceDatalogTest1::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("\n======================TEST:END========================\n");
}

//001. 接口实现加载时不指定namespace(null), 卸载时不指定namespace(null), 预期成功 
TEST_F(SupNamespaceDatalogTest1, DataLog_085_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//105.不创建心跳，创建两个命名空间，使用接口对一个so分别加载至两个命名空间，对两个inp1输入表进行prepare操作，使用接口卸载两个命名空间的so，
//校验CATA_VERTEX_LABEL_INFO视图，预期只有一个inp1表信息。DTS2025021600667
TEST_F(SupNamespaceDatalogTest1, DataLog_085_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(6);
    char gmsysviewcmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmsysviewcmd, MAX_NAME_LENGTH, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=inp1 | grep index | wc -l");
    ret = executeCommand(gmsysviewcmd, "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//106.创建心跳，创建两个命名空间，使用工具对一个so分别加载至两个命名空间，对两个inp1输入表进行prepare操作，使用工具卸载两个命名空间的so，
// 校验CATA_VERTEX_LABEL_INFO视图，预期只有一个inp1表信息。DTS2025021600667
TEST_F(SupNamespaceDatalogTest, DataLog_085_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(6);
    char gmsysviewcmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmsysviewcmd, MAX_NAME_LENGTH, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=inp1 | grep index | wc -l");
    ret = executeCommand(gmsysviewcmd, "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


//002. 接口实现加载时指定namespace(public), 卸载时不指定namespace(null), 预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//003. 接口实现加载时不指定namespace(null), 卸载时指定namespace(public), 预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//004. 接口实现加载时指定namespace(nsp1)(未创建),  预期返回失败  返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);  //  返回错误码
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//005. 接口实现加载时指定namespace(nsp1), 卸载时指定namespace(nsp1), 预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//006. 接口实现加载时指定namespace(nsp1), 卸载时so文件名错误, 预期返回失败  返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, wrongsoname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);  //  返回错误码
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//007. 接口实现加载时指定namespace(nsp1), 卸载时指定namespace(public), 预期返回失败  返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);  //  返回错误码
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//008. 接口实现加载时指定namespace(nsp1), 卸载时指定namespace(nsp2), 预期返回失败   返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);  //  返回错误码
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//009. 接口实现加载时指定namespace(nsp1), 卸载时不指定namespace(null), 预期返回失败   返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);  //  返回错误码
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//010. 接口实现加载时指定namespace(nsp1), 使用时更换namespace(public), 卸载时不指定namespace(null), 预期返回失败  返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);  //  返回错误码
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//011. 接口实现重复加载同一个so至相同namespace(nsp1), 第二次加载预期返回失败  返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);  //  返回错误码
    ret = UnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//012. 接口实现加载同一个so至不同namespace(nsp1. nsp2), 预期加载成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//013. 接口实现创建Datalog结构体时GmcImportDatalogOptionsCreate时为null，加载so文件，预期失败  返回错误码
TEST_F(SupNamespaceDatalogTest, DataLog_085_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcImportDatalogOptionsT *options = NULL;
    int ret = GmcImportDatalogOptionsCreate(&options);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcImportDatalogWithOption(g_stmt, options);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret); //  返回错误码
    GmcImportDatalogOptionsDestroy(options);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//014. 接口实现加载so时GmcImportDatalogOptionsSetFilePath传入null, 预期失败  返回错误码
TEST_F(SupNamespaceDatalogTest, DataLog_085_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, NULL, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret); //  返回错误码
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//015. 接口实现加载so时指定GmcImportDatalogOptionsSetFilePath(so不存在), 预期失败   返回错误码
TEST_F(SupNamespaceDatalogTest, DataLog_085_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_FILE_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, wrongfilepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret); //  返回错误码
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//016.接口实现创建Datalog结构体时GmcImportDatalogOptionsSetNamespaceName时为null，预期失败  返回错误码
TEST_F(SupNamespaceDatalogTest, DataLog_085_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcImportDatalogOptionsT *options = NULL;
    int ret = GmcImportDatalogOptionsCreate(&options);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcImportDatalogOptionsSetNamespaceName(options, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);  //  返回错误码
    GmcImportDatalogOptionsDestroy(options);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//017. 接口实现加载时指定namespace(nsp1), 卸载so时GmcUnimportDatalogOptionsCreate入参为null, 卸载预期失败   返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnimportDatalogOptionsT *options = NULL;
    int ret = GmcUnimportDatalogOptionsCreate(&options);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnimportDatalogWithOption(g_stmt, options);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret); //  返回错误码
    GmcUnimportDatalogOptionsDestroy(options);
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//018. 接口实现卸载so时GmcUnimportDatalogOptionsSetSoName时为null，卸载预期失败  返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, NULL, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret); //  返回错误码
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//019. 接口实现卸载so时GmcUnimportDatalogOptionsSetSoName(soname不存在)，卸载预期失败  返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, wrongsoname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret); //  返回错误码
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//020. 接口实现卸载前创建Datalog结构体时GmcUnimportDatalogOptionsSetNamespaceName时为null，卸载预期失败 返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnimportDatalogOptionsT *options = NULL;
    int ret = GmcUnimportDatalogOptionsCreate(&options);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnimportDatalogOptionsSetNamespaceName(options, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);  //  返回错误码
    GmcUnimportDatalogOptionsDestroy(options);
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//021. GmcCreateNamespaceWithCfg创建入参结构体命名空间nsp1, 用户为null, 表空间default,接口实现加载时指定namespace(nsp1), 卸载时指定namespace(nsp1), 预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcNspCfgT cfg = {};
    cfg.tablespaceName = NULL;
    cfg.namespaceName = g_namespace1;
    cfg.userName = NULL;
    cfg.trxCfg = (GmcTrxCfgT){GMC_DEFAULT_TRX, GMC_TX_ISOLATION_DEFAULT};
    ret = GmcCreateNamespaceWithCfg(g_stmt, &cfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//022. GmcCreateNamespaceWithCfg创建入参结构体命名空间nsp1, 用户为null, 表空间未创建, 预期创建失败  返回错误码
TEST_F(SupNamespaceDatalogTest, DataLog_085_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    GmcNspCfgT cfg = {};
    cfg.tablespaceName = "tsp1";
    cfg.namespaceName = g_namespace1;
    cfg.userName = NULL;
    cfg.trxCfg = (GmcTrxCfgT){GMC_DEFAULT_TRX, GMC_TX_ISOLATION_DEFAULT};
    ret = GmcCreateNamespaceWithCfg(g_stmt, &cfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret); //  返回错误码
    AW_FUN_Log(LOG_STEP, "test end.");
}

//023. GmcCreateNamespaceWithCfg创建入参结构体命名空间nsp1, 用户为null, 表空间已创建,接口实现加载时指定namespace(nsp1), 卸载时指定namespace(nsp1), 预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = g_tablespace1;
    tspCfg.initSize = 4;
    tspCfg.stepSize = 4;
    tspCfg.maxSize = 8;
    Status ret = GmcCreateTablespace(g_stmt, &tspCfg);
    GmcNspCfgT cfg = {};
    cfg.tablespaceName = g_tablespace1;
    cfg.namespaceName = g_namespace1;
    cfg.userName = NULL;
    cfg.trxCfg = (GmcTrxCfgT){GMC_DEFAULT_TRX, GMC_TX_ISOLATION_DEFAULT};
    ret = GmcCreateNamespaceWithCfg(g_stmt, &cfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmt, g_tablespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//024. GmcCreateNamespaceWithCfg创建入参结构体命名空间public, 用户为null, 表空间已创建,创建命名空间，预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = g_tablespace1;
    tspCfg.initSize = 4;
    tspCfg.stepSize = 4;
    tspCfg.maxSize = 8;
    Status ret = GmcCreateTablespace(g_stmt, &tspCfg);
    GmcNspCfgT cfg = {};
    cfg.tablespaceName = g_tablespace1;
    cfg.namespaceName = g_namespace;
    cfg.userName = NULL;
    cfg.trxCfg = (GmcTrxCfgT){GMC_DEFAULT_TRX, GMC_TX_ISOLATION_DEFAULT};
    ret = GmcCreateNamespaceWithCfg(g_stmt, &cfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret); //  返回错误码
    ret = GmcDropTablespace(g_stmt, g_tablespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//025. GmcCreateNamespaceWithCfg创建入参结构体为null, 创建命名空间预期失败
TEST_F(SupNamespaceDatalogTest, DataLog_085_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcNspCfgT *cfg = NULL;
    ret = GmcCreateNamespaceWithCfg(g_stmt, cfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret); //  返回错误码
    AW_FUN_Log(LOG_STEP, "test end.");
}

//026. 接口实现加载同一个so至namespace数量上限(64个), 预期加载成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char nsName[MAX_NAME_LENGTH] = {0};
    int32_t threadNum;
#if defined RUN_INDEPENDENT
    threadNum = MAX_NAMESPACE_CNT;
#else
    threadNum = MAX_NAMESPACE_CNT - 2;
#endif
    for(int i = 1; i < threadNum + 1; i++){
        (void)snprintf(nsName, MAX_NAME_LENGTH, "nsp%d", i);
        ret = GmcCreateNamespace(g_stmt, nsName, g_user);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(nsName, MAX_NAME_LENGTH, "nsp65");
    ret = GmcCreateNamespace(g_stmt, nsName, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    for(int i = 1; i < threadNum + 1; i++){
        (void)snprintf(nsName, MAX_NAME_LENGTH, "nsp%d", i);
        ret = GmcUseNamespace(g_stmt, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = LoadExternalVertex();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = LoadDatalog(g_stmt, filepath, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for(int i = 1; i < threadNum + 1; i++){
        (void)snprintf(nsName, MAX_NAME_LENGTH, "nsp%d", i);
        ret = TestUninstallDatalog(soname, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = DropExternalVertex();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmt, nsName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

//027. 接口实现重复从相同namespace(nsp1)卸载同一个so, 第二次卸载预期返回失败 返回错误码？
TEST_F(SupNamespaceDatalogTest, DataLog_085_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret); //  返回错误码
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//028. 接口实现从不同namespace(nsp1. nsp2)卸载同一个so, 预期卸载成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//029. 接口实现加载时指定namespace(nsp1), 卸载时指定namespace(Nsp1), 预期执行失败
TEST_F(SupNamespaceDatalogTest, DataLog_085_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char wrongNamespace1[32] = "Nsp1";
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, wrongNamespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);
    ret = TestUninstallDatalog(soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//030. 接口实现创建namespace长度128位, 含有正常字符, 预期创建成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char longnamespace[128] = {0};
    for (int i = 0; i < 127; i++) {
        strcat(longnamespace, "k");
    }
    ret = GmcCreateNamespace(g_stmt, longnamespace, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, longnamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, filepath, longnamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UnLoadDatalog(g_stmt, soname, longnamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, longnamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//031. 编写带有init/uninit的tbm表，接口实现加载时指定namespace(nsp1)，init通过GmUdfGetNamespaceName接口获取namesapce并校验，预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_031)
{
    char originTxt[MAX_NAME_LENGTH] = "./namespace.txt";
    char generateTxt[MAX_NAME_LENGTH] = {0};
    char rpath[] = {'/','\0'};
    (void)sprintf(generateTxt, "%croot/_datalog_/TbmRunLog.txt",rpath[0]);
    char tbmfilepath[MAX_NAME_LENGTH] = "./datalogfile/load/tbmtable.so";
    char tbmsoname[MAX_NAME_LENGTH] = "tbmtable";
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, tbmfilepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FILE *file = fopen(originTxt, "w+");
    (void)fprintf(file, "%s", g_namespace1);
    (void)fclose(file);
    ret = CheckFile(generateTxt, originTxt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(tbmsoname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RmTxt(originTxt);
    RmTxt(generateTxt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//032. 编写带有init/uninit的tbm表，接口实现加载时不指定namespace(null)，init通过GmUdfGetNamespaceName接口获取namesapce并校验，预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_032)
{
    char originTxt[MAX_NAME_LENGTH] = "./namespace.txt";
    char generateTxt[MAX_NAME_LENGTH] = {0};
    char rpath[] = {'/','\0'};
    (void)sprintf(generateTxt, "%croot/_datalog_/TbmRunLog.txt",rpath[0]);
    char tbmfilepath[MAX_NAME_LENGTH] = "./datalogfile/load/tbmtable.so";
    char tbmsoname[MAX_NAME_LENGTH] = "tbmtable";
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, tbmfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FILE *file = fopen(originTxt, "w+");
    (void)fprintf(file, "%s", g_namespace);
    (void)fclose(file);
    ret = CheckFile(generateTxt, originTxt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(tbmsoname, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RmTxt(originTxt);
    RmTxt(generateTxt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//033. 编写带有init/uninit的tbm表，接口实现加载时指定namespace(public)，init通过GmUdfGetNamespaceName接口获取namesapce并校验，预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_033)
{
    char originTxt[MAX_NAME_LENGTH] = "./namespace.txt";
    char generateTxt[MAX_NAME_LENGTH] = {0};
    char rpath[] = {'/','\0'};
    (void)sprintf(generateTxt, "%croot/_datalog_/TbmRunLog.txt",rpath[0]);
    char tbmfilepath[MAX_NAME_LENGTH] = "./datalogfile/load/tbmtable.so";
    char tbmsoname[MAX_NAME_LENGTH] = "tbmtable";
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, tbmfilepath, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FILE *file = fopen(originTxt, "w+");
    (void)fprintf(file, "%s", g_namespace);
    (void)fclose(file);
    ret = CheckFile(generateTxt, originTxt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(tbmsoname, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RmTxt(originTxt);
    RmTxt(generateTxt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//034. 编写带有init/uninit的tbm表，接口实现加载时指定namespace(public)，init通过GmUdfGetNamespaceName接口入参为nulll获取namesapce并校验，预期失败
TEST_F(SupNamespaceDatalogTest, DataLog_085_034)
{
    char originTxt[MAX_NAME_LENGTH] = "./namespace.txt";
    char generateTxt[MAX_NAME_LENGTH] = {0};
    char rpath[] = {'/','\0'};
    (void)sprintf(generateTxt, "%croot/_datalog_/TbmRunLog.txt",rpath[0]);
    char tbmfilepath[MAX_NAME_LENGTH] = "./datalogfile/load/tbmtable1.so";
    char tbmsoname[MAX_NAME_LENGTH] = "tbmtable1";
    AW_FUN_Log(LOG_STEP, "test start.");
    char wrongNamespace1[32] = "Nsp1";
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = {0};
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, tbmfilepath, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FILE *file = fopen(originTxt, "w+");
    (void)fprintf(file, "%s", g_namespace);
    (void)fclose(file);
    ret = CheckFile(generateTxt, originTxt);
    AW_MACRO_EXPECT_EQ_INT(FAILED, ret);
    ret = TestUninstallDatalog(tbmsoname, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RmTxt(originTxt);
    RmTxt(generateTxt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//035. 编写udf 及其他表，接口实现加载时指定namespace(public)，通过GmUdfGetNamespaceName接口获取namesapce并校验，预期成功
TEST_F(SupNamespaceDatalogTest, DataLog_085_035)
{
    char originTxt[MAX_NAME_LENGTH] = "./namespace.txt";
    char generateTxt[MAX_NAME_LENGTH] = {0};
    char rpath[] = {'/','\0'};
    (void)sprintf(generateTxt, "%croot/_datalog_/TbmRunLog.txt",rpath[0]);
    char tbmfilepath[MAX_NAME_LENGTH] = "./datalogfile/load/normal1.so";
    char tbmsoname[MAX_NAME_LENGTH] = "normal1";
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadDatalog(g_stmt, tbmfilepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FILE *file = fopen(originTxt, "w+");
    (void)fprintf(file, "%s", g_namespace1);
    (void)fclose(file);
    ret = CheckFile(generateTxt, originTxt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(tbmsoname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RmTxt(originTxt);
    RmTxt(generateTxt);
    AW_FUN_Log(LOG_STEP, "test end.");
}


