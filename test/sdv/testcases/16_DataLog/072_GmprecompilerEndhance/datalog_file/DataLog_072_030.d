%version v0.0.0
namespace ns1{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b))}
%table out(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b))}
out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table inp1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r1 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r2 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r3 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r4 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r5 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r6 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r7 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r8 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r9 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r10 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r11 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r12 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r13 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r14 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r15 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r16 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r17 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r18 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r19 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r20 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp21(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r21 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp21(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp22(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r22 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp22(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp23(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r23 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp23(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp24(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r24 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp24(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp25(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r25 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp25(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp26(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r26 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp26(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp27(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r27 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp27(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp28(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r28 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp28(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp29(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r29 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp29(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp30(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r30 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp30(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp31(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r31 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp31(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp32(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r32 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp32(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp33(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r33 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp33(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp34(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r34 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp34(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp35(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r35 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp35(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp36(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r36 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp36(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp37(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r37 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp37(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp38(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r38 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp38(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp39(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r39 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp39(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp40(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r40 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp40(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp41(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r41 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp41(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp42(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r42 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp42(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp43(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r43 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp43(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp44(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r44 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp44(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp45(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r45 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp45(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp46(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r46 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp46(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp47(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r47 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp47(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp48(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r48 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp48(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp49(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r49 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp49(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp50(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r50 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp50(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp51(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r51 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp51(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp52(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r52 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp52(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp53(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r53 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp53(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp54(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r54 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp54(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp55(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r55 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp55(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp56(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r56 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp56(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp57(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r57 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp57(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp58(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r58 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp58(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp59(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r59 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp59(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp60(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r60 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp60(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp61(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r61 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp61(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp62(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r62 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp62(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp63(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r63 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp63(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp64(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r64 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp64(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp65(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r65 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp65(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp66(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r66 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp66(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp67(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r67 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp67(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp68(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r68 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp68(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp69(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r69 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp69(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp70(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r70 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp70(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp71(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r71 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp71(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp72(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r72 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp72(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp73(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r73 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp73(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp74(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r74 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp74(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp75(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r75 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp75(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp76(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r76 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp76(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp77(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r77 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp77(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp78(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r78 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp78(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp79(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r79 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp79(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp80(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r80 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp80(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp81(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r81 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp81(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp82(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r82 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp82(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp83(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r83 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp83(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp84(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r84 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp84(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp85(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r85 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp85(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp86(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r86 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp86(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp87(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r87 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp87(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp88(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r88 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp88(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp89(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r89 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp89(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp90(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r90 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp90(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp91(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r91 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp91(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp92(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r92 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp92(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp93(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r93 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp93(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp94(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r94 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp94(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp95(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r95 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp95(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp96(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r96 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp96(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp97(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r97 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp97(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp98(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r98 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp98(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp99(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r99 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp99(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp100(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r100 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp101(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r101 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp101(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp102(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r102 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp102(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp103(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r103 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp103(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp104(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r104 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp104(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp105(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r105 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp105(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp106(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r106 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp106(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp107(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r107 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp107(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp108(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r108 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp108(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp109(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r109 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp109(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp110(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r110 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp111(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r111 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp111(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp112(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r112 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp112(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp113(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r113 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp113(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp114(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r114 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp114(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp115(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r115 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp115(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp116(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r116 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp116(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp117(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r117 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp117(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp118(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r118 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp118(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp119(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r119 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp119(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp120(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r120 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp121(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r121 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp121(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp122(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r122 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp122(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp123(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r123 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp123(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp124(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r124 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp124(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp125(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r125 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp125(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp126(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r126 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp126(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp127(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r127 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp127(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp128(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r128 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp128(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp129(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r129 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp129(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp130(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r130 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp131(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r131 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp131(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp132(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r132 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp132(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp133(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r133 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp133(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp134(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r134 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp134(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp135(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r135 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp135(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp136(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r136 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp136(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp137(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r137 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp137(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp138(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r138 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp138(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp139(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r139 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp139(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp140(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r140 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp141(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r141 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp141(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp142(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r142 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp142(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp143(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r143 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp143(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp144(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r144 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp144(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp145(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r145 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp145(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp146(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r146 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp146(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp147(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r147 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp147(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp148(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r148 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp148(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp149(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r149 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp149(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp150(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r150 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp151(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r151 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp151(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp152(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r152 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp152(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp153(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r153 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp153(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp154(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r154 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp154(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp155(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r155 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp155(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp156(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r156 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp156(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp157(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r157 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp157(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp158(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r158 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp158(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp159(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r159 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp159(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp160(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r160 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp161(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r161 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp161(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp162(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r162 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp162(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp163(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r163 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp163(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp164(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r164 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp164(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp165(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r165 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp165(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp166(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r166 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp166(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp167(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r167 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp167(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp168(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r168 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp168(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp169(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r169 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp169(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp170(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r170 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp171(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r171 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp171(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp172(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r172 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp172(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp173(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r173 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp173(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp174(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r174 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp174(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp175(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r175 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp175(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp176(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r176 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp176(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp177(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r177 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp177(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp178(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r178 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp178(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp179(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r179 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp179(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp180(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r180 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp181(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r181 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp181(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp182(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r182 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp182(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp183(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r183 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp183(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp184(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r184 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp184(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp185(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r185 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp185(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp186(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r186 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp186(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp187(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r187 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp187(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp188(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r188 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp188(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp189(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r189 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp189(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp190(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r190 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp191(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r191 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp191(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp192(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r192 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp192(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp193(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r193 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp193(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp194(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r194 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp194(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp195(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r195 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp195(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp196(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r196 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp196(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp197(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r197 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp197(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp198(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r198 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp198(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp199(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r199 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp199(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp200(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r200 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp200(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp201(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r201 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp201(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp202(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r202 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp202(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp203(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r203 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp203(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp204(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r204 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp204(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp205(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r205 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp205(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp206(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r206 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp206(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp207(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r207 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp207(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp208(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r208 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp208(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp209(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r209 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp209(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp210(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r210 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp210(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp211(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r211 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp211(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp212(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r212 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp212(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp213(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r213 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp213(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp214(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r214 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp214(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp215(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r215 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp215(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp216(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r216 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp216(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp217(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r217 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp217(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp218(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r218 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp218(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp219(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r219 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp219(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp220(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r220 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp220(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp221(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r221 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp221(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp222(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r222 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp222(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp223(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r223 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp223(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp224(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r224 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp224(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp225(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r225 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp225(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp226(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r226 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp226(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp227(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r227 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp227(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp228(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r228 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp228(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp229(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r229 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp229(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp230(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r230 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp230(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp231(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r231 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp231(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp232(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r232 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp232(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp233(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r233 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp233(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp234(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r234 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp234(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp235(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r235 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp235(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp236(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r236 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp236(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp237(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r237 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp237(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp238(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r238 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp238(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp239(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r239 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp239(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp240(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r240 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp240(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp241(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r241 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp241(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp242(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r242 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp242(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp243(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r243 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp243(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp244(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r244 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp244(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp245(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r245 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp245(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp246(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r246 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp246(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp247(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r247 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp247(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp248(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r248 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp248(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp249(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r249 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp249(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp250(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r250 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp250(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp251(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r251 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp251(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp252(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r252 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp252(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp253(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r253 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp253(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp254(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r254 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp254(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp255(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r255 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp255(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp256(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r256 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp256(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp257(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r257 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp257(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp258(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r258 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp258(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp259(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r259 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp259(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp260(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r260 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp260(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp261(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r261 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp261(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp262(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r262 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp262(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp263(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r263 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp263(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp264(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r264 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp264(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp265(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r265 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp265(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp266(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r266 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp266(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp267(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r267 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp267(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp268(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r268 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp268(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp269(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r269 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp269(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp270(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r270 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp270(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp271(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r271 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp271(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp272(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r272 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp272(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp273(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r273 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp273(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp274(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r274 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp274(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp275(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r275 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp275(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp276(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r276 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp276(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp277(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r277 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp277(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp278(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r278 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp278(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp279(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r279 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp279(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp280(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r280 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp280(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp281(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r281 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp281(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp282(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r282 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp282(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp283(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r283 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp283(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp284(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r284 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp284(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp285(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r285 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp285(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp286(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r286 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp286(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp287(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r287 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp287(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp288(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r288 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp288(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp289(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r289 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp289(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp290(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r290 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp290(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp291(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r291 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp291(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp292(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r292 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp292(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp293(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r293 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp293(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp294(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r294 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp294(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp295(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r295 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp295(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp296(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r296 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp296(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp297(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r297 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp297(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp298(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r298 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp298(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp299(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r299 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp299(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp300(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r300 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp300(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp301(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r301 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp301(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp302(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r302 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp302(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp303(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r303 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp303(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp304(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r304 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp304(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp305(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r305 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp305(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp306(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r306 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp306(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp307(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r307 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp307(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp308(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r308 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp308(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp309(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r309 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp309(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp310(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r310 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp310(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp311(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r311 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp311(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp312(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r312 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp312(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp313(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r313 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp313(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp314(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r314 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp314(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp315(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r315 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp315(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp316(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r316 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp316(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp317(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r317 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp317(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp318(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r318 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp318(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp319(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r319 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp319(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp320(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r320 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp320(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp321(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r321 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp321(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp322(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r322 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp322(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp323(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r323 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp323(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp324(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r324 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp324(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp325(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r325 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp325(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp326(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r326 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp326(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp327(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r327 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp327(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp328(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r328 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp328(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp329(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r329 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp329(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp330(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r330 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp330(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp331(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r331 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp331(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp332(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r332 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp332(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp333(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r333 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp333(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp334(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r334 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp334(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp335(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r335 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp335(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp336(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r336 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp336(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp337(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r337 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp337(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp338(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r338 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp338(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp339(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r339 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp339(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp340(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r340 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp340(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp341(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r341 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp341(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp342(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r342 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp342(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp343(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r343 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp343(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp344(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r344 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp344(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp345(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r345 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp345(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp346(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r346 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp346(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp347(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r347 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp347(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp348(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r348 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp348(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp349(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r349 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp349(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp350(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r350 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp350(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp351(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r351 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp351(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp352(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r352 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp352(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp353(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r353 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp353(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp354(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r354 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp354(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp355(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r355 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp355(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp356(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r356 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp356(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp357(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r357 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp357(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp358(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r358 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp358(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp359(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r359 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp359(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp360(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r360 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp360(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp361(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r361 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp361(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp362(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r362 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp362(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp363(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r363 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp363(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp364(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r364 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp364(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp365(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r365 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp365(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp366(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r366 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp366(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp367(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r367 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp367(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp368(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r368 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp368(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp369(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r369 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp369(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp370(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r370 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp370(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp371(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r371 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp371(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp372(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r372 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp372(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp373(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r373 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp373(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp374(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r374 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp374(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp375(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r375 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp375(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp376(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r376 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp376(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp377(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r377 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp377(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp378(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r378 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp378(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp379(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r379 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp379(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp380(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r380 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp380(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp381(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r381 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp381(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp382(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r382 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp382(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp383(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r383 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp383(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp384(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r384 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp384(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp385(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r385 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp385(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp386(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r386 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp386(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp387(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r387 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp387(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp388(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r388 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp388(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp389(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r389 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp389(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp390(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r390 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp390(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp391(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r391 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp391(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp392(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r392 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp392(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp393(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r393 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp393(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp394(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r394 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp394(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp395(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r395 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp395(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp396(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r396 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp396(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp397(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r397 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp397(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp398(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r398 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp398(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp399(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r399 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp399(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp400(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r400 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp400(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp401(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r401 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp401(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp402(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r402 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp402(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp403(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r403 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp403(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp404(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r404 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp404(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp405(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r405 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp405(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp406(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r406 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp406(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp407(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r407 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp407(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp408(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r408 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp408(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp409(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r409 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp409(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp410(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r410 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp410(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp411(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r411 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp411(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp412(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r412 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp412(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp413(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r413 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp413(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp414(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r414 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp414(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp415(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r415 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp415(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp416(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r416 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp416(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp417(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r417 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp417(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp418(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r418 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp418(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp419(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r419 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp419(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp420(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r420 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp420(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp421(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r421 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp421(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp422(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r422 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp422(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp423(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r423 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp423(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp424(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r424 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp424(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp425(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r425 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp425(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp426(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r426 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp426(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp427(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r427 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp427(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp428(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r428 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp428(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp429(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r429 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp429(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp430(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r430 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp430(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp431(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r431 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp431(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp432(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r432 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp432(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp433(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r433 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp433(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp434(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r434 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp434(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp435(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r435 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp435(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp436(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r436 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp436(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp437(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r437 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp437(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp438(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r438 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp438(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp439(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r439 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp439(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp440(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r440 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp440(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp441(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r441 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp441(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp442(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r442 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp442(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp443(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r443 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp443(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp444(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r444 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp444(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp445(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r445 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp445(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp446(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r446 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp446(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp447(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r447 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp447(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp448(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r448 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp448(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp449(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r449 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp449(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp450(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r450 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp450(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp451(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r451 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp451(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp452(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r452 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp452(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp453(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r453 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp453(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp454(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r454 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp454(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp455(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r455 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp455(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp456(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r456 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp456(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp457(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r457 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp457(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp458(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r458 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp458(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp459(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r459 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp459(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp460(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r460 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp460(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp461(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r461 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp461(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp462(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r462 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp462(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp463(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r463 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp463(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp464(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r464 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp464(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp465(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r465 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp465(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp466(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r466 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp466(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp467(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r467 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp467(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp468(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r468 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp468(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp469(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r469 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp469(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp470(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r470 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp470(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp471(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r471 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp471(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp472(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r472 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp472(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp473(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r473 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp473(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp474(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r474 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp474(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp475(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r475 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp475(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp476(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r476 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp476(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp477(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r477 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp477(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp478(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r478 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp478(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp479(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r479 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp479(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp480(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r480 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp480(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp481(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r481 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp481(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp482(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r482 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp482(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp483(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r483 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp483(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp484(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r484 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp484(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp485(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r485 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp485(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp486(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r486 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp486(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp487(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r487 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp487(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp488(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r488 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp488(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp489(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r489 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp489(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp490(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r490 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp490(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp491(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r491 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp491(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp492(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r492 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp492(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp493(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r493 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp493(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp494(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r494 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp494(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp495(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r495 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp495(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp496(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r496 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp496(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp497(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r497 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp497(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp498(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r498 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp498(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp499(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r499 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp499(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp500(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r500 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp500(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp501(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r501 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp501(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp502(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r502 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp502(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp503(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r503 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp503(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp504(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r504 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp504(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp505(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r505 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp505(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp506(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r506 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp506(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp507(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r507 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp507(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp508(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r508 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp508(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp509(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r509 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp509(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp510(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r510 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp510(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp511(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r511 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp511(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp512(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r512 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp512(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp513(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r513 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp513(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp514(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r514 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp514(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp515(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r515 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp515(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp516(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r516 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp516(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp517(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r517 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp517(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp518(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r518 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp518(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp519(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r519 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp519(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp520(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r520 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp520(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp521(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r521 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp521(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp522(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r522 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp522(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp523(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r523 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp523(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp524(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r524 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp524(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp525(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r525 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp525(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp526(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r526 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp526(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp527(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r527 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp527(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp528(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r528 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp528(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp529(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r529 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp529(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp530(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r530 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp530(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp531(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r531 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp531(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp532(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r532 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp532(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp533(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r533 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp533(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp534(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r534 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp534(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp535(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r535 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp535(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp536(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r536 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp536(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp537(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r537 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp537(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp538(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r538 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp538(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp539(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r539 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp539(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp540(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r540 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp540(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp541(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r541 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp541(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp542(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r542 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp542(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp543(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r543 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp543(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp544(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r544 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp544(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp545(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r545 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp545(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp546(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r546 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp546(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp547(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r547 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp547(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp548(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r548 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp548(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp549(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r549 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp549(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp550(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r550 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp550(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp551(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r551 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp551(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp552(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r552 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp552(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp553(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r553 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp553(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp554(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r554 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp554(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp555(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r555 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp555(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp556(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r556 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp556(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp557(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r557 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp557(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp558(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r558 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp558(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp559(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r559 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp559(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp560(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r560 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp560(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp561(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r561 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp561(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp562(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r562 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp562(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp563(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r563 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp563(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp564(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r564 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp564(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp565(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r565 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp565(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp566(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r566 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp566(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp567(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r567 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp567(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp568(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r568 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp568(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp569(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r569 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp569(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp570(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r570 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp570(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp571(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r571 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp571(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp572(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r572 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp572(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp573(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r573 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp573(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp574(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r574 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp574(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp575(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r575 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp575(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp576(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r576 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp576(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp577(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r577 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp577(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp578(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r578 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp578(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp579(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r579 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp579(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp580(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r580 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp580(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp581(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r581 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp581(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp582(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r582 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp582(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp583(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r583 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp583(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp584(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r584 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp584(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp585(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r585 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp585(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp586(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r586 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp586(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp587(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r587 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp587(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp588(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r588 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp588(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp589(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r589 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp589(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp590(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r590 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp590(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp591(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r591 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp591(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp592(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r592 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp592(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp593(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r593 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp593(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp594(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r594 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp594(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp595(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r595 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp595(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp596(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r596 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp596(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp597(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r597 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp597(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp598(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r598 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp598(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp599(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r599 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp599(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp600(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r600 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp600(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp601(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r601 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp601(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp602(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r602 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp602(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp603(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r603 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp603(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp604(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r604 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp604(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp605(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r605 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp605(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp606(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r606 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp606(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp607(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r607 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp607(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp608(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r608 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp608(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp609(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r609 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp609(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp610(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r610 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp610(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp611(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r611 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp611(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp612(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r612 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp612(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp613(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r613 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp613(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp614(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r614 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp614(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp615(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r615 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp615(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp616(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r616 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp616(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp617(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r617 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp617(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp618(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r618 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp618(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp619(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r619 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp619(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp620(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r620 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp620(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp621(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r621 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp621(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp622(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r622 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp622(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp623(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r623 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp623(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp624(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r624 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp624(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp625(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r625 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp625(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp626(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r626 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp626(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp627(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r627 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp627(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp628(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r628 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp628(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp629(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r629 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp629(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp630(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r630 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp630(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp631(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r631 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp631(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp632(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r632 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp632(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp633(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r633 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp633(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp634(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r634 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp634(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp635(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r635 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp635(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp636(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r636 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp636(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp637(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r637 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp637(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp638(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r638 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp638(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp639(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r639 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp639(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp640(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r640 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp640(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp641(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r641 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp641(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp642(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r642 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp642(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp643(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r643 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp643(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp644(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r644 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp644(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp645(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r645 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp645(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp646(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r646 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp646(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp647(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r647 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp647(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp648(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r648 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp648(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp649(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r649 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp649(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp650(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r650 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp650(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp651(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r651 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp651(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp652(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r652 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp652(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp653(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r653 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp653(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp654(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r654 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp654(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp655(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r655 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp655(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp656(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r656 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp656(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp657(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r657 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp657(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp658(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r658 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp658(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp659(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r659 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp659(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp660(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r660 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp660(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp661(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r661 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp661(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp662(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r662 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp662(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp663(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r663 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp663(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp664(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r664 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp664(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp665(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r665 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp665(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp666(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r666 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp666(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp667(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r667 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp667(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp668(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r668 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp668(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp669(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r669 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp669(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp670(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r670 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp670(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp671(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r671 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp671(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp672(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r672 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp672(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp673(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r673 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp673(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp674(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r674 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp674(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp675(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r675 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp675(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp676(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r676 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp676(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp677(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r677 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp677(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp678(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r678 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp678(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp679(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r679 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp679(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp680(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r680 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp680(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp681(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r681 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp681(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp682(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r682 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp682(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp683(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r683 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp683(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp684(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r684 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp684(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp685(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r685 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp685(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp686(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r686 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp686(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp687(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r687 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp687(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp688(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r688 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp688(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp689(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r689 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp689(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp690(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r690 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp690(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp691(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r691 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp691(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp692(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r692 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp692(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp693(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r693 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp693(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp694(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r694 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp694(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp695(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r695 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp695(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp696(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r696 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp696(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp697(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r697 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp697(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp698(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r698 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp698(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp699(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r699 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp699(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp700(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r700 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp700(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp701(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r701 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp701(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp702(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r702 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp702(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp703(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r703 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp703(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp704(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r704 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp704(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp705(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r705 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp705(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp706(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r706 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp706(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp707(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r707 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp707(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp708(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r708 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp708(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp709(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r709 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp709(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp710(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r710 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp710(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp711(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r711 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp711(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp712(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r712 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp712(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp713(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r713 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp713(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp714(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r714 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp714(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp715(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r715 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp715(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp716(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r716 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp716(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp717(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r717 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp717(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp718(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r718 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp718(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp719(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r719 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp719(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp720(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r720 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp720(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp721(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r721 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp721(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp722(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r722 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp722(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp723(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r723 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp723(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp724(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r724 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp724(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp725(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r725 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp725(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp726(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r726 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp726(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp727(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r727 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp727(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp728(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r728 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp728(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp729(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r729 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp729(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp730(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r730 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp730(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp731(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r731 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp731(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp732(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r732 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp732(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp733(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r733 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp733(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp734(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r734 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp734(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp735(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r735 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp735(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp736(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r736 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp736(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp737(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r737 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp737(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp738(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r738 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp738(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp739(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r739 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp739(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp740(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r740 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp740(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp741(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r741 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp741(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp742(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r742 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp742(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp743(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r743 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp743(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp744(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r744 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp744(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp745(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r745 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp745(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp746(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r746 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp746(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp747(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r747 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp747(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp748(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r748 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp748(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp749(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r749 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp749(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp750(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r750 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp750(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp751(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r751 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp751(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp752(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r752 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp752(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp753(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r753 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp753(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp754(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r754 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp754(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp755(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r755 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp755(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp756(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r756 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp756(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp757(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r757 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp757(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp758(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r758 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp758(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp759(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r759 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp759(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp760(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r760 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp760(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp761(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r761 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp761(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp762(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r762 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp762(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp763(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r763 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp763(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp764(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r764 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp764(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp765(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r765 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp765(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp766(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r766 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp766(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp767(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r767 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp767(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp768(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r768 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp768(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp769(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r769 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp769(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp770(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r770 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp770(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp771(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r771 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp771(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp772(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r772 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp772(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp773(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r773 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp773(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp774(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r774 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp774(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp775(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r775 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp775(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp776(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r776 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp776(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp777(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r777 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp777(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp778(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r778 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp778(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp779(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r779 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp779(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp780(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r780 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp780(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp781(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r781 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp781(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp782(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r782 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp782(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp783(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r783 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp783(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp784(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r784 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp784(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp785(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r785 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp785(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp786(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r786 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp786(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp787(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r787 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp787(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp788(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r788 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp788(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp789(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r789 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp789(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp790(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r790 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp790(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp791(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r791 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp791(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp792(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r792 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp792(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp793(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r793 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp793(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp794(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r794 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp794(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp795(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r795 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp795(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp796(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r796 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp796(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp797(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r797 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp797(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp798(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r798 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp798(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp799(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r799 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp799(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp800(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r800 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp800(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp801(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r801 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp801(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp802(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r802 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp802(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp803(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r803 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp803(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp804(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r804 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp804(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp805(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r805 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp805(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp806(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r806 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp806(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp807(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r807 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp807(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp808(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r808 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp808(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp809(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r809 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp809(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp810(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r810 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp810(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp811(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r811 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp811(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp812(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r812 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp812(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp813(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r813 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp813(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp814(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r814 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp814(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp815(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r815 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp815(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp816(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r816 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp816(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp817(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r817 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp817(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp818(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r818 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp818(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp819(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r819 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp819(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp820(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r820 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp820(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp821(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r821 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp821(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp822(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r822 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp822(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp823(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r823 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp823(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp824(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r824 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp824(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp825(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r825 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp825(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp826(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r826 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp826(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp827(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r827 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp827(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp828(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r828 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp828(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp829(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r829 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp829(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp830(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r830 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp830(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp831(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r831 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp831(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp832(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r832 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp832(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp833(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r833 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp833(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp834(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r834 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp834(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp835(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r835 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp835(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp836(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r836 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp836(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp837(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r837 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp837(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp838(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r838 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp838(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp839(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r839 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp839(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp840(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r840 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp840(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp841(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r841 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp841(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp842(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r842 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp842(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp843(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r843 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp843(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp844(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r844 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp844(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp845(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r845 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp845(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp846(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r846 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp846(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp847(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r847 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp847(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp848(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r848 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp848(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp849(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r849 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp849(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp850(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r850 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp850(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp851(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r851 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp851(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp852(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r852 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp852(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp853(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r853 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp853(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp854(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r854 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp854(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp855(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r855 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp855(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp856(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r856 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp856(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp857(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r857 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp857(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp858(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r858 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp858(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp859(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r859 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp859(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp860(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r860 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp860(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp861(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r861 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp861(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp862(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r862 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp862(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp863(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r863 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp863(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp864(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r864 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp864(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp865(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r865 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp865(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp866(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r866 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp866(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp867(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r867 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp867(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp868(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r868 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp868(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp869(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r869 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp869(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp870(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r870 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp870(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp871(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r871 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp871(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp872(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r872 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp872(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp873(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r873 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp873(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp874(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r874 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp874(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp875(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r875 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp875(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp876(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r876 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp876(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp877(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r877 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp877(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp878(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r878 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp878(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp879(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r879 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp879(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp880(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r880 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp880(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp881(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r881 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp881(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp882(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r882 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp882(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp883(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r883 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp883(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp884(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r884 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp884(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp885(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r885 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp885(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp886(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r886 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp886(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp887(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r887 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp887(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp888(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r888 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp888(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp889(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r889 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp889(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp890(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r890 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp890(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp891(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r891 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp891(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp892(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r892 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp892(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp893(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r893 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp893(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp894(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r894 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp894(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp895(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r895 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp895(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp896(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r896 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp896(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp897(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r897 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp897(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp898(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r898 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp898(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp899(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r899 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp899(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp900(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r900 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp900(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp901(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r901 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp901(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp902(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r902 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp902(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp903(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r903 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp903(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp904(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r904 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp904(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp905(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r905 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp905(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp906(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r906 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp906(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp907(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r907 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp907(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp908(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r908 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp908(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp909(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r909 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp909(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp910(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r910 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp910(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp911(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r911 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp911(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp912(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r912 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp912(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp913(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r913 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp913(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp914(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r914 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp914(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp915(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r915 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp915(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp916(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r916 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp916(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp917(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r917 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp917(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp918(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r918 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp918(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp919(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r919 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp919(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp920(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r920 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp920(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp921(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r921 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp921(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp922(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r922 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp922(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp923(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r923 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp923(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp924(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r924 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp924(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp925(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r925 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp925(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp926(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r926 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp926(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp927(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r927 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp927(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp928(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r928 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp928(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp929(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r929 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp929(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp930(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r930 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp930(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp931(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r931 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp931(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp932(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r932 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp932(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp933(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r933 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp933(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp934(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r934 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp934(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp935(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r935 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp935(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp936(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r936 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp936(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp937(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r937 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp937(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp938(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r938 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp938(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp939(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r939 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp939(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp940(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r940 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp940(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp941(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r941 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp941(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp942(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r942 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp942(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp943(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r943 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp943(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp944(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r944 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp944(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp945(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r945 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp945(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp946(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r946 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp946(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp947(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r947 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp947(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp948(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r948 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp948(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp949(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r949 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp949(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp950(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r950 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp950(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp951(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r951 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp951(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp952(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r952 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp952(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp953(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r953 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp953(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp954(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r954 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp954(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp955(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r955 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp955(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp956(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r956 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp956(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp957(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r957 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp957(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp958(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r958 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp958(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp959(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r959 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp959(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp960(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r960 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp960(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp961(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r961 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp961(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp962(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r962 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp962(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp963(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r963 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp963(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp964(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r964 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp964(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp965(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r965 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp965(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp966(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r966 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp966(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp967(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r967 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp967(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp968(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r968 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp968(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp969(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r969 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp969(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp970(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r970 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp970(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp971(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r971 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp971(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp972(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r972 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp972(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp973(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r973 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp973(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp974(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r974 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp974(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp975(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r975 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp975(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp976(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r976 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp976(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp977(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r977 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp977(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp978(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r978 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp978(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp979(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r979 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp979(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp980(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r980 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp980(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp981(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r981 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp981(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp982(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r982 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp982(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp983(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r983 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp983(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp984(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r984 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp984(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp985(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r985 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp985(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp986(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r986 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp986(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp987(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r987 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp987(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp988(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r988 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp988(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp989(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r989 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp989(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp990(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r990 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp990(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp991(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r991 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp991(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp992(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r992 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp992(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp993(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r993 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp993(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp994(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r994 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp994(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp995(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r995 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp995(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp996(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r996 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp996(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp997(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r997 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp997(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table inp998(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b)),update}
%rule r998 out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp998(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}
