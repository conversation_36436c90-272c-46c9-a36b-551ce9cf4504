/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"
#include "unistd.h"

#pragma pack(1)
typedef struct {
    int32_t count;
    int32_t a;
    int32_t b;
} DEL;
#pragma pack(0)

int32_t dtl_ext_func_func2(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func3(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func4(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func5(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func6(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func7(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func8(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func9(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func10(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func11(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func12(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func13(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func14(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func15(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func16(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func17(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func18(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func19(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func20(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func21(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func22(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func23(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func24(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func25(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func26(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func27(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func28(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func29(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func30(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func31(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func32(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func33(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func34(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func35(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func36(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func37(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func38(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func39(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func40(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func41(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func42(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func43(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func44(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func45(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func46(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func47(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func48(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func49(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func50(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func51(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func52(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func53(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func54(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func55(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func56(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func57(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func58(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func59(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func60(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func61(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func62(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func63(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func64(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func65(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func66(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func67(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func68(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func69(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func70(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func71(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func72(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func73(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func74(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func75(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func76(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func77(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func78(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func79(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func80(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func81(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func82(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func83(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func84(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func85(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func86(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func87(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func88(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func89(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func90(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func91(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func92(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func93(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func94(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func95(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func96(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func97(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func98(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func99(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func100(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func101(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func102(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func103(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func104(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func105(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func106(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func107(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func108(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func109(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func110(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func111(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func112(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func113(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func114(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func115(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func116(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func117(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func118(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func119(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func120(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func121(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func122(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func123(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func124(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func125(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func126(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func127(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func128(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func129(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func130(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func131(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func132(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func133(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func134(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func135(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func136(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func137(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func138(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func139(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func140(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func141(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func142(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func143(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func144(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func145(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func146(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func147(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func148(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func149(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func150(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func151(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func152(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func153(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func154(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func155(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func156(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func157(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func158(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func159(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func160(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func161(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func162(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func163(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func164(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func165(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func166(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func167(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func168(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func169(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func170(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func171(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func172(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func173(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func174(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func175(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func176(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func177(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func178(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func179(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func180(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func181(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func182(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func183(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func184(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func185(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func186(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func187(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func188(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func189(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func190(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func191(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func192(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func193(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func194(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func195(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func196(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func197(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func198(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func199(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func200(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func201(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func202(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func203(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func204(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func205(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func206(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func207(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func208(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func209(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func210(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func211(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func212(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func213(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func214(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func215(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func216(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func217(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func218(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func219(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func220(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func221(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func222(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func223(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func224(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func225(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func226(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func227(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func228(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func229(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func230(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func231(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func232(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func233(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func234(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func235(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func236(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func237(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func238(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func239(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func240(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func241(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func242(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func243(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func244(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func245(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func246(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func247(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func248(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func249(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func250(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func251(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func252(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func253(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func254(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func255(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func256(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func257(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func258(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func259(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func260(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func261(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func262(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func263(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func264(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func265(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func266(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func267(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func268(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func269(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func270(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func271(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func272(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func273(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func274(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func275(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func276(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func277(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func278(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func279(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func280(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func281(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func282(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func283(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func284(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func285(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func286(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func287(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func288(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func289(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func290(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func291(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func292(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func293(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func294(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func295(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func296(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func297(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func298(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func299(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func300(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func301(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func302(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func303(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func304(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func305(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func306(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func307(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func308(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func309(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func310(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func311(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func312(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func313(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func314(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func315(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func316(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func317(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func318(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func319(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func320(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func321(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func322(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func323(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func324(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func325(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func326(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func327(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func328(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func329(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func330(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func331(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func332(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func333(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func334(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func335(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func336(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func337(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func338(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func339(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func340(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func341(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func342(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func343(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func344(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func345(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func346(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func347(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func348(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func349(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func350(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func351(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func352(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func353(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func354(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func355(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func356(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func357(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func358(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func359(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func360(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func361(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func362(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func363(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func364(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func365(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func366(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func367(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func368(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func369(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func370(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func371(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func372(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func373(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func374(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func375(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func376(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func377(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func378(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func379(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func380(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func381(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func382(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func383(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func384(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func385(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func386(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func387(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func388(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func389(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func390(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func391(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func392(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func393(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func394(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func395(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func396(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func397(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func398(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func399(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func400(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func401(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func402(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func403(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func404(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func405(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func406(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func407(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func408(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func409(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func410(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func411(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func412(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func413(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func414(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func415(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func416(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func417(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func418(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func419(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func420(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func421(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func422(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func423(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func424(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func425(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func426(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func427(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func428(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func429(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func430(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func431(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func432(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func433(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func434(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func435(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func436(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func437(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func438(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func439(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func440(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func441(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func442(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func443(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func444(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func445(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func446(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func447(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func448(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func449(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func450(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func451(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func452(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func453(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func454(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func455(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func456(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func457(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func458(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func459(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func460(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func461(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func462(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func463(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func464(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func465(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func466(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func467(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func468(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func469(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func470(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func471(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func472(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func473(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func474(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func475(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func476(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func477(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func478(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func479(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func480(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func481(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func482(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func483(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func484(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func485(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func486(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func487(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func488(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func489(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func490(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func491(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func492(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func493(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func494(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func495(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func496(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func497(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func498(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func499(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func500(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func501(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func502(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func503(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func504(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func505(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func506(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func507(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func508(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func509(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func510(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func511(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func512(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func513(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func514(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func515(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func516(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func517(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func518(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func519(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func520(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func521(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func522(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func523(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func524(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func525(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func526(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func527(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func528(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func529(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func530(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func531(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func532(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func533(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func534(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func535(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func536(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func537(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func538(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func539(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func540(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func541(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func542(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func543(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func544(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func545(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func546(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func547(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func548(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func549(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func550(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func551(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func552(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func553(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func554(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func555(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func556(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func557(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func558(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func559(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func560(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func561(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func562(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func563(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func564(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func565(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func566(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func567(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func568(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func569(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func570(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func571(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func572(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func573(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func574(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func575(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func576(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func577(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func578(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func579(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func580(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func581(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func582(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func583(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func584(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func585(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func586(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func587(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func588(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func589(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func590(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func591(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func592(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func593(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func594(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func595(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func596(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func597(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func598(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func599(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func600(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func601(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func602(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func603(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func604(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func605(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func606(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func607(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func608(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func609(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func610(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func611(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func612(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func613(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func614(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func615(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func616(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func617(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func618(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func619(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func620(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func621(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func622(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func623(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func624(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func625(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func626(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func627(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func628(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func629(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func630(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func631(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func632(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func633(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func634(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func635(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func636(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func637(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func638(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func639(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func640(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func641(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func642(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func643(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func644(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func645(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func646(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func647(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func648(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func649(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func650(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func651(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func652(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func653(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func654(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func655(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func656(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func657(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func658(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func659(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func660(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func661(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func662(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func663(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func664(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func665(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func666(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func667(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func668(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func669(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func670(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func671(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func672(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func673(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func674(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func675(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func676(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func677(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func678(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func679(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func680(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func681(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func682(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func683(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func684(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func685(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func686(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func687(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func688(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func689(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func690(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func691(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func692(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func693(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func694(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func695(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func696(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func697(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func698(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func699(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func700(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func701(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func702(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func703(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func704(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func705(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func706(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func707(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func708(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func709(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func710(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func711(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func712(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func713(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func714(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func715(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func716(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func717(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func718(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func719(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func720(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func721(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func722(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func723(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func724(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func725(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func726(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func727(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func728(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func729(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func730(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func731(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func732(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func733(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func734(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func735(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func736(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func737(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func738(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func739(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func740(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func741(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func742(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func743(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func744(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func745(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func746(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func747(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func748(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func749(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func750(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func751(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func752(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func753(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func754(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func755(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func756(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func757(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func758(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func759(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func760(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func761(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func762(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func763(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func764(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func765(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func766(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func767(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func768(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func769(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func770(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func771(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func772(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func773(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func774(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func775(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func776(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func777(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func778(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func779(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func780(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func781(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func782(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func783(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func784(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func785(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func786(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func787(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func788(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func789(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func790(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func791(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func792(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func793(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func794(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func795(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func796(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func797(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func798(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func799(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func800(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func801(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func802(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func803(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func804(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func805(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func806(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func807(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func808(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func809(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func810(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func811(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func812(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func813(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func814(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func815(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func816(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func817(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func818(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func819(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func820(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func821(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func822(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func823(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func824(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func825(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func826(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func827(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func828(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func829(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func830(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func831(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func832(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func833(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func834(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func835(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func836(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func837(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func838(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func839(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func840(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func841(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func842(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func843(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func844(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func845(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func846(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func847(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func848(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func849(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func850(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func851(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func852(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func853(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func854(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func855(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func856(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func857(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func858(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func859(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func860(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func861(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func862(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func863(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func864(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func865(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func866(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func867(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func868(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func869(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func870(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func871(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func872(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func873(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func874(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func875(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func876(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func877(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func878(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func879(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func880(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func881(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func882(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func883(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func884(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func885(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func886(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func887(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func888(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func889(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func890(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func891(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func892(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func893(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func894(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func895(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func896(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func897(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func898(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func899(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func900(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func901(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func902(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func903(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func904(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func905(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func906(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func907(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func908(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func909(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func910(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func911(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func912(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func913(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func914(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func915(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func916(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func917(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func918(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func919(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func920(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func921(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func922(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func923(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func924(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func925(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func926(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func927(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func928(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func929(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func930(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func931(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func932(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func933(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func934(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func935(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func936(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func937(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func938(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func939(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func940(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func941(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func942(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func943(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func944(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func945(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func946(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func947(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func948(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func949(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func950(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func951(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func952(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func953(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func954(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func955(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func956(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func957(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func958(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func959(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func960(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func961(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func962(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func963(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func964(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func965(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func966(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func967(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func968(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func969(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func970(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func971(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func972(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func973(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func974(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func975(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func976(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func977(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func978(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func979(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func980(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func981(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func982(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func983(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func984(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func985(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func986(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func987(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func988(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func989(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func990(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func991(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func992(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func993(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func994(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func995(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func996(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func997(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func998(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func999(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1000(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1001(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1002(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1003(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1004(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1005(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1006(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1007(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1008(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1009(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1010(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1011(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1012(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1013(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1014(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1015(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1016(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1017(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1018(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1019(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1020(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1021(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1022(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1023(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}

int32_t dtl_ext_func_func1024(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a;
    return GMERR_OK;
}
