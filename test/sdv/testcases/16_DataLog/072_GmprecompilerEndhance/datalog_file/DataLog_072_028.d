%version v0.0.0
%table out(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b))}
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)), index(1(b))}
out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%function func2(c:int4 -> c1:int4){}
%rule r2 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func2(c,c10).
%function func3(c:int4 -> c1:int4){}
%rule r3 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func3(c,c10).
%function func4(c:int4 -> c1:int4){}
%rule r4 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func4(c,c10).
%function func5(c:int4 -> c1:int4){}
%rule r5 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func5(c,c10).
%function func6(c:int4 -> c1:int4){}
%rule r6 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func6(c,c10).
%function func7(c:int4 -> c1:int4){}
%rule r7 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func7(c,c10).
%function func8(c:int4 -> c1:int4){}
%rule r8 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func8(c,c10).
%function func9(c:int4 -> c1:int4){}
%rule r9 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func9(c,c10).
%function func10(c:int4 -> c1:int4){}
%rule x0 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func10(c,c10).
%function func11(c:int4 -> c1:int4){}
%rule x1 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func11(c,c10).
%function func12(c:int4 -> c1:int4){}
%rule x2 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func12(c,c10).
%function func13(c:int4 -> c1:int4){}
%rule x3 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func13(c,c10).
%function func14(c:int4 -> c1:int4){}
%rule x4 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func14(c,c10).
%function func15(c:int4 -> c1:int4){}
%rule x5 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func15(c,c10).
%function func16(c:int4 -> c1:int4){}
%rule x6 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func16(c,c10).
%function func17(c:int4 -> c1:int4){}
%rule x7 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func17(c,c10).
%function func18(c:int4 -> c1:int4){}
%rule x8 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func18(c,c10).
%function func19(c:int4 -> c1:int4){}
%rule x9 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func19(c,c10).
%function func20(c:int4 -> c1:int4){}
%rule r20 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func20(c,c10).
%function func21(c:int4 -> c1:int4){}
%rule r21 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func21(c,c10).
%function func22(c:int4 -> c1:int4){}
%rule r22 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func22(c,c10).
%function func23(c:int4 -> c1:int4){}
%rule r23 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func23(c,c10).
%function func24(c:int4 -> c1:int4){}
%rule r24 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func24(c,c10).
%function func25(c:int4 -> c1:int4){}
%rule r25 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func25(c,c10).
%function func26(c:int4 -> c1:int4){}
%rule r26 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func26(c,c10).
%function func27(c:int4 -> c1:int4){}
%rule r27 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func27(c,c10).
%function func28(c:int4 -> c1:int4){}
%rule r28 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func28(c,c10).
%function func29(c:int4 -> c1:int4){}
%rule r29 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func29(c,c10).
%function func30(c:int4 -> c1:int4){}
%rule r30 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func30(c,c10).
%function func31(c:int4 -> c1:int4){}
%rule r31 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func31(c,c10).
%function func32(c:int4 -> c1:int4){}
%rule r32 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func32(c,c10).
%function func33(c:int4 -> c1:int4){}
%rule r33 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func33(c,c10).
%function func34(c:int4 -> c1:int4){}
%rule r34 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func34(c,c10).
%function func35(c:int4 -> c1:int4){}
%rule r35 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func35(c,c10).
%function func36(c:int4 -> c1:int4){}
%rule r36 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func36(c,c10).
%function func37(c:int4 -> c1:int4){}
%rule r37 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func37(c,c10).
%function func38(c:int4 -> c1:int4){}
%rule r38 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func38(c,c10).
%function func39(c:int4 -> c1:int4){}
%rule r39 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func39(c,c10).
%function func40(c:int4 -> c1:int4){}
%rule r40 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func40(c,c10).
%function func41(c:int4 -> c1:int4){}
%rule r41 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func41(c,c10).
%function func42(c:int4 -> c1:int4){}
%rule r42 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func42(c,c10).
%function func43(c:int4 -> c1:int4){}
%rule r43 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func43(c,c10).
%function func44(c:int4 -> c1:int4){}
%rule r44 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func44(c,c10).
%function func45(c:int4 -> c1:int4){}
%rule r45 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func45(c,c10).
%function func46(c:int4 -> c1:int4){}
%rule r46 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func46(c,c10).
%function func47(c:int4 -> c1:int4){}
%rule r47 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func47(c,c10).
%function func48(c:int4 -> c1:int4){}
%rule r48 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func48(c,c10).
%function func49(c:int4 -> c1:int4){}
%rule r49 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func49(c,c10).
%function func50(c:int4 -> c1:int4){}
%rule r50 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func50(c,c10).
%function func51(c:int4 -> c1:int4){}
%rule r51 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func51(c,c10).
%function func52(c:int4 -> c1:int4){}
%rule r52 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func52(c,c10).
%function func53(c:int4 -> c1:int4){}
%rule r53 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func53(c,c10).
%function func54(c:int4 -> c1:int4){}
%rule r54 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func54(c,c10).
%function func55(c:int4 -> c1:int4){}
%rule r55 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func55(c,c10).
%function func56(c:int4 -> c1:int4){}
%rule r56 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func56(c,c10).
%function func57(c:int4 -> c1:int4){}
%rule r57 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func57(c,c10).
%function func58(c:int4 -> c1:int4){}
%rule r58 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func58(c,c10).
%function func59(c:int4 -> c1:int4){}
%rule r59 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func59(c,c10).
%function func60(c:int4 -> c1:int4){}
%rule r60 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func60(c,c10).
%function func61(c:int4 -> c1:int4){}
%rule r61 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func61(c,c10).
%function func62(c:int4 -> c1:int4){}
%rule r62 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func62(c,c10).
%function func63(c:int4 -> c1:int4){}
%rule r63 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func63(c,c10).
%function func64(c:int4 -> c1:int4){}
%rule r64 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func64(c,c10).
%function func65(c:int4 -> c1:int4){}
%rule r65 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func65(c,c10).
%function func66(c:int4 -> c1:int4){}
%rule r66 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func66(c,c10).
%function func67(c:int4 -> c1:int4){}
%rule r67 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func67(c,c10).
%function func68(c:int4 -> c1:int4){}
%rule r68 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func68(c,c10).
%function func69(c:int4 -> c1:int4){}
%rule r69 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func69(c,c10).
%function func70(c:int4 -> c1:int4){}
%rule r70 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func70(c,c10).
%function func71(c:int4 -> c1:int4){}
%rule r71 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func71(c,c10).
%function func72(c:int4 -> c1:int4){}
%rule r72 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func72(c,c10).
%function func73(c:int4 -> c1:int4){}
%rule r73 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func73(c,c10).
%function func74(c:int4 -> c1:int4){}
%rule r74 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func74(c,c10).
%function func75(c:int4 -> c1:int4){}
%rule r75 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func75(c,c10).
%function func76(c:int4 -> c1:int4){}
%rule r76 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func76(c,c10).
%function func77(c:int4 -> c1:int4){}
%rule r77 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func77(c,c10).
%function func78(c:int4 -> c1:int4){}
%rule r78 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func78(c,c10).
%function func79(c:int4 -> c1:int4){}
%rule r79 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func79(c,c10).
%function func80(c:int4 -> c1:int4){}
%rule r80 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func80(c,c10).
%function func81(c:int4 -> c1:int4){}
%rule r81 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func81(c,c10).
%function func82(c:int4 -> c1:int4){}
%rule r82 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func82(c,c10).
%function func83(c:int4 -> c1:int4){}
%rule r83 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func83(c,c10).
%function func84(c:int4 -> c1:int4){}
%rule r84 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func84(c,c10).
%function func85(c:int4 -> c1:int4){}
%rule r85 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func85(c,c10).
%function func86(c:int4 -> c1:int4){}
%rule r86 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func86(c,c10).
%function func87(c:int4 -> c1:int4){}
%rule r87 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func87(c,c10).
%function func88(c:int4 -> c1:int4){}
%rule r88 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func88(c,c10).
%function func89(c:int4 -> c1:int4){}
%rule r89 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func89(c,c10).
%function func90(c:int4 -> c1:int4){}
%rule r90 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func90(c,c10).
%function func91(c:int4 -> c1:int4){}
%rule r91 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func91(c,c10).
%function func92(c:int4 -> c1:int4){}
%rule r92 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func92(c,c10).
%function func93(c:int4 -> c1:int4){}
%rule r93 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func93(c,c10).
%function func94(c:int4 -> c1:int4){}
%rule r94 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func94(c,c10).
%function func95(c:int4 -> c1:int4){}
%rule r95 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func95(c,c10).
%function func96(c:int4 -> c1:int4){}
%rule r96 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func96(c,c10).
%function func97(c:int4 -> c1:int4){}
%rule r97 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func97(c,c10).
%function func98(c:int4 -> c1:int4){}
%rule r98 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func98(c,c10).
%function func99(c:int4 -> c1:int4){}
%rule r99 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func99(c,c10).
%function func100(c:int4 -> c1:int4){}
%rule x00 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func100(c,c10).
%function func101(c:int4 -> c1:int4){}
%rule x01 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func101(c,c10).
%function func102(c:int4 -> c1:int4){}
%rule x02 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func102(c,c10).
%function func103(c:int4 -> c1:int4){}
%rule x03 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func103(c,c10).
%function func104(c:int4 -> c1:int4){}
%rule x04 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func104(c,c10).
%function func105(c:int4 -> c1:int4){}
%rule x05 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func105(c,c10).
%function func106(c:int4 -> c1:int4){}
%rule x06 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func106(c,c10).
%function func107(c:int4 -> c1:int4){}
%rule x07 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func107(c,c10).
%function func108(c:int4 -> c1:int4){}
%rule x08 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func108(c,c10).
%function func109(c:int4 -> c1:int4){}
%rule x09 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func109(c,c10).
%function func110(c:int4 -> c1:int4){}
%rule x10 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func110(c,c10).
%function func111(c:int4 -> c1:int4){}
%rule x11 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func111(c,c10).
%function func112(c:int4 -> c1:int4){}
%rule x12 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func112(c,c10).
%function func113(c:int4 -> c1:int4){}
%rule x13 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func113(c,c10).
%function func114(c:int4 -> c1:int4){}
%rule x14 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func114(c,c10).
%function func115(c:int4 -> c1:int4){}
%rule x15 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func115(c,c10).
%function func116(c:int4 -> c1:int4){}
%rule x16 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func116(c,c10).
%function func117(c:int4 -> c1:int4){}
%rule x17 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func117(c,c10).
%function func118(c:int4 -> c1:int4){}
%rule x18 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func118(c,c10).
%function func119(c:int4 -> c1:int4){}
%rule x19 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func119(c,c10).
%function func120(c:int4 -> c1:int4){}
%rule x20 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func120(c,c10).
%function func121(c:int4 -> c1:int4){}
%rule x21 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func121(c,c10).
%function func122(c:int4 -> c1:int4){}
%rule x22 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func122(c,c10).
%function func123(c:int4 -> c1:int4){}
%rule x23 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func123(c,c10).
%function func124(c:int4 -> c1:int4){}
%rule x24 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func124(c,c10).
%function func125(c:int4 -> c1:int4){}
%rule x25 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func125(c,c10).
%function func126(c:int4 -> c1:int4){}
%rule x26 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func126(c,c10).
%function func127(c:int4 -> c1:int4){}
%rule x27 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func127(c,c10).
%function func128(c:int4 -> c1:int4){}
%rule x28 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func128(c,c10).
%function func129(c:int4 -> c1:int4){}
%rule x29 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func129(c,c10).
%function func130(c:int4 -> c1:int4){}
%rule x30 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func130(c,c10).
%function func131(c:int4 -> c1:int4){}
%rule x31 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func131(c,c10).
%function func132(c:int4 -> c1:int4){}
%rule x32 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func132(c,c10).
%function func133(c:int4 -> c1:int4){}
%rule x33 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func133(c,c10).
%function func134(c:int4 -> c1:int4){}
%rule x34 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func134(c,c10).
%function func135(c:int4 -> c1:int4){}
%rule x35 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func135(c,c10).
%function func136(c:int4 -> c1:int4){}
%rule x36 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func136(c,c10).
%function func137(c:int4 -> c1:int4){}
%rule x37 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func137(c,c10).
%function func138(c:int4 -> c1:int4){}
%rule x38 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func138(c,c10).
%function func139(c:int4 -> c1:int4){}
%rule x39 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func139(c,c10).
%function func140(c:int4 -> c1:int4){}
%rule x40 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func140(c,c10).
%function func141(c:int4 -> c1:int4){}
%rule x41 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func141(c,c10).
%function func142(c:int4 -> c1:int4){}
%rule x42 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func142(c,c10).
%function func143(c:int4 -> c1:int4){}
%rule x43 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func143(c,c10).
%function func144(c:int4 -> c1:int4){}
%rule x44 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func144(c,c10).
%function func145(c:int4 -> c1:int4){}
%rule x45 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func145(c,c10).
%function func146(c:int4 -> c1:int4){}
%rule x46 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func146(c,c10).
%function func147(c:int4 -> c1:int4){}
%rule x47 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func147(c,c10).
%function func148(c:int4 -> c1:int4){}
%rule x48 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func148(c,c10).
%function func149(c:int4 -> c1:int4){}
%rule x49 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func149(c,c10).
%function func150(c:int4 -> c1:int4){}
%rule x50 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func150(c,c10).
%function func151(c:int4 -> c1:int4){}
%rule x51 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func151(c,c10).
%function func152(c:int4 -> c1:int4){}
%rule x52 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func152(c,c10).
%function func153(c:int4 -> c1:int4){}
%rule x53 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func153(c,c10).
%function func154(c:int4 -> c1:int4){}
%rule x54 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func154(c,c10).
%function func155(c:int4 -> c1:int4){}
%rule x55 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func155(c,c10).
%function func156(c:int4 -> c1:int4){}
%rule x56 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func156(c,c10).
%function func157(c:int4 -> c1:int4){}
%rule x57 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func157(c,c10).
%function func158(c:int4 -> c1:int4){}
%rule x58 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func158(c,c10).
%function func159(c:int4 -> c1:int4){}
%rule x59 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func159(c,c10).
%function func160(c:int4 -> c1:int4){}
%rule x60 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func160(c,c10).
%function func161(c:int4 -> c1:int4){}
%rule x61 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func161(c,c10).
%function func162(c:int4 -> c1:int4){}
%rule x62 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func162(c,c10).
%function func163(c:int4 -> c1:int4){}
%rule x63 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func163(c,c10).
%function func164(c:int4 -> c1:int4){}
%rule x64 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func164(c,c10).
%function func165(c:int4 -> c1:int4){}
%rule x65 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func165(c,c10).
%function func166(c:int4 -> c1:int4){}
%rule x66 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func166(c,c10).
%function func167(c:int4 -> c1:int4){}
%rule x67 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func167(c,c10).
%function func168(c:int4 -> c1:int4){}
%rule x68 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func168(c,c10).
%function func169(c:int4 -> c1:int4){}
%rule x69 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func169(c,c10).
%function func170(c:int4 -> c1:int4){}
%rule x70 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func170(c,c10).
%function func171(c:int4 -> c1:int4){}
%rule x71 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func171(c,c10).
%function func172(c:int4 -> c1:int4){}
%rule x72 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func172(c,c10).
%function func173(c:int4 -> c1:int4){}
%rule x73 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func173(c,c10).
%function func174(c:int4 -> c1:int4){}
%rule x74 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func174(c,c10).
%function func175(c:int4 -> c1:int4){}
%rule x75 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func175(c,c10).
%function func176(c:int4 -> c1:int4){}
%rule x76 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func176(c,c10).
%function func177(c:int4 -> c1:int4){}
%rule x77 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func177(c,c10).
%function func178(c:int4 -> c1:int4){}
%rule x78 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func178(c,c10).
%function func179(c:int4 -> c1:int4){}
%rule x79 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func179(c,c10).
%function func180(c:int4 -> c1:int4){}
%rule x80 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func180(c,c10).
%function func181(c:int4 -> c1:int4){}
%rule x81 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func181(c,c10).
%function func182(c:int4 -> c1:int4){}
%rule x82 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func182(c,c10).
%function func183(c:int4 -> c1:int4){}
%rule x83 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func183(c,c10).
%function func184(c:int4 -> c1:int4){}
%rule x84 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func184(c,c10).
%function func185(c:int4 -> c1:int4){}
%rule x85 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func185(c,c10).
%function func186(c:int4 -> c1:int4){}
%rule x86 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func186(c,c10).
%function func187(c:int4 -> c1:int4){}
%rule x87 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func187(c,c10).
%function func188(c:int4 -> c1:int4){}
%rule x88 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func188(c,c10).
%function func189(c:int4 -> c1:int4){}
%rule x89 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func189(c,c10).
%function func190(c:int4 -> c1:int4){}
%rule x90 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func190(c,c10).
%function func191(c:int4 -> c1:int4){}
%rule x91 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func191(c,c10).
%function func192(c:int4 -> c1:int4){}
%rule x92 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func192(c,c10).
%function func193(c:int4 -> c1:int4){}
%rule x93 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func193(c,c10).
%function func194(c:int4 -> c1:int4){}
%rule x94 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func194(c,c10).
%function func195(c:int4 -> c1:int4){}
%rule x95 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func195(c,c10).
%function func196(c:int4 -> c1:int4){}
%rule x96 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func196(c,c10).
%function func197(c:int4 -> c1:int4){}
%rule x97 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func197(c,c10).
%function func198(c:int4 -> c1:int4){}
%rule x98 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func198(c,c10).
%function func199(c:int4 -> c1:int4){}
%rule x99 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func199(c,c10).
%function func200(c:int4 -> c1:int4){}
%rule r200 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func200(c,c10).
%function func201(c:int4 -> c1:int4){}
%rule r201 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func201(c,c10).
%function func202(c:int4 -> c1:int4){}
%rule r202 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func202(c,c10).
%function func203(c:int4 -> c1:int4){}
%rule r203 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func203(c,c10).
%function func204(c:int4 -> c1:int4){}
%rule r204 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func204(c,c10).
%function func205(c:int4 -> c1:int4){}
%rule r205 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func205(c,c10).
%function func206(c:int4 -> c1:int4){}
%rule r206 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func206(c,c10).
%function func207(c:int4 -> c1:int4){}
%rule r207 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func207(c,c10).
%function func208(c:int4 -> c1:int4){}
%rule r208 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func208(c,c10).
%function func209(c:int4 -> c1:int4){}
%rule r209 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func209(c,c10).
%function func210(c:int4 -> c1:int4){}
%rule r210 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func210(c,c10).
%function func211(c:int4 -> c1:int4){}
%rule r211 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func211(c,c10).
%function func212(c:int4 -> c1:int4){}
%rule r212 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func212(c,c10).
%function func213(c:int4 -> c1:int4){}
%rule r213 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func213(c,c10).
%function func214(c:int4 -> c1:int4){}
%rule r214 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func214(c,c10).
%function func215(c:int4 -> c1:int4){}
%rule r215 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func215(c,c10).
%function func216(c:int4 -> c1:int4){}
%rule r216 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func216(c,c10).
%function func217(c:int4 -> c1:int4){}
%rule r217 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func217(c,c10).
%function func218(c:int4 -> c1:int4){}
%rule r218 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func218(c,c10).
%function func219(c:int4 -> c1:int4){}
%rule r219 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func219(c,c10).
%function func220(c:int4 -> c1:int4){}
%rule r220 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func220(c,c10).
%function func221(c:int4 -> c1:int4){}
%rule r221 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func221(c,c10).
%function func222(c:int4 -> c1:int4){}
%rule r222 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func222(c,c10).
%function func223(c:int4 -> c1:int4){}
%rule r223 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func223(c,c10).
%function func224(c:int4 -> c1:int4){}
%rule r224 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func224(c,c10).
%function func225(c:int4 -> c1:int4){}
%rule r225 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func225(c,c10).
%function func226(c:int4 -> c1:int4){}
%rule r226 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func226(c,c10).
%function func227(c:int4 -> c1:int4){}
%rule r227 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func227(c,c10).
%function func228(c:int4 -> c1:int4){}
%rule r228 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func228(c,c10).
%function func229(c:int4 -> c1:int4){}
%rule r229 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func229(c,c10).
%function func230(c:int4 -> c1:int4){}
%rule r230 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func230(c,c10).
%function func231(c:int4 -> c1:int4){}
%rule r231 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func231(c,c10).
%function func232(c:int4 -> c1:int4){}
%rule r232 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func232(c,c10).
%function func233(c:int4 -> c1:int4){}
%rule r233 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func233(c,c10).
%function func234(c:int4 -> c1:int4){}
%rule r234 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func234(c,c10).
%function func235(c:int4 -> c1:int4){}
%rule r235 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func235(c,c10).
%function func236(c:int4 -> c1:int4){}
%rule r236 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func236(c,c10).
%function func237(c:int4 -> c1:int4){}
%rule r237 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func237(c,c10).
%function func238(c:int4 -> c1:int4){}
%rule r238 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func238(c,c10).
%function func239(c:int4 -> c1:int4){}
%rule r239 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func239(c,c10).
%function func240(c:int4 -> c1:int4){}
%rule r240 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func240(c,c10).
%function func241(c:int4 -> c1:int4){}
%rule r241 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func241(c,c10).
%function func242(c:int4 -> c1:int4){}
%rule r242 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func242(c,c10).
%function func243(c:int4 -> c1:int4){}
%rule r243 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func243(c,c10).
%function func244(c:int4 -> c1:int4){}
%rule r244 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func244(c,c10).
%function func245(c:int4 -> c1:int4){}
%rule r245 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func245(c,c10).
%function func246(c:int4 -> c1:int4){}
%rule r246 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func246(c,c10).
%function func247(c:int4 -> c1:int4){}
%rule r247 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func247(c,c10).
%function func248(c:int4 -> c1:int4){}
%rule r248 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func248(c,c10).
%function func249(c:int4 -> c1:int4){}
%rule r249 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func249(c,c10).
%function func250(c:int4 -> c1:int4){}
%rule r250 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func250(c,c10).
%function func251(c:int4 -> c1:int4){}
%rule r251 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func251(c,c10).
%function func252(c:int4 -> c1:int4){}
%rule r252 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func252(c,c10).
%function func253(c:int4 -> c1:int4){}
%rule r253 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func253(c,c10).
%function func254(c:int4 -> c1:int4){}
%rule r254 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func254(c,c10).
%function func255(c:int4 -> c1:int4){}
%rule r255 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func255(c,c10).
%function func256(c:int4 -> c1:int4){}
%rule r256 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func256(c,c10).
%function func257(c:int4 -> c1:int4){}
%rule r257 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func257(c,c10).
%function func258(c:int4 -> c1:int4){}
%rule r258 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func258(c,c10).
%function func259(c:int4 -> c1:int4){}
%rule r259 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func259(c,c10).
%function func260(c:int4 -> c1:int4){}
%rule r260 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func260(c,c10).
%function func261(c:int4 -> c1:int4){}
%rule r261 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func261(c,c10).
%function func262(c:int4 -> c1:int4){}
%rule r262 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func262(c,c10).
%function func263(c:int4 -> c1:int4){}
%rule r263 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func263(c,c10).
%function func264(c:int4 -> c1:int4){}
%rule r264 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func264(c,c10).
%function func265(c:int4 -> c1:int4){}
%rule r265 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func265(c,c10).
%function func266(c:int4 -> c1:int4){}
%rule r266 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func266(c,c10).
%function func267(c:int4 -> c1:int4){}
%rule r267 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func267(c,c10).
%function func268(c:int4 -> c1:int4){}
%rule r268 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func268(c,c10).
%function func269(c:int4 -> c1:int4){}
%rule r269 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func269(c,c10).
%function func270(c:int4 -> c1:int4){}
%rule r270 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func270(c,c10).
%function func271(c:int4 -> c1:int4){}
%rule r271 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func271(c,c10).
%function func272(c:int4 -> c1:int4){}
%rule r272 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func272(c,c10).
%function func273(c:int4 -> c1:int4){}
%rule r273 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func273(c,c10).
%function func274(c:int4 -> c1:int4){}
%rule r274 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func274(c,c10).
%function func275(c:int4 -> c1:int4){}
%rule r275 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func275(c,c10).
%function func276(c:int4 -> c1:int4){}
%rule r276 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func276(c,c10).
%function func277(c:int4 -> c1:int4){}
%rule r277 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func277(c,c10).
%function func278(c:int4 -> c1:int4){}
%rule r278 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func278(c,c10).
%function func279(c:int4 -> c1:int4){}
%rule r279 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func279(c,c10).
%function func280(c:int4 -> c1:int4){}
%rule r280 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func280(c,c10).
%function func281(c:int4 -> c1:int4){}
%rule r281 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func281(c,c10).
%function func282(c:int4 -> c1:int4){}
%rule r282 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func282(c,c10).
%function func283(c:int4 -> c1:int4){}
%rule r283 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func283(c,c10).
%function func284(c:int4 -> c1:int4){}
%rule r284 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func284(c,c10).
%function func285(c:int4 -> c1:int4){}
%rule r285 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func285(c,c10).
%function func286(c:int4 -> c1:int4){}
%rule r286 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func286(c,c10).
%function func287(c:int4 -> c1:int4){}
%rule r287 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func287(c,c10).
%function func288(c:int4 -> c1:int4){}
%rule r288 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func288(c,c10).
%function func289(c:int4 -> c1:int4){}
%rule r289 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func289(c,c10).
%function func290(c:int4 -> c1:int4){}
%rule r290 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func290(c,c10).
%function func291(c:int4 -> c1:int4){}
%rule r291 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func291(c,c10).
%function func292(c:int4 -> c1:int4){}
%rule r292 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func292(c,c10).
%function func293(c:int4 -> c1:int4){}
%rule r293 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func293(c,c10).
%function func294(c:int4 -> c1:int4){}
%rule r294 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func294(c,c10).
%function func295(c:int4 -> c1:int4){}
%rule r295 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func295(c,c10).
%function func296(c:int4 -> c1:int4){}
%rule r296 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func296(c,c10).
%function func297(c:int4 -> c1:int4){}
%rule r297 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func297(c,c10).
%function func298(c:int4 -> c1:int4){}
%rule r298 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func298(c,c10).
%function func299(c:int4 -> c1:int4){}
%rule r299 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func299(c,c10).
%function func300(c:int4 -> c1:int4){}
%rule r300 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func300(c,c10).
%function func301(c:int4 -> c1:int4){}
%rule r301 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func301(c,c10).
%function func302(c:int4 -> c1:int4){}
%rule r302 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func302(c,c10).
%function func303(c:int4 -> c1:int4){}
%rule r303 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func303(c,c10).
%function func304(c:int4 -> c1:int4){}
%rule r304 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func304(c,c10).
%function func305(c:int4 -> c1:int4){}
%rule r305 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func305(c,c10).
%function func306(c:int4 -> c1:int4){}
%rule r306 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func306(c,c10).
%function func307(c:int4 -> c1:int4){}
%rule r307 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func307(c,c10).
%function func308(c:int4 -> c1:int4){}
%rule r308 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func308(c,c10).
%function func309(c:int4 -> c1:int4){}
%rule r309 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func309(c,c10).
%function func310(c:int4 -> c1:int4){}
%rule r310 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func310(c,c10).
%function func311(c:int4 -> c1:int4){}
%rule r311 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func311(c,c10).
%function func312(c:int4 -> c1:int4){}
%rule r312 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func312(c,c10).
%function func313(c:int4 -> c1:int4){}
%rule r313 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func313(c,c10).
%function func314(c:int4 -> c1:int4){}
%rule r314 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func314(c,c10).
%function func315(c:int4 -> c1:int4){}
%rule r315 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func315(c,c10).
%function func316(c:int4 -> c1:int4){}
%rule r316 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func316(c,c10).
%function func317(c:int4 -> c1:int4){}
%rule r317 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func317(c,c10).
%function func318(c:int4 -> c1:int4){}
%rule r318 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func318(c,c10).
%function func319(c:int4 -> c1:int4){}
%rule r319 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func319(c,c10).
%function func320(c:int4 -> c1:int4){}
%rule r320 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func320(c,c10).
%function func321(c:int4 -> c1:int4){}
%rule r321 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func321(c,c10).
%function func322(c:int4 -> c1:int4){}
%rule r322 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func322(c,c10).
%function func323(c:int4 -> c1:int4){}
%rule r323 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func323(c,c10).
%function func324(c:int4 -> c1:int4){}
%rule r324 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func324(c,c10).
%function func325(c:int4 -> c1:int4){}
%rule r325 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func325(c,c10).
%function func326(c:int4 -> c1:int4){}
%rule r326 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func326(c,c10).
%function func327(c:int4 -> c1:int4){}
%rule r327 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func327(c,c10).
%function func328(c:int4 -> c1:int4){}
%rule r328 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func328(c,c10).
%function func329(c:int4 -> c1:int4){}
%rule r329 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func329(c,c10).
%function func330(c:int4 -> c1:int4){}
%rule r330 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func330(c,c10).
%function func331(c:int4 -> c1:int4){}
%rule r331 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func331(c,c10).
%function func332(c:int4 -> c1:int4){}
%rule r332 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func332(c,c10).
%function func333(c:int4 -> c1:int4){}
%rule r333 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func333(c,c10).
%function func334(c:int4 -> c1:int4){}
%rule r334 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func334(c,c10).
%function func335(c:int4 -> c1:int4){}
%rule r335 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func335(c,c10).
%function func336(c:int4 -> c1:int4){}
%rule r336 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func336(c,c10).
%function func337(c:int4 -> c1:int4){}
%rule r337 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func337(c,c10).
%function func338(c:int4 -> c1:int4){}
%rule r338 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func338(c,c10).
%function func339(c:int4 -> c1:int4){}
%rule r339 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func339(c,c10).
%function func340(c:int4 -> c1:int4){}
%rule r340 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func340(c,c10).
%function func341(c:int4 -> c1:int4){}
%rule r341 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func341(c,c10).
%function func342(c:int4 -> c1:int4){}
%rule r342 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func342(c,c10).
%function func343(c:int4 -> c1:int4){}
%rule r343 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func343(c,c10).
%function func344(c:int4 -> c1:int4){}
%rule r344 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func344(c,c10).
%function func345(c:int4 -> c1:int4){}
%rule r345 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func345(c,c10).
%function func346(c:int4 -> c1:int4){}
%rule r346 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func346(c,c10).
%function func347(c:int4 -> c1:int4){}
%rule r347 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func347(c,c10).
%function func348(c:int4 -> c1:int4){}
%rule r348 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func348(c,c10).
%function func349(c:int4 -> c1:int4){}
%rule r349 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func349(c,c10).
%function func350(c:int4 -> c1:int4){}
%rule r350 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func350(c,c10).
%function func351(c:int4 -> c1:int4){}
%rule r351 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func351(c,c10).
%function func352(c:int4 -> c1:int4){}
%rule r352 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func352(c,c10).
%function func353(c:int4 -> c1:int4){}
%rule r353 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func353(c,c10).
%function func354(c:int4 -> c1:int4){}
%rule r354 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func354(c,c10).
%function func355(c:int4 -> c1:int4){}
%rule r355 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func355(c,c10).
%function func356(c:int4 -> c1:int4){}
%rule r356 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func356(c,c10).
%function func357(c:int4 -> c1:int4){}
%rule r357 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func357(c,c10).
%function func358(c:int4 -> c1:int4){}
%rule r358 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func358(c,c10).
%function func359(c:int4 -> c1:int4){}
%rule r359 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func359(c,c10).
%function func360(c:int4 -> c1:int4){}
%rule r360 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func360(c,c10).
%function func361(c:int4 -> c1:int4){}
%rule r361 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func361(c,c10).
%function func362(c:int4 -> c1:int4){}
%rule r362 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func362(c,c10).
%function func363(c:int4 -> c1:int4){}
%rule r363 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func363(c,c10).
%function func364(c:int4 -> c1:int4){}
%rule r364 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func364(c,c10).
%function func365(c:int4 -> c1:int4){}
%rule r365 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func365(c,c10).
%function func366(c:int4 -> c1:int4){}
%rule r366 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func366(c,c10).
%function func367(c:int4 -> c1:int4){}
%rule r367 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func367(c,c10).
%function func368(c:int4 -> c1:int4){}
%rule r368 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func368(c,c10).
%function func369(c:int4 -> c1:int4){}
%rule r369 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func369(c,c10).
%function func370(c:int4 -> c1:int4){}
%rule r370 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func370(c,c10).
%function func371(c:int4 -> c1:int4){}
%rule r371 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func371(c,c10).
%function func372(c:int4 -> c1:int4){}
%rule r372 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func372(c,c10).
%function func373(c:int4 -> c1:int4){}
%rule r373 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func373(c,c10).
%function func374(c:int4 -> c1:int4){}
%rule r374 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func374(c,c10).
%function func375(c:int4 -> c1:int4){}
%rule r375 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func375(c,c10).
%function func376(c:int4 -> c1:int4){}
%rule r376 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func376(c,c10).
%function func377(c:int4 -> c1:int4){}
%rule r377 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func377(c,c10).
%function func378(c:int4 -> c1:int4){}
%rule r378 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func378(c,c10).
%function func379(c:int4 -> c1:int4){}
%rule r379 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func379(c,c10).
%function func380(c:int4 -> c1:int4){}
%rule r380 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func380(c,c10).
%function func381(c:int4 -> c1:int4){}
%rule r381 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func381(c,c10).
%function func382(c:int4 -> c1:int4){}
%rule r382 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func382(c,c10).
%function func383(c:int4 -> c1:int4){}
%rule r383 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func383(c,c10).
%function func384(c:int4 -> c1:int4){}
%rule r384 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func384(c,c10).
%function func385(c:int4 -> c1:int4){}
%rule r385 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func385(c,c10).
%function func386(c:int4 -> c1:int4){}
%rule r386 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func386(c,c10).
%function func387(c:int4 -> c1:int4){}
%rule r387 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func387(c,c10).
%function func388(c:int4 -> c1:int4){}
%rule r388 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func388(c,c10).
%function func389(c:int4 -> c1:int4){}
%rule r389 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func389(c,c10).
%function func390(c:int4 -> c1:int4){}
%rule r390 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func390(c,c10).
%function func391(c:int4 -> c1:int4){}
%rule r391 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func391(c,c10).
%function func392(c:int4 -> c1:int4){}
%rule r392 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func392(c,c10).
%function func393(c:int4 -> c1:int4){}
%rule r393 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func393(c,c10).
%function func394(c:int4 -> c1:int4){}
%rule r394 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func394(c,c10).
%function func395(c:int4 -> c1:int4){}
%rule r395 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func395(c,c10).
%function func396(c:int4 -> c1:int4){}
%rule r396 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func396(c,c10).
%function func397(c:int4 -> c1:int4){}
%rule r397 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func397(c,c10).
%function func398(c:int4 -> c1:int4){}
%rule r398 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func398(c,c10).
%function func399(c:int4 -> c1:int4){}
%rule r399 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func399(c,c10).
%function func400(c:int4 -> c1:int4){}
%rule r400 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func400(c,c10).
%function func401(c:int4 -> c1:int4){}
%rule r401 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func401(c,c10).
%function func402(c:int4 -> c1:int4){}
%rule r402 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func402(c,c10).
%function func403(c:int4 -> c1:int4){}
%rule r403 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func403(c,c10).
%function func404(c:int4 -> c1:int4){}
%rule r404 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func404(c,c10).
%function func405(c:int4 -> c1:int4){}
%rule r405 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func405(c,c10).
%function func406(c:int4 -> c1:int4){}
%rule r406 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func406(c,c10).
%function func407(c:int4 -> c1:int4){}
%rule r407 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func407(c,c10).
%function func408(c:int4 -> c1:int4){}
%rule r408 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func408(c,c10).
%function func409(c:int4 -> c1:int4){}
%rule r409 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func409(c,c10).
%function func410(c:int4 -> c1:int4){}
%rule r410 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func410(c,c10).
%function func411(c:int4 -> c1:int4){}
%rule r411 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func411(c,c10).
%function func412(c:int4 -> c1:int4){}
%rule r412 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func412(c,c10).
%function func413(c:int4 -> c1:int4){}
%rule r413 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func413(c,c10).
%function func414(c:int4 -> c1:int4){}
%rule r414 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func414(c,c10).
%function func415(c:int4 -> c1:int4){}
%rule r415 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func415(c,c10).
%function func416(c:int4 -> c1:int4){}
%rule r416 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func416(c,c10).
%function func417(c:int4 -> c1:int4){}
%rule r417 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func417(c,c10).
%function func418(c:int4 -> c1:int4){}
%rule r418 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func418(c,c10).
%function func419(c:int4 -> c1:int4){}
%rule r419 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func419(c,c10).
%function func420(c:int4 -> c1:int4){}
%rule r420 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func420(c,c10).
%function func421(c:int4 -> c1:int4){}
%rule r421 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func421(c,c10).
%function func422(c:int4 -> c1:int4){}
%rule r422 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func422(c,c10).
%function func423(c:int4 -> c1:int4){}
%rule r423 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func423(c,c10).
%function func424(c:int4 -> c1:int4){}
%rule r424 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func424(c,c10).
%function func425(c:int4 -> c1:int4){}
%rule r425 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func425(c,c10).
%function func426(c:int4 -> c1:int4){}
%rule r426 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func426(c,c10).
%function func427(c:int4 -> c1:int4){}
%rule r427 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func427(c,c10).
%function func428(c:int4 -> c1:int4){}
%rule r428 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func428(c,c10).
%function func429(c:int4 -> c1:int4){}
%rule r429 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func429(c,c10).
%function func430(c:int4 -> c1:int4){}
%rule r430 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func430(c,c10).
%function func431(c:int4 -> c1:int4){}
%rule r431 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func431(c,c10).
%function func432(c:int4 -> c1:int4){}
%rule r432 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func432(c,c10).
%function func433(c:int4 -> c1:int4){}
%rule r433 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func433(c,c10).
%function func434(c:int4 -> c1:int4){}
%rule r434 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func434(c,c10).
%function func435(c:int4 -> c1:int4){}
%rule r435 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func435(c,c10).
%function func436(c:int4 -> c1:int4){}
%rule r436 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func436(c,c10).
%function func437(c:int4 -> c1:int4){}
%rule r437 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func437(c,c10).
%function func438(c:int4 -> c1:int4){}
%rule r438 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func438(c,c10).
%function func439(c:int4 -> c1:int4){}
%rule r439 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func439(c,c10).
%function func440(c:int4 -> c1:int4){}
%rule r440 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func440(c,c10).
%function func441(c:int4 -> c1:int4){}
%rule r441 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func441(c,c10).
%function func442(c:int4 -> c1:int4){}
%rule r442 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func442(c,c10).
%function func443(c:int4 -> c1:int4){}
%rule r443 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func443(c,c10).
%function func444(c:int4 -> c1:int4){}
%rule r444 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func444(c,c10).
%function func445(c:int4 -> c1:int4){}
%rule r445 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func445(c,c10).
%function func446(c:int4 -> c1:int4){}
%rule r446 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func446(c,c10).
%function func447(c:int4 -> c1:int4){}
%rule r447 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func447(c,c10).
%function func448(c:int4 -> c1:int4){}
%rule r448 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func448(c,c10).
%function func449(c:int4 -> c1:int4){}
%rule r449 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func449(c,c10).
%function func450(c:int4 -> c1:int4){}
%rule r450 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func450(c,c10).
%function func451(c:int4 -> c1:int4){}
%rule r451 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func451(c,c10).
%function func452(c:int4 -> c1:int4){}
%rule r452 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func452(c,c10).
%function func453(c:int4 -> c1:int4){}
%rule r453 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func453(c,c10).
%function func454(c:int4 -> c1:int4){}
%rule r454 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func454(c,c10).
%function func455(c:int4 -> c1:int4){}
%rule r455 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func455(c,c10).
%function func456(c:int4 -> c1:int4){}
%rule r456 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func456(c,c10).
%function func457(c:int4 -> c1:int4){}
%rule r457 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func457(c,c10).
%function func458(c:int4 -> c1:int4){}
%rule r458 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func458(c,c10).
%function func459(c:int4 -> c1:int4){}
%rule r459 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func459(c,c10).
%function func460(c:int4 -> c1:int4){}
%rule r460 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func460(c,c10).
%function func461(c:int4 -> c1:int4){}
%rule r461 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func461(c,c10).
%function func462(c:int4 -> c1:int4){}
%rule r462 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func462(c,c10).
%function func463(c:int4 -> c1:int4){}
%rule r463 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func463(c,c10).
%function func464(c:int4 -> c1:int4){}
%rule r464 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func464(c,c10).
%function func465(c:int4 -> c1:int4){}
%rule r465 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func465(c,c10).
%function func466(c:int4 -> c1:int4){}
%rule r466 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func466(c,c10).
%function func467(c:int4 -> c1:int4){}
%rule r467 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func467(c,c10).
%function func468(c:int4 -> c1:int4){}
%rule r468 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func468(c,c10).
%function func469(c:int4 -> c1:int4){}
%rule r469 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func469(c,c10).
%function func470(c:int4 -> c1:int4){}
%rule r470 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func470(c,c10).
%function func471(c:int4 -> c1:int4){}
%rule r471 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func471(c,c10).
%function func472(c:int4 -> c1:int4){}
%rule r472 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func472(c,c10).
%function func473(c:int4 -> c1:int4){}
%rule r473 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func473(c,c10).
%function func474(c:int4 -> c1:int4){}
%rule r474 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func474(c,c10).
%function func475(c:int4 -> c1:int4){}
%rule r475 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func475(c,c10).
%function func476(c:int4 -> c1:int4){}
%rule r476 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func476(c,c10).
%function func477(c:int4 -> c1:int4){}
%rule r477 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func477(c,c10).
%function func478(c:int4 -> c1:int4){}
%rule r478 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func478(c,c10).
%function func479(c:int4 -> c1:int4){}
%rule r479 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func479(c,c10).
%function func480(c:int4 -> c1:int4){}
%rule r480 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func480(c,c10).
%function func481(c:int4 -> c1:int4){}
%rule r481 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func481(c,c10).
%function func482(c:int4 -> c1:int4){}
%rule r482 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func482(c,c10).
%function func483(c:int4 -> c1:int4){}
%rule r483 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func483(c,c10).
%function func484(c:int4 -> c1:int4){}
%rule r484 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func484(c,c10).
%function func485(c:int4 -> c1:int4){}
%rule r485 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func485(c,c10).
%function func486(c:int4 -> c1:int4){}
%rule r486 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func486(c,c10).
%function func487(c:int4 -> c1:int4){}
%rule r487 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func487(c,c10).
%function func488(c:int4 -> c1:int4){}
%rule r488 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func488(c,c10).
%function func489(c:int4 -> c1:int4){}
%rule r489 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func489(c,c10).
%function func490(c:int4 -> c1:int4){}
%rule r490 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func490(c,c10).
%function func491(c:int4 -> c1:int4){}
%rule r491 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func491(c,c10).
%function func492(c:int4 -> c1:int4){}
%rule r492 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func492(c,c10).
%function func493(c:int4 -> c1:int4){}
%rule r493 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func493(c,c10).
%function func494(c:int4 -> c1:int4){}
%rule r494 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func494(c,c10).
%function func495(c:int4 -> c1:int4){}
%rule r495 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func495(c,c10).
%function func496(c:int4 -> c1:int4){}
%rule r496 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func496(c,c10).
%function func497(c:int4 -> c1:int4){}
%rule r497 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func497(c,c10).
%function func498(c:int4 -> c1:int4){}
%rule r498 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func498(c,c10).
%function func499(c:int4 -> c1:int4){}
%rule r499 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func499(c,c10).
%function func500(c:int4 -> c1:int4){}
%rule r500 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func500(c,c10).
%function func501(c:int4 -> c1:int4){}
%rule r501 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func501(c,c10).
%function func502(c:int4 -> c1:int4){}
%rule r502 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func502(c,c10).
%function func503(c:int4 -> c1:int4){}
%rule r503 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func503(c,c10).
%function func504(c:int4 -> c1:int4){}
%rule r504 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func504(c,c10).
%function func505(c:int4 -> c1:int4){}
%rule r505 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func505(c,c10).
%function func506(c:int4 -> c1:int4){}
%rule r506 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func506(c,c10).
%function func507(c:int4 -> c1:int4){}
%rule r507 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func507(c,c10).
%function func508(c:int4 -> c1:int4){}
%rule r508 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func508(c,c10).
%function func509(c:int4 -> c1:int4){}
%rule r509 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func509(c,c10).
%function func510(c:int4 -> c1:int4){}
%rule r510 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func510(c,c10).
%function func511(c:int4 -> c1:int4){}
%rule r511 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func511(c,c10).
%function func512(c:int4 -> c1:int4){}
%rule r512 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func512(c,c10).
%function func513(c:int4 -> c1:int4){}
%rule r513 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func513(c,c10).
%function func514(c:int4 -> c1:int4){}
%rule r514 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func514(c,c10).
%function func515(c:int4 -> c1:int4){}
%rule r515 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func515(c,c10).
%function func516(c:int4 -> c1:int4){}
%rule r516 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func516(c,c10).
%function func517(c:int4 -> c1:int4){}
%rule r517 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func517(c,c10).
%function func518(c:int4 -> c1:int4){}
%rule r518 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func518(c,c10).
%function func519(c:int4 -> c1:int4){}
%rule r519 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func519(c,c10).
%function func520(c:int4 -> c1:int4){}
%rule r520 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func520(c,c10).
%function func521(c:int4 -> c1:int4){}
%rule r521 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func521(c,c10).
%function func522(c:int4 -> c1:int4){}
%rule r522 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func522(c,c10).
%function func523(c:int4 -> c1:int4){}
%rule r523 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func523(c,c10).
%function func524(c:int4 -> c1:int4){}
%rule r524 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func524(c,c10).
%function func525(c:int4 -> c1:int4){}
%rule r525 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func525(c,c10).
%function func526(c:int4 -> c1:int4){}
%rule r526 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func526(c,c10).
%function func527(c:int4 -> c1:int4){}
%rule r527 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func527(c,c10).
%function func528(c:int4 -> c1:int4){}
%rule r528 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func528(c,c10).
%function func529(c:int4 -> c1:int4){}
%rule r529 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func529(c,c10).
%function func530(c:int4 -> c1:int4){}
%rule r530 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func530(c,c10).
%function func531(c:int4 -> c1:int4){}
%rule r531 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func531(c,c10).
%function func532(c:int4 -> c1:int4){}
%rule r532 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func532(c,c10).
%function func533(c:int4 -> c1:int4){}
%rule r533 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func533(c,c10).
%function func534(c:int4 -> c1:int4){}
%rule r534 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func534(c,c10).
%function func535(c:int4 -> c1:int4){}
%rule r535 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func535(c,c10).
%function func536(c:int4 -> c1:int4){}
%rule r536 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func536(c,c10).
%function func537(c:int4 -> c1:int4){}
%rule r537 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func537(c,c10).
%function func538(c:int4 -> c1:int4){}
%rule r538 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func538(c,c10).
%function func539(c:int4 -> c1:int4){}
%rule r539 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func539(c,c10).
%function func540(c:int4 -> c1:int4){}
%rule r540 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func540(c,c10).
%function func541(c:int4 -> c1:int4){}
%rule r541 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func541(c,c10).
%function func542(c:int4 -> c1:int4){}
%rule r542 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func542(c,c10).
%function func543(c:int4 -> c1:int4){}
%rule r543 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func543(c,c10).
%function func544(c:int4 -> c1:int4){}
%rule r544 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func544(c,c10).
%function func545(c:int4 -> c1:int4){}
%rule r545 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func545(c,c10).
%function func546(c:int4 -> c1:int4){}
%rule r546 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func546(c,c10).
%function func547(c:int4 -> c1:int4){}
%rule r547 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func547(c,c10).
%function func548(c:int4 -> c1:int4){}
%rule r548 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func548(c,c10).
%function func549(c:int4 -> c1:int4){}
%rule r549 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func549(c,c10).
%function func550(c:int4 -> c1:int4){}
%rule r550 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func550(c,c10).
%function func551(c:int4 -> c1:int4){}
%rule r551 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func551(c,c10).
%function func552(c:int4 -> c1:int4){}
%rule r552 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func552(c,c10).
%function func553(c:int4 -> c1:int4){}
%rule r553 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func553(c,c10).
%function func554(c:int4 -> c1:int4){}
%rule r554 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func554(c,c10).
%function func555(c:int4 -> c1:int4){}
%rule r555 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func555(c,c10).
%function func556(c:int4 -> c1:int4){}
%rule r556 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func556(c,c10).
%function func557(c:int4 -> c1:int4){}
%rule r557 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func557(c,c10).
%function func558(c:int4 -> c1:int4){}
%rule r558 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func558(c,c10).
%function func559(c:int4 -> c1:int4){}
%rule r559 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func559(c,c10).
%function func560(c:int4 -> c1:int4){}
%rule r560 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func560(c,c10).
%function func561(c:int4 -> c1:int4){}
%rule r561 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func561(c,c10).
%function func562(c:int4 -> c1:int4){}
%rule r562 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func562(c,c10).
%function func563(c:int4 -> c1:int4){}
%rule r563 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func563(c,c10).
%function func564(c:int4 -> c1:int4){}
%rule r564 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func564(c,c10).
%function func565(c:int4 -> c1:int4){}
%rule r565 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func565(c,c10).
%function func566(c:int4 -> c1:int4){}
%rule r566 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func566(c,c10).
%function func567(c:int4 -> c1:int4){}
%rule r567 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func567(c,c10).
%function func568(c:int4 -> c1:int4){}
%rule r568 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func568(c,c10).
%function func569(c:int4 -> c1:int4){}
%rule r569 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func569(c,c10).
%function func570(c:int4 -> c1:int4){}
%rule r570 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func570(c,c10).
%function func571(c:int4 -> c1:int4){}
%rule r571 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func571(c,c10).
%function func572(c:int4 -> c1:int4){}
%rule r572 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func572(c,c10).
%function func573(c:int4 -> c1:int4){}
%rule r573 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func573(c,c10).
%function func574(c:int4 -> c1:int4){}
%rule r574 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func574(c,c10).
%function func575(c:int4 -> c1:int4){}
%rule r575 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func575(c,c10).
%function func576(c:int4 -> c1:int4){}
%rule r576 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func576(c,c10).
%function func577(c:int4 -> c1:int4){}
%rule r577 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func577(c,c10).
%function func578(c:int4 -> c1:int4){}
%rule r578 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func578(c,c10).
%function func579(c:int4 -> c1:int4){}
%rule r579 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func579(c,c10).
%function func580(c:int4 -> c1:int4){}
%rule r580 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func580(c,c10).
%function func581(c:int4 -> c1:int4){}
%rule r581 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func581(c,c10).
%function func582(c:int4 -> c1:int4){}
%rule r582 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func582(c,c10).
%function func583(c:int4 -> c1:int4){}
%rule r583 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func583(c,c10).
%function func584(c:int4 -> c1:int4){}
%rule r584 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func584(c,c10).
%function func585(c:int4 -> c1:int4){}
%rule r585 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func585(c,c10).
%function func586(c:int4 -> c1:int4){}
%rule r586 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func586(c,c10).
%function func587(c:int4 -> c1:int4){}
%rule r587 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func587(c,c10).
%function func588(c:int4 -> c1:int4){}
%rule r588 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func588(c,c10).
%function func589(c:int4 -> c1:int4){}
%rule r589 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func589(c,c10).
%function func590(c:int4 -> c1:int4){}
%rule r590 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func590(c,c10).
%function func591(c:int4 -> c1:int4){}
%rule r591 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func591(c,c10).
%function func592(c:int4 -> c1:int4){}
%rule r592 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func592(c,c10).
%function func593(c:int4 -> c1:int4){}
%rule r593 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func593(c,c10).
%function func594(c:int4 -> c1:int4){}
%rule r594 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func594(c,c10).
%function func595(c:int4 -> c1:int4){}
%rule r595 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func595(c,c10).
%function func596(c:int4 -> c1:int4){}
%rule r596 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func596(c,c10).
%function func597(c:int4 -> c1:int4){}
%rule r597 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func597(c,c10).
%function func598(c:int4 -> c1:int4){}
%rule r598 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func598(c,c10).
%function func599(c:int4 -> c1:int4){}
%rule r599 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func599(c,c10).
%function func600(c:int4 -> c1:int4){}
%rule r600 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func600(c,c10).
%function func601(c:int4 -> c1:int4){}
%rule r601 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func601(c,c10).
%function func602(c:int4 -> c1:int4){}
%rule r602 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func602(c,c10).
%function func603(c:int4 -> c1:int4){}
%rule r603 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func603(c,c10).
%function func604(c:int4 -> c1:int4){}
%rule r604 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func604(c,c10).
%function func605(c:int4 -> c1:int4){}
%rule r605 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func605(c,c10).
%function func606(c:int4 -> c1:int4){}
%rule r606 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func606(c,c10).
%function func607(c:int4 -> c1:int4){}
%rule r607 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func607(c,c10).
%function func608(c:int4 -> c1:int4){}
%rule r608 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func608(c,c10).
%function func609(c:int4 -> c1:int4){}
%rule r609 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func609(c,c10).
%function func610(c:int4 -> c1:int4){}
%rule r610 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func610(c,c10).
%function func611(c:int4 -> c1:int4){}
%rule r611 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func611(c,c10).
%function func612(c:int4 -> c1:int4){}
%rule r612 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func612(c,c10).
%function func613(c:int4 -> c1:int4){}
%rule r613 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func613(c,c10).
%function func614(c:int4 -> c1:int4){}
%rule r614 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func614(c,c10).
%function func615(c:int4 -> c1:int4){}
%rule r615 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func615(c,c10).
%function func616(c:int4 -> c1:int4){}
%rule r616 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func616(c,c10).
%function func617(c:int4 -> c1:int4){}
%rule r617 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func617(c,c10).
%function func618(c:int4 -> c1:int4){}
%rule r618 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func618(c,c10).
%function func619(c:int4 -> c1:int4){}
%rule r619 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func619(c,c10).
%function func620(c:int4 -> c1:int4){}
%rule r620 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func620(c,c10).
%function func621(c:int4 -> c1:int4){}
%rule r621 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func621(c,c10).
%function func622(c:int4 -> c1:int4){}
%rule r622 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func622(c,c10).
%function func623(c:int4 -> c1:int4){}
%rule r623 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func623(c,c10).
%function func624(c:int4 -> c1:int4){}
%rule r624 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func624(c,c10).
%function func625(c:int4 -> c1:int4){}
%rule r625 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func625(c,c10).
%function func626(c:int4 -> c1:int4){}
%rule r626 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func626(c,c10).
%function func627(c:int4 -> c1:int4){}
%rule r627 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func627(c,c10).
%function func628(c:int4 -> c1:int4){}
%rule r628 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func628(c,c10).
%function func629(c:int4 -> c1:int4){}
%rule r629 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func629(c,c10).
%function func630(c:int4 -> c1:int4){}
%rule r630 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func630(c,c10).
%function func631(c:int4 -> c1:int4){}
%rule r631 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func631(c,c10).
%function func632(c:int4 -> c1:int4){}
%rule r632 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func632(c,c10).
%function func633(c:int4 -> c1:int4){}
%rule r633 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func633(c,c10).
%function func634(c:int4 -> c1:int4){}
%rule r634 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func634(c,c10).
%function func635(c:int4 -> c1:int4){}
%rule r635 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func635(c,c10).
%function func636(c:int4 -> c1:int4){}
%rule r636 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func636(c,c10).
%function func637(c:int4 -> c1:int4){}
%rule r637 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func637(c,c10).
%function func638(c:int4 -> c1:int4){}
%rule r638 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func638(c,c10).
%function func639(c:int4 -> c1:int4){}
%rule r639 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func639(c,c10).
%function func640(c:int4 -> c1:int4){}
%rule r640 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func640(c,c10).
%function func641(c:int4 -> c1:int4){}
%rule r641 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func641(c,c10).
%function func642(c:int4 -> c1:int4){}
%rule r642 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func642(c,c10).
%function func643(c:int4 -> c1:int4){}
%rule r643 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func643(c,c10).
%function func644(c:int4 -> c1:int4){}
%rule r644 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func644(c,c10).
%function func645(c:int4 -> c1:int4){}
%rule r645 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func645(c,c10).
%function func646(c:int4 -> c1:int4){}
%rule r646 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func646(c,c10).
%function func647(c:int4 -> c1:int4){}
%rule r647 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func647(c,c10).
%function func648(c:int4 -> c1:int4){}
%rule r648 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func648(c,c10).
%function func649(c:int4 -> c1:int4){}
%rule r649 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func649(c,c10).
%function func650(c:int4 -> c1:int4){}
%rule r650 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func650(c,c10).
%function func651(c:int4 -> c1:int4){}
%rule r651 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func651(c,c10).
%function func652(c:int4 -> c1:int4){}
%rule r652 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func652(c,c10).
%function func653(c:int4 -> c1:int4){}
%rule r653 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func653(c,c10).
%function func654(c:int4 -> c1:int4){}
%rule r654 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func654(c,c10).
%function func655(c:int4 -> c1:int4){}
%rule r655 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func655(c,c10).
%function func656(c:int4 -> c1:int4){}
%rule r656 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func656(c,c10).
%function func657(c:int4 -> c1:int4){}
%rule r657 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func657(c,c10).
%function func658(c:int4 -> c1:int4){}
%rule r658 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func658(c,c10).
%function func659(c:int4 -> c1:int4){}
%rule r659 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func659(c,c10).
%function func660(c:int4 -> c1:int4){}
%rule r660 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func660(c,c10).
%function func661(c:int4 -> c1:int4){}
%rule r661 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func661(c,c10).
%function func662(c:int4 -> c1:int4){}
%rule r662 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func662(c,c10).
%function func663(c:int4 -> c1:int4){}
%rule r663 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func663(c,c10).
%function func664(c:int4 -> c1:int4){}
%rule r664 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func664(c,c10).
%function func665(c:int4 -> c1:int4){}
%rule r665 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func665(c,c10).
%function func666(c:int4 -> c1:int4){}
%rule r666 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func666(c,c10).
%function func667(c:int4 -> c1:int4){}
%rule r667 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func667(c,c10).
%function func668(c:int4 -> c1:int4){}
%rule r668 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func668(c,c10).
%function func669(c:int4 -> c1:int4){}
%rule r669 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func669(c,c10).
%function func670(c:int4 -> c1:int4){}
%rule r670 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func670(c,c10).
%function func671(c:int4 -> c1:int4){}
%rule r671 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func671(c,c10).
%function func672(c:int4 -> c1:int4){}
%rule r672 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func672(c,c10).
%function func673(c:int4 -> c1:int4){}
%rule r673 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func673(c,c10).
%function func674(c:int4 -> c1:int4){}
%rule r674 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func674(c,c10).
%function func675(c:int4 -> c1:int4){}
%rule r675 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func675(c,c10).
%function func676(c:int4 -> c1:int4){}
%rule r676 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func676(c,c10).
%function func677(c:int4 -> c1:int4){}
%rule r677 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func677(c,c10).
%function func678(c:int4 -> c1:int4){}
%rule r678 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func678(c,c10).
%function func679(c:int4 -> c1:int4){}
%rule r679 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func679(c,c10).
%function func680(c:int4 -> c1:int4){}
%rule r680 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func680(c,c10).
%function func681(c:int4 -> c1:int4){}
%rule r681 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func681(c,c10).
%function func682(c:int4 -> c1:int4){}
%rule r682 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func682(c,c10).
%function func683(c:int4 -> c1:int4){}
%rule r683 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func683(c,c10).
%function func684(c:int4 -> c1:int4){}
%rule r684 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func684(c,c10).
%function func685(c:int4 -> c1:int4){}
%rule r685 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func685(c,c10).
%function func686(c:int4 -> c1:int4){}
%rule r686 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func686(c,c10).
%function func687(c:int4 -> c1:int4){}
%rule r687 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func687(c,c10).
%function func688(c:int4 -> c1:int4){}
%rule r688 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func688(c,c10).
%function func689(c:int4 -> c1:int4){}
%rule r689 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func689(c,c10).
%function func690(c:int4 -> c1:int4){}
%rule r690 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func690(c,c10).
%function func691(c:int4 -> c1:int4){}
%rule r691 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func691(c,c10).
%function func692(c:int4 -> c1:int4){}
%rule r692 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func692(c,c10).
%function func693(c:int4 -> c1:int4){}
%rule r693 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func693(c,c10).
%function func694(c:int4 -> c1:int4){}
%rule r694 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func694(c,c10).
%function func695(c:int4 -> c1:int4){}
%rule r695 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func695(c,c10).
%function func696(c:int4 -> c1:int4){}
%rule r696 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func696(c,c10).
%function func697(c:int4 -> c1:int4){}
%rule r697 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func697(c,c10).
%function func698(c:int4 -> c1:int4){}
%rule r698 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func698(c,c10).
%function func699(c:int4 -> c1:int4){}
%rule r699 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func699(c,c10).
%function func700(c:int4 -> c1:int4){}
%rule r700 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func700(c,c10).
%function func701(c:int4 -> c1:int4){}
%rule r701 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func701(c,c10).
%function func702(c:int4 -> c1:int4){}
%rule r702 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func702(c,c10).
%function func703(c:int4 -> c1:int4){}
%rule r703 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func703(c,c10).
%function func704(c:int4 -> c1:int4){}
%rule r704 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func704(c,c10).
%function func705(c:int4 -> c1:int4){}
%rule r705 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func705(c,c10).
%function func706(c:int4 -> c1:int4){}
%rule r706 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func706(c,c10).
%function func707(c:int4 -> c1:int4){}
%rule r707 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func707(c,c10).
%function func708(c:int4 -> c1:int4){}
%rule r708 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func708(c,c10).
%function func709(c:int4 -> c1:int4){}
%rule r709 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func709(c,c10).
%function func710(c:int4 -> c1:int4){}
%rule r710 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func710(c,c10).
%function func711(c:int4 -> c1:int4){}
%rule r711 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func711(c,c10).
%function func712(c:int4 -> c1:int4){}
%rule r712 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func712(c,c10).
%function func713(c:int4 -> c1:int4){}
%rule r713 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func713(c,c10).
%function func714(c:int4 -> c1:int4){}
%rule r714 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func714(c,c10).
%function func715(c:int4 -> c1:int4){}
%rule r715 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func715(c,c10).
%function func716(c:int4 -> c1:int4){}
%rule r716 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func716(c,c10).
%function func717(c:int4 -> c1:int4){}
%rule r717 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func717(c,c10).
%function func718(c:int4 -> c1:int4){}
%rule r718 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func718(c,c10).
%function func719(c:int4 -> c1:int4){}
%rule r719 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func719(c,c10).
%function func720(c:int4 -> c1:int4){}
%rule r720 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func720(c,c10).
%function func721(c:int4 -> c1:int4){}
%rule r721 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func721(c,c10).
%function func722(c:int4 -> c1:int4){}
%rule r722 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func722(c,c10).
%function func723(c:int4 -> c1:int4){}
%rule r723 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func723(c,c10).
%function func724(c:int4 -> c1:int4){}
%rule r724 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func724(c,c10).
%function func725(c:int4 -> c1:int4){}
%rule r725 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func725(c,c10).
%function func726(c:int4 -> c1:int4){}
%rule r726 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func726(c,c10).
%function func727(c:int4 -> c1:int4){}
%rule r727 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func727(c,c10).
%function func728(c:int4 -> c1:int4){}
%rule r728 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func728(c,c10).
%function func729(c:int4 -> c1:int4){}
%rule r729 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func729(c,c10).
%function func730(c:int4 -> c1:int4){}
%rule r730 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func730(c,c10).
%function func731(c:int4 -> c1:int4){}
%rule r731 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func731(c,c10).
%function func732(c:int4 -> c1:int4){}
%rule r732 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func732(c,c10).
%function func733(c:int4 -> c1:int4){}
%rule r733 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func733(c,c10).
%function func734(c:int4 -> c1:int4){}
%rule r734 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func734(c,c10).
%function func735(c:int4 -> c1:int4){}
%rule r735 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func735(c,c10).
%function func736(c:int4 -> c1:int4){}
%rule r736 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func736(c,c10).
%function func737(c:int4 -> c1:int4){}
%rule r737 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func737(c,c10).
%function func738(c:int4 -> c1:int4){}
%rule r738 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func738(c,c10).
%function func739(c:int4 -> c1:int4){}
%rule r739 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func739(c,c10).
%function func740(c:int4 -> c1:int4){}
%rule r740 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func740(c,c10).
%function func741(c:int4 -> c1:int4){}
%rule r741 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func741(c,c10).
%function func742(c:int4 -> c1:int4){}
%rule r742 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func742(c,c10).
%function func743(c:int4 -> c1:int4){}
%rule r743 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func743(c,c10).
%function func744(c:int4 -> c1:int4){}
%rule r744 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func744(c,c10).
%function func745(c:int4 -> c1:int4){}
%rule r745 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func745(c,c10).
%function func746(c:int4 -> c1:int4){}
%rule r746 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func746(c,c10).
%function func747(c:int4 -> c1:int4){}
%rule r747 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func747(c,c10).
%function func748(c:int4 -> c1:int4){}
%rule r748 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func748(c,c10).
%function func749(c:int4 -> c1:int4){}
%rule r749 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func749(c,c10).
%function func750(c:int4 -> c1:int4){}
%rule r750 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func750(c,c10).
%function func751(c:int4 -> c1:int4){}
%rule r751 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func751(c,c10).
%function func752(c:int4 -> c1:int4){}
%rule r752 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func752(c,c10).
%function func753(c:int4 -> c1:int4){}
%rule r753 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func753(c,c10).
%function func754(c:int4 -> c1:int4){}
%rule r754 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func754(c,c10).
%function func755(c:int4 -> c1:int4){}
%rule r755 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func755(c,c10).
%function func756(c:int4 -> c1:int4){}
%rule r756 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func756(c,c10).
%function func757(c:int4 -> c1:int4){}
%rule r757 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func757(c,c10).
%function func758(c:int4 -> c1:int4){}
%rule r758 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func758(c,c10).
%function func759(c:int4 -> c1:int4){}
%rule r759 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func759(c,c10).
%function func760(c:int4 -> c1:int4){}
%rule r760 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func760(c,c10).
%function func761(c:int4 -> c1:int4){}
%rule r761 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func761(c,c10).
%function func762(c:int4 -> c1:int4){}
%rule r762 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func762(c,c10).
%function func763(c:int4 -> c1:int4){}
%rule r763 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func763(c,c10).
%function func764(c:int4 -> c1:int4){}
%rule r764 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func764(c,c10).
%function func765(c:int4 -> c1:int4){}
%rule r765 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func765(c,c10).
%function func766(c:int4 -> c1:int4){}
%rule r766 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func766(c,c10).
%function func767(c:int4 -> c1:int4){}
%rule r767 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func767(c,c10).
%function func768(c:int4 -> c1:int4){}
%rule r768 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func768(c,c10).
%function func769(c:int4 -> c1:int4){}
%rule r769 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func769(c,c10).
%function func770(c:int4 -> c1:int4){}
%rule r770 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func770(c,c10).
%function func771(c:int4 -> c1:int4){}
%rule r771 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func771(c,c10).
%function func772(c:int4 -> c1:int4){}
%rule r772 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func772(c,c10).
%function func773(c:int4 -> c1:int4){}
%rule r773 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func773(c,c10).
%function func774(c:int4 -> c1:int4){}
%rule r774 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func774(c,c10).
%function func775(c:int4 -> c1:int4){}
%rule r775 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func775(c,c10).
%function func776(c:int4 -> c1:int4){}
%rule r776 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func776(c,c10).
%function func777(c:int4 -> c1:int4){}
%rule r777 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func777(c,c10).
%function func778(c:int4 -> c1:int4){}
%rule r778 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func778(c,c10).
%function func779(c:int4 -> c1:int4){}
%rule r779 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func779(c,c10).
%function func780(c:int4 -> c1:int4){}
%rule r780 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func780(c,c10).
%function func781(c:int4 -> c1:int4){}
%rule r781 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func781(c,c10).
%function func782(c:int4 -> c1:int4){}
%rule r782 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func782(c,c10).
%function func783(c:int4 -> c1:int4){}
%rule r783 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func783(c,c10).
%function func784(c:int4 -> c1:int4){}
%rule r784 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func784(c,c10).
%function func785(c:int4 -> c1:int4){}
%rule r785 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func785(c,c10).
%function func786(c:int4 -> c1:int4){}
%rule r786 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func786(c,c10).
%function func787(c:int4 -> c1:int4){}
%rule r787 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func787(c,c10).
%function func788(c:int4 -> c1:int4){}
%rule r788 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func788(c,c10).
%function func789(c:int4 -> c1:int4){}
%rule r789 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func789(c,c10).
%function func790(c:int4 -> c1:int4){}
%rule r790 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func790(c,c10).
%function func791(c:int4 -> c1:int4){}
%rule r791 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func791(c,c10).
%function func792(c:int4 -> c1:int4){}
%rule r792 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func792(c,c10).
%function func793(c:int4 -> c1:int4){}
%rule r793 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func793(c,c10).
%function func794(c:int4 -> c1:int4){}
%rule r794 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func794(c,c10).
%function func795(c:int4 -> c1:int4){}
%rule r795 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func795(c,c10).
%function func796(c:int4 -> c1:int4){}
%rule r796 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func796(c,c10).
%function func797(c:int4 -> c1:int4){}
%rule r797 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func797(c,c10).
%function func798(c:int4 -> c1:int4){}
%rule r798 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func798(c,c10).
%function func799(c:int4 -> c1:int4){}
%rule r799 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func799(c,c10).
%function func800(c:int4 -> c1:int4){}
%rule r800 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func800(c,c10).
%function func801(c:int4 -> c1:int4){}
%rule r801 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func801(c,c10).
%function func802(c:int4 -> c1:int4){}
%rule r802 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func802(c,c10).
%function func803(c:int4 -> c1:int4){}
%rule r803 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func803(c,c10).
%function func804(c:int4 -> c1:int4){}
%rule r804 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func804(c,c10).
%function func805(c:int4 -> c1:int4){}
%rule r805 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func805(c,c10).
%function func806(c:int4 -> c1:int4){}
%rule r806 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func806(c,c10).
%function func807(c:int4 -> c1:int4){}
%rule r807 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func807(c,c10).
%function func808(c:int4 -> c1:int4){}
%rule r808 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func808(c,c10).
%function func809(c:int4 -> c1:int4){}
%rule r809 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func809(c,c10).
%function func810(c:int4 -> c1:int4){}
%rule r810 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func810(c,c10).
%function func811(c:int4 -> c1:int4){}
%rule r811 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func811(c,c10).
%function func812(c:int4 -> c1:int4){}
%rule r812 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func812(c,c10).
%function func813(c:int4 -> c1:int4){}
%rule r813 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func813(c,c10).
%function func814(c:int4 -> c1:int4){}
%rule r814 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func814(c,c10).
%function func815(c:int4 -> c1:int4){}
%rule r815 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func815(c,c10).
%function func816(c:int4 -> c1:int4){}
%rule r816 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func816(c,c10).
%function func817(c:int4 -> c1:int4){}
%rule r817 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func817(c,c10).
%function func818(c:int4 -> c1:int4){}
%rule r818 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func818(c,c10).
%function func819(c:int4 -> c1:int4){}
%rule r819 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func819(c,c10).
%function func820(c:int4 -> c1:int4){}
%rule r820 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func820(c,c10).
%function func821(c:int4 -> c1:int4){}
%rule r821 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func821(c,c10).
%function func822(c:int4 -> c1:int4){}
%rule r822 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func822(c,c10).
%function func823(c:int4 -> c1:int4){}
%rule r823 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func823(c,c10).
%function func824(c:int4 -> c1:int4){}
%rule r824 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func824(c,c10).
%function func825(c:int4 -> c1:int4){}
%rule r825 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func825(c,c10).
%function func826(c:int4 -> c1:int4){}
%rule r826 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func826(c,c10).
%function func827(c:int4 -> c1:int4){}
%rule r827 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func827(c,c10).
%function func828(c:int4 -> c1:int4){}
%rule r828 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func828(c,c10).
%function func829(c:int4 -> c1:int4){}
%rule r829 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func829(c,c10).
%function func830(c:int4 -> c1:int4){}
%rule r830 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func830(c,c10).
%function func831(c:int4 -> c1:int4){}
%rule r831 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func831(c,c10).
%function func832(c:int4 -> c1:int4){}
%rule r832 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func832(c,c10).
%function func833(c:int4 -> c1:int4){}
%rule r833 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func833(c,c10).
%function func834(c:int4 -> c1:int4){}
%rule r834 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func834(c,c10).
%function func835(c:int4 -> c1:int4){}
%rule r835 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func835(c,c10).
%function func836(c:int4 -> c1:int4){}
%rule r836 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func836(c,c10).
%function func837(c:int4 -> c1:int4){}
%rule r837 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func837(c,c10).
%function func838(c:int4 -> c1:int4){}
%rule r838 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func838(c,c10).
%function func839(c:int4 -> c1:int4){}
%rule r839 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func839(c,c10).
%function func840(c:int4 -> c1:int4){}
%rule r840 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func840(c,c10).
%function func841(c:int4 -> c1:int4){}
%rule r841 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func841(c,c10).
%function func842(c:int4 -> c1:int4){}
%rule r842 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func842(c,c10).
%function func843(c:int4 -> c1:int4){}
%rule r843 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func843(c,c10).
%function func844(c:int4 -> c1:int4){}
%rule r844 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func844(c,c10).
%function func845(c:int4 -> c1:int4){}
%rule r845 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func845(c,c10).
%function func846(c:int4 -> c1:int4){}
%rule r846 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func846(c,c10).
%function func847(c:int4 -> c1:int4){}
%rule r847 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func847(c,c10).
%function func848(c:int4 -> c1:int4){}
%rule r848 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func848(c,c10).
%function func849(c:int4 -> c1:int4){}
%rule r849 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func849(c,c10).
%function func850(c:int4 -> c1:int4){}
%rule r850 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func850(c,c10).
%function func851(c:int4 -> c1:int4){}
%rule r851 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func851(c,c10).
%function func852(c:int4 -> c1:int4){}
%rule r852 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func852(c,c10).
%function func853(c:int4 -> c1:int4){}
%rule r853 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func853(c,c10).
%function func854(c:int4 -> c1:int4){}
%rule r854 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func854(c,c10).
%function func855(c:int4 -> c1:int4){}
%rule r855 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func855(c,c10).
%function func856(c:int4 -> c1:int4){}
%rule r856 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func856(c,c10).
%function func857(c:int4 -> c1:int4){}
%rule r857 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func857(c,c10).
%function func858(c:int4 -> c1:int4){}
%rule r858 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func858(c,c10).
%function func859(c:int4 -> c1:int4){}
%rule r859 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func859(c,c10).
%function func860(c:int4 -> c1:int4){}
%rule r860 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func860(c,c10).
%function func861(c:int4 -> c1:int4){}
%rule r861 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func861(c,c10).
%function func862(c:int4 -> c1:int4){}
%rule r862 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func862(c,c10).
%function func863(c:int4 -> c1:int4){}
%rule r863 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func863(c,c10).
%function func864(c:int4 -> c1:int4){}
%rule r864 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func864(c,c10).
%function func865(c:int4 -> c1:int4){}
%rule r865 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func865(c,c10).
%function func866(c:int4 -> c1:int4){}
%rule r866 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func866(c,c10).
%function func867(c:int4 -> c1:int4){}
%rule r867 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func867(c,c10).
%function func868(c:int4 -> c1:int4){}
%rule r868 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func868(c,c10).
%function func869(c:int4 -> c1:int4){}
%rule r869 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func869(c,c10).
%function func870(c:int4 -> c1:int4){}
%rule r870 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func870(c,c10).
%function func871(c:int4 -> c1:int4){}
%rule r871 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func871(c,c10).
%function func872(c:int4 -> c1:int4){}
%rule r872 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func872(c,c10).
%function func873(c:int4 -> c1:int4){}
%rule r873 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func873(c,c10).
%function func874(c:int4 -> c1:int4){}
%rule r874 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func874(c,c10).
%function func875(c:int4 -> c1:int4){}
%rule r875 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func875(c,c10).
%function func876(c:int4 -> c1:int4){}
%rule r876 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func876(c,c10).
%function func877(c:int4 -> c1:int4){}
%rule r877 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func877(c,c10).
%function func878(c:int4 -> c1:int4){}
%rule r878 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func878(c,c10).
%function func879(c:int4 -> c1:int4){}
%rule r879 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func879(c,c10).
%function func880(c:int4 -> c1:int4){}
%rule r880 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func880(c,c10).
%function func881(c:int4 -> c1:int4){}
%rule r881 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func881(c,c10).
%function func882(c:int4 -> c1:int4){}
%rule r882 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func882(c,c10).
%function func883(c:int4 -> c1:int4){}
%rule r883 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func883(c,c10).
%function func884(c:int4 -> c1:int4){}
%rule r884 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func884(c,c10).
%function func885(c:int4 -> c1:int4){}
%rule r885 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func885(c,c10).
%function func886(c:int4 -> c1:int4){}
%rule r886 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func886(c,c10).
%function func887(c:int4 -> c1:int4){}
%rule r887 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func887(c,c10).
%function func888(c:int4 -> c1:int4){}
%rule r888 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func888(c,c10).
%function func889(c:int4 -> c1:int4){}
%rule r889 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func889(c,c10).
%function func890(c:int4 -> c1:int4){}
%rule r890 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func890(c,c10).
%function func891(c:int4 -> c1:int4){}
%rule r891 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func891(c,c10).
%function func892(c:int4 -> c1:int4){}
%rule r892 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func892(c,c10).
%function func893(c:int4 -> c1:int4){}
%rule r893 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func893(c,c10).
%function func894(c:int4 -> c1:int4){}
%rule r894 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func894(c,c10).
%function func895(c:int4 -> c1:int4){}
%rule r895 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func895(c,c10).
%function func896(c:int4 -> c1:int4){}
%rule r896 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func896(c,c10).
%function func897(c:int4 -> c1:int4){}
%rule r897 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func897(c,c10).
%function func898(c:int4 -> c1:int4){}
%rule r898 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func898(c,c10).
%function func899(c:int4 -> c1:int4){}
%rule r899 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func899(c,c10).
%function func900(c:int4 -> c1:int4){}
%rule r900 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func900(c,c10).
%function func901(c:int4 -> c1:int4){}
%rule r901 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func901(c,c10).
%function func902(c:int4 -> c1:int4){}
%rule r902 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func902(c,c10).
%function func903(c:int4 -> c1:int4){}
%rule r903 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func903(c,c10).
%function func904(c:int4 -> c1:int4){}
%rule r904 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func904(c,c10).
%function func905(c:int4 -> c1:int4){}
%rule r905 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func905(c,c10).
%function func906(c:int4 -> c1:int4){}
%rule r906 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func906(c,c10).
%function func907(c:int4 -> c1:int4){}
%rule r907 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func907(c,c10).
%function func908(c:int4 -> c1:int4){}
%rule r908 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func908(c,c10).
%function func909(c:int4 -> c1:int4){}
%rule r909 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func909(c,c10).
%function func910(c:int4 -> c1:int4){}
%rule r910 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func910(c,c10).
%function func911(c:int4 -> c1:int4){}
%rule r911 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func911(c,c10).
%function func912(c:int4 -> c1:int4){}
%rule r912 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func912(c,c10).
%function func913(c:int4 -> c1:int4){}
%rule r913 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func913(c,c10).
%function func914(c:int4 -> c1:int4){}
%rule r914 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func914(c,c10).
%function func915(c:int4 -> c1:int4){}
%rule r915 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func915(c,c10).
%function func916(c:int4 -> c1:int4){}
%rule r916 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func916(c,c10).
%function func917(c:int4 -> c1:int4){}
%rule r917 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func917(c,c10).
%function func918(c:int4 -> c1:int4){}
%rule r918 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func918(c,c10).
%function func919(c:int4 -> c1:int4){}
%rule r919 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func919(c,c10).
%function func920(c:int4 -> c1:int4){}
%rule r920 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func920(c,c10).
%function func921(c:int4 -> c1:int4){}
%rule r921 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func921(c,c10).
%function func922(c:int4 -> c1:int4){}
%rule r922 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func922(c,c10).
%function func923(c:int4 -> c1:int4){}
%rule r923 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func923(c,c10).
%function func924(c:int4 -> c1:int4){}
%rule r924 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func924(c,c10).
%function func925(c:int4 -> c1:int4){}
%rule r925 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func925(c,c10).
%function func926(c:int4 -> c1:int4){}
%rule r926 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func926(c,c10).
%function func927(c:int4 -> c1:int4){}
%rule r927 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func927(c,c10).
%function func928(c:int4 -> c1:int4){}
%rule r928 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func928(c,c10).
%function func929(c:int4 -> c1:int4){}
%rule r929 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func929(c,c10).
%function func930(c:int4 -> c1:int4){}
%rule r930 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func930(c,c10).
%function func931(c:int4 -> c1:int4){}
%rule r931 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func931(c,c10).
%function func932(c:int4 -> c1:int4){}
%rule r932 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func932(c,c10).
%function func933(c:int4 -> c1:int4){}
%rule r933 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func933(c,c10).
%function func934(c:int4 -> c1:int4){}
%rule r934 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func934(c,c10).
%function func935(c:int4 -> c1:int4){}
%rule r935 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func935(c,c10).
%function func936(c:int4 -> c1:int4){}
%rule r936 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func936(c,c10).
%function func937(c:int4 -> c1:int4){}
%rule r937 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func937(c,c10).
%function func938(c:int4 -> c1:int4){}
%rule r938 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func938(c,c10).
%function func939(c:int4 -> c1:int4){}
%rule r939 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func939(c,c10).
%function func940(c:int4 -> c1:int4){}
%rule r940 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func940(c,c10).
%function func941(c:int4 -> c1:int4){}
%rule r941 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func941(c,c10).
%function func942(c:int4 -> c1:int4){}
%rule r942 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func942(c,c10).
%function func943(c:int4 -> c1:int4){}
%rule r943 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func943(c,c10).
%function func944(c:int4 -> c1:int4){}
%rule r944 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func944(c,c10).
%function func945(c:int4 -> c1:int4){}
%rule r945 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func945(c,c10).
%function func946(c:int4 -> c1:int4){}
%rule r946 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func946(c,c10).
%function func947(c:int4 -> c1:int4){}
%rule r947 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func947(c,c10).
%function func948(c:int4 -> c1:int4){}
%rule r948 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func948(c,c10).
%function func949(c:int4 -> c1:int4){}
%rule r949 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func949(c,c10).
%function func950(c:int4 -> c1:int4){}
%rule r950 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func950(c,c10).
%function func951(c:int4 -> c1:int4){}
%rule r951 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func951(c,c10).
%function func952(c:int4 -> c1:int4){}
%rule r952 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func952(c,c10).
%function func953(c:int4 -> c1:int4){}
%rule r953 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func953(c,c10).
%function func954(c:int4 -> c1:int4){}
%rule r954 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func954(c,c10).
%function func955(c:int4 -> c1:int4){}
%rule r955 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func955(c,c10).
%function func956(c:int4 -> c1:int4){}
%rule r956 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func956(c,c10).
%function func957(c:int4 -> c1:int4){}
%rule r957 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func957(c,c10).
%function func958(c:int4 -> c1:int4){}
%rule r958 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func958(c,c10).
%function func959(c:int4 -> c1:int4){}
%rule r959 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func959(c,c10).
%function func960(c:int4 -> c1:int4){}
%rule r960 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func960(c,c10).
%function func961(c:int4 -> c1:int4){}
%rule r961 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func961(c,c10).
%function func962(c:int4 -> c1:int4){}
%rule r962 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func962(c,c10).
%function func963(c:int4 -> c1:int4){}
%rule r963 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func963(c,c10).
%function func964(c:int4 -> c1:int4){}
%rule r964 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func964(c,c10).
%function func965(c:int4 -> c1:int4){}
%rule r965 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func965(c,c10).
%function func966(c:int4 -> c1:int4){}
%rule r966 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func966(c,c10).
%function func967(c:int4 -> c1:int4){}
%rule r967 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func967(c,c10).
%function func968(c:int4 -> c1:int4){}
%rule r968 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func968(c,c10).
%function func969(c:int4 -> c1:int4){}
%rule r969 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func969(c,c10).
%function func970(c:int4 -> c1:int4){}
%rule r970 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func970(c,c10).
%function func971(c:int4 -> c1:int4){}
%rule r971 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func971(c,c10).
%function func972(c:int4 -> c1:int4){}
%rule r972 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func972(c,c10).
%function func973(c:int4 -> c1:int4){}
%rule r973 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func973(c,c10).
%function func974(c:int4 -> c1:int4){}
%rule r974 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func974(c,c10).
%function func975(c:int4 -> c1:int4){}
%rule r975 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func975(c,c10).
%function func976(c:int4 -> c1:int4){}
%rule r976 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func976(c,c10).
%function func977(c:int4 -> c1:int4){}
%rule r977 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func977(c,c10).
%function func978(c:int4 -> c1:int4){}
%rule r978 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func978(c,c10).
%function func979(c:int4 -> c1:int4){}
%rule r979 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func979(c,c10).
%function func980(c:int4 -> c1:int4){}
%rule r980 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func980(c,c10).
%function func981(c:int4 -> c1:int4){}
%rule r981 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func981(c,c10).
%function func982(c:int4 -> c1:int4){}
%rule r982 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func982(c,c10).
%function func983(c:int4 -> c1:int4){}
%rule r983 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func983(c,c10).
%function func984(c:int4 -> c1:int4){}
%rule r984 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func984(c,c10).
%function func985(c:int4 -> c1:int4){}
%rule r985 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func985(c,c10).
%function func986(c:int4 -> c1:int4){}
%rule r986 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func986(c,c10).
%function func987(c:int4 -> c1:int4){}
%rule r987 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func987(c,c10).
%function func988(c:int4 -> c1:int4){}
%rule r988 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func988(c,c10).
%function func989(c:int4 -> c1:int4){}
%rule r989 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func989(c,c10).
%function func990(c:int4 -> c1:int4){}
%rule r990 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func990(c,c10).
%function func991(c:int4 -> c1:int4){}
%rule r991 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func991(c,c10).
%function func992(c:int4 -> c1:int4){}
%rule r992 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func992(c,c10).
%function func993(c:int4 -> c1:int4){}
%rule r993 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func993(c,c10).
%function func994(c:int4 -> c1:int4){}
%rule r994 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func994(c,c10).
%function func995(c:int4 -> c1:int4){}
%rule r995 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func995(c,c10).
%function func996(c:int4 -> c1:int4){}
%rule r996 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func996(c,c10).
%function func997(c:int4 -> c1:int4){}
%rule r997 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func997(c,c10).
%function func998(c:int4 -> c1:int4){}
%rule r998 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func998(c,c10).
%function func999(c:int4 -> c1:int4){}
%rule r999 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func999(c,c10).
%function func1000(c:int4 -> c1:int4){}
%rule x000 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1000(c,c10).
%function func1001(c:int4 -> c1:int4){}
%rule x001 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1001(c,c10).
%function func1002(c:int4 -> c1:int4){}
%rule x002 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1002(c,c10).
%function func1003(c:int4 -> c1:int4){}
%rule x003 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1003(c,c10).
%function func1004(c:int4 -> c1:int4){}
%rule x004 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1004(c,c10).
%function func1005(c:int4 -> c1:int4){}
%rule x005 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1005(c,c10).
%function func1006(c:int4 -> c1:int4){}
%rule x006 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1006(c,c10).
%function func1007(c:int4 -> c1:int4){}
%rule x007 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1007(c,c10).
%function func1008(c:int4 -> c1:int4){}
%rule x008 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1008(c,c10).
%function func1009(c:int4 -> c1:int4){}
%rule x009 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1009(c,c10).
%function func1010(c:int4 -> c1:int4){}
%rule x010 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1010(c,c10).
%function func1011(c:int4 -> c1:int4){}
%rule x011 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1011(c,c10).
%function func1012(c:int4 -> c1:int4){}
%rule x012 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1012(c,c10).
%function func1013(c:int4 -> c1:int4){}
%rule x013 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1013(c,c10).
%function func1014(c:int4 -> c1:int4){}
%rule x014 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1014(c,c10).
%function func1015(c:int4 -> c1:int4){}
%rule x015 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1015(c,c10).
%function func1016(c:int4 -> c1:int4){}
%rule x016 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1016(c,c10).
%function func1017(c:int4 -> c1:int4){}
%rule x017 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1017(c,c10).
%function func1018(c:int4 -> c1:int4){}
%rule x018 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1018(c,c10).
%function func1019(c:int4 -> c1:int4){}
%rule x019 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1019(c,c10).
%function func1020(c:int4 -> c1:int4){}
%rule x020 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1020(c,c10).
%function func1021(c:int4 -> c1:int4){}
%rule x021 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1021(c,c10).
%function func1022(c:int4 -> c1:int4){}
%rule x022 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1022(c,c10).
%function func1023(c:int4 -> c1:int4){}
%rule x023 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1023(c,c10).
%function func1024(c:int4 -> c1:int4){}
%rule x024 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1024(c,c10).
