%version v0.0.0 -> v1.0.0
%function func1(c:int4 -> c1:int4){}
%alter rule r0 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1(c,c10).
%alter function func2(c:int4 -> c1:int4){}
%alter rule r2 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func2(c,c10).
%alter function func3(c:int4 -> c1:int4){}
%alter rule r3 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func3(c,c10).
%alter function func4(c:int4 -> c1:int4){}
%alter rule r4 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func4(c,c10).
%alter function func5(c:int4 -> c1:int4){}
%alter rule r5 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func5(c,c10).
%alter function func6(c:int4 -> c1:int4){}
%alter rule r6 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func6(c,c10).
%alter function func7(c:int4 -> c1:int4){}
%alter rule r7 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func7(c,c10).
%alter function func8(c:int4 -> c1:int4){}
%alter rule r8 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func8(c,c10).
%alter function func9(c:int4 -> c1:int4){}
%alter rule r9 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func9(c,c10).
%alter function func10(c:int4 -> c1:int4){}
%alter rule x0 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func10(c,c10).
%alter function func11(c:int4 -> c1:int4){}
%alter rule x1 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func11(c,c10).
%alter function func12(c:int4 -> c1:int4){}
%alter rule x2 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func12(c,c10).
%alter function func13(c:int4 -> c1:int4){}
%alter rule x3 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func13(c,c10).
%alter function func14(c:int4 -> c1:int4){}
%alter rule x4 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func14(c,c10).
%alter function func15(c:int4 -> c1:int4){}
%alter rule x5 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func15(c,c10).
%alter function func16(c:int4 -> c1:int4){}
%alter rule x6 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func16(c,c10).
%alter function func17(c:int4 -> c1:int4){}
%alter rule x7 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func17(c,c10).
%alter function func18(c:int4 -> c1:int4){}
%alter rule x8 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func18(c,c10).
%alter function func19(c:int4 -> c1:int4){}
%alter rule x9 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func19(c,c10).
%alter function func20(c:int4 -> c1:int4){}
%alter rule r20 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func20(c,c10).
%alter function func21(c:int4 -> c1:int4){}
%alter rule r21 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func21(c,c10).
%alter function func22(c:int4 -> c1:int4){}
%alter rule r22 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func22(c,c10).
%alter function func23(c:int4 -> c1:int4){}
%alter rule r23 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func23(c,c10).
%alter function func24(c:int4 -> c1:int4){}
%alter rule r24 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func24(c,c10).
%alter function func25(c:int4 -> c1:int4){}
%alter rule r25 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func25(c,c10).
%alter function func26(c:int4 -> c1:int4){}
%alter rule r26 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func26(c,c10).
%alter function func27(c:int4 -> c1:int4){}
%alter rule r27 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func27(c,c10).
%alter function func28(c:int4 -> c1:int4){}
%alter rule r28 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func28(c,c10).
%alter function func29(c:int4 -> c1:int4){}
%alter rule r29 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func29(c,c10).
%alter function func30(c:int4 -> c1:int4){}
%alter rule r30 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func30(c,c10).
%alter function func31(c:int4 -> c1:int4){}
%alter rule r31 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func31(c,c10).
%alter function func32(c:int4 -> c1:int4){}
%alter rule r32 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func32(c,c10).
%alter function func33(c:int4 -> c1:int4){}
%alter rule r33 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func33(c,c10).
%alter function func34(c:int4 -> c1:int4){}
%alter rule r34 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func34(c,c10).
%alter function func35(c:int4 -> c1:int4){}
%alter rule r35 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func35(c,c10).
%alter function func36(c:int4 -> c1:int4){}
%alter rule r36 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func36(c,c10).
%alter function func37(c:int4 -> c1:int4){}
%alter rule r37 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func37(c,c10).
%alter function func38(c:int4 -> c1:int4){}
%alter rule r38 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func38(c,c10).
%alter function func39(c:int4 -> c1:int4){}
%alter rule r39 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func39(c,c10).
%alter function func40(c:int4 -> c1:int4){}
%alter rule r40 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func40(c,c10).
%alter function func41(c:int4 -> c1:int4){}
%alter rule r41 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func41(c,c10).
%alter function func42(c:int4 -> c1:int4){}
%alter rule r42 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func42(c,c10).
%alter function func43(c:int4 -> c1:int4){}
%alter rule r43 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func43(c,c10).
%alter function func44(c:int4 -> c1:int4){}
%alter rule r44 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func44(c,c10).
%alter function func45(c:int4 -> c1:int4){}
%alter rule r45 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func45(c,c10).
%alter function func46(c:int4 -> c1:int4){}
%alter rule r46 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func46(c,c10).
%alter function func47(c:int4 -> c1:int4){}
%alter rule r47 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func47(c,c10).
%alter function func48(c:int4 -> c1:int4){}
%alter rule r48 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func48(c,c10).
%alter function func49(c:int4 -> c1:int4){}
%alter rule r49 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func49(c,c10).
%alter function func50(c:int4 -> c1:int4){}
%alter rule r50 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func50(c,c10).
%alter function func51(c:int4 -> c1:int4){}
%alter rule r51 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func51(c,c10).
%alter function func52(c:int4 -> c1:int4){}
%alter rule r52 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func52(c,c10).
%alter function func53(c:int4 -> c1:int4){}
%alter rule r53 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func53(c,c10).
%alter function func54(c:int4 -> c1:int4){}
%alter rule r54 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func54(c,c10).
%alter function func55(c:int4 -> c1:int4){}
%alter rule r55 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func55(c,c10).
%alter function func56(c:int4 -> c1:int4){}
%alter rule r56 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func56(c,c10).
%alter function func57(c:int4 -> c1:int4){}
%alter rule r57 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func57(c,c10).
%alter function func58(c:int4 -> c1:int4){}
%alter rule r58 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func58(c,c10).
%alter function func59(c:int4 -> c1:int4){}
%alter rule r59 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func59(c,c10).
%alter function func60(c:int4 -> c1:int4){}
%alter rule r60 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func60(c,c10).
%alter function func61(c:int4 -> c1:int4){}
%alter rule r61 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func61(c,c10).
%alter function func62(c:int4 -> c1:int4){}
%alter rule r62 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func62(c,c10).
%alter function func63(c:int4 -> c1:int4){}
%alter rule r63 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func63(c,c10).
%alter function func64(c:int4 -> c1:int4){}
%alter rule r64 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func64(c,c10).
%alter function func65(c:int4 -> c1:int4){}
%alter rule r65 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func65(c,c10).
%alter function func66(c:int4 -> c1:int4){}
%alter rule r66 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func66(c,c10).
%alter function func67(c:int4 -> c1:int4){}
%alter rule r67 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func67(c,c10).
%alter function func68(c:int4 -> c1:int4){}
%alter rule r68 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func68(c,c10).
%alter function func69(c:int4 -> c1:int4){}
%alter rule r69 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func69(c,c10).
%alter function func70(c:int4 -> c1:int4){}
%alter rule r70 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func70(c,c10).
%alter function func71(c:int4 -> c1:int4){}
%alter rule r71 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func71(c,c10).
%alter function func72(c:int4 -> c1:int4){}
%alter rule r72 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func72(c,c10).
%alter function func73(c:int4 -> c1:int4){}
%alter rule r73 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func73(c,c10).
%alter function func74(c:int4 -> c1:int4){}
%alter rule r74 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func74(c,c10).
%alter function func75(c:int4 -> c1:int4){}
%alter rule r75 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func75(c,c10).
%alter function func76(c:int4 -> c1:int4){}
%alter rule r76 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func76(c,c10).
%alter function func77(c:int4 -> c1:int4){}
%alter rule r77 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func77(c,c10).
%alter function func78(c:int4 -> c1:int4){}
%alter rule r78 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func78(c,c10).
%alter function func79(c:int4 -> c1:int4){}
%alter rule r79 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func79(c,c10).
%alter function func80(c:int4 -> c1:int4){}
%alter rule r80 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func80(c,c10).
%alter function func81(c:int4 -> c1:int4){}
%alter rule r81 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func81(c,c10).
%alter function func82(c:int4 -> c1:int4){}
%alter rule r82 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func82(c,c10).
%alter function func83(c:int4 -> c1:int4){}
%alter rule r83 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func83(c,c10).
%alter function func84(c:int4 -> c1:int4){}
%alter rule r84 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func84(c,c10).
%alter function func85(c:int4 -> c1:int4){}
%alter rule r85 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func85(c,c10).
%alter function func86(c:int4 -> c1:int4){}
%alter rule r86 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func86(c,c10).
%alter function func87(c:int4 -> c1:int4){}
%alter rule r87 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func87(c,c10).
%alter function func88(c:int4 -> c1:int4){}
%alter rule r88 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func88(c,c10).
%alter function func89(c:int4 -> c1:int4){}
%alter rule r89 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func89(c,c10).
%alter function func90(c:int4 -> c1:int4){}
%alter rule r90 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func90(c,c10).
%alter function func91(c:int4 -> c1:int4){}
%alter rule r91 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func91(c,c10).
%alter function func92(c:int4 -> c1:int4){}
%alter rule r92 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func92(c,c10).
%alter function func93(c:int4 -> c1:int4){}
%alter rule r93 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func93(c,c10).
%alter function func94(c:int4 -> c1:int4){}
%alter rule r94 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func94(c,c10).
%alter function func95(c:int4 -> c1:int4){}
%alter rule r95 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func95(c,c10).
%alter function func96(c:int4 -> c1:int4){}
%alter rule r96 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func96(c,c10).
%alter function func97(c:int4 -> c1:int4){}
%alter rule r97 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func97(c,c10).
%alter function func98(c:int4 -> c1:int4){}
%alter rule r98 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func98(c,c10).
%alter function func99(c:int4 -> c1:int4){}
%alter rule r99 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func99(c,c10).
%alter function func100(c:int4 -> c1:int4){}
%alter rule x00 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func100(c,c10).
%alter function func101(c:int4 -> c1:int4){}
%alter rule x01 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func101(c,c10).
%alter function func102(c:int4 -> c1:int4){}
%alter rule x02 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func102(c,c10).
%alter function func103(c:int4 -> c1:int4){}
%alter rule x03 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func103(c,c10).
%alter function func104(c:int4 -> c1:int4){}
%alter rule x04 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func104(c,c10).
%alter function func105(c:int4 -> c1:int4){}
%alter rule x05 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func105(c,c10).
%alter function func106(c:int4 -> c1:int4){}
%alter rule x06 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func106(c,c10).
%alter function func107(c:int4 -> c1:int4){}
%alter rule x07 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func107(c,c10).
%alter function func108(c:int4 -> c1:int4){}
%alter rule x08 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func108(c,c10).
%alter function func109(c:int4 -> c1:int4){}
%alter rule x09 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func109(c,c10).
%alter function func110(c:int4 -> c1:int4){}
%alter rule x10 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func110(c,c10).
%alter function func111(c:int4 -> c1:int4){}
%alter rule x11 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func111(c,c10).
%alter function func112(c:int4 -> c1:int4){}
%alter rule x12 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func112(c,c10).
%alter function func113(c:int4 -> c1:int4){}
%alter rule x13 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func113(c,c10).
%alter function func114(c:int4 -> c1:int4){}
%alter rule x14 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func114(c,c10).
%alter function func115(c:int4 -> c1:int4){}
%alter rule x15 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func115(c,c10).
%alter function func116(c:int4 -> c1:int4){}
%alter rule x16 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func116(c,c10).
%alter function func117(c:int4 -> c1:int4){}
%alter rule x17 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func117(c,c10).
%alter function func118(c:int4 -> c1:int4){}
%alter rule x18 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func118(c,c10).
%alter function func119(c:int4 -> c1:int4){}
%alter rule x19 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func119(c,c10).
%alter function func120(c:int4 -> c1:int4){}
%alter rule x20 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func120(c,c10).
%alter function func121(c:int4 -> c1:int4){}
%alter rule x21 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func121(c,c10).
%alter function func122(c:int4 -> c1:int4){}
%alter rule x22 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func122(c,c10).
%alter function func123(c:int4 -> c1:int4){}
%alter rule x23 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func123(c,c10).
%alter function func124(c:int4 -> c1:int4){}
%alter rule x24 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func124(c,c10).
%alter function func125(c:int4 -> c1:int4){}
%alter rule x25 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func125(c,c10).
%alter function func126(c:int4 -> c1:int4){}
%alter rule x26 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func126(c,c10).
%alter function func127(c:int4 -> c1:int4){}
%alter rule x27 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func127(c,c10).
%alter function func128(c:int4 -> c1:int4){}
%alter rule x28 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func128(c,c10).
%alter function func129(c:int4 -> c1:int4){}
%alter rule x29 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func129(c,c10).
%alter function func130(c:int4 -> c1:int4){}
%alter rule x30 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func130(c,c10).
%alter function func131(c:int4 -> c1:int4){}
%alter rule x31 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func131(c,c10).
%alter function func132(c:int4 -> c1:int4){}
%alter rule x32 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func132(c,c10).
%alter function func133(c:int4 -> c1:int4){}
%alter rule x33 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func133(c,c10).
%alter function func134(c:int4 -> c1:int4){}
%alter rule x34 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func134(c,c10).
%alter function func135(c:int4 -> c1:int4){}
%alter rule x35 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func135(c,c10).
%alter function func136(c:int4 -> c1:int4){}
%alter rule x36 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func136(c,c10).
%alter function func137(c:int4 -> c1:int4){}
%alter rule x37 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func137(c,c10).
%alter function func138(c:int4 -> c1:int4){}
%alter rule x38 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func138(c,c10).
%alter function func139(c:int4 -> c1:int4){}
%alter rule x39 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func139(c,c10).
%alter function func140(c:int4 -> c1:int4){}
%alter rule x40 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func140(c,c10).
%alter function func141(c:int4 -> c1:int4){}
%alter rule x41 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func141(c,c10).
%alter function func142(c:int4 -> c1:int4){}
%alter rule x42 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func142(c,c10).
%alter function func143(c:int4 -> c1:int4){}
%alter rule x43 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func143(c,c10).
%alter function func144(c:int4 -> c1:int4){}
%alter rule x44 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func144(c,c10).
%alter function func145(c:int4 -> c1:int4){}
%alter rule x45 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func145(c,c10).
%alter function func146(c:int4 -> c1:int4){}
%alter rule x46 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func146(c,c10).
%alter function func147(c:int4 -> c1:int4){}
%alter rule x47 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func147(c,c10).
%alter function func148(c:int4 -> c1:int4){}
%alter rule x48 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func148(c,c10).
%alter function func149(c:int4 -> c1:int4){}
%alter rule x49 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func149(c,c10).
%alter function func150(c:int4 -> c1:int4){}
%alter rule x50 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func150(c,c10).
%alter function func151(c:int4 -> c1:int4){}
%alter rule x51 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func151(c,c10).
%alter function func152(c:int4 -> c1:int4){}
%alter rule x52 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func152(c,c10).
%alter function func153(c:int4 -> c1:int4){}
%alter rule x53 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func153(c,c10).
%alter function func154(c:int4 -> c1:int4){}
%alter rule x54 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func154(c,c10).
%alter function func155(c:int4 -> c1:int4){}
%alter rule x55 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func155(c,c10).
%alter function func156(c:int4 -> c1:int4){}
%alter rule x56 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func156(c,c10).
%alter function func157(c:int4 -> c1:int4){}
%alter rule x57 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func157(c,c10).
%alter function func158(c:int4 -> c1:int4){}
%alter rule x58 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func158(c,c10).
%alter function func159(c:int4 -> c1:int4){}
%alter rule x59 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func159(c,c10).
%alter function func160(c:int4 -> c1:int4){}
%alter rule x60 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func160(c,c10).
%alter function func161(c:int4 -> c1:int4){}
%alter rule x61 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func161(c,c10).
%alter function func162(c:int4 -> c1:int4){}
%alter rule x62 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func162(c,c10).
%alter function func163(c:int4 -> c1:int4){}
%alter rule x63 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func163(c,c10).
%alter function func164(c:int4 -> c1:int4){}
%alter rule x64 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func164(c,c10).
%alter function func165(c:int4 -> c1:int4){}
%alter rule x65 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func165(c,c10).
%alter function func166(c:int4 -> c1:int4){}
%alter rule x66 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func166(c,c10).
%alter function func167(c:int4 -> c1:int4){}
%alter rule x67 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func167(c,c10).
%alter function func168(c:int4 -> c1:int4){}
%alter rule x68 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func168(c,c10).
%alter function func169(c:int4 -> c1:int4){}
%alter rule x69 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func169(c,c10).
%alter function func170(c:int4 -> c1:int4){}
%alter rule x70 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func170(c,c10).
%alter function func171(c:int4 -> c1:int4){}
%alter rule x71 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func171(c,c10).
%alter function func172(c:int4 -> c1:int4){}
%alter rule x72 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func172(c,c10).
%alter function func173(c:int4 -> c1:int4){}
%alter rule x73 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func173(c,c10).
%alter function func174(c:int4 -> c1:int4){}
%alter rule x74 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func174(c,c10).
%alter function func175(c:int4 -> c1:int4){}
%alter rule x75 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func175(c,c10).
%alter function func176(c:int4 -> c1:int4){}
%alter rule x76 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func176(c,c10).
%alter function func177(c:int4 -> c1:int4){}
%alter rule x77 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func177(c,c10).
%alter function func178(c:int4 -> c1:int4){}
%alter rule x78 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func178(c,c10).
%alter function func179(c:int4 -> c1:int4){}
%alter rule x79 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func179(c,c10).
%alter function func180(c:int4 -> c1:int4){}
%alter rule x80 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func180(c,c10).
%alter function func181(c:int4 -> c1:int4){}
%alter rule x81 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func181(c,c10).
%alter function func182(c:int4 -> c1:int4){}
%alter rule x82 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func182(c,c10).
%alter function func183(c:int4 -> c1:int4){}
%alter rule x83 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func183(c,c10).
%alter function func184(c:int4 -> c1:int4){}
%alter rule x84 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func184(c,c10).
%alter function func185(c:int4 -> c1:int4){}
%alter rule x85 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func185(c,c10).
%alter function func186(c:int4 -> c1:int4){}
%alter rule x86 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func186(c,c10).
%alter function func187(c:int4 -> c1:int4){}
%alter rule x87 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func187(c,c10).
%alter function func188(c:int4 -> c1:int4){}
%alter rule x88 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func188(c,c10).
%alter function func189(c:int4 -> c1:int4){}
%alter rule x89 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func189(c,c10).
%alter function func190(c:int4 -> c1:int4){}
%alter rule x90 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func190(c,c10).
%alter function func191(c:int4 -> c1:int4){}
%alter rule x91 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func191(c,c10).
%alter function func192(c:int4 -> c1:int4){}
%alter rule x92 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func192(c,c10).
%alter function func193(c:int4 -> c1:int4){}
%alter rule x93 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func193(c,c10).
%alter function func194(c:int4 -> c1:int4){}
%alter rule x94 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func194(c,c10).
%alter function func195(c:int4 -> c1:int4){}
%alter rule x95 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func195(c,c10).
%alter function func196(c:int4 -> c1:int4){}
%alter rule x96 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func196(c,c10).
%alter function func197(c:int4 -> c1:int4){}
%alter rule x97 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func197(c,c10).
%alter function func198(c:int4 -> c1:int4){}
%alter rule x98 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func198(c,c10).
%alter function func199(c:int4 -> c1:int4){}
%alter rule x99 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func199(c,c10).
%alter function func200(c:int4 -> c1:int4){}
%alter rule r200 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func200(c,c10).
%alter function func201(c:int4 -> c1:int4){}
%alter rule r201 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func201(c,c10).
%alter function func202(c:int4 -> c1:int4){}
%alter rule r202 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func202(c,c10).
%alter function func203(c:int4 -> c1:int4){}
%alter rule r203 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func203(c,c10).
%alter function func204(c:int4 -> c1:int4){}
%alter rule r204 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func204(c,c10).
%alter function func205(c:int4 -> c1:int4){}
%alter rule r205 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func205(c,c10).
%alter function func206(c:int4 -> c1:int4){}
%alter rule r206 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func206(c,c10).
%alter function func207(c:int4 -> c1:int4){}
%alter rule r207 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func207(c,c10).
%alter function func208(c:int4 -> c1:int4){}
%alter rule r208 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func208(c,c10).
%alter function func209(c:int4 -> c1:int4){}
%alter rule r209 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func209(c,c10).
%alter function func210(c:int4 -> c1:int4){}
%alter rule r210 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func210(c,c10).
%alter function func211(c:int4 -> c1:int4){}
%alter rule r211 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func211(c,c10).
%alter function func212(c:int4 -> c1:int4){}
%alter rule r212 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func212(c,c10).
%alter function func213(c:int4 -> c1:int4){}
%alter rule r213 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func213(c,c10).
%alter function func214(c:int4 -> c1:int4){}
%alter rule r214 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func214(c,c10).
%alter function func215(c:int4 -> c1:int4){}
%alter rule r215 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func215(c,c10).
%alter function func216(c:int4 -> c1:int4){}
%alter rule r216 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func216(c,c10).
%alter function func217(c:int4 -> c1:int4){}
%alter rule r217 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func217(c,c10).
%alter function func218(c:int4 -> c1:int4){}
%alter rule r218 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func218(c,c10).
%alter function func219(c:int4 -> c1:int4){}
%alter rule r219 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func219(c,c10).
%alter function func220(c:int4 -> c1:int4){}
%alter rule r220 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func220(c,c10).
%alter function func221(c:int4 -> c1:int4){}
%alter rule r221 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func221(c,c10).
%alter function func222(c:int4 -> c1:int4){}
%alter rule r222 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func222(c,c10).
%alter function func223(c:int4 -> c1:int4){}
%alter rule r223 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func223(c,c10).
%alter function func224(c:int4 -> c1:int4){}
%alter rule r224 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func224(c,c10).
%alter function func225(c:int4 -> c1:int4){}
%alter rule r225 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func225(c,c10).
%alter function func226(c:int4 -> c1:int4){}
%alter rule r226 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func226(c,c10).
%alter function func227(c:int4 -> c1:int4){}
%alter rule r227 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func227(c,c10).
%alter function func228(c:int4 -> c1:int4){}
%alter rule r228 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func228(c,c10).
%alter function func229(c:int4 -> c1:int4){}
%alter rule r229 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func229(c,c10).
%alter function func230(c:int4 -> c1:int4){}
%alter rule r230 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func230(c,c10).
%alter function func231(c:int4 -> c1:int4){}
%alter rule r231 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func231(c,c10).
%alter function func232(c:int4 -> c1:int4){}
%alter rule r232 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func232(c,c10).
%alter function func233(c:int4 -> c1:int4){}
%alter rule r233 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func233(c,c10).
%alter function func234(c:int4 -> c1:int4){}
%alter rule r234 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func234(c,c10).
%alter function func235(c:int4 -> c1:int4){}
%alter rule r235 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func235(c,c10).
%alter function func236(c:int4 -> c1:int4){}
%alter rule r236 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func236(c,c10).
%alter function func237(c:int4 -> c1:int4){}
%alter rule r237 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func237(c,c10).
%alter function func238(c:int4 -> c1:int4){}
%alter rule r238 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func238(c,c10).
%alter function func239(c:int4 -> c1:int4){}
%alter rule r239 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func239(c,c10).
%alter function func240(c:int4 -> c1:int4){}
%alter rule r240 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func240(c,c10).
%alter function func241(c:int4 -> c1:int4){}
%alter rule r241 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func241(c,c10).
%alter function func242(c:int4 -> c1:int4){}
%alter rule r242 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func242(c,c10).
%alter function func243(c:int4 -> c1:int4){}
%alter rule r243 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func243(c,c10).
%alter function func244(c:int4 -> c1:int4){}
%alter rule r244 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func244(c,c10).
%alter function func245(c:int4 -> c1:int4){}
%alter rule r245 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func245(c,c10).
%alter function func246(c:int4 -> c1:int4){}
%alter rule r246 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func246(c,c10).
%alter function func247(c:int4 -> c1:int4){}
%alter rule r247 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func247(c,c10).
%alter function func248(c:int4 -> c1:int4){}
%alter rule r248 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func248(c,c10).
%alter function func249(c:int4 -> c1:int4){}
%alter rule r249 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func249(c,c10).
%alter function func250(c:int4 -> c1:int4){}
%alter rule r250 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func250(c,c10).
%alter function func251(c:int4 -> c1:int4){}
%alter rule r251 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func251(c,c10).
%alter function func252(c:int4 -> c1:int4){}
%alter rule r252 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func252(c,c10).
%alter function func253(c:int4 -> c1:int4){}
%alter rule r253 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func253(c,c10).
%alter function func254(c:int4 -> c1:int4){}
%alter rule r254 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func254(c,c10).
%alter function func255(c:int4 -> c1:int4){}
%alter rule r255 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func255(c,c10).
%alter function func256(c:int4 -> c1:int4){}
%alter rule r256 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func256(c,c10).
%alter function func257(c:int4 -> c1:int4){}
%alter rule r257 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func257(c,c10).
%alter function func258(c:int4 -> c1:int4){}
%alter rule r258 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func258(c,c10).
%alter function func259(c:int4 -> c1:int4){}
%alter rule r259 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func259(c,c10).
%alter function func260(c:int4 -> c1:int4){}
%alter rule r260 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func260(c,c10).
%alter function func261(c:int4 -> c1:int4){}
%alter rule r261 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func261(c,c10).
%alter function func262(c:int4 -> c1:int4){}
%alter rule r262 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func262(c,c10).
%alter function func263(c:int4 -> c1:int4){}
%alter rule r263 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func263(c,c10).
%alter function func264(c:int4 -> c1:int4){}
%alter rule r264 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func264(c,c10).
%alter function func265(c:int4 -> c1:int4){}
%alter rule r265 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func265(c,c10).
%alter function func266(c:int4 -> c1:int4){}
%alter rule r266 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func266(c,c10).
%alter function func267(c:int4 -> c1:int4){}
%alter rule r267 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func267(c,c10).
%alter function func268(c:int4 -> c1:int4){}
%alter rule r268 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func268(c,c10).
%alter function func269(c:int4 -> c1:int4){}
%alter rule r269 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func269(c,c10).
%alter function func270(c:int4 -> c1:int4){}
%alter rule r270 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func270(c,c10).
%alter function func271(c:int4 -> c1:int4){}
%alter rule r271 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func271(c,c10).
%alter function func272(c:int4 -> c1:int4){}
%alter rule r272 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func272(c,c10).
%alter function func273(c:int4 -> c1:int4){}
%alter rule r273 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func273(c,c10).
%alter function func274(c:int4 -> c1:int4){}
%alter rule r274 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func274(c,c10).
%alter function func275(c:int4 -> c1:int4){}
%alter rule r275 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func275(c,c10).
%alter function func276(c:int4 -> c1:int4){}
%alter rule r276 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func276(c,c10).
%alter function func277(c:int4 -> c1:int4){}
%alter rule r277 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func277(c,c10).
%alter function func278(c:int4 -> c1:int4){}
%alter rule r278 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func278(c,c10).
%alter function func279(c:int4 -> c1:int4){}
%alter rule r279 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func279(c,c10).
%alter function func280(c:int4 -> c1:int4){}
%alter rule r280 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func280(c,c10).
%alter function func281(c:int4 -> c1:int4){}
%alter rule r281 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func281(c,c10).
%alter function func282(c:int4 -> c1:int4){}
%alter rule r282 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func282(c,c10).
%alter function func283(c:int4 -> c1:int4){}
%alter rule r283 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func283(c,c10).
%alter function func284(c:int4 -> c1:int4){}
%alter rule r284 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func284(c,c10).
%alter function func285(c:int4 -> c1:int4){}
%alter rule r285 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func285(c,c10).
%alter function func286(c:int4 -> c1:int4){}
%alter rule r286 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func286(c,c10).
%alter function func287(c:int4 -> c1:int4){}
%alter rule r287 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func287(c,c10).
%alter function func288(c:int4 -> c1:int4){}
%alter rule r288 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func288(c,c10).
%alter function func289(c:int4 -> c1:int4){}
%alter rule r289 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func289(c,c10).
%alter function func290(c:int4 -> c1:int4){}
%alter rule r290 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func290(c,c10).
%alter function func291(c:int4 -> c1:int4){}
%alter rule r291 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func291(c,c10).
%alter function func292(c:int4 -> c1:int4){}
%alter rule r292 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func292(c,c10).
%alter function func293(c:int4 -> c1:int4){}
%alter rule r293 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func293(c,c10).
%alter function func294(c:int4 -> c1:int4){}
%alter rule r294 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func294(c,c10).
%alter function func295(c:int4 -> c1:int4){}
%alter rule r295 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func295(c,c10).
%alter function func296(c:int4 -> c1:int4){}
%alter rule r296 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func296(c,c10).
%alter function func297(c:int4 -> c1:int4){}
%alter rule r297 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func297(c,c10).
%alter function func298(c:int4 -> c1:int4){}
%alter rule r298 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func298(c,c10).
%alter function func299(c:int4 -> c1:int4){}
%alter rule r299 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func299(c,c10).
%alter function func300(c:int4 -> c1:int4){}
%alter rule r300 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func300(c,c10).
%alter function func301(c:int4 -> c1:int4){}
%alter rule r301 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func301(c,c10).
%alter function func302(c:int4 -> c1:int4){}
%alter rule r302 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func302(c,c10).
%alter function func303(c:int4 -> c1:int4){}
%alter rule r303 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func303(c,c10).
%alter function func304(c:int4 -> c1:int4){}
%alter rule r304 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func304(c,c10).
%alter function func305(c:int4 -> c1:int4){}
%alter rule r305 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func305(c,c10).
%alter function func306(c:int4 -> c1:int4){}
%alter rule r306 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func306(c,c10).
%alter function func307(c:int4 -> c1:int4){}
%alter rule r307 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func307(c,c10).
%alter function func308(c:int4 -> c1:int4){}
%alter rule r308 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func308(c,c10).
%alter function func309(c:int4 -> c1:int4){}
%alter rule r309 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func309(c,c10).
%alter function func310(c:int4 -> c1:int4){}
%alter rule r310 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func310(c,c10).
%alter function func311(c:int4 -> c1:int4){}
%alter rule r311 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func311(c,c10).
%alter function func312(c:int4 -> c1:int4){}
%alter rule r312 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func312(c,c10).
%alter function func313(c:int4 -> c1:int4){}
%alter rule r313 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func313(c,c10).
%alter function func314(c:int4 -> c1:int4){}
%alter rule r314 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func314(c,c10).
%alter function func315(c:int4 -> c1:int4){}
%alter rule r315 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func315(c,c10).
%alter function func316(c:int4 -> c1:int4){}
%alter rule r316 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func316(c,c10).
%alter function func317(c:int4 -> c1:int4){}
%alter rule r317 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func317(c,c10).
%alter function func318(c:int4 -> c1:int4){}
%alter rule r318 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func318(c,c10).
%alter function func319(c:int4 -> c1:int4){}
%alter rule r319 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func319(c,c10).
%alter function func320(c:int4 -> c1:int4){}
%alter rule r320 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func320(c,c10).
%alter function func321(c:int4 -> c1:int4){}
%alter rule r321 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func321(c,c10).
%alter function func322(c:int4 -> c1:int4){}
%alter rule r322 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func322(c,c10).
%alter function func323(c:int4 -> c1:int4){}
%alter rule r323 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func323(c,c10).
%alter function func324(c:int4 -> c1:int4){}
%alter rule r324 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func324(c,c10).
%alter function func325(c:int4 -> c1:int4){}
%alter rule r325 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func325(c,c10).
%alter function func326(c:int4 -> c1:int4){}
%alter rule r326 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func326(c,c10).
%alter function func327(c:int4 -> c1:int4){}
%alter rule r327 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func327(c,c10).
%alter function func328(c:int4 -> c1:int4){}
%alter rule r328 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func328(c,c10).
%alter function func329(c:int4 -> c1:int4){}
%alter rule r329 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func329(c,c10).
%alter function func330(c:int4 -> c1:int4){}
%alter rule r330 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func330(c,c10).
%alter function func331(c:int4 -> c1:int4){}
%alter rule r331 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func331(c,c10).
%alter function func332(c:int4 -> c1:int4){}
%alter rule r332 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func332(c,c10).
%alter function func333(c:int4 -> c1:int4){}
%alter rule r333 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func333(c,c10).
%alter function func334(c:int4 -> c1:int4){}
%alter rule r334 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func334(c,c10).
%alter function func335(c:int4 -> c1:int4){}
%alter rule r335 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func335(c,c10).
%alter function func336(c:int4 -> c1:int4){}
%alter rule r336 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func336(c,c10).
%alter function func337(c:int4 -> c1:int4){}
%alter rule r337 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func337(c,c10).
%alter function func338(c:int4 -> c1:int4){}
%alter rule r338 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func338(c,c10).
%alter function func339(c:int4 -> c1:int4){}
%alter rule r339 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func339(c,c10).
%alter function func340(c:int4 -> c1:int4){}
%alter rule r340 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func340(c,c10).
%alter function func341(c:int4 -> c1:int4){}
%alter rule r341 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func341(c,c10).
%alter function func342(c:int4 -> c1:int4){}
%alter rule r342 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func342(c,c10).
%alter function func343(c:int4 -> c1:int4){}
%alter rule r343 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func343(c,c10).
%alter function func344(c:int4 -> c1:int4){}
%alter rule r344 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func344(c,c10).
%alter function func345(c:int4 -> c1:int4){}
%alter rule r345 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func345(c,c10).
%alter function func346(c:int4 -> c1:int4){}
%alter rule r346 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func346(c,c10).
%alter function func347(c:int4 -> c1:int4){}
%alter rule r347 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func347(c,c10).
%alter function func348(c:int4 -> c1:int4){}
%alter rule r348 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func348(c,c10).
%alter function func349(c:int4 -> c1:int4){}
%alter rule r349 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func349(c,c10).
%alter function func350(c:int4 -> c1:int4){}
%alter rule r350 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func350(c,c10).
%alter function func351(c:int4 -> c1:int4){}
%alter rule r351 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func351(c,c10).
%alter function func352(c:int4 -> c1:int4){}
%alter rule r352 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func352(c,c10).
%alter function func353(c:int4 -> c1:int4){}
%alter rule r353 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func353(c,c10).
%alter function func354(c:int4 -> c1:int4){}
%alter rule r354 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func354(c,c10).
%alter function func355(c:int4 -> c1:int4){}
%alter rule r355 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func355(c,c10).
%alter function func356(c:int4 -> c1:int4){}
%alter rule r356 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func356(c,c10).
%alter function func357(c:int4 -> c1:int4){}
%alter rule r357 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func357(c,c10).
%alter function func358(c:int4 -> c1:int4){}
%alter rule r358 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func358(c,c10).
%alter function func359(c:int4 -> c1:int4){}
%alter rule r359 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func359(c,c10).
%alter function func360(c:int4 -> c1:int4){}
%alter rule r360 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func360(c,c10).
%alter function func361(c:int4 -> c1:int4){}
%alter rule r361 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func361(c,c10).
%alter function func362(c:int4 -> c1:int4){}
%alter rule r362 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func362(c,c10).
%alter function func363(c:int4 -> c1:int4){}
%alter rule r363 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func363(c,c10).
%alter function func364(c:int4 -> c1:int4){}
%alter rule r364 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func364(c,c10).
%alter function func365(c:int4 -> c1:int4){}
%alter rule r365 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func365(c,c10).
%alter function func366(c:int4 -> c1:int4){}
%alter rule r366 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func366(c,c10).
%alter function func367(c:int4 -> c1:int4){}
%alter rule r367 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func367(c,c10).
%alter function func368(c:int4 -> c1:int4){}
%alter rule r368 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func368(c,c10).
%alter function func369(c:int4 -> c1:int4){}
%alter rule r369 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func369(c,c10).
%alter function func370(c:int4 -> c1:int4){}
%alter rule r370 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func370(c,c10).
%alter function func371(c:int4 -> c1:int4){}
%alter rule r371 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func371(c,c10).
%alter function func372(c:int4 -> c1:int4){}
%alter rule r372 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func372(c,c10).
%alter function func373(c:int4 -> c1:int4){}
%alter rule r373 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func373(c,c10).
%alter function func374(c:int4 -> c1:int4){}
%alter rule r374 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func374(c,c10).
%alter function func375(c:int4 -> c1:int4){}
%alter rule r375 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func375(c,c10).
%alter function func376(c:int4 -> c1:int4){}
%alter rule r376 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func376(c,c10).
%alter function func377(c:int4 -> c1:int4){}
%alter rule r377 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func377(c,c10).
%alter function func378(c:int4 -> c1:int4){}
%alter rule r378 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func378(c,c10).
%alter function func379(c:int4 -> c1:int4){}
%alter rule r379 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func379(c,c10).
%alter function func380(c:int4 -> c1:int4){}
%alter rule r380 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func380(c,c10).
%alter function func381(c:int4 -> c1:int4){}
%alter rule r381 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func381(c,c10).
%alter function func382(c:int4 -> c1:int4){}
%alter rule r382 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func382(c,c10).
%alter function func383(c:int4 -> c1:int4){}
%alter rule r383 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func383(c,c10).
%alter function func384(c:int4 -> c1:int4){}
%alter rule r384 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func384(c,c10).
%alter function func385(c:int4 -> c1:int4){}
%alter rule r385 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func385(c,c10).
%alter function func386(c:int4 -> c1:int4){}
%alter rule r386 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func386(c,c10).
%alter function func387(c:int4 -> c1:int4){}
%alter rule r387 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func387(c,c10).
%alter function func388(c:int4 -> c1:int4){}
%alter rule r388 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func388(c,c10).
%alter function func389(c:int4 -> c1:int4){}
%alter rule r389 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func389(c,c10).
%alter function func390(c:int4 -> c1:int4){}
%alter rule r390 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func390(c,c10).
%alter function func391(c:int4 -> c1:int4){}
%alter rule r391 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func391(c,c10).
%alter function func392(c:int4 -> c1:int4){}
%alter rule r392 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func392(c,c10).
%alter function func393(c:int4 -> c1:int4){}
%alter rule r393 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func393(c,c10).
%alter function func394(c:int4 -> c1:int4){}
%alter rule r394 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func394(c,c10).
%alter function func395(c:int4 -> c1:int4){}
%alter rule r395 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func395(c,c10).
%alter function func396(c:int4 -> c1:int4){}
%alter rule r396 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func396(c,c10).
%alter function func397(c:int4 -> c1:int4){}
%alter rule r397 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func397(c,c10).
%alter function func398(c:int4 -> c1:int4){}
%alter rule r398 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func398(c,c10).
%alter function func399(c:int4 -> c1:int4){}
%alter rule r399 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func399(c,c10).
%alter function func400(c:int4 -> c1:int4){}
%alter rule r400 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func400(c,c10).
%alter function func401(c:int4 -> c1:int4){}
%alter rule r401 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func401(c,c10).
%alter function func402(c:int4 -> c1:int4){}
%alter rule r402 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func402(c,c10).
%alter function func403(c:int4 -> c1:int4){}
%alter rule r403 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func403(c,c10).
%alter function func404(c:int4 -> c1:int4){}
%alter rule r404 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func404(c,c10).
%alter function func405(c:int4 -> c1:int4){}
%alter rule r405 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func405(c,c10).
%alter function func406(c:int4 -> c1:int4){}
%alter rule r406 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func406(c,c10).
%alter function func407(c:int4 -> c1:int4){}
%alter rule r407 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func407(c,c10).
%alter function func408(c:int4 -> c1:int4){}
%alter rule r408 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func408(c,c10).
%alter function func409(c:int4 -> c1:int4){}
%alter rule r409 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func409(c,c10).
%alter function func410(c:int4 -> c1:int4){}
%alter rule r410 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func410(c,c10).
%alter function func411(c:int4 -> c1:int4){}
%alter rule r411 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func411(c,c10).
%alter function func412(c:int4 -> c1:int4){}
%alter rule r412 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func412(c,c10).
%alter function func413(c:int4 -> c1:int4){}
%alter rule r413 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func413(c,c10).
%alter function func414(c:int4 -> c1:int4){}
%alter rule r414 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func414(c,c10).
%alter function func415(c:int4 -> c1:int4){}
%alter rule r415 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func415(c,c10).
%alter function func416(c:int4 -> c1:int4){}
%alter rule r416 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func416(c,c10).
%alter function func417(c:int4 -> c1:int4){}
%alter rule r417 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func417(c,c10).
%alter function func418(c:int4 -> c1:int4){}
%alter rule r418 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func418(c,c10).
%alter function func419(c:int4 -> c1:int4){}
%alter rule r419 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func419(c,c10).
%alter function func420(c:int4 -> c1:int4){}
%alter rule r420 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func420(c,c10).
%alter function func421(c:int4 -> c1:int4){}
%alter rule r421 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func421(c,c10).
%alter function func422(c:int4 -> c1:int4){}
%alter rule r422 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func422(c,c10).
%alter function func423(c:int4 -> c1:int4){}
%alter rule r423 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func423(c,c10).
%alter function func424(c:int4 -> c1:int4){}
%alter rule r424 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func424(c,c10).
%alter function func425(c:int4 -> c1:int4){}
%alter rule r425 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func425(c,c10).
%alter function func426(c:int4 -> c1:int4){}
%alter rule r426 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func426(c,c10).
%alter function func427(c:int4 -> c1:int4){}
%alter rule r427 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func427(c,c10).
%alter function func428(c:int4 -> c1:int4){}
%alter rule r428 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func428(c,c10).
%alter function func429(c:int4 -> c1:int4){}
%alter rule r429 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func429(c,c10).
%alter function func430(c:int4 -> c1:int4){}
%alter rule r430 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func430(c,c10).
%alter function func431(c:int4 -> c1:int4){}
%alter rule r431 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func431(c,c10).
%alter function func432(c:int4 -> c1:int4){}
%alter rule r432 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func432(c,c10).
%alter function func433(c:int4 -> c1:int4){}
%alter rule r433 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func433(c,c10).
%alter function func434(c:int4 -> c1:int4){}
%alter rule r434 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func434(c,c10).
%alter function func435(c:int4 -> c1:int4){}
%alter rule r435 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func435(c,c10).
%alter function func436(c:int4 -> c1:int4){}
%alter rule r436 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func436(c,c10).
%alter function func437(c:int4 -> c1:int4){}
%alter rule r437 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func437(c,c10).
%alter function func438(c:int4 -> c1:int4){}
%alter rule r438 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func438(c,c10).
%alter function func439(c:int4 -> c1:int4){}
%alter rule r439 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func439(c,c10).
%alter function func440(c:int4 -> c1:int4){}
%alter rule r440 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func440(c,c10).
%alter function func441(c:int4 -> c1:int4){}
%alter rule r441 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func441(c,c10).
%alter function func442(c:int4 -> c1:int4){}
%alter rule r442 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func442(c,c10).
%alter function func443(c:int4 -> c1:int4){}
%alter rule r443 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func443(c,c10).
%alter function func444(c:int4 -> c1:int4){}
%alter rule r444 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func444(c,c10).
%alter function func445(c:int4 -> c1:int4){}
%alter rule r445 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func445(c,c10).
%alter function func446(c:int4 -> c1:int4){}
%alter rule r446 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func446(c,c10).
%alter function func447(c:int4 -> c1:int4){}
%alter rule r447 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func447(c,c10).
%alter function func448(c:int4 -> c1:int4){}
%alter rule r448 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func448(c,c10).
%alter function func449(c:int4 -> c1:int4){}
%alter rule r449 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func449(c,c10).
%alter function func450(c:int4 -> c1:int4){}
%alter rule r450 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func450(c,c10).
%alter function func451(c:int4 -> c1:int4){}
%alter rule r451 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func451(c,c10).
%alter function func452(c:int4 -> c1:int4){}
%alter rule r452 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func452(c,c10).
%alter function func453(c:int4 -> c1:int4){}
%alter rule r453 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func453(c,c10).
%alter function func454(c:int4 -> c1:int4){}
%alter rule r454 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func454(c,c10).
%alter function func455(c:int4 -> c1:int4){}
%alter rule r455 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func455(c,c10).
%alter function func456(c:int4 -> c1:int4){}
%alter rule r456 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func456(c,c10).
%alter function func457(c:int4 -> c1:int4){}
%alter rule r457 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func457(c,c10).
%alter function func458(c:int4 -> c1:int4){}
%alter rule r458 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func458(c,c10).
%alter function func459(c:int4 -> c1:int4){}
%alter rule r459 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func459(c,c10).
%alter function func460(c:int4 -> c1:int4){}
%alter rule r460 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func460(c,c10).
%alter function func461(c:int4 -> c1:int4){}
%alter rule r461 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func461(c,c10).
%alter function func462(c:int4 -> c1:int4){}
%alter rule r462 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func462(c,c10).
%alter function func463(c:int4 -> c1:int4){}
%alter rule r463 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func463(c,c10).
%alter function func464(c:int4 -> c1:int4){}
%alter rule r464 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func464(c,c10).
%alter function func465(c:int4 -> c1:int4){}
%alter rule r465 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func465(c,c10).
%alter function func466(c:int4 -> c1:int4){}
%alter rule r466 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func466(c,c10).
%alter function func467(c:int4 -> c1:int4){}
%alter rule r467 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func467(c,c10).
%alter function func468(c:int4 -> c1:int4){}
%alter rule r468 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func468(c,c10).
%alter function func469(c:int4 -> c1:int4){}
%alter rule r469 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func469(c,c10).
%alter function func470(c:int4 -> c1:int4){}
%alter rule r470 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func470(c,c10).
%alter function func471(c:int4 -> c1:int4){}
%alter rule r471 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func471(c,c10).
%alter function func472(c:int4 -> c1:int4){}
%alter rule r472 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func472(c,c10).
%alter function func473(c:int4 -> c1:int4){}
%alter rule r473 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func473(c,c10).
%alter function func474(c:int4 -> c1:int4){}
%alter rule r474 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func474(c,c10).
%alter function func475(c:int4 -> c1:int4){}
%alter rule r475 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func475(c,c10).
%alter function func476(c:int4 -> c1:int4){}
%alter rule r476 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func476(c,c10).
%alter function func477(c:int4 -> c1:int4){}
%alter rule r477 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func477(c,c10).
%alter function func478(c:int4 -> c1:int4){}
%alter rule r478 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func478(c,c10).
%alter function func479(c:int4 -> c1:int4){}
%alter rule r479 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func479(c,c10).
%alter function func480(c:int4 -> c1:int4){}
%alter rule r480 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func480(c,c10).
%alter function func481(c:int4 -> c1:int4){}
%alter rule r481 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func481(c,c10).
%alter function func482(c:int4 -> c1:int4){}
%alter rule r482 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func482(c,c10).
%alter function func483(c:int4 -> c1:int4){}
%alter rule r483 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func483(c,c10).
%alter function func484(c:int4 -> c1:int4){}
%alter rule r484 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func484(c,c10).
%alter function func485(c:int4 -> c1:int4){}
%alter rule r485 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func485(c,c10).
%alter function func486(c:int4 -> c1:int4){}
%alter rule r486 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func486(c,c10).
%alter function func487(c:int4 -> c1:int4){}
%alter rule r487 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func487(c,c10).
%alter function func488(c:int4 -> c1:int4){}
%alter rule r488 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func488(c,c10).
%alter function func489(c:int4 -> c1:int4){}
%alter rule r489 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func489(c,c10).
%alter function func490(c:int4 -> c1:int4){}
%alter rule r490 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func490(c,c10).
%alter function func491(c:int4 -> c1:int4){}
%alter rule r491 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func491(c,c10).
%alter function func492(c:int4 -> c1:int4){}
%alter rule r492 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func492(c,c10).
%alter function func493(c:int4 -> c1:int4){}
%alter rule r493 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func493(c,c10).
%alter function func494(c:int4 -> c1:int4){}
%alter rule r494 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func494(c,c10).
%alter function func495(c:int4 -> c1:int4){}
%alter rule r495 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func495(c,c10).
%alter function func496(c:int4 -> c1:int4){}
%alter rule r496 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func496(c,c10).
%alter function func497(c:int4 -> c1:int4){}
%alter rule r497 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func497(c,c10).
%alter function func498(c:int4 -> c1:int4){}
%alter rule r498 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func498(c,c10).
%alter function func499(c:int4 -> c1:int4){}
%alter rule r499 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func499(c,c10).
%alter function func500(c:int4 -> c1:int4){}
%alter rule r500 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func500(c,c10).
%alter function func501(c:int4 -> c1:int4){}
%alter rule r501 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func501(c,c10).
%alter function func502(c:int4 -> c1:int4){}
%alter rule r502 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func502(c,c10).
%alter function func503(c:int4 -> c1:int4){}
%alter rule r503 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func503(c,c10).
%alter function func504(c:int4 -> c1:int4){}
%alter rule r504 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func504(c,c10).
%alter function func505(c:int4 -> c1:int4){}
%alter rule r505 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func505(c,c10).
%alter function func506(c:int4 -> c1:int4){}
%alter rule r506 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func506(c,c10).
%alter function func507(c:int4 -> c1:int4){}
%alter rule r507 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func507(c,c10).
%alter function func508(c:int4 -> c1:int4){}
%alter rule r508 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func508(c,c10).
%alter function func509(c:int4 -> c1:int4){}
%alter rule r509 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func509(c,c10).
%alter function func510(c:int4 -> c1:int4){}
%alter rule r510 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func510(c,c10).
%alter function func511(c:int4 -> c1:int4){}
%alter rule r511 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func511(c,c10).
%alter function func512(c:int4 -> c1:int4){}
%alter rule r512 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func512(c,c10).
%alter function func513(c:int4 -> c1:int4){}
%alter rule r513 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func513(c,c10).
%alter function func514(c:int4 -> c1:int4){}
%alter rule r514 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func514(c,c10).
%alter function func515(c:int4 -> c1:int4){}
%alter rule r515 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func515(c,c10).
%alter function func516(c:int4 -> c1:int4){}
%alter rule r516 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func516(c,c10).
%alter function func517(c:int4 -> c1:int4){}
%alter rule r517 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func517(c,c10).
%alter function func518(c:int4 -> c1:int4){}
%alter rule r518 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func518(c,c10).
%alter function func519(c:int4 -> c1:int4){}
%alter rule r519 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func519(c,c10).
%alter function func520(c:int4 -> c1:int4){}
%alter rule r520 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func520(c,c10).
%alter function func521(c:int4 -> c1:int4){}
%alter rule r521 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func521(c,c10).
%alter function func522(c:int4 -> c1:int4){}
%alter rule r522 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func522(c,c10).
%alter function func523(c:int4 -> c1:int4){}
%alter rule r523 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func523(c,c10).
%alter function func524(c:int4 -> c1:int4){}
%alter rule r524 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func524(c,c10).
%alter function func525(c:int4 -> c1:int4){}
%alter rule r525 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func525(c,c10).
%alter function func526(c:int4 -> c1:int4){}
%alter rule r526 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func526(c,c10).
%alter function func527(c:int4 -> c1:int4){}
%alter rule r527 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func527(c,c10).
%alter function func528(c:int4 -> c1:int4){}
%alter rule r528 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func528(c,c10).
%alter function func529(c:int4 -> c1:int4){}
%alter rule r529 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func529(c,c10).
%alter function func530(c:int4 -> c1:int4){}
%alter rule r530 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func530(c,c10).
%alter function func531(c:int4 -> c1:int4){}
%alter rule r531 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func531(c,c10).
%alter function func532(c:int4 -> c1:int4){}
%alter rule r532 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func532(c,c10).
%alter function func533(c:int4 -> c1:int4){}
%alter rule r533 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func533(c,c10).
%alter function func534(c:int4 -> c1:int4){}
%alter rule r534 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func534(c,c10).
%alter function func535(c:int4 -> c1:int4){}
%alter rule r535 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func535(c,c10).
%alter function func536(c:int4 -> c1:int4){}
%alter rule r536 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func536(c,c10).
%alter function func537(c:int4 -> c1:int4){}
%alter rule r537 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func537(c,c10).
%alter function func538(c:int4 -> c1:int4){}
%alter rule r538 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func538(c,c10).
%alter function func539(c:int4 -> c1:int4){}
%alter rule r539 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func539(c,c10).
%alter function func540(c:int4 -> c1:int4){}
%alter rule r540 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func540(c,c10).
%alter function func541(c:int4 -> c1:int4){}
%alter rule r541 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func541(c,c10).
%alter function func542(c:int4 -> c1:int4){}
%alter rule r542 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func542(c,c10).
%alter function func543(c:int4 -> c1:int4){}
%alter rule r543 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func543(c,c10).
%alter function func544(c:int4 -> c1:int4){}
%alter rule r544 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func544(c,c10).
%alter function func545(c:int4 -> c1:int4){}
%alter rule r545 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func545(c,c10).
%alter function func546(c:int4 -> c1:int4){}
%alter rule r546 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func546(c,c10).
%alter function func547(c:int4 -> c1:int4){}
%alter rule r547 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func547(c,c10).
%alter function func548(c:int4 -> c1:int4){}
%alter rule r548 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func548(c,c10).
%alter function func549(c:int4 -> c1:int4){}
%alter rule r549 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func549(c,c10).
%alter function func550(c:int4 -> c1:int4){}
%alter rule r550 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func550(c,c10).
%alter function func551(c:int4 -> c1:int4){}
%alter rule r551 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func551(c,c10).
%alter function func552(c:int4 -> c1:int4){}
%alter rule r552 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func552(c,c10).
%alter function func553(c:int4 -> c1:int4){}
%alter rule r553 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func553(c,c10).
%alter function func554(c:int4 -> c1:int4){}
%alter rule r554 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func554(c,c10).
%alter function func555(c:int4 -> c1:int4){}
%alter rule r555 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func555(c,c10).
%alter function func556(c:int4 -> c1:int4){}
%alter rule r556 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func556(c,c10).
%alter function func557(c:int4 -> c1:int4){}
%alter rule r557 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func557(c,c10).
%alter function func558(c:int4 -> c1:int4){}
%alter rule r558 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func558(c,c10).
%alter function func559(c:int4 -> c1:int4){}
%alter rule r559 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func559(c,c10).
%alter function func560(c:int4 -> c1:int4){}
%alter rule r560 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func560(c,c10).
%alter function func561(c:int4 -> c1:int4){}
%alter rule r561 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func561(c,c10).
%alter function func562(c:int4 -> c1:int4){}
%alter rule r562 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func562(c,c10).
%alter function func563(c:int4 -> c1:int4){}
%alter rule r563 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func563(c,c10).
%alter function func564(c:int4 -> c1:int4){}
%alter rule r564 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func564(c,c10).
%alter function func565(c:int4 -> c1:int4){}
%alter rule r565 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func565(c,c10).
%alter function func566(c:int4 -> c1:int4){}
%alter rule r566 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func566(c,c10).
%alter function func567(c:int4 -> c1:int4){}
%alter rule r567 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func567(c,c10).
%alter function func568(c:int4 -> c1:int4){}
%alter rule r568 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func568(c,c10).
%alter function func569(c:int4 -> c1:int4){}
%alter rule r569 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func569(c,c10).
%alter function func570(c:int4 -> c1:int4){}
%alter rule r570 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func570(c,c10).
%alter function func571(c:int4 -> c1:int4){}
%alter rule r571 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func571(c,c10).
%alter function func572(c:int4 -> c1:int4){}
%alter rule r572 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func572(c,c10).
%alter function func573(c:int4 -> c1:int4){}
%alter rule r573 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func573(c,c10).
%alter function func574(c:int4 -> c1:int4){}
%alter rule r574 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func574(c,c10).
%alter function func575(c:int4 -> c1:int4){}
%alter rule r575 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func575(c,c10).
%alter function func576(c:int4 -> c1:int4){}
%alter rule r576 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func576(c,c10).
%alter function func577(c:int4 -> c1:int4){}
%alter rule r577 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func577(c,c10).
%alter function func578(c:int4 -> c1:int4){}
%alter rule r578 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func578(c,c10).
%alter function func579(c:int4 -> c1:int4){}
%alter rule r579 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func579(c,c10).
%alter function func580(c:int4 -> c1:int4){}
%alter rule r580 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func580(c,c10).
%alter function func581(c:int4 -> c1:int4){}
%alter rule r581 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func581(c,c10).
%alter function func582(c:int4 -> c1:int4){}
%alter rule r582 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func582(c,c10).
%alter function func583(c:int4 -> c1:int4){}
%alter rule r583 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func583(c,c10).
%alter function func584(c:int4 -> c1:int4){}
%alter rule r584 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func584(c,c10).
%alter function func585(c:int4 -> c1:int4){}
%alter rule r585 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func585(c,c10).
%alter function func586(c:int4 -> c1:int4){}
%alter rule r586 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func586(c,c10).
%alter function func587(c:int4 -> c1:int4){}
%alter rule r587 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func587(c,c10).
%alter function func588(c:int4 -> c1:int4){}
%alter rule r588 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func588(c,c10).
%alter function func589(c:int4 -> c1:int4){}
%alter rule r589 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func589(c,c10).
%alter function func590(c:int4 -> c1:int4){}
%alter rule r590 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func590(c,c10).
%alter function func591(c:int4 -> c1:int4){}
%alter rule r591 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func591(c,c10).
%alter function func592(c:int4 -> c1:int4){}
%alter rule r592 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func592(c,c10).
%alter function func593(c:int4 -> c1:int4){}
%alter rule r593 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func593(c,c10).
%alter function func594(c:int4 -> c1:int4){}
%alter rule r594 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func594(c,c10).
%alter function func595(c:int4 -> c1:int4){}
%alter rule r595 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func595(c,c10).
%alter function func596(c:int4 -> c1:int4){}
%alter rule r596 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func596(c,c10).
%alter function func597(c:int4 -> c1:int4){}
%alter rule r597 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func597(c,c10).
%alter function func598(c:int4 -> c1:int4){}
%alter rule r598 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func598(c,c10).
%alter function func599(c:int4 -> c1:int4){}
%alter rule r599 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func599(c,c10).
%alter function func600(c:int4 -> c1:int4){}
%alter rule r600 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func600(c,c10).
%alter function func601(c:int4 -> c1:int4){}
%alter rule r601 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func601(c,c10).
%alter function func602(c:int4 -> c1:int4){}
%alter rule r602 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func602(c,c10).
%alter function func603(c:int4 -> c1:int4){}
%alter rule r603 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func603(c,c10).
%alter function func604(c:int4 -> c1:int4){}
%alter rule r604 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func604(c,c10).
%alter function func605(c:int4 -> c1:int4){}
%alter rule r605 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func605(c,c10).
%alter function func606(c:int4 -> c1:int4){}
%alter rule r606 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func606(c,c10).
%alter function func607(c:int4 -> c1:int4){}
%alter rule r607 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func607(c,c10).
%alter function func608(c:int4 -> c1:int4){}
%alter rule r608 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func608(c,c10).
%alter function func609(c:int4 -> c1:int4){}
%alter rule r609 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func609(c,c10).
%alter function func610(c:int4 -> c1:int4){}
%alter rule r610 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func610(c,c10).
%alter function func611(c:int4 -> c1:int4){}
%alter rule r611 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func611(c,c10).
%alter function func612(c:int4 -> c1:int4){}
%alter rule r612 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func612(c,c10).
%alter function func613(c:int4 -> c1:int4){}
%alter rule r613 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func613(c,c10).
%alter function func614(c:int4 -> c1:int4){}
%alter rule r614 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func614(c,c10).
%alter function func615(c:int4 -> c1:int4){}
%alter rule r615 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func615(c,c10).
%alter function func616(c:int4 -> c1:int4){}
%alter rule r616 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func616(c,c10).
%alter function func617(c:int4 -> c1:int4){}
%alter rule r617 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func617(c,c10).
%alter function func618(c:int4 -> c1:int4){}
%alter rule r618 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func618(c,c10).
%alter function func619(c:int4 -> c1:int4){}
%alter rule r619 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func619(c,c10).
%alter function func620(c:int4 -> c1:int4){}
%alter rule r620 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func620(c,c10).
%alter function func621(c:int4 -> c1:int4){}
%alter rule r621 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func621(c,c10).
%alter function func622(c:int4 -> c1:int4){}
%alter rule r622 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func622(c,c10).
%alter function func623(c:int4 -> c1:int4){}
%alter rule r623 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func623(c,c10).
%alter function func624(c:int4 -> c1:int4){}
%alter rule r624 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func624(c,c10).
%alter function func625(c:int4 -> c1:int4){}
%alter rule r625 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func625(c,c10).
%alter function func626(c:int4 -> c1:int4){}
%alter rule r626 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func626(c,c10).
%alter function func627(c:int4 -> c1:int4){}
%alter rule r627 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func627(c,c10).
%alter function func628(c:int4 -> c1:int4){}
%alter rule r628 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func628(c,c10).
%alter function func629(c:int4 -> c1:int4){}
%alter rule r629 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func629(c,c10).
%alter function func630(c:int4 -> c1:int4){}
%alter rule r630 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func630(c,c10).
%alter function func631(c:int4 -> c1:int4){}
%alter rule r631 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func631(c,c10).
%alter function func632(c:int4 -> c1:int4){}
%alter rule r632 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func632(c,c10).
%alter function func633(c:int4 -> c1:int4){}
%alter rule r633 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func633(c,c10).
%alter function func634(c:int4 -> c1:int4){}
%alter rule r634 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func634(c,c10).
%alter function func635(c:int4 -> c1:int4){}
%alter rule r635 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func635(c,c10).
%alter function func636(c:int4 -> c1:int4){}
%alter rule r636 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func636(c,c10).
%alter function func637(c:int4 -> c1:int4){}
%alter rule r637 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func637(c,c10).
%alter function func638(c:int4 -> c1:int4){}
%alter rule r638 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func638(c,c10).
%alter function func639(c:int4 -> c1:int4){}
%alter rule r639 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func639(c,c10).
%alter function func640(c:int4 -> c1:int4){}
%alter rule r640 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func640(c,c10).
%alter function func641(c:int4 -> c1:int4){}
%alter rule r641 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func641(c,c10).
%alter function func642(c:int4 -> c1:int4){}
%alter rule r642 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func642(c,c10).
%alter function func643(c:int4 -> c1:int4){}
%alter rule r643 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func643(c,c10).
%alter function func644(c:int4 -> c1:int4){}
%alter rule r644 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func644(c,c10).
%alter function func645(c:int4 -> c1:int4){}
%alter rule r645 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func645(c,c10).
%alter function func646(c:int4 -> c1:int4){}
%alter rule r646 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func646(c,c10).
%alter function func647(c:int4 -> c1:int4){}
%alter rule r647 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func647(c,c10).
%alter function func648(c:int4 -> c1:int4){}
%alter rule r648 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func648(c,c10).
%alter function func649(c:int4 -> c1:int4){}
%alter rule r649 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func649(c,c10).
%alter function func650(c:int4 -> c1:int4){}
%alter rule r650 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func650(c,c10).
%alter function func651(c:int4 -> c1:int4){}
%alter rule r651 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func651(c,c10).
%alter function func652(c:int4 -> c1:int4){}
%alter rule r652 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func652(c,c10).
%alter function func653(c:int4 -> c1:int4){}
%alter rule r653 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func653(c,c10).
%alter function func654(c:int4 -> c1:int4){}
%alter rule r654 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func654(c,c10).
%alter function func655(c:int4 -> c1:int4){}
%alter rule r655 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func655(c,c10).
%alter function func656(c:int4 -> c1:int4){}
%alter rule r656 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func656(c,c10).
%alter function func657(c:int4 -> c1:int4){}
%alter rule r657 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func657(c,c10).
%alter function func658(c:int4 -> c1:int4){}
%alter rule r658 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func658(c,c10).
%alter function func659(c:int4 -> c1:int4){}
%alter rule r659 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func659(c,c10).
%alter function func660(c:int4 -> c1:int4){}
%alter rule r660 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func660(c,c10).
%alter function func661(c:int4 -> c1:int4){}
%alter rule r661 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func661(c,c10).
%alter function func662(c:int4 -> c1:int4){}
%alter rule r662 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func662(c,c10).
%alter function func663(c:int4 -> c1:int4){}
%alter rule r663 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func663(c,c10).
%alter function func664(c:int4 -> c1:int4){}
%alter rule r664 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func664(c,c10).
%alter function func665(c:int4 -> c1:int4){}
%alter rule r665 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func665(c,c10).
%alter function func666(c:int4 -> c1:int4){}
%alter rule r666 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func666(c,c10).
%alter function func667(c:int4 -> c1:int4){}
%alter rule r667 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func667(c,c10).
%alter function func668(c:int4 -> c1:int4){}
%alter rule r668 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func668(c,c10).
%alter function func669(c:int4 -> c1:int4){}
%alter rule r669 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func669(c,c10).
%alter function func670(c:int4 -> c1:int4){}
%alter rule r670 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func670(c,c10).
%alter function func671(c:int4 -> c1:int4){}
%alter rule r671 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func671(c,c10).
%alter function func672(c:int4 -> c1:int4){}
%alter rule r672 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func672(c,c10).
%alter function func673(c:int4 -> c1:int4){}
%alter rule r673 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func673(c,c10).
%alter function func674(c:int4 -> c1:int4){}
%alter rule r674 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func674(c,c10).
%alter function func675(c:int4 -> c1:int4){}
%alter rule r675 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func675(c,c10).
%alter function func676(c:int4 -> c1:int4){}
%alter rule r676 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func676(c,c10).
%alter function func677(c:int4 -> c1:int4){}
%alter rule r677 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func677(c,c10).
%alter function func678(c:int4 -> c1:int4){}
%alter rule r678 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func678(c,c10).
%alter function func679(c:int4 -> c1:int4){}
%alter rule r679 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func679(c,c10).
%alter function func680(c:int4 -> c1:int4){}
%alter rule r680 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func680(c,c10).
%alter function func681(c:int4 -> c1:int4){}
%alter rule r681 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func681(c,c10).
%alter function func682(c:int4 -> c1:int4){}
%alter rule r682 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func682(c,c10).
%alter function func683(c:int4 -> c1:int4){}
%alter rule r683 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func683(c,c10).
%alter function func684(c:int4 -> c1:int4){}
%alter rule r684 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func684(c,c10).
%alter function func685(c:int4 -> c1:int4){}
%alter rule r685 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func685(c,c10).
%alter function func686(c:int4 -> c1:int4){}
%alter rule r686 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func686(c,c10).
%alter function func687(c:int4 -> c1:int4){}
%alter rule r687 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func687(c,c10).
%alter function func688(c:int4 -> c1:int4){}
%alter rule r688 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func688(c,c10).
%alter function func689(c:int4 -> c1:int4){}
%alter rule r689 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func689(c,c10).
%alter function func690(c:int4 -> c1:int4){}
%alter rule r690 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func690(c,c10).
%alter function func691(c:int4 -> c1:int4){}
%alter rule r691 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func691(c,c10).
%alter function func692(c:int4 -> c1:int4){}
%alter rule r692 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func692(c,c10).
%alter function func693(c:int4 -> c1:int4){}
%alter rule r693 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func693(c,c10).
%alter function func694(c:int4 -> c1:int4){}
%alter rule r694 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func694(c,c10).
%alter function func695(c:int4 -> c1:int4){}
%alter rule r695 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func695(c,c10).
%alter function func696(c:int4 -> c1:int4){}
%alter rule r696 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func696(c,c10).
%alter function func697(c:int4 -> c1:int4){}
%alter rule r697 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func697(c,c10).
%alter function func698(c:int4 -> c1:int4){}
%alter rule r698 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func698(c,c10).
%alter function func699(c:int4 -> c1:int4){}
%alter rule r699 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func699(c,c10).
%alter function func700(c:int4 -> c1:int4){}
%alter rule r700 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func700(c,c10).
%alter function func701(c:int4 -> c1:int4){}
%alter rule r701 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func701(c,c10).
%alter function func702(c:int4 -> c1:int4){}
%alter rule r702 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func702(c,c10).
%alter function func703(c:int4 -> c1:int4){}
%alter rule r703 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func703(c,c10).
%alter function func704(c:int4 -> c1:int4){}
%alter rule r704 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func704(c,c10).
%alter function func705(c:int4 -> c1:int4){}
%alter rule r705 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func705(c,c10).
%alter function func706(c:int4 -> c1:int4){}
%alter rule r706 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func706(c,c10).
%alter function func707(c:int4 -> c1:int4){}
%alter rule r707 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func707(c,c10).
%alter function func708(c:int4 -> c1:int4){}
%alter rule r708 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func708(c,c10).
%alter function func709(c:int4 -> c1:int4){}
%alter rule r709 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func709(c,c10).
%alter function func710(c:int4 -> c1:int4){}
%alter rule r710 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func710(c,c10).
%alter function func711(c:int4 -> c1:int4){}
%alter rule r711 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func711(c,c10).
%alter function func712(c:int4 -> c1:int4){}
%alter rule r712 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func712(c,c10).
%alter function func713(c:int4 -> c1:int4){}
%alter rule r713 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func713(c,c10).
%alter function func714(c:int4 -> c1:int4){}
%alter rule r714 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func714(c,c10).
%alter function func715(c:int4 -> c1:int4){}
%alter rule r715 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func715(c,c10).
%alter function func716(c:int4 -> c1:int4){}
%alter rule r716 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func716(c,c10).
%alter function func717(c:int4 -> c1:int4){}
%alter rule r717 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func717(c,c10).
%alter function func718(c:int4 -> c1:int4){}
%alter rule r718 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func718(c,c10).
%alter function func719(c:int4 -> c1:int4){}
%alter rule r719 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func719(c,c10).
%alter function func720(c:int4 -> c1:int4){}
%alter rule r720 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func720(c,c10).
%alter function func721(c:int4 -> c1:int4){}
%alter rule r721 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func721(c,c10).
%alter function func722(c:int4 -> c1:int4){}
%alter rule r722 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func722(c,c10).
%alter function func723(c:int4 -> c1:int4){}
%alter rule r723 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func723(c,c10).
%alter function func724(c:int4 -> c1:int4){}
%alter rule r724 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func724(c,c10).
%alter function func725(c:int4 -> c1:int4){}
%alter rule r725 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func725(c,c10).
%alter function func726(c:int4 -> c1:int4){}
%alter rule r726 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func726(c,c10).
%alter function func727(c:int4 -> c1:int4){}
%alter rule r727 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func727(c,c10).
%alter function func728(c:int4 -> c1:int4){}
%alter rule r728 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func728(c,c10).
%alter function func729(c:int4 -> c1:int4){}
%alter rule r729 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func729(c,c10).
%alter function func730(c:int4 -> c1:int4){}
%alter rule r730 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func730(c,c10).
%alter function func731(c:int4 -> c1:int4){}
%alter rule r731 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func731(c,c10).
%alter function func732(c:int4 -> c1:int4){}
%alter rule r732 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func732(c,c10).
%alter function func733(c:int4 -> c1:int4){}
%alter rule r733 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func733(c,c10).
%alter function func734(c:int4 -> c1:int4){}
%alter rule r734 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func734(c,c10).
%alter function func735(c:int4 -> c1:int4){}
%alter rule r735 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func735(c,c10).
%alter function func736(c:int4 -> c1:int4){}
%alter rule r736 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func736(c,c10).
%alter function func737(c:int4 -> c1:int4){}
%alter rule r737 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func737(c,c10).
%alter function func738(c:int4 -> c1:int4){}
%alter rule r738 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func738(c,c10).
%alter function func739(c:int4 -> c1:int4){}
%alter rule r739 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func739(c,c10).
%alter function func740(c:int4 -> c1:int4){}
%alter rule r740 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func740(c,c10).
%alter function func741(c:int4 -> c1:int4){}
%alter rule r741 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func741(c,c10).
%alter function func742(c:int4 -> c1:int4){}
%alter rule r742 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func742(c,c10).
%alter function func743(c:int4 -> c1:int4){}
%alter rule r743 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func743(c,c10).
%alter function func744(c:int4 -> c1:int4){}
%alter rule r744 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func744(c,c10).
%alter function func745(c:int4 -> c1:int4){}
%alter rule r745 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func745(c,c10).
%alter function func746(c:int4 -> c1:int4){}
%alter rule r746 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func746(c,c10).
%alter function func747(c:int4 -> c1:int4){}
%alter rule r747 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func747(c,c10).
%alter function func748(c:int4 -> c1:int4){}
%alter rule r748 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func748(c,c10).
%alter function func749(c:int4 -> c1:int4){}
%alter rule r749 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func749(c,c10).
%alter function func750(c:int4 -> c1:int4){}
%alter rule r750 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func750(c,c10).
%alter function func751(c:int4 -> c1:int4){}
%alter rule r751 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func751(c,c10).
%alter function func752(c:int4 -> c1:int4){}
%alter rule r752 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func752(c,c10).
%alter function func753(c:int4 -> c1:int4){}
%alter rule r753 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func753(c,c10).
%alter function func754(c:int4 -> c1:int4){}
%alter rule r754 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func754(c,c10).
%alter function func755(c:int4 -> c1:int4){}
%alter rule r755 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func755(c,c10).
%alter function func756(c:int4 -> c1:int4){}
%alter rule r756 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func756(c,c10).
%alter function func757(c:int4 -> c1:int4){}
%alter rule r757 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func757(c,c10).
%alter function func758(c:int4 -> c1:int4){}
%alter rule r758 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func758(c,c10).
%alter function func759(c:int4 -> c1:int4){}
%alter rule r759 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func759(c,c10).
%alter function func760(c:int4 -> c1:int4){}
%alter rule r760 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func760(c,c10).
%alter function func761(c:int4 -> c1:int4){}
%alter rule r761 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func761(c,c10).
%alter function func762(c:int4 -> c1:int4){}
%alter rule r762 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func762(c,c10).
%alter function func763(c:int4 -> c1:int4){}
%alter rule r763 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func763(c,c10).
%alter function func764(c:int4 -> c1:int4){}
%alter rule r764 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func764(c,c10).
%alter function func765(c:int4 -> c1:int4){}
%alter rule r765 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func765(c,c10).
%alter function func766(c:int4 -> c1:int4){}
%alter rule r766 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func766(c,c10).
%alter function func767(c:int4 -> c1:int4){}
%alter rule r767 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func767(c,c10).
%alter function func768(c:int4 -> c1:int4){}
%alter rule r768 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func768(c,c10).
%alter function func769(c:int4 -> c1:int4){}
%alter rule r769 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func769(c,c10).
%alter function func770(c:int4 -> c1:int4){}
%alter rule r770 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func770(c,c10).
%alter function func771(c:int4 -> c1:int4){}
%alter rule r771 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func771(c,c10).
%alter function func772(c:int4 -> c1:int4){}
%alter rule r772 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func772(c,c10).
%alter function func773(c:int4 -> c1:int4){}
%alter rule r773 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func773(c,c10).
%alter function func774(c:int4 -> c1:int4){}
%alter rule r774 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func774(c,c10).
%alter function func775(c:int4 -> c1:int4){}
%alter rule r775 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func775(c,c10).
%alter function func776(c:int4 -> c1:int4){}
%alter rule r776 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func776(c,c10).
%alter function func777(c:int4 -> c1:int4){}
%alter rule r777 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func777(c,c10).
%alter function func778(c:int4 -> c1:int4){}
%alter rule r778 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func778(c,c10).
%alter function func779(c:int4 -> c1:int4){}
%alter rule r779 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func779(c,c10).
%alter function func780(c:int4 -> c1:int4){}
%alter rule r780 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func780(c,c10).
%alter function func781(c:int4 -> c1:int4){}
%alter rule r781 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func781(c,c10).
%alter function func782(c:int4 -> c1:int4){}
%alter rule r782 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func782(c,c10).
%alter function func783(c:int4 -> c1:int4){}
%alter rule r783 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func783(c,c10).
%alter function func784(c:int4 -> c1:int4){}
%alter rule r784 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func784(c,c10).
%alter function func785(c:int4 -> c1:int4){}
%alter rule r785 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func785(c,c10).
%alter function func786(c:int4 -> c1:int4){}
%alter rule r786 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func786(c,c10).
%alter function func787(c:int4 -> c1:int4){}
%alter rule r787 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func787(c,c10).
%alter function func788(c:int4 -> c1:int4){}
%alter rule r788 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func788(c,c10).
%alter function func789(c:int4 -> c1:int4){}
%alter rule r789 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func789(c,c10).
%alter function func790(c:int4 -> c1:int4){}
%alter rule r790 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func790(c,c10).
%alter function func791(c:int4 -> c1:int4){}
%alter rule r791 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func791(c,c10).
%alter function func792(c:int4 -> c1:int4){}
%alter rule r792 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func792(c,c10).
%alter function func793(c:int4 -> c1:int4){}
%alter rule r793 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func793(c,c10).
%alter function func794(c:int4 -> c1:int4){}
%alter rule r794 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func794(c,c10).
%alter function func795(c:int4 -> c1:int4){}
%alter rule r795 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func795(c,c10).
%alter function func796(c:int4 -> c1:int4){}
%alter rule r796 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func796(c,c10).
%alter function func797(c:int4 -> c1:int4){}
%alter rule r797 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func797(c,c10).
%alter function func798(c:int4 -> c1:int4){}
%alter rule r798 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func798(c,c10).
%alter function func799(c:int4 -> c1:int4){}
%alter rule r799 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func799(c,c10).
%alter function func800(c:int4 -> c1:int4){}
%alter rule r800 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func800(c,c10).
%alter function func801(c:int4 -> c1:int4){}
%alter rule r801 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func801(c,c10).
%alter function func802(c:int4 -> c1:int4){}
%alter rule r802 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func802(c,c10).
%alter function func803(c:int4 -> c1:int4){}
%alter rule r803 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func803(c,c10).
%alter function func804(c:int4 -> c1:int4){}
%alter rule r804 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func804(c,c10).
%alter function func805(c:int4 -> c1:int4){}
%alter rule r805 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func805(c,c10).
%alter function func806(c:int4 -> c1:int4){}
%alter rule r806 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func806(c,c10).
%alter function func807(c:int4 -> c1:int4){}
%alter rule r807 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func807(c,c10).
%alter function func808(c:int4 -> c1:int4){}
%alter rule r808 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func808(c,c10).
%alter function func809(c:int4 -> c1:int4){}
%alter rule r809 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func809(c,c10).
%alter function func810(c:int4 -> c1:int4){}
%alter rule r810 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func810(c,c10).
%alter function func811(c:int4 -> c1:int4){}
%alter rule r811 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func811(c,c10).
%alter function func812(c:int4 -> c1:int4){}
%alter rule r812 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func812(c,c10).
%alter function func813(c:int4 -> c1:int4){}
%alter rule r813 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func813(c,c10).
%alter function func814(c:int4 -> c1:int4){}
%alter rule r814 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func814(c,c10).
%alter function func815(c:int4 -> c1:int4){}
%alter rule r815 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func815(c,c10).
%alter function func816(c:int4 -> c1:int4){}
%alter rule r816 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func816(c,c10).
%alter function func817(c:int4 -> c1:int4){}
%alter rule r817 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func817(c,c10).
%alter function func818(c:int4 -> c1:int4){}
%alter rule r818 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func818(c,c10).
%alter function func819(c:int4 -> c1:int4){}
%alter rule r819 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func819(c,c10).
%alter function func820(c:int4 -> c1:int4){}
%alter rule r820 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func820(c,c10).
%alter function func821(c:int4 -> c1:int4){}
%alter rule r821 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func821(c,c10).
%alter function func822(c:int4 -> c1:int4){}
%alter rule r822 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func822(c,c10).
%alter function func823(c:int4 -> c1:int4){}
%alter rule r823 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func823(c,c10).
%alter function func824(c:int4 -> c1:int4){}
%alter rule r824 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func824(c,c10).
%alter function func825(c:int4 -> c1:int4){}
%alter rule r825 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func825(c,c10).
%alter function func826(c:int4 -> c1:int4){}
%alter rule r826 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func826(c,c10).
%alter function func827(c:int4 -> c1:int4){}
%alter rule r827 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func827(c,c10).
%alter function func828(c:int4 -> c1:int4){}
%alter rule r828 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func828(c,c10).
%alter function func829(c:int4 -> c1:int4){}
%alter rule r829 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func829(c,c10).
%alter function func830(c:int4 -> c1:int4){}
%alter rule r830 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func830(c,c10).
%alter function func831(c:int4 -> c1:int4){}
%alter rule r831 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func831(c,c10).
%alter function func832(c:int4 -> c1:int4){}
%alter rule r832 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func832(c,c10).
%alter function func833(c:int4 -> c1:int4){}
%alter rule r833 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func833(c,c10).
%alter function func834(c:int4 -> c1:int4){}
%alter rule r834 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func834(c,c10).
%alter function func835(c:int4 -> c1:int4){}
%alter rule r835 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func835(c,c10).
%alter function func836(c:int4 -> c1:int4){}
%alter rule r836 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func836(c,c10).
%alter function func837(c:int4 -> c1:int4){}
%alter rule r837 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func837(c,c10).
%alter function func838(c:int4 -> c1:int4){}
%alter rule r838 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func838(c,c10).
%alter function func839(c:int4 -> c1:int4){}
%alter rule r839 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func839(c,c10).
%alter function func840(c:int4 -> c1:int4){}
%alter rule r840 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func840(c,c10).
%alter function func841(c:int4 -> c1:int4){}
%alter rule r841 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func841(c,c10).
%alter function func842(c:int4 -> c1:int4){}
%alter rule r842 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func842(c,c10).
%alter function func843(c:int4 -> c1:int4){}
%alter rule r843 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func843(c,c10).
%alter function func844(c:int4 -> c1:int4){}
%alter rule r844 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func844(c,c10).
%alter function func845(c:int4 -> c1:int4){}
%alter rule r845 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func845(c,c10).
%alter function func846(c:int4 -> c1:int4){}
%alter rule r846 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func846(c,c10).
%alter function func847(c:int4 -> c1:int4){}
%alter rule r847 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func847(c,c10).
%alter function func848(c:int4 -> c1:int4){}
%alter rule r848 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func848(c,c10).
%alter function func849(c:int4 -> c1:int4){}
%alter rule r849 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func849(c,c10).
%alter function func850(c:int4 -> c1:int4){}
%alter rule r850 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func850(c,c10).
%alter function func851(c:int4 -> c1:int4){}
%alter rule r851 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func851(c,c10).
%alter function func852(c:int4 -> c1:int4){}
%alter rule r852 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func852(c,c10).
%alter function func853(c:int4 -> c1:int4){}
%alter rule r853 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func853(c,c10).
%alter function func854(c:int4 -> c1:int4){}
%alter rule r854 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func854(c,c10).
%alter function func855(c:int4 -> c1:int4){}
%alter rule r855 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func855(c,c10).
%alter function func856(c:int4 -> c1:int4){}
%alter rule r856 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func856(c,c10).
%alter function func857(c:int4 -> c1:int4){}
%alter rule r857 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func857(c,c10).
%alter function func858(c:int4 -> c1:int4){}
%alter rule r858 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func858(c,c10).
%alter function func859(c:int4 -> c1:int4){}
%alter rule r859 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func859(c,c10).
%alter function func860(c:int4 -> c1:int4){}
%alter rule r860 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func860(c,c10).
%alter function func861(c:int4 -> c1:int4){}
%alter rule r861 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func861(c,c10).
%alter function func862(c:int4 -> c1:int4){}
%alter rule r862 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func862(c,c10).
%alter function func863(c:int4 -> c1:int4){}
%alter rule r863 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func863(c,c10).
%alter function func864(c:int4 -> c1:int4){}
%alter rule r864 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func864(c,c10).
%alter function func865(c:int4 -> c1:int4){}
%alter rule r865 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func865(c,c10).
%alter function func866(c:int4 -> c1:int4){}
%alter rule r866 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func866(c,c10).
%alter function func867(c:int4 -> c1:int4){}
%alter rule r867 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func867(c,c10).
%alter function func868(c:int4 -> c1:int4){}
%alter rule r868 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func868(c,c10).
%alter function func869(c:int4 -> c1:int4){}
%alter rule r869 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func869(c,c10).
%alter function func870(c:int4 -> c1:int4){}
%alter rule r870 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func870(c,c10).
%alter function func871(c:int4 -> c1:int4){}
%alter rule r871 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func871(c,c10).
%alter function func872(c:int4 -> c1:int4){}
%alter rule r872 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func872(c,c10).
%alter function func873(c:int4 -> c1:int4){}
%alter rule r873 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func873(c,c10).
%alter function func874(c:int4 -> c1:int4){}
%alter rule r874 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func874(c,c10).
%alter function func875(c:int4 -> c1:int4){}
%alter rule r875 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func875(c,c10).
%alter function func876(c:int4 -> c1:int4){}
%alter rule r876 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func876(c,c10).
%alter function func877(c:int4 -> c1:int4){}
%alter rule r877 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func877(c,c10).
%alter function func878(c:int4 -> c1:int4){}
%alter rule r878 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func878(c,c10).
%alter function func879(c:int4 -> c1:int4){}
%alter rule r879 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func879(c,c10).
%alter function func880(c:int4 -> c1:int4){}
%alter rule r880 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func880(c,c10).
%alter function func881(c:int4 -> c1:int4){}
%alter rule r881 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func881(c,c10).
%alter function func882(c:int4 -> c1:int4){}
%alter rule r882 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func882(c,c10).
%alter function func883(c:int4 -> c1:int4){}
%alter rule r883 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func883(c,c10).
%alter function func884(c:int4 -> c1:int4){}
%alter rule r884 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func884(c,c10).
%alter function func885(c:int4 -> c1:int4){}
%alter rule r885 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func885(c,c10).
%alter function func886(c:int4 -> c1:int4){}
%alter rule r886 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func886(c,c10).
%alter function func887(c:int4 -> c1:int4){}
%alter rule r887 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func887(c,c10).
%alter function func888(c:int4 -> c1:int4){}
%alter rule r888 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func888(c,c10).
%alter function func889(c:int4 -> c1:int4){}
%alter rule r889 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func889(c,c10).
%alter function func890(c:int4 -> c1:int4){}
%alter rule r890 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func890(c,c10).
%alter function func891(c:int4 -> c1:int4){}
%alter rule r891 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func891(c,c10).
%alter function func892(c:int4 -> c1:int4){}
%alter rule r892 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func892(c,c10).
%alter function func893(c:int4 -> c1:int4){}
%alter rule r893 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func893(c,c10).
%alter function func894(c:int4 -> c1:int4){}
%alter rule r894 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func894(c,c10).
%alter function func895(c:int4 -> c1:int4){}
%alter rule r895 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func895(c,c10).
%alter function func896(c:int4 -> c1:int4){}
%alter rule r896 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func896(c,c10).
%alter function func897(c:int4 -> c1:int4){}
%alter rule r897 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func897(c,c10).
%alter function func898(c:int4 -> c1:int4){}
%alter rule r898 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func898(c,c10).
%alter function func899(c:int4 -> c1:int4){}
%alter rule r899 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func899(c,c10).
%alter function func900(c:int4 -> c1:int4){}
%alter rule r900 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func900(c,c10).
%alter function func901(c:int4 -> c1:int4){}
%alter rule r901 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func901(c,c10).
%alter function func902(c:int4 -> c1:int4){}
%alter rule r902 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func902(c,c10).
%alter function func903(c:int4 -> c1:int4){}
%alter rule r903 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func903(c,c10).
%alter function func904(c:int4 -> c1:int4){}
%alter rule r904 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func904(c,c10).
%alter function func905(c:int4 -> c1:int4){}
%alter rule r905 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func905(c,c10).
%alter function func906(c:int4 -> c1:int4){}
%alter rule r906 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func906(c,c10).
%alter function func907(c:int4 -> c1:int4){}
%alter rule r907 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func907(c,c10).
%alter function func908(c:int4 -> c1:int4){}
%alter rule r908 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func908(c,c10).
%alter function func909(c:int4 -> c1:int4){}
%alter rule r909 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func909(c,c10).
%alter function func910(c:int4 -> c1:int4){}
%alter rule r910 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func910(c,c10).
%alter function func911(c:int4 -> c1:int4){}
%alter rule r911 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func911(c,c10).
%alter function func912(c:int4 -> c1:int4){}
%alter rule r912 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func912(c,c10).
%alter function func913(c:int4 -> c1:int4){}
%alter rule r913 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func913(c,c10).
%alter function func914(c:int4 -> c1:int4){}
%alter rule r914 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func914(c,c10).
%alter function func915(c:int4 -> c1:int4){}
%alter rule r915 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func915(c,c10).
%alter function func916(c:int4 -> c1:int4){}
%alter rule r916 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func916(c,c10).
%alter function func917(c:int4 -> c1:int4){}
%alter rule r917 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func917(c,c10).
%alter function func918(c:int4 -> c1:int4){}
%alter rule r918 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func918(c,c10).
%alter function func919(c:int4 -> c1:int4){}
%alter rule r919 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func919(c,c10).
%alter function func920(c:int4 -> c1:int4){}
%alter rule r920 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func920(c,c10).
%alter function func921(c:int4 -> c1:int4){}
%alter rule r921 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func921(c,c10).
%alter function func922(c:int4 -> c1:int4){}
%alter rule r922 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func922(c,c10).
%alter function func923(c:int4 -> c1:int4){}
%alter rule r923 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func923(c,c10).
%alter function func924(c:int4 -> c1:int4){}
%alter rule r924 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func924(c,c10).
%alter function func925(c:int4 -> c1:int4){}
%alter rule r925 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func925(c,c10).
%alter function func926(c:int4 -> c1:int4){}
%alter rule r926 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func926(c,c10).
%alter function func927(c:int4 -> c1:int4){}
%alter rule r927 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func927(c,c10).
%alter function func928(c:int4 -> c1:int4){}
%alter rule r928 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func928(c,c10).
%alter function func929(c:int4 -> c1:int4){}
%alter rule r929 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func929(c,c10).
%alter function func930(c:int4 -> c1:int4){}
%alter rule r930 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func930(c,c10).
%alter function func931(c:int4 -> c1:int4){}
%alter rule r931 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func931(c,c10).
%alter function func932(c:int4 -> c1:int4){}
%alter rule r932 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func932(c,c10).
%alter function func933(c:int4 -> c1:int4){}
%alter rule r933 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func933(c,c10).
%alter function func934(c:int4 -> c1:int4){}
%alter rule r934 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func934(c,c10).
%alter function func935(c:int4 -> c1:int4){}
%alter rule r935 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func935(c,c10).
%alter function func936(c:int4 -> c1:int4){}
%alter rule r936 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func936(c,c10).
%alter function func937(c:int4 -> c1:int4){}
%alter rule r937 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func937(c,c10).
%alter function func938(c:int4 -> c1:int4){}
%alter rule r938 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func938(c,c10).
%alter function func939(c:int4 -> c1:int4){}
%alter rule r939 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func939(c,c10).
%alter function func940(c:int4 -> c1:int4){}
%alter rule r940 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func940(c,c10).
%alter function func941(c:int4 -> c1:int4){}
%alter rule r941 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func941(c,c10).
%alter function func942(c:int4 -> c1:int4){}
%alter rule r942 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func942(c,c10).
%alter function func943(c:int4 -> c1:int4){}
%alter rule r943 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func943(c,c10).
%alter function func944(c:int4 -> c1:int4){}
%alter rule r944 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func944(c,c10).
%alter function func945(c:int4 -> c1:int4){}
%alter rule r945 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func945(c,c10).
%alter function func946(c:int4 -> c1:int4){}
%alter rule r946 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func946(c,c10).
%alter function func947(c:int4 -> c1:int4){}
%alter rule r947 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func947(c,c10).
%alter function func948(c:int4 -> c1:int4){}
%alter rule r948 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func948(c,c10).
%alter function func949(c:int4 -> c1:int4){}
%alter rule r949 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func949(c,c10).
%alter function func950(c:int4 -> c1:int4){}
%alter rule r950 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func950(c,c10).
%alter function func951(c:int4 -> c1:int4){}
%alter rule r951 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func951(c,c10).
%alter function func952(c:int4 -> c1:int4){}
%alter rule r952 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func952(c,c10).
%alter function func953(c:int4 -> c1:int4){}
%alter rule r953 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func953(c,c10).
%alter function func954(c:int4 -> c1:int4){}
%alter rule r954 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func954(c,c10).
%alter function func955(c:int4 -> c1:int4){}
%alter rule r955 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func955(c,c10).
%alter function func956(c:int4 -> c1:int4){}
%alter rule r956 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func956(c,c10).
%alter function func957(c:int4 -> c1:int4){}
%alter rule r957 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func957(c,c10).
%alter function func958(c:int4 -> c1:int4){}
%alter rule r958 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func958(c,c10).
%alter function func959(c:int4 -> c1:int4){}
%alter rule r959 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func959(c,c10).
%alter function func960(c:int4 -> c1:int4){}
%alter rule r960 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func960(c,c10).
%alter function func961(c:int4 -> c1:int4){}
%alter rule r961 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func961(c,c10).
%alter function func962(c:int4 -> c1:int4){}
%alter rule r962 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func962(c,c10).
%alter function func963(c:int4 -> c1:int4){}
%alter rule r963 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func963(c,c10).
%alter function func964(c:int4 -> c1:int4){}
%alter rule r964 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func964(c,c10).
%alter function func965(c:int4 -> c1:int4){}
%alter rule r965 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func965(c,c10).
%alter function func966(c:int4 -> c1:int4){}
%alter rule r966 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func966(c,c10).
%alter function func967(c:int4 -> c1:int4){}
%alter rule r967 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func967(c,c10).
%alter function func968(c:int4 -> c1:int4){}
%alter rule r968 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func968(c,c10).
%alter function func969(c:int4 -> c1:int4){}
%alter rule r969 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func969(c,c10).
%alter function func970(c:int4 -> c1:int4){}
%alter rule r970 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func970(c,c10).
%alter function func971(c:int4 -> c1:int4){}
%alter rule r971 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func971(c,c10).
%alter function func972(c:int4 -> c1:int4){}
%alter rule r972 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func972(c,c10).
%alter function func973(c:int4 -> c1:int4){}
%alter rule r973 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func973(c,c10).
%alter function func974(c:int4 -> c1:int4){}
%alter rule r974 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func974(c,c10).
%alter function func975(c:int4 -> c1:int4){}
%alter rule r975 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func975(c,c10).
%alter function func976(c:int4 -> c1:int4){}
%alter rule r976 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func976(c,c10).
%alter function func977(c:int4 -> c1:int4){}
%alter rule r977 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func977(c,c10).
%alter function func978(c:int4 -> c1:int4){}
%alter rule r978 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func978(c,c10).
%alter function func979(c:int4 -> c1:int4){}
%alter rule r979 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func979(c,c10).
%alter function func980(c:int4 -> c1:int4){}
%alter rule r980 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func980(c,c10).
%alter function func981(c:int4 -> c1:int4){}
%alter rule r981 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func981(c,c10).
%alter function func982(c:int4 -> c1:int4){}
%alter rule r982 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func982(c,c10).
%alter function func983(c:int4 -> c1:int4){}
%alter rule r983 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func983(c,c10).
%alter function func984(c:int4 -> c1:int4){}
%alter rule r984 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func984(c,c10).
%alter function func985(c:int4 -> c1:int4){}
%alter rule r985 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func985(c,c10).
%alter function func986(c:int4 -> c1:int4){}
%alter rule r986 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func986(c,c10).
%alter function func987(c:int4 -> c1:int4){}
%alter rule r987 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func987(c,c10).
%alter function func988(c:int4 -> c1:int4){}
%alter rule r988 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func988(c,c10).
%alter function func989(c:int4 -> c1:int4){}
%alter rule r989 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func989(c,c10).
%alter function func990(c:int4 -> c1:int4){}
%alter rule r990 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func990(c,c10).
%alter function func991(c:int4 -> c1:int4){}
%alter rule r991 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func991(c,c10).
%alter function func992(c:int4 -> c1:int4){}
%alter rule r992 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func992(c,c10).
%alter function func993(c:int4 -> c1:int4){}
%alter rule r993 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func993(c,c10).
%alter function func994(c:int4 -> c1:int4){}
%alter rule r994 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func994(c,c10).
%alter function func995(c:int4 -> c1:int4){}
%alter rule r995 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func995(c,c10).
%alter function func996(c:int4 -> c1:int4){}
%alter rule r996 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func996(c,c10).
%alter function func997(c:int4 -> c1:int4){}
%alter rule r997 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func997(c,c10).
%alter function func998(c:int4 -> c1:int4){}
%alter rule r998 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func998(c,c10).
%alter function func999(c:int4 -> c1:int4){}
%alter rule r999 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func999(c,c10).
%alter function func1000(c:int4 -> c1:int4){}
%alter rule x000 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1000(c,c10).
%alter function func1001(c:int4 -> c1:int4){}
%alter rule x001 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1001(c,c10).
%alter function func1002(c:int4 -> c1:int4){}
%alter rule x002 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1002(c,c10).
%alter function func1003(c:int4 -> c1:int4){}
%alter rule x003 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1003(c,c10).
%alter function func1004(c:int4 -> c1:int4){}
%alter rule x004 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1004(c,c10).
%alter function func1005(c:int4 -> c1:int4){}
%alter rule x005 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1005(c,c10).
%alter function func1006(c:int4 -> c1:int4){}
%alter rule x006 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1006(c,c10).
%alter function func1007(c:int4 -> c1:int4){}
%alter rule x007 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1007(c,c10).
%alter function func1008(c:int4 -> c1:int4){}
%alter rule x008 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1008(c,c10).
%alter function func1009(c:int4 -> c1:int4){}
%alter rule x009 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1009(c,c10).
%alter function func1010(c:int4 -> c1:int4){}
%alter rule x010 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1010(c,c10).
%alter function func1011(c:int4 -> c1:int4){}
%alter rule x011 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1011(c,c10).
%alter function func1012(c:int4 -> c1:int4){}
%alter rule x012 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1012(c,c10).
%alter function func1013(c:int4 -> c1:int4){}
%alter rule x013 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1013(c,c10).
%alter function func1014(c:int4 -> c1:int4){}
%alter rule x014 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1014(c,c10).
%alter function func1015(c:int4 -> c1:int4){}
%alter rule x015 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1015(c,c10).
%alter function func1016(c:int4 -> c1:int4){}
%alter rule x016 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1016(c,c10).
%alter function func1017(c:int4 -> c1:int4){}
%alter rule x017 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1017(c,c10).
%alter function func1018(c:int4 -> c1:int4){}
%alter rule x018 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1018(c,c10).
%alter function func1019(c:int4 -> c1:int4){}
%alter rule x019 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1019(c,c10).
%alter function func1020(c:int4 -> c1:int4){}
%alter rule x020 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1020(c,c10).
%alter function func1021(c:int4 -> c1:int4){}
%alter rule x021 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1021(c,c10).
%alter function func1022(c:int4 -> c1:int4){}
%alter rule x022 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1022(c,c10).
%alter function func1023(c:int4 -> c1:int4){}
%alter rule x023 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1023(c,c10).
%alter function func1024(c:int4 -> c1:int4){}
%alter rule x024 out(a,b,c10,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4):-inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4),func1024(c,c10).
%block 0
