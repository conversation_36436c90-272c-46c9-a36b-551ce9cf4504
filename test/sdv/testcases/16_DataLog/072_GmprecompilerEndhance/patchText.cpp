/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: patchText.cpp
 * Description: Datalog Gmprecompiler generate text file
 * Author: youwanyong ywx1157510
 * Create: 2024-3-12
 */
#include "UniversalTools.h"
#include "t_datacom_lite.h"

using namespace std;

class patchText : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testEnvClean();
    }
};

void patchText::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void patchText::TearDown()
{
    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  : 001.升级patch.d异常预期不会产生patch_redoItems.txt文本，
**************************************************************************** */
TEST_F(patchText, DataLog_072_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_001";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    // a.升级patch.d异常预期不会产生patch_redoItems.txt文本，
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "upgraded rule \"r1\" does not exist in old datalog near line 2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验文本预期不会产生文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "out");
    AW_MACRO_ASSERT_NE_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.不支持指定生成文本名
**************************************************************************** */
TEST_F(patchText, DataLog_072_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_002";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    system("rm -rf ./datalog_file/DataLog_072_002_patch_redoItems.txt");
    // a.不支持指定生成文本名
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d de.c de.d %s.txt", g_toolPath,
        completePath, completePath, soName1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(
        command, "The number of parameters for the option(\"-u\") is in the range((2, 4)). please check your input.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验文本预期不存在该文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "out");
    AW_MACRO_ASSERT_NE_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.支持生成文本名长度256
**************************************************************************** */
TEST_F(patchText, DataLog_072_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_003";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    char newlibName[260] = {0};
    char filePath[256] = "cds";
    // 当前文件长度=当前路径长度(offset)+soName长度(soNameLength)+.so后缀及结束符(suffixAndEnd)
    int32_t soNameLength = 115;                                           // 构造soname名称为115字节
    int offset = GetPathLengthNums();                                     // 获取当前路径长度
    int32_t suffixAndEnd = 9;                                             // _patch.d后缀+结束符长度
    int32_t needPathLength = 256 - offset - soNameLength - suffixAndEnd;  // 剩余需要的构造路径长度
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    // 当前路径下创建指定长度文件夹
    memset_s(newlibName, 260, '\0', 260);
    memset_s(newlibName, soNameLength, 'b', soNameLength);
    (void)SystemSnprintf("mkdir -p %s", filePath);
    (void)snprintf(newlibName + soNameLength, 260, "%s", "_patch.d");
    (void)SystemSnprintf("cp ./datalog_file/%s_patch.d %s/%s", soName1, filePath, newlibName);

    // a.支持生成文本名长度256
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "gmprecompiler -u %s_rule.d %s%s", completePath, filePath, newlibName);
    system(command);
    AW_FUN_Log(LOG_INFO, "1.cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE,
        "md5sum "
        "%sbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb"
        "bbbbbbb_patch_redoItems.txt",
        filePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "2.cmd: %s", command);
    ret = executeCommand(command, "b4784cde88e5f710cfa115ec401c555d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)SystemSnprintf("rm -rf ./b/");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.支持升级阻塞模式下，表升级，投影到null(0）校验patch_redoItems.txt文本内容
**************************************************************************** */
TEST_F(patchText, DataLog_072_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_004";
    char patchSoName[FILE_PATH] = "DataLog_072_004_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，表升级，投影到null(0）校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7e7acd18ac42eacd512073d73c4ee3a3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(
            cmd, "PATCH_STATE: SUCCESS", "RULE_NAME: r0", "REDO_RELATED_TABLES", "TABLE_NAME: ns1.out");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.支持升级非阻塞模式下，表升级，投影到null(0），校验patch_redoItems.txt文本内容
**************************************************************************** */
TEST_F(patchText, DataLog_072_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_005";
    char patchSoName[FILE_PATH] = "DataLog_072_005_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 持升级非阻塞模式下，表升级，投影到null(0），校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "b4784cde88e5f710cfa115ec401c555d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(
            cmd, "UPDATE_RULES", "RULE_NAME: r0", "REDO_RELATED_TABLES", "TABLE_NAME: inp", "TABLE_NAME: out");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.支持升级阻塞模式下，连续表升级，升级2join规则，校验patch_redoItems.txt文本内容
 阻塞模式不支持跨topo升级，升级2改为非阻塞模式
**************************************************************************** */
TEST_F(patchText, DataLog_072_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_006";
    char patchSoName[FILE_PATH] = "DataLog_072_006_patchV2";
    char patchV2RuleName[FILE_PATH] = "DataLog_072_006_ruleV2";
    char patchV2SoName[FILE_PATH] = "DataLog_072_006_patchV3";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，连续表升级，升级2join规则，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7f919e1200e7334a4d30d7c109f4d8be");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "REDO_RELATED_TABLES", "TABLE_NAME: out");
        sleep(1);
    }

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, patchV2RuleName);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7aa0fe76348bc64d139e9ac4e6e3bcfe");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载升级so2
    AW_FUN_Log(LOG_INFO, "加载升级so2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchV2SoName));
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.支持升级非阻塞模式下，连续表升级，升级2join升级1规则，校验patch_redoItems.txt文本内容
**************************************************************************** */
TEST_F(patchText, DataLog_072_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_007";
    char patchSoName[FILE_PATH] = "DataLog_072_007_patchV2";
    char patchV2RuleName[FILE_PATH] = "DataLog_072_007_ruleV2";
    char patchV2SoName[FILE_PATH] = "DataLog_072_007_patchV3";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级非阻塞模式下，连续表升级，升级2join升级1规则，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "b4784cde88e5f710cfa115ec401c555d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, patchV2RuleName);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7aa0fe76348bc64d139e9ac4e6e3bcfe");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    // 加载升级so2
    AW_FUN_Log(LOG_INFO, "加载升级so2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchV2SoName));
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.支持升级阻塞模式下，规则升级，规格内修改规则内容，升级join升级规则，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_008";
    char patchSoName[FILE_PATH] = "DataLog_072_008_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，规则升级，规格内修改规则内容，升级join升级规则，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7f919e1200e7334a4d30d7c109f4d8be");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.支持升级非阻塞模式下，规则升级，规格内修改规则内容，升级join升级规则，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_009";
    char patchSoName[FILE_PATH] = "DataLog_072_009_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，规则升级，规格内修改规则内容，升级join升级规则，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "b4784cde88e5f710cfa115ec401c555d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_010";
    char patchSoName[FILE_PATH] = "DataLog_072_010_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7f919e1200e7334a4d30d7c109f4d8be");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.支持升级非阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_011";
    char patchSoName[FILE_PATH] = "DataLog_072_011_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "b4784cde88e5f710cfa115ec401c555d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.支持升级阻塞模式下，udf升级，，新增udf  校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_012";
    char patchSoName[FILE_PATH] = "DataLog_072_012_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7f919e1200e7334a4d30d7c109f4d8be");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.支持升级非阻塞模式下，udf升级，新增udf，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_013";
    char patchSoName[FILE_PATH] = "DataLog_072_013_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "b4784cde88e5f710cfa115ec401c555d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.含namespace,支持升级阻塞模式下，表升级，投影到null(0）校验patch_redoItems.txt文本内容
**************************************************************************** */
TEST_F(patchText, DataLog_072_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_014";
    char patchSoName[FILE_PATH] = "DataLog_072_014_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，表升级，投影到null(0）校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7e7acd18ac42eacd512073d73c4ee3a3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.含namespace,支持升级非阻塞模式下，表升级，投影到null(0），校验patch_redoItems.txt文本内容
**************************************************************************** */
TEST_F(patchText, DataLog_072_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_015";
    char patchSoName[FILE_PATH] = "DataLog_072_015_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 持升级非阻塞模式下，表升级，投影到null(0），校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "6098b7e387598027758fb5a374a3692a");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.含namespace,支持升级阻塞模式下，连续表升级，升级2join规则，校验patch_redoItems.txt文本内容
**************************************************************************** */
TEST_F(patchText, DataLog_072_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_016";
    char patchSoName[FILE_PATH] = "DataLog_072_016_patchV2";
    char patchV2RuleName[FILE_PATH] = "DataLog_072_016_ruleV2";
    char patchV2SoName[FILE_PATH] = "DataLog_072_016_patchV3";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，连续表升级，升级2join规则，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7e7acd18ac42eacd512073d73c4ee3a3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "UPDATE_RULES", "RULE_NAME: r0", "REDO_RELATED_TABLES", "TABLE_NAME: ns1.out");
        sleep(1);
    }

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, patchV2RuleName);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "9535c4e02b0ef7f143e0374000e3fef3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载升级so2
    AW_FUN_Log(LOG_INFO, "加载升级so2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchV2SoName));
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.含namespace,支持升级非阻塞模式下，连续表升级，升级2join升级1规则，校验patch_redoItems.txt文本内容
**************************************************************************** */
TEST_F(patchText, DataLog_072_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_017";
    char patchSoName[FILE_PATH] = "DataLog_072_017_patchV2";
    char patchV2RuleName[FILE_PATH] = "DataLog_072_017_ruleV2";
    char patchV2SoName[FILE_PATH] = "DataLog_072_017_patchV3";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级非阻塞模式下，连续表升级，升级2join升级1规则，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "6098b7e387598027758fb5a374a3692a");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, patchV2RuleName);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "9535c4e02b0ef7f143e0374000e3fef3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    // 加载升级so2
    AW_FUN_Log(LOG_INFO, "加载升级so2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchV2SoName));
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.含namespace,支持升级阻塞模式下，规则升级，规格内修改规则内容，升级join升级规则，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_018";
    char patchSoName[FILE_PATH] = "DataLog_072_018_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，规则升级，规格内修改规则内容，升级join升级规则，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7e7acd18ac42eacd512073d73c4ee3a3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.含namespace,支持升级非阻塞模式下，规则升级，规格内修改规则内容，升级join升级规则，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_019";
    char patchSoName[FILE_PATH] = "DataLog_072_019_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，规则升级，规格内修改规则内容，升级join升级规则，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "6098b7e387598027758fb5a374a3692a");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.含namespace,支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_020";
    char patchSoName[FILE_PATH] = "DataLog_072_020_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7e7acd18ac42eacd512073d73c4ee3a3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.含namespace,支持升级非阻塞模式下，udf升级，规则内修改udf实现，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_021";
    char patchSoName[FILE_PATH] = "DataLog_072_021_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "6098b7e387598027758fb5a374a3692a");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.含namespace,支持升级阻塞模式下，udf升级，，新增udf  校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_022";
    char patchSoName[FILE_PATH] = "DataLog_072_022_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7e7acd18ac42eacd512073d73c4ee3a3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.含namespace,支持升级非阻塞模式下，udf升级，新增udf，校验patch_redoItems.txt文本内容
TEST_F(patchText, DataLog_072_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_023";
    char patchSoName[FILE_PATH] = "DataLog_072_023_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 支持升级阻塞模式下，udf升级，规格内修改udf实现，校验patch_redoItems.txt文本内容
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "6098b7e387598027758fb5a374a3692a");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so1
    AW_FUN_Log(LOG_INFO, "加载升级so1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.循环10000次编译生成patch_redoItems.txt
TEST_F(patchText, DataLog_072_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_004";
    char patchSoName[FILE_PATH] = "DataLog_072_004_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 用例超时减少循环次数(10000->5000)
    int32_t cycle = 5000;
    // 支持升级阻塞模式下，表升级，投影到null(0）校验patch_redoItems.txt文本内容
    for (int32_t i = 0; i < cycle; i++) {
        (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
        (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d",
            g_toolPath, completePath, completePath, completePath, completePath);
        system(command);
        AW_FUN_Log(LOG_INFO, "cmd: %s", command);
        ret = executeCommand(command, "Serialize done");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验文本
        memset(completePath, 0, MAX_CMD_SIZE);
        memset(command, 0, MAX_CMD_SIZE);
        (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
        system(command);
        SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
        AW_FUN_Log(LOG_INFO, "cmd: %s", command);
        ret = executeCommand(command, "7e7acd18ac42eacd512073d73c4ee3a3");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(completePath, 0, MAX_CMD_SIZE);
        memset(command, 0, MAX_CMD_SIZE);
    }

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.阻塞（NULL(0)）支持编译新增含998个输入表patch.d 产生patch_redoItems.txt文本，
TEST_F(patchText, DataLog_072_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_025";
    char patchSoName[FILE_PATH] = "DataLog_072_025_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 阻塞（NULL(0)）支持编译新增含998个输入表patch.d 产生patch_redoItems.txt文本，
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "dc40bd1ecfb13b1b89f5f012c429f372");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.非阻塞（含join规则）支持编译新增含998个输入表patch.d 产生patch_redoItems.txt文本，
TEST_F(patchText, DataLog_072_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_026";
    char patchSoName[FILE_PATH] = "DataLog_072_026_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 非阻塞（含join规则）支持编译新增含998个输入表patch.d 产生patch_redoItems.txt文本，
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "dc40bd1ecfb13b1b89f5f012c429f372");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.阻塞模式 支持编译新增含1024个function (一个新增) patch.d 产生patch_redoItems.txt文本
TEST_F(patchText, DataLog_072_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_027";
    char patchSoName[FILE_PATH] = "DataLog_072_027_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 阻塞模式 支持编译新增含1024个function  patch.d 产生patch_redoItems.txt文本
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "7f919e1200e7334a4d30d7c109f4d8be");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.非阻塞模式 支持编译新增含1024个function (一个新增，1023个修改)  patch.d 产生patch_redoItems.txt文本
TEST_F(patchText, DataLog_072_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_028";
    char patchSoName[FILE_PATH] = "DataLog_072_028_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 阻塞模式 支持编译新增含1024个function  patch.d 产生patch_redoItems.txt文本
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "94c16b018052aa670ef1733ff3019890");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.支持编译3M patch.d 生成patch_redoItems.txt文本，
TEST_F(patchText, DataLog_072_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_029";
    char patchSoName[FILE_PATH] = "DataLog_072_029_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 阻塞模式 支持编译新增含1024个function  patch.d 产生patch_redoItems.txt文本
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "0cc00c6cc50c4471a213f9fdeacb5565");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.1000张表新增function ，join使用输入表，（规则使用相同function）
TEST_F(patchText, DataLog_072_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_030";
    char patchSoName[FILE_PATH] = "DataLog_072_030_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    AW_ADD_TRUNCATION_WHITE_LIST(1, "GMWARN-0, Operate successfully. Tables have been redo");

    (void)TestUninstallDatalog(soName1);
    // 阻塞模式 支持编译新增含1024个function  patch.d 产生patch_redoItems.txt文本
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d", g_toolPath,
        completePath, completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "f59f26fe9e832570402ad3fce98376a9");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.ylog 非阻塞，修改规则，生成patch_redoItems.txt文本
TEST_F(patchText, DataLog_072_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_031";
    char patchSoName[FILE_PATH] = "DataLog_072_031_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    SystemSnprintf("gmprecompiler -f %s%s.d %s%s.c -supressSpeErr", datalogFile, soName1, datalogFile, soName1);
    SystemSnprintf("gcc %s%s.c -fPIC --shared -Wl,-Bsymbolic -I pub/include/ -o %s%s.so", datalogFile, soName1,
        datalogFile, soName1);
    // ylog 非阻塞，修改规则，生成patch_redoItems.txt文本
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE,
        "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d -supressSpeErr", g_toolPath, completePath,
        completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "a4870af39a11c158e82598df3a2ec123");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.ylog 阻塞，修改规则，生成patch_redoItems.txt文本
TEST_F(patchText, DataLog_072_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_032";
    char patchSoName[FILE_PATH] = "DataLog_072_032_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    SystemSnprintf("gmprecompiler -f %s%s.d %s%s.c -supressSpeErr", datalogFile, soName1, datalogFile, soName1);
    SystemSnprintf("gcc %s%s.c -fPIC --shared -Wl,-Bsymbolic -I pub/include/ -o %s%s.so", datalogFile, soName1,
        datalogFile, soName1);
    // ylog 非阻塞，修改规则，生成patch_redoItems.txt文本
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE,
        "%s/gmprecompiler -u %s_rule.d %s_patch.d %s_patch.c %s_ruleV2.d -supressSpeErr", g_toolPath, completePath,
        completePath, completePath, completePath);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "97057ef39f0a0a54b80d9be27343d298");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.patch.d和文本生成路径一致，（rule.d和patch.d不在相同路径下)
TEST_F(patchText, DataLog_072_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_033";
    char patchSoName[FILE_PATH] = "DataLog_072_033_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    char completePath1[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // patch.d和文本生成路径一致，（rule.d和patch.d不在相同路径下)
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(completePath1, MAX_CMD_SIZE, "%spatch/%s", datalogFile, soName1);
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d", g_toolPath, completePath, completePath1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %spatch/%s_patch_redoItems.txt", datalogFile, soName1);
    SystemSnprintf("cat %spatch/%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "b4784cde88e5f710cfa115ec401c555d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.原始so自定义规则名，规则升级，生成文本
TEST_F(patchText, DataLog_072_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_034";
    char patchSoName[FILE_PATH] = "DataLog_072_034_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    char completePath1[MAX_CMD_SIZE] = {0};

    (void)TestUninstallDatalog(soName1);
    // 原始so自定义规则名，规则升级，生成文本
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(completePath1, MAX_CMD_SIZE, "%s/%s", datalogFile, soName1);
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d", g_toolPath, completePath, completePath1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "74d4c90121a59199c97aa659eba54d74");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.环境中含同名文件，文件中含内容，升级，生成文本
TEST_F(patchText, DataLog_072_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_034";
    char patchSoName[FILE_PATH] = "DataLog_072_034_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    char completePath1[MAX_CMD_SIZE] = {0};

    SystemSnprintf("touch %s%s_patch_redoItems.txt", datalogFile, soName1);
    SystemSnprintf("cat DataLog_072_035.txt %s%s_patch_redoItems.txt", datalogFile, soName1);

    (void)TestUninstallDatalog(soName1);
    // 环境中含同名文件，文件中含内容，升级，生成文本
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(completePath1, MAX_CMD_SIZE, "%s/%s", datalogFile, soName1);
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d", g_toolPath, completePath, completePath1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "74d4c90121a59199c97aa659eba54d74");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.部分输出表含%redo关键字，预期该部分输出表不会显示
TEST_F(patchText, DataLog_072_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_036";
    char patchSoName[FILE_PATH] = "DataLog_072_036_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    char completePath1[MAX_CMD_SIZE] = {0};

    SystemSnprintf("touch %s%s_patch_redoItems.txt", datalogFile, soName1);

    (void)TestUninstallDatalog(soName1);
    // 部分输出表含%redo关键字，预期该部分输出表不会显示
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(completePath1, MAX_CMD_SIZE, "%s/%s", datalogFile, soName1);
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d", g_toolPath, completePath, completePath1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "aefa7125ba0281671e48c072c9ba1c76");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.阻塞模式 原始.d中输入表投影到null(0），支持新增表join该规则
TEST_F(patchText, DataLog_072_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_072_037";
    char patchSoName[FILE_PATH] = "DataLog_072_037_patchV2";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};
    char completePath1[MAX_CMD_SIZE] = {0};

    SystemSnprintf("touch %s%s_patch_redoItems.txt", datalogFile, soName1);

    (void)TestUninstallDatalog(soName1);
    // 部分输出表含%redo关键字，预期该部分输出表不会显示
    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s", datalogFile, soName1);
    (void)snprintf(completePath1, MAX_CMD_SIZE, "%s/%s", datalogFile, soName1);
    (void)snprintf(
        command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s_rule.d %s_patch.d", g_toolPath, completePath, completePath1);
    system(command);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验文本
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    (void)snprintf(command, MAX_CMD_SIZE, "md5sum %s%s_patch_redoItems.txt", datalogFile, soName1);
    system(command);
    SystemSnprintf("cat %s%s_patch_redoItems.txt", datalogFile, soName1);
    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "dc40bd1ecfb13b1b89f5f012c429f372");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);

    // 加载so
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_FUN_Log(LOG_INFO, "加载升级so.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=%s", soName1);
    ret = -1;
    int32_t cycle = 100;  // 降低此处失败耗时
    while ((ret == -1) && (cycle > 0)) {
        cycle--;
        system(cmd);
        ret = Debug_executeCommand(cmd, "PATCH_STATE: SUCCESS");
        sleep(1);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
