// 固定资源表字段个数上限只能支持到33个,索引字段长度超过532字节， 外部表索引限制，tbm只能存在主键,外部表和.d文件声名均占用表数量规格
// 状态表只能支持到
// 普通表
// normal表
%table A000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), index0_type(chained)}

// transient选项不能与update、timeout选项同时出现。
// 对分组记录做整体输出
// transient字段有且只有1个，且必须是int4类型
// 既有transient field为0也有transient field为1的数据，会优先插入transient field为0的数据
// transient  (field)
%table B000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{
index(0(a)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(field(c))
}

// transient选项不能与update、timeout选项同时出现。
// 对分组记录做整体输出
// transient字段有且只有1个，且必须是int4类型
// 既有transient field为0也有transient field为1的数据，会优先插入transient field为0的数据
// transient  (tuple)
%table C000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{
index(0(a)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)
}

// 投影规则
B000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- C000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).
A000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- B000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// 过期表(不带UDF, 带UDF)
// 仅%table支持定义timeout选项。
// 带timeout选项的表必须是输入表，普通表、可更新表作为输入表的情况下支持表记录过期。
// •state_function中的选项只能是access_current，有且只能有一个access_current 。,默认2s过期，过期数据同样会产生投影
// 过期字段会占用一个索引规格
// 不带udf没有state_function
%table D000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{
index(0(a)),index(1(b)),index(2(c)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), timeout(field(d))
}

// 带udf
%table E000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{
index(0(a)),index(1(b)),index(2(c)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), timeout(field(d), state_function)
}

// 投影规则
A000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- D000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).
A000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- E000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// count大于0,且主键在原表中已经存在的数据 为更新操作
// 可更新表(完全, 部分, 比较UDF)，完全可更新表可以作为输入和输出，其余只能作为输入表
// 1.完全可更新表（更新时更新整条记录）
%table F000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{
index(0(a)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update
}

// 2.部分可更新表（更新时可以仅更新记录中的部分字段）
%table G000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{
index(0(a)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update_partial
}

// 3.按照指定的优先级更新，由用户给出排序函数
// 保留数据大的
%table H000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{
index(0(a)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update_by_rank, update
}

// 保留数据小的
%table I000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{
index(0(a)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update_by_rank, update_partial
}

F000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- I000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).
F000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- G000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).
F000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- H000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// 固定资源表,输出字段只能为1个，？？有org吗 
%resource J000(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4 -> e4:int4)
{
index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), sequential(max_size(1000))
}

// pusub资源表，输出字段可以有多个
%resource K000(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4 -> e4:int4, f4:byte128, g4:byte256,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:int4)
{
index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), pending_id(1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1)
}

// 资源表输入表
%table L000(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:int4)
{
index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))
}

// 资源表输出表
%table M000(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8, e4:int4, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:int4)
{
index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))
}

J000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,-) :- L000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
M000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,1,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- J000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e4).
K000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-) :- L000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-).
M000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,1,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- K000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// 外部表 外部表需要创建订阅merge insert/merge update
%table N000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), external}
N000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- D000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// tbm表输入表
%table Q000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:str, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}

// tbm表
%table O000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:str, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a,b,c,d,e,f,g,a1)), tbm}
%function init()
%function uninit()
O000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- Q000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// 消息通知表
%table P000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:str, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a,b,c,d,e,f,g,a1)), msg_notify, batch_msg_size(1024)}
P000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- Q000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// 状态表输入表
%table S000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:str, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}

// 状态表输出表
%table T000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:str, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}
// 状态表
%table R000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:str, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)), state}
// 状态表function
%function tran(e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str -> e41:byte1, f41:byte128, g41:byte256 ,a51:int1, b51:int2, c51:int4, d51:int8, e51:byte1, f51:byte128, g51:byte256 ,a61:int1, b61:int2, c61:int4, d61:int8, e61:byte1, f61:byte128, g61:byte256 ,a71:int1, b71:int2, c71:int4, d71:int8, e71:byte1, f71:byte128, g71:byte256 ,a81:int1, b81:int2, c81:int4, d81:int8, e81:byte1, f81:byte128, a91:str) { state_transfer}
R000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e421,f421,g421,a521,b521,c521,d521,e521,f521,g521,a621,b621,c621,d621,e621,f621,g621,a721,b721,c721,d721,e721,f721,g721,a821,b821,c821,d821,e821,f821,a921) :- S000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e42,f42,g42,a52,b52,c52,d52,e52,f52,g52,a62,b62,c62,d62,e62,f62,g62,a72,b72,c72,d72,e72,f72,g72,a82,b82,c82,d82,e82,f82,a92), tran(e42,f42,g42,a52,b52,c52,d52,e52,f52,g52,a62,b62,c62,d62,e62,f62,g62,a72,b72,c72,d72,e72,f72,g72,a82,b82,c82,d82,e82,f82,a92, e421,f421,g421,a521,b521,c521,d521,e521,f521,g521,a621,b621,c621,d621,e621,f621,g621,a721,b721,c721,d721,e721,f721,g721,a821,b821,c821,d821,e821,f821,a921).
T000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e421,f421,g421,a521,b521,c521,d521,e521,f521,g521,a621,b621,c621,d621,e621,f621,g621,a721,b721,c721,d721,e721,f721,g721,a821,b821,c821,d821,e821,f821,a921):- R000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e421,f421,g421,a521,b521,c521,d521,e521,f521,g521,a621,b621,c621,d621,e621,f621,g621,a721,b721,c721,d721,e721,f721,g721,a821,b821,c821,d821,e821,f821,a921).

%table A019(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}

%table A020(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}

%table A021(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}

// 规则not join
A021(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A020(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), NOT A019(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// 规则笛卡尔积
A021(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A020(-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), A019(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-).

// 级联删除
%table A022(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update, union_delete(A023,A024,A025,A026,A027,A028,A029,A030)}

%table A023(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update}

%table A024(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update}

%table A025(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update}

%table A026(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update}

%table A027(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update}

%table A028(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update}

%table A029(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update}

%table A030(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), update}

// transient tuple表含variant作为中间表
%table A031(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

%table A032(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

%table A033(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

%table A034(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

%table A035(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

%table A036(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

%table A037(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

%table A038(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

%table A039(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), transient(tuple)}

// 指定9张pubsub表作为输出表
%table A040(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

%table A041(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

%table A042(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

%table A043(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

%table A044(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

%table A045(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

%table A046(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

%table A047(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

%table A048(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte128, g1:byte256, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte128, g2:byte256, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte128, g3:byte256 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
  index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)), notify}

// 指定每个输入表的funcion,读输出表的delta
%function funcA022(a: int4 -> b: int4){ access_delta(A040)}
%function funcA023(a: int4 -> b: int4){ access_delta(A041) }
%function funcA024(a: int4 -> b: int4){ access_delta(A042) }
%function funcA025(a: int4 -> b: int4){ access_delta(A043) }
%function funcA026(a: int4 -> b: int4){ access_delta(A044) }
%function funcA027(a: int4 -> b: int4){ access_delta(A045) }
%function funcA028(a: int4 -> b: int4){ access_delta(A046) }
%function funcA029(a: int4 -> b: int4){ access_delta(A047) }
%function funcA030(a: int4 -> b: int4){ access_delta(A048) }
%precedence A022, A023, A024, A025, A026, A027, A028, A029, A030
%precedence A031, A032, A033, A034, A035, A036, A037, A038, A039
%precedence A040, A041, A042, A043, A044, A045, A046, A047, A048
// 级联删除规则
A031(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A022(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA022(c, c1).
A040(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A031(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

A032(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A023(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA023(c, c1).
A041(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A032(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

A033(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A024(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA024(c, c1).
A042(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A033(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

A034(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A025(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA025(c, c1).
A043(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A034(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

A035(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A026(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA026(c, c1).
A044(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A035(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

A036(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A027(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA027(c, c1).
A045(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A036(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

A037(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A028(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA028(c, c1).
A046(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A037(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

A038(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A029(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA029(c, c1).
A047(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A038(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

A039(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A030(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA030(c, c1).
A048(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A039(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9).

// 关于聚合函数规则
%table A049(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte1, g3:byte1 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}

%table A050(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte1, g3:byte1 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}

%table A051(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte1, g:byte1, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte1, g1:byte1, a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte1, g2:byte1, a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte1, g3:byte1 ,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str)
{ index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4)), index(1(b)), index(2(c)), index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)), index(23(c3)), index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3))}

%function funcA050(a: int4 -> b: int4){ access_current(A049) }
%aggregate aggA049(d4:int8,e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, a9:str -> d4100:int8, e4100:byte1, f4100:byte128, g4100:byte256 ,a5100:int1, b5100:int2, c5100:int4, d5100:int8, e5100:byte1, f5100:byte128, g5100:byte256 ,a6100:int1, b6100:int2, c6100:int4, d6100:int8, e6100:byte1, f6100:byte128, g6100:byte256 ,a7100:int1, b7100:int2, c7100:int4, d7100:int8, e7100:byte1, f7100:byte128, g7100:byte256 ,a8100:int1, b8100:int2, c8100:int4, d8100:int8, e8100:byte1, f8100:byte128, a9100:str) { many_to_one, access_current(A050), access_delta(A050), index0_type(chained) }
A050(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4100,e4100,f4100,g4100,a5100,b5100,c5100,d5100,e5100,f5100,g5100,a6100,b6100,c6100,d6100,e6100,f6100,g6100,a7100,b7100,c7100,d7100,e7100,f7100,g7100,a8100,b8100,c8100,d8100,e8100,f8100,a9100) :- A049(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) GROUP-BY(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4) aggA049(d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9,d4100,e4100,f4100,g4100,a5100,b5100,c5100,d5100,e5100,f5100,g5100,a6100,b6100,c6100,d6100,e6100,f6100,g6100,a7100,b7100,c7100,d7100,e7100,f7100,g7100,a8100,b8100,c8100,d8100,e8100,f8100,a9100).
A051(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A050(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA050(c,c1).
