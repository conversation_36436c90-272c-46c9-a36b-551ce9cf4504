/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"
#define INSERT 0
#define DELETE 1
#define UPDATE 2

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[1];
    int8_t g[1];
    int8_t a1;
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[1];
    int8_t g1[1];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[1];
    int8_t g2[1];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[1];
    int8_t g3[1];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a9Len;
    char *a9;
} TupleA;
#pragma pack(0)
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[1];
    int8_t g[1];
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[1];
    int8_t g1[1];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[1];
    int8_t g2[1];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[1];
    int8_t g3[1];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a1Len;
    char *a1;
    int32_t a9Len;
    char *a9;
} TupleT;
#pragma pack(0)

// TIMEOUT
int32_t dtl_timeout_callback_E000(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple, GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录
    TupleA *input = (TupleA *)timeoutTuple;
    TupleA *output = GmUdfMemAlloc(ctx, sizeof(TupleA));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a * 2;
    output->b = input->b * 2;
    output->c = input->c * 2;
    output->d = input->d + (int64_t)(10 * 60 * 60 * 1000);
    output->a9Len = input->a9Len;
    output->a9 = input->a9;
    output->a1 = input->a1;
    output->b1 = input->b1;
    output->c1 = input->c1;
    output->d1 = input->d1;
    output->a2 = input->a2;
    output->b2 = input->b2;
    output->c2 = input->c2;
    output->d2 = input->d2;
    output->a3 = input->a3;
    output->b3 = input->b3;
    output->c3 = input->c3;
    output->d3 = input->d3;
    output->a4 = input->a4;
    output->b4 = input->b4;
    output->c4 = input->c4;
    output->d4 = input->d4;
    output->a5 = input->a5;
    output->b5 = input->b5;
    output->c5 = input->c5;
    output->d5 = input->d5;
    output->a6 = input->a6;
    output->b6 = input->b6;
    output->c6 = input->c6;
    output->d6 = input->d6;
    output->a7 = input->a7;
    output->b7 = input->b7;
    output->c7 = input->c7;
    output->d7 = input->d7;
    output->a8 = input->a8;
    output->b8 = input->b8;
    output->c8 = input->c8;
    output->d8 = input->d8;
    output->dtlReservedCount = 2;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(TupleA);

    return GMERR_OK;
}

// UPDATE BY RANK save delta max  delta值， org表值
int32_t dtl_compare_tuple_H000(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    TupleA *inp1 = (TupleA *)tuple1;
    TupleA *inp2 = (TupleA *)tuple2;
    if (inp1->d < inp2->d) {
        return -1;  // delta save
    } else if (inp1->d > inp2->d) {
        return 1;  // org save
    } else {
        return 0;
    }
}

// UPDATE BY RANK save delta min
int32_t dtl_compare_tuple_I000(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    TupleA *inp1 = (TupleA *)tuple1;
    TupleA *inp2 = (TupleA *)tuple2;
    if (inp1->d < inp2->d) {
        return 1;
    } else if (inp1->d > inp2->d) {
        return -1;
    } else {
        return 0;
    }
}

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_init.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_uninit.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_O000(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(
        fp, "[%s] dtl_tbm_tbl_A, op = %d, a = %d, b = %d.\n", __FILE__, op, ((TupleT *)tuple)->a, ((TupleT *)tuple)->b);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_msg_notify_P000(GmMsgNotifyTupleChangeT *tups, uint32_t arrLen)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }
    for (uint32_t i = 0; i < arrLen; ++i) {
        uint32_t op = tups[i].op;
        TupleT *oldTup = (TupleT *)(tups[i].oldTup);
        TupleT *newTup = (TupleT *)(tups[i].newTup);
        if (op == INSERT) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_P000 insert a: %d, b: %d, count: %d, op = %d, \n", __FILE__,
                newTup->a, newTup->b, newTup->dtlReservedCount, tups[i].op);
        } else if (op == DELETE) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_P000 deltete a: %d, b: %d, count: %d, op = %d, \n", __FILE__,
                oldTup->a, oldTup->b, oldTup->dtlReservedCount, tups[i].op);
        } else if (op == UPDATE) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_P000 UPDATE a: %d, b: %d, count: %d, op = %d, \n", __FILE__,
                oldTup->a, oldTup->b, oldTup->dtlReservedCount, tups[i].op);
            (void)fprintf(fp, "[%s] dtl_msg_notify_P000 UPDATE a: %d, b: %d, count: %d, op = %d, \n", __FILE__,
                newTup->a, newTup->b, newTup->dtlReservedCount, tups[i].op);
        }
    }
    (void)fclose(fp);
    return GMERR_OK;
}

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int8_t se4[1];
    int8_t sf4[1];
    int8_t sg4[1];
    int8_t sa5;
    int16_t sb5;
    int32_t sc5;
    int64_t sd5;
    int8_t se5[1];
    int8_t sf5[1];
    int8_t sg5[1];
    int8_t sa6;
    int16_t sb6;
    int32_t sc6;
    int64_t sd6;
    int8_t se6[1];
    int8_t sf6[1];
    int8_t sg6[1];
    int8_t sa7;
    int16_t sb7;
    int32_t sc7;
    int64_t sd7;
    int8_t se7[1];
    int8_t sf7[1];
    int8_t sg7[1];
    int8_t sa8;
    int16_t sb8;
    int32_t sc8;
    int64_t sd8;
    int8_t se8[1];
    int8_t sf8[1];
    int32_t a9Len;
    char *a9;
    int32_t sa9Len;
    char *sa9;
} TupleS;
#pragma pack(0)

int32_t dtl_ext_func_tran(void *tuple, GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    TupleS *result = (TupleS *)tuple;
    (void)fprintf(fp, "[%s] dtl_ext_func_tran\n", __FILE__);
    (void)fprintf(fp, "[%s] dtl_ext_func_tran, result->a9Len = %d ,%p\n", __FILE__, result->a9Len, result->a9);
    //  (void)fprintf(fp, "[%s] dtl_ext_func_tran.\n", __FILE__);
    // (void)fprintf(fp, "[%s] dtl_ext_func_tran, count = %d\n", __FILE__, *count);
    (void)fclose(fp);
    memcpy(result->se4, result->e4, 1);
    memcpy(result->se5, result->e5, 1);
    memcpy(result->se6, result->e6, 1);
    memcpy(result->se7, result->e7, 1);
    memcpy(result->se8, result->e8, 1);
    memcpy(result->sf4, result->f4, 1);
    memcpy(result->sf5, result->f5, 1);
    memcpy(result->sf6, result->f6, 1);
    memcpy(result->sf7, result->f7, 1);
    memcpy(result->sf8, result->f8, 1);
    memcpy(result->sg4, result->g4, 1);
    memcpy(result->sg5, result->g5, 1);
    memcpy(result->sg6, result->g6, 1);
    memcpy(result->sg7, result->g7, 1);
    result->sa9 = NULL;
    result->sa9 = result->a9;
    result->sa9Len = result->a9Len;
    result->sa5 = result->a5;
    result->sb5 = result->b5;
    result->sc5 = result->c5;
    result->sd5 = result->d5;
    result->sa6 = result->a6;
    result->sb6 = result->b6;
    result->sc6 = result->c6;
    result->sd6 = result->d6;
    result->sa7 = result->a7;
    result->sb7 = result->b7;
    result->sc7 = result->c7;
    result->sd7 = result->d7;
    result->sa8 = result->a8;
    result->sb8 = result->b8;
    result->sc8 = result->c8;
    result->sd8 = result->d8;
    int *p = &result->c5;
    int *s = &result->sc5;

    int *count = &result->dtlReservedCount;
    // 对应的状态变化
    // if p == 0 && count == 1 && s == 1, s = 0;
    // if p == 1 && count == 1 && s == 0, s = 1;
    // if p == 2 && count == 0, init, s = 1, count = 1;
    // if p == 3 && count == 1, delete, count = -1;

    if (*count == 0) {
        if (*p > 0) {
            *s = 1;
            *count = 1;
            return GMERR_OK;
        }
    } else if (*count == 1) {
        if (*s == 1 && *p == 0) {
            *s = 0;
            return GMERR_OK;
        }
        if (*s == 0 && *p == 1) {
            *s = 1;
            return GMERR_OK;
        }
        if (*p == 3) {
            *count = -1;
            return GMERR_OK;
        }
    } else {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_NO_DATA;
}

// 级联删除function
#pragma pack(1)
typedef struct {
    int32_t count;
    int32_t a;
    int32_t b;
} DEL;
#pragma pack(0)
int32_t dtl_ext_func_funcA022(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA023(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA024(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA025(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA026(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA027(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA028(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA029(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA030(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA050(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[1];
    int8_t g[1];
    int8_t a1;
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[1];
    int8_t g1[1];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[1];
    int8_t g2[1];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[1];
    int8_t g3[1];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a9Len;
    char *a9;
} TupleAgg;
#pragma pack(0)

#pragma pack(1)
typedef struct FuncAggIn {
    int32_t count;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a9Len;
    char *a9;
} FuncAggIn;

typedef struct FuncAggOut {
    int64_t d4;
    int8_t e4[1];
    int8_t f4[1];
    int8_t g4[1];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[1];
    int8_t g5[1];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[1];
    int8_t g6[1];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[1];
    int8_t g7[1];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[1];
    int32_t a9Len;
    char *a9;
} FuncAggOut;
#pragma pack(0)

int32_t dtl_agg_func_aggA049(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));
        outStruct->a9Len = inpStruct->a9Len;
        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    TupleAgg *c = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&c), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}
