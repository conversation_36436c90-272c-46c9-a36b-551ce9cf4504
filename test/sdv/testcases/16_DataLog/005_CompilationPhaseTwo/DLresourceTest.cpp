/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: tableTest.cpp
 * Description: datalog compilation
 * Author: yuhonglei 30033205
 * Create: 2022-09-13
 */

#include "datalog.h"
#include "t_datacom_lite.h"

using namespace std;

class resourceDatalog : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void resourceDatalog::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    AW_CHECK_LOG_BEGIN();
}
void resourceDatalog::TearDown()
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  : 固定资源池建表
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/066.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 函数型资源池建表
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/067.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 多个resource连接
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/068.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为左表输出字段出现常量
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/069.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: left record: 2 in resource: res1 output should be - near line 5.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为左表输入字段出现常量
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/070.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为左表输出字段出现省略“-”
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/071.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为左表输入字段出现省略“-”
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/072.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: wrong '-' field in left table \"res1\": only resource's output field could be '-' near line 5.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为左表全部字段出现省略“-”
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/073.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: wrong '-' field in left table \"res1\": only resource's output field could be '-' near line 5.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为右表时，输入字段部分为忽略
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/074.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为右表时，输入字段部分为常量
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/075.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为右表时，输入字段全部为忽略
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/076.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为右表时，输入字段全部为常量
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/077.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为右表时，输出字段为常量
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/078.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为右表时，输出字段为忽略
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/079.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为右表时，字段为常量
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/080.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource作为右表时，字段为忽略
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/081.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表时，option选项为空
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/082.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: resource \"res1\" expects 1~38 options, actually we have 0 near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表时，不添加option选项
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/083.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: resource \"res1\" expects 1~38 options, actually we have 0 near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 将定义好的resource表仅用作左表
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/084.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: sequential resource \"res1\" should be an intermediate table (both input and output) near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 将定义好的resource表仅用作右表
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/085.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: sequential resource \"res1\" should be an intermediate table (both input and output) near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 固定型资源池建表输出字段大于1个
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/086.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: there should be only 1 output field for sequential resource \"res1\" near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 固定型资源池建表输出字段类型设置为非int4类型
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/087.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: the output field of sequential resource \"res1\" should be int4 near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource资源表option选项中添加无关关键字，如index
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/088.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表缺乏输入参数
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/089.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d", GMERR_SYNTAX_ERROR);
    int ret = executeCommand(command, "Failed to parse datalog program, syntax error: '->' near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表缺乏输出参数
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/090.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d", GMERR_SYNTAX_ERROR);
    int ret = executeCommand(command, "Failed to parse datalog program, syntax error: ')' near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 资源表option同时定义了固定资源池和函数型资源池字段
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/091.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: conflict options in resource \"res1\": \"sequential\" vs \"pending_id\" near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 函数资源建表选项pending_id定义字段类型和输出字段类型不匹配
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/092.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表后未对其进行使用
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/093.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: resource \"res1\" is defined but not used in the rules near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 固定资源池中max_size定义为-1
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/094.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: max_size of resource \"res1\" should be positive near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 固定资源池中max_size定义为0
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/095.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid range of max_size in resource \"res1\" near line 3,max_size=0,valid range=[1,1000000].", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 固定资源池中max_size定义为2^32
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/096.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d", GMERR_SYNTAX_ERROR);
    int ret = executeCommand(command, "Failed to parse datalog program, unknown character near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 固定资源池中max_size定义为2^32-1
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/097.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 固定资源池中max_size定义为5.1
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/098.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d", GMERR_SYNTAX_ERROR);
    int ret = executeCommand(command, "Failed to parse datalog program, syntax error: '.' near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义两个数值不同的max_size
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/099.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: duplicate option in resource \"res1\": sequential near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义两个数值相同的max_size
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/100.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: duplicate option in resource \"res1\": sequential near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表时不添加%
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/101.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d", GMERR_SYNTAX_ERROR);
    int ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'resource' near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表添加option为local
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/102.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": local near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表添加option为shared
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/103.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": shared near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义resource表添加option为notify
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/104.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": notify near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + max_size
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/105.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + transient(field())
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/106.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": transient near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + transient(tuple)
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/107.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": transient near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + index0
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/108.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + update
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/109.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": update near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + update_partial
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/110.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: invalid option in resource \"res1\": update_partial near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + update_by_rank
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/111.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: invalid option in resource \"res1\": update_by_rank near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + ordered
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/112.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": ordered near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + many_to_one
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/113.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": many_to_one near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + many_to_many
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/114.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: invalid option in resource \"res1\": many_to_many near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + access_delta
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/115.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: invalid option in resource \"res1\": access_delta near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + access_current
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/116.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: invalid option in resource \"res1\": access_current near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + index1
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/117.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 将定义resource表从%resource更改为%RESOURCE
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/118.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d", GMERR_SYNTAX_ERROR);
    int ret = executeCommand(command, "Failed to parse datalog program, syntax error: 'RESOURCE' near line 3.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource表显式定义主键索引
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_291)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/291.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 显式定义二级索引
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_292)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/292.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 显式定义索引个数为32个
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_293)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/293.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 显式定义索引个数为33个
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_294)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/294.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: invalid index sequence number in relation \"res1\": 32 near line 6.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义索引字段个数为32个
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_295)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/295.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义索引字段个数为33个
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_296)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/296.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: invalid index sequence number in relation \"res1\": 255 near line 6.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 显式定义index序号为index(32(a))
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_297)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/297.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: invalid index sequence number in relation \"res1\": 32 near line 6.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义index内的字段不是resource内的字段 --- 有问题
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_298)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/298.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: index field \"x\" is not found in the relation field of relation \"res1\"", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义index，内的字段存在重复 ---- index(1(a, a))
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_299)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/299.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: duplicate index field in relation \"res1\": a near line 5.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义index，索引号不规范 ---- index(a1(a))
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_300)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/300.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret =
        executeCommand(command, "Error: invalid index sequence number in relation \"res1\": a1 near line 5.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : index索引号为小数
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_301)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/301.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to parse datalog file. Exit with code %d", GMERR_SYNTAX_ERROR);
    int ret = executeCommand(command, "Failed to parse datalog program, syntax error: '.' near line 5.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : index索引号为负数
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_302)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/302.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: index sequence number of relation \"res1\" should be non-negative near line 5.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : index索引号为-0
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_303)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/303.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: index sequence number of relation \"res1\" should be non-negative near line 5.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义多个index，存在重复索引序号
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_304)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/304.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: duplicate index sequence number in relation \"res1\": 1 near line 6.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : precedence + resource
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_305)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/305.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : precedence + resource + table时，table表为中间表
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_306)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/306.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : precedence + resource + table时，table表为输入表
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_307)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/307.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: %precedence should not have tables with different in/out type: \"res1\" vs \"table2\" near line 9.",
        errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : precedence + resource + table时，table表为输出表
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_308)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/308.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: %precedence should not have tables with different in/out type: \"res1\" vs \"table1\" near line 9.",
        errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义资源池输入字段个数为32个
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_309)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/309.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义资源池输入字段个数为64个
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_310)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/310.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(
        command, "Error: relation \"ns1.C\" has 256 fields which exceeds the maximum value 64 near line 34.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义固定资源池规格为100万 ---- sequential(nax_size(1000000))
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_311)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/311.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 定义固定资源池规格超过100万
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_312)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/312.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid range of max_size in resource \"res1\" near line 4,max_size=1000001,valid range=[1,1000000].", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource + timeout
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_313)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/313.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command, "Error: invalid option in resource \"res1\": timeout near line 4.", errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resource表作为左表时，输出字段为忽略，右表为agg聚合规则
**************************************************************************** */
TEST_F(resourceDatalog, DataLog_005_314)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char command[MAX_CMD_SIZE] = {0};
    char inputFile[FILE_PATH] = "./datalogFile/resource/314.d";
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, inputFile);
    system(command);

    char errorMsg[MAX_CMD_SIZE] = {0};
    (void)snprintf(errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d", GMERR_SEMANTIC_ERROR);
    int ret = executeCommand(command,
        "Error: resource \"B\" should not appear in the aggregate rule \"funcA\" near line 6.",
        errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
