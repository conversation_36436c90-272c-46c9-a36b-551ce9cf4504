namespace ns000{ 
%table Ans000(a:int4) 
%table Bns000(a:int4)
Bns000(a) :- Ans000(a).
}
namespace ns001{ 
%table Ans001(a:int4) 
%table Bns001(a:int4)
Bns001(a) :- Ans001(a).
}
namespace ns002{ 
%table Ans002(a:int4) 
%table Bns002(a:int4) 
Bns002(a) :- Ans002(a).
}
namespace ns003{ 
%table Ans003(a:int4) 
%table Bns003(a:int4) 
Bns003(a) :- Ans003(a).
}
namespace ns004{ 
%table Ans004(a:int4) 
%table Bns004(a:int4) 
Bns004(a) :- Ans004(a).
}
namespace ns005{ 
%table Ans005(a:int4) 
%table Bns005(a:int4) 
Bns005(a) :- Ans005(a).
}
namespace ns006{ 
%table Ans006(a:int4) 
%table Bns006(a:int4) 
Bns006(a) :- Ans006(a).
}
namespace ns007{ 
%table Ans007(a:int4) 
%table Bns007(a:int4) 
Bns007(a) :- Ans007(a).
}
namespace ns008{ 
%table Ans008(a:int4) 
%table Bns008(a:int4) 
Bns008(a) :- Ans008(a).
}
namespace ns009{ 
%table Ans009(a:int4) 
%table Bns009(a:int4) 
Bns009(a) :- Ans009(a).
}
namespace ns010{ 
%table Ans010(a:int4) 
%table Bns010(a:int4) 
Bns010(a) :- Ans010(a).
}
namespace ns011{ 
%table Ans011(a:int4) 
%table Bns011(a:int4) 
Bns011(a) :- Ans011(a).
}
namespace ns012{ 
%table Ans012(a:int4) 
%table Bns012(a:int4) 
Bns012(a) :- Ans012(a).
}
namespace ns013{ 
%table Ans013(a:int4) 
%table Bns013(a:int4) 
Bns013(a) :- Ans013(a).
}
namespace ns014{ 
%table Ans014(a:int4) 
%table Bns014(a:int4) 
Bns014(a) :- Ans014(a).
}
namespace ns015{ 
%table Ans015(a:int4) 
%table Bns015(a:int4) 
Bns015(a) :- Ans015(a).
}
namespace ns016{ 
%table Ans016(a:int4) 
%table Bns016(a:int4) 
Bns016(a) :- Ans016(a).
}
namespace ns017{ 
%table Ans017(a:int4) 
%table Bns017(a:int4) 
Bns017(a) :- Ans017(a).
}
namespace ns018{ 
%table Ans018(a:int4) 
%table Bns018(a:int4) 
Bns018(a) :- Ans018(a).
}
namespace ns019{ 
%table Ans019(a:int4) 
%table Bns019(a:int4) 
Bns019(a) :- Ans019(a).
}
namespace ns020{ 
%table Ans020(a:int4) 
%table Bns020(a:int4) 
Bns020(a) :- Ans020(a).
}
namespace ns021{ 
%table Ans021(a:int4) 
%table Bns021(a:int4) 
Bns021(a) :- Ans021(a).
}
namespace ns022{ 
%table Ans022(a:int4) 
%table Bns022(a:int4) 
Bns022(a) :- Ans022(a).
}
namespace ns023{ 
%table Ans023(a:int4) 
%table Bns023(a:int4) 
Bns023(a) :- Ans023(a).
}
namespace ns024{ 
%table Ans024(a:int4) 
%table Bns024(a:int4) 
Bns024(a) :- Ans024(a).
}
namespace ns025{ 
%table Ans025(a:int4) 
%table Bns025(a:int4) 
Bns025(a) :- Ans025(a).
}
namespace ns026{ 
%table Ans026(a:int4) 
%table Bns026(a:int4) 
Bns026(a) :- Ans026(a).
}
namespace ns027{ 
%table Ans027(a:int4) 
%table Bns027(a:int4) 
Bns027(a) :- Ans027(a).
}
namespace ns028{ 
%table Ans028(a:int4) 
%table Bns028(a:int4) 
Bns028(a) :- Ans028(a).
}
namespace ns029{ 
%table Ans029(a:int4) 
%table Bns029(a:int4) 
Bns029(a) :- Ans029(a).
}
namespace ns030{ 
%table Ans030(a:int4) 
%table Bns030(a:int4) 
Bns030(a) :- Ans030(a).
}
namespace ns031{ 
%table Ans031(a:int4) 
%table Bns031(a:int4) 
Bns031(a) :- Ans031(a).
}
namespace ns032{ 
%table Ans032(a:int4) 
%table Bns032(a:int4) 
Bns032(a) :- Ans032(a).
}
namespace ns033{ 
%table Ans033(a:int4) 
%table Bns033(a:int4) 
Bns033(a) :- Ans033(a).
}
namespace ns034{ 
%table Ans034(a:int4) 
%table Bns034(a:int4) 
Bns034(a) :- Ans034(a).
}
namespace ns035{ 
%table Ans035(a:int4) 
%table Bns035(a:int4) 
Bns035(a) :- Ans035(a).
}
namespace ns036{ 
%table Ans036(a:int4) 
%table Bns036(a:int4) 
Bns036(a) :- Ans036(a).
}
namespace ns037{ 
%table Ans037(a:int4) 
%table Bns037(a:int4) 
Bns037(a) :- Ans037(a).
}
namespace ns038{ 
%table Ans038(a:int4) 
%table Bns038(a:int4) 
Bns038(a) :- Ans038(a).
}
namespace ns039{ 
%table Ans039(a:int4) 
%table Bns039(a:int4) 
Bns039(a) :- Ans039(a).
}
namespace ns040{ 
%table Ans040(a:int4) 
%table Bns040(a:int4) 
Bns040(a) :- Ans040(a).
}
namespace ns041{ 
%table Ans041(a:int4) 
%table Bns041(a:int4) 
Bns041(a) :- Ans041(a).
}
namespace ns042{ 
%table Ans042(a:int4) 
%table Bns042(a:int4) 
Bns042(a) :- Ans042(a).
}
namespace ns043{ 
%table Ans043(a:int4) 
%table Bns043(a:int4) 
Bns043(a) :- Ans043(a).
}
namespace ns044{ 
%table Ans044(a:int4) 
%table Bns044(a:int4) 
Bns044(a) :- Ans044(a).
}
namespace ns045{ 
%table Ans045(a:int4) 
%table Bns045(a:int4) 
Bns045(a) :- Ans045(a).
}
namespace ns046{ 
%table Ans046(a:int4) 
%table Bns046(a:int4) 
Bns046(a) :- Ans046(a).
}
namespace ns047{ 
%table Ans047(a:int4) 
%table Bns047(a:int4) 
Bns047(a) :- Ans047(a).
}
namespace ns048{ 
%table Ans048(a:int4) 
%table Bns048(a:int4) 
Bns048(a) :- Ans048(a).
}
namespace ns049{ 
%table Ans049(a:int4) 
%table Bns049(a:int4) 
Bns049(a) :- Ans049(a).
}
namespace ns050{ 
%table Ans050(a:int4) 
%table Bns050(a:int4) 
Bns050(a) :- Ans050(a).
}
namespace ns051{ 
%table Ans051(a:int4) 
%table Bns051(a:int4) 
Bns051(a) :- Ans051(a).
}
namespace ns052{ 
%table Ans052(a:int4) 
%table Bns052(a:int4) 
Bns052(a) :- Ans052(a).
}
namespace ns053{ 
%table Ans053(a:int4) 
%table Bns053(a:int4) 
Bns053(a) :- Ans053(a).
}
namespace ns054{ 
%table Ans054(a:int4) 
%table Bns054(a:int4) 
Bns054(a) :- Ans054(a).
}
namespace ns055{ 
%table Ans055(a:int4) 
%table Bns055(a:int4) 
Bns055(a) :- Ans055(a).
}
namespace ns056{ 
%table Ans056(a:int4) 
%table Bns056(a:int4) 
Bns056(a) :- Ans056(a).
}
namespace ns057{ 
%table Ans057(a:int4) 
%table Bns057(a:int4) 
Bns057(a) :- Ans057(a).
}
namespace ns058{ 
%table Ans058(a:int4) 
%table Bns058(a:int4) 
Bns058(a) :- Ans058(a).
}
namespace ns059{ 
%table Ans059(a:int4) 
%table Bns059(a:int4) 
Bns059(a) :- Ans059(a).
}
namespace ns060{ 
%table Ans060(a:int4) 
%table Bns060(a:int4) 
Bns060(a) :- Ans060(a).
}
namespace ns061{ 
%table Ans061(a:int4) 
%table Bns061(a:int4) 
Bns061(a) :- Ans061(a).
}
namespace ns062{ 
%table Ans062(a:int4) 
%table Bns062(a:int4) 
Bns062(a) :- Ans062(a).
}
namespace ns063{ 
%table Ans063(a:int4) 
%table Bns063(a:int4) 
Bns063(a) :- Ans063(a).
}
namespace ns064{ 
%table Ans064(a:int4) 
%table Bns064(a:int4) 
Bns064(a) :- Ans064(a).
}
namespace ns065{ 
%table Ans065(a:int4) 
%table Bns065(a:int4) 
Bns065(a) :- Ans065(a).
}
namespace ns066{ 
%table Ans066(a:int4) 
%table Bns066(a:int4) 
Bns066(a) :- Ans066(a).
}
namespace ns067{ 
%table Ans067(a:int4) 
%table Bns067(a:int4) 
Bns067(a) :- Ans067(a).
}
namespace ns068{ 
%table Ans068(a:int4) 
%table Bns068(a:int4) 
Bns068(a) :- Ans068(a).
}
namespace ns069{ 
%table Ans069(a:int4) 
%table Bns069(a:int4) 
Bns069(a) :- Ans069(a).
}
namespace ns070{ 
%table Ans070(a:int4) 
%table Bns070(a:int4) 
Bns070(a) :- Ans070(a).
}
namespace ns071{ 
%table Ans071(a:int4) 
%table Bns071(a:int4) 
Bns071(a) :- Ans071(a).
}
namespace ns072{ 
%table Ans072(a:int4) 
%table Bns072(a:int4) 
Bns072(a) :- Ans072(a).
}
namespace ns073{ 
%table Ans073(a:int4) 
%table Bns073(a:int4) 
Bns073(a) :- Ans073(a).
}
namespace ns074{ 
%table Ans074(a:int4) 
%table Bns074(a:int4) 
Bns074(a) :- Ans074(a).
}
namespace ns075{ 
%table Ans075(a:int4) 
%table Bns075(a:int4) 
Bns075(a) :- Ans075(a).
}
namespace ns076{ 
%table Ans076(a:int4) 
%table Bns076(a:int4) 
Bns076(a) :- Ans076(a).
}
namespace ns077{ 
%table Ans077(a:int4) 
%table Bns077(a:int4) 
Bns077(a) :- Ans077(a).
}
namespace ns078{ 
%table Ans078(a:int4) 
%table Bns078(a:int4) 
Bns078(a) :- Ans078(a).
}
namespace ns079{ 
%table Ans079(a:int4) 
%table Bns079(a:int4) 
Bns079(a) :- Ans079(a).
}
namespace ns080{ 
%table Ans080(a:int4) 
%table Bns080(a:int4) 
Bns080(a) :- Ans080(a).
}
namespace ns081{ 
%table Ans081(a:int4) 
%table Bns081(a:int4) 
Bns081(a) :- Ans081(a).
}
namespace ns082{ 
%table Ans082(a:int4) 
%table Bns082(a:int4) 
Bns082(a) :- Ans082(a).
}
namespace ns083{ 
%table Ans083(a:int4) 
%table Bns083(a:int4) 
Bns083(a) :- Ans083(a).
}
namespace ns084{ 
%table Ans084(a:int4) 
%table Bns084(a:int4) 
Bns084(a) :- Ans084(a).
}
namespace ns085{ 
%table Ans085(a:int4) 
%table Bns085(a:int4) 
Bns085(a) :- Ans085(a).
}
namespace ns086{ 
%table Ans086(a:int4) 
%table Bns086(a:int4) 
Bns086(a) :- Ans086(a).
}
namespace ns087{ 
%table Ans087(a:int4) 
%table Bns087(a:int4) 
Bns087(a) :- Ans087(a).
}
namespace ns088{ 
%table Ans088(a:int4) 
%table Bns088(a:int4) 
Bns088(a) :- Ans088(a).
}
namespace ns089{ 
%table Ans089(a:int4) 
%table Bns089(a:int4) 
Bns089(a) :- Ans089(a).
}
namespace ns090{ 
%table Ans090(a:int4) 
%table Bns090(a:int4) 
Bns090(a) :- Ans090(a).
}
namespace ns091{ 
%table Ans091(a:int4) 
%table Bns091(a:int4) 
Bns091(a) :- Ans091(a).
}
namespace ns092{ 
%table Ans092(a:int4) 
%table Bns092(a:int4) 
Bns092(a) :- Ans092(a).
}
namespace ns093{ 
%table Ans093(a:int4) 
%table Bns093(a:int4) 
Bns093(a) :- Ans093(a).
}
namespace ns094{ 
%table Ans094(a:int4) 
%table Bns094(a:int4) 
Bns094(a) :- Ans094(a).
}
namespace ns095{ 
%table Ans095(a:int4) 
%table Bns095(a:int4) 
Bns095(a) :- Ans095(a).
}
namespace ns096{ 
%table Ans096(a:int4) 
%table Bns096(a:int4) 
Bns096(a) :- Ans096(a).
}
namespace ns097{ 
%table Ans097(a:int4) 
%table Bns097(a:int4) 
Bns097(a) :- Ans097(a).
}
namespace ns098{ 
%table Ans098(a:int4) 
%table Bns098(a:int4) 
Bns098(a) :- Ans098(a).
}
namespace ns099{ 
%table Ans099(a:int4) 
%table Bns099(a:int4) 
Bns099(a) :- Ans099(a).
}
namespace ns100{ 
%table Ans100(a:int4) 
%table Bns100(a:int4) 
Bns100(a) :- Ans100(a).
}
namespace ns101{ 
%table Ans101(a:int4) 
%table Bns101(a:int4) 
Bns101(a) :- Ans101(a).
}
namespace ns102{ 
%table Ans102(a:int4) 
%table Bns102(a:int4) 
Bns102(a) :- Ans102(a).
}
namespace ns103{ 
%table Ans103(a:int4) 
%table Bns103(a:int4) 
Bns103(a) :- Ans103(a).
}
namespace ns104{ 
%table Ans104(a:int4) 
%table Bns104(a:int4) 
Bns104(a) :- Ans104(a).
}
namespace ns105{ 
%table Ans105(a:int4) 
%table Bns105(a:int4) 
Bns105(a) :- Ans105(a).
}
namespace ns106{ 
%table Ans106(a:int4) 
%table Bns106(a:int4) 
Bns106(a) :- Ans106(a).
}
namespace ns107{ 
%table Ans107(a:int4) 
%table Bns107(a:int4) 
Bns107(a) :- Ans107(a).
}
namespace ns108{ 
%table Ans108(a:int4) 
%table Bns108(a:int4) 
Bns108(a) :- Ans108(a).
}
namespace ns109{ 
%table Ans109(a:int4) 
%table Bns109(a:int4) 
Bns109(a) :- Ans109(a).
}
namespace ns110{ 
%table Ans110(a:int4) 
%table Bns110(a:int4) 
Bns110(a) :- Ans110(a).
}
namespace ns111{ 
%table Ans111(a:int4) 
%table Bns111(a:int4) 
Bns111(a) :- Ans111(a).
}
namespace ns112{ 
%table Ans112(a:int4) 
%table Bns112(a:int4) 
Bns112(a) :- Ans112(a).
}
namespace ns113{ 
%table Ans113(a:int4) 
%table Bns113(a:int4) 
Bns113(a) :- Ans113(a).
}
namespace ns114{ 
%table Ans114(a:int4) 
%table Bns114(a:int4) 
Bns114(a) :- Ans114(a).
}
namespace ns115{ 
%table Ans115(a:int4) 
%table Bns115(a:int4) 
Bns115(a) :- Ans115(a).
}
namespace ns116{ 
%table Ans116(a:int4) 
%table Bns116(a:int4) 
Bns116(a) :- Ans116(a).
}
namespace ns117{ 
%table Ans117(a:int4) 
%table Bns117(a:int4) 
Bns117(a) :- Ans117(a).
}
namespace ns118{ 
%table Ans118(a:int4) 
%table Bns118(a:int4) 
Bns118(a) :- Ans118(a).
}
namespace ns119{ 
%table Ans119(a:int4) 
%table Bns119(a:int4) 
Bns119(a) :- Ans119(a).
}
namespace ns120{ 
%table Ans120(a:int4) 
%table Bns120(a:int4) 
Bns120(a) :- Ans120(a).
}
namespace ns121{ 
%table Ans121(a:int4) 
%table Bns121(a:int4) 
Bns121(a) :- Ans121(a).
}
namespace ns122{ 
%table Ans122(a:int4) 
%table Bns122(a:int4) 
Bns122(a) :- Ans122(a).
}
namespace ns123{ 
%table Ans123(a:int4) 
%table Bns123(a:int4) 
Bns123(a) :- Ans123(a).
}
namespace ns124{ 
%table Ans124(a:int4) 
%table Bns124(a:int4) 
Bns124(a) :- Ans124(a).
}
namespace ns125{ 
%table Ans125(a:int4) 
%table Bns125(a:int4) 
Bns125(a) :- Ans125(a).
}
namespace ns126{ 
%table Ans126(a:int4) 
%table Bns126(a:int4) 
Bns126(a) :- Ans126(a).
}
namespace ns127{ 
%table Ans127(a:int4) 
%table Bns127(a:int4) 
Bns127(a) :- Ans127(a).
}
namespace ns128{ 
%table Ans128(a:int4) 
%table Bns128(a:int4) 
Bns128(a) :- Ans128(a).
}
namespace ns129{ 
%table Ans129(a:int4) 
%table Bns129(a:int4) 
Bns129(a) :- Ans129(a).
}
namespace ns130{ 
%table Ans130(a:int4) 
%table Bns130(a:int4) 
Bns130(a) :- Ans130(a).
}
namespace ns131{ 
%table Ans131(a:int4) 
%table Bns131(a:int4) 
Bns131(a) :- Ans131(a).
}
namespace ns132{ 
%table Ans132(a:int4) 
%table Bns132(a:int4) 
Bns132(a) :- Ans132(a).
}
namespace ns133{ 
%table Ans133(a:int4) 
%table Bns133(a:int4) 
Bns133(a) :- Ans133(a).
}
namespace ns134{ 
%table Ans134(a:int4) 
%table Bns134(a:int4) 
Bns134(a) :- Ans134(a).
}
namespace ns135{ 
%table Ans135(a:int4) 
%table Bns135(a:int4) 
Bns135(a) :- Ans135(a).
}
namespace ns136{ 
%table Ans136(a:int4) 
%table Bns136(a:int4) 
Bns136(a) :- Ans136(a).
}
namespace ns137{ 
%table Ans137(a:int4) 
%table Bns137(a:int4) 
Bns137(a) :- Ans137(a).
}
namespace ns138{ 
%table Ans138(a:int4) 
%table Bns138(a:int4) 
Bns138(a) :- Ans138(a).
}
namespace ns139{ 
%table Ans139(a:int4) 
%table Bns139(a:int4) 
Bns139(a) :- Ans139(a).
}
namespace ns140{ 
%table Ans140(a:int4) 
%table Bns140(a:int4) 
Bns140(a) :- Ans140(a).
}
namespace ns141{ 
%table Ans141(a:int4) 
%table Bns141(a:int4) 
Bns141(a) :- Ans141(a).
}
namespace ns142{ 
%table Ans142(a:int4) 
%table Bns142(a:int4) 
Bns142(a) :- Ans142(a).
}
namespace ns143{ 
%table Ans143(a:int4) 
%table Bns143(a:int4) 
Bns143(a) :- Ans143(a).
}
namespace ns144{ 
%table Ans144(a:int4) 
%table Bns144(a:int4) 
Bns144(a) :- Ans144(a).
}
namespace ns145{ 
%table Ans145(a:int4) 
%table Bns145(a:int4) 
Bns145(a) :- Ans145(a).
}
namespace ns146{ 
%table Ans146(a:int4) 
%table Bns146(a:int4) 
Bns146(a) :- Ans146(a).
}
namespace ns147{ 
%table Ans147(a:int4) 
%table Bns147(a:int4) 
Bns147(a) :- Ans147(a).
}
namespace ns148{ 
%table Ans148(a:int4) 
%table Bns148(a:int4) 
Bns148(a) :- Ans148(a).
}
namespace ns149{ 
%table Ans149(a:int4) 
%table Bns149(a:int4) 
Bns149(a) :- Ans149(a).
}
namespace ns150{ 
%table Ans150(a:int4) 
%table Bns150(a:int4) 
Bns150(a) :- Ans150(a).
}
namespace ns151{ 
%table Ans151(a:int4) 
%table Bns151(a:int4) 
Bns151(a) :- Ans151(a).
}
namespace ns152{ 
%table Ans152(a:int4) 
%table Bns152(a:int4) 
Bns152(a) :- Ans152(a).
}
namespace ns153{ 
%table Ans153(a:int4) 
%table Bns153(a:int4) 
Bns153(a) :- Ans153(a).
}
namespace ns154{ 
%table Ans154(a:int4) 
%table Bns154(a:int4) 
Bns154(a) :- Ans154(a).
}
namespace ns155{ 
%table Ans155(a:int4) 
%table Bns155(a:int4) 
Bns155(a) :- Ans155(a).
}
namespace ns156{ 
%table Ans156(a:int4) 
%table Bns156(a:int4) 
Bns156(a) :- Ans156(a).
}
namespace ns157{ 
%table Ans157(a:int4) 
%table Bns157(a:int4) 
Bns157(a) :- Ans157(a).
}
namespace ns158{ 
%table Ans158(a:int4) 
%table Bns158(a:int4) 
Bns158(a) :- Ans158(a).
}
namespace ns159{ 
%table Ans159(a:int4) 
%table Bns159(a:int4) 
Bns159(a) :- Ans159(a).
}
namespace ns160{ 
%table Ans160(a:int4) 
%table Bns160(a:int4) 
Bns160(a) :- Ans160(a).
}
namespace ns161{ 
%table Ans161(a:int4) 
%table Bns161(a:int4) 
Bns161(a) :- Ans161(a).
}
namespace ns162{ 
%table Ans162(a:int4) 
%table Bns162(a:int4) 
Bns162(a) :- Ans162(a).
}
namespace ns163{ 
%table Ans163(a:int4) 
%table Bns163(a:int4) 
Bns163(a) :- Ans163(a).
}
namespace ns164{ 
%table Ans164(a:int4) 
%table Bns164(a:int4) 
Bns164(a) :- Ans164(a).
}
namespace ns165{ 
%table Ans165(a:int4) 
%table Bns165(a:int4) 
Bns165(a) :- Ans165(a).
}
namespace ns166{ 
%table Ans166(a:int4) 
%table Bns166(a:int4) 
Bns166(a) :- Ans166(a).
}
namespace ns167{ 
%table Ans167(a:int4) 
%table Bns167(a:int4) 
Bns167(a) :- Ans167(a).
}
namespace ns168{ 
%table Ans168(a:int4) 
%table Bns168(a:int4) 
Bns168(a) :- Ans168(a).
}
namespace ns169{ 
%table Ans169(a:int4) 
%table Bns169(a:int4) 
Bns169(a) :- Ans169(a).
}
namespace ns170{ 
%table Ans170(a:int4) 
%table Bns170(a:int4) 
Bns170(a) :- Ans170(a).
}
namespace ns171{ 
%table Ans171(a:int4) 
%table Bns171(a:int4) 
Bns171(a) :- Ans171(a).
}
namespace ns172{ 
%table Ans172(a:int4) 
%table Bns172(a:int4) 
Bns172(a) :- Ans172(a).
}
namespace ns173{ 
%table Ans173(a:int4) 
%table Bns173(a:int4) 
Bns173(a) :- Ans173(a).
}
namespace ns174{ 
%table Ans174(a:int4) 
%table Bns174(a:int4) 
Bns174(a) :- Ans174(a).
}
namespace ns175{ 
%table Ans175(a:int4) 
%table Bns175(a:int4) 
Bns175(a) :- Ans175(a).
}
namespace ns176{ 
%table Ans176(a:int4) 
%table Bns176(a:int4) 
Bns176(a) :- Ans176(a).
}
namespace ns177{ 
%table Ans177(a:int4) 
%table Bns177(a:int4) 
Bns177(a) :- Ans177(a).
}
namespace ns178{ 
%table Ans178(a:int4) 
%table Bns178(a:int4) 
Bns178(a) :- Ans178(a).
}
namespace ns179{ 
%table Ans179(a:int4) 
%table Bns179(a:int4) 
Bns179(a) :- Ans179(a).
}
namespace ns180{ 
%table Ans180(a:int4) 
%table Bns180(a:int4) 
Bns180(a) :- Ans180(a).
}
namespace ns181{ 
%table Ans181(a:int4) 
%table Bns181(a:int4) 
Bns181(a) :- Ans181(a).
}
namespace ns182{ 
%table Ans182(a:int4) 
%table Bns182(a:int4) 
Bns182(a) :- Ans182(a).
}
namespace ns183{ 
%table Ans183(a:int4) 
%table Bns183(a:int4) 
Bns183(a) :- Ans183(a).
}
namespace ns184{ 
%table Ans184(a:int4) 
%table Bns184(a:int4) 
Bns184(a) :- Ans184(a).
}
namespace ns185{ 
%table Ans185(a:int4) 
%table Bns185(a:int4) 
Bns185(a) :- Ans185(a).
}
namespace ns186{ 
%table Ans186(a:int4) 
%table Bns186(a:int4) 
Bns186(a) :- Ans186(a).
}
namespace ns187{ 
%table Ans187(a:int4) 
%table Bns187(a:int4) 
Bns187(a) :- Ans187(a).
}
namespace ns188{ 
%table Ans188(a:int4) 
%table Bns188(a:int4) 
Bns188(a) :- Ans188(a).
}
namespace ns189{ 
%table Ans189(a:int4) 
%table Bns189(a:int4) 
Bns189(a) :- Ans189(a).
}
namespace ns190{ 
%table Ans190(a:int4) 
%table Bns190(a:int4) 
Bns190(a) :- Ans190(a).
}
namespace ns191{ 
%table Ans191(a:int4) 
%table Bns191(a:int4) 
Bns191(a) :- Ans191(a).
}
namespace ns192{ 
%table Ans192(a:int4) 
%table Bns192(a:int4) 
Bns192(a) :- Ans192(a).
}
namespace ns193{ 
%table Ans193(a:int4) 
%table Bns193(a:int4) 
Bns193(a) :- Ans193(a).
}
namespace ns194{ 
%table Ans194(a:int4) 
%table Bns194(a:int4) 
Bns194(a) :- Ans194(a).
}
namespace ns195{ 
%table Ans195(a:int4) 
%table Bns195(a:int4) 
Bns195(a) :- Ans195(a).
}
namespace ns196{ 
%table Ans196(a:int4) 
%table Bns196(a:int4) 
Bns196(a) :- Ans196(a).
}
namespace ns197{ 
%table Ans197(a:int4) 
%table Bns197(a:int4) 
Bns197(a) :- Ans197(a).
}
namespace ns198{ 
%table Ans198(a:int4) 
%table Bns198(a:int4) 
Bns198(a) :- Ans198(a).
}
namespace ns199{ 
%table Ans199(a:int4) 
%table Bns199(a:int4) 
Bns199(a) :- Ans199(a).
}
namespace ns200{ 
%table Ans200(a:int4) 
%table Bns200(a:int4) 
Bns200(a) :- Ans200(a).
}
namespace ns201{ 
%table Ans201(a:int4) 
%table Bns201(a:int4) 
Bns201(a) :- Ans201(a).
}
namespace ns202{ 
%table Ans202(a:int4) 
%table Bns202(a:int4) 
Bns202(a) :- Ans202(a).
}
namespace ns203{ 
%table Ans203(a:int4) 
%table Bns203(a:int4) 
Bns203(a) :- Ans203(a).
}
namespace ns204{ 
%table Ans204(a:int4) 
%table Bns204(a:int4) 
Bns204(a) :- Ans204(a).
}
namespace ns205{ 
%table Ans205(a:int4) 
%table Bns205(a:int4) 
Bns205(a) :- Ans205(a).
}
namespace ns206{ 
%table Ans206(a:int4) 
%table Bns206(a:int4) 
Bns206(a) :- Ans206(a).
}
namespace ns207{ 
%table Ans207(a:int4) 
%table Bns207(a:int4) 
Bns207(a) :- Ans207(a).
}
namespace ns208{ 
%table Ans208(a:int4) 
%table Bns208(a:int4) 
Bns208(a) :- Ans208(a).
}
namespace ns209{ 
%table Ans209(a:int4) 
%table Bns209(a:int4) 
Bns209(a) :- Ans209(a).
}
namespace ns210{ 
%table Ans210(a:int4) 
%table Bns210(a:int4) 
Bns210(a) :- Ans210(a).
}
namespace ns211{ 
%table Ans211(a:int4) 
%table Bns211(a:int4) 
Bns211(a) :- Ans211(a).
}
namespace ns212{ 
%table Ans212(a:int4) 
%table Bns212(a:int4) 
Bns212(a) :- Ans212(a).
}
namespace ns213{ 
%table Ans213(a:int4) 
%table Bns213(a:int4) 
Bns213(a) :- Ans213(a).
}
namespace ns214{ 
%table Ans214(a:int4) 
%table Bns214(a:int4) 
Bns214(a) :- Ans214(a).
}
namespace ns215{ 
%table Ans215(a:int4) 
%table Bns215(a:int4) 
Bns215(a) :- Ans215(a).
}
namespace ns216{ 
%table Ans216(a:int4) 
%table Bns216(a:int4) 
Bns216(a) :- Ans216(a).
}
namespace ns217{ 
%table Ans217(a:int4) 
%table Bns217(a:int4) 
Bns217(a) :- Ans217(a).
}
namespace ns218{ 
%table Ans218(a:int4) 
%table Bns218(a:int4) 
Bns218(a) :- Ans218(a).
}
namespace ns219{ 
%table Ans219(a:int4) 
%table Bns219(a:int4) 
Bns219(a) :- Ans219(a).
}
namespace ns220{ 
%table Ans220(a:int4) 
%table Bns220(a:int4) 
Bns220(a) :- Ans220(a).
}
namespace ns221{ 
%table Ans221(a:int4) 
%table Bns221(a:int4) 
Bns221(a) :- Ans221(a).
}
namespace ns222{ 
%table Ans222(a:int4) 
%table Bns222(a:int4) 
Bns222(a) :- Ans222(a).
}
namespace ns223{ 
%table Ans223(a:int4) 
%table Bns223(a:int4) 
Bns223(a) :- Ans223(a).
}
namespace ns224{ 
%table Ans224(a:int4) 
%table Bns224(a:int4) 
Bns224(a) :- Ans224(a).
}
namespace ns225{ 
%table Ans225(a:int4) 
%table Bns225(a:int4) 
Bns225(a) :- Ans225(a).
}
namespace ns226{ 
%table Ans226(a:int4) 
%table Bns226(a:int4) 
Bns226(a) :- Ans226(a).
}
namespace ns227{ 
%table Ans227(a:int4) 
%table Bns227(a:int4) 
Bns227(a) :- Ans227(a).
}
namespace ns228{ 
%table Ans228(a:int4) 
%table Bns228(a:int4) 
Bns228(a) :- Ans228(a).
}
namespace ns229{ 
%table Ans229(a:int4) 
%table Bns229(a:int4) 
Bns229(a) :- Ans229(a).
}
namespace ns230{ 
%table Ans230(a:int4) 
%table Bns230(a:int4) 
Bns230(a) :- Ans230(a).
}
namespace ns231{ 
%table Ans231(a:int4) 
%table Bns231(a:int4) 
Bns231(a) :- Ans231(a).
}
namespace ns232{ 
%table Ans232(a:int4) 
%table Bns232(a:int4) 
Bns232(a) :- Ans232(a).
}
namespace ns233{ 
%table Ans233(a:int4) 
%table Bns233(a:int4) 
Bns233(a) :- Ans233(a).
}
namespace ns234{ 
%table Ans234(a:int4) 
%table Bns234(a:int4) 
Bns234(a) :- Ans234(a).
}
namespace ns235{ 
%table Ans235(a:int4) 
%table Bns235(a:int4) 
Bns235(a) :- Ans235(a).
}
namespace ns236{ 
%table Ans236(a:int4) 
%table Bns236(a:int4) 
Bns236(a) :- Ans236(a).
}
namespace ns237{ 
%table Ans237(a:int4) 
%table Bns237(a:int4) 
Bns237(a) :- Ans237(a).
}
namespace ns238{ 
%table Ans238(a:int4) 
%table Bns238(a:int4) 
Bns238(a) :- Ans238(a).
}
namespace ns239{ 
%table Ans239(a:int4) 
%table Bns239(a:int4) 
Bns239(a) :- Ans239(a).
}
namespace ns240{ 
%table Ans240(a:int4) 
%table Bns240(a:int4) 
Bns240(a) :- Ans240(a).
}
namespace ns241{ 
%table Ans241(a:int4) 
%table Bns241(a:int4) 
Bns241(a) :- Ans241(a).
}
namespace ns242{ 
%table Ans242(a:int4) 
%table Bns242(a:int4) 
Bns242(a) :- Ans242(a).
}
namespace ns243{ 
%table Ans243(a:int4) 
%table Bns243(a:int4) 
Bns243(a) :- Ans243(a).
}
namespace ns244{ 
%table Ans244(a:int4) 
%table Bns244(a:int4) 
Bns244(a) :- Ans244(a).
}
namespace ns245{ 
%table Ans245(a:int4) 
%table Bns245(a:int4) 
Bns245(a) :- Ans245(a).
}
namespace ns246{ 
%table Ans246(a:int4) 
%table Bns246(a:int4) 
Bns246(a) :- Ans246(a).
}
namespace ns247{ 
%table Ans247(a:int4) 
%table Bns247(a:int4) 
Bns247(a) :- Ans247(a).
}
namespace ns248{ 
%table Ans248(a:int4) 
%table Bns248(a:int4) 
Bns248(a) :- Ans248(a).
}
namespace ns249{ 
%table Ans249(a:int4) 
%table Bns249(a:int4) 
Bns249(a) :- Ans249(a).
}
namespace ns250{ 
%table Ans250(a:int4) 
%table Bns250(a:int4) 
Bns250(a) :- Ans250(a).
}
namespace ns251{ 
%table Ans251(a:int4) 
%table Bns251(a:int4) 
Bns251(a) :- Ans251(a).
}
namespace ns252{ 
%table Ans252(a:int4) 
%table Bns252(a:int4) 
Bns252(a) :- Ans252(a).
}
namespace ns253{ 
%table Ans253(a:int4) 
%table Bns253(a:int4) 
Bns253(a) :- Ans253(a).
}
namespace ns254{ 
%table Ans254(a:int4) 
%table Bns254(a:int4) 
Bns254(a) :- Ans254(a).
}
