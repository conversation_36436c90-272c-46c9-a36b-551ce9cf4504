/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: tbmtransfer.cpp
 * Description: tbm table transfer to external table
 * Author: qibingsen 00880292
 * Create: 2024-08-31
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "modeltransfer.h"

int ret;
class tbmModelTransferTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void tbmModelTransferTest::SetUpTestCase()
{
    system("sh ${TEST_HOME}/tools/stop.sh -f");
#if defined ENV_EULER
    system("${TEST_HOME}/tools/modifyCfg.sh  workerHungThreshold=3,4,10");
#endif
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void tbmModelTransferTest::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
}

void tbmModelTransferTest::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void tbmModelTransferTest::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    printf("\n======================TEST:END========================\n");
}

/* ****************************************************************************
写包含tbm表的d文件，tbm表名长度511，包含uinit和init函数，编译.so，打开分布式开关，加载so到public命名空间，预期加载失败
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    char g_errorCode02[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(g_errorCode02, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_INTERNAL_ERROR);
    AW_ADD_TRUNCATION_WHITE_LIST(3, "Memory operate unsucc. sprintf fakeName", "Internal unsucc. release vertexLabel",
                                 "Internal unsucc. label reference count mistake");
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char filepath[MAX_FILE_PATH] = "datalogfile/tbmfile/longtbmtable.so";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath, g_testNameSpace, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_MEMORY_OPERATE_FAILED, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，在.d文件中创建命名空间nsp1, 其中创建tbm表，表名长度506，包含uinit和init函数，编译.so，
打开分布式开关，加载so到public命名空间，预期加载失败
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    char g_errorCode02[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(g_errorCode02, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_INTERNAL_ERROR);
    AW_ADD_TRUNCATION_WHITE_LIST(3, "Memory operate unsucc. sprintf fakeName", "Internal unsucc. release vertexLabel",
                                 "Internal unsucc. label reference count mistake");
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char filepath[MAX_FILE_PATH] = "datalogfile/tbmfile/longtbmtableinnamespace.so";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath, g_testNameSpace, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_MEMORY_OPERATE_FAILED, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，包含uinit和init函数，gmprecompiler生成.c文件，gcc不加udf生成so
打开分布式开关，加载so到public命名空间，预期加载失败。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_GET_THIRD_PARTY_FUNCTION_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char soname[MAX_FILE_PATH] = "tbmtablewithoutudf";
    char dfilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf.d";
    char sofilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf.so";
    char cfilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf.c";
    char rulefilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf_rule.d";
    char gmdbpath[MAX_FILE_PATH] = "../../../../../pub/include/";
    RmTxt(cfilepath);
    RmTxt(sofilepath);
    RmTxt(rulefilepath);
    char gmprecompilercmd[MAX_CMD_SIZE] = { 0 };
    char gcccmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(gmprecompilercmd, MAX_CMD_SIZE, "gmprecompiler -f ./%s ./%s", dfilepath, cfilepath);
    ret = executeCommand(gmprecompilercmd, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gcccmd, MAX_CMD_SIZE, "gcc ./%s  -fPIC --shared -Wl,-Bsymbolic -I %s -o ./%s", cfilepath, gmdbpath,
                   sofilepath);
    ret = executeCommand(gcccmd, "");
    AW_MACRO_EXPECT_EQ_INT(CMDOUTNULL, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, sofilepath, g_testNameSpace, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_GET_THIRD_PARTY_FUNCTION_FAILED, ret);
    RmTxt(cfilepath);
    RmTxt(sofilepath);
    RmTxt(rulefilepath);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，包含uinit和init函数，gmprecompiler生成.c文件，gcc加udf生成so
打开分布式开关，加载so到public命名空间，预期加载成功，卸载so，预期卸载成功。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char soname[MAX_FILE_PATH] = "tbmtablewithoutudf";
    char dfilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf.d";
    char sofilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf.so";
    char cfilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf.c";
    char udffilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf_udf.c";
    char rulefilepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtablewithoutudf_rule.d";
    char gmdbpath[MAX_FILE_PATH] = "../../../../../pub/include/";
    char gmprecompilercmd[MAX_CMD_SIZE] = { 0 };
    char gcccmd[MAX_CMD_SIZE] = { 0 };
    RmTxt(cfilepath);
    RmTxt(sofilepath);
    RmTxt(rulefilepath);
    (void)snprintf(gmprecompilercmd, MAX_CMD_SIZE, "gmprecompiler -f ./%s ./%s", dfilepath, cfilepath);
    ret = executeCommand(gmprecompilercmd, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gcccmd, MAX_CMD_SIZE, "gcc ./%s ./%s -fPIC --shared -Wl,-Bsymbolic -I %s -o ./%s", cfilepath,
                   udffilepath, gmdbpath, sofilepath);
    ret = executeCommand(gmprecompilercmd, "");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(gcccmd);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, sofilepath, g_testNameSpace, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RmTxt(cfilepath);
    RmTxt(sofilepath);
    RmTxt(rulefilepath);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含1个tbm表的d文件和包含1个tbm表的d文件（tbm表异名  长度506），编译1.so和2.so，
1.so打开分布式开关加载到nsp1，2.so关闭分布式开关加载到nsp1，预期加载失败。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char filepath1[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtable1.so";
    char soname1[MAX_FILE_PATH] = "tbmtable1";
    char filepath2[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtable2.so";
    char soname2[MAX_FILE_PATH] = "tbmtable2";
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath1, g_namespace1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath2, g_namespace1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含1个tbm表的d文件和包含1个tbm表的d文件（tbm表同名，长度506），编译1.so和2.so，
1.so打开分布式开关加载到nsp1，2.so关闭分布式开关加载到nsp1，预期加载失败。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_TRUNCATION_WHITE_LIST(1, "Duplicate table. table exist mistake, label");
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char filepath1[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtable1.so";
    char soname1[MAX_FILE_PATH] = "tbmtable1";
    char filepath2[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmtable1_1.so";
    char soname2[MAX_FILE_PATH] = "tbmtable1_1";
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath1, g_namespace1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath2, g_namespace1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，包含uinit和init函数，编译.so，打开分布式开关，加载so到public命名空间，
检查tbm表对应外部表是否调用init/uinit函数和tbm函数，预期校验成功。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath1[MAX_FILE_PATH] = "datalogfile/tbmfile/normaltbmtable.so";
    char soname1[MAX_FILE_PATH] = "normaltbmtable";
    char tbmwritefile[MAX_FILE_PATH] = { 0 };
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath1, g_testNameSpace, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char rpath[] = { '/', '\0' };
    (void)snprintf(tbmwritefile, MAX_FILE_PATH, "%croot/_datalog_/TbmRunLog.txt", rpath[0]);
    ret = CheckFileExist(tbmwritefile);
    AW_MACRO_EXPECT_EQ_INT(FILENOTEXIST, ret);
    int32_t dataStart = 1, writeCount = 5, keyId = 0, threadId = 0;
    bool isBatch = false, isStruct = false, isAsync = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp, dataStart, writeCount, isBatch, isStruct, g_namespace1 };
    ret = WriteTable(g_conn, g_stmt, vertexCfg, SetinpValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckFileExist(tbmwritefile);
    AW_MACRO_EXPECT_EQ_INT(FILENOTEXIST, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckFileExist(tbmwritefile);
    AW_MACRO_EXPECT_EQ_INT(FILENOTEXIST, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class tbmModelTransferTest1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void tbmModelTransferTest1::SetUpTestCase()
{
#if defined RUN_INDEPENDENT
    system("sh ${TEST_HOME}/tools/stop.sh -f");
#endif
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void tbmModelTransferTest1::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tbmModelTransferTest1::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void tbmModelTransferTest1::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    printf("\n======================TEST:END========================\n");
}

/* ****************************************************************************
写包含tbm表的d文件，包含uinit和init函数，编译.so，打开分布式开关，加载so到public命名空间，
直接删除tbm对应的外部表，卸载so，校验warning日志。
**************************************************************************** */
TEST_F(tbmModelTransferTest1, DataLog_091_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#if defined RUN_INDEPENDENT
    char envCheck[] = "$TEST_HOME/";
    char log_path[] = "log/run/rgmserver/rgmserver.log";
#else
    char envCheck[] = { '/', '\0' };
    char log_path[] = "opt/vrpv8/home/<USER>/diag.log";
#endif
    char filepath1[MAX_FILE_PATH] = "datalogfile/tbmfile/normaltbmtable.so";
    char soname1[MAX_FILE_PATH] = "normaltbmtable";
    char tbmwritefile[MAX_FILE_PATH] = { 0 };
    char catcmd[MAX_CMD_SIZE] = { 0 };
    char logcmd[MAX_CMD_SIZE] = { 0 };
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath1, g_testNameSpace, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  //预期成功
    ret = ModelTransferUnLoadDatalog(g_stmt, soname1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#ifndef RUN_INDEPENDENT
    (void)snprintf(logcmd, MAX_CMD_SIZE, "%sbin/logctrl --cachefilestore=diaglog", &envCheck);
    system(logcmd);
#endif
    (void)snprintf(catcmd, MAX_CMD_SIZE, "cat %s%s", &envCheck, &log_path);
    ret = executeCommand(catcmd,
                         "GMWARN-1009010, Undefined table. get external table by fake label:fake_tbmtable when drop "
                         "all table in so");  //预期warning日志为找不到外部表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，表名长度506，包含各种字段63个，主键包含31个字段，
包含uinit和init函数，编译.so，同一文件分别加载到两个命名空间在nsp1打开分布式开关，nsp2关闭分布式开关，
分别校验tbm在两个命名空间的字段，预期校验成功。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath1[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmalltype.so";
    char soname1[MAX_FILE_PATH] = "tbmalltype";
    AW_ADD_TRUNCATION_WHITE_LIST(1, "GMWARN-1004003, Internal field overflow.");
    char tbmwritefile[MAX_FILE_PATH] = { 0 };
    char catcmd[MAX_CMD_SIZE] = { 0 };
    char tbmtablename[MAX_NAME_LENGTH] = { 0 };
    for (int i = 0; i < 63; i++) {
        strcat(tbmtablename, "tbmtable");
    }
    strcat(tbmtablename, "tb");
    int32_t dataStart = 1, writeCount = 10, keyId = 0, threadId = 0;
    bool isBatch = false, isStruct = false, isAsync = false;
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath1, g_namespace1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp2, dataStart, writeCount, isBatch, isStruct, g_namespace1 };
    ret = WriteTable(g_conn, g_stmt, vertexCfg, SetAllTypeValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtlabelCfgT vertexCfg1 = { GMC_OPERATION_UPDATE, tbmtablename, dataStart, writeCount, isBatch, isStruct,
                               g_namespace1 };
    ret = UpdateTableUpdateExternalAllTypeValue(g_conn, g_stmt, vertexCfg1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  //预期成功
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath1, g_namespace2, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtlabelCfgT vertexCfg2 = { GMC_OPERATION_INSERT, g_inp2, dataStart, writeCount, isBatch, isStruct, g_namespace2 };
    ret = WriteTable(g_conn, g_stmt, vertexCfg2, SetAllTypeValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtlabelCfgT vertexCfg3 = { GMC_OPERATION_UPDATE, g_inp2, dataStart, writeCount, isBatch, isStruct, g_namespace2 };
    ret = UpdateTableUpdateDatalogAllTypeValue(g_conn, g_stmt, vertexCfg3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname1, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，包含uinit和init函数，编译.so，同一文件加载到public命名空间，
查询CATA_UDF_INFO，CATA_TBM_TABLE_INFO，CATA_VERTEX_LABEL_INFO视图，预期校验成功
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/tbmfile/normaltbmtable.so";
    char soname[MAX_FILE_PATH] = "normaltbmtable";
    char tbmwritefile[MAX_FILE_PATH] = { 0 };
    char catcmd[MAX_CMD_SIZE] = { 0 };
    char logcmd[MAX_CMD_SIZE] = { 0 };
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath, g_namespace1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath, g_namespace2, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int nsp1_ID = 0;
    ret = TestGetNamespaceIDVal(&nsp1_ID, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int nsp2_ID = 0;
    ret = TestGetNamespaceIDVal(&nsp2_ID, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsp1udfcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(nsp1udfcmd, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_UDF_INFO -f NAMESPACE_ID=%d", nsp1_ID);
    char nsp2udfcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(nsp2udfcmd, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_UDF_INFO -f NAMESPACE_ID=%d", nsp2_ID);
    char nsp1tbmcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(nsp1tbmcmd, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_TBM_TABLE_INFO -f NAMESPACE_ID=%d", nsp1_ID);
    char nsp2tbmcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(nsp2tbmcmd, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_TBM_TABLE_INFO -f NAMESPACE_ID=%d", nsp2_ID);

    ret = executeCommand(nsp1tbmcmd, "TBM_TABLE_NAME: tbmtable");  //查询不到
    AW_MACRO_EXPECT_EQ_INT(VIEWNOTFIND, ret);
    ret = executeCommand(nsp2tbmcmd, "TBM_TABLE_NAME: tbmtable");  //查询到
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(nsp1udfcmd, "UDF_TYPE: DM_UDF_TBM");  //查询不到
    AW_MACRO_EXPECT_EQ_INT(VIEWNOTFIND, ret);
    ret = executeCommand(nsp2udfcmd, "UDF_TYPE: DM_UDF_TBM");  //查询到
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int nsp1vertexid = -1;
    ret = TestGetCertainVertexID(nsp1_ID, g_tbmtable, &nsp1vertexid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsp1vertexcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(nsp1vertexcmd, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_ID=%d",
                   nsp1vertexid);
    ret = executeCommand(nsp1vertexcmd, "VERTEX_LABEL_TYPE: VERTEX_TYPE_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  //查询到
    nsp1vertexid = -1;
    ret = TestGetCertainVertexID(nsp1_ID, g_faketbmtable, &nsp1vertexid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(nsp1vertexcmd, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_ID=%d",
                   nsp1vertexid);
    ret = executeCommand(nsp1vertexcmd, "VERTEX_LABEL_TYPE: VERTEX_TYPE_DATALOG");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  //查询到
    int nsp2vertexid = -1;
    ret = TestGetCertainVertexID(nsp2_ID, g_tbmtable, &nsp2vertexid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsp2vertexcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(nsp2vertexcmd, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_ID=%d",
                   nsp2vertexid);
    ret = executeCommand(nsp2vertexcmd, "VERTEX_LABEL_TYPE: VERTEX_TYPE_DATALOG");
    AW_MACRO_EXPECT_EQ_INT(VIEWNOTFIND, ret);  //查询不到
    (void)snprintf(nsp2vertexcmd, MAX_CMD_SIZE, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_ID=%d",
                   nsp2vertexid);
    ret = executeCommand(nsp2vertexcmd, "VERTEX_LABEL_TYPE: VERTEX_TYPE_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(VIEWNOTFIND, ret);  //查询不到
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，包含uinit和init函数，编译.so，加载到nsp1打开分布式开关，向tbm转换的外部表写数据，预期写入成功。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/tbmfile/tbmalltype.so";
    char soname[MAX_FILE_PATH] = "tbmalltype";
    char tbmwritefile[MAX_FILE_PATH] = { 0 };
    char tbmtablename[MAX_NAME_LENGTH] = { 0 };
    for (int i = 0; i < 63; i++) {
        strcat(tbmtablename, "tbmtable");
    }
    strcat(tbmtablename, "tb");
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath, g_namespace1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t dataStart = 1, writeCount = 5, keyId = 0, threadId = 0;
    bool isBatch = false, isStruct = false, isAsync = false;
    GtlabelCfgT vertexCfg = {
        GMC_OPERATION_INSERT, tbmtablename, dataStart, writeCount, isBatch, isStruct, g_namespace1
    };
    ret = WriteTable(g_conn, g_stmt, vertexCfg, SetExternalAllTypeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，包含uinit和init函数，编译.so，打开分布式开关，加载so到public命名空间，
正常订阅tbm表对应的外部表，向输入表中写数据，校验tbm对应外部表的数据是否正确，预期校验成功，取消订阅，卸载so，预期卸载成功。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char filepath[MAX_FILE_PATH] = "datalogfile/tbmfile/normaltbmtable.so";
    char soname[MAX_FILE_PATH] = "normaltbmtable";
    char tbmwritefile[MAX_FILE_PATH] = { 0 };
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath, g_testNameSpace, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataWithFuncT userDataF = { 0 };
    int32_t dataStart = 1, writeCount = 10, keyId = 0, threadId = 0;
    bool isBatch = false, isStruct = false, isAsync = false;
    ret = createSubscription(g_stmt, g_connSub, (char *)"schemafile/tbmtable.gmjson", &userDataF,
                             dataStart + writeCount, g_tbmtable, snCallback, ExternaltbmtableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp, dataStart, writeCount, isBatch, isStruct, g_testNameSpace };
    ret = WriteTable(g_conn, g_stmt, vertexCfg, SetinpValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataF.data, GMC_SUB_EVENT_MERGE, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(writeCount, userDataF.data->callbackTimes);

    ret = cancelSubscription(g_stmt, g_tbmtable, &userDataF, dataStart, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm表的d文件，编译.so，打开分布式开关，加载so到public命名空间，
订阅tbm表对应的外部表，卸载so，预期卸载失败，取消订阅，卸载so失败，删除tbm对应外部表成功。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    char g_errorCode02[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char filepath[MAX_FILE_PATH] = "datalogfile/tbmfile/normaltbmtable.so";
    char soname[MAX_FILE_PATH] = "normaltbmtable";
    char tbmwritefile[MAX_FILE_PATH] = { 0 };
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(g_stmt, filepath, g_testNameSpace, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataWithFuncT userDataF = { 0 };
    int32_t dataStart = 1, writeCount = 10, keyId = 0, threadId = 0;
    bool isBatch = false, isStruct = false, isAsync = false;
    ret = createSubscription(g_stmt, g_connSub, (char *)"schemafile/tbmtable.gmjson", &userDataF,
                             dataStart + writeCount, g_tbmtable, snCallback, ExternaltbmtableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp, dataStart, writeCount, isBatch, isStruct, g_testNameSpace };
    ret = WriteTable(g_conn, g_stmt, vertexCfg, SetinpValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF.data, GMC_SUB_EVENT_MERGE, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(writeCount, userDataF.data->callbackTimes);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = cancelSubscription(g_stmt, g_tbmtable, &userDataF, dataStart, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(g_stmt, soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm/notify表的d文件，init() sleep(4)编译.so，一个打开分布式开关，一个关闭分布式开关，
并发加载so到两个命名空间，分布式开关关闭的so先执行，预期加载失败。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char filepath[MAX_NAME_LENGTH] = { 0 };
    char soname[MAX_NAME_LENGTH] = { 0 };
#if defined ENV_RTOSV2X
    (void)snprintf(filepath, MAX_NAME_LENGTH, "./datalogfile/tbmfile/initsleep16table.so");
    (void)snprintf(soname, MAX_NAME_LENGTH, "initsleep16table");
#else
    (void)snprintf(filepath, MAX_NAME_LENGTH, "./datalogfile/tbmfile/initsleep4table.so");
    (void)snprintf(soname, MAX_NAME_LENGTH, "initsleep4table");
#endif
    GmcConnT *conn1 = NULL, *conn2 = NULL;
    GmcStmtT *stmt1 = NULL, *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt1, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt2, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    LoadDataLogCfgT cfg1 = { g_namespace1, filepath, soname, stmt1, false, false };
    LoadDataLogCfgT cfg2 = { g_namespace2, filepath, soname, stmt2, true, true };
    int32_t threadNum = 2;
    pthread_t write_threads[threadNum];

    //多线程并发写
    ret = pthread_create(&write_threads[0], NULL, MultiThreadLoadDataLog1, (LoadDataLogCfgT *)&cfg1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&write_threads[1], NULL, MultiThreadLoadDataLog2, (LoadDataLogCfgT *)&cfg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        ret = pthread_join(write_threads[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm/notify表的d文件，init() sleep(4)编译.so，一个打开分布式开关，一个关闭分布式开关，
并发加载so到两个命名空间，分布式开关打开的so先执行，预期加载成功。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char filepath[MAX_NAME_LENGTH] = { 0 };
    char soname[MAX_NAME_LENGTH] = { 0 };
#if defined ENV_RTOSV2X
    (void)snprintf(filepath, MAX_NAME_LENGTH, "./datalogfile/tbmfile/initsleep16table.so");
    (void)snprintf(soname, MAX_NAME_LENGTH, "initsleep16table");
#else
    (void)snprintf(filepath, MAX_NAME_LENGTH, "./datalogfile/tbmfile/initsleep4table.so");
    (void)snprintf(soname, MAX_NAME_LENGTH, "initsleep4table");
#endif
    GmcConnT *conn1 = NULL, *conn2 = NULL;
    GmcStmtT *stmt1 = NULL, *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt1, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt2, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    LoadDataLogCfgT cfg1 = { g_namespace1, filepath, soname, stmt1, false, true };
    LoadDataLogCfgT cfg2 = { g_namespace2, filepath, soname, stmt2, false, false };
    int32_t threadNum = 2;
    pthread_t write_threads[threadNum];

    //多线程并发写
    ret = pthread_create(&write_threads[0], NULL, MultiThreadLoadDataLog, (LoadDataLogCfgT *)&cfg1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&write_threads[1], NULL, MultiThreadLoadDataLog, (LoadDataLogCfgT *)&cfg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        ret = pthread_join(write_threads[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm/notify表的d文件，uinit() sleep(4)编译.so，打开分布式开关加载so到两个命名空间，
并发卸载so，先分布式开关关闭卸载so，后分布式开关打开卸载so，预期卸载失败。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char filepath[MAX_NAME_LENGTH] = { 0 };
    char soname[MAX_NAME_LENGTH] = { 0 };
#if defined ENV_RTOSV2X
    (void)snprintf(filepath, MAX_NAME_LENGTH, "./datalogfile/tbmfile/uninitsleep16table.so");
    (void)snprintf(soname, MAX_NAME_LENGTH, "uninitsleep16table");
#else
    (void)snprintf(filepath, MAX_NAME_LENGTH, "./datalogfile/tbmfile/uninitsleep4table.so");
    (void)snprintf(soname, MAX_NAME_LENGTH, "uninitsleep4table");
#endif
    GmcConnT *conn1 = NULL, *conn2 = NULL;
    GmcStmtT *stmt1 = NULL, *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt1, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt2, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(stmt1, filepath, g_namespace1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt2, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(stmt2, filepath, g_namespace2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    LoadDataLogCfgT cfg1 = { g_namespace1, filepath, soname, stmt1, false, false };
    LoadDataLogCfgT cfg2 = { g_namespace2, filepath, soname, stmt2, true, true };
    int32_t threadNum = 2;
    pthread_t write_threads[threadNum];

    //多线程并发写
    ret = pthread_create(&write_threads[0], NULL, MultiThreadUnLoadDataLog, (LoadDataLogCfgT *)&cfg1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&write_threads[1], NULL, MultiThreadUnLoadDataLog, (LoadDataLogCfgT *)&cfg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        ret = pthread_join(write_threads[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespace(stmt1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt2, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferUnLoadDatalog(stmt2, soname, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt2, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
写包含tbm/notify表的d文件，uinit() sleep(4)编译.so，打开分布式开关加载so到两个命名空间，
并发卸载so，先分布式开关打开卸载so，后分布式开关关闭卸载so，预期卸载成功。
**************************************************************************** */
TEST_F(tbmModelTransferTest, DataLog_091_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_errorCode01[LOG_MAX_SIZE_OF_ERROR_MSG] = { 0 };
    (void)snprintf(g_errorCode01, LOG_MAX_SIZE_OF_ERROR_MSG, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char filepath[MAX_NAME_LENGTH] = { 0 };
    char soname[MAX_NAME_LENGTH] = { 0 };
#if defined ENV_RTOSV2X
    (void)snprintf(filepath, MAX_NAME_LENGTH, "./datalogfile/tbmfile/uninitsleep16table.so");
    (void)snprintf(soname, MAX_NAME_LENGTH, "uninitsleep16table");
#else
    (void)snprintf(filepath, MAX_NAME_LENGTH, "./datalogfile/tbmfile/uninitsleep4table.so");
    (void)snprintf(soname, MAX_NAME_LENGTH, "uninitsleep4table");
#endif
    GmcConnT *conn1 = NULL, *conn2 = NULL;
    GmcStmtT *stmt1 = NULL, *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt1, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt2, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(stmt1, filepath, g_namespace1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt2, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ModelTransferLoadDatalog(stmt2, filepath, g_namespace2, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    LoadDataLogCfgT cfg1 = { g_namespace1, filepath, soname, stmt1, false, true };
    LoadDataLogCfgT cfg2 = { g_namespace2, filepath, soname, stmt2, false, false };
    int32_t threadNum = 2;
    pthread_t write_threads[threadNum];

    //多线程并发写
    ret = pthread_create(&write_threads[0], NULL, MultiThreadUnLoadDataLog, (LoadDataLogCfgT *)&cfg1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&write_threads[1], NULL, MultiThreadUnLoadDataLog, (LoadDataLogCfgT *)&cfg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        ret = pthread_join(write_threads[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespace(stmt1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt1, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt2, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt2, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
