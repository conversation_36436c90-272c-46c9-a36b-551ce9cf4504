/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: SupGmconvert.cpp
 * Description: Datalog支持gmconvert工具
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2022-12-16
 */
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "SupGmconvert.h"

#define MAX_CMD_SIZE 1024

using namespace std;
char g_command[MAX_CMD_SIZE];

class SupGmconvert : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SupGmconvert::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}
void SupGmconvert::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001.gmconvert -v正确展示版本信息
TEST_F(SupGmconvert, DataLog_016_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -v", g_toolPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "VERSION", "gmconvert GMDBV5", "Copyright (c) Huawei Technologies Co.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.gmconvert -h正确展示help信息
TEST_F(SupGmconvert, DataLog_016_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -h", g_toolPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectHelp, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.gmconvert指定1个.d文件，不指定输出文件夹，在当前目录生成gmjson文件
TEST_F(SupGmconvert, DataLog_016_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char inPath[256] = "project_file/project_alltype.d";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s", g_toolPath, inPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验A1.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat A1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_gmjson1, result));
    free(result);

    // 校验B1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat B1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_gmjson2, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.gmconvert指定1个.d文件，指定一个输出文件夹，在指定文件夹生成gmjson文件
TEST_F(SupGmconvert, DataLog_016_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_alltype.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验A1.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/A1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_gmjson1, result));
    free(result);

    // 校验B1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/B1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_gmjson2, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.gmconvert转化一个normal_table
TEST_F(SupGmconvert, DataLog_016_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_alltype.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验A1.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/A1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_gmjson1, result));
    free(result);

    // 校验B1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/B1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_gmjson2, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.gmconvert转化一个update_table
TEST_F(SupGmconvert, DataLog_016_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_update.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验Update.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/Update.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_update, result));
    free(result);

    // 校验UpdateOut.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/UpdateOut.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_updateout, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.gmconvert转化一个partially_update_table
TEST_F(SupGmconvert, DataLog_016_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_partially_update.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验partially_update.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/partially_update.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_partially_update, result));
    free(result);

    // 校验partially_update_out.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/partially_update_out.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_partially_update_out, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.gmconvert转化一个transient_table
TEST_F(SupGmconvert, DataLog_016_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_transient.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验transient_tuple.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/transient_tuple.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_transient_tuple, result));
    free(result);

    // 校验transient_field.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/transient_field.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_transient_field, result));
    free(result);

    // 校验transient_out.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/transient_out.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_transient_out, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.gmconvert转化一个extern_table
TEST_F(SupGmconvert, DataLog_016_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_external.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // external不会被输出
    snprintf(g_command, MAX_CMD_SIZE, "ls planStrFile");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "external.gmjson");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = executeCommand(g_command, "E1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.gmconvert转化一个sequential_resource
TEST_F(SupGmconvert, DataLog_016_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_sequential_resource.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验seqrsc.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/seqrsc.gmjson"); // resource的主键字段不包括输出字段
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_seqrsc, result));
    free(result);

    // 校验seqout.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/seqout.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_seqout, result));
    free(result);

    // 校验seqin.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/seqin.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_seqin, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.gmconvert转化一个pubsub_resource
TEST_F(SupGmconvert, DataLog_016_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_pubsub_resource.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验pubsubrsc.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/pubsubrsc.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_pubsub_resource, result));
    free(result);

    // 校验pubsubin.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/pubsubin.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_pubsubin, result));
    free(result);

    // 校验pubsubout.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/pubsubout.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_pubsubout, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.gmconvert转化一个function
TEST_F(SupGmconvert, DataLog_016_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_function.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验func1.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/func1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_func1, result));
    free(result);

    // 校验func2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/func2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_func2, result));
    free(result);

    // 校验F1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/F1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_F1, result));
    free(result);

    // 校验F2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/F2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_F2, result));
    free(result);

    // 校验F3.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/F3.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_F3, result));
    free(result);

    // 校验F4.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/F4.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_F4, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.gmconvert转化一个aggregate
TEST_F(SupGmconvert, DataLog_016_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_aggregate.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验agg_1.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_1.gmjson"); //agg预留key，目前没有使用，无需index0
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_1, result));
    free(result);

    // 校验agg_2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_2, result));
    free(result);

    // 校验agg_3.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_3.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_3, result));
    free(result);

    // 校验agg_a1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_a1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_a1, result));
    free(result);

    // 校验agg_a2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_a2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_a2, result));
    free(result);

    // 校验agg_a3.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_a3.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_a3, result));
    free(result);

    // 校验agg_b1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_b1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_b1, result));
    free(result);

    // 校验agg_b2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_b2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_b2, result));
    free(result);

    // 校验agg_b3.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/agg_b3.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_agg_b3, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.gmconvert转化一个含input，intermediate，output表的.d文件
TEST_F(SupGmconvert, DataLog_016_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/join_int.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验J1.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/J1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_j1, result));
    free(result);

    // 校验J2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/J2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_j2, result));
    free(result);

    // 校验J1J2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/J1J2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_j1j2, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.gmconvert转化一个含多个表的.d文件
TEST_F(SupGmconvert, DataLog_016_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_multiple.d";
    char outPath[256] = "planStrFile2";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验A1.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/A1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_gmjson1, result));
    free(result);

    // 校验B1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/B1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_gmjson2, result));
    free(result);

    // 校验J1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/J1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_j1, result));
    free(result);

    // 校验J2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/J2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_j2, result));
    free(result);

    // 校验J1J2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/J1J2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_j1j2, result));
    free(result);

    // 校验func1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/func1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_func1, result));
    free(result);

    // 校验F1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/F1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_F1, result));
    free(result);

    // 校验F2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/F2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_F2, result));
    free(result);

    // 校验Update.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/Update.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_update, result));
    free(result);

    // 校验UpdateOut.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/UpdateOut.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_updateout, result));
    free(result);

    // 校验seqrsc.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/seqrsc.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_seqrsc, result));
    free(result);

    // 校验seqin.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/seqin.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_seqin, result));
    free(result);

    // 校验seqout.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/seqout.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_seqout, result));
    free(result);

    // 校验timeout1.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/timeout1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_timeout1, result));
    free(result);

    // 校验timeout2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile2/timeout2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_timeout2, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.gmconvert转化一个.d文件100次
TEST_F(SupGmconvert, DataLog_016_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_alltype.d";
    char outPath[256] = "planStrFile";

    for (int i = 0; i < 100; i++) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
        ret = executeCommand(g_command, "successfully");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验A1.gmjson
        char *result = NULL;
        snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/A1.gmjson");
        ret = TestViewData(g_command, &result);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        EXPECT_STRNE(NULL, strstr(expect_gmjson1, result));
        free(result);

        // 校验B1.gmjson
        result = NULL;
        snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/B1.gmjson");
        ret = TestViewData(g_command, &result);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        EXPECT_STRNE(NULL, strstr(expect_gmjson2, result));
        free(result);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.gmconvert转化一个含maxSize的表
TEST_F(SupGmconvert, DataLog_016_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_maxsize.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验maxsize_in.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/maxsize_in.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_maxsize_in, result));
    free(result);

    // 校验maxsize_out.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/maxsize_out.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_maxsize_out, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.gmconvert转化一个含ns名称的表
TEST_F(SupGmconvert, DataLog_016_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_namespace.d";
    char outPath[256] = "planStrFile";

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验ns1_A.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/ns1_A.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_ns1_A, result));
    free(result);

    // 校验ns1_B.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/ns1_B.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_ns1_B, result));
    free(result);

    // 校验ns2_A.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/ns2_A.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_ns2_A, result));
    free(result);

    // 校验ns2_B.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/ns2_B.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_ns2_B, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.gmconvert转化一个显式定义32个index的表
TEST_F(SupGmconvert, DataLog_016_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_32index.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验index32A.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/index32A.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_32indexA, result));
    free(result);

    // 校验index32B.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/index32B.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_32indexB, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.gmconvert转化一个不连续index的表
TEST_F(SupGmconvert, DataLog_016_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_discontinuity.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验discontinuityin.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/discontinuityin.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_discontinuityin, result));
    free(result);

    // 校验discontinuityout.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/discontinuityout.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_discontinuityout, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 异常场景
// 025.gmconvert -i 传入一个.c后缀的文件，工具报错
TEST_F(SupGmconvert, DataLog_016_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_alltype.c";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "传入.c后缀文件，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "Input file is not a datalog file");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.gmconvert -i 传入2个.d文件，工具报错
TEST_F(SupGmconvert, DataLog_016_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath1[256] = "project_file/project_alltype.d";
    char inPath2[256] = "project_file/project_update.d";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s %s", g_toolPath, inPath1, inPath2);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "传入2个.d文件，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "The option(\"-i\") must input 1 parameter(s).");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.gmconvert -i 后不加参数，工具报错
TEST_F(SupGmconvert, DataLog_016_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i", g_toolPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "-i 后不加参数，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "The option(\"-i\") must input 1 parameter(s).");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.gmconvert -o 后不加参数，工具报错
TEST_F(SupGmconvert, DataLog_016_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_alltype.d";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o", g_toolPath, inPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "-o 后不加参数，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "The option(\"-o\") must input 1 parameter(s).");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.gmconvert -o 后加一个不存在的路径，工具报错
TEST_F(SupGmconvert, DataLog_016_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_alltype.d";
    char outPath[256] = "aaaaaa";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "-o 后加一个不存在的路径，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "Output Directory is not exist");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.gmconvert -i 后加一个不存在的路径，工具报错
TEST_F(SupGmconvert, DataLog_016_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "abcdefg/project_alltype.d";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s", g_toolPath, inPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "-i 后加一个不存在的路径，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "Open file error, can not get file size");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.gmconvert -i 后加一个文件夹，工具报错
TEST_F(SupGmconvert, DataLog_016_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s", g_toolPath, inPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "-i 后加一个文件夹，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "Input file is not a datalog file");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.gmconvert -o 后传2个路径，工具报错
TEST_F(SupGmconvert, DataLog_016_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_alltype.d";
    char outPath[256] = "planStrFile";
    char outPath2[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s %s", g_toolPath, inPath, outPath, outPath2);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "-o 后传2个路径，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "The option(\"-o\") must input 1 parameter(s).");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.gmconvert -i 传入一个不规范的.d文件，工具报错
TEST_F(SupGmconvert, DataLog_016_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_errorfile.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "-i 传入一个不规范的.d文件，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "Fail to verify datalog file");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.gmconvert -i 传入一个相同表名的.d文件，工具报错
TEST_F(SupGmconvert, DataLog_016_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_samename.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);

    AW_FUN_Log(LOG_STEP, "传入一个相同表名的.d文件，工具报错");
    system(g_command);
    ret = executeCommand(g_command, "relations(table/resource/function/aggregate) have duplicate name: A1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.gmconvert转化一个timeout表
TEST_F(SupGmconvert, DataLog_016_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/project_timeout.d";
    char outPath[256] = "planStrFile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验timeout1.gmjson
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/timeout1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_timeout1, result));
    free(result);

    // 校验timeout2.gmjson
    result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat planStrFile/timeout2.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_timeout2, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.gmconvert转化一个3M的.d文件
TEST_F(SupGmconvert, DataLog_016_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/3M.d";
    char outPath[256] = "planStrFile";
    AW_FUN_Log(LOG_STEP, "gmconvert转化一个3M的.d文件，成功");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "ls %s", outPath);
    ret = executeCommand(g_command, "ns1_A000.gmjson", "ns1_A500.gmjson", "ns1_A999.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = system("rm -rf planStrFile/*");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.gmconvert转化一个超过3M的.d文件
TEST_F(SupGmconvert, DataLog_016_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "project_file/3MOVER.d";
    char outPath[256] = "planStrFile";
    AW_FUN_Log(LOG_STEP, "gmconvert转化一个超过3M的.d文件，报错");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret =
        executeCommand(g_command, "[ERROR] Open file error, file size 3146842 larger than max size 3145728 Byte.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
