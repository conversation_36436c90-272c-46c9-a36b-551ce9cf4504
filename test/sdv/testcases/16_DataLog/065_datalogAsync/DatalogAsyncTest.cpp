/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name:DatalogAsyncTest.cpp
 * Description: datalog支持异步批写
 * Author: <PERSON><PERSON><PERSON><PERSON> ywx1252574
 * Create: 2024-01-11
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <string>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "t_rd_sn.h"
#include "DatalogRunEnhance.h"
#define MAX_CMD_SIZE 1024

#if defined ENV_EULER
char envCheck[] = "$TEST_HOME/";
char log_path[] = "log/run/rgmserver/rgmserver.log";
#else
char envCheck[] = {'/','\0'};
char log_path[] = "opt/vrpv8/home/<USER>/diag.current.log";
#endif

class datalogAsync : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase()
    {

    }
    static void TearDownTestCase()
    {
        
    }

};
void datalogAsync::SetUp()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void datalogAsync::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
}
class datalogAsync1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase()
    {

    }
    static void TearDownTestCase()
    {
        
    }

};
void datalogAsync1::SetUp()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(0);
}

void datalogAsync1::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0");
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
}
// 001.常规表使用异步接口进行delete操作
TEST_F(datalogAsync, DataLog_065_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile01.d";
    char udfFileName[] = "datalogFiles/dtlfile01_udf.c";
    char libName[] = "datalogFiles/dtlfile01.so";
    char nsName[] = "dtlfile01";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    char indexName[] = "0";
    int32_t data2[3] = {0,5,5};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetIndexKeyName(stmt, indexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &data2[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, dataRev.status);
    AW_MACRO_EXPECT_EQ_INT(1, dataRev.affectRows);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002.function表使用异步接口进行 update操作
TEST_F(datalogAsync, DataLog_065_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile02.d";
    char udfFileName[] = "datalogFiles/dtlfile02_udf.c";
    char libName[] = "datalogFiles/dtlfile02.so";
    char nsName[] = "dtlfile02";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t data[3] = {1,1,1};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    ret = GmcExecuteAsync(stmt, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    EXPECT_EQ(GMERR_OK, ret);

    char indexName[] = "0";
    int32_t data2[3] = {0,5,5};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetIndexKeyName(stmt, indexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &data2[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data2[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = GmcExecuteAsync(stmt, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, dataRev.status);
    AW_MACRO_EXPECT_EQ_INT(1, dataRev.affectRows);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"b\": 15");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003.aggregate表使用异步接口进行结构化单写操作
TEST_F(datalogAsync, DataLog_065_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile03.d";
    char udfFileName[] = "datalogFiles/dtlfile03_udf.c";
    char libName[] = "datalogFiles/dtlfile03.so";
    char nsName[] = "dtlfile03";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int writeCount = 20;
    ThreeInt4StA *Obj_in = (ThreeInt4StA *)malloc(sizeof(ThreeInt4StA) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 1;
        Obj_in[i].dtlReservedCount = 1;
        Obj_in[i].upgradeVersion = 1;
    }
    ret = writeRecordAsync3(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4A_set, false,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync3(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set,false,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"b\": 45","\"b\": 145");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004.aggregate compare表使用异步接口进行结构化批写操作
TEST_F(datalogAsync, DataLog_065_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile04.d";
    char udfFileName[] = "datalogFiles/dtlfile04_udf.c";
    char libName[] = "datalogFiles/dtlfile04.so";
    char nsName[] = "dtlfile04";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    ThreeInt4StA *Obj_in = (ThreeInt4StA *)malloc(sizeof(ThreeInt4StA) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 1;
        Obj_in[i].dtlReservedCount = 1;
        Obj_in[i].upgradeVersion = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecordAsync3(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4A_set, true,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync3(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set,true,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"b\": 45","\"b\": 145");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005.not join表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile05.d";
    char udfFileName[] = "datalogFiles/dtlfile05_udf.c";
    char libName[] = "datalogFiles/dtlfile05.so";
    char nsName[] = "dtlfile05";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,1};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    ret = GmcExecuteAsync(stmt, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    AW_MACRO_EXPECT_EQ_INT(1, dataRev.affectRows);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t data1[3] = {3,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecuteAsync(stmt, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    AW_MACRO_EXPECT_EQ_INT(1, dataRev.affectRows);
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006.状态机函数使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile06.d";
    char udfFileName[] = "datalogFiles/dtlfile06_udf.c";
    char libName[] = "datalogFiles/dtlfile06.so";
    char nsName[] = "dtlfile06";
    char labelName_in[] = "trigger";
    char labelName_out[] = "out";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,1};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "t", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    ret = GmcExecuteAsync(stmt, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    AW_MACRO_EXPECT_EQ_INT(1, dataRev.affectRows);
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007.循环发送数据100次，udf里sleep2秒，中途断连
TEST_F(datalogAsync, DataLog_065_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libName[] = "datalogFiles/dtlfile07.so";
    char nsName[] = "dtlfile07";
    int opNums = 100;
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_CONNECTION_SEND_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < opNums;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        if(ret == GMERR_CONNECTION_SEND_BUFFER_FULL)
        {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "execute");
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "disconnect");
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008.循环（批写随机条数据），验证日志内事务，回调条数
TEST_F(datalogAsync1, DataLog_065_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile08.d";
    char udfFileName[] = "datalogFiles/dtlfile08_udf.c";
    char libName[] = "datalogFiles/dtlfile08.so";
    char nsName[] = "dtlfile08";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    srand(time(0));

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int random = rand()%100+1;

    int32_t data[3] = {1,0,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    char randomin[4] = {0};
    for (int i = 0;i < 5;i++)
    {
        random = rand()%100+1;
        for (int i = 0;i < random;i++)
        {
            ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // set value
            ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &dataRev);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, dataRev.status);
        AW_MACRO_EXPECT_EQ_INT(random, dataRev.totalNum);
        AW_MACRO_EXPECT_EQ_INT(random, dataRev.succNum);
        EXPECT_EQ(GMERR_OK, ret);

        string randomin = to_string(random);
        char checkRet[MAX_CMD_SIZE];
        memset(g_command, 0, sizeof(g_command));
        (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s | grep requestId",&envCheck,&log_path);
        (void)snprintf(checkRet, MAX_CMD_SIZE, "BeforeMerge %s: deltaTable cnt:%s.",labelName_in,randomin.c_str());
        AW_FUN_Log(LOG_STEP, checkRet);
        ret = executeCommand(g_command, checkRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.循环到队列超过3M
TEST_F(datalogAsync, DataLog_065_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile09.d";
    char udfFileName[] = "datalogFiles/dtlfile09_udf.c";
    char libName[] = "datalogFiles/dtlfile09.so";
    char nsName[] = "dtlfile09";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_SEND_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    while(1)
    {
        int i = 0;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        if (ret != GMERR_OK)
        {
            break;
        }
        i++;
    }
    sleep(15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL,ret);
    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.开启显示事务 | 当前只支持加载so到public，此场景无法构造
TEST_F(datalogAsync, DataLog_065_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile10.d";
    char udfFileName[] = "datalogFiles/dtlfile10_udf.c";
    char libName[] = "datalogFiles/dtlfile10.so";
    char nsName[] = "dtlfile10";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AsyncUserDataT datans{0}; 
    memset(&datans, 0, sizeof(AsyncUserDataT));
    const char *nameSpace = "Na15";
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = nameSpace;
    char *nameSpaceUserName = (char *)"abc";
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = nameSpaceUserName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_SERIALIZABLE};  // 悲观可串行化
    ret = GmcCreateNamespaceWithCfgAsync(stmt, &nspCfg, create_namespace_callback, &datans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&datans);
    AW_MACRO_EXPECT_EQ_INT(0, datans.status);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&datans, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, "Na15", use_namespace_callback, &datans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&datans);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, datans.status);

    AsyncUserDataT datacfg{0}; 
    memset(&datacfg, 0, sizeof(AsyncUserDataT));
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    GmcTransStartAsync(conn, &config, trans_start_callback, &datacfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&datacfg);
    AW_MACRO_EXPECT_EQ_INT(0, datacfg.status);
    EXPECT_EQ(GMERR_OK, ret);

    (void)TestUninstallDatalog(nsName);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -f ./datalogFiles/dtlfile10.so -ns Na15");
    ret = executeCommand(g_command, "import_datalog","successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    system("gmimport -c datalog -d dtlfile10 -ns Na15");

    memset(&datans, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(stmt, nameSpace, drop_namespace_callback, &datans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&datans);
    AW_MACRO_EXPECT_EQ_INT(0, datans.status);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011.输入表为部分可更新表使用异步接口进行 insert操作
TEST_F(datalogAsync, DataLog_065_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile11.d";
    char udfFileName[] = "datalogFiles/dtlfile11_udf.c";
    char libName[] = "datalogFiles/dtlfile11.so";
    char nsName[] = "dtlfile11";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 1","\"a\": 2","\"b\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012.输入表为可更新表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile12.d";
    char udfFileName[] = "datalogFiles/dtlfile12_udf.c";
    char libName[] = "datalogFiles/dtlfile12.so";
    char nsName[] = "dtlfile12";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 1","\"a\": 2","\"b\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013.输入表为过期表使用异步接口进行delete操作
TEST_F(datalogAsync, DataLog_065_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile13.d";
    char udfFileName[] = "datalogFiles/dtlfile13_udf.c";
    char libName[] = "datalogFiles/dtlfile13.so";
    char nsName[] = "dtlfile13";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    int64_t timeout = 10000;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &timeout, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 1","\"a\": 2","\"b\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014.输入表为Transient Tuple表使用异步接口进行 insert操作
TEST_F(datalogAsync, DataLog_065_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile14.d";
    char udfFileName[] = "datalogFiles/dtlfile14_udf.c";
    char libName[] = "datalogFiles/dtlfile14.so";
    char nsName[] = "dtlfile14";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 1","\"a\": 2","\"b\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015.输入表为Transient Finish表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile15.d";
    char udfFileName[] = "datalogFiles/dtlfile15_udf.c";
    char libName[] = "datalogFiles/dtlfile15.so";
    char nsName[] = "dtlfile15";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 1","\"a\": 2","\"b\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.一个batch同时操作多张输入表（不同的dml）
TEST_F(datalogAsync, DataLog_065_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile16.d";
    char udfFileName[] = "datalogFiles/dtlfile16_udf.c";
    char libName[] = "datalogFiles/dtlfile16.so";
    char nsName[] = "dtlfile16";
    char labelName_in[] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,2,3,4};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char indexName[] = "0";
    int32_t data2[3] = {0,5,5};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, indexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &data2[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &dataRev);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, dataRev.status);
    AW_MACRO_EXPECT_EQ_INT(2, dataRev.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, dataRev.succNum);
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "\"a\": 2");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    ret = executeCommand(g_command, "\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.同时操作8张表
TEST_F(datalogAsync, DataLog_065_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile17.d";
    char udfFileName[] = "datalogFiles/dtlfile17_udf.c";
    char libName[] = "datalogFiles/dtlfile17.so";
    char nsName[] = "dtlfile17";
    char labelName_in[9] = "inpA";
    char labelName_in1[] = "inpB";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,2,3,4};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0;i < 8;i++)
    {
        (void)snprintf(labelName_in, MAX_CMD_SIZE, "inpA%d", i);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &dataRev);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, dataRev.status);
    AW_MACRO_EXPECT_EQ_INT(8, dataRev.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, dataRev.succNum);
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018.中间表为SeqResource表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile18.d";
    char udfFileName[] = "datalogFiles/dtlfile18_udf.c";
    char libName[] = "datalogFiles/dtlfile18.so";
    char nsName[] = "dtlfile18";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 0","\"a\": 1","\"a\": 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019.中间表为PubsubResource表使用异步接口进行 insert操作
TEST_F(datalogAsync, DataLog_065_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile19.d";
    char udfFileName[] = "datalogFiles/dtlfile19_udf.c";
    char libName[] = "datalogFiles/dtlfile19.so";
    char nsName[] = "dtlfile19";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_in);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.中间表为Transient Tuple表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile20.d";
    char udfFileName[] = "datalogFiles/dtlfile20_udf.c";
    char libName[] = "datalogFiles/dtlfile20.so";
    char nsName[] = "dtlfile20";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021.中间表为Transient Finish表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile21.d";
    char udfFileName[] = "datalogFiles/dtlfile21_udf.c";
    char libName[] = "datalogFiles/dtlfile21.so";
    char nsName[] = "dtlfile21";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command,"\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022.中间表为Transient Field表使用异步接口进行 insert操作
TEST_F(datalogAsync, DataLog_065_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile22.d";
    char udfFileName[] = "datalogFiles/dtlfile22_udf.c";
    char libName[] = "datalogFiles/dtlfile22.so";
    char nsName[] = "dtlfile22";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_in);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023.中间表为状态表使用异步接口进行update操作
TEST_F(datalogAsync, DataLog_065_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile23.d";
    char udfFileName[] = "datalogFiles/dtlfile23_udf.c";
    char libName[] = "datalogFiles/dtlfile23.so";
    char nsName[] = "dtlfile23";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    char indexName[] = "0";
    int32_t data2[3] = {0,5,5};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetIndexKeyName(stmt, indexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &data2[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data2[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = GmcExecuteAsync(stmt, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, dataRev.status);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2","\"a\": 3","\"a\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024.输出表为Pubsub可更新表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile24.d";
    char udfFileName[] = "datalogFiles/dtlfile24_udf.c";
    char libName[] = "datalogFiles/dtlfile24.so";
    char nsName[] = "dtlfile24";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_in);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025.输出表为Pubsub表使用异步接口进行 insert操作
TEST_F(datalogAsync, DataLog_065_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile25.d";
    char udfFileName[] = "datalogFiles/dtlfile25_udf.c";
    char libName[] = "datalogFiles/dtlfile25.so";
    char nsName[] = "dtlfile25";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_DOES_NOT_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_in);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 026.输出表为可更新表使用异步接口进行update操作
TEST_F(datalogAsync, DataLog_065_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile26.d";
    char udfFileName[] = "datalogFiles/dtlfile26_udf.c";
    char libName[] = "datalogFiles/dtlfile26.so";
    char nsName[] = "dtlfile26";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027.输出表为tbm表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile27.d";
    char udfFileName[] = "datalogFiles/dtlfile27_udf.c";
    char libName[] = "datalogFiles/dtlfile27.so";
    char nsName[] = "dtlfile27";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_in);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028.输出表为Extern表使用异步接口进行 insert操作
TEST_F(datalogAsync, DataLog_065_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile28.d";
    char udfFileName[] = "datalogFiles/dtlfile28_udf.c";
    char libName[] = "datalogFiles/dtlfile28.so";
    char nsName[] = "dtlfile28";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("gmimport -c vschema -f ./schemafiles/schema_normal.gmjson");
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 1","\"a\": 2","\"a\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    system("gmddl -c drop -t outA");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 029.输出表为MsgNotify表使用异步接口进行insert操作
TEST_F(datalogAsync, DataLog_065_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile29.d";
    char udfFileName[] = "datalogFiles/dtlfile29_udf.c";
    char libName[] = "datalogFiles/dtlfile29.so";
    char nsName[] = "dtlfile29";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,3};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a0", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_in);
    system(g_command);
    ret = executeCommand(g_command, "\"a0\": 1","\"a0\": 2","\"a0\": 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
void *DMLEXECBATCH(void *args)
{
    char labelName_in[] = "inpA";
    int ret = 0;
    int32_t data[3] = {1,1,1};
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 5;
    // set value
    ThreeInt4StA *Obj_in = (ThreeInt4StA *)malloc(sizeof(ThreeInt4StA) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].c = 1;
        Obj_in[i].dtlReservedCount = 1;
        Obj_in[i].upgradeVersion = 1;
    }
    AW_FUN_Log(LOG_STEP, "inputAbegin------------------------------------------------");
    ret = writeRecordAsync(conn, stmt, labelName_in, Obj_in, writeCount, ThreeInt4A_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *DMLEXECBATCHF(void *args)
{
    char labelName_in[] = "inpA";
    int ret = 0;
    int32_t data[3] = {1,1,1};
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 5;
    // set value
    ThreeInt4StA *Obj_in = (ThreeInt4StA *)malloc(sizeof(ThreeInt4StA) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].c = 1;
        Obj_in[i].dtlReservedCount = 1;
        Obj_in[i].upgradeVersion = 1;
    }
    AW_FUN_Log(LOG_STEP, "inputAbegin------------------------------------------------");
    ret = writeRecordAsyncF(conn, stmt, labelName_in, Obj_in, writeCount, ThreeInt4A_set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *DMLEXEC(void *args)
{
    char labelName_in[] = "inpA";
    int ret = 0;
    int32_t data[3] = {1,1,1};
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 3;
    // set value
    ThreeInt4StA *Obj_in = (ThreeInt4StA *)malloc(sizeof(ThreeInt4StA) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].c = 1;
        Obj_in[i].dtlReservedCount = 1;
        Obj_in[i].upgradeVersion = 1;
    }
    AW_FUN_Log(LOG_STEP, "inputAbegin------------------------------------------------");
    ret = writeRecordAsync(conn, stmt, labelName_in, Obj_in, writeCount, ThreeInt4A_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *LOADSO01(void *args)
{
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -f ./datalogFiles/dtlfile30_1.so");
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1012002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
}
void *LOADSO01S(void *args)
{
    int ret = 0;
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -f ./datalogFiles/dtlfile30_1.so");
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
}
void *UPDATE01(void *args)
{
    system("gmimport -c datalog -upgrade ./datalogFiles/dtlfile31_patchV2.so");
}
void *ROLLBACK01(void *args)
{
    system("gmimport -c datalog -rollback ./datalogFiles/dtlfile31_rollbackV2.so");
}
// 030.普通表使用GmcExecuteBatchAsync接口时并发加载.so文件
TEST_F(datalogAsync, DataLog_065_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile30.d";
    char udfFileName[] = "datalogFiles/dtlfile30_udf.c";
    char libName[] = "datalogFiles/dtlfile30.so";
    char nsName[] = "dtlfile30";
    char nsName1[] = "dtlfile30_1";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXECBATCH, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
#ifdef ENV_EULER
    ret = pthread_create(&client_thr[1], NULL, LOADSO01, NULL);
#else
    ret = pthread_create(&client_thr[1], NULL, LOADSO01S, NULL);
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 3","\"b\": 3","\"c\": 11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName1);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 031.配置为不并发时，普通表使用GmcExecuteAsync接口时并发加载热补丁文件
TEST_F(datalogAsync, DataLog_065_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile31.d";
    char udfFileName[] = "datalogFiles/dtlfile31_udf.c";
    char libName[] = "datalogFiles/dtlfile31.so";
    char nsName[] = "dtlfile31";
    char patchfile[] = "datalogFiles/dtlfile31_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    for (int i =0;i<2;i++){
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, UPDATE01, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&client_thr[1], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"c\": 11","\"c\": 10","\"c\": 12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    sleep(10);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.配置为并发时，普通表使用GmcExecuteBatchAsync接口时并发卸载热补丁文件
TEST_F(datalogAsync, DataLog_065_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile31.d";
    char udfFileName[] = "datalogFiles/dtlfile31_udf.c";
    char libName[] = "datalogFiles/dtlfile31.so";
    char nsName[] = "dtlfile31";
    char patchfile[] = "datalogFiles/dtlfile31_patchV2.so";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmimport -c datalog -upgrade ./datalogFiles/dtlfile31_patchV2.so");

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    for (int i =0;i<3;i++){
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, ROLLBACK01, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = pthread_create(&client_thr[1], NULL, DMLEXECBATCHF, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(1);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s%s | grep GMERR",&envCheck,&log_path);
    ret = executeCommand(g_command, "Lock is not available. lock datalog table in data service.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    sleep(15);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 033.使用GmcExecuteAsync接口并发写同一张表
TEST_F(datalogAsync, DataLog_065_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile33.d";
    char udfFileName[] = "datalogFiles/dtlfile33_udf.c";
    char libName[] = "datalogFiles/dtlfile33.so";
    char nsName[] = "dtlfile33";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[1], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[2], NULL, DMLEXEC, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(5);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2","\"b\": 2","\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 034.使用GmcExecuteBatchAsync接口并发写同一张表，udf里sleep到事务锁超时时间
TEST_F(datalogAsync, DataLog_065_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile34.d";
    char udfFileName[] = "datalogFiles/dtlfile34_udf.c";
    char libName[] = "datalogFiles/dtlfile34.so";
    char nsName[] = "dtlfile34";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, DMLEXECBATCH, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
#ifdef ENV_EULER
    ret = pthread_create(&client_thr[1], NULL, DMLEXECBATCHF, NULL);
#else
    ret = pthread_create(&client_thr[1], NULL, DMLEXECBATCH, NULL);
#endif

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 3","\"b\": 3","\"c\": 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 035.同一个异步连接，并发调用多种DML
TEST_F(datalogAsync, DataLog_065_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFiles/dtlfile35.d";
    char udfFileName[] = "datalogFiles/dtlfile35_udf.c";
    char libName[] = "datalogFiles/dtlfile35.so";
    char nsName[] = "dtlfile35";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outB";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,2,3,4};
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &dataRev;
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    for (int i = 0;i < 3;i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // set value
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[i], sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&dataRev);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char indexName[] = "0";
    int32_t data2[3] = {0,5,5};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, indexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &data2[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &dataRev);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, dataRev.status);
    AW_MACRO_EXPECT_EQ_INT(2, dataRev.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, dataRev.succNum);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(5);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    system(g_command);
    ret = executeCommand(g_command, "\"a\": 2","\"a\": 3","\"a\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
