/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#ifndef __IPOOLTHREAD_H__
#define __IPOOLTHREAD_H__


#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define LABELNAME_MAX_LENGTH 128
char g_labelName[LABELNAME_MAX_LENGTH] = "ip4forward00000";
char g_configJson[128] = "{\"max_record_count\" : 999999, \"isFastReadUncommitted\":0}";


const char *g_namespace = "NamespaceA";
char *g_namespaceUserName = (char *)"abc";

// Vertex 实际拉起线程个数
int g_threadNum = 100;


#define THR_NUM 300
GmcConnT *g_conn_tht[THR_NUM * 2];
GmcStmtT *g_stmt_tht[THR_NUM * 2];
int g_start = 0;
int g_mid1 = 1000;
int g_mid2 = 2000;
int g_end = 3000;

GmcConnT *g_conn_tht_2[THR_NUM * 2];
GmcStmtT *g_stmt_tht_2[THR_NUM * 2];

GmcConnT *g_subConn_tht[THR_NUM * 2];
GmcStmtT *g_subStmt_tht[THR_NUM * 2];

// 打印 AW_FUN_Log(LOG_INFO, " count:%llu ", count);
void PrintTableCount(GmcStmtT *tStmt, const char* tTableName)
{
    uint64_t count = 0;
    int ret = GmcGetVertexCount(tStmt, tTableName, NULL, &count);
    if (ret == GMERR_TRANSACTION_ROLLBACK) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
        return;
    }

    // 006用例，异步批量，悲观事务；
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
        return;
    }

    EXPECT_EQ(ret, GMERR_OK);
    return;
}

void *ThreadVertexDml(void *args)
{
    int connId = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[connId], &g_stmt_tht[connId]);
    EXPECT_EQ(GMERR_OK, res);

    int ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int tInsertStart = g_start + (connId) * ((g_mid1 - g_start) / g_threadNum);
    int tInsertEnd = (connId + 1) * ((g_mid1 - g_start) / g_threadNum);
    AW_FUN_Log(LOG_INFO, ">> thread:%d insert (%d - %d)", connId, tInsertStart, tInsertEnd);
    uint64_t i = 0;
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
            break;
        } else {
        EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
            break;
        } else {
        EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> thread:%d replace ", connId);
    for (i = g_mid1; i < g_mid2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
            break;
        } else {
        EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    for (i = g_mid2; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_merge_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
            break;
        } else {
        EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt_tht[connId]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_tht[connId], &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tFlags = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt_tht[connId], (char *)"flags", &tFlags, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tFlags, i);
        }
    }

    for (i = g_start; i < g_mid1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
            break;
        } else {
        EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    ret = GmcTruncateVertexLabel(g_stmt_tht[connId], g_labelName);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
    EXPECT_EQ(GMERR_OK, ret);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    ret = testGmcDisconnect(g_conn_tht[connId], g_stmt_tht[connId]);
    EXPECT_EQ(ret, GMERR_OK);
    return ((void *)0);
}


void *ThreadVertexBatchDml(void *args)
{
    int connId = *((int *)args);
    int ret = testGmcConnect(&g_conn_tht[connId], &g_stmt_tht[connId]);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int totalNum;
    unsigned int successNum;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int tInsertStart = g_start + (connId) * ((g_mid1 - g_start) / g_threadNum);
    int tInsertEnd = (connId + 1) * ((g_mid1 - g_start) / g_threadNum);
    AW_FUN_Log(LOG_INFO, ">> thread:%d insert (%d - %d)", connId, tInsertStart, tInsertEnd);

    uint64_t i = 0;
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    batch = NULL;
    EXPECT_EQ(tInsertEnd - tInsertStart, totalNum);
    EXPECT_EQ(tInsertEnd - tInsertStart, successNum);

    PrintTableCount(g_stmt_tht[connId], g_labelName);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchDestroy(batch);
        batch = NULL;
        EXPECT_EQ(tInsertEnd - tInsertStart, totalNum);
        EXPECT_EQ(tInsertEnd - tInsertStart, successNum);

        PrintTableCount(g_stmt_tht[connId], g_labelName);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid1; i < g_mid2; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    // DTS2023041408011 用例报12002的原因是获取锁的时候等待超时 高并发场景下，用例报12002属于正常现象
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchDestroy(batch);
        batch = NULL;

        EXPECT_EQ(g_mid2 - g_mid1, totalNum);
        EXPECT_EQ(g_mid2 - g_mid1, successNum);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid2; i < g_end; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    // DTS2023041408011 用例报12002的原因是获取锁的时候等待超时 高并发场景下，用例报12002属于正常现象
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchDestroy(batch);
        batch = NULL;
        EXPECT_EQ(g_end - g_mid2, totalNum);
        EXPECT_EQ(g_end - g_mid2, successNum);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    for (i = g_start; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt_tht[connId]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_tht[connId], &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tFlags = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt_tht[connId], (char *)"flags", &tFlags, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tFlags, i);
        }
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid2; i < g_end; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    // DTS2023041408011 用例报12002的原因是获取锁的时候等待超时 高并发场景下，用例报12002属于正常现象
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchDestroy(batch);
        batch = NULL;
        EXPECT_EQ(g_end - g_mid2, totalNum);
        EXPECT_EQ(g_end - g_mid2, successNum);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    ret = GmcTruncateVertexLabel(g_stmt_tht[connId], g_labelName);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    ret = testGmcDisconnect(g_conn_tht[connId], g_stmt_tht[connId]);
    EXPECT_EQ(ret, GMERR_OK);
    return ((void *)0);
}


int g_dmlTransSuccCount = 0;
void *ThreadVertexDmlTrans(void *args)
{
    int connId = *((int *)args);
    int ret = testGmcConnect(&g_conn_tht[connId], &g_stmt_tht[connId]);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "--- thread:%d.", connId);

    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(g_conn_tht[connId], &config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">> thread:%d insert ", connId);
    uint64_t i = 0;
    for (i = g_start; i < g_mid1; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_TRANSACTION_ROLLBACK || ret == GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> thrad:%d update ", connId);
    for (i = g_start; i < g_mid1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_TRANSACTION_ROLLBACK || ret == GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> thread:%d replace ", connId);
    for (i = g_mid1; i < g_mid2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_TRANSACTION_ROLLBACK || ret == GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> thread:%d merge ", connId);
    for (i = g_mid2; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_merge_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_TRANSACTION_ROLLBACK || ret == GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);
    AW_FUN_Log(LOG_INFO, ">> thread:%d read ", connId);
    for (i = g_start; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_TRANSACTION_ROLLBACK || ret == GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
            break;
        } else {
            EXPECT_EQ(ret, GMERR_OK);
        }
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_tht[connId], &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tFlags = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt_tht[connId], (char *)"flags", &tFlags, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tFlags, i);
        }
    }

    AW_FUN_Log(LOG_INFO, ">> thread:%d delete %d - %d ", connId, g_start, g_end);
    for (i = 0; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[connId]);
        if (ret == GMERR_TRANSACTION_ROLLBACK || ret == GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    // 提交事务
    ret = GmcTransCommit(g_conn_tht[connId]);
    if (ret == GMERR_TRANSACTION_ROLLBACK) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        g_dmlTransSuccCount++;
        EXPECT_EQ(ret, GMERR_OK);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    // 可能返回事务回滚
    ret = GmcTruncateVertexLabel(g_stmt_tht[connId], g_labelName);

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    ret = testGmcDisconnect(g_conn_tht[connId], g_stmt_tht[connId]);
    EXPECT_EQ(ret, GMERR_OK);
    return ((void *)0);
}

void *ThreadVertexDmlAsync(void *args)
{
    int connId = *((int *)args);
    int ret = 0;

    // 申请同步stmt，读操作
    ret = testGmcConnect(&g_conn_tht_2[connId], &g_stmt_tht_2[connId]);
    EXPECT_EQ(GMERR_OK, ret);

    // 单个客户端限制异步连接最多128个
    ret = testGmcConnect(&g_conn_tht[connId], &g_stmt_tht[connId], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};

    // 并发affectRows可能多个，可能0个，不做判断  EXPECT_GE(data.affectRows, 1);
    int tInsertStart = g_start + (connId) * ((g_mid1 - g_start) / g_threadNum);
    int tInsertEnd = (connId + 1) * ((g_mid1 - g_start) / g_threadNum);
    AW_FUN_Log(LOG_INFO, ">> thread:%d insert (%d - %d)", connId, tInsertStart, tInsertEnd);
    uint64_t i = 0;
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        memset(&data, 0, sizeof(AsyncUserDataT));
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_tht[connId], &insertRequestCtx);
        EXPECT_EQ(ret, GMERR_OK);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(GMERR_OK, data.status);
        }
    }

    uint64_t count;
    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> thrad:%d update ", connId);
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        memset(&data, 0, sizeof(AsyncUserDataT));
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_tht[connId], &updateRequestCtx);
        EXPECT_EQ(ret, GMERR_OK);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(GMERR_OK, data.status);
        }
    }

    for (i = g_mid1; i < g_mid2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        memset(&data, 0, sizeof(AsyncUserDataT));
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_tht[connId], &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(GMERR_OK, data.status);
        }
    }

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    for (i = g_mid2; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_merge_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        memset(&data, 0, sizeof(AsyncUserDataT));
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_tht[connId], &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(GMERR_OK, data.status);
        }
    }

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    for (i = g_start; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht_2[connId], g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht_2[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht_2[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt_tht_2[connId]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_tht_2[connId], &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tFlags = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt_tht_2[connId], (char *)"flags", &tFlags,
                sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tFlags, i);
        }
    }

    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        memset(&data, 0, sizeof(AsyncUserDataT));
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_tht[connId], &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
            EXPECT_EQ(GMERR_OK, GMERR_OK);
        } else {
            EXPECT_EQ(GMERR_OK, data.status);
        }
    }

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = GmcTruncateVertexLabel(g_stmt_tht_2[connId], g_labelName);
    if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcDisconnect(g_conn_tht[connId], g_stmt_tht[connId]);
    EXPECT_EQ(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn_tht_2[connId], g_stmt_tht_2[connId]);
    EXPECT_EQ(ret, GMERR_OK);

    return ((void *)0);
}

int g_succNum1 = 0;
int g_succNum2 = 0;
int g_succNum3 = 0;
int g_succNum4 = 0;
int g_succNum5 = 0;

void *ThreadVertexDmlAsyncBatch(void *args)
{
    int connId = *((int *)args);
    int ret = 0;

    // 申请同步stmt，读操作
    ret = testGmcConnect(&g_conn_tht_2[connId], &g_stmt_tht_2[connId]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_tht[connId], &g_stmt_tht[connId], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
 
    AsyncUserDataT data = {0};

    unsigned int totalNum;
    unsigned int successNum;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int tInsertStart = g_start + (connId) * ((g_mid1 - g_start) / g_threadNum);
    int tInsertEnd = (connId + 1) * ((g_mid1 - g_start) / g_threadNum);
    AW_FUN_Log(LOG_INFO, ">> thread:%d insert (%d - %d)", connId, tInsertStart, tInsertEnd);

    uint64_t i = 0;
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, data.status);

        EXPECT_EQ(tInsertEnd - tInsertStart, data.totalNum);
        EXPECT_EQ(tInsertEnd - tInsertStart, data.succNum);
        g_succNum1++;
    }
    GmcBatchDestroy(batch);
    batch = NULL;

    uint64_t count;
    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, data.status);

        EXPECT_EQ(tInsertEnd - tInsertStart, data.totalNum);
        EXPECT_EQ(tInsertEnd - tInsertStart, data.succNum);
        g_succNum2++;
    }
    GmcBatchDestroy(batch);
    batch = NULL;

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid1; i < g_mid2; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, data.status);

        EXPECT_EQ(g_mid2 - g_mid1, data.totalNum);
        EXPECT_EQ(g_mid2 - g_mid1, data.succNum);
        g_succNum3++;
    }

    GmcBatchDestroy(batch);
    batch = NULL;

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid2; i < g_end; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    if (data.status == GMERR_LOCK_NOT_AVAILABLE || data.status == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, data.status);

        EXPECT_EQ(g_end - g_mid2, data.totalNum);
        EXPECT_EQ(g_end - g_mid2, data.succNum);
        g_succNum4++;
    }
    GmcBatchDestroy(batch);
    batch = NULL;
    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    for (i = g_start; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht_2[connId], g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht_2[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht_2[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt_tht_2[connId]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_tht_2[connId], &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tFlags = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt_tht_2[connId], (char *)"flags",
                &tFlags, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tFlags, i);
        }
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid2; i < g_end; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    batch = NULL;
    if (data.status == GMERR_LOCK_NOT_AVAILABLE  || data.status == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, data.status);

        EXPECT_EQ(g_end - g_mid2, data.totalNum);
        EXPECT_EQ(g_end - g_mid2, data.succNum);
        g_succNum5++;
    }

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = GmcTruncateVertexLabel(g_stmt_tht_2[connId], g_labelName);
    if (data.status == GMERR_LOCK_NOT_AVAILABLE  || data.status == GMERR_REQUEST_TIME_OUT) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcDisconnect(g_conn_tht[connId], g_stmt_tht[connId]);
    EXPECT_EQ(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn_tht_2[connId], g_stmt_tht_2[connId]);
    EXPECT_EQ(ret, GMERR_OK);

    return ((void *)0);
}


void *ThreadVertexDmlAsyncBatchTrans(void *args)
{
    int connId = *((int *)args);
    int ret = 0;

    // 申请同步stmt，读操作
    ret = testGmcConnect(&g_conn_tht_2[connId], &g_stmt_tht_2[connId]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_tht[connId], &g_stmt_tht[connId], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};

    // 开启一个事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStartAsync(g_conn_tht[connId], &config, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    unsigned int totalNum;
    unsigned int successNum;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int tInsertStart = g_start + (connId) * ((g_mid1 - g_start) / g_threadNum);
    int tInsertEnd = (connId + 1) * ((g_mid1 - g_start) / g_threadNum);
    AW_FUN_Log(LOG_INFO, ">> thread:%d insert (%d - %d)", connId, tInsertStart, tInsertEnd);

    uint64_t i = 0;
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    batch = NULL;
    if (GMERR_LOCK_NOT_AVAILABLE != data.status) {
        EXPECT_EQ(GMERR_OK, data.status);
    } else {
        EXPECT_EQ(tInsertEnd - tInsertStart, data.totalNum);
        EXPECT_EQ(tInsertEnd - tInsertStart, data.succNum);
    }

    uint64_t count;
    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    batch = NULL;
    EXPECT_EQ(tInsertEnd - tInsertStart, data.totalNum);
    EXPECT_EQ(tInsertEnd - tInsertStart, data.succNum);

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid1; i < g_mid2; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    if (data.status == GMERR_TRANSACTION_ROLLBACK || data.status == GMERR_LOCK_NOT_AVAILABLE) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(data.status, GMERR_OK);
        EXPECT_EQ(g_mid2 - g_mid1, data.totalNum);
        EXPECT_EQ(g_mid2 - g_mid1, data.succNum);
    }
    GmcBatchDestroy(batch);
    batch = NULL;

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid2; i < g_end; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    if (data.status == GMERR_TRANSACTION_ROLLBACK || data.status == GMERR_LOCK_NOT_AVAILABLE) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(data.status, GMERR_OK);
        EXPECT_EQ(g_end - g_mid2, data.totalNum);
        EXPECT_EQ(g_end - g_mid2, data.succNum);
    }
    GmcBatchDestroy(batch);
    batch = NULL;

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    for (i = g_start; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht_2[connId], g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht_2[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht_2[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt_tht_2[connId]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_tht_2[connId], &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tFlags = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt_tht_2[connId], (char *)"flags",
                &tFlags, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tFlags, i);
        }
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_tht[connId], &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_mid2; i < g_end; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    if (data.status == GMERR_TRANSACTION_ROLLBACK || data.status == GMERR_LOCK_NOT_AVAILABLE) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        EXPECT_EQ(data.status, GMERR_OK);
        EXPECT_EQ(g_end - g_mid2, data.totalNum);
        EXPECT_EQ(g_end - g_mid2, data.succNum);
    }
    GmcBatchDestroy(batch);
    batch = NULL;

    // 事务提交
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcTransCommitAsync(g_conn_tht[connId], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_TRANSACTION_ROLLBACK || data.status == GMERR_LOCK_NOT_AVAILABLE) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        g_dmlTransSuccCount++;
        EXPECT_EQ(ret, GMERR_OK);
    }
    memset(&data, 0, sizeof(AsyncUserDataT));

    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    // 可能返回需要回滚
    ret = GmcTruncateVertexLabel(g_stmt_tht_2[connId], g_labelName);
    PrintTableCount(g_stmt_tht_2[connId], g_labelName);

    ret = testGmcDisconnect(g_conn_tht[connId], g_stmt_tht[connId]);
    EXPECT_EQ(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn_tht_2[connId], g_stmt_tht_2[connId]);
    EXPECT_EQ(ret, GMERR_OK);

    return ((void *)0);
}


void *ThreadVertexDmlOtimicTrans(void *args)
{
    int connId = *((int *)args);
    int ret = testGmcConnect(&g_conn_tht[connId], &g_stmt_tht[connId]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt_tht[connId], g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn_tht[connId], &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "--- thread:%d.", connId);
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int tInsertStart = g_start + (connId) * ((g_mid1 - g_start) / g_threadNum);
    int tInsertEnd = (connId + 1) * ((g_mid1 - g_start) / g_threadNum);
    AW_FUN_Log(LOG_INFO, ">> thread:%d insert (%d - %d)", connId, tInsertStart, tInsertEnd);

    uint64_t i = 0;
    for (i = tInsertStart; i < tInsertEnd; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> thread:%d replace ", connId);
    for (i = g_mid1; i < g_mid2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> thrad:%d update ", connId);
    for (i = g_mid1; i < g_mid2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1 * connId;

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        ret = GmcSetVertexProperty(
            g_stmt_tht[connId], "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    AW_FUN_Log(LOG_INFO, ">> thread:%d merge ", connId);
    for (i = g_mid2; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_merge_obj(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> thread:%d read ", connId);
    for (i = g_start; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(g_stmt_tht[connId]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_tht[connId], &isFinish);
            if (ret) {
                AW_FUN_Log(LOG_ERROR, "GmcFetch index:%u .", i);
                break;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t tFlags = 0;
            bool isNull = 0;
            ret = GmcGetVertexPropertyByName(g_stmt_tht[connId], (char *)"flags", &tFlags, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName flags.");
            AW_MACRO_EXPECT_EQ_INT(tFlags, i);
        }
    }

    AW_FUN_Log(LOG_INFO, ">> thread:%d delete ", connId);
    for (i = g_start; i < g_end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[connId], g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[connId], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[connId], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[connId]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcTransCommit(g_conn_tht[connId]);
    if (ret == GMERR_RESTRICT_VIOLATION) {
        EXPECT_EQ(GMERR_OK, GMERR_OK);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_dmlTransSuccCount++;
    }

    PrintTableCount(g_stmt_tht[connId], g_labelName);

    AW_FUN_Log(LOG_INFO, ">> truncate ");
    ret = GmcTruncateVertexLabel(g_stmt_tht[connId], g_labelName);

    PrintTableCount(g_stmt_tht[connId], g_labelName);
    return ((void *)0);
}


SnUserDataT g_userData;
void *ThreadVertexSub(void *args)
{
    int connId = *((int *)args);

    int ret = 0;

    // 申请同步stmt，读操作
    ret = testGmcConnect(&g_conn_tht_2[connId], &g_stmt_tht_2[connId]);
    EXPECT_EQ(GMERR_OK, ret);

    char subConnName[20] = {0};
    (void)(void)snprintf(subConnName, sizeof(subConnName), "subConn_%d", connId);

    // 创建订阅连接
    int chanRingLen = 256;
    ConnOptionT *connOption = NULL;

    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, 0, 0, 0, NULL, NULL,
        NULL, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubConnect(&g_subConn_tht[connId], &g_subStmt_tht[connId], 1, g_epoll_reg_info,
        (char *)subConnName, &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    free(connOption);
    connOption = NULL;

     // 订阅关系1
    char subName[36] = {0};
    char subTxt[1024] = {0};

    memset(subName, 0, sizeof(subName));
    (void)snprintf(subName, 36, "%s_sub_%d", (char *)g_labelName, connId);
    AW_FUN_Log(LOG_INFO, "--- sub thread:%d subName:%s.", connId, (char *)subName);

    memset(subTxt, 0, sizeof(subTxt));
    (void)snprintf(subTxt, 1024, "{ \"name\":\"%s_sub_%d\", ", (char *)g_labelName, connId);
    int len = strlen(subTxt);
    (void)snprintf(subTxt + len, 1024 - len,
        "\"label_name\":\"%s\", \"type\":\"before_commit\", \"events\": [  "
        "{\"type\":\"insert\", \"msgTypes\":[\"new object\", \"old object\"]}, "
        "{\"type\":\"update\", \"msgTypes\":[\"new object\", \"old object\"]}, "
        "{\"type\":\"delete\", \"msgTypes\":[\"new object\", \"old object\"]}, "
        "{\"type\":\"replace insert\", \"msgTypes\":[\"new object\", \"old object\"]}, "
        "{\"type\":\"replace update\", \"msgTypes\":[\"new object\", \"old object\"]}, "
        "{\"type\":\"merge insert\", \"msgTypes\":[\"new object\", \"old object\"]}, "
        "{\"type\":\"merge update\", \"msgTypes\":[\"new object\", \"old object\"]} "
        " ], \"is_path\":false, \"retry\":true }",
        (char *)g_labelName);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = subName;
    tmp_g_sub_info.configJson = subTxt;
    ret = GmcSubscribe(g_stmt_tht_2[connId], &tmp_g_sub_info, g_subConn_tht[connId], sn_callback_wait, &g_userData);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);

    // 有可能推送不满 15*(g_mid2 - g_mid1)，期待>14*(g_mid2 - g_mid1)
    ret = myTestWaitSnRecv(GMC_SUB_EVENT_REPLACE_INSERT, 14*(g_mid2 - g_mid1));
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "sub %u insert: %d update:%d delete:%d, replaceinsert:%d \n", g_subTriggerCnt,
        g_sub_insertNum, g_sub_updateNum, g_sub_deleteNum,  g_sub_replaceInsertNum);

    ret = GmcUnSubscribe(g_stmt_tht_2[connId], subName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_tht[connId], g_stmt_tht[connId]);
    EXPECT_EQ(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn_tht_2[connId], g_stmt_tht_2[connId]);
    EXPECT_EQ(ret, GMERR_OK);

    return ((void *)0);
}

#endif
