
extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/mman.h>
#include "gtest/gtest.h"
#include "tPoolTest.h"

#define MAX_VERTEX_NUM 10000
 

AsyncUserDataT data = {0};

#define LABELNAME_MAX_LENGTH 128

char *schema_json = NULL;
char g_labelName[LABELNAME_MAX_LENGTH] = "ip4forward00000";
char g_labelNameTree[LABELNAME_MAX_LENGTH] = "Tree_Vector";
 
const char *g_namespace = "NamespaceA";
char *g_namespaceUserName = (char *)"abc";

char g_configJson[128] = "{\"max_record_count\" : 999999, \"isFastReadUncommitted\":0}";
 
class tSglVtxConn : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tSglVtxConn::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=3\"");

    system("sh $TEST_HOME/tools/start.sh");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tSglVtxConn::TearDownTestCase()
{
    int ret;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void tSglVtxConn::SetUp()
{
    system("rm -rf \"../../../log/run/rgmserver/rgmserver.log\"");
    AW_CHECK_LOG_BEGIN();
}

void tSglVtxConn::TearDown()
{
    AW_CHECK_LOG_END();
}

// 实际拉起线程个数
#define TEST_THR_NUM 200

#define THR_NUM (TEST_THR_NUM/2 + 10)
GmcConnT *g_conn_tht[THR_NUM * 2];
GmcStmtT *g_stmt_tht[THR_NUM * 2];
 

GmcConnT *g_conn_tht_2[THR_NUM * 2];
GmcStmtT *g_stmt_tht_2[THR_NUM * 2];

GmcConnT *g_subConn_tht[THR_NUM * 2];
GmcStmtT *g_subStmt_tht[THR_NUM * 2];


#define MAX_CONN MAX_ASYNC_CONN_SIZE_PER_PRO
int g_succCon = 0;
int g_succDis = 0;

// 008.创建1024个异步连接
TEST_F(tSglVtxConn, Other_050_003_008)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    g_succCon = 0;
    g_succDis = 0;

    GmcConnT *conn_t[MAX_CONN + 1] = {0};
    GmcStmtT *stmt_t[MAX_CONN + 1] = {0};
    int i;
    int value = 0;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    value = existConnNum;
#elif defined(ENV_RTOSV2)  // AC环境预留的2个逃生通道普通用户无法使用，报18004
    value = 2;
#endif
#ifdef ENV_SUSE
    value += 1;
#endif
    AW_FUN_Log(LOG_INFO, "Create %d conn", MAX_CONN - value);
    for (i = 0; i < MAX_CONN - value; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!ret) {
            g_succCon++;
        }
    }

    ret = testGmcConnect(&conn_t[MAX_CONN], &stmt_t[MAX_CONN], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_NE(GMERR_OK, ret);

    for (i = 0; i < MAX_CONN - value; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!ret) {
            g_succDis++;
        }
    }

    AW_FUN_Log(LOG_INFO, "create conn:%d dsiconn:%d", g_succCon, g_succDis);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TOO_MANY_CONNECTIONS);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

GmcConnT *g_conn_t[MAX_CONN] = {0};
GmcStmtT *g_stmt_t[MAX_CONN] = {0};
void *thread_client_connect_2(void *args)
{
    int ret = 0;
    int index = *((int *)args);
    ret = testGmcConnect(&g_conn_t[index], &g_stmt_t[index], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!ret) {
            g_succCon++;
        }
    return ((void *)0);
}

// 009.建立1个异步连接并断开，启动1024个线程进行连接，完成后再建立1个连接
TEST_F(tSglVtxConn, Other_050_003_009)
{
    int ret = 0;
    g_succCon = 0;
    g_succDis = 0;

    pthread_t thr_arr[MAX_CONN];
    void *thr_ret[MAX_CONN];
    int i;
    uint32_t existConnNum = 0;
#if defined(ENV_RTOSV2X)
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
#ifdef ENV_SUSE
    existConnNum += 1;
#endif
    AW_FUN_Log(LOG_INFO, "Create %d thread to create conn", MAX_CONN - existConnNum);
    int index[MAX_CONN] = {0};
    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_client_connect_2, (void *)&index[i]);
    }
    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    for (i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcDisconnect(g_conn_t[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!ret) {
            g_succDis++;
        }
    }

    AW_FUN_Log(LOG_STEP, "create conn:%d dsiconn:%d", g_succCon, g_succDis);
}


// 010.创建1024个异步连接，再次建连失败，断开1个连接，再次建连
TEST_F(tSglVtxConn, Other_050_003_010)
{
    GmcConnT *conn_t[MAX_CONN + 2] = {0}, *conn = NULL;
    int ret = 0;
    g_succCon = 0;
    g_succDis = 0;

    int i;
    int value = 0;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    value = existConnNum;
#endif
#ifdef ENV_SUSE
    value += 1;
#endif
    AW_FUN_Log(LOG_INFO, "Create %d conn", MAX_CONN - value);
    for (i = 0; i < MAX_CONN - value; i++) {
        ret = testGmcConnect(&conn_t[i], NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!ret) {
            g_succCon++;
        }
    }

    ret = testGmcConnect(&conn, NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_NE(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_t[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000000);

    ret = testGmcConnect(&conn_t[MAX_CONN], NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn_t[MAX_CONN + 1], NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_NE(GMERR_OK, ret);

    for (i = 1; i < MAX_CONN - value; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn_t[MAX_CONN]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TOO_MANY_CONNECTIONS);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}


// 11 创建1024个异步连接，断开1024个异步连接，重复多次
TEST_F(tSglVtxConn, Other_050_003_011)
{
    GmcConnT *conn_t[MAX_CONN + 1] = {0};
    int ret = 0;
    int i, j;
    int value = 0;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    value = existConnNum;
#endif
#ifdef ENV_SUSE
    value += 1;
#endif
    AW_FUN_Log(LOG_INFO, "Create %d thread to create conn", MAX_CONN - value);
    system("sh connect_top.sh");
    for (j = 0; j < 100; j++) {
        AW_FUN_Log(LOG_INFO, ">>> test %d\n", j);
        for (i = 0; i < MAX_CONN - value; i++) {
            ret = testGmcConnect(&conn_t[i], NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        for (i = 0; i < MAX_CONN - value; i++) {
            ret = testGmcDisconnect(conn_t[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        system("sh connect_top.sh");
        system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=dynaMemCtx|grep -w GLOBAL_ALLOC_SIZE ");
        system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=dynaMemCtx|grep -w TOTAL_ALLOC_SIZE ");
        system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD |grep SERVER_DYM ");
        system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD |grep SERVER_SHM ");
        memset(conn_t, 0, sizeof(conn_t));
    }
}

// 12 异步连接达到上限后，连接逃生客户端，查看连接情况
TEST_F(tSglVtxConn, Other_050_003_012)
{
    int ret = 0;
    int i;
    g_succCon = 0;
    GmcConnT *conn_t[MAX_CONN] = {0};
    int value = 0;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    value = existConnNum;
#endif
#ifdef ENV_SUSE
    value += 1;
#endif
    for (i = 0; i < MAX_CONN - value; i++) {
        ret = testGmcConnect(&conn_t[i], NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
       if (!ret) {
            g_succCon++;
        }
    }

    AW_FUN_Log(LOG_INFO, ">>> Create %d conn", g_succCon);

    for (i = 0; i < MAX_CONN - value; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

struct AllTaskResult {
    int number;
    sem_t sem_id;
};

static AllTaskResult *g_taskRst = 0;
GmcConnT *g_conn[MAX_CONN * 2];

class tPoolVtxConnProcess : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void *ThreadStartProcess1(void *args)
{
    int ret = system("./tSglThreAsyCn --gtest_also_run_disabled_tests "
                     "--gtest_filter=tPoolVtxConnProcess.DISABLED_Process_1 > process_1.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 单起进程建立连接
TEST_F(tPoolVtxConnProcess, DISABLED_Process_1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    g_needCheckWhenSucc = false;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_conn, 0, sizeof(void *) * MAX_CONN * 2);

    int value = GetValueFromFile("process.flag");
    AW_FUN_Log(LOG_INFO, ">> start Process_1 value:%d", value);

    free(g_epollData.events);
    memset(&g_epollData, 0, sizeof(g_epollData));
    free(g_timeoutEpollData.events);
    memset(&g_timeoutEpollData, 0, sizeof(g_timeoutEpollData));
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    int i;
    for (i = 0; i < (MAX_CONN - value) / 2; i++) {
        int ret = testGmcConnect(&g_conn[i], NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "pid1 connect success: %d", i);
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat flagFile/flag_true.txt >> process_1.flag");
    AW_FUN_Log(LOG_STEP, "END");
}

void *ThreadStartProcess2(void *args)
{
    int ret = system("./tSglThreAsyCn --gtest_also_run_disabled_tests "
                     "--gtest_filter=tPoolVtxConnProcess.DISABLED_Process_2 >process_2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 单起进程建立连接
TEST_F(tPoolVtxConnProcess, DISABLED_Process_2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    g_needCheckWhenSucc = false;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_conn, 0, sizeof(void *) * MAX_CONN * 2);

    int value = GetValueFromFile("process.flag");
    AW_FUN_Log(LOG_INFO, ">> start Process_2 value:%d", value);

    free(g_epollData.events);
    memset(&g_epollData, 0, sizeof(g_epollData));
    free(g_timeoutEpollData.events);
    memset(&g_timeoutEpollData, 0, sizeof(g_timeoutEpollData));
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建连接
    int i;
#if defined(ENV_RTOSV2X)
    for (i = (MAX_CONN - value) / 2; i < MAX_CONN - value; i++) {
        int ret = testGmcConnect(&g_conn[i], NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "pid2 connect success: %d", i);
    }
    for (i = (MAX_CONN - value) / 2; i < MAX_CONN - value; i++) {
        ret = testGmcDisconnect(g_conn[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
#else
    for (i = MAX_CONN / 2; i < MAX_CONN / 2 + MAX_CONN; i++) {
        int ret = testGmcConnect(&g_conn[i], NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "pid2 connect success: %d", i);
    }
    for (i = MAX_CONN / 2; i < MAX_CONN / 2 + MAX_CONN; i++) {
        ret = testGmcDisconnect(g_conn[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
#endif
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END");
}

// 13. 两个进程分别建立异步连接，第1个进程退出，第二个进程建立1024个连接，验证进程1资源是否释放
TEST_F(tSglVtxConn, Other_050_003_013) 
{
    AW_CHECK_LOG_BEGIN(0);

    int ret = 0;
    memset(g_conn, 0, sizeof(void *) * MAX_CONN * 2);

    int value = 0;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    value = existConnNum;
#endif
#ifdef ENV_SUSE
    value += 1;
#endif
    system("rm -rf process.flag");
    ret = WriteFile("process.flag", value);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">> create max_conn:%d value:%d", MAX_CONN, value);

    system("rm -rf process_1.flag");
    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, ThreadStartProcess1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);

    AW_FUN_Log(LOG_STEP, "等待客户端进程1结束");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat process_1.flag");
    ret = executeCommand(g_command, "true");
    int cnt = 0;
    while (ret != GMERR_OK && cnt < 100) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "开启客户端进程2");
    ret = pthread_create(&thrArr[1], NULL, ThreadStartProcess2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[1], NULL);


    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
}

