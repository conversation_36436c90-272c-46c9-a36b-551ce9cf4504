/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 057_PessimisiticRR
 * Author: hanyang
 * Create: 2023-9-13
 */
#include "PessRR.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn1 = NULL;
GmcStmtT *g_stmt1 = NULL;
GmcConnT *g_conn2 = NULL;
GmcStmtT *g_stmt2 = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

class PessimisiticRR : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void PessimisiticRR::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void PessimisiticRR::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void PessimisiticRR::SetUp()
{
    int ret;

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);

    TestCreateNamespace(g_stmt, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt, g_namespace);
    TestUseNamespace(g_stmt1, g_namespace);
    TestUseNamespace(g_stmt2, g_namespace);

    AsyncUserDataT asyncData;
    memset(&asyncData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 创建Vertex
    TestCreateLabelTrx(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void PessimisiticRR::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    // 删除namespace
    TestDropNamespace(g_stmt, g_namespace);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn1 = NULL;
    g_stmt1 = NULL;

    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn2 = NULL;
    g_stmt2 = NULL;

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/*****************************************************************************
 Description  : 001.无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 insert数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 002.无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 update数据1--增量数据，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 update, 不冲突数据
    initValue = 2;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 冲突数据
    initValue = 0;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 003.无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 merge数据1--增量数据，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 merge, 不冲突数据
    initValue = 2;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 merge, 冲突数据
    initValue = 0;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 004.无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 replace数据1--增量数据，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 replace, 不冲突数据
    initValue = 2;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 replace, 冲突数据
    initValue = 0;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 005.无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 delete数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 Delete, 不冲突数据
    initValue = 2;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 冲突数据
    initValue = 0;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 006.无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 insert数据1，报错主键冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_PRIMARY_KEY_VIOLATION, 0);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 007.无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 update数据1--增量数据，不报错，affectedRows=0，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 不冲突数据
    initValue = 2;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 冲突数据
    initValue = 0;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 008.无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 merge数据1--增量数据，报错主键冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 merge, 不冲突数据
    initValue = 2;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 2);
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt1, "Vertex_01", expectNum, "Vertex_pk", 0);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt, "Vertex_01", expectNum, "Vertex_pk", 0);

    // 事务2 merge, 冲突数据
    initValue = 0;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_PRIMARY_KEY_VIOLATION, 0);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 009.无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 replace数据1--增量数据，报错主键冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 replace, 不冲突数据
    initValue = 2;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 2);
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt1, "Vertex_01", expectNum, "Vertex_pk", 0);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt, "Vertex_01", expectNum, "Vertex_pk", 0);

    // 事务2 replace, 冲突数据
    initValue = 0;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_PRIMARY_KEY_VIOLATION, 0);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 010.无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 delete数据1，不报错，affectedRows=0，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 不冲突数据
    initValue = 2;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 冲突数据
    initValue = 0;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 011.有初始数据1，依次启动两个事务，事务1 update数据1，未提交，
                事务2 insert数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 012.有初始数据1，依次启动两个事务，事务1 update数据1，未提交，
                事务2 update数据1，锁冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 update, 不冲突数据
    initValue = 2;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 冲突数据
    initValue = 0;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 013.有初始数据1，依次启动两个事务，事务1 update数据1，未提交，
                事务2 merge数据1，锁冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 merge, 不冲突数据
    initValue = 2;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 merge, 冲突数据
    initValue = 0;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 014.有初始数据1，依次启动两个事务，事务1 update数据1，未提交，
                事务2 replace数据1，锁冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 replace, 不冲突数据
    initValue = 2;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 replace, 冲突数据
    initValue = 0;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 015.有初始数据1，依次启动两个事务，事务1 update数据1，未提交，
                事务2 delete数据1，锁冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 Delete, 不冲突数据
    initValue = 2;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 冲突数据
    initValue = 0;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 016.有初始数据1，依次启动两个事务，事务1 update数据1，事务1提交，
                事务2 insert数据1，报错主键冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_PRIMARY_KEY_VIOLATION, 0);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 017.有初始数据1，依次启动两个事务，事务1 update数据1，事务1提交，
                事务2 update数据1，事务冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 不冲突数据
    initValue = 2;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 冲突数据
    initValue = 0;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 018.有初始数据1，依次启动两个事务，事务1 update数据1，事务1提交，
                事务2 merge数据1，事务冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 merge, 不冲突数据
    initValue = 2;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 0);
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 2);
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt1, "Vertex_01", expectNum, "Vertex_pk", 0);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt, "Vertex_01", expectNum, "Vertex_pk", 0);

    // 事务2 merge, 冲突数据
    initValue = 0;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 019.有初始数据1，依次启动两个事务，事务1 update数据1，事务1提交，
                事务2 replace数据1，事务冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 replace, 不冲突数据
    initValue = 2;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 0);
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 2);
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt1, "Vertex_01", expectNum, "Vertex_pk", 0);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt, "Vertex_01", expectNum, "Vertex_pk", 0);

    // 事务2 replace, 冲突数据
    initValue = 0;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 020.有初始数据1，依次启动两个事务，事务1 update数据1，事务1提交，
                事务2 delete数据1，事务冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 不冲突数据
    initValue = 2;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 冲突数据
    initValue = 0;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 021.有初始数据1，依次启动两个事务，事务1 delete数据1，未提交，
                事务2 insert数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 022.有初始数据1，依次启动两个事务，事务1 delete数据1，未提交，
                事务2 update数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 不冲突数据
    initValue = 2;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 冲突数据
    initValue = 0;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 023.有初始数据1，依次启动两个事务，事务1 delete数据1，未提交，
                事务2 merge数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 merge, 不冲突数据
    initValue = 2;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 merge, 冲突数据
    initValue = 0;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 024.有初始数据1，依次启动两个事务，事务1 delete数据1，未提交，
                事务2 replace数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 replace, 不冲突数据
    initValue = 2;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 replace, 冲突数据
    initValue = 0;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 025.有初始数据1，依次启动两个事务，事务1 delete数据1，未提交，
                事务2 delete数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 不冲突数据
    initValue = 2;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 冲突数据
    initValue = 0;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 026.有初始数据1，依次启动两个事务，事务1 delete数据1，事务1提交，
                事务2 insert数据1，报错主键冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_PRIMARY_KEY_VIOLATION, 0);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 027.有初始数据1，依次启动两个事务，事务1 delete数据1，事务1提交，
                事务2 update数据1，事务冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 不冲突数据
    initValue = 2;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 update, 冲突数据
    initValue = 0;
    TestUpdateVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 028.有初始数据1，依次启动两个事务，事务1 delete数据1，事务1提交，
                事务2 merge数据1，事务冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 merge, 不冲突数据
    initValue = 2;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 0);
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 2);
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 merge, 冲突数据
    initValue = 0;
    TestMergeVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 029.有初始数据1，依次启动两个事务，事务1 delete数据1，事务1提交，
                事务2 replace数据1，事务冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 replace, 不冲突数据
    initValue = 2;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 1);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 0);
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_pk", 2);
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 replace, 冲突数据
    initValue = 0;
    TestReplaceVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 030.有初始数据1，依次启动两个事务，事务1 delete数据1，事务1提交，
                事务2 delete数据1，事务冲突报错，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 Delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 不冲突数据
    initValue = 2;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 0);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 Delete, 冲突数据
    initValue = 0;
    TestDeleteVertexLabel(g_stmt2, "Vertex_pk", times, initValue, "Vertex_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 031.有初始数据1，依次启动两个事务，事务1 insert数据2，
                事务2 读取数据，只能读到初始数据1，事务1提交，事务2 读取数据，只能读到初始数据1
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    initValue = 1;
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 032.有初始数据1，依次启动两个事务，事务1 update数据1，
                事务2 读取数据，只能读到初始数据1，事务1提交，事务2 读取数据，只能读到初始数据1
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 6;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update
    TestUpdateVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(6, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(6, getValue);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2数值改变
    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);
}

/*****************************************************************************
 Description  : 033.有初始数据1，依次启动两个事务，事务1 merge数据1，
                事务2 读取数据，只能读到初始数据1，事务1提交，事务2 读取数据，只能读到初始数据1
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 6;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 merge
    TestMergeVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 2);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(6, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(6, getValue);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2数值改变
    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);
}

/*****************************************************************************
 Description  : 034.有初始数据1，依次启动两个事务，事务1 replace数据1，
                事务2 读取数据，只能读到初始数据1，事务1提交，事务2 读取数据，只能读到初始数据1
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 6;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 replace
    TestReplaceVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 2);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(6, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(6, getValue);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2数值改变
    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);
}

/*****************************************************************************
 Description  : 035.有初始数据1，依次启动两个事务，事务1 delete数据1，
                事务2 读取数据，只能读到初始数据1，事务1提交，事务2 读取数据，只能读到初始数据1
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 6;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 delete
    TestDeleteVertexLabel(g_stmt1, "Vertex_pk", times, initValue, "Vertex_01");

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(6, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(6, getValue);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 036.有初始数据1，唯一local索引1，依次启动两个事务，
                事务1 update唯一local索引2，事务1提交，
                事务2 insert数据2，唯一local索引2，报错值冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    uint32_t updateValue = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    system("gmadmin -cfgName compatibleV3 -cfgVal 0");

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    // 创建Vertex
    TestCreateLabelTrx(g_stmt);

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update, F1更新为1
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "Vertex_01", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    updateValue = 1;
    ret = GmcSetVertexProperty(g_stmt1, "F1", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update
    ret = GmcSetIndexKeyName(g_stmt1, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcGetStmtAttr(g_stmt1, 1, 0);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F1", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F1", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 事务2 insert
    initValue = 1;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_UNIQUE_VIOLATION, 0);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_NO_DATA);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 0;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F1", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    // 创建Vertex
    TestCreateLabelTrx(g_stmt);
}

/*****************************************************************************
 Description  : 037.有初始数据1，唯一hashcluster索引1，依次启动两个事务，
                事务1 update唯一hashcluster索引2，事务1提交，
                事务2 insert数据2，唯一hashcluster索引2，报错值冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    uint32_t updateValue = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    system("gmadmin -cfgName compatibleV3 -cfgVal 0");

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    // 创建Vertex
    TestCreateLabelTrx(g_stmt);

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update, F1更新为1
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "Vertex_01", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    updateValue = 1;
    ret = GmcSetVertexProperty(g_stmt1, "F3", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update
    ret = GmcSetIndexKeyName(g_stmt1, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcGetStmtAttr(g_stmt1, 1, 0);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F3", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F3", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 事务2 insert
    initValue = 1;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_UNIQUE_VIOLATION, 0);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_NO_DATA);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 0;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F3", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    // 创建Vertex
    TestCreateLabelTrx(g_stmt);
}

/*****************************************************************************
 Description  : 038.有初始数据1，唯一lpm索引1，依次启动两个事务，
                事务1 update唯一lpm索引2，事务1提交，
                事务2 insert数据2，唯一lpm索引2，报错值冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    uint32_t updateValue = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 replace, vrid更新为1
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "Vertex_01", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt1, "PK", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt1, "F1", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt1, "F2", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt1, "F3", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt1, "F4", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // lpm4
    updateValue = 1;
    uint8_t maskLen = 24;
    ret = GmcSetVertexProperty(g_stmt1, "vr_id", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt1, "vrf_index", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ipAddrTmp[16];
    ret = sprintf_s(ipAddrTmp, 15, "192.%d.%d.%d", updateValue, updateValue, updateValue);
    ASSERT_LT(0, ret);
    uint32_t transVal = TransIp(ipAddrTmp);
    ret = GmcSetVertexProperty(g_stmt1, "dest_ip_addr", GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt1, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcGetStmtAttr(g_stmt1, 2, 0);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "vr_id", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "vr_id", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 事务2 insert
    initValue = 1;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_UNIQUE_VIOLATION, 0);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 0;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "vr_id", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);
}

/*****************************************************************************
 Description  : 039.有初始数据1，唯一member key索引1，依次启动两个事务，
                事务1 update唯一member key索引2，事务1提交，
                事务2 insert数据2，唯一member key索引2，报错值冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    uint32_t updateValue = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;
    GmcNodeT *root = NULL, *V1 = NULL;
    uint32_t updateindex = 0;

    // 初始数据
    TestInsertTreeLabel(g_stmt, times, initValue, "Tree_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update, V1.F1更新为1
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "Tree_01", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt1, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    updateValue = 1;
    updateindex = 0;
    ret = GmcNodeGetElementByIndex(V1, updateindex, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(V1, "F1", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update
    ret = GmcSetIndexKeyName(g_stmt1, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcGetStmtAttr(g_stmt1, 1, 0);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Tree_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt2, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(V1, 0, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcNodeGetPropertyByName(V1, "F1", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Tree_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt2, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(V1, 0, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcNodeGetPropertyByName(V1, "F1", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 事务2 update, V1.F1 append一个新的元素为1
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Tree_01", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt2, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    updateValue = 1;
    ret = GmcNodeAppendElement(V1, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(V1, "F1", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update
    ret = GmcSetIndexKeyName(g_stmt2, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    TestGmcGetStmtAttr(g_stmt2, 0, 0);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Tree_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 0;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt2, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(V1, 0, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcNodeGetPropertyByName(V1, "F1", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);
}

/*****************************************************************************
 Description  : 040.有初始数据1，非唯一local索引1，依次启动两个事务，
                事务1 update非唯一local索引2，事务1提交，
                事务2 insert数据2，非唯一local索引2，业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    uint32_t updateValue = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update, F1更新为1
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "Vertex_01", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    updateValue = 1;
    ret = GmcSetVertexProperty(g_stmt1, "F2", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update
    ret = GmcSetIndexKeyName(g_stmt1, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcGetStmtAttr(g_stmt1, 1, 0);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 事务2 insert
    initValue = 1;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_local", 1);

    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 0;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);

    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 1;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);
}

/*****************************************************************************
 Description  : 041.有初始数据1，非唯一hashcluster索引1，依次启动两个事务，
                事务1 update非唯一hashcluster索引2，事务1提交，
                事务2 insert数据2，非唯一hashcluster索引2，业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    uint32_t updateValue = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;

    // 初始数据
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update, F1更新为1
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "Vertex_01", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    updateValue = 1;
    ret = GmcSetVertexProperty(g_stmt1, "F4", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update
    ret = GmcSetIndexKeyName(g_stmt1, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcGetStmtAttr(g_stmt1, 1, 0);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F4", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F4", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 事务2 insert
    initValue = 1;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelByKey(g_stmt2, "Vertex_01", expectNum, "Vertex_hashcluster", 1);

    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 0;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F4", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Vertex_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 1;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Vertex_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcGetVertexPropertyByName(g_stmt2, "F4", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);
}

/*****************************************************************************
 Description  : 042.有初始数据1，非唯一member key索引1，依次启动两个事务，
                事务1 update非唯一member key索引2，事务1提交，
                事务2 insert数据2，非唯一member key索引2，业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;
    uint32_t updateValue = 0;
    bool isFinish = false;
    bool isNull = true;
    uint32_t getValue = 0;
    GmcNodeT *root = NULL, *V1 = NULL;
    uint32_t updateindex = 0;

    // 初始数据
    TestInsertTreeLabel(g_stmt, times, initValue, "Tree_01");

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 update, V1.F1更新为1
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "Tree_01", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt1, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt1, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    updateValue = 1;
    updateindex = 0;
    ret = GmcNodeGetElementByIndex(V1, updateindex, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(V1, "F2", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update
    ret = GmcSetIndexKeyName(g_stmt1, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcGetStmtAttr(g_stmt1, 1, 0);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Tree_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt2, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(V1, 0, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcNodeGetPropertyByName(V1, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Tree_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt2, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(V1, 0, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcNodeGetPropertyByName(V1, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(0, getValue);

    // 事务2 insert
    initValue = 1;
    TestInsertTreeLabel(g_stmt2, times, initValue, "Tree_01");

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Tree_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 0;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt2, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(V1, 0, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcNodeGetPropertyByName(V1, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt2, "Tree_01", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 1;
    ret = GmcSetIndexKeyValue(g_stmt2, 0, GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt2, "Tree_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt2, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt2, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "V1", &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetElementByIndex(V1, 0, &V1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    getValue = 0;
    ret = GmcNodeGetPropertyByName(V1, "F2", &getValue, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(1, getValue);
}

/*****************************************************************************
 Description  : 043.KV表无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 insert数据1，报错锁冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertKVTable(g_stmt1, times, initValue, "KVTable_01");

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 事务2 insert
    TestInsertKVTable(g_stmt2, times, initValue, "KVTable_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
}

/*****************************************************************************
 Description  : 044.KV表无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 update数据1，报错锁冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertKVTable(g_stmt1, times, initValue, "KVTable_01");

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 事务2 Update
    TestUpdateKVTable(g_stmt2, times, initValue, "KVTable_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
}

/*****************************************************************************
 Description  : 045.KV表无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 delete数据1，报错锁冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertKVTable(g_stmt1, times, initValue, "KVTable_01");

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 事务2 delete
    TestDeleteKVTable(g_stmt2, times, initValue, "KVTable_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
}

/*****************************************************************************
 Description  : 046.KV表无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 insert数据1，报错事务冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertKVTable(g_stmt1, times, initValue, "KVTable_01");

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);

    // 事务2 insert
    TestInsertKVTable(g_stmt2, times, initValue, "KVTable_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
}

/*****************************************************************************
 Description  : 047.KV表无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 update数据1，报错事务冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertKVTable(g_stmt1, times, initValue, "KVTable_01");

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);

    // 事务2 Update
    TestUpdateKVTable(g_stmt2, times, initValue, "KVTable_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
}

/*****************************************************************************
 Description  : 048.KV表无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 delete数据1，报错事务冲突
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertKVTable(g_stmt1, times, initValue, "KVTable_01");

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
    expectNum = 0;
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);

    // 事务2 delete
    TestDeleteKVTable(g_stmt2, times, initValue, "KVTable_01", GMERR_RESTRICT_VIOLATION, 0);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanKVLabelCount(g_stmt1, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt, "KVTable_01", expectNum);
    TestScanKVLabelCount(g_stmt2, "KVTable_01", expectNum);
}

/*****************************************************************************
 Description  : 049.开启事务，写入大对象数据，事务提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    uint32_t i;
    uint32_t value;

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_01", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        // set Property
        TestGmcSetVertexProperty(g_stmt, value);
        TestGmcSetVertexPropertyMem(g_stmt, value);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", times);
}

/*****************************************************************************
 Description  : 050.开启事务，写入大对象数据，事务回滚
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    uint32_t i;
    uint32_t value;

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_01", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        // set Property
        TestGmcSetVertexProperty(g_stmt, value);
        TestGmcSetVertexPropertyMem(g_stmt, value);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 回滚事务
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", 0);
}

/*****************************************************************************
 Description  : 051.开启事务，批量写入数据，事务提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i;
    uint32_t value;
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_01", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        value = initValue + i;
        TestGmcSetVertexProperty_PK(g_stmt, value);
        TestGmcSetVertexProperty(g_stmt, value);

        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(times, totalNum);
    AW_MACRO_EXPECT_EQ_INT(times, successNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", times);
}

/*****************************************************************************
 Description  : 052.开启事务，批量写入数据，事务回滚
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i;
    uint32_t value;
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_01", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        value = initValue + i;
        TestGmcSetVertexProperty_PK(g_stmt, value);
        TestGmcSetVertexProperty(g_stmt, value);

        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(times, totalNum);
    AW_MACRO_EXPECT_EQ_INT(times, successNum);
    GmcBatchDestroy(batch);

    // 回滚事务
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", 0);
}

/*****************************************************************************
 Description  : 053.开启事务，异步连接写入数据，事务提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    AsyncUserDataT asyncData;
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = GmcTransStartAsync(g_conn_async, &g_msTrxCfgRR, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // insert
    uint32_t i;
    uint32_t value;

    // insert
    for (i = 0; i < times; i++) {
        // 写数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "Vertex_01", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        value = initValue + i;

        TestGmcSetVertexProperty_PK(g_stmt_async, value);
        TestGmcSetVertexProperty(g_stmt_async, value);

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        AW_MACRO_EXPECT_EQ_INT(1, asyncData.affectRows);
        memset(&asyncData, 0, sizeof(AsyncUserDataT));
    }

    // 提交事务
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", times);
}

/*****************************************************************************
 Description  : 054.开启事务，异步连接写入数据，事务回滚
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    AsyncUserDataT asyncData;
    memset(&asyncData, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = GmcTransStartAsync(g_conn_async, &g_msTrxCfgRR, trans_start_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // insert
    uint32_t i;
    uint32_t value;

    // insert
    for (i = 0; i < times; i++) {
        // 写数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "Vertex_01", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        value = initValue + i;

        TestGmcSetVertexProperty_PK(g_stmt_async, value);
        TestGmcSetVertexProperty(g_stmt_async, value);

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);
        AW_MACRO_EXPECT_EQ_INT(1, asyncData.affectRows);
        memset(&asyncData, 0, sizeof(AsyncUserDataT));
    }

    // 回滚事务
    ret = GmcTransRollBackAsync(g_conn_async, trans_commit_callback, &asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncData.status);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", 0);
}

/*****************************************************************************
 Description  : 055.包含边关系的表，开启事务，分别插入数据，触发自动建边，事务提交。
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;

    // 创建带边关系的表
    TestCreateEdgeLabelTrx(g_stmt);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    uint32_t i;
    uint32_t value;

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_02", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        // set Property
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_03", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        // set Property
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_02", times);
    TestScanLabelCount(g_stmt, "Vertex_03", times);

    // 删除表
    TestDropEdgeLabel(g_stmt);
}

/*****************************************************************************
 Description  : 056.包含边关系的表，开启事务，分别插入数据，触发自动建边，事务回滚。
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;

    // 创建带边关系的表
    TestCreateEdgeLabelTrx(g_stmt);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    uint32_t i;
    uint32_t value;

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_02", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        // set Property
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_03", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        // set Property
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 回滚事务
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_02", 0);
    TestScanLabelCount(g_stmt, "Vertex_03", 0);

    // 删除表
    TestDropEdgeLabel(g_stmt);
}

/*****************************************************************************
 Description  : 057.开启事务，资源表写入数据，事务提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;

    // 建立资源池
    ret = GmcCreateResPool(g_stmt, g_resPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建资源表
    TestCreateResLabelTrx(g_stmt);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt, g_resPoolTestName, "Vertex_04");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    uint32_t i;
    uint32_t value;
    uint64_t tmpResIdx = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_04", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        // set Property
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 资源池ID 固定为0，count固定为1
        ret = GmcSetPoolIdResource(0, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(1, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F10", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_04", times);

    // 删除数据
    TestDeleteVertexLabel(g_stmt, "Vertex_pk", times, initValue, "Vertex_04");

    // 删除资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt, "Vertex_04");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, g_resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    TestDropResLabel(g_stmt);
}

/*****************************************************************************
 Description  : 058.开启事务，资源表写入数据，事务回滚
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;

    // 建立资源池
    ret = GmcCreateResPool(g_stmt, g_resPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建资源表
    TestCreateResLabelTrx(g_stmt);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt, g_resPoolTestName, "Vertex_04");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    uint32_t i;
    uint32_t value;
    uint64_t tmpResIdx = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_04", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        // set Property
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 资源池ID 固定为0，count固定为1
        ret = GmcSetPoolIdResource(0, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(1, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F10", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 回滚事务
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_04", 0);

    // 删除资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt, "Vertex_04");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, g_resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    TestDropResLabel(g_stmt);
}

/*****************************************************************************
 Description  : 059.开启事务，写入1个页左右的数据，事务提交，再次开启事务，
                更新数据，包含大量变长数据，提交事务
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 300;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = system("gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=Vertex_01");
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2 update
    TestUpdateVertexLabel(g_stmt, "Vertex_pk", times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = system("gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=Vertex_01");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
}

/*****************************************************************************
 Description  : 060.多线程都开启事务，并发写入主键不冲突的数据，事务提交，预期都成功
 Author       : hanyang
*****************************************************************************/
void *Thread_060(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t cycle = 0;

    uint32_t connId = *((uint32_t *)args);

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    // 启动事务
    ret = GmcTransStart(conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread %d] Insert start==================\n\n", connId);

    // 事务 insert
    initValue = connId * times;
    TestInsertVertexLabel(stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread %d] Insert end==================\n\n", connId);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// main
TEST_F(PessimisiticRR, Other_057_Func_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 并发操作
    pthread_t tid[10];
    uint32_t index[10] = {0};

    for (uint32_t i = 0; i < 10; i++) {
        index[i] = i;
        pthread_create(&tid[i], NULL, Thread_060, (void *)&index[i]);
    }

    for (uint32_t i = 0; i < 10; i++) {
        pthread_join(tid[i], NULL);
    }

    // 总数据条数正确10 * 100
    TestScanLabelCount(g_stmt, "Vertex_01", 1000);
}

/*****************************************************************************
 Description  : 061.有基础数据，多线程都开启事务，并发不同的DML操作不同的数据，事务提交，预期都成功
 Author       : hanyang
*****************************************************************************/
void *Thread_061_01(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;

    AW_FUN_Log(LOG_STEP, "==============[thread 1] Insert start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    // 启动事务
    ret = GmcTransStart(conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 insert
    initValue = 500;
    TestInsertVertexLabel(stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 1] Insert end==================\n\n");
    return NULL;
}

void *Thread_061_02(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;

    AW_FUN_Log(LOG_STEP, "==============[thread 2] Update start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    // 启动事务
    ret = GmcTransStart(conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 Update
    initValue = 100;
    TestUpdateVertexLabel(stmt, "Vertex_pk", times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 2] Update end==================\n\n");
    return NULL;
}

void *Thread_061_03(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;

    AW_FUN_Log(LOG_STEP, "==============[thread 3] Merge start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    // 启动事务
    ret = GmcTransStart(conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 Merge
    initValue = 200;
    TestMergeVertexLabel(stmt, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 2);

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 3] Merge end==================\n\n");
    return NULL;
}

void *Thread_061_04(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;

    AW_FUN_Log(LOG_STEP, "==============[thread 4] Replace start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    // 启动事务
    ret = GmcTransStart(conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 Replace
    initValue = 300;
    TestReplaceVertexLabel(stmt, "Vertex_pk", times, initValue, "Vertex_01", GMERR_OK, 2);

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 4] Replace end==================\n\n");
    return NULL;
}

void *Thread_061_05(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;

    AW_FUN_Log(LOG_STEP, "==============[thread 5] Delete start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    // 启动事务
    ret = GmcTransStart(conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 Delete
    initValue = 400;
    TestDeleteVertexLabel(stmt, "Vertex_pk", times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 5] Delete end==================\n\n");
    return NULL;
}

// main
TEST_F(PessimisiticRR, Other_057_Func_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 初始数据
    uint32_t initValue = 0;
    uint32_t times = 500;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 并发操作
    pthread_t tid[10];

    pthread_create(&tid[0], NULL, Thread_061_01, NULL);
    pthread_create(&tid[1], NULL, Thread_061_02, NULL);
    pthread_create(&tid[2], NULL, Thread_061_03, NULL);
    pthread_create(&tid[3], NULL, Thread_061_04, NULL);
    pthread_create(&tid[4], NULL, Thread_061_05, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    pthread_join(tid[3], NULL);
    pthread_join(tid[4], NULL);
}

/*****************************************************************************
 Description  : 062.有基础数据，一个线程开启事务，DML操作数据，事务提交，不停循环，
                另一个线程循环多次读取当前的数据
 Author       : hanyang
*****************************************************************************/
void *Thread_062_01(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 20000;
    uint32_t times = 1000;

    AW_FUN_Log(LOG_STEP, "==============[thread 1] DML start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    for (uint32_t i = 0; i < 100; i++) {
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_DEBUG, "[thread 1] DML The number of cycle is %d.\n", i);
        }

        // 启动事务
        ret = GmcTransStart(conn, &g_msTrxCfgRR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 事务 insert
        TestInsertVertexLabel(stmt, times, initValue, "Vertex_01");

        usleep(1000);

        // 事务 Delete
        TestDeleteVertexLabel(stmt, "Vertex_pk", times, initValue, "Vertex_01");

        // 提交事务
        ret = GmcTransCommit(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 1] DML end==================\n\n");
    return NULL;
}

void *Thread_062_02(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;

    AW_FUN_Log(LOG_STEP, "==============[thread 2] Scan start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    for (uint32_t i = 0; i < 100; i++) {
        usleep(50000);
        // 启动事务
        ret = GmcTransStart(conn, &g_msTrxCfgRR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // scan vertex
        ret = testGmcPrepareStmtByLabelName(stmt, "Vertex_01", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;

        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            if (GMERR_OK != ret) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = testGmcGetLastError();
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            if (isFinish) {
                break;
            }

            cnt++;
        }

        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_INFO, "[thread 2] Full scan is complete, count is %d.", cnt);
        }

        // 提交事务
        ret = GmcTransCommit(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 2] Scan end==================\n\n");
    return NULL;
}

// main
TEST_F(PessimisiticRR, Other_057_Func_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 初始数据
    uint32_t initValue = 0;
    uint32_t times = RECORD_NUM_003;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 并发操作
    pthread_t tid[10];

    pthread_create(&tid[0], NULL, Thread_062_01, NULL);
    pthread_create(&tid[1], NULL, Thread_062_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/*****************************************************************************
 Description  : 063.一个线程不停操作表升降级，另一线程持续写老版本的数据
 Author       : hanyang
*****************************************************************************/
void *Thread_063_01(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 20000;
    uint32_t times = 1000;
    uint32_t j;
    uint32_t value;

    AW_FUN_Log(LOG_STEP, "==============[thread 1] DML start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    for (uint32_t i = 0; i < 100; i++) {
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_DEBUG, "[thread 1] DML The number of cycle is %d.\n", i);
        }

        // 启动事务
        ret = GmcTransStart(conn, &g_msTrxCfgRR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 事务 insert
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "Upgrad", 1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // insert vertex
        for (j = 0; j < times; j++) {
            // 写数据
            value = initValue + j;

            // set pk
            TestGmcSetVertexProperty_PK(stmt, value);

            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            TestGmcGetStmtAttr(stmt, 1, j);
        }

        usleep(10000);

        // 事务 Delete
        TestDeleteVertexLabel(stmt, "Vertex_pk", times, initValue, "Upgrad");

        // 提交事务
        ret = GmcTransCommit(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 1] DML end==================\n\n");
    return NULL;
}

void *Thread_063_02(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;

    AW_FUN_Log(LOG_STEP, "==============[thread 2] alter start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(stmt, g_namespace);

    int32_t schemaVersion = 3;
    char *expectValue1 = (char *)"upgrade unsuccessfully";
    char *expectValue2 = (char *)"degrade unsuccessfully";
    char *schemaPath = (char *)"./schema_file/Upgrad.gmjson";
    char *schemaUpFile1 = (char *)"./schema_file/Upgrad_01.gmjson";
    char *schemaUpFile2 = (char *)"./schema_file/Upgrad_02.gmjson";

    // 2323.11.27, 悲观+可重复读事务不支持表升降级
    for (uint32_t i = 0; i < 10; i++) {
        // 升级
        ret = TestUpdateVertexLabelSchema(schemaUpFile1, expectValue1, "Upgrad");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 降级
        schemaVersion = 1;
        ret = TestDownGradeVertexLabel("Upgrad", schemaVersion, expectValue2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 2] alter end==================\n\n");
    return NULL;
}

// main
TEST_F(PessimisiticRR, Other_057_Func_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建表
    TestCreateUpgradLabelTrx(g_stmt);

    // 并发操作
    pthread_t tid[10];

    pthread_create(&tid[0], NULL, Thread_063_01, NULL);
    pthread_create(&tid[1], NULL, Thread_063_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    // 删除表
    TestDropUpgradLabel(g_stmt);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
}

/*****************************************************************************
 Description  : 065.开启事务后，DML操作，DDL操作（建表），继续DML操作，事务提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 insert
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // DDL操作
    char *vLabelSchema = NULL;
    readJanssonFile("schema_file/Vertex_02.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 再次DML操作
    initValue = 1000;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 2 * times;
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    TestDropLabel(g_stmt, "Vertex_02");
}

/*****************************************************************************
 Description  : 066.开启事务后，DML操作，DDL操作（建表），继续DML操作，事务回滚，回滚没有效果，
                另一个事务同时开启，一直读，都没有数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // DDL操作
    char *vLabelSchema = NULL;
    readJanssonFile("schema_file/Vertex_02.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt1, vLabelSchema, g_msConfigTrx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 再次DML操作
    initValue = 1000;
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 回滚事务1
    ret = GmcTransRollBack(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 2 * times;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    TestDropLabel(g_stmt1, "Vertex_02");
}

/*****************************************************************************
 Description  : 067.无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 insert数据1，报错锁冲突，事务2回滚，事务1继续DML操作，事务1提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务1 insert
    initValue = 1;
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 068.无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 insert数据2，事务1回滚，事务2继续DML操作，事务2提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert, key = 0
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 insert, key = 1
    initValue = 1;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");

    // 回滚事务1
    ret = GmcTransRollBack(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 insert, key = 0
    initValue = 0;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 2;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 069.无初始数据，依次启动两个事务，事务1 insert数据1,2,3，未提交，
                事务2 insert数据4,5,1，报错后，事务2回滚，事务1继续insert数据4,5，事务1提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert, key = 0,1,2
    initValue = 0;
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");
    initValue = 1;
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");
    initValue = 2;
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 3;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 insert, key = 4,5,1
    initValue = 4;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");
    initValue = 5;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");
    initValue = 1;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 3;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务1 insert, key = 4,5
    initValue = 4;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");
    initValue = 5;
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 5;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 075.表中含有localhash索引，创建时配置为悲观+可重复读，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;

    // 创建Vertex表
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/Vertex_06.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    free(vLabelSchema);
}

/*****************************************************************************
 Description  : 076.事务内多次重复开启事务，对业务无影响，读到的都是第一次开启前的数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 重复启动事务2
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复启动事务2
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}

/*****************************************************************************
 Description  : 077.事务内连接中断，重新连接后，开启事务，业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    // 启动事务1
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // 连接中断
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn1 = NULL;
    g_stmt1 = NULL;

    // 启动事务2
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");

    // 事务1重新建立连接
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUseNamespace(g_stmt1, g_namespace);

    // 启动事务1
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚事务1
    ret = GmcTransRollBack(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
}


/*****************************************************************************
 Description  : 078.开启事务，读数据过程中，客户端异常中断，重启客户端，业务正常
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Func_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = RECORD_NUM_003;

    // 初始数据
    // 启动事务2
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01");

    // 提交事务2
    ret = GmcTransCommit(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        ret = system("./PessRR_078 ");
        AW_MACRO_EXPECT_EQ_INT(9, ret);
    }
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_NO_DATA);
}

class PessimisiticRR_Restart : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void PessimisiticRR_Restart::SetUpTestCase()
{
}

void PessimisiticRR_Restart::TearDownTestCase()
{
}

void PessimisiticRR_Restart::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void PessimisiticRR_Restart::TearDown()
{
    AW_CHECK_LOG_END();
}


/*****************************************************************************
 Description  : 064.开启缩容开关，开启事务，删除数据，提交数据，查看是否触发缩容，
                再次插入数据，删除数据，循环操作
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR_Restart, Other_057_Func_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 5000;
    uint32_t i;
    uint32_t value;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"minFragmentationMemThreshold=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"minFragmentationRateThreshold=10\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    char *vLabelSchema = NULL;
    readJanssonFile("schema_file/Vertex_01.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigDef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // insert初始数据
    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_01", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        TestGmcSetVertexProperty(g_stmt, value);
        TestGmcSetVertexPropertyStr(g_stmt, value);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除一半以上数据
    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 Delete
    times = 4000;
    TestDeleteVertexLabel(g_stmt, "Vertex_pk", times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(61);

    // 查询是否触发了缩容
    uint32_t compactCount = 0;
    GetClusteredStat("DEFRAGMENTATION_CNT", &compactCount);
    EXPECT_GE(compactCount, 1);

    // 删除Vertex
    ret = GmcDropVertexLabel(g_stmt, "Vertex_01");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 070.无初始数据，依次启动两个事务，事务1 insert数据1，未提交，
                事务2 insert数据1，报错锁冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR_Restart, Other_057_Func_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    TestCreateLabelTrx(g_stmt);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_LOCK_NOT_AVAILABLE, 0);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 071.无初始数据，依次启动两个事务，事务1 insert数据1，事务1提交，
                事务2 insert数据1，报错主键冲突，过程中多次读取数据
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR_Restart, Other_057_Func_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    TestCreateLabelTrx(g_stmt);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn2, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    expectNum = 0;
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 事务2 insert
    TestInsertVertexLabel(g_stmt2, times, initValue, "Vertex_01", GMERR_PRIMARY_KEY_VIOLATION, 0);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 回滚事务2
    ret = GmcTransRollBack(g_conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 1;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt, "Vertex_01", expectNum);
    TestScanLabelCount(g_stmt2, "Vertex_01", expectNum);

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 072.多线程都开启事务，并发写入主键不冲突的数据，事务提交，预期都成功
 Author       : hanyang
*****************************************************************************/

void *Thread_072(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t cycle = 0;

    uint32_t connId = *((uint32_t *)args);

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread %d] Insert start==================\n\n", connId);

    // 事务 insert
    initValue = connId * times;
    TestInsertVertexLabel(stmt, times, initValue, "Vertex_01");

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread %d] Insert end==================\n\n", connId);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// main
TEST_F(PessimisiticRR_Restart, Other_057_Func_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    TestCreateLabelTrx(g_stmt);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 并发操作
    pthread_t tid[10];
    uint32_t index[10] = {0};

    for (uint32_t i = 0; i < 10; i++) {
        index[i] = i;
        pthread_create(&tid[i], NULL, Thread_072, (void *)&index[i]);
    }

    for (uint32_t i = 0; i < 10; i++) {
        pthread_join(tid[i], NULL);
    }

    // 总数据条数正确10 * 100
    TestScanLabelCount(g_stmt, "Vertex_01", 1000);

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 073.有基础数据，一个线程开启事务，DML操作数据，事务提交，不停循环，
                另一个线程循环多次读取当前的数据
 Author       : hanyang
*****************************************************************************/
void *Thread_073_01(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 20000;
    uint32_t times = 1000;

    AW_FUN_Log(LOG_STEP, "==============[thread 1] DML start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_DEBUG, "[thread 1] DML The number of cycle is %d.\n", i);
        }

        // 启动事务
        ret = GmcTransStart(conn, &g_msTrxCfgRR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 事务 insert
        TestInsertVertexLabel(stmt, times, initValue, "Vertex_01");

        usleep(1000);

        // 事务 Delete
        TestDeleteVertexLabel(stmt, "Vertex_pk", times, initValue, "Vertex_01");

        // 提交事务
        ret = GmcTransCommit(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 1] DML end==================\n\n");
    return NULL;
}

void *Thread_073_02(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 100;

    AW_FUN_Log(LOG_STEP, "==============[thread 2] Scan start==================\n\n");

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        usleep(50000);
        // 启动事务
        ret = GmcTransStart(conn, &g_msTrxCfgRR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestScanLabelCount(stmt, "Vertex_01", RECORD_NUM_003);

        // 提交事务
        ret = GmcTransCommit(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[thread 2] Scan end==================\n\n");
    return NULL;
}

// main
TEST_F(PessimisiticRR_Restart, Other_057_Func_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    TestCreateLabelTrx(g_stmt);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 初始数据
    uint32_t initValue = 0;
    uint32_t times = RECORD_NUM_003;
    TestInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");

    // 并发操作
    pthread_t tid[10];

    pthread_create(&tid[0], NULL, Thread_073_01, NULL);
    pthread_create(&tid[1], NULL, Thread_073_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 074.开启聚簇容器开关，定长表创建时配置为悲观+可重复读，不使用聚簇容器
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR_Restart, Other_057_Func_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1000;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/Vertex_05.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 查询表没有使用聚簇容器
    char command[MAX_CMD_SIZE];

    // 聚簇容器视图查不到
    char const *view_name = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = executeCommand(command, "LABEL_NAME: Vertex_05");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    // heap视图查得到
    char const *view_name1 = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name1);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = executeCommand(command, "LABEL_NAME: Vertex_05");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt1, "Vertex_05", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    uint32_t i;
    uint32_t value;
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt1, value);

        ret = GmcSetVertexProperty(g_stmt1, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1, "F3", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1, "F4", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt1, 1, i);
    }

    // 提交事务1
    ret = GmcTransCommit(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除Vertex
    TestDropLabel(g_stmt, "Vertex_05");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 079.开启事务，读数据，长事务超时，超时后不能继续读取数据，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR_Restart, Other_057_Func_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=\"5,10\"\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    TestCreateLabelTrx(g_stmt);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 启动事务
    ret = GmcTransStart(g_conn1, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1 insert
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01");

    // 长事务检测
    sleep(15);

    // 再次操作报错
    initValue = 1000;
    times = 100;
    TestInsertVertexLabel(g_stmt1, times, initValue, "Vertex_01", GMERR_TRANSACTION_ROLLBACK, 0);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);

    // 回滚事务1
    ret = GmcTransRollBack(g_conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    expectNum = 0;
    TestScanLabelCount(g_stmt1, "Vertex_01", expectNum);

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
