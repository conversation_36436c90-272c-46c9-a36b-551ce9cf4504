/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2017-2027. All rights reserved.
 * File Name:
 * Author: yaosiyuan ywx758883
 * Date: 2021-5-31
 * Describle:  account check test
 */

#include "gtest/gtest.h"
#include "syCommon.h"
#include "t_datacom_lite.h"
#include "vertex.h"
#include "kv.h"
#include "tree.h"

class basic : public testing::Test {
public:
    virtual void SetUp()
    {
        g_subIndex = 0;
        memset(g_sub_callback_count, 0, 10 * sizeof(uint32_t));
        mallocSubData();
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        uint32_t ret = 1;
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char *labelName = (char *)"schema_datatype";
        GmcDropVertexLabel(stmt, labelName);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    };
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        freeMallocSqace(g_userData);
    };
    static void SetUpTestCase()
    {
        int ret = 0;
        if (g_envType == 1) {
            system("sh $TEST_HOME/tools/stop.sh");
            system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
        }
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        int ret = 0;
        ret = close_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    };
};

// GmcBeginCheck里的stmt参数为NULL
TEST_F(basic, Other_015_001)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt1, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcBeginCheck里的labelName参数为NULL
TEST_F(basic, Other_015_002)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcBeginCheck里的partition参数传个负数
TEST_F(basic, Other_015_003)
{
    int ret = 0;
    uint8_t partition = -1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, partition);
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcEndCheck(stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcBeginCheck里的partition参数为0
TEST_F(basic, Other_015_004)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    uint8_t partition = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, partition);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcBeginCheck里的partition参数为15
TEST_F(basic, Other_015_005)
{
    int ret = 0;
    uint8_t partition = 15;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, partition);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);  // 该错误码有疑问，使用的为非分区表，报错partition id无效

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcBeginCheck里的partition参数为16
TEST_F(basic, Other_015_006)
{
    int ret = 0;
    uint8_t partition = 16;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 补充分区表
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, partition);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcBeginCheck里的partition参数为0xff, 正常对账时写入超限数据
TEST_F(basic, Other_015_007)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = (char *)"schema_datatype";
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, 128, "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelConfig[] = "{\"max_record_count\":10}";
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // 写入超限数据: 失败
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1 = 10;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    // 等待对账老化结束
    sleep(1);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // 重新写入数据: 成功
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 10;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    bool isNull;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int sizeF1;
    ret = GmcGetVertexPropertySizeByName(stmt, "F1", &sizeF1);
    EXPECT_EQ(GMERR_OK,ret);
    uint32_t scanValueF1;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &scanValueF1, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(F1, scanValueF1);

    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcEndCheck里的stmt参数为NULL
TEST_F(basic, Other_015_008)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(NULL, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcEndCheck里的labeName参数为NULL
TEST_F(basic, Other_015_009)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, NULL, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcEndCheck里的partition参数为负数
TEST_F(basic, Other_015_010)
{
    int ret = 0;
    uint8_t partition = -1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_partition.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, partition, isAbnormal);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcEndCheck(stmt, labelName, 1, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcEndCheck里的partition参数为0
TEST_F(basic, Other_015_011)
{
    int ret = 0;
    uint8_t partition = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, partition, isAbnormal);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 异常对账时写入超限数据
TEST_F(basic, Other_015_012)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint8_t partition = 15;
    bool isAbnormal = true;
    char *labelName = (char *)"schema_datatype";
    char errorMsg1[128] = {0};
    char errorMsg2[128] = {0};
    (void)snprintf(errorMsg1, 128, "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    (void)snprintf(errorMsg2, 128, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char labelConfig[] = "{\"max_record_count\":10}";
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, false);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入超限数据: 成功
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1 = 10;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);

    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    // 可行 ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    // 可行 EXPECT_EQ(GMERR_TABLE_NOT_IN_CHECKING, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    // 重新对账
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 10;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);

    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcEndCheck里的partition参数为16
TEST_F(basic, Other_015_013)
{
    int ret = 0;
    uint8_t partition = 16;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_partition.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, partition, isAbnormal);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcEndCheck(stmt, labelName, 1, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcUpdateCheckVersion里的stmt参数为NULL
TEST_F(basic, Other_015_014)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    char *filter = (char *)"pk";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //待补充filter
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcUpdateCheckVersion里的labelName参数为NULL
TEST_F(basic, Other_015_015)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const char *filter = (char *)"pk";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //待补充filter
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcUpdateCheckVersion里的filter参数为主键索引
TEST_F(basic, Other_015_016)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const char *filter = (char *)"pk";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcUpdateCheckVersion里的filter参数为其他非主键索引
TEST_F(basic, Other_015_017)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const char *filter = (char *)"hashcluster";
    const char *filter1 = (char *)"pk";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);  // ：报错原因与实际不符合,2021.7.7已改过来
    int y = 5;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &y, sizeof(y));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcUpdateCheckVersion里的filter参数为非索引字段
TEST_F(basic, Other_015_018)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const char *filter = (char *)"F1";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcGetCheckInfo里的stmt参数为NULL
TEST_F(basic, Other_015_019)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcCheckInfoT *checkInfo;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt1, labelName, FULLTABLE, &checkInfo);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcGetCheckInfo里的labelName参数为NULL
TEST_F(basic, Other_015_020)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcCheckInfoT *checkInfo;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, NULL, FULLTABLE, &checkInfo);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcGetCheckInfo里的partition参数为0
TEST_F(basic, Other_015_021)
{
    int ret = 0;
    uint8_t partition = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcCheckInfoT *checkInfo;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, labelName, partition, &checkInfo);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcGetCheckInfo里的partition参数为15
TEST_F(basic, Other_015_022)
{
    int ret = 0;
    uint8_t partition = 15;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcCheckInfoT *checkInfo;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, labelName, partition, &checkInfo);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcGetCheckInfo里的partition参数为16
TEST_F(basic, Other_015_023)
{
    int ret = 0;
    uint8_t partition = 16;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcCheckInfoT *checkInfo;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, labelName, partition, &checkInfo);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcGetCheckInfo里的partition参数为256
TEST_F(basic, Other_015_024)
{
    int ret = 0;
    uint16_t partition = 256;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    GmcCheckInfoT *checkInfo;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_partition.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, partition);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, labelName, partition, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, partition, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 异常对账下的非唯一索引冲突
TEST_F(basic, Other_015_025)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = true;
    char *labelName = (char *)"schema_datatype";
    char errorMsg1[128] = {0};
    char errorMsg2[128] = {0};
    (void)snprintf(errorMsg1, 128, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, 128, "GMERR-%d", GMERR_TABLE_NOT_IN_CHECKING);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype_rel.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    // 二级索引唯一性冲突
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1 = 11;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F6 = 1;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal); // 出于对账中
    EXPECT_EQ(GMERR_OK, ret);

    sleep(1);
    // 可行 ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal); // 对账异常
    // 可行 EXPECT_EQ(GMERR_TABLE_NOT_IN_CHECKING, ret);
    // 重新写入F6的值, 预期还是写入失败
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 11;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
    EXPECT_EQ(GMERR_OK, ret);
    F6 = 1;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 不存在数据更新版本号
TEST_F(basic, Other_015_026)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    const char *filter = (char *)"pk";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 待确认
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 不存在数据，开启对账，写数据读数据，结束对账，核查数据
TEST_F(basic, Other_015_027)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 不存在数据，开启对账，replace数据读数据，结束对账，核查数据
TEST_F(basic, Other_015_028)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 不存在数据，开启对账，replace数据读数据，结束对账，核查数据
TEST_F(basic, Other_015_029)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexMerge(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据后执行对账，结束对账，核查数据
TEST_F(basic, Other_015_030)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, 128, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype_rel.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record schema_datatype");

    // 二级索引唯一性冲突
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1 = 11;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F6 = 1;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    
    // 重新写入F6的值, 预期写入成功
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 11;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
    EXPECT_EQ(GMERR_OK, ret);
    F6 = 1;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    bool isNull;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int sizeF6;
    ret = GmcGetVertexPropertySizeByName(stmt, "F6", &sizeF6);
    EXPECT_EQ(GMERR_OK,ret);
    uint32_t scanValueF6;
    ret = GmcGetVertexPropertyByName(stmt, "F6", &scanValueF6, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(F6, scanValueF6);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据后执行对账，更新版本号，结束对账，核查数据
TEST_F(basic, Other_015_031)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    char *primarykey = (char *)"pk";
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, primarykey);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据后更新执行对账，更新版本号，结束对账，核查数据
TEST_F(basic, Other_015_032)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, primarykey);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据后更新删除执行对账，更新版本号，结束对账，核查数据
TEST_F(basic, Other_015_033)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;  // ：更新版本号的值不存在不报错
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, primarykey);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据后更新删除replace执行对账，结束对账，核查数据
TEST_F(basic, Other_015_034)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据后更新删除merge执行对账，结束对账，核查数据
TEST_F(basic, Other_015_035)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexMerge(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，replace后读取数据，结束对账，核查数据
TEST_F(basic, Other_015_036)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，写入新数据读取数据，结束对账，核查数据
TEST_F(basic, Other_015_037)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, labelName);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND * 2, stmt, labelName, vertexLabel, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，更新数据读取数据，结束对账，核查数据
TEST_F(basic, Other_015_038)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，更新数据读取数据，结束对账，核查数据
TEST_F(basic, Other_015_039)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，更新数据删除数据replace数据读取数据，结束对账，核查数据
TEST_F(basic, Other_015_040)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);  // 全表扫描只扫描了一半的数据
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，更新数据删除数据merge数据读取数据，结束对账，核查数据
TEST_F(basic, Other_015_041)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexMerge(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，truncate数据，结束对账，核查数据
TEST_F(basic, Other_015_042)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 建边，写数据，开启对账（只老化一个顶点），结束对账，核查数据
TEST_F(basic, Other_015_043)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *edgeName = (char *)"edge_1";
    char *labelName = (char *)"schema_datatype";
    char *labelNameVertex2 = (char *)"T8";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_2.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEdgeLabel((char *)"schema/edge_1.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertex2Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelNameVertex2);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    ret = GmcDropGraphLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 建边，开启对账，写数据，结束对账(老化一个顶点后确认是否可以通过拓扑节点获取)
TEST_F(basic, Other_015_044)
{
    int ret = 0;
    uint32_t nhp_group_id = 1;
    uint32_t vr_id = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *edgeLabel = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *edgeName = (char *)"edge_1";
    char *labelName = (char *)"schema_datatype";
    char *labelNameVertex2 = (char *)"T8";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_2.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEdgeLabel((char *)"schema/edge_1.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertex2Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelNameVertex2);

    // 手动建边
    int affectRows;
    uint32_t F1 = 1;
    ret = GmcOpenEdgeLabelByName(stmt, edgeName, &edgeLabel);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, (char *)"pk");  // ：labelname没有校验
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1,
        sizeof(F1));           // :这里使用GMC_DATATYPE_INT8会报错下面使用GMC_DATATYPE_UINT32没有报错
    ASSERT_EQ(GMERR_OK, ret);  // ：手动建边的数据不存在时没有报错
    ret = GmcSetEdgeDstVertexIndexName(stmt, (char *)"primary_key_nhp_group");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &nhp_group_id, sizeof(GMC_DATATYPE_UINT32));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vr_id, sizeof(GMC_DATATYPE_UINT32));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcInsertEdge(stmt, edgeLabel);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, affectRows);
    ret = GmcCloseEdgeLabel(stmt, edgeLabel);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //拓扑邻点
    unsigned int sizeVaule;
    bool isNull;
    int valueF2 = 0;
    for (int i = 2; i < 10; i++) {
        uint32_t F1 = i;
        ret = GmcAllocStmt(conn, &stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1, sizeof(F1));  //这里需要用新的stmt 原因是什么
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, edgeName);
        ASSERT_EQ(GMERR_OK, ret);
        bool isEof;

        ret = GmcFetch(stmt, &isEof);
        // ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(false, isEof);
        ret = GmcGetVertexPropertySizeByName(stmt, "nhp_number", &sizeVaule);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, "nhp_number", &valueF2, sizeVaule, &isNull);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcFetch(stmt, &isEof);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(true, isEof);
        ret = GmcDirectFetchNeighborEnd(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        GmcFreeStmt(stmt);
    }
    stmt = NULL;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelNameVertex2, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelNameVertex2, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelNameVertex2);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    ret = GmcDropGraphLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 建边，写数据，开启对账（只老化一个顶点），写数据，结束对账，核查数据
TEST_F(basic, Other_015_045)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    uint32_t nhp_group_id = 1;
    uint32_t vr_id = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *edgeLabel = NULL;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    bool isAbnormal = false;
    char *edgeName = (char *)"edge_1";
    char *labelName = (char *)"schema_datatype";
    char *labelNameVertex2 = (char *)"T8";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_2.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEdgeLabel((char *)"schema/edge_1.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertex2Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelNameVertex2);
    // 开启对账
    ret = GmcBeginCheck(stmt, labelNameVertex2, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, labelName);
    ret = GmcEndCheck(stmt, labelNameVertex2, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$STORAGE_EDGE_LABEL_STAT", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropGraphLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，进行全表扫描，结束对账，核查数据
TEST_F(basic, Other_015_046)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，进行localhash扫描，结束对账，核查数据
TEST_F(basic, Other_015_047)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLocalhash(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，进行localkey扫描，结束对账，核查数据
TEST_F(basic, Other_015_048)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    // ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    // EXPECT_EQ(GMERR_OK, ret);
    vertexLocalkey(stmt, labelName, vertexLabel, RECORDCOUNTSTART, RECORDCOUNTEND);
    // ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = checkAccountStatus(stmt, labelName);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，进行hashcluster扫描，结束对账，核查数据
TEST_F(basic, Other_015_049)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexHashcluster(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，写入新数据进行全表扫描，结束对账，核查数据
TEST_F(basic, Other_015_050)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, labelName);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，写入新数据进行localhash扫描，结束对账，核查数据
TEST_F(basic, Other_015_051)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, labelName);
    ret = vertexLocalhash(stmt, labelName, vertexLabel, RECORDCOUNTEND * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，写入新数据进行localkey扫描，结束对账，核查数据
TEST_F(basic, Other_015_052)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, labelName);
    vertexLocalkey(stmt, labelName, vertexLabel, RECORDCOUNTSTART, RECORDCOUNTEND * 2);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，写入新数据进行hashcluster扫描，结束对账，核查数据
TEST_F(basic, Other_015_053)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, labelName);
    ret = vertexHashcluster(stmt, labelName, vertexLabel, RECORDCOUNTEND * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，删除数据进行全表扫描，结束对账，核查数据
TEST_F(basic, Other_015_054)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，删除数据进行localhash扫描，结束对账，核查数据
TEST_F(basic, Other_015_055)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = vertexLocalhash(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，删除数据进行localkey扫描，结束对账，核查数据
TEST_F(basic, Other_015_056)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART + 1, RECORDCOUNTEND, stmt, labelName, primarykey);
    vertexLocalkey(
        stmt, labelName, vertexLabel, RECORDCOUNTSTART, RECORDCOUNTSTART + 1);  // ：兼容v3local扫描左右值不能相等
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，删除数据进行hashcluster扫描，结束对账，核查数据
TEST_F(basic, Other_015_057)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = vertexHashcluster(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，replace数据进行全表扫描，结束对账，核查数据
TEST_F(basic, Other_015_058)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，replace数据进行localhash扫描，结束对账，核查数据
TEST_F(basic, Other_015_059)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = vertexLocalhash(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，replace数据进行localkey扫描，结束对账，核查数据
TEST_F(basic, Other_015_060)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexLocalkey(stmt, labelName, vertexLabel, RECORDCOUNTSTART, RECORDCOUNTEND);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，replace数据进行hashcluster扫描，结束对账，核查数据
TEST_F(basic, Other_015_061)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = vertexHashcluster(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，merge数据进行全表扫描，结束对账，核查数据
TEST_F(basic, Other_015_062)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexMerge(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，merge数据进行localhash扫描，结束对账，核查数据
TEST_F(basic, Other_015_063)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexMerge(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = vertexLocalhash(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，merge数据进行localkey扫描，结束对账，核查数据
TEST_F(basic, Other_015_064)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexMerge(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexLocalkey(stmt, labelName, vertexLabel, RECORDCOUNTSTART, RECORDCOUNTEND);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，merge数据进行hashcluster扫描，结束对账，核查数据
TEST_F(basic, Other_015_065)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexMerge(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = vertexHashcluster(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，truncate数据进行全表扫描，结束对账，核查数据
TEST_F(basic, Other_015_066)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，truncate数据进行localhash扫描，结束对账，核查数据
TEST_F(basic, Other_015_067)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexLocalhash(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，truncate数据进行localkey扫描，结束对账，核查数据
TEST_F(basic, Other_015_068)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexLocalkey(
        stmt, labelName, vertexLabel, RECORDCOUNTSTART, RECORDCOUNTEND);  // ：兼容v3local扫描左右值不能相等
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，truncate数据进行hashcluster扫描，结束对账，核查数据
TEST_F(basic, Other_015_069)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexHashcluster(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，同步执行批量写数据更新，结束对账，核查数据
TEST_F(basic, Other_015_070)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexBatchWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, conn, labelName);
    vertexBatchUpdate(RECORDCOUNTEND, stmt, conn, labelName, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，同步执行批量写数据更新删除，结束对账，核查数据
TEST_F(basic, Other_015_071)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexBatchWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, conn, labelName);
    vertexBatchUpdate(RECORDCOUNTEND * 2, stmt, conn, labelName, primarykey);
    vertexBatchDelete(RECORDCOUNTEND * 2, stmt, conn, labelName, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，异步执行批量写更新，结束对账，核查数据
TEST_F(basic, Other_015_072)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *schema = NULL;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char label_config[] = "{\"max_record_count\":3000000}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schema/schema_datatype.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, label_config, create_vertex_label_callback, &g_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    ret = testWaitAsyncRecv(&g_data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, g_data.status);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, g_data.status);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWriteAsync(RECORDCOUNTSTART, RECORDCOUNTEND, g_stmt_async, labelName);
    vertexUpdateAsync(RECORDCOUNTEND, g_stmt_async, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据开启对账，异步执行批量写更新删除，结束对账，核查数据
TEST_F(basic, Other_015_073)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *schema = NULL;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char label_config[] = "{\"max_record_count\":3000000}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schema/schema_datatype.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, label_config, create_vertex_label_callback, &g_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    ret = testWaitAsyncRecv(&g_data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, g_data.status);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, g_data.status);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWriteAsync(RECORDCOUNTSTART, RECORDCOUNTEND, g_stmt_async, labelName);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdateAsync(RECORDCOUNTEND, g_stmt_async, primarykey);
    // vertexDeleteAsync(RECORDCOUNTEND, g_stmt_async, vertexLabel, primarykey); // ：异步删除表，报错104004
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 全表订阅，无数据对账
TEST_F(basic, Other_015_074)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 全表订阅，写数据，开启对账，结束对账
TEST_F(basic, Other_015_075)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    // EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND * 2);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTEND * 2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 全表订阅，写数据，开启对账，truncate数据，结束对账
TEST_F(basic, Other_015_076)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 只订阅老化事件，写数据，开启对账，结束对账
TEST_F(basic, Other_015_077)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND * 2);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTEND * 2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，开启对账，执行写更新删除操作，结束对账
TEST_F(basic, Other_015_078)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    // DML
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，开启对账，执行写更新删除replace与merge操作，结束对账
TEST_F(basic, Other_015_079)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    // DML
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    // vertexMerge(RECORDCOUNTEND, RECORDCOUNTEND, stmt, isSub);//merge是否触发replace推送
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_REPLACE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND * 2);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTEND * 2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，订阅，执行写更新删除操作，结束对账
TEST_F(basic, Other_015_080)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // DML
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，订阅，执行写更新删除replace与merge操作，结束对账
TEST_F(basic, Other_015_081)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // DML
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexReplace(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_REPLACE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexMerge(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_MERGE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，写数据，开启对账，更新版本号，结束对账
TEST_F(basic, Other_015_082)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    // DML
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    char *primarykey = (char *)"pk";
    int i = 9;  // i=0等于0时释放连接core
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, primarykey);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, (RECORDCOUNTEND - 1) * 2);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, (RECORDCOUNTEND - 1) * 2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// path订阅，写数据，开启对账，结束对账
TEST_F(basic, Other_015_083)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *edgeName = (char *)"edge_1";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";
    char *labelNameVertex2 = (char *)"T8";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_2.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEdgeLabel((char *)"schema/edge_1.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertex2Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelNameVertex2);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelNameVertex2, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelNameVertex2, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelNameVertex2);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND * 2);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTEND * 2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// path订阅，写数据，开启对账，truncate数据，结束对账
TEST_F(basic, Other_015_084)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *edgeName = (char *)"edge_1";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";
    char *labelNameVertex2 = (char *)"T8";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_2.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertex2Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelNameVertex2);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelNameVertex2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// path订阅，开启对账，执行写更新操作，结束对账
TEST_F(basic, Other_015_085)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *edgeName = (char *)"edge_1";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";
    char *labelNameVertex2 = (char *)"T8";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_2.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEdgeLabel((char *)"schema/edge_1.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelNameVertex2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    vertex2Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelNameVertex2);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，path订阅，执行写更新操作，结束对账
TEST_F(basic, Other_015_086)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *edgeName = (char *)"edge_1";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";
    char *labelNameVertex2 = (char *)"T8";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_2.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEdgeLabel((char *)"schema/edge_1.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // check
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    // dml
    vertex2Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelNameVertex2);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 条件订阅主键字段，写数据，开启对账，结束对账
TEST_F(basic, Other_015_087)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_condition_pk.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);
    g_subIndex = 0;
    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTEND*2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 条件订阅非主键字段，开启对账，执行dml操作，结束对账
TEST_F(basic, Other_015_088)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_condition_nopk.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);
    g_subIndex = 0;
    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTEND*2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，条件订阅主键字段，执行dml操作，结束对账
TEST_F(basic, Other_015_089)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_condition_pk.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);
    g_subIndex = 0;
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，条件订阅非主键字段，执行dml操作，结束对账
TEST_F(basic, Other_015_090)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_condition_nopk.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);
    g_subIndex = 0;
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，条件订阅多个字段，执行dml操作，结束对账
TEST_F(basic, Other_015_091)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_condition_two.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 条件订阅多个字段，开启对账，执行dml操作，结束对账
TEST_F(basic, Other_015_092)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_condition_two.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// Kv表，写数据，开启对账，结束对账
TEST_F(basic, Other_015_093)
{
    int ret = 0;
    bool isAbnormal = false;
    uint32_t recordCount = 10;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    char tableName[128] = "kv";
    char configJson[128] = "{\"max_record_count\" : 10000}";
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = subKvWriteNoSub(recordCount, stmt, kvtable);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, tableName, FULLTABLE);
    // EXPECT_EQ(GMERR_OK, ret); //：报错59002，不及预期
    ret = GmcEndCheck(stmt, tableName, FULLTABLE, isAbnormal);
    // EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// Kv表，订阅，写数据，开启对账，结束对账
TEST_F(basic, Other_015_094)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    bool isAbnormal = false;
    uint32_t recordCount = 10;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    char *subName = (char *)"subKv";
    char tableName[128] = "kv";
    char configJson[128] = "{\"max_record_count\" : 10000}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, tableName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    ASSERT_EQ(GMERR_OK, ret);

    // ret = createSubscribe((char *)"schema/subinfo_kv.gmjson", subName, stmt, g_conn_sub, snKvCallback);
    ret = createSubscribe((char *)"schema/subinfo_kv.gmjson", subName, stmt, g_conn_sub, snKvCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = subKvWrite(recordCount, stmt, kvtable, GMC_SUB_EVENT_KV_SET);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_KV_SET, RECORDCOUNTEND);

    ret = GmcBeginCheck(stmt, tableName, FULLTABLE);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, tableName, FULLTABLE, isAbnormal);
    // EXPECT_EQ(GMERR_OK, ret);
    ageData(RECORDCOUNTSTART, RECORDCOUNTEND);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建namespace，执行写更新操作，开启对账，结束对账
TEST_F(basic, Other_015_095)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char *nameSpace = (char *)"nameSpace";
    char *nameSpace1 = (char *)"nameSpace1";
    char *userName = (char *)"user";
    char *userName1 = (char *)"user1";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create namespace
    for (int i = 0; i < 2; i++) {
        if (i % 2 == 0) {
            ret = GmcCreateNamespace(stmt, nameSpace, userName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcCreateNamespace(stmt, nameSpace1, userName1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
        vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
        ret = checkAccountStatus(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
        EXPECT_EQ(GMERR_OK, ret);
        ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建namespace，开启对账，执行写更新操作，结束对账
TEST_F(basic, Other_015_096)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char *nameSpace = (char *)"nameSpace";
    char *nameSpace1 = (char *)"nameSpace1";
    char *userName = (char *)"user";
    char *userName1 = (char *)"user1";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // create namespace
    for (int i = 0; i < 2; i++) {
        if (i % 2 == 0) {
            ret = GmcCreateNamespace(stmt, nameSpace, userName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcCreateNamespace(stmt, nameSpace1, userName1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
        vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
        vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
        ret = checkAccountStatus(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建namespace，订阅，执行写更新操作，开启对账，结束对账
TEST_F(basic, Other_015_097)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char *nameSpace = (char *)"nameSpace";
    char *nameSpace1 = (char *)"nameSpace1";
    char *userName = (char *)"user";
    char *userName1 = (char *)"user1";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    // create namespace
    for (int i = 0; i < 2; i++) {
        if (i % 2 == 0) {
            ret = GmcCreateNamespace(stmt, nameSpace, userName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcCreateNamespace(stmt, nameSpace1, userName1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
        EXPECT_EQ(GMERR_OK, ret);
        ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        g_subIndex = 0;
        vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
        vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
        ret = checkAccountStatus(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
        ageData(RECORDCOUNTSTART, RECORDCOUNTEND * 2);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTEND * 2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subNameAged);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建namespace，订阅，开启对账，执行写更新操作，结束对账
TEST_F(basic, Other_015_098)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char *nameSpace = (char *)"nameSpace";
    char *nameSpace1 = (char *)"nameSpace1";
    char *userName = (char *)"user";
    char *userName1 = (char *)"user1";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    // create namespace
    for (int i = 0; i < 2; i++) {
        if (i % 2 == 0) {
            ret = GmcCreateNamespace(stmt, nameSpace, userName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcCreateNamespace(stmt, nameSpace1, userName1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
        EXPECT_EQ(GMERR_OK, ret);
        ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
        vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
        vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
        ret = checkAccountStatus(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
        EXPECT_EQ(GMERR_OK, ret);
        vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
        ret = GmcUnSubscribe(stmt, subName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, subNameAged);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建多个namespace，订阅，开启对账，执行写更新操作，结束对账
TEST_F(basic, Other_015_099)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *subName = (char *)"subVertexLabel";
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char *nameSpace = (char *)"nameSpace";
    char *nameSpace1 = (char *)"nameSpace1";
    char *userName = (char *)"user";
    char *userName1 = (char *)"user1";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    // create namespace
    for (int i = 0; i < 2; i++) {
        if (i % 2 == 0) {
            ret = GmcCreateNamespace(stmt, nameSpace, userName);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcCreateNamespace(stmt, nameSpace1, userName1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt, nameSpace1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
        vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
        vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
        ret = checkAccountStatus(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
        EXPECT_EQ(GMERR_OK, ret);
        vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);
        ret = GmcUnSubscribe(stmt, subName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启事务，执行dml操作，开启对账，结束对账，提交事务
TEST_F(basic, Other_015_100)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，开启事务，执行dml操作，提交事务，结束对账
TEST_F(basic, Other_015_101)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // 开启事务
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，开启事务，执行dml操作，结束对账，提交事务
TEST_F(basic, Other_015_102)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *labelName = (char *)"schema_datatype";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // 开启事务
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 预制数据，开启对账，开启事务，回滚事务，结束对账
TEST_F(basic, Other_015_103)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = true;
    char *labelName = (char *)"schema_datatype";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // 开启事务
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(3);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 预制数据，开启对账，开启事务，写入新数据，回滚事务，结束对账
TEST_F(basic, Other_015_104)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // 开启事务
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, labelName);
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，开启对账，开启事务，执行dml操作，提交事务，结束对账
TEST_F(basic, Other_015_105)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *primarykey = (char *)"pk";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = system("gmsysview -q V\\$QRY_AGE_TASK -p XXpwd -e RTOS -u XXuser -s usocket:/run/verona/unix_emserver");
    // EXPECT_EQ(GMERR_OK, ret);
    // 开启事务
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = system("gmsysview -q V\\$QRY_AGE_TASK -p XXpwd -e RTOS -u XXuser -s usocket:/run/verona/unix_emserver");
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 使用gmimport工具导入表，写数据，开启对账，结束对账
TEST_F(basic, Other_015_106)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *cmdType = (char *)"vschema";
    char *labelName = (char *)"schema_datatype";
    char *schemaFile = (char *)"./schema/schema_datatype.gmjson";

    ret = toolModelOperation(GMIMPORT, cmdType, schemaFile, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = system("gmsysview -q V\\$QRY_AGE_TASK -p XXpwd -e RTOS -u XXuser -s usocket:/run/verona/unix_emserver");
    // EXPECT_EQ(GMERR_OK, ret); ：批跑cpp执行该指令报错

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 使用gmimport工具导入数据，开启对账，结束对账
TEST_F(basic, Other_015_107)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *cmdType = (char *)"vdata";
    char *labelName = (char *)"schema_datatype";
    char *primarykey = (char *)"pk";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = toolModelOperation(GMEXPORT, cmdType, NULL, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
    ret = toolModelOperation(GMIMPORT, cmdType, (char *)"schema_datatype.gmdata", NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，使用gmimport工具导入数据，结束对账
TEST_F(basic, Other_015_108)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *cmdType = (char *)"vdata";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 开始对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMIMPORT, cmdType, (char *)"schema_datatype.gmdata", NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 使用gmimport工具导入表与数据，开启对账，结束对账
TEST_F(basic, Other_015_109)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *cmdType = (char *)"vschema";
    char *cmdType1 = (char *)"vdata";
    char *labelName = (char *)"schema_datatype";
    char *schemaFile = (char *)"./schema/schema_datatype.gmjson";

    ret = toolModelOperation(GMIMPORT, cmdType, schemaFile, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = toolModelOperation(GMIMPORT, cmdType1, (char *)"schema_datatype.gmdata", NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 使用gmimport工具导入边，写数据，开启对账，结束对账
TEST_F(basic, Other_015_110)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *edgeName = (char *)"edge_1";
    char *cmdType = (char *)"eschema";
    char *labelName = (char *)"schema_datatype";
    char *labelNameVertex2 = (char *)"T8";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/vertex_2.gmjson", stmt, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEdgeLabel((char *)"schema/edge_1.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    vertex2Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelNameVertex2);
    // 开始对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    ret = GmcDropGraphLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，写入新数据，结束对账，使用gmexport工具导出未老化数据
TEST_F(basic, Other_015_111)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *cmdType = (char *)"vdata";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTEND, RECORDCOUNTEND * 2, stmt, labelName);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMEXPORT, cmdType, NULL, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建kv表，写数据，开启对账，结束对账
TEST_F(basic, Other_015_112)
{
    int ret = 0;
    bool isAbnormal = false;
    uint32_t recordCount = 10;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    char tableName[128] = "kv";
    char configJson[128] = "{\"max_record_count\" : 10000}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, tableName, configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = subKvWriteNoSub(recordCount, stmt, kvtable);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, tableName, FULLTABLE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcEndCheck(stmt, tableName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写大量数据，订阅，对账，结束对账，推送老化数据将订阅通道占满
// ：2022.01.22 客户端移除设置订阅通道内存上限的接口
// https://codehub-y.huawei.com/Gauss/GMDB/GMDBV5/merge_requests/19865
TEST_F(basic, Other_015_113)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = system("gmsysview -q V\\$QRY_DML_OPER_STATIS -p XXpwd -e RTOS -u XXuser -s
    // usocket:/run/verona/unix_emserver"); EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_aged.gmjson", subNameAged, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(
        stmt, labelName, GMC_OPERATION_INSERT);  // open table 在下发订阅关系之前会导致接收的消息异常
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);  // 注意回调会清0
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$QRY_DML_OPER_STATIS", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    /* ret = system("gmsysview -q V\\$QRY_DML_OPER_STATIS -p XXpwd -e RTOS -u XXuser -s
    usocket:/run/verona/unix_emserver"); EXPECT_EQ(GMERR_OK, ret); */
    // sleep(2);
    // g_subIndex = 0;
    // system("gmsysview -q TEST_T0 -p XXpwd -e RTOS -u XXuser -s usocket:/run/verona/unix_emserver");
    ageData(0, RECORDCOUNTEND * 2);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, RECORDCOUNTEND * 2);
    EXPECT_EQ(GMERR_OK, ret);

    // system("gmsysview -q V\\$DRT_CONN_STAT -p XXpwd -e RTOS -u XXuser -s usocket:/run/verona/unix_emserver");
    // system("gmsysview -q schema_datatype -p XXpwd -e RTOS -u XXuser -s usocket:/run/verona/unix_emserver");
    // system("gmsysview -q V\\$QRY_AGE_TASK -p XXpwd -e RTOS -u XXuser -s usocket:/run/verona/unix_emserver");

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 对账结束后对数据进行查询
TEST_F(basic, Other_015_114)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$QRY_AGE_TASK", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 对账，写数据，部分数据写入失败，结束对账
TEST_F(basic, Other_015_115)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"big_object";
    char stringValue[1500];

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/big_object.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1499] = '\0';
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWriteBigObject(RECORDCOUNTSTART, 140000, stmt, labelName, stringValue);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 连续开启两次对账
TEST_F(basic, Other_015_116)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TABLE_IN_CHECKING);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_TABLE_IN_CHECKING, ret);  // :预期需要报错待转测后适配
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启一次对账，结束两次对账
TEST_F(basic, Other_015_117)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TABLE_NOT_IN_CHECKING);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);

    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_TABLE_NOT_IN_CHECKING, ret);  // :预期需要报错待转测后适配
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 未开启对账，使用更新版本号接口
TEST_F(basic, Other_015_118)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TABLE_NOT_IN_CHECKING);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    char *filter = (char *)"pk";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_TABLE_NOT_IN_CHECKING, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 表不存在，开启对账
TEST_F(basic, Other_015_119)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，部分数据写失败，开启对账，结束对账
TEST_F(basic, Other_015_120)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"big_object";
    char stringValue[1500];
#if defined TEST_STATIC_ASAN
#define num 5000
#else
#define num 50000
#endif
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/big_object.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1499] = '\0';
    vertexWriteBigObject(RECORDCOUNTSTART, num, stmt, labelName, stringValue);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$QRY_AGE_TASK", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，写数据，部分数据写失败，结束对账
TEST_F(basic, Other_015_121)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"big_object";
    char stringValue[1500];
#if defined TEST_STATIC_ASAN
#define num 1400
#else
#define num 140000
#endif
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/big_object.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1499] = '\0';
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWriteBigObject(RECORDCOUNTSTART, num, stmt, labelName, stringValue);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，开启对账，写数据，部分数据写失败，结束对账
TEST_F(basic, Other_015_122)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"big_object";
    char *subName = (char *)"subVertexLabelBig";
    char stringValue[1500];

#if defined TEST_STATIC_ASAN
#define num 1400
#else
#define num 140000
#endif
    g_maxRecordCount = num;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/big_object.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_big.gmjson", subName, stmt, g_conn_sub, vertexBigSnCallback);
    EXPECT_EQ(GMERR_OK, ret);

    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1499] = '\0';
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWriteBigObject(RECORDCOUNTSTART, num, stmt, labelName, stringValue);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    printf("g_maxRecordCount=%d\n", g_maxRecordCount);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, g_maxRecordCount);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，使用非主键索引更新版本号，结束对账
TEST_F(basic, Other_015_123)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    const char *filter = (char *)"hashcluster";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, filter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = vertexFullScan(stmt, labelName, vertexLabel, RECORDCOUNTSTART);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账，结束对账使用不同的stmt
TEST_F(basic, Other_015_124)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt1, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
}

void vertexWriteSame(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        uint32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        int16_t F3 = 1;
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        EXPECT_EQ(GMERR_OK, ret);
        uint16_t F4 = 1;
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &F4, sizeof(F4));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F5 = 1;
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &F5, sizeof(F5));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F6 = 1;
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
        EXPECT_EQ(GMERR_OK, ret);
        int64_t F7 = 1;
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT64, &F7, sizeof(F7));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t F8 = 1;
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UINT64, &F8, sizeof(F8));
        EXPECT_EQ(GMERR_OK, ret);
        int F9 = 1;
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT32, &F9, sizeof(F9));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t F2 = 1;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT8, &F2, sizeof(F2));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

// 循环，不断写主键1-10000相同的值，开启结束对账
TEST_F(basic, Other_015_125)
{
    int ret = 0;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 2; i++) {
        vertexWriteSame(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多线程，多表订阅对账dml
TEST_F(basic, Other_015_126)
{
    int ret = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    char *cmdType = (char *)"vschema";
    char *schemaFile = (char *)"./schema/schema_datatype.gmjson";
    char *schemaFile_2 = (char *)"./schema/vertex_2.gmjson";
    pthread_t thr_arr[5];
    void *thr_ret[5];

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);

    ret = toolModelOperation(GMIMPORT, cmdType, schemaFile, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMIMPORT, cmdType, schemaFile_2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[0], NULL, threadVertexWrite, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, threadVertex2Write, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, threadVertexSub, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[3], NULL, threadVertexAged, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(thr_arr[0], &thr_ret[0]);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(thr_arr[1], &thr_ret[1]);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(thr_arr[2], &thr_ret[2]);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(thr_arr[3], &thr_ret[3]);
    ASSERT_EQ(GMERR_OK, ret);

    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, (char *)"schema_datatype");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, (char *)"T8");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 循环开启结束对账1000次，执行过程中根据localhash索引更新
TEST_F(basic, Other_015_127)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *localhashkey = (char *)"localhash";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // DML
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    // 开启对账
    for (int i = 0; i < 1000; i++) {
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexLocalhashUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localhashkey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char command[512];
    snprintf(command, 512, "%s/gmsysview record %s", g_toolPath, "schema_datatype");
    system(command);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 循环开启结束对账1000次，执行过程中根据hashcluster索引更新
TEST_F(basic, Other_015_128)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *hashclusterkey = (char *)"hashcluster";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // DML
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    // 开启对账
    for (int i = 0; i < 1000; i++) {
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexHashclusterUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, hashclusterkey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 循环开启结束对账1000次，执行过程中根据localkey索引更新
TEST_F(basic, Other_015_129)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *localkey = (char *)"hashcluster";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // DML
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    // 开启对账
    for (int i = 0; i < 1000; i++) {
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexHashclusterUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, localkey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 循环写对账结束对账，对账中加10s延时
TEST_F(basic, Other_015_130)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    for (int i = 0; i < 20; i++) {
        vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
        sleep(2);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建多个订阅关系，反复执行写更新删除对账
TEST_F(basic, Other_015_131)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *primarykey = (char *)"pk";
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo.gmjson", subName, stmt, g_conn_sub, vertexNoUserSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_old.gmjson", subNameAged, stmt, g_conn_sub, vertexNoUserSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    // 开启对账
    for (int i = 0; i < 20; i++) {
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
        vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
        vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, primarykey);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写满内存，开启对账
TEST_F(basic, Other_015_132)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"big_object";
    char stringValue[1500];
    
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/big_object.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1499] = '\0';
    vertexWriteBigObject(RECORDCOUNTSTART, 140000, stmt, labelName, stringValue);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启事务，写满内存，开启对账
TEST_F(basic, Other_015_133)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"big_object";
    char stringValue[1500];
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/big_object.gmjson", stmt, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1499] = '\0';
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWriteBigObject(
        RECORDCOUNTSTART, 140000, stmt, labelName, stringValue);  // ：开启事务，写满内存，开启对账报错108000
    if (g_maxRecordCount == 100000) {
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);
    }
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多个连接操作同一张表的对账
TEST_F(basic, Other_015_134)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt1, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账 子节点的更新，结束对账
TEST_F(basic, Other_015_135)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int ret = 0;
    int recordCount = 2;
    int array_num = 3;
    int vector_num = 3;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *config = NULL;
    char *primaryKey = (char *)"TEST_PK";
    char *labelName = (char *)"TEST_T0";
    char string1[] = "string1";
    char string2[] = "string1";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/TreeModel_001.gmjson", stmt, config);
    EXPECT_EQ(GMERR_OK, ret);

    subTreeWrite(stmt, labelName, recordCount, 0, 0, string1, array_num, vector_num);
    ret = GmcBeginCheck(stmt, labelName, 1);  // :报错111000
    EXPECT_EQ(GMERR_OK, ret);

    subTreeUpdate(stmt, labelName, recordCount, 1, 0, string2, array_num, vector_num, primaryKey);
    ret = GmcEndCheck(stmt, labelName, 1, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据后老化数据，老化的推送将订阅通道占满，过段时间后不阻塞通道，执行老化任务，老化任务结束后，查看内存回收情况
TEST_F(basic, Other_015_136)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"big_object";
    char stringValue[1500];

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/big_object.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *schema = NULL;
    readJanssonFile((char *)"schema/subinfo_aged_big.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = (char *)"subOnlyAged";
    tmp_schema.configJson = schema;
    ret = GmcSubscribe(stmt, &tmp_schema, g_conn_sub, vertexNoUserSnCallback, g_userData);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$QRY_DML_OPER_STATIS", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[1499] = '\0';
    g_maxRecordCount = 100000;
    vertexWriteBigObject(RECORDCOUNTSTART, g_maxRecordCount, stmt, labelName, stringValue);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    int time = 0;
    while (1) {
        time++;
        sleep(4);
        ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$QRY_AGE_TASK", NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        if (time > 20) {
            break;
        }
    }
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_AGED, g_maxRecordCount);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$QRY_DML_OPER_STATIS", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subOnlyAged");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 使用32k大对象建表写数据
TEST_F(basic, Other_015_137)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *labelName = (char *)"big_object";
    char stringValue[4096];

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_32k.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[4095] = '\0';
    vertexWrite32K(RECORDCOUNTSTART, RECORDCOUNTSTART + 1, stmt, labelName, stringValue);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅推key，执行dml操作
TEST_F(basic, Other_015_138)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *subName = (char *)"subVertexLabel";
    char *subNameAged = (char *)"subOnlyAged";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createSubscribe((char *)"schema/subinfo_key.gmjson", subName, stmt, g_conn_sub, vertexSnCallback);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, RECORDCOUNTEND);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_LABEL_SUBS_INFO", NULL, NULL);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subNameAged);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

int vertexSetLpmIndex(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t vr_id = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vrf_index = 1;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t dest_ip_addr = i;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(dest_ip_addr));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t mask_len = 32;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int lpmScan(GmcStmtT *stmt, uint32_t i, char *key, uint32_t expect_count = 1)
{
    int ret = 0;
    bool isFinish = false;
    uint32_t scan_count = 0;
    ret = vertexSetLpmIndex(stmt, i);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    while (isFinish == false) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_count, scan_count);
    GmcFreeIndexKey(stmt);
    return ret;
}

// 写数据，开启对账，进行lpm4扫描，结束对账，核查数据
TEST_F(basic, Other_015_139)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *key = (char *)"lpm4";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_lpm4.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexLpm4Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // 根据lpm索引获取记录数
    uint64_t result = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = RECORDCOUNTSTART; i < RECORDCOUNTEND; i++) {
        ret = vertexSetLpmIndex(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexCount(stmt, labelName, "lpm4", &result);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(result, RECORDCOUNTEND);
        GmcFreeIndexKey(stmt);
        ret = lpmScan(stmt, i, key);
        ASSERT_EQ(GMERR_OK, ret);
    }
    bool isAbnormal = false;
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

int vertexSetLpm6Index(GmcStmtT *stmt, uint32_t i, char *string1)
{
    int ret = 0;
    uint32_t vr_id = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vrf_index = 1;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, string1, 16);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t mask_len = 32;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int lpm6Scan(GmcStmtT *stmt, uint32_t i, char *key, char *string, uint32_t expect_count = 1)
{
    int ret = 0;
    bool isFinish = false;
    uint32_t scan_count = 0;
    ret = vertexSetLpm6Index(stmt, i, string);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, key);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    while (isFinish == false) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_count, scan_count);
    GmcFreeIndexKey(stmt);
    return ret;
}

// 写数据，开启对账，进行lpm6扫描，结束对账，核查数据
TEST_F(basic, Other_015_140)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char string[] = "string1string123";
    char *key = (char *)"lpm6";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_lpm6.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexLpm6Write(RECORDCOUNTSTART, 1, stmt, labelName, string);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // 根据lpm索引获取记录数
    uint64_t result = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = RECORDCOUNTSTART; i < 1; i++) {
        ret = vertexSetLpm6Index(stmt, i, string);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexCount(stmt, labelName, "lpm6", &result);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(result, 1);
        GmcFreeIndexKey(stmt);
        ret = vertexSetLpm6Index(stmt, i, string);
        ASSERT_EQ(GMERR_OK, ret);
        ret = lpm6Scan(stmt, i, key, string);
        ASSERT_EQ(GMERR_OK, ret);
    }
    bool isAbnormal = false;
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，结束对账，进行lpm4扫描
TEST_F(basic, Other_015_141)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *key = (char *)"lpm4";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_lpm4.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexLpm4Write(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    bool isAbnormal = false;
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    // 根据lpm索引获取记录数
    uint64_t result = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = RECORDCOUNTSTART; i < RECORDCOUNTEND; i++) {
        ret = vertexSetLpmIndex(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexCount(stmt, labelName, "lpm4", &result);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(result, RECORDCOUNTSTART);
        GmcFreeIndexKey(stmt);
        ret = lpmScan(stmt, i, key, RECORDCOUNTSTART);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写数据，开启对账，进行lpm6扫描，结束对账，核查数据
TEST_F(basic, Other_015_142)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char string[] = "string1string123";
    char *key = (char *)"lpm6";
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_lpm6.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    vertexLpm6Write(RECORDCOUNTSTART, 1, stmt, labelName, string);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    bool isAbnormal = false;
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    // 根据lpm索引获取记录数
    uint64_t result = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = RECORDCOUNTSTART; i < 1; i++) {
        ret = vertexSetLpm6Index(stmt, i, string);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexCount(stmt, labelName, "lpm6", &result);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(result, 0);
        GmcFreeIndexKey(stmt);
        ret = vertexSetLpm6Index(stmt, i, string);
        ASSERT_EQ(GMERR_OK, ret);
        ret = lpm6Scan(stmt, i, key, string, RECORDCOUNTSTART);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启对账, 写入数据, 结束对账, 核查数据
TEST_F(basic, Other_015_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, 128, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype_rel.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int count = 2;
    for (int i = 0; i < 5; i++) {
        ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
        EXPECT_EQ(GMERR_OK, ret);
        vertexWrite(i * count, i * count + count, stmt, labelName);
        ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    sleep(1);
    char cmd[1024] = {0};
    const char *viewName = "V\\$STORAGE_VERTEX_COUNT";
    (void)snprintf(cmd, 1024, "%s/gmsysview -q %s", g_toolPath, viewName);
    ret = executeCommand(cmd, "record count: 2");
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview record schema_datatype"); // 只看到第8/9条
    memset(cmd, 0, sizeof(cmd));
    
    // 异常对账
    vertexWrite(10, 20, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, true);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    (void)snprintf(cmd, 1024, "%s/gmsysview -q %s", g_toolPath, viewName);
    ret = executeCommand(cmd, "record count: 12");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

TEST_F(basic, DISABLED_test)
{
    int ret = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    char *cmdType = (char *)"vschema";
    char *schemaFile = (char *)"./schema/schema_datatype.gmjson";
    char *schemaFile_2 = (char *)"./schema/vertex_2.gmjson";
    pthread_t thr_arr[5];
    void *thr_ret[5];

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);

    ret = toolModelOperation(GMIMPORT, cmdType, schemaFile, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = toolModelOperation(GMIMPORT, cmdType, schemaFile_2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[0], NULL, threadVertexWrite, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, threadVertex2Write, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[2], NULL, threadVertexSub, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[3], NULL, threadVertexAged, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(thr_arr[0], &thr_ret[0]);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(thr_arr[1], &thr_ret[1]);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(thr_arr[2], &thr_ret[2]);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(thr_arr[3], &thr_ret[3]);
    ASSERT_EQ(GMERR_OK, ret);

    ret = toolModelOperation(GMSYSVIEW, (char *)"V\\$CATA_VERTEX_LABEL_CHECK_INFO", NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, (char *)"schema_datatype");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, (char *)"T8");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *updateCheck(void *arg)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    char *primarykey = (char *)"pk";
    int i = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, primarykey);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    vertexPkRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, vertexLabel, primarykey);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

TEST_F(basic, DISABLED_thread)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *vertexLabel = NULL;
    bool isAbnormal = false;
    char *labelName = (char *)"schema_datatype";
    pthread_t thr_arr[5];
    void *thr_ret[5];

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_datatype.gmjson", stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 5; i++) {
        ret = pthread_create(&thr_arr[i], NULL, updateCheck, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 5; i++) {
        ret = pthread_join(thr_arr[i], &thr_ret[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
static const char *gResPoolName = "ResourcePool001";
static const uint64_t gResPoolId = 0;
static const uint64_t gResStartId = 0;
static const uint64_t gResCapacity = 1000000;
static const char *gResPoolConfigJson =
    R"({
        "name" : "ResourcePool001",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";
static const char *gResPoolExtendedName = "resource_pool_extended";
static const char *gResPoolExternal =
    R"({
        "name" : "resource_pool_extended",
        "pool_id" : 65535,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 0,
        "alloc_type" : 0
    })";
// 写数据 开启对账，结束对账
TEST_F(basic, Other_015_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    int ret = 0;
    int recordCount = 16;
    int array_num = 3;
    int vector_num = 3;
    bool isAbnormal = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *config = NULL;
    char *primaryKey = (char *)"TEST_PK";
    char *labelName = (char *)"TEST_T0";
    char string1[] = "string1";
    char string2[] = "string1";

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建带有资源字段和bitfieldzi字段的表
    ret = createVertexLabel((char *)"schema/TreeModel_002.gmjson", stmt, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建资源池
    GmcDestroyResPool(stmt, gResPoolName);
    GmcDestroyResPool(stmt, gResPoolExtendedName);
    ret = GmcCreateResPool(stmt, gResPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateResPool(stmt, gResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 绑定资源池
    ret = GmcBindResPoolToLabel(stmt, gResPoolName, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindExtResPool(stmt, gResPoolName, gResPoolExtendedName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写带有资源字段的数据
    AllTypeTreeWrite(stmt, labelName, recordCount, 0, 0, string1, array_num, vector_num);
    system("gmsysview count TEST_T0");
    uint8_t partId = 0;
    for (uint8_t i = 0; i < recordCount; i++) {
        partId = i;
        ret = GmcBeginCheck(stmt, labelName, partId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, labelName, partId, isAbnormal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    system("gmsysview -q V\\$QRY_AGE_TASK");
    int32_t cycle = 100;
    int partNum = 10;
    int finishStatus = 11;
    char viewCmd1[100] = "gmsysview -q V\\$QRY_AGE_TASK -f LABEL_NAME=TEST_T0|grep 'PARTITION_ID'|wc -l";
    char viewCmd2[100] = "gmsysview -q V\\$QRY_AGE_TASK -f LABEL_NAME=TEST_T0|grep 'TASK_STATUS: FINISHED'|wc -l";
    while (cycle > 0) {
        TestGetResultCommand(viewCmd1, &partNum);
        TestGetResultCommand(viewCmd2, &finishStatus);
        cycle--;
        if (partNum == finishStatus) {
            break;
        }
        sleep(1);
    }
    // 老化任务全部结束再删表
    AW_MACRO_EXPECT_EQ_INT(partNum, finishStatus);
    ret = GmcUnbindResPoolFromLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    ret = GmcUnbindExtResPool(stmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(stmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(stmt, gResPoolExtendedName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char*)"gmsysview count TEST_T0","0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
