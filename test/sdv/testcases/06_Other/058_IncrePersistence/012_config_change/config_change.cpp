/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 配置变更持久化重启测试
 * Author: sunyiwei
 * Create: 2023-12-21
 */


#include "../common/incre_pst_common.h"

int g_beginIndex = 0;
int g_endIndex = 40000;

class ConfigChange : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        char *pwdDir = getenv("PWD");
        if (pwdDir == NULL) {
            printf("get env PWD fail.\n");
        }
        (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
        (void)Rmdir(g_dbFilePath);
        int ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        system("sh $TEST_HOME/tools/stop.sh -f");
        ret = ChangeGmserverCfg((char *)"recover", NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"workerHungThreshold", (char *)"20,299,300");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        system("sh $TEST_HOME/tools/start.sh -f");
        ret = testEnvInit(-1, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
    }
};

void ConfigChange::SetUp()
{
    printf("[INFO] ConfigChange Start.\n");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void ConfigChange::TearDown()
{
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    printf("[INFO] ConfigChange End.\n");
}

char g_indexName[] = "primary_key";
char g_cond[] = "vr_id(uint32)=%i{0}";

char *g_ip4Schema = (char *)R"(
    [{
    "version": "2.0", "type": "record", "name": "lable1",
    "fields": [
        { "name": "vr_id", "type": "uint32"},
        { "name": "vrf_index", "type": "uint32"},
        { "name": "dest_ip_addr", "type": "uint32"},
        { "name": "mask_len", "type": "uint8"},
        { "name": "other", "type": "string"}
    ],
    "keys": [
        { "name": "primary_key", "index": { "type": "primary" },
            "node": "ip4forward_test",
            "fields": [ "vr_id" ],
            "constraints": { "unique": true }
        }
    ]
}]
)";

char *g_tableName = (char *)"lable1";

int DoAccess()
{
    int ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    char strValue[3900] = {0};
    (void)memset(strValue, 'A', sizeof(strValue) - 1);
    for (int i = g_beginIndex; i < g_endIndex; i++) {
        uint32_t vr_id = i;
        ret = GmcSetVertexProperty(g_stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint32_t vrf_index = i + 2;
        ret = GmcSetVertexProperty(g_stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint32_t dest_ip_addr = i + 3;
        ret = GmcSetVertexProperty(g_stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint8_t mask_len = i % 127;
        ret = GmcSetVertexProperty(g_stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(g_stmt, "other", GMC_DATATYPE_STRING, strValue, sizeof(strValue) - 1);
        RETURN_IFERR(ret);
        ret = GmcExecute(g_stmt);
        RETURN_IFERR(ret);
    }
    return T_OK;
}

// 重启前变更deviceSize
TEST_F(ConfigChange, Other_058_012_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"deviceSize", (char *)"16");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");

    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重启前变更pageSize
TEST_F(ConfigChange, Other_058_012_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"pageSize", (char *)"16");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");

    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重启前变更maxSeMem
TEST_F(ConfigChange, Other_058_012_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"maxSeMem", (char *)"64");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");

    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重启前变更persistentMode
TEST_F(ConfigChange, Other_058_012_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
 
    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
 
 
// 重启前变更redoPubBufSize
TEST_F(ConfigChange, Other_058_012_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoPubBufSize", (char *)"256");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
 
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoPubBufSize", (char *)"16384");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
 
// 重启前变更redoBufParts
TEST_F(ConfigChange, Other_058_012_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoBufParts", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoBufParts", (char *)"16");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重启前变更redoFlushCheckPeriod
TEST_F(ConfigChange, Other_058_012_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoFlushCheckPeriod", (char *)"10");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoFlushCheckPeriod", (char *)"300000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重启前变更redoFileSize
TEST_F(ConfigChange, Other_058_012_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"1024");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重启前变更redoFileCount
TEST_F(ConfigChange, Other_058_012_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoFileCount", (char *)"2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"redoFileCount", (char *)"64");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重启前变更dataFileMaxCount
TEST_F(ConfigChange, Other_058_012_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表跑业务操作
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_ip4Schema, g_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DoAccess();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"dataFileMaxCount", (char *)"3");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 修改配置
    ret = ChangeGmserverCfg((char *)"dataFileMaxCount", (char *)"1024");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
 
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    //  验证数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_indexName, g_cond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
 
