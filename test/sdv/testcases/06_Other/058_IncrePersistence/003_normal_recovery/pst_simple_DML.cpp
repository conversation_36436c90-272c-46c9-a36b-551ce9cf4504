/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: 增量持久化  简单表DML
 * 【 简单表】
 * 插入数据，delete部分数据，调用GmcFlushData，重启，校验记录数，全表扫描并校验记录
 * 插入数据，merge部分数据，调用GmcFlushData，重启，全表扫描并校验记录
 * 插入数据，replace部分数据，调用GmcFlushData，重启，全表扫描并校验记录
 * 插入数据，update部分数据，调用GmcFlushData，重启，全表扫描并校验记录
 * 插入数据，调用GmcFlushData，重启，全表扫描并校验记录
 * 补充用例1 删记录使用GmcTruncateVertexLabel接口
 * 补充用例2 建表写入数据删表，反复操作多次
 * 补充用例3 刷盘数据与DML操作顺序打乱
 * Author: pwx860460
 * Create: 2023-08-19
 * History:
 */

#include "../../053_OnDemandPersistence/Persistence_common.h"

class PstSimpleDML : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        
    }

    static void TearDownTestCase()
    {
        
    };
public:
    virtual void SetUp();
    virtual void TearDown();
};
void PstSimpleDML::SetUp()
{
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void PstSimpleDML::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
    system("sh $TEST_HOME/tools/stop.sh -f");
}

// 插入数据，delete部分数据，调用GmcFlushData，重启，校验记录数，全表扫描并校验记录
TEST_F(PstSimpleDML, Other_058_003_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删除数据
    count=100;
    ret = VlSimpleDeleteByIndex(g_stmt, start, count, "PrimaryKey");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查数据恢复情况
    uint64_t scanCount;
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = VlSimpleScanFullTable(g_stmt, 0, count, 0, GMC_ORDER_ASC, &scanCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(900, scanCount);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 插入数据，merge部分数据，调用GmcFlushData，重启，全表扫描并校验记录
TEST_F(PstSimpleDML, Other_058_003_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // merge部分数据
    coefficient = 1;
    ret = VlSimpleMerge(g_stmt, start, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查数据恢复情况
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    EXPECT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 插入数据，replace部分数据，调用GmcFlushData，重启，全表扫描并校验记录
TEST_F(PstSimpleDML, Other_058_003_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // replace数据
    coefficient = 1, count = 500;
    ret = VlSimpleReplace(g_stmt, start, count, coefficient);
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查数据恢复情况
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 插入数据，update部分数据，调用GmcFlushData，重启，全表扫描并校验记录
TEST_F(PstSimpleDML, Other_058_003_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // update
    coefficient = 1;
    ret = VlSimpleUpdateByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查数据恢复情况
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 插入数据，调用GmcFlushData，重启，全表扫描并校验记录
TEST_F(PstSimpleDML, Other_058_003_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删除数据
    ret = VlSimpleDeleteByIndex(g_stmt, start, count, "PrimaryKey");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey", &scanCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(scanCount, 0);

    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查数据恢复情况
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    // 删除数据
    ret = VlSimpleDeleteByIndex(g_stmt, start, count, "PrimaryKey");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey", &scanCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(scanCount, 0);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 插入数据，delete部分数据，调用GmcFlushData，重启，校验记录数，全表扫描并校验记录
TEST_F(PstSimpleDML, Other_058_003_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删除数据
    ret = GmcTruncateVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t scanCount;
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey", &scanCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(scanCount, 0);

    ret = GmcFlushData(g_stmt, NULL, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查数据恢复情况
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = VlSimpleScanByIndex(g_stmt, start, count, coefficient, "PrimaryKey", &scanCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(scanCount, 0);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 建表写入数据删表，反复操作多次
TEST_F(PstSimpleDML, Other_058_003_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    int32_t repeat_count=30;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表删表 flush 多次
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    for(int32_t i = 1; i < repeat_count; i++){
        ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 落盘(PATH为NULL，使用默认路径落盘)
        ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
        EXPECT_EQ(GMERR_OK, ret);
        // 打乱顺序有异常
        ret = GmcFlushData(g_stmt, NULL, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查数据恢复情况
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 建表，调用GmcFlushData，重启，表和数据存在  ##打乱顺序有异常 数据对不上 
TEST_F(PstSimpleDML, Other_058_003_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表删表 flush 500ci
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    for(int32_t i = 1; i < 10; i++){
        ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
        EXPECT_EQ(GMERR_OK, ret);
        // 打乱顺序有异常
        // 落盘(PATH为NULL，使用默认路径落盘)
        ret = GmcFlushData(g_stmt, NULL, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查数据恢复情况
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t countApi, countFullTable, countPk;
    ret = GmcGetVertexCount(g_stmt, VL_SIMPLE_NAME, NULL, &countApi);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleScanFullTable(g_stmt, 0, 1000, 0, GMC_ORDER_ASC, &countFullTable);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleScanByIndex(g_stmt, 0, 1000, 0, "PrimaryKey", &countPk);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(countApi, 1000);
    AW_MACRO_ASSERT_EQ_INT(countApi, countFullTable);
    AW_MACRO_ASSERT_EQ_INT(countApi, countPk);

    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

