/*****************************************************************************
 Description  : 对账对象权限check   对账不校验对象权限  2021/08/03
 Notes        :
 History      :
 Author       : <PERSON><PERSON>hao 30021737
 Modification :
 Date         : 2025/4/8
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "TcpObjPrivsTest.h"
#include "TcpRegPolicy.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;

const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};

class TcpObjPrivsCheckFun : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 打开鉴权模式
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        readJanssonFile("config/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
        readJanssonFile("schema/NormalSubVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(config_json);
        free(normal_vertexlabel_schema);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TcpObjPrivsCheckFun::SetUp()
{
    // 导入白名单
    char allow_list_file[128] = "./allow_list/allow_list.gmuser";
    char sys_policy_file[128] = "./gmpolicy_file/SysPrivsAll.gmpolicy";
    AllowlistGmruleImport(allow_list_file, expectValue1);
    PolicyGmruleImport(sys_policy_file, expectValue1);

    int ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, g_ConnUser);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL,
        -1, 0, &g_epollData.userEpollFd, false, g_ConnUser);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void TcpObjPrivsCheckFun::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    char allow_list_file[128] = "./allow_list/allow_list.gmuser";
    char sys_policy_file[128] = "./gmpolicy_file/SysPrivsAll.gmpolicy";

    AllowlistGmruleremove(allow_list_file, expectValue1);
    PolicyGmruleRevoke(sys_policy_file, expectValue1);

    AW_CHECK_LOG_END();
}

// 001.Vertex对象权限Insert校验，同步插入，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsCheckFun, Other_093_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // before grant
    AccountCheck(g_normal_vertexlabel_name, g_normal_pk_name, GMC_FULL_TABLE, GMERR_FEATURE_NOT_SUPPORTED);

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // check account
    AccountCheck(g_normal_vertexlabel_name, g_normal_pk_name, GMC_FULL_TABLE, GMERR_FEATURE_NOT_SUPPORTED);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
}

// 002.导入delete对象权限，操作完整的分区对账流程
TEST_F(TcpObjPrivsCheckFun, Other_093_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 15;
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    char *labelJson = NULL;
    readJanssonFile("schema/PartitionVertexLabel.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, config_json);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(labelJson);

    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
}

// 003.无delete对象权限，调用GmcBeginCheck接口
TEST_F(TcpObjPrivsCheckFun, Other_093_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // before grant
    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    bool isAbornormal = (ret != GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_INSUFFICIENT_PRIVILEGE);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
}

// 004.无delete对象权限，调用GmcEndCheck接口
TEST_F(TcpObjPrivsCheckFun, Other_093_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // before grant
    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    bool isAbornormal = (ret != GMERR_OK);

    do {
        // 查询对账的状态
        GmcCheckInfoT *checkInfo;
        GmcCheckStatusE checkStatus;
        ret = GmcGetCheckInfo(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, &checkInfo);
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcGetCheckStatus(checkInfo, &checkStatus);
        if (ret != GMERR_OK) {
            break;
        }
        if (checkStatus != GMC_CHECK_STATUS_CHECKING) {
            break;
        }
        // 结束对账
        (void)GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    } while (0);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
}

// 005.无delete对象权限，调用GmcUpdateCheckVersion接口
TEST_F(TcpObjPrivsCheckFun, Other_093_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // before grant
    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    // refresh version
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE_VERSION);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t pk = 0;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_TABLE_NOT_IN_CHECKING, ret);

    bool isAbornormal = (ret != GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    PolicyGmruleImport(obj_policy_file2, expectValue1);

    ret = GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    AddWhiteList(GMERR_TABLE_NOT_IN_CHECKING);
}

// 006.无delete对象权限，调用GmcGetCheckInfo接口
TEST_F(TcpObjPrivsCheckFun, Other_093_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // before grant
    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    GmcCheckInfoT *checkInfo;
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckInfo(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, &checkInfo);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    PolicyGmruleImport(obj_policy_file2, expectValue1);

    ret = GmcGetCheckInfo(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, &checkInfo);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    bool isAbornormal = (ret != GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
}
