/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 073_alias
 * Author: swx703884
 * Create: 2024-02-22
 */
#ifndef ALIAS_TOOL_H
#define ALIAS_TOOL_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

#ifndef RUN_INDEPENDENT
#define FILEPATH "/opt/vrpv8/home/"
#else
#define FILEPATH "/root/data/"
#endif

void TestCheckValidateModelAsyncFail(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(1, checkData.failCount);

    printf(">>> module check\n");
    printf(">>> validateRes: %d\n", checkData.validateRes);
    printf(">>> failCount: %u\n", checkData.failCount);

    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}


// yang set T0层f0
void testYangSetVertexProperty_Fx(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype, const char *name)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), name, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


int TestYangSetFieldID(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType, GmcAttributeTypeE attrType = GMC_ATTRIBUTE_NAME)
{
    int ret = 0;
    int ret1 = 0;

    GmcAttributePropertyT attrProperty;
    attrProperty.type = attrType;
    attrProperty.size = size;
    attrProperty.value = value;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = (void *)&attrProperty;
    propValue.size = sizeof(attrProperty);
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

void TestYangSetIDName(GmcNodeT *node, const char *fieldName, const char* name, uint32_t size,
    GmcYangPropOpTypeE opType)
{
    int ret = 0;
    AW_MACRO_EXPECT_NE_INT(0, size);

    char nameSet[100] = {0};
    (void)memcpy_s(nameSet, size, name, size);
    ret = TestYangSetFieldID(node, GMC_DATATYPE_IDENTITY, nameSet, size, fieldName, opType, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetIDValue(GmcNodeT *node, const char *fieldName, int32_t value, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    int32_t valueSet = value;
    ret = TestYangSetFieldID(node, GMC_DATATYPE_IDENTITY, &valueSet, sizeof(int32_t), fieldName,
        opType, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetIDEnumValue(GmcNodeT *node, const char *fieldName, int32_t value, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    int32_t valueSet = value;
    ret =
        TestYangSetFieldID(node, GMC_DATATYPE_ENUM, &valueSet, sizeof(int32_t), fieldName, opType, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

void TestYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF2 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF3 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F5写入和默认值不同的值
    uint32_t valueF5 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF5, sizeof(uint32_t), "F5", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F6写入和默认值相同的值
    uint32_t valueF6 = 666;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF6, sizeof(uint32_t), "F6", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F7，F8，F9不写入值

    char valueF10[10] = {0};
    (void)snprintf(valueF10, sizeof(valueF10), "str00%d", i);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, valueF10, (strlen(valueF10)), "F10", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F11写入和默认值不同的值
    char valueF11[10] = "string";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, valueF11, (strlen(valueF11)), "F11", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F12写入和默认值相同的值
    char valueF12[10] = "default12";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, valueF12, (strlen(valueF12)), "F12", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F13不写入值
}


/******************************Subtree*******************************************/
// userData结构
struct subtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    AsyncUserDataT *data;
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};

// userData ：用户数据 replyJson ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    int ret;
    subtreeFilterCbParam *param = (subtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    if (GMERR_OK != status) {
        AW_FUN_Log(LOG_ERROR, "[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
    bool isEnd = false;
    bool isEqual = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ret = GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(true, isEnd);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    AW_MACRO_EXPECT_NOTNULL(jsonReply);
    printf("replyJson:\n%s\n", jsonReply[0]);

    if (param->expectReplyJson != NULL) {
        isEqual = testYangJsonIsEqual((const char*)jsonReply[0], param->expectReplyJson);
        if (isEqual == true) {
            AW_MACRO_EXPECT_EQ_INT(true, isEqual);
        } else {
            printf("expectJson:\n%s\n", param->expectReplyJson);
            AW_MACRO_EXPECT_EQ_INT(true, isEqual);
        }
    } else {
        AW_FUN_Log(LOG_ERROR, "[err] no replyjson   \n ");
    }
    param->step++;
    ((AsyncUserDataT *)(param->data))->recvNum = ((AsyncUserDataT *)(param->data))->recvNum + 1;
}

void TestSubtreeFilter(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED)
{
    int ret;

    // subtree 查询
    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);

    subtreeFilterCbParam data = {0};
    data.expectReplyJson = replyJson;
    data.expectStatus = GMERR_OK;
    data.step = 0;

    char filterPath[1024] = {0};
    ret = snprintf(filterPath, 1024, "SubTreeFilterJson/Yang_066_Func.json");
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *filterJson = NULL;
    readJanssonFile(filterPath, &filterJson);
    ASSERT_NE((void *)NULL, filterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = rootName;
    filter.subtree.json = filterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    AsyncUserDataT asyncData = { 0 };
    data.data = &asyncData;
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncSubtreeFilterCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(data.data, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.expectStatus);

    free(filterJson);
    free(replyJson);
    filterJson = NULL;
    replyJson = NULL;
}

/******************************Subtree obj模式*******************************************/
void TestSubtreeFilterObjAll(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED)
{
    int ret;

    GmcNodeT *rootNode = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

int checkAutoIndexView(int expectNum, const char *labelName = "ListOne")
{
    char tmpStr[128] = {0};
    (void)snprintf(tmpStr, 128, "%d", expectNum);

    // 检查表STORAGE_HASH_COLLISION_STAT
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_HASH_COLLISION_STAT -f LABEL_NAME=%s "
        "|grep \"AutoIndex_\"|wc -l", labelName);
    system("gmsysview -q V\\$STORAGE_HASH_COLLISION_STAT -f LABEL_NAME=ListOne |grep \"AutoIndex_\"");

    int ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -q V\\$STORAGE_HASH_COLLISION_STAT -f LABEL_NAME=%s |grep \"AutoIndex_\"", labelName);
    system(g_command);
    if (ret) {
        return ret;
    }

    // 检查表STORAGE_HASH_LINKLIST_INDEX_STAT
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f LABEL_NAME=%s "
        "|grep \"AutoIndex_\"|wc -l", labelName);
    system("gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f LABEL_NAME=ListOne |grep \"AutoIndex_\"");

    ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f LABEL_NAME=%s |grep \"AutoIndex_\"", labelName);
    system(g_command);
    if (ret) {
        return ret;
    }

    // 检查表元数据
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=%s "
        "|grep \"AutoIndex_\"|wc -l", labelName);
    ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=%s "
        "|grep \"AutoIndex_\"", labelName);
    system(g_command);
    if (ret) {
        return ret;
    }
}

char g_nspName[128] = {0};
int checkXpathMergeView(
    int expectNum, const char *xpathStr, const char *xpathType = "WHEN", const char *nsName = g_nspName)
{
    char tmpStr[128] = {0};
    (void)snprintf(tmpStr, 128, "%d", expectNum);

    // 检查表STORAGE_HASH_COLLISION_STAT
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO -f NAMESPACE_NAME=%s "
        "|grep %s -A 10 |grep \"%s\" |wc -l", nsName, xpathType, xpathStr);
    printf(">> %s\n", g_command);
    system(g_command);

    int ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}


int checkMemView(
    GmcStmtT *stmt, const char *expectStr, const char *sessionName)
{
    // 检查表COM_DYN_CTX
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "CTX_NAME='%s'", sessionName);
    int ret = TestRDScanSysview(stmt, "V$COM_DYN_CTX", g_command);
    EXPECT_EQ(GMERR_OK, ret);

    int tNum = 0;
    ret = TestRDGetSysviewRecordNum(stmt, &tNum);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">>> %s %d\n", sessionName, tNum);
    if (tNum == 0) {
        return GMERR_OK;
    }

    char tmpStr[128] = {0};
    (void)snprintf(tmpStr, 128, "%s", expectStr);

    // 检查表STORAGE_HASH_COLLISION_STAT
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='%s' ", sessionName);
    printf(">> %s\n", g_command);

    ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// ********************************* namespace *********************************
// 异步创建namespace
void createNameSpaceAsync(GmcStmtT *asyncStmt, const char *nameSpaceName, const char *userName)
{
    AsyncUserDataT data = {0};

    int ret;
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = nameSpaceName;
    nspCfg.userName = userName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(asyncStmt, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

int useNameSpaceAsync(GmcStmtT *asyncStmt, const char *nameSpaceName)
{
    AsyncUserDataT data = {0};
    int ret = GmcUseNamespaceAsync(asyncStmt, nameSpaceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return data.status;
}

void dropNameSpaceAsync(GmcStmtT *asyncStmt, const char *nameSpaceName)
{
    AsyncUserDataT data = {0};
    int ret = GmcDropNamespaceAsync(asyncStmt, nameSpaceName, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

// clearNsp方法
void testClearNsp(GmcStmtT *stmt, const char *namespaceName)
{
    AsyncUserDataT data = {0};
    int ret = GmcClearNamespaceAsync(stmt, namespaceName, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

GmcPersistenceConfigT GetPersistenceConfig(
    GmcPersistenceModeE mode, GmcDigestAlgorithmE algorithm, const char *dir, const char *password)
{
    GmcPersistenceConfigT config;
    config.algorithm = algorithm;
    config.mode = mode;
    config.dir = dir;
    config.password = password;
    return config;
}

void ConfigImportCallback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

void ConfigExportCallback(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback(userData, status, errMsg);
}

int CreateVertexAndEdge(const char *vertexPath, const char *edgePath)
{
    int ret = 0;

    // 建表 建边
    readJanssonFile(vertexPath, &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    if (!g_vertexschema) {
        return -1;
    }
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    free(g_vertexschema);
    RETURN_IFERR(ret);
    ret = testWaitAsyncRecv(&userData);
    RETURN_IFERR(ret);
    RETURN_IFERR(userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile(edgePath, &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    if (!g_edgeschema) {
        return -1;
    }
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    free(g_edgeschema);
    RETURN_IFERR(ret);
    ret = testWaitAsyncRecv(&userData);
    RETURN_IFERR(ret);
    RETURN_IFERR(userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    return ret;
}

int TestYangSetNodeStrField(GmcNodeT *node, GmcYangPropOpTypeE opType, const char * fieldName, uint32_t fieldSize,
    int nFlag = 1, char cInputMark = 'a')
{
    int ret = 0;

    // 写string数据
    char cMark = 'a' + (cInputMark % 26);

    string strValue = (string)fieldName + "_" + to_string(nFlag);
    while (strValue.length() < fieldSize) {
        strValue += cMark;
    }

    ret = TestYangSetField(node, GMC_DATATYPE_STRING, (char *)strValue.c_str(), strValue.length(), fieldName, opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}


int g_writeTimes = 0;
void AsyncFetchRetCb123(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    printf(">> reply write_time: %d status:%u\n", g_writeTimes, status);
    g_writeTimes++;
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    ASSERT_EQ(param->expectStatus, status) << errMsg;
    if (param->expectStatus != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_EQ((uint32_t)param->expectReply.size(), count);
    ASSERT_TRUE(yangTree != NULL);
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param->expectReply[i].c_str(), "");
            continue;
        }
        char *reply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[i], &reply));

        ofstream fout;
        (void)snprintf(g_command, MAX_CMD_SIZE, "./reply/%d.txt", g_writeTimes);
        fout.open(g_command);
        fout << reply << endl;
        fout.close();

        GmcYangFreeTree(yangTree[i]);
    }
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param->stmt, NULL, fetchRet, AsyncFetchRetCb123, param));
    return;
}


/******************************Subtree obj大对象模式*******************************************/
void TestSubtreeFilterBigObjAll(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED)
{
    int ret;

    system("mkdir -p reply; rm -rf reply/*");

    GmcNodeT *rootNode = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    std::vector<std::string> reply(1);

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncSubtreeRecv_API(&param, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/******************************diff*******************************************/

int g_diffWriteTime = 1;
void TestCheckYangTreeBatch(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        printf(">> reply diff_time: %d count：%u\n", g_diffWriteTime, count);
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "replyDiff/%d.txt", g_diffWriteTime++);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }

        // cout << "actual diff：\n" << res;
        // cout << "expect：\n" << expectReply[i].c_str();
        // ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiff_callbackBatch(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTreeBatch(userData1->stmt, yangTree, count, *userData1->expectDiff);

            AW_FUN_Log(LOG_INFO, "diff count is %d, userData1->lastExpectIdx is %d.",
                count, userData1->lastExpectIdx);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                    userData1->recvNum++;
                    GmcYangFreeFetchRet(fetchRet);
                    return;
                }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK,
                GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL, FetchDiff_callbackBatch, userData1));
            return;
        } else if (status == GMERR_NO_DATA) {
            // 如果root contain下数据超过2M，分批时候，diff报错返回
            userData1->recvNum++;
        }
    }
}

void TestFetchAndDeparseDiffBatch(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK)
{
    system("mkdir -p replyDiff;rm -rf replyDiff/*");

    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callbackBatch, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
    AW_FUN_Log(LOG_INFO, "diff data.lastExpectIdx is %d.", data.lastExpectIdx);
}

static vector<string> expectDiffNotCmp = {
    "huawei-ifm:ifm:update[(priKey(ID:1)),(priKey(ID:1))]\n"
};


int CheckReplyKeyCount(int expectNum, const int fileSeq, const char *keyStr)
{
    char tmpStr[128] = {0};
    (void)snprintf(tmpStr, 128, "%d", expectNum);

    // 检查表STORAGE_HASH_COLLISION_STAT
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat reply/%d.txt "
        "|grep \"%s\"|wc -l", fileSeq, keyStr);

    int ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int CheckReplyDiffKeyCount(int expectNum, const int fileSeq, const char *keyStr)
{
    char tmpStr[128] = {0};
    (void)snprintf(tmpStr, 128, "%d", expectNum);

    // 检查表STORAGE_HASH_COLLISION_STAT
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat replyDiff/%d.txt "
        "|grep \"%s\"|wc -l", fileSeq, keyStr);

    int ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int CheckReplySnKeyCount(int expectNum, const int fileSeq, const char *keyStr)
{
    char tmpStr[128] = {0};
    (void)snprintf(tmpStr, 128, "%d", expectNum);

    // 检查表STORAGE_HASH_COLLISION_STAT
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat replySn/%d.txt "
        "|grep \"%s\"|wc -l", fileSeq, keyStr);

    int ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int CheckReplySnCount(int expectNum)
{
    char tmpStr[128] = {0};
    (void)snprintf(tmpStr, 128, "%d", expectNum);

    // 检查表STORAGE_HASH_COLLISION_STAT
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat replySn/*.txt |wc -l");

    int ret = executeCommand(g_command, tmpStr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}


void TestSubtreeFilterObj(GmcStmtT *stmt, GmcNodeT *rootNode, const char * jsonName, const char * rootName,
    GmcSubtreeWithDefaultModeE defaultMode, uint32_t depth = 0,
    GmcSubtreeWithConfigModeE configMode = GMC_SUBTREE_FILTER_DEFAULT)
{
    int ret;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;
    filter.maxDepth = depth;
    filter.configFlag = configMode;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

#endif /* ALIASTOOL_H */
